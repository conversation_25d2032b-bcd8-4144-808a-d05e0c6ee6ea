/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *             File:  Rte_FallbackSigOutput.h
 *           Config:  DiDi_FBU.dpa
 *      ECU-Project:  DiDi_FBU
 *
 *        Generator:  MICROSAR RTE Generator Version 4.27.0
 *                    RTE Core Version 1.27.0
 *          License:  CBD2100894
 *
 *      Description:  Application header file for SW-C <FallbackSigOutput>
 *********************************************************************************************************************/

/* double include prevention */
#ifndef RTE_FALLBACKSIGOUTPUT_H
# define RTE_FALLBACKSIGOUTPUT_H

# ifndef RTE_CORE
#  ifdef RTE_APPLICATION_HEADER_FILE
//#   error Multiple application header files included.
#  endif
#  define RTE_APPLICATION_HEADER_FILE
#  ifndef RTE_PTR2ARRAYBASETYPE_PASSING
#   define RTE_PTR2ARRAYBASETYPE_PASSING
#  endif
# endif

# ifdef __cplusplus
extern "C"
{
# endif /* __cplusplus */

/* include files */

# include "_out/Appl/GenData/Components/Rte_FallbackSigOutput_Type.h"
# include "_out/Appl/GenData/Rte_DataHandleType.h"


# ifndef RTE_CORE
/**********************************************************************************************************************
 * Init Values for unqueued S/R communication (primitive types only)
 *********************************************************************************************************************/

#  define Rte_InitValue_ESC_DA_MESSAGE_ESC_ABA_active (FALSE)
#  define Rte_InitValue_ESC_DA_MESSAGE_ESC_ABA_available (FALSE)
#  define Rte_InitValue_ESC_DA_MESSAGE_ESC_ABP_active (FALSE)
#  define Rte_InitValue_ESC_DA_MESSAGE_ESC_ABP_available (FALSE)
#  define Rte_InitValue_ESC_DA_MESSAGE_ESC_AEB_active (FALSE)
#  define Rte_InitValue_ESC_DA_MESSAGE_ESC_AEB_available (FALSE)
#  define Rte_InitValue_ESC_DA_MESSAGE_ESC_AWB_active (FALSE)
#  define Rte_InitValue_ESC_DA_MESSAGE_ESC_AWB_available (FALSE)
#  define Rte_InitValue_ESC_DA_MESSAGE_ESC_BrakeTempTooHigh (FALSE)
#  define Rte_InitValue_ESC_DA_MESSAGE_ESC_DA_MESSAGE_AliveCounter (0U)
#  define Rte_InitValue_ESC_DA_MESSAGE_ESC_DA_MESSAGE_Checksum (0U)
#  define Rte_InitValue_ESC_DA_MESSAGE_ESC_DTC_Active (FALSE)
#  define Rte_InitValue_ESC_DA_MESSAGE_ESC_DiagExtModSts (FALSE)
#  define Rte_InitValue_ESC_DA_MESSAGE_ESC_NoBrakeForce (FALSE)
#  define Rte_InitValue_ESC_DA_MESSAGE_ESC_QDCFRS (FALSE)
#  define Rte_InitValue_ESC_DA_MESSAGE_ESC_Vehiclestandstill (2U)
#  define Rte_InitValue_ESC_FrontWheelSpeedKPH_ESC_FLWheelDirection (3U)
#  define Rte_InitValue_ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedInvalid (TRUE)
#  define Rte_InitValue_ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedKPH (0U)
#  define Rte_InitValue_ESC_FrontWheelSpeedKPH_ESC_FRWheelDirection (3U)
#  define Rte_InitValue_ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedInvalid (TRUE)
#  define Rte_InitValue_ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedKPH (0U)
#  define Rte_InitValue_ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_AliveCounter (0U)
#  define Rte_InitValue_ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_Checksum (0U)
#  define Rte_InitValue_ESC_FrontWheelSpeedKPH_ESC_Mcylinder_Pressure (0U)
#  define Rte_InitValue_ESC_FrontWheelSpeedKPH_ESC_Mcylinder_PressureInvalid (TRUE)
#  define Rte_InitValue_ESC_RearWheelSpeedKPH_ESC_RLWheelDirection (3U)
#  define Rte_InitValue_ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedInvalid (TRUE)
#  define Rte_InitValue_ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedKPH (0U)
#  define Rte_InitValue_ESC_RearWheelSpeedKPH_ESC_RRWheelDirection (3U)
#  define Rte_InitValue_ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedInvalid (TRUE)
#  define Rte_InitValue_ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedKPH (0U)
#  define Rte_InitValue_ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_AliveCounter (0U)
#  define Rte_InitValue_ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_Checksum (0U)
#  define Rte_InitValue_ESC_Status_ESC_ABSActive (FALSE)
#  define Rte_InitValue_ESC_Status_ESC_AVHStatus (0U)
#  define Rte_InitValue_ESC_Status_ESC_BrakePedalSwitchInvalid (TRUE)
#  define Rte_InitValue_ESC_Status_ESC_BrakePedalSwitchStatus (FALSE)
#  define Rte_InitValue_ESC_Status_ESC_EPBStatus (3U)
#  define Rte_InitValue_ESC_Status_ESC_ESPActive (FALSE)
#  define Rte_InitValue_ESC_Status_ESC_ESPFailed (TRUE)
#  define Rte_InitValue_ESC_Status_ESC_HHCActive (FALSE)
#  define Rte_InitValue_ESC_Status_ESC_PATAResponse (FALSE)
#  define Rte_InitValue_ESC_Status_ESC_Status_AliveCounter (0U)
#  define Rte_InitValue_ESC_Status_ESC_Status_Checksum (0U)
#  define Rte_InitValue_ESC_Status_ESC_TCSActive (FALSE)
#  define Rte_InitValue_ESC_Status_ESC_VehicleSpeed (0U)
#  define Rte_InitValue_ESC_Status_ESC_VehicleSpeedInvalid (FALSE)
#  define Rte_InitValue_FRS_Status_FRS_Fail (FALSE)
#  define Rte_InitValue_FRS_Status_FRS_HostSpeed (800U)
#  define Rte_InitValue_FRS_Status_FRS_Host_Yaw (1024U)
#  define Rte_InitValue_FRS_Status_FRS_Latency (0U)
#  define Rte_InitValue_FRS_Status_FRS_MeasEnabled (TRUE)
#  define Rte_InitValue_FRS_Status_FRS_Msg_AliveCounter (0U)
#  define Rte_InitValue_FRS_Status_FRS_Msg_CheckSum (0U)
#  define Rte_InitValue_FRS_Status_FRS_Status_BlkProg (FALSE)
#  define Rte_InitValue_FRS_Status_FRS_Status_HWErr (FALSE)
#  define Rte_InitValue_FRS_Status_FRS_Status_MisAlign (0U)
#  define Rte_InitValue_FRS_Status_FRS_TimeStamp (0U)
#  define Rte_InitValue_FallbackDebugInfo_AccRequestAfterRateLimit (200U)
#  define Rte_InitValue_FallbackDebugInfo_AccRequestByOutOfOdd (200U)
#  define Rte_InitValue_FallbackDebugInfo_AccRequestBySpeed (200U)
#  define Rte_InitValue_FallbackDebugInfo_AccRequestForSystemError (200U)
#  define Rte_InitValue_FallbackDebugInfo_AvoidCollisionEnable (FALSE)
#  define Rte_InitValue_FallbackDebugInfo_EgoLaneWidth (0U)
#  define Rte_InitValue_FallbackDebugInfo_EgoStopTime (0U)
#  define Rte_InitValue_FallbackDebugInfo_EmergencyBrakeAcc (200U)
#  define Rte_InitValue_FallbackDebugInfo_FallbackDebugInfoReserve1 (0U)
#  define Rte_InitValue_FallbackDebugInfo_FallbackDebugInfoReserve2 (0U)
#  define Rte_InitValue_FallbackDebugInfo_FallbackDebugInfoReserve3 (0U)
#  define Rte_InitValue_FallbackDebugInfo_FallbackDebugInfoReserve4 (0U)
#  define Rte_InitValue_FallbackDebugInfo_FallbackDebugInfoReserve5 (0U)
#  define Rte_InitValue_FallbackDebugInfo_FallbackDebugInfoRollingCounter (0U)
#  define Rte_InitValue_FallbackDebugInfo_FallbackTriggerStatus (0U)
#  define Rte_InitValue_FallbackDebugInfo_FeedforwardsSteerAngle (7800U)
#  define Rte_InitValue_FallbackDebugInfo_GradientLimitAccRequest (200U)
#  define Rte_InitValue_FallbackDebugInfo_HeadingAngleContribution (0U)
#  define Rte_InitValue_FallbackDebugInfo_HeadingAngleError (201U)
#  define Rte_InitValue_FallbackDebugInfo_HeadingAngleErrorWeight (0U)
#  define Rte_InitValue_FallbackDebugInfo_LaneValidState (0U)
#  define Rte_InitValue_FallbackDebugInfo_LateralContribution (0U)
#  define Rte_InitValue_FallbackDebugInfo_LateralDistanceError (2825U)
#  define Rte_InitValue_FallbackDebugInfo_LateralDistanceErrorWeight (0U)
#  define Rte_InitValue_FallbackDebugInfo_LateralSystemState (0U)
#  define Rte_InitValue_FallbackDebugInfo_LimitAccRequest (200U)
#  define Rte_InitValue_FallbackDebugInfo_LimitSteerAngle (0U)
#  define Rte_InitValue_FallbackDebugInfo_LimitSteerAngleRequest (7800U)
#  define Rte_InitValue_FallbackDebugInfo_LongAccRequest (200U)
#  define Rte_InitValue_FallbackDebugInfo_LongNecAcc (200U)
#  define Rte_InitValue_FallbackDebugInfo_LqrIterationError (0U)
#  define Rte_InitValue_FallbackDebugInfo_LqrIterationNums (0U)
#  define Rte_InitValue_FallbackDebugInfo_MaxSteerAngleRateThreshold (0U)
#  define Rte_InitValue_FallbackDebugInfo_MaxSteerAngleThreshold (7800U)
#  define Rte_InitValue_FallbackDebugInfo_MinAccRate (0U)
#  define Rte_InitValue_FallbackDebugInfo_ObjectStopTime (0U)
#  define Rte_InitValue_FallbackDebugInfo_RawAccRequest (200U)
#  define Rte_InitValue_FallbackDebugInfo_RawSteerAngle (7800U)
#  define Rte_InitValue_FallbackDebugInfo_SafeDistance (0U)
#  define Rte_InitValue_FallbackDebugInfo_SteerAngle (7800U)
#  define Rte_InitValue_FallbackDebugInfo_SteerAngleByLQR (0U)
#  define Rte_InitValue_FallbackDebugInfo_SteerAngleForSystemError (0U)
#  define Rte_InitValue_FallbackDebugInfo_SystemState (0U)
#  define Rte_InitValue_FallbackDebugInfo_TimeToCollison (0U)
#  define Rte_InitValue_FallbackDebugInfo_TrajectoryCurvature (15625U)
#  define Rte_InitValue_FallbackDebugInfo_TrajectoryCurvatureChange (480000U)
#  define Rte_InitValue_FallbackDebugInfo_TrajectoryHeadingAngle (201U)
#  define Rte_InitValue_FallbackDebugInfo_TrajectoryLength (0U)
#  define Rte_InitValue_FallbackDebugInfo_TrajectoryPosY0 (2825U)
#  define Rte_InitValue_FallbackSystemStatus_AcuFbCanMessageID (0U)
#  define Rte_InitValue_FallbackSystemStatus_AcuFbCanTimeout (FALSE)
#  define Rte_InitValue_FallbackSystemStatus_AcuFbCanTimer (0U)
#  define Rte_InitValue_FallbackSystemStatus_AcuMid3SsmCounter0MessageID (0U)
#  define Rte_InitValue_FallbackSystemStatus_AcuMid3SsmCounter0Timeout (FALSE)
#  define Rte_InitValue_FallbackSystemStatus_AcuMid3SsmCounter0Timer (0U)
#  define Rte_InitValue_FallbackSystemStatus_AcuMid3SsmCounter1MessageID (0U)
#  define Rte_InitValue_FallbackSystemStatus_AcuMid3SsmCounter1Timeout (FALSE)
#  define Rte_InitValue_FallbackSystemStatus_AcuMid3SsmCounter1Timer (0U)
#  define Rte_InitValue_FallbackSystemStatus_AcuMid5SsmCounter0MessageID (0U)
#  define Rte_InitValue_FallbackSystemStatus_AcuMid5SsmCounter0Timeout (FALSE)
#  define Rte_InitValue_FallbackSystemStatus_AcuMid5SsmCounter0Timer (0U)
#  define Rte_InitValue_FallbackSystemStatus_AcuMid5SsmCounter1MessageID (0U)
#  define Rte_InitValue_FallbackSystemStatus_AcuMid5SsmCounter1Timeout (FALSE)
#  define Rte_InitValue_FallbackSystemStatus_AcuMid5SsmCounter1Timer (0U)
#  define Rte_InitValue_FallbackSystemStatus_AcuMid6SsmCounter0MessageID (0U)
#  define Rte_InitValue_FallbackSystemStatus_AcuMid6SsmCounter0Timeout (FALSE)
#  define Rte_InitValue_FallbackSystemStatus_AcuMid6SsmCounter0Timer (0U)
#  define Rte_InitValue_FallbackSystemStatus_AcuMid6SsmCounter1MessageID (0U)
#  define Rte_InitValue_FallbackSystemStatus_AcuMid6SsmCounter1Timeout (FALSE)
#  define Rte_InitValue_FallbackSystemStatus_AcuMid6SsmCounter1Timer (0U)
#  define Rte_InitValue_FallbackSystemStatus_AswSoftwarewareVersion (0U)
#  define Rte_InitValue_FallbackSystemStatus_BootLoaderVersion (0U)
#  define Rte_InitValue_FallbackSystemStatus_BswSoftwarewareVersion (0U)
#  define Rte_InitValue_FallbackSystemStatus_FrontCameraCalibrationStatus (0U)
#  define Rte_InitValue_FallbackSystemStatus_FrontCameraCanMessageID (0U)
#  define Rte_InitValue_FallbackSystemStatus_FrontCameraCanTimeout (FALSE)
#  define Rte_InitValue_FallbackSystemStatus_FrontCameraCanTimer (0U)
#  define Rte_InitValue_FallbackSystemStatus_FrontCameraFailureStatus (0U)
#  define Rte_InitValue_FallbackSystemStatus_FrontRadarCalibrationStatus (0U)
#  define Rte_InitValue_FallbackSystemStatus_FrontRadarCanMessageID (0U)
#  define Rte_InitValue_FallbackSystemStatus_FrontRadarCanTimeout (FALSE)
#  define Rte_InitValue_FallbackSystemStatus_FrontRadarCanTimer (0U)
#  define Rte_InitValue_FallbackSystemStatus_FrontRadarFailureStatus (0U)
#  define Rte_InitValue_FallbackSystemStatus_SystemStatusReserved1 (0U)
#  define Rte_InitValue_FallbackSystemStatus_SystemStatusReserved2 (0U)
#  define Rte_InitValue_FallbackSystemStatus_SystemStatusReserved3 (0U)
#  define Rte_InitValue_FallbackSystemStatus_SystemStatusReserved4 (0U)
#  define Rte_InitValue_FallbackSystemStatus_SystemStatusRollingCounter (0U)
#  define Rte_InitValue_FallbackSystemStatus_VehMid3SsmCounter0MessageID (0U)
#  define Rte_InitValue_FallbackSystemStatus_VehMid3SsmCounter0Timeout (FALSE)
#  define Rte_InitValue_FallbackSystemStatus_VehMid3SsmCounter0Timer (0U)
#  define Rte_InitValue_FallbackSystemStatus_VehMid3SsmCounter1MessageID (0U)
#  define Rte_InitValue_FallbackSystemStatus_VehMid3SsmCounter1Timeout (FALSE)
#  define Rte_InitValue_FallbackSystemStatus_VehMid3SsmCounter1Timer (0U)
#  define Rte_InitValue_FallbackSystemStatus_VehMid3VcuCounter0MessageID (0U)
#  define Rte_InitValue_FallbackSystemStatus_VehMid3VcuCounter0Timeout (FALSE)
#  define Rte_InitValue_FallbackSystemStatus_VehMid3VcuCounter0Timer (0U)
#  define Rte_InitValue_FallbackSystemStatus_VehMid3VcuCounter1MessageID (0U)
#  define Rte_InitValue_FallbackSystemStatus_VehMid3VcuCounter1Timeout (FALSE)
#  define Rte_InitValue_FallbackSystemStatus_VehMid3VcuCounter1Timer (0U)
#  define Rte_InitValue_FallbackSystemStatus_VehMid5SsmCounter0MessageID (0U)
#  define Rte_InitValue_FallbackSystemStatus_VehMid5SsmCounter0Timeout (FALSE)
#  define Rte_InitValue_FallbackSystemStatus_VehMid5SsmCounter0Timer (0U)
#  define Rte_InitValue_FallbackSystemStatus_VehMid5SsmCounter1MessageID (0U)
#  define Rte_InitValue_FallbackSystemStatus_VehMid5SsmCounter1Timeout (FALSE)
#  define Rte_InitValue_FallbackSystemStatus_VehMid5SsmCounter1Timer (0U)
#  define Rte_InitValue_FallbackSystemStatus_VehMid6SsmCounter0MessageID (0U)
#  define Rte_InitValue_FallbackSystemStatus_VehMid6SsmCounter0Timeout (FALSE)
#  define Rte_InitValue_FallbackSystemStatus_VehMid6SsmCounter0Timer (0U)
#  define Rte_InitValue_FallbackSystemStatus_VehMid6SsmCounter1MessageID (0U)
#  define Rte_InitValue_FallbackSystemStatus_VehMid6SsmCounter1Timeout (FALSE)
#  define Rte_InitValue_FallbackSystemStatus_VehMid6SsmCounter1Timer (0U)
#  define Rte_InitValue_FbAcuAvailable_FallbackSelfCheckStatus (0U)
#  define Rte_InitValue_FbAcuAvailable_FbAcuReserved (0U)
#  define Rte_InitValue_FbAcuAvailable_FbAcuRollingCounter (0U)
#  define Rte_InitValue_FbAcuAvailable_McuStatus (0U)
#  define Rte_InitValue_FbAcuAvailable_Sensor1v1rStatus (0U)
#  define Rte_InitValue_FbAcuAvailable_VehControlStatus (0U)
#  define Rte_InitValue_SAS_Status_SAS_CalibrationSts (FALSE)
#  define Rte_InitValue_SAS_Status_SAS_FailureSts (FALSE)
#  define Rte_InitValue_SAS_Status_SAS_Status_AliveCounter (0U)
#  define Rte_InitValue_SAS_Status_SAS_Status_Checksum (0U)
#  define Rte_InitValue_SAS_Status_SAS_SteerWheelAngle (0)
#  define Rte_InitValue_SAS_Status_SAS_SteerWheelRotSpd (0U)
#  define Rte_InitValue_SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNomQf_C (1U)
#  define Rte_InitValue_SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNom_C (0U)
#  define Rte_InitValue_SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNomQf_C (1U)
#  define Rte_InitValue_SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNom_C (16384U)
#  define Rte_InitValue_SSMMid5CanFdFr03_1V1R_PrimALatDataRawSafeNom_C (16384U)
#  define Rte_InitValue_VCU1Mid3CanFr03_1V1R_PrpsnTqDirAct_C (0U)
#  define Rte_InitValue_VCU1Mid3CanFr06_ACU_CarTiGlb_A (0U)
#  define Rte_InitValue_VCU1Mid3CanFr08_1V1R_YawRate1Qf1_C (1U)
#  define Rte_InitValue_VCU1Mid3CanFr08_1V1R_YawRate1_C (0)
#  define Rte_InitValue_VCU1Mid3CanFr36_1V1R_WhlLockStsLockSts_C (0U)
#  define Rte_InitValue_VIMMid3CanFr04_IDcDcAvlLoSideExt (0U)
#  define Rte_InitValue_VIMMid3CanFr04_IDcDcAvlMaxLoSideExt (0U)
#  define Rte_InitValue_VIMMid3CanFr04_UDcDcAvlLoSideExt (0U)
#  define Rte_InitValue_VIMMid3CanFr09_VehUsgStReq (0U)
#  define Rte_InitValue_VIMMid3CanFr13_AdSetSpd (0U)
#  define Rte_InitValue_YRS1_YRS1_AliveCounter (0U)
#  define Rte_InitValue_YRS1_YRS1_Checksum (0U)
#  define Rte_InitValue_YRS1_YRS_LateralAcce (2000U)
#  define Rte_InitValue_YRS1_YRS_LateralSensorState (3U)
#  define Rte_InitValue_YRS1_YRS_YawRate (18000U)
#  define Rte_InitValue_YRS1_YRS_YawRateSensorState (3U)
#  define Rte_InitValue_YRS2_YRS2_Checksum (0U)
#  define Rte_InitValue_YRS2_YRS_AliveCounter (0U)
#  define Rte_InitValue_YRS2_YRS_LongitAcce (2000U)
#  define Rte_InitValue_YRS2_YRS_LongitSensorState (3U)
# endif


# ifndef RTE_CORE

/**********************************************************************************************************************
 * Buffers for implicit communication
 *********************************************************************************************************************/
#  define RTE_START_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(Rte_tsMainTask_Core0_10ms, RTE_VAR_NOINIT) Rte_MainTask_Core0_10ms; /* PRQA S 0759 */ /* MD_MSR_Union */

#  define RTE_STOP_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

#  define RTE_START_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(Rte_tsOsTask_Init_Core0_APP, RTE_VAR_NOINIT) Rte_OsTask_Init_Core0_APP; /* PRQA S 0759 */ /* MD_MSR_Union */

#  define RTE_STOP_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


# endif


# ifndef RTE_CORE

/**********************************************************************************************************************
 * Rte_IRead_<r>_<p>_<d>
 * Rte_IStatus_<r>_<p>_<d>
 * Rte_IFeedback_<r>_<p>_<d>
 * Rte_IWrite_<r>_<p>_<d>
 * Rte_IWriteRef_<r>_<p>_<d>
 * Rte_IInvalidate_<r>_<p>_<d>
 *********************************************************************************************************************/


#  define Rte_IRead_FallbackSigOutput_10ms_Runnable_CSI_LaneInfo_CSI_LaneInfo() \
  (&Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_CameraSigInput_CSI_LaneInfo_CSI_LaneInfo.value)


#  define Rte_IRead_FallbackSigOutput_10ms_Runnable_ControlCommand_ControlCommand() \
  (&Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_Control_ControlCommand_ControlCommand.value)


#  define Rte_IRead_FallbackSigOutput_10ms_Runnable_FRS_Status_FRS_Fail() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FRS_Status_FRS_Fail.value)


#  define Rte_IRead_FallbackSigOutput_10ms_Runnable_FRS_Status_FRS_HostSpeed() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FRS_Status_FRS_HostSpeed.value)


#  define Rte_IRead_FallbackSigOutput_10ms_Runnable_FRS_Status_FRS_Host_Yaw() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FRS_Status_FRS_Host_Yaw.value)


#  define Rte_IRead_FallbackSigOutput_10ms_Runnable_FRS_Status_FRS_Latency() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FRS_Status_FRS_Latency.value)


#  define Rte_IRead_FallbackSigOutput_10ms_Runnable_FRS_Status_FRS_MeasEnabled() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FRS_Status_FRS_MeasEnabled.value)


#  define Rte_IRead_FallbackSigOutput_10ms_Runnable_FRS_Status_FRS_Msg_AliveCounter() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FRS_Status_FRS_Msg_AliveCounter.value)


#  define Rte_IRead_FallbackSigOutput_10ms_Runnable_FRS_Status_FRS_Msg_CheckSum() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FRS_Status_FRS_Msg_CheckSum.value)


#  define Rte_IRead_FallbackSigOutput_10ms_Runnable_FRS_Status_FRS_Status_BlkProg() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FRS_Status_FRS_Status_BlkProg.value)


#  define Rte_IRead_FallbackSigOutput_10ms_Runnable_FRS_Status_FRS_Status_HWErr() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FRS_Status_FRS_Status_HWErr.value)


#  define Rte_IRead_FallbackSigOutput_10ms_Runnable_FRS_Status_FRS_Status_MisAlign() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FRS_Status_FRS_Status_MisAlign.value)


#  define Rte_IRead_FallbackSigOutput_10ms_Runnable_FRS_Status_FRS_TimeStamp() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FRS_Status_FRS_TimeStamp.value)


#  define Rte_IRead_FallbackSigOutput_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd() \
  (&Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_LatCtrlFct_LAT_CtrlCmd_LAT_CtrlCmd.value)


#  define Rte_IRead_FallbackSigOutput_10ms_Runnable_LGT_CtrlCmd_LGT_CtrlCmd() \
  (&Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_LgtCtrlFct_LGT_CtrlCmd_LGT_CtrlCmd.value)


#  define Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout() \
  (&Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_VehSigInput_VSI_McuCanTimeout_VSI_McuCanTimeout.value)


#  define Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R() \
  (&Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_VehSigInput_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R.value)


#  define Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo() \
  (&Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_VehSigInput_VSI_VehicleInfo_VSI_VehicleInfo.value)


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_ABA_active(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_ABA_active.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_ABA_active() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_ABA_active.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_ABA_available(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_ABA_available.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_ABA_available() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_ABA_available.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_ABP_active(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_ABP_active.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_ABP_active() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_ABP_active.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_ABP_available(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_ABP_available.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_ABP_available() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_ABP_available.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_AEB_active(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_AEB_active.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_AEB_active() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_AEB_active.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_AEB_available(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_AEB_available.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_AEB_available() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_AEB_available.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_AWB_active(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_AWB_active.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_AWB_active() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_AWB_active.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_AWB_available(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_AWB_available.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_AWB_available() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_AWB_available.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_BrakeTempTooHigh(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_BrakeTempTooHigh.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_BrakeTempTooHigh() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_BrakeTempTooHigh.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_DA_MESSAGE_AliveCounter(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_DA_MESSAGE_AliveCounter.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_DA_MESSAGE_AliveCounter() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_DA_MESSAGE_AliveCounter.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_DA_MESSAGE_Checksum(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_DA_MESSAGE_Checksum.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_DA_MESSAGE_Checksum() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_DA_MESSAGE_Checksum.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_DTC_Active(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_DTC_Active.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_DTC_Active() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_DTC_Active.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_DiagExtModSts(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_DiagExtModSts.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_DiagExtModSts() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_DiagExtModSts.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_NoBrakeForce(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_NoBrakeForce.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_NoBrakeForce() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_NoBrakeForce.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_QDCFRS(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_QDCFRS.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_QDCFRS() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_QDCFRS.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_Vehiclestandstill(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_Vehiclestandstill.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_Vehiclestandstill() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_DA_MESSAGE_ESC_Vehiclestandstill.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_FLWheelDirection(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_FrontWheelSpeedKPH_ESC_FLWheelDirection.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_FLWheelDirection() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_FrontWheelSpeedKPH_ESC_FLWheelDirection.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedInvalid(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedInvalid.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedInvalid() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedInvalid.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedKPH(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedKPH.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedKPH() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedKPH.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_FRWheelDirection(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_FrontWheelSpeedKPH_ESC_FRWheelDirection.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_FRWheelDirection() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_FrontWheelSpeedKPH_ESC_FRWheelDirection.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedInvalid(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedInvalid.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedInvalid() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedInvalid.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedKPH(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedKPH.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedKPH() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedKPH.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_AliveCounter(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_AliveCounter.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_AliveCounter() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_AliveCounter.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_Checksum(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_Checksum.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_Checksum() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_Checksum.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_Mcylinder_Pressure(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_FrontWheelSpeedKPH_ESC_Mcylinder_Pressure.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_Mcylinder_Pressure() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_FrontWheelSpeedKPH_ESC_Mcylinder_Pressure.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_Mcylinder_PressureInvalid(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_FrontWheelSpeedKPH_ESC_Mcylinder_PressureInvalid.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_Mcylinder_PressureInvalid() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_FrontWheelSpeedKPH_ESC_Mcylinder_PressureInvalid.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_RearWheelSpeedKPH_ESC_RLWheelDirection(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_RearWheelSpeedKPH_ESC_RLWheelDirection.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_RearWheelSpeedKPH_ESC_RLWheelDirection() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_RearWheelSpeedKPH_ESC_RLWheelDirection.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedInvalid(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedInvalid.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedInvalid() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedInvalid.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedKPH(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedKPH.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedKPH() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedKPH.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_RearWheelSpeedKPH_ESC_RRWheelDirection(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_RearWheelSpeedKPH_ESC_RRWheelDirection.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_RearWheelSpeedKPH_ESC_RRWheelDirection() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_RearWheelSpeedKPH_ESC_RRWheelDirection.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedInvalid(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedInvalid.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedInvalid() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedInvalid.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedKPH(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedKPH.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedKPH() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedKPH.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_AliveCounter(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_AliveCounter.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_AliveCounter() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_AliveCounter.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_Checksum(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_Checksum.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_Checksum() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_Checksum.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_ABSActive(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_Status_ESC_ABSActive.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_ABSActive() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_Status_ESC_ABSActive.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_AVHStatus(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_Status_ESC_AVHStatus.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_AVHStatus() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_Status_ESC_AVHStatus.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_BrakePedalSwitchInvalid(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_Status_ESC_BrakePedalSwitchInvalid.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_BrakePedalSwitchInvalid() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_Status_ESC_BrakePedalSwitchInvalid.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_BrakePedalSwitchStatus(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_Status_ESC_BrakePedalSwitchStatus.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_BrakePedalSwitchStatus() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_Status_ESC_BrakePedalSwitchStatus.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_EPBStatus(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_Status_ESC_EPBStatus.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_EPBStatus() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_Status_ESC_EPBStatus.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_ESPActive(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_Status_ESC_ESPActive.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_ESPActive() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_Status_ESC_ESPActive.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_ESPFailed(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_Status_ESC_ESPFailed.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_ESPFailed() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_Status_ESC_ESPFailed.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_HHCActive(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_Status_ESC_HHCActive.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_HHCActive() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_Status_ESC_HHCActive.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_PATAResponse(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_Status_ESC_PATAResponse.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_PATAResponse() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_Status_ESC_PATAResponse.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_Status_AliveCounter(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_Status_ESC_Status_AliveCounter.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_Status_AliveCounter() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_Status_ESC_Status_AliveCounter.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_Status_Checksum(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_Status_ESC_Status_Checksum.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_Status_Checksum() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_Status_ESC_Status_Checksum.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_TCSActive(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_Status_ESC_TCSActive.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_TCSActive() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_Status_ESC_TCSActive.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_VehicleSpeed(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_Status_ESC_VehicleSpeed.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_VehicleSpeed() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_Status_ESC_VehicleSpeed.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_VehicleSpeedInvalid(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_Status_ESC_VehicleSpeedInvalid.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_VehicleSpeedInvalid() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_ESC_Status_ESC_VehicleSpeedInvalid.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FBS_DebugInfo_FBS_DebugInfo(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FBS_DebugInfo_FBS_DebugInfo.value = *(data) \
  )

#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FBS_DebugInfo_FBS_DebugInfo() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FBS_DebugInfo_FBS_DebugInfo.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_AccRequestAfterRateLimit(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_AccRequestAfterRateLimit.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_AccRequestAfterRateLimit() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_AccRequestAfterRateLimit.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_AccRequestByOutOfOdd(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_AccRequestByOutOfOdd.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_AccRequestByOutOfOdd() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_AccRequestByOutOfOdd.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_AccRequestBySpeed(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_AccRequestBySpeed.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_AccRequestBySpeed() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_AccRequestBySpeed.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_AccRequestForSystemError(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_AccRequestForSystemError.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_AccRequestForSystemError() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_AccRequestForSystemError.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_AvoidCollisionEnable(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_AvoidCollisionEnable.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_AvoidCollisionEnable() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_AvoidCollisionEnable.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_EgoLaneWidth(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_EgoLaneWidth.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_EgoLaneWidth() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_EgoLaneWidth.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_EgoStopTime(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_EgoStopTime.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_EgoStopTime() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_EgoStopTime.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_EmergencyBrakeAcc(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_EmergencyBrakeAcc.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_EmergencyBrakeAcc() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_EmergencyBrakeAcc.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_FallbackDebugInfoReserve1(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_FallbackDebugInfoReserve1.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_FallbackDebugInfoReserve1() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_FallbackDebugInfoReserve1.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_FallbackDebugInfoReserve2(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_FallbackDebugInfoReserve2.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_FallbackDebugInfoReserve2() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_FallbackDebugInfoReserve2.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_FallbackDebugInfoReserve3(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_FallbackDebugInfoReserve3.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_FallbackDebugInfoReserve3() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_FallbackDebugInfoReserve3.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_FallbackDebugInfoReserve4(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_FallbackDebugInfoReserve4.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_FallbackDebugInfoReserve4() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_FallbackDebugInfoReserve4.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_FallbackDebugInfoReserve5(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_FallbackDebugInfoReserve5.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_FallbackDebugInfoReserve5() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_FallbackDebugInfoReserve5.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_FallbackDebugInfoRollingCounter(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_FallbackDebugInfoRollingCounter.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_FallbackDebugInfoRollingCounter() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_FallbackDebugInfoRollingCounter.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_FallbackTriggerStatus(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_FallbackTriggerStatus.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_FallbackTriggerStatus() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_FallbackTriggerStatus.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_FeedforwardsSteerAngle(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_FeedforwardsSteerAngle.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_FeedforwardsSteerAngle() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_FeedforwardsSteerAngle.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_GradientLimitAccRequest(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_GradientLimitAccRequest.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_GradientLimitAccRequest() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_GradientLimitAccRequest.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_HeadingAngleContribution(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_HeadingAngleContribution.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_HeadingAngleContribution() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_HeadingAngleContribution.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_HeadingAngleError(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_HeadingAngleError.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_HeadingAngleError() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_HeadingAngleError.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_HeadingAngleErrorWeight(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_HeadingAngleErrorWeight.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_HeadingAngleErrorWeight() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_HeadingAngleErrorWeight.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LaneValidState(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_LaneValidState.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LaneValidState() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_LaneValidState.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LateralContribution(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_LateralContribution.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LateralContribution() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_LateralContribution.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LateralDistanceError(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_LateralDistanceError.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LateralDistanceError() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_LateralDistanceError.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LateralDistanceErrorWeight(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_LateralDistanceErrorWeight.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LateralDistanceErrorWeight() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_LateralDistanceErrorWeight.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LateralSystemState(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_LateralSystemState.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LateralSystemState() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_LateralSystemState.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LimitAccRequest(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_LimitAccRequest.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LimitAccRequest() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_LimitAccRequest.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LimitSteerAngle(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_LimitSteerAngle.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LimitSteerAngle() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_LimitSteerAngle.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LimitSteerAngleRequest(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_LimitSteerAngleRequest.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LimitSteerAngleRequest() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_LimitSteerAngleRequest.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LongAccRequest(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_LongAccRequest.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LongAccRequest() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_LongAccRequest.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LongNecAcc(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_LongNecAcc.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LongNecAcc() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_LongNecAcc.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LqrIterationError(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_LqrIterationError.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LqrIterationError() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_LqrIterationError.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LqrIterationNums(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_LqrIterationNums.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LqrIterationNums() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_LqrIterationNums.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_MaxSteerAngleRateThreshold(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_MaxSteerAngleRateThreshold.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_MaxSteerAngleRateThreshold() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_MaxSteerAngleRateThreshold.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_MaxSteerAngleThreshold(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_MaxSteerAngleThreshold.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_MaxSteerAngleThreshold() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_MaxSteerAngleThreshold.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_MinAccRate(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_MinAccRate.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_MinAccRate() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_MinAccRate.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_ObjectStopTime(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_ObjectStopTime.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_ObjectStopTime() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_ObjectStopTime.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_RawAccRequest(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_RawAccRequest.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_RawAccRequest() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_RawAccRequest.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_RawSteerAngle(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_RawSteerAngle.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_RawSteerAngle() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_RawSteerAngle.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_SafeDistance(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_SafeDistance.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_SafeDistance() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_SafeDistance.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_SteerAngle(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_SteerAngle.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_SteerAngle() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_SteerAngle.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_SteerAngleByLQR(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_SteerAngleByLQR.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_SteerAngleByLQR() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_SteerAngleByLQR.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_SteerAngleForSystemError(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_SteerAngleForSystemError.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_SteerAngleForSystemError() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_SteerAngleForSystemError.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_SystemState(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_SystemState.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_SystemState() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_SystemState.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_TimeToCollison(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_TimeToCollison.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_TimeToCollison() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_TimeToCollison.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_TrajectoryCurvature(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_TrajectoryCurvature.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_TrajectoryCurvature() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_TrajectoryCurvature.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_TrajectoryCurvatureChange(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_TrajectoryCurvatureChange.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_TrajectoryCurvatureChange() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_TrajectoryCurvatureChange.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_TrajectoryHeadingAngle(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_TrajectoryHeadingAngle.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_TrajectoryHeadingAngle() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_TrajectoryHeadingAngle.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_TrajectoryLength(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_TrajectoryLength.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_TrajectoryLength() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_TrajectoryLength.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_TrajectoryPosY0(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_TrajectoryPosY0.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_TrajectoryPosY0() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackDebugInfo_TrajectoryPosY0.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuFbCanMessageID(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuFbCanMessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuFbCanMessageID() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuFbCanMessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuFbCanTimeout(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuFbCanTimeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuFbCanTimeout() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuFbCanTimeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuFbCanTimer(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuFbCanTimer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuFbCanTimer() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuFbCanTimer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid3SsmCounter0MessageID(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid3SsmCounter0MessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid3SsmCounter0MessageID() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid3SsmCounter0MessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid3SsmCounter0Timeout(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid3SsmCounter0Timeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid3SsmCounter0Timeout() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid3SsmCounter0Timeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid3SsmCounter0Timer(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid3SsmCounter0Timer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid3SsmCounter0Timer() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid3SsmCounter0Timer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid3SsmCounter1MessageID(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid3SsmCounter1MessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid3SsmCounter1MessageID() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid3SsmCounter1MessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid3SsmCounter1Timeout(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid3SsmCounter1Timeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid3SsmCounter1Timeout() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid3SsmCounter1Timeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid3SsmCounter1Timer(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid3SsmCounter1Timer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid3SsmCounter1Timer() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid3SsmCounter1Timer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid5SsmCounter0MessageID(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid5SsmCounter0MessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid5SsmCounter0MessageID() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid5SsmCounter0MessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid5SsmCounter0Timeout(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid5SsmCounter0Timeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid5SsmCounter0Timeout() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid5SsmCounter0Timeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid5SsmCounter0Timer(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid5SsmCounter0Timer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid5SsmCounter0Timer() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid5SsmCounter0Timer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid5SsmCounter1MessageID(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid5SsmCounter1MessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid5SsmCounter1MessageID() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid5SsmCounter1MessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid5SsmCounter1Timeout(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid5SsmCounter1Timeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid5SsmCounter1Timeout() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid5SsmCounter1Timeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid5SsmCounter1Timer(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid5SsmCounter1Timer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid5SsmCounter1Timer() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid5SsmCounter1Timer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid6SsmCounter0MessageID(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid6SsmCounter0MessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid6SsmCounter0MessageID() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid6SsmCounter0MessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid6SsmCounter0Timeout(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid6SsmCounter0Timeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid6SsmCounter0Timeout() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid6SsmCounter0Timeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid6SsmCounter0Timer(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid6SsmCounter0Timer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid6SsmCounter0Timer() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid6SsmCounter0Timer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid6SsmCounter1MessageID(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid6SsmCounter1MessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid6SsmCounter1MessageID() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid6SsmCounter1MessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid6SsmCounter1Timeout(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid6SsmCounter1Timeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid6SsmCounter1Timeout() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid6SsmCounter1Timeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid6SsmCounter1Timer(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid6SsmCounter1Timer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid6SsmCounter1Timer() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AcuMid6SsmCounter1Timer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AswSoftwarewareVersion(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AswSoftwarewareVersion.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AswSoftwarewareVersion() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_AswSoftwarewareVersion.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_BootLoaderVersion(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_BootLoaderVersion.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_BootLoaderVersion() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_BootLoaderVersion.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_BswSoftwarewareVersion(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_BswSoftwarewareVersion.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_BswSoftwarewareVersion() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_BswSoftwarewareVersion.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontCameraCalibrationStatus(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_FrontCameraCalibrationStatus.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontCameraCalibrationStatus() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_FrontCameraCalibrationStatus.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontCameraCanMessageID(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_FrontCameraCanMessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontCameraCanMessageID() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_FrontCameraCanMessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontCameraCanTimeout(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_FrontCameraCanTimeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontCameraCanTimeout() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_FrontCameraCanTimeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontCameraCanTimer(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_FrontCameraCanTimer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontCameraCanTimer() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_FrontCameraCanTimer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontCameraFailureStatus(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_FrontCameraFailureStatus.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontCameraFailureStatus() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_FrontCameraFailureStatus.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontRadarCalibrationStatus(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_FrontRadarCalibrationStatus.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontRadarCalibrationStatus() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_FrontRadarCalibrationStatus.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontRadarCanMessageID(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_FrontRadarCanMessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontRadarCanMessageID() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_FrontRadarCanMessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontRadarCanTimeout(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_FrontRadarCanTimeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontRadarCanTimeout() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_FrontRadarCanTimeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontRadarCanTimer(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_FrontRadarCanTimer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontRadarCanTimer() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_FrontRadarCanTimer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontRadarFailureStatus(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_FrontRadarFailureStatus.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontRadarFailureStatus() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_FrontRadarFailureStatus.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_SystemStatusReserved1(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_SystemStatusReserved1.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_SystemStatusReserved1() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_SystemStatusReserved1.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_SystemStatusReserved2(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_SystemStatusReserved2.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_SystemStatusReserved2() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_SystemStatusReserved2.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_SystemStatusReserved3(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_SystemStatusReserved3.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_SystemStatusReserved3() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_SystemStatusReserved3.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_SystemStatusReserved4(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_SystemStatusReserved4.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_SystemStatusReserved4() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_SystemStatusReserved4.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_SystemStatusRollingCounter(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_SystemStatusRollingCounter.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_SystemStatusRollingCounter() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_SystemStatusRollingCounter.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3SsmCounter0MessageID(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid3SsmCounter0MessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3SsmCounter0MessageID() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid3SsmCounter0MessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3SsmCounter0Timeout(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid3SsmCounter0Timeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3SsmCounter0Timeout() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid3SsmCounter0Timeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3SsmCounter0Timer(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid3SsmCounter0Timer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3SsmCounter0Timer() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid3SsmCounter0Timer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3SsmCounter1MessageID(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid3SsmCounter1MessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3SsmCounter1MessageID() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid3SsmCounter1MessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3SsmCounter1Timeout(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid3SsmCounter1Timeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3SsmCounter1Timeout() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid3SsmCounter1Timeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3SsmCounter1Timer(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid3SsmCounter1Timer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3SsmCounter1Timer() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid3SsmCounter1Timer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3VcuCounter0MessageID(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid3VcuCounter0MessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3VcuCounter0MessageID() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid3VcuCounter0MessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3VcuCounter0Timeout(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid3VcuCounter0Timeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3VcuCounter0Timeout() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid3VcuCounter0Timeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3VcuCounter0Timer(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid3VcuCounter0Timer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3VcuCounter0Timer() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid3VcuCounter0Timer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3VcuCounter1MessageID(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid3VcuCounter1MessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3VcuCounter1MessageID() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid3VcuCounter1MessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3VcuCounter1Timeout(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid3VcuCounter1Timeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3VcuCounter1Timeout() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid3VcuCounter1Timeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3VcuCounter1Timer(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid3VcuCounter1Timer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3VcuCounter1Timer() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid3VcuCounter1Timer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid5SsmCounter0MessageID(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid5SsmCounter0MessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid5SsmCounter0MessageID() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid5SsmCounter0MessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid5SsmCounter0Timeout(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid5SsmCounter0Timeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid5SsmCounter0Timeout() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid5SsmCounter0Timeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid5SsmCounter0Timer(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid5SsmCounter0Timer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid5SsmCounter0Timer() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid5SsmCounter0Timer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid5SsmCounter1MessageID(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid5SsmCounter1MessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid5SsmCounter1MessageID() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid5SsmCounter1MessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid5SsmCounter1Timeout(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid5SsmCounter1Timeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid5SsmCounter1Timeout() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid5SsmCounter1Timeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid5SsmCounter1Timer(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid5SsmCounter1Timer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid5SsmCounter1Timer() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid5SsmCounter1Timer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid6SsmCounter0MessageID(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid6SsmCounter0MessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid6SsmCounter0MessageID() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid6SsmCounter0MessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid6SsmCounter0Timeout(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid6SsmCounter0Timeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid6SsmCounter0Timeout() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid6SsmCounter0Timeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid6SsmCounter0Timer(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid6SsmCounter0Timer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid6SsmCounter0Timer() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid6SsmCounter0Timer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid6SsmCounter1MessageID(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid6SsmCounter1MessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid6SsmCounter1MessageID() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid6SsmCounter1MessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid6SsmCounter1Timeout(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid6SsmCounter1Timeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid6SsmCounter1Timeout() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid6SsmCounter1Timeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid6SsmCounter1Timer(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid6SsmCounter1Timer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid6SsmCounter1Timer() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FallbackSystemStatus_VehMid6SsmCounter1Timer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FbAcuAvailable_FallbackSelfCheckStatus(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FbAcuAvailable_FallbackSelfCheckStatus.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FbAcuAvailable_FallbackSelfCheckStatus() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FbAcuAvailable_FallbackSelfCheckStatus.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FbAcuAvailable_FbAcuReserved(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FbAcuAvailable_FbAcuReserved.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FbAcuAvailable_FbAcuReserved() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FbAcuAvailable_FbAcuReserved.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FbAcuAvailable_FbAcuRollingCounter(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FbAcuAvailable_FbAcuRollingCounter.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FbAcuAvailable_FbAcuRollingCounter() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FbAcuAvailable_FbAcuRollingCounter.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FbAcuAvailable_McuStatus(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FbAcuAvailable_McuStatus.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FbAcuAvailable_McuStatus() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FbAcuAvailable_McuStatus.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FbAcuAvailable_Sensor1v1rStatus(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FbAcuAvailable_Sensor1v1rStatus.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FbAcuAvailable_Sensor1v1rStatus() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FbAcuAvailable_Sensor1v1rStatus.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_FbAcuAvailable_VehControlStatus(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FbAcuAvailable_VehControlStatus.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_FbAcuAvailable_VehControlStatus() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_FbAcuAvailable_VehControlStatus.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_SAS_Status_SAS_CalibrationSts(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_SAS_Status_SAS_CalibrationSts.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_SAS_Status_SAS_CalibrationSts() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_SAS_Status_SAS_CalibrationSts.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_SAS_Status_SAS_FailureSts(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_SAS_Status_SAS_FailureSts.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_SAS_Status_SAS_FailureSts() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_SAS_Status_SAS_FailureSts.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_SAS_Status_SAS_Status_AliveCounter(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_SAS_Status_SAS_Status_AliveCounter.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_SAS_Status_SAS_Status_AliveCounter() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_SAS_Status_SAS_Status_AliveCounter.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_SAS_Status_SAS_Status_Checksum(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_SAS_Status_SAS_Status_Checksum.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_SAS_Status_SAS_Status_Checksum() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_SAS_Status_SAS_Status_Checksum.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_SAS_Status_SAS_SteerWheelAngle(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_SAS_Status_SAS_SteerWheelAngle.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_SAS_Status_SAS_SteerWheelAngle() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_SAS_Status_SAS_SteerWheelAngle.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_SAS_Status_SAS_SteerWheelRotSpd(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_SAS_Status_SAS_SteerWheelRotSpd.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_SAS_Status_SAS_SteerWheelRotSpd() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_SAS_Status_SAS_SteerWheelRotSpd.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNomQf_C(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNomQf_C.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNomQf_C() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNomQf_C.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNom_C(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNom_C.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNom_C() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNom_C.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNomQf_C(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNomQf_C.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNomQf_C() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNomQf_C.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNom_C(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNom_C.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNom_C() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNom_C.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_SSMMid5CanFdFr03_1V1R_PrimALatDataRawSafeNom_C(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_SSMMid5CanFdFr03_1V1R_PrimALatDataRawSafeNom_C.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_SSMMid5CanFdFr03_1V1R_PrimALatDataRawSafeNom_C() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_SSMMid5CanFdFr03_1V1R_PrimALatDataRawSafeNom_C.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_VCU1Mid3CanFr03_1V1R_PrpsnTqDirAct_C(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VCU1Mid3CanFr03_1V1R_PrpsnTqDirAct_C.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_VCU1Mid3CanFr03_1V1R_PrpsnTqDirAct_C() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VCU1Mid3CanFr03_1V1R_PrpsnTqDirAct_C.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_VCU1Mid3CanFr06_ACU_CarTiGlb_A(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VCU1Mid3CanFr06_ACU_CarTiGlb_A.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_VCU1Mid3CanFr06_ACU_CarTiGlb_A() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VCU1Mid3CanFr06_ACU_CarTiGlb_A.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_VCU1Mid3CanFr08_1V1R_YawRate1Qf1_C(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VCU1Mid3CanFr08_1V1R_YawRate1Qf1_C.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_VCU1Mid3CanFr08_1V1R_YawRate1Qf1_C() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VCU1Mid3CanFr08_1V1R_YawRate1Qf1_C.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_VCU1Mid3CanFr08_1V1R_YawRate1_C(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VCU1Mid3CanFr08_1V1R_YawRate1_C.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_VCU1Mid3CanFr08_1V1R_YawRate1_C() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VCU1Mid3CanFr08_1V1R_YawRate1_C.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_VCU1Mid3CanFr36_1V1R_WhlLockStsLockSts_C(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VCU1Mid3CanFr36_1V1R_WhlLockStsLockSts_C.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_VCU1Mid3CanFr36_1V1R_WhlLockStsLockSts_C() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VCU1Mid3CanFr36_1V1R_WhlLockStsLockSts_C.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMBMid6CanFdFr14_SG_AdSecWhlAgReqGroupSafe(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMBMid6CanFdFr14_SG_AdSecWhlAgReqGroupSafe.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_VIMBMid6CanFdFr14_SG_AdSecWhlAgReqGroupSafe() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMBMid6CanFdFr14_SG_AdSecWhlAgReqGroupSafe.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMBMid6CanFdFr28_SG_AdSecALgtLimReqGroupSafe(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMBMid6CanFdFr28_SG_AdSecALgtLimReqGroupSafe.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_VIMBMid6CanFdFr28_SG_AdSecALgtLimReqGroupSafe() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMBMid6CanFdFr28_SG_AdSecALgtLimReqGroupSafe.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMBMid6CanFdFr28_SG_SecAdNomALgtReqGroupSafe(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMBMid6CanFdFr28_SG_SecAdNomALgtReqGroupSafe.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_VIMBMid6CanFdFr28_SG_SecAdNomALgtReqGroupSafe() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMBMid6CanFdFr28_SG_SecAdNomALgtReqGroupSafe.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMBMid6CanFdFr29_SG_SecAdWhlLockReq(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMBMid6CanFdFr29_SG_SecAdWhlLockReq.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_VIMBMid6CanFdFr29_SG_SecAdWhlLockReq() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMBMid6CanFdFr29_SG_SecAdWhlLockReq.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr04_IDcDcAvlLoSideExt(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr04_IDcDcAvlLoSideExt.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr04_IDcDcAvlLoSideExt() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr04_IDcDcAvlLoSideExt.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr04_IDcDcAvlMaxLoSideExt(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr04_IDcDcAvlMaxLoSideExt.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr04_IDcDcAvlMaxLoSideExt() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr04_IDcDcAvlMaxLoSideExt.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr04_SG_HmiAutnmsSts(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr04_SG_HmiAutnmsSts.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr04_SG_HmiAutnmsSts() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr04_SG_HmiAutnmsSts.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr04_UDcDcAvlLoSideExt(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr04_UDcDcAvlLoSideExt.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr04_UDcDcAvlLoSideExt() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr04_UDcDcAvlLoSideExt.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr07_SG_AutnmsDrvStReq(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr07_SG_AutnmsDrvStReq.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr07_SG_AutnmsDrvStReq() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr07_SG_AutnmsDrvStReq.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr08_SG_VehOperStReq(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr08_SG_VehOperStReq.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr08_SG_VehOperStReq() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr08_SG_VehOperStReq.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr09_SG_AdDirReq(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr09_SG_AdDirReq.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr09_SG_AdDirReq() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr09_SG_AdDirReq.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr09_SG_AdStandStillReq(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr09_SG_AdStandStillReq.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr09_SG_AdStandStillReq() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr09_SG_AdStandStillReq.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr09_VehUsgStReq(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr09_VehUsgStReq.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr09_VehUsgStReq() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr09_VehUsgStReq.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr11_SG_AdFusedFricEstimn(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr11_SG_AdFusedFricEstimn.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr11_SG_AdFusedFricEstimn() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr11_SG_AdFusedFricEstimn.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr11_SG_AdpLiReqFromAPI(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr11_SG_AdpLiReqFromAPI.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr11_SG_AdpLiReqFromAPI() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr11_SG_AdpLiReqFromAPI.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr11_SG_SwtExtrLiFromAPI(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr11_SG_SwtExtrLiFromAPI.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr11_SG_SwtExtrLiFromAPI() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr11_SG_SwtExtrLiFromAPI.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr13_AdSetSpd(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr13_AdSetSpd.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr13_AdSetSpd() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr13_AdSetSpd.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr13_SG_AdFreeDst(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr13_SG_AdFreeDst.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr13_SG_AdFreeDst() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr13_SG_AdFreeDst.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr13_SG_AdWhlLockReq(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr13_SG_AdWhlLockReq.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr13_SG_AdWhlLockReq() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr13_SG_AdWhlLockReq.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr14_SG_AdNomALgtReqGroupSafe(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr14_SG_AdNomALgtReqGroupSafe.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr14_SG_AdNomALgtReqGroupSafe() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr14_SG_AdNomALgtReqGroupSafe.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr15_SG_AdPrimALgtLimReqGroupSafe(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr15_SG_AdPrimALgtLimReqGroupSafe.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr15_SG_AdPrimALgtLimReqGroupSafe() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid3CanFr15_SG_AdPrimALgtLimReqGroupSafe.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid5CanFdFr12_SG_AdPrimWhlAgReqGroupSafe(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid5CanFdFr12_SG_AdPrimWhlAgReqGroupSafe.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_VIMMid5CanFdFr12_SG_AdPrimWhlAgReqGroupSafe() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_VIMMid5CanFdFr12_SG_AdPrimWhlAgReqGroupSafe.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_YRS1_YRS1_AliveCounter(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_YRS1_YRS1_AliveCounter.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_YRS1_YRS1_AliveCounter() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_YRS1_YRS1_AliveCounter.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_YRS1_YRS1_Checksum(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_YRS1_YRS1_Checksum.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_YRS1_YRS1_Checksum() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_YRS1_YRS1_Checksum.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_YRS1_YRS_LateralAcce(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_YRS1_YRS_LateralAcce.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_YRS1_YRS_LateralAcce() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_YRS1_YRS_LateralAcce.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_YRS1_YRS_LateralSensorState(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_YRS1_YRS_LateralSensorState.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_YRS1_YRS_LateralSensorState() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_YRS1_YRS_LateralSensorState.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_YRS1_YRS_YawRate(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_YRS1_YRS_YawRate.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_YRS1_YRS_YawRate() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_YRS1_YRS_YawRate.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_YRS1_YRS_YawRateSensorState(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_YRS1_YRS_YawRateSensorState.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_YRS1_YRS_YawRateSensorState() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_YRS1_YRS_YawRateSensorState.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_YRS2_YRS2_Checksum(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_YRS2_YRS2_Checksum.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_YRS2_YRS2_Checksum() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_YRS2_YRS2_Checksum.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_YRS2_YRS_AliveCounter(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_YRS2_YRS_AliveCounter.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_YRS2_YRS_AliveCounter() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_YRS2_YRS_AliveCounter.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_YRS2_YRS_LongitAcce(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_YRS2_YRS_LongitAcce.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_YRS2_YRS_LongitAcce() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_YRS2_YRS_LongitAcce.value \
  )


#  define Rte_IWrite_FallbackSigOutput_10ms_Runnable_YRS2_YRS_LongitSensorState(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_YRS2_YRS_LongitSensorState.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_10ms_Runnable_YRS2_YRS_LongitSensorState() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable.Rte_YRS2_YRS_LongitSensorState.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_ABA_active(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_ABA_active.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_ABA_active() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_ABA_active.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_ABA_available(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_ABA_available.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_ABA_available() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_ABA_available.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_ABP_active(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_ABP_active.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_ABP_active() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_ABP_active.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_ABP_available(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_ABP_available.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_ABP_available() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_ABP_available.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_AEB_active(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_AEB_active.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_AEB_active() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_AEB_active.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_AEB_available(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_AEB_available.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_AEB_available() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_AEB_available.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_AWB_active(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_AWB_active.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_AWB_active() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_AWB_active.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_AWB_available(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_AWB_available.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_AWB_available() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_AWB_available.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_BrakeTempTooHigh(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_BrakeTempTooHigh.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_BrakeTempTooHigh() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_BrakeTempTooHigh.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_DA_MESSAGE_AliveCounter(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_DA_MESSAGE_AliveCounter.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_DA_MESSAGE_AliveCounter() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_DA_MESSAGE_AliveCounter.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_DA_MESSAGE_Checksum(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_DA_MESSAGE_Checksum.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_DA_MESSAGE_Checksum() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_DA_MESSAGE_Checksum.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_DTC_Active(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_DTC_Active.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_DTC_Active() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_DTC_Active.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_DiagExtModSts(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_DiagExtModSts.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_DiagExtModSts() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_DiagExtModSts.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_NoBrakeForce(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_NoBrakeForce.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_NoBrakeForce() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_NoBrakeForce.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_QDCFRS(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_QDCFRS.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_QDCFRS() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_QDCFRS.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_Vehiclestandstill(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_Vehiclestandstill.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_Vehiclestandstill() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_DA_MESSAGE_ESC_Vehiclestandstill.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_FLWheelDirection(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_FrontWheelSpeedKPH_ESC_FLWheelDirection.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_FLWheelDirection() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_FrontWheelSpeedKPH_ESC_FLWheelDirection.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedInvalid(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedInvalid.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedInvalid() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedInvalid.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedKPH(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedKPH.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedKPH() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedKPH.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_FRWheelDirection(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_FrontWheelSpeedKPH_ESC_FRWheelDirection.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_FRWheelDirection() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_FrontWheelSpeedKPH_ESC_FRWheelDirection.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedInvalid(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedInvalid.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedInvalid() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedInvalid.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedKPH(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedKPH.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedKPH() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedKPH.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_AliveCounter(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_AliveCounter.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_AliveCounter() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_AliveCounter.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_Checksum(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_Checksum.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_Checksum() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_Checksum.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_Mcylinder_Pressure(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_FrontWheelSpeedKPH_ESC_Mcylinder_Pressure.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_Mcylinder_Pressure() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_FrontWheelSpeedKPH_ESC_Mcylinder_Pressure.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_Mcylinder_PressureInvalid(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_FrontWheelSpeedKPH_ESC_Mcylinder_PressureInvalid.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_Mcylinder_PressureInvalid() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_FrontWheelSpeedKPH_ESC_Mcylinder_PressureInvalid.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_RearWheelSpeedKPH_ESC_RLWheelDirection(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_RearWheelSpeedKPH_ESC_RLWheelDirection.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_RearWheelSpeedKPH_ESC_RLWheelDirection() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_RearWheelSpeedKPH_ESC_RLWheelDirection.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedInvalid(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedInvalid.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedInvalid() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedInvalid.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedKPH(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedKPH.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedKPH() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedKPH.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_RearWheelSpeedKPH_ESC_RRWheelDirection(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_RearWheelSpeedKPH_ESC_RRWheelDirection.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_RearWheelSpeedKPH_ESC_RRWheelDirection() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_RearWheelSpeedKPH_ESC_RRWheelDirection.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedInvalid(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedInvalid.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedInvalid() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedInvalid.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedKPH(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedKPH.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedKPH() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedKPH.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_AliveCounter(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_AliveCounter.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_AliveCounter() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_AliveCounter.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_Checksum(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_Checksum.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_Checksum() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_Checksum.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_Status_ESC_ABSActive(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_Status_ESC_ABSActive.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_Status_ESC_ABSActive() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_Status_ESC_ABSActive.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_Status_ESC_AVHStatus(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_Status_ESC_AVHStatus.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_Status_ESC_AVHStatus() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_Status_ESC_AVHStatus.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_Status_ESC_BrakePedalSwitchInvalid(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_Status_ESC_BrakePedalSwitchInvalid.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_Status_ESC_BrakePedalSwitchInvalid() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_Status_ESC_BrakePedalSwitchInvalid.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_Status_ESC_BrakePedalSwitchStatus(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_Status_ESC_BrakePedalSwitchStatus.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_Status_ESC_BrakePedalSwitchStatus() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_Status_ESC_BrakePedalSwitchStatus.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_Status_ESC_EPBStatus(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_Status_ESC_EPBStatus.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_Status_ESC_EPBStatus() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_Status_ESC_EPBStatus.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_Status_ESC_ESPActive(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_Status_ESC_ESPActive.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_Status_ESC_ESPActive() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_Status_ESC_ESPActive.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_Status_ESC_ESPFailed(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_Status_ESC_ESPFailed.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_Status_ESC_ESPFailed() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_Status_ESC_ESPFailed.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_Status_ESC_HHCActive(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_Status_ESC_HHCActive.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_Status_ESC_HHCActive() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_Status_ESC_HHCActive.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_Status_ESC_PATAResponse(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_Status_ESC_PATAResponse.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_Status_ESC_PATAResponse() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_Status_ESC_PATAResponse.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_Status_ESC_Status_AliveCounter(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_Status_ESC_Status_AliveCounter.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_Status_ESC_Status_AliveCounter() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_Status_ESC_Status_AliveCounter.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_Status_ESC_Status_Checksum(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_Status_ESC_Status_Checksum.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_Status_ESC_Status_Checksum() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_Status_ESC_Status_Checksum.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_Status_ESC_TCSActive(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_Status_ESC_TCSActive.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_Status_ESC_TCSActive() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_Status_ESC_TCSActive.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_Status_ESC_VehicleSpeed(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_Status_ESC_VehicleSpeed.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_Status_ESC_VehicleSpeed() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_Status_ESC_VehicleSpeed.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_ESC_Status_ESC_VehicleSpeedInvalid(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_Status_ESC_VehicleSpeedInvalid.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_ESC_Status_ESC_VehicleSpeedInvalid() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_ESC_Status_ESC_VehicleSpeedInvalid.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FBS_DebugInfo_FBS_DebugInfo(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FBS_DebugInfo_FBS_DebugInfo.value = *(data) \
  )



#  define Rte_IWriteRef_FallbackSigOutput_Init_FBS_DebugInfo_FBS_DebugInfo() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FBS_DebugInfo_FBS_DebugInfo.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_AccRequestAfterRateLimit(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_AccRequestAfterRateLimit.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_AccRequestAfterRateLimit() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_AccRequestAfterRateLimit.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_AccRequestByOutOfOdd(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_AccRequestByOutOfOdd.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_AccRequestByOutOfOdd() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_AccRequestByOutOfOdd.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_AccRequestBySpeed(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_AccRequestBySpeed.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_AccRequestBySpeed() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_AccRequestBySpeed.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_AccRequestForSystemError(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_AccRequestForSystemError.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_AccRequestForSystemError() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_AccRequestForSystemError.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_AvoidCollisionEnable(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_AvoidCollisionEnable.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_AvoidCollisionEnable() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_AvoidCollisionEnable.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_EgoLaneWidth(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_EgoLaneWidth.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_EgoLaneWidth() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_EgoLaneWidth.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_EgoStopTime(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_EgoStopTime.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_EgoStopTime() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_EgoStopTime.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_EmergencyBrakeAcc(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_EmergencyBrakeAcc.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_EmergencyBrakeAcc() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_EmergencyBrakeAcc.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_FallbackDebugInfoReserve1(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_FallbackDebugInfoReserve1.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_FallbackDebugInfoReserve1() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_FallbackDebugInfoReserve1.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_FallbackDebugInfoReserve2(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_FallbackDebugInfoReserve2.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_FallbackDebugInfoReserve2() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_FallbackDebugInfoReserve2.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_FallbackDebugInfoReserve3(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_FallbackDebugInfoReserve3.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_FallbackDebugInfoReserve3() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_FallbackDebugInfoReserve3.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_FallbackDebugInfoReserve4(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_FallbackDebugInfoReserve4.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_FallbackDebugInfoReserve4() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_FallbackDebugInfoReserve4.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_FallbackDebugInfoReserve5(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_FallbackDebugInfoReserve5.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_FallbackDebugInfoReserve5() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_FallbackDebugInfoReserve5.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_FallbackDebugInfoRollingCounter(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_FallbackDebugInfoRollingCounter.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_FallbackDebugInfoRollingCounter() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_FallbackDebugInfoRollingCounter.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_FallbackTriggerStatus(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_FallbackTriggerStatus.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_FallbackTriggerStatus() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_FallbackTriggerStatus.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_FeedforwardsSteerAngle(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_FeedforwardsSteerAngle.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_FeedforwardsSteerAngle() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_FeedforwardsSteerAngle.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_GradientLimitAccRequest(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_GradientLimitAccRequest.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_GradientLimitAccRequest() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_GradientLimitAccRequest.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_HeadingAngleContribution(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_HeadingAngleContribution.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_HeadingAngleContribution() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_HeadingAngleContribution.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_HeadingAngleError(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_HeadingAngleError.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_HeadingAngleError() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_HeadingAngleError.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_HeadingAngleErrorWeight(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_HeadingAngleErrorWeight.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_HeadingAngleErrorWeight() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_HeadingAngleErrorWeight.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_LaneValidState(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_LaneValidState.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_LaneValidState() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_LaneValidState.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_LateralContribution(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_LateralContribution.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_LateralContribution() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_LateralContribution.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_LateralDistanceError(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_LateralDistanceError.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_LateralDistanceError() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_LateralDistanceError.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_LateralDistanceErrorWeight(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_LateralDistanceErrorWeight.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_LateralDistanceErrorWeight() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_LateralDistanceErrorWeight.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_LateralSystemState(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_LateralSystemState.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_LateralSystemState() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_LateralSystemState.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_LimitAccRequest(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_LimitAccRequest.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_LimitAccRequest() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_LimitAccRequest.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_LimitSteerAngle(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_LimitSteerAngle.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_LimitSteerAngle() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_LimitSteerAngle.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_LimitSteerAngleRequest(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_LimitSteerAngleRequest.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_LimitSteerAngleRequest() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_LimitSteerAngleRequest.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_LongAccRequest(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_LongAccRequest.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_LongAccRequest() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_LongAccRequest.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_LongNecAcc(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_LongNecAcc.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_LongNecAcc() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_LongNecAcc.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_LqrIterationError(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_LqrIterationError.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_LqrIterationError() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_LqrIterationError.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_LqrIterationNums(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_LqrIterationNums.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_LqrIterationNums() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_LqrIterationNums.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_MaxSteerAngleRateThreshold(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_MaxSteerAngleRateThreshold.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_MaxSteerAngleRateThreshold() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_MaxSteerAngleRateThreshold.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_MaxSteerAngleThreshold(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_MaxSteerAngleThreshold.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_MaxSteerAngleThreshold() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_MaxSteerAngleThreshold.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_MinAccRate(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_MinAccRate.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_MinAccRate() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_MinAccRate.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_ObjectStopTime(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_ObjectStopTime.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_ObjectStopTime() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_ObjectStopTime.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_RawAccRequest(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_RawAccRequest.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_RawAccRequest() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_RawAccRequest.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_RawSteerAngle(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_RawSteerAngle.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_RawSteerAngle() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_RawSteerAngle.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_SafeDistance(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_SafeDistance.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_SafeDistance() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_SafeDistance.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_SteerAngle(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_SteerAngle.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_SteerAngle() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_SteerAngle.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_SteerAngleByLQR(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_SteerAngleByLQR.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_SteerAngleByLQR() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_SteerAngleByLQR.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_SteerAngleForSystemError(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_SteerAngleForSystemError.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_SteerAngleForSystemError() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_SteerAngleForSystemError.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_SystemState(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_SystemState.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_SystemState() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_SystemState.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_TimeToCollison(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_TimeToCollison.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_TimeToCollison() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_TimeToCollison.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_TrajectoryCurvature(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_TrajectoryCurvature.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_TrajectoryCurvature() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_TrajectoryCurvature.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_TrajectoryCurvatureChange(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_TrajectoryCurvatureChange.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_TrajectoryCurvatureChange() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_TrajectoryCurvatureChange.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_TrajectoryHeadingAngle(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_TrajectoryHeadingAngle.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_TrajectoryHeadingAngle() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_TrajectoryHeadingAngle.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_TrajectoryLength(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_TrajectoryLength.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_TrajectoryLength() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_TrajectoryLength.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_TrajectoryPosY0(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_TrajectoryPosY0.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackDebugInfo_TrajectoryPosY0() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackDebugInfo_TrajectoryPosY0.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuFbCanMessageID(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuFbCanMessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_AcuFbCanMessageID() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuFbCanMessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuFbCanTimeout(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuFbCanTimeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_AcuFbCanTimeout() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuFbCanTimeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuFbCanTimer(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuFbCanTimer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_AcuFbCanTimer() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuFbCanTimer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid3SsmCounter0MessageID(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid3SsmCounter0MessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid3SsmCounter0MessageID() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid3SsmCounter0MessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid3SsmCounter0Timeout(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid3SsmCounter0Timeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid3SsmCounter0Timeout() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid3SsmCounter0Timeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid3SsmCounter0Timer(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid3SsmCounter0Timer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid3SsmCounter0Timer() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid3SsmCounter0Timer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid3SsmCounter1MessageID(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid3SsmCounter1MessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid3SsmCounter1MessageID() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid3SsmCounter1MessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid3SsmCounter1Timeout(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid3SsmCounter1Timeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid3SsmCounter1Timeout() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid3SsmCounter1Timeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid3SsmCounter1Timer(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid3SsmCounter1Timer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid3SsmCounter1Timer() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid3SsmCounter1Timer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid5SsmCounter0MessageID(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid5SsmCounter0MessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid5SsmCounter0MessageID() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid5SsmCounter0MessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid5SsmCounter0Timeout(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid5SsmCounter0Timeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid5SsmCounter0Timeout() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid5SsmCounter0Timeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid5SsmCounter0Timer(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid5SsmCounter0Timer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid5SsmCounter0Timer() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid5SsmCounter0Timer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid5SsmCounter1MessageID(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid5SsmCounter1MessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid5SsmCounter1MessageID() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid5SsmCounter1MessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid5SsmCounter1Timeout(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid5SsmCounter1Timeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid5SsmCounter1Timeout() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid5SsmCounter1Timeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid5SsmCounter1Timer(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid5SsmCounter1Timer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid5SsmCounter1Timer() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid5SsmCounter1Timer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid6SsmCounter0MessageID(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid6SsmCounter0MessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid6SsmCounter0MessageID() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid6SsmCounter0MessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid6SsmCounter0Timeout(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid6SsmCounter0Timeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid6SsmCounter0Timeout() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid6SsmCounter0Timeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid6SsmCounter0Timer(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid6SsmCounter0Timer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid6SsmCounter0Timer() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid6SsmCounter0Timer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid6SsmCounter1MessageID(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid6SsmCounter1MessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid6SsmCounter1MessageID() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid6SsmCounter1MessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid6SsmCounter1Timeout(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid6SsmCounter1Timeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid6SsmCounter1Timeout() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid6SsmCounter1Timeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid6SsmCounter1Timer(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid6SsmCounter1Timer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid6SsmCounter1Timer() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AcuMid6SsmCounter1Timer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AswSoftwarewareVersion(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AswSoftwarewareVersion.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_AswSoftwarewareVersion() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_AswSoftwarewareVersion.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_BootLoaderVersion(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_BootLoaderVersion.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_BootLoaderVersion() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_BootLoaderVersion.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_BswSoftwarewareVersion(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_BswSoftwarewareVersion.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_BswSoftwarewareVersion() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_BswSoftwarewareVersion.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_FrontCameraCalibrationStatus(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_FrontCameraCalibrationStatus.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_FrontCameraCalibrationStatus() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_FrontCameraCalibrationStatus.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_FrontCameraCanMessageID(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_FrontCameraCanMessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_FrontCameraCanMessageID() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_FrontCameraCanMessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_FrontCameraCanTimeout(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_FrontCameraCanTimeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_FrontCameraCanTimeout() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_FrontCameraCanTimeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_FrontCameraCanTimer(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_FrontCameraCanTimer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_FrontCameraCanTimer() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_FrontCameraCanTimer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_FrontCameraFailureStatus(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_FrontCameraFailureStatus.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_FrontCameraFailureStatus() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_FrontCameraFailureStatus.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_FrontRadarCalibrationStatus(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_FrontRadarCalibrationStatus.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_FrontRadarCalibrationStatus() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_FrontRadarCalibrationStatus.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_FrontRadarCanMessageID(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_FrontRadarCanMessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_FrontRadarCanMessageID() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_FrontRadarCanMessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_FrontRadarCanTimeout(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_FrontRadarCanTimeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_FrontRadarCanTimeout() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_FrontRadarCanTimeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_FrontRadarCanTimer(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_FrontRadarCanTimer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_FrontRadarCanTimer() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_FrontRadarCanTimer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_FrontRadarFailureStatus(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_FrontRadarFailureStatus.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_FrontRadarFailureStatus() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_FrontRadarFailureStatus.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_SystemStatusReserved1(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_SystemStatusReserved1.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_SystemStatusReserved1() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_SystemStatusReserved1.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_SystemStatusReserved2(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_SystemStatusReserved2.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_SystemStatusReserved2() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_SystemStatusReserved2.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_SystemStatusReserved3(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_SystemStatusReserved3.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_SystemStatusReserved3() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_SystemStatusReserved3.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_SystemStatusReserved4(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_SystemStatusReserved4.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_SystemStatusReserved4() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_SystemStatusReserved4.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_SystemStatusRollingCounter(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_SystemStatusRollingCounter.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_SystemStatusRollingCounter() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_SystemStatusRollingCounter.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3SsmCounter0MessageID(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid3SsmCounter0MessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3SsmCounter0MessageID() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid3SsmCounter0MessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3SsmCounter0Timeout(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid3SsmCounter0Timeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3SsmCounter0Timeout() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid3SsmCounter0Timeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3SsmCounter0Timer(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid3SsmCounter0Timer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3SsmCounter0Timer() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid3SsmCounter0Timer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3SsmCounter1MessageID(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid3SsmCounter1MessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3SsmCounter1MessageID() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid3SsmCounter1MessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3SsmCounter1Timeout(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid3SsmCounter1Timeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3SsmCounter1Timeout() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid3SsmCounter1Timeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3SsmCounter1Timer(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid3SsmCounter1Timer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3SsmCounter1Timer() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid3SsmCounter1Timer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3VcuCounter0MessageID(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid3VcuCounter0MessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3VcuCounter0MessageID() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid3VcuCounter0MessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3VcuCounter0Timeout(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid3VcuCounter0Timeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3VcuCounter0Timeout() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid3VcuCounter0Timeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3VcuCounter0Timer(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid3VcuCounter0Timer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3VcuCounter0Timer() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid3VcuCounter0Timer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3VcuCounter1MessageID(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid3VcuCounter1MessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3VcuCounter1MessageID() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid3VcuCounter1MessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3VcuCounter1Timeout(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid3VcuCounter1Timeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3VcuCounter1Timeout() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid3VcuCounter1Timeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3VcuCounter1Timer(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid3VcuCounter1Timer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3VcuCounter1Timer() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid3VcuCounter1Timer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid5SsmCounter0MessageID(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid5SsmCounter0MessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_VehMid5SsmCounter0MessageID() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid5SsmCounter0MessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid5SsmCounter0Timeout(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid5SsmCounter0Timeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_VehMid5SsmCounter0Timeout() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid5SsmCounter0Timeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid5SsmCounter0Timer(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid5SsmCounter0Timer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_VehMid5SsmCounter0Timer() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid5SsmCounter0Timer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid5SsmCounter1MessageID(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid5SsmCounter1MessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_VehMid5SsmCounter1MessageID() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid5SsmCounter1MessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid5SsmCounter1Timeout(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid5SsmCounter1Timeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_VehMid5SsmCounter1Timeout() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid5SsmCounter1Timeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid5SsmCounter1Timer(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid5SsmCounter1Timer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_VehMid5SsmCounter1Timer() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid5SsmCounter1Timer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid6SsmCounter0MessageID(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid6SsmCounter0MessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_VehMid6SsmCounter0MessageID() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid6SsmCounter0MessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid6SsmCounter0Timeout(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid6SsmCounter0Timeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_VehMid6SsmCounter0Timeout() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid6SsmCounter0Timeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid6SsmCounter0Timer(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid6SsmCounter0Timer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_VehMid6SsmCounter0Timer() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid6SsmCounter0Timer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid6SsmCounter1MessageID(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid6SsmCounter1MessageID.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_VehMid6SsmCounter1MessageID() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid6SsmCounter1MessageID.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid6SsmCounter1Timeout(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid6SsmCounter1Timeout.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_VehMid6SsmCounter1Timeout() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid6SsmCounter1Timeout.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid6SsmCounter1Timer(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid6SsmCounter1Timer.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FallbackSystemStatus_VehMid6SsmCounter1Timer() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FallbackSystemStatus_VehMid6SsmCounter1Timer.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FbAcuAvailable_FallbackSelfCheckStatus(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FbAcuAvailable_FallbackSelfCheckStatus.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FbAcuAvailable_FallbackSelfCheckStatus() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FbAcuAvailable_FallbackSelfCheckStatus.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FbAcuAvailable_FbAcuReserved(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FbAcuAvailable_FbAcuReserved.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FbAcuAvailable_FbAcuReserved() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FbAcuAvailable_FbAcuReserved.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FbAcuAvailable_FbAcuRollingCounter(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FbAcuAvailable_FbAcuRollingCounter.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FbAcuAvailable_FbAcuRollingCounter() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FbAcuAvailable_FbAcuRollingCounter.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FbAcuAvailable_McuStatus(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FbAcuAvailable_McuStatus.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FbAcuAvailable_McuStatus() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FbAcuAvailable_McuStatus.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FbAcuAvailable_Sensor1v1rStatus(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FbAcuAvailable_Sensor1v1rStatus.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FbAcuAvailable_Sensor1v1rStatus() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FbAcuAvailable_Sensor1v1rStatus.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_FbAcuAvailable_VehControlStatus(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FbAcuAvailable_VehControlStatus.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_FbAcuAvailable_VehControlStatus() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_FbAcuAvailable_VehControlStatus.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_SAS_Status_SAS_CalibrationSts(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_SAS_Status_SAS_CalibrationSts.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_SAS_Status_SAS_CalibrationSts() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_SAS_Status_SAS_CalibrationSts.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_SAS_Status_SAS_FailureSts(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_SAS_Status_SAS_FailureSts.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_SAS_Status_SAS_FailureSts() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_SAS_Status_SAS_FailureSts.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_SAS_Status_SAS_Status_AliveCounter(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_SAS_Status_SAS_Status_AliveCounter.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_SAS_Status_SAS_Status_AliveCounter() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_SAS_Status_SAS_Status_AliveCounter.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_SAS_Status_SAS_Status_Checksum(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_SAS_Status_SAS_Status_Checksum.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_SAS_Status_SAS_Status_Checksum() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_SAS_Status_SAS_Status_Checksum.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_SAS_Status_SAS_SteerWheelAngle(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_SAS_Status_SAS_SteerWheelAngle.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_SAS_Status_SAS_SteerWheelAngle() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_SAS_Status_SAS_SteerWheelAngle.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_SAS_Status_SAS_SteerWheelRotSpd(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_SAS_Status_SAS_SteerWheelRotSpd.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_SAS_Status_SAS_SteerWheelRotSpd() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_SAS_Status_SAS_SteerWheelRotSpd.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNomQf_C(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNomQf_C.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNomQf_C() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNomQf_C.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNom_C(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNom_C.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNom_C() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNom_C.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNomQf_C(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNomQf_C.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNomQf_C() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNomQf_C.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNom_C(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNom_C.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNom_C() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNom_C.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_SSMMid5CanFdFr03_1V1R_PrimALatDataRawSafeNom_C(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_SSMMid5CanFdFr03_1V1R_PrimALatDataRawSafeNom_C.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_SSMMid5CanFdFr03_1V1R_PrimALatDataRawSafeNom_C() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_SSMMid5CanFdFr03_1V1R_PrimALatDataRawSafeNom_C.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_VCU1Mid3CanFr03_1V1R_PrpsnTqDirAct_C(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VCU1Mid3CanFr03_1V1R_PrpsnTqDirAct_C.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_VCU1Mid3CanFr03_1V1R_PrpsnTqDirAct_C() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VCU1Mid3CanFr03_1V1R_PrpsnTqDirAct_C.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_VCU1Mid3CanFr06_ACU_CarTiGlb_A(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VCU1Mid3CanFr06_ACU_CarTiGlb_A.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_VCU1Mid3CanFr06_ACU_CarTiGlb_A() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VCU1Mid3CanFr06_ACU_CarTiGlb_A.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_VCU1Mid3CanFr08_1V1R_YawRate1Qf1_C(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VCU1Mid3CanFr08_1V1R_YawRate1Qf1_C.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_VCU1Mid3CanFr08_1V1R_YawRate1Qf1_C() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VCU1Mid3CanFr08_1V1R_YawRate1Qf1_C.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_VCU1Mid3CanFr08_1V1R_YawRate1_C(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VCU1Mid3CanFr08_1V1R_YawRate1_C.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_VCU1Mid3CanFr08_1V1R_YawRate1_C() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VCU1Mid3CanFr08_1V1R_YawRate1_C.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_VCU1Mid3CanFr36_1V1R_WhlLockStsLockSts_C(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VCU1Mid3CanFr36_1V1R_WhlLockStsLockSts_C.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_VCU1Mid3CanFr36_1V1R_WhlLockStsLockSts_C() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VCU1Mid3CanFr36_1V1R_WhlLockStsLockSts_C.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_VIMBMid6CanFdFr14_SG_AdSecWhlAgReqGroupSafe(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMBMid6CanFdFr14_SG_AdSecWhlAgReqGroupSafe.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_VIMBMid6CanFdFr14_SG_AdSecWhlAgReqGroupSafe() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMBMid6CanFdFr14_SG_AdSecWhlAgReqGroupSafe.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_VIMBMid6CanFdFr28_SG_AdSecALgtLimReqGroupSafe(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMBMid6CanFdFr28_SG_AdSecALgtLimReqGroupSafe.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_VIMBMid6CanFdFr28_SG_AdSecALgtLimReqGroupSafe() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMBMid6CanFdFr28_SG_AdSecALgtLimReqGroupSafe.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_VIMBMid6CanFdFr28_SG_SecAdNomALgtReqGroupSafe(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMBMid6CanFdFr28_SG_SecAdNomALgtReqGroupSafe.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_VIMBMid6CanFdFr28_SG_SecAdNomALgtReqGroupSafe() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMBMid6CanFdFr28_SG_SecAdNomALgtReqGroupSafe.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_VIMBMid6CanFdFr29_SG_SecAdWhlLockReq(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMBMid6CanFdFr29_SG_SecAdWhlLockReq.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_VIMBMid6CanFdFr29_SG_SecAdWhlLockReq() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMBMid6CanFdFr29_SG_SecAdWhlLockReq.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr04_IDcDcAvlLoSideExt(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr04_IDcDcAvlLoSideExt.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_VIMMid3CanFr04_IDcDcAvlLoSideExt() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr04_IDcDcAvlLoSideExt.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr04_IDcDcAvlMaxLoSideExt(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr04_IDcDcAvlMaxLoSideExt.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_VIMMid3CanFr04_IDcDcAvlMaxLoSideExt() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr04_IDcDcAvlMaxLoSideExt.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr04_SG_HmiAutnmsSts(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr04_SG_HmiAutnmsSts.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_VIMMid3CanFr04_SG_HmiAutnmsSts() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr04_SG_HmiAutnmsSts.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr04_UDcDcAvlLoSideExt(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr04_UDcDcAvlLoSideExt.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_VIMMid3CanFr04_UDcDcAvlLoSideExt() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr04_UDcDcAvlLoSideExt.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr07_SG_AutnmsDrvStReq(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr07_SG_AutnmsDrvStReq.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_VIMMid3CanFr07_SG_AutnmsDrvStReq() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr07_SG_AutnmsDrvStReq.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr08_SG_VehOperStReq(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr08_SG_VehOperStReq.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_VIMMid3CanFr08_SG_VehOperStReq() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr08_SG_VehOperStReq.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr09_SG_AdDirReq(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr09_SG_AdDirReq.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_VIMMid3CanFr09_SG_AdDirReq() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr09_SG_AdDirReq.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr09_SG_AdStandStillReq(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr09_SG_AdStandStillReq.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_VIMMid3CanFr09_SG_AdStandStillReq() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr09_SG_AdStandStillReq.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr09_VehUsgStReq(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr09_VehUsgStReq.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_VIMMid3CanFr09_VehUsgStReq() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr09_VehUsgStReq.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr11_SG_AdFusedFricEstimn(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr11_SG_AdFusedFricEstimn.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_VIMMid3CanFr11_SG_AdFusedFricEstimn() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr11_SG_AdFusedFricEstimn.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr11_SG_AdpLiReqFromAPI(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr11_SG_AdpLiReqFromAPI.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_VIMMid3CanFr11_SG_AdpLiReqFromAPI() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr11_SG_AdpLiReqFromAPI.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr11_SG_SwtExtrLiFromAPI(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr11_SG_SwtExtrLiFromAPI.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_VIMMid3CanFr11_SG_SwtExtrLiFromAPI() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr11_SG_SwtExtrLiFromAPI.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr13_AdSetSpd(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr13_AdSetSpd.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_VIMMid3CanFr13_AdSetSpd() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr13_AdSetSpd.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr13_SG_AdFreeDst(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr13_SG_AdFreeDst.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_VIMMid3CanFr13_SG_AdFreeDst() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr13_SG_AdFreeDst.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr13_SG_AdWhlLockReq(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr13_SG_AdWhlLockReq.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_VIMMid3CanFr13_SG_AdWhlLockReq() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr13_SG_AdWhlLockReq.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr14_SG_AdNomALgtReqGroupSafe(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr14_SG_AdNomALgtReqGroupSafe.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_VIMMid3CanFr14_SG_AdNomALgtReqGroupSafe() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr14_SG_AdNomALgtReqGroupSafe.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr15_SG_AdPrimALgtLimReqGroupSafe(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr15_SG_AdPrimALgtLimReqGroupSafe.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_VIMMid3CanFr15_SG_AdPrimALgtLimReqGroupSafe() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid3CanFr15_SG_AdPrimALgtLimReqGroupSafe.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_VIMMid5CanFdFr12_SG_AdPrimWhlAgReqGroupSafe(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid5CanFdFr12_SG_AdPrimWhlAgReqGroupSafe.value = *(data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_VIMMid5CanFdFr12_SG_AdPrimWhlAgReqGroupSafe() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_VIMMid5CanFdFr12_SG_AdPrimWhlAgReqGroupSafe.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_YRS1_YRS1_AliveCounter(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_YRS1_YRS1_AliveCounter.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_YRS1_YRS1_AliveCounter() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_YRS1_YRS1_AliveCounter.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_YRS1_YRS1_Checksum(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_YRS1_YRS1_Checksum.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_YRS1_YRS1_Checksum() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_YRS1_YRS1_Checksum.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_YRS1_YRS_LateralAcce(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_YRS1_YRS_LateralAcce.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_YRS1_YRS_LateralAcce() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_YRS1_YRS_LateralAcce.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_YRS1_YRS_LateralSensorState(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_YRS1_YRS_LateralSensorState.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_YRS1_YRS_LateralSensorState() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_YRS1_YRS_LateralSensorState.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_YRS1_YRS_YawRate(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_YRS1_YRS_YawRate.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_YRS1_YRS_YawRate() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_YRS1_YRS_YawRate.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_YRS1_YRS_YawRateSensorState(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_YRS1_YRS_YawRateSensorState.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_YRS1_YRS_YawRateSensorState() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_YRS1_YRS_YawRateSensorState.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_YRS2_YRS2_Checksum(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_YRS2_YRS2_Checksum.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_YRS2_YRS2_Checksum() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_YRS2_YRS2_Checksum.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_YRS2_YRS_AliveCounter(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_YRS2_YRS_AliveCounter.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_YRS2_YRS_AliveCounter() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_YRS2_YRS_AliveCounter.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_YRS2_YRS_LongitAcce(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_YRS2_YRS_LongitAcce.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_YRS2_YRS_LongitAcce() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_YRS2_YRS_LongitAcce.value \
  )


#  define Rte_IWrite_FallbackSigOutput_Init_YRS2_YRS_LongitSensorState(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_YRS2_YRS_LongitSensorState.value = (data) \
  )


#  define Rte_IWriteRef_FallbackSigOutput_Init_YRS2_YRS_LongitSensorState() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_FallbackSigOutput_FallbackSigOutput_Init.Rte_YRS2_YRS_LongitSensorState.value \
  )


# endif /* !defined(RTE_CORE) */


# define FallbackSigOutput_START_SEC_CODE
//# include "FallbackSigOutput_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/**********************************************************************************************************************
 * Runnable entities
 *********************************************************************************************************************/

# ifndef RTE_CORE
#  define RTE_RUNNABLE_FallbackSigOutput_10ms_Runnable FallbackSigOutput_10ms_Runnable
#  define RTE_RUNNABLE_FallbackSigOutput_Init FallbackSigOutput_Init
# endif

FUNC(void, FallbackSigOutput_CODE) FallbackSigOutput_10ms_Runnable(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */
FUNC(void, FallbackSigOutput_CODE) FallbackSigOutput_Init(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */

# define FallbackSigOutput_STOP_SEC_CODE
//# include "FallbackSigOutput_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

# ifdef __cplusplus
} /* extern "C" */
# endif /* __cplusplus */

#endif /* RTE_FALLBACKSIGOUTPUT_H */

/**********************************************************************************************************************
 MISRA 2012 violations and justifications
 *********************************************************************************************************************/

/* module specific MISRA deviations:
   MD_Rte_0786:  MISRA rule: Rule5.5
     Reason:     Same macro and idintifier names in first 63 characters are required to meet AUTOSAR spec.
     Risk:       No functional risk.
     Prevention: Not required.

   MD_Rte_3449:  MISRA rule: Rule8.5
     Reason:     Schedulable entities are declared by the RTE and also by the BSW modules.
     Risk:       No functional risk.
     Prevention: Not required.

   MD_Rte_3451:  MISRA rule: Rule8.5
     Reason:     Schedulable entities are declared by the RTE and also by the BSW modules.
     Risk:       No functional risk.
     Prevention: Not required.

*/
