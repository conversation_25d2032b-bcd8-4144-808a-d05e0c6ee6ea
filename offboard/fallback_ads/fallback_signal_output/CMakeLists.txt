###########
## Build ##
###########
# clangd 
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

add_subdirectory(checksum)

include_directories(checksum)

add_library(fallback_sig_output
  FallbackSigOutput_data.c
  FallbackSigOutput.c
)

target_link_libraries(fallback_sig_output
 PUBLIC
 checksum
)

#############
## Install ##
#############
install(TARGETS fallback_sig_output
  EXCLUDE_FROM_ALL
  EXPORT fb_l2_node
)
install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
  EXCLUDE_FROM_ALL
  DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
  FILES_MATCHING # install only matched files
  PATTERN "*.h" # select header files
)
