#include "planner/world_model/regional_map/stuck_avoidance/waypoint_assist_fix_point_checker.h"

#include <algorithm>
#include <utility>

namespace planner {
namespace lane_selection {
namespace {
// Maximum distance to consider as near a fix point.
constexpr double kMaxDistForNearFixPoint = 5.0;
// Maximum distance to compare two regional section sequence.
constexpr double kMaxDistForRegionalSectionSequence = 50.0;
// Maximum time interval for empirical fix point from cloud to be effective.
constexpr int64_t kEffectiveTimeIntervalInSecond = 86400;
// Event type for fix point source
constexpr char kFixPointSourceEvent[] = "receive_fix_point_from_cloud";
// Returns the trimmed section along the sequence within maximum distance
// threshold.
routing::utility::OrderInfo GetOrderInfoFromMetaData(
    const StuckScenarioCheckMetaData* meta_data) {
  if (meta_data != nullptr && meta_data->global_route_solution != nullptr) {
    return routing::utility::OrderInfo(
        meta_data->global_route_solution->proposed_route_query(),
        /*command_type=*/std::nullopt,
        /*command_purpose=*/std::nullopt);
  }
  return routing::utility::OrderInfo();
}

const pnc_map::Section* GetTargetSectionWithinRange(
    const std::vector<const pnc_map::Section*>& regional_map_trimmed_sections,
    const pnc_map::Section* start_section) {
  const auto& curr_iter = std::find_if(
      regional_map_trimmed_sections.begin(),
      regional_map_trimmed_sections.end(),
      [start_section](const pnc_map::Section* section) {
        return section != nullptr && section->id() == start_section->id();
      });
  if (curr_iter == regional_map_trimmed_sections.end()) {
    return nullptr;
  }
  double accumulate_distance = 0.0;
  for (auto iter = std::next(curr_iter);
       iter != regional_map_trimmed_sections.end(); ++iter) {
    if (*iter == nullptr) {
      return nullptr;
    }
    accumulate_distance += (*iter)->GetLength();
    if (accumulate_distance >= kMaxDistForRegionalSectionSequence) {
      return *iter;
    }
  }
  // If the remaining sections's length is less than the threshold, return the
  // last section.
  return regional_map_trimmed_sections.back();
}

// Returns true if two section sequences can be considered as matched within
// range.
bool IsSectionSequencesMatched(
    const std::vector<int64>& regional_map_trimmed_section_ids,
    const std::vector<const pnc_map::Section*>& regional_map_trimmed_sections,
    const pnc_map::Section* current_section) {
  if (regional_map_trimmed_section_ids.empty() ||
      regional_map_trimmed_sections.empty() || current_section == nullptr) {
    return false;
  }
  if (std::find_if(regional_map_trimmed_section_ids.begin(),
                   regional_map_trimmed_section_ids.end(),
                   [current_section](const int64_t section_id) {
                     return section_id == current_section->id();
                   }) == regional_map_trimmed_section_ids.end()) {
    return false;
  }
  const pnc_map::Section* target_section = GetTargetSectionWithinRange(
      regional_map_trimmed_sections, current_section);
  if (target_section == nullptr) {
    return false;
  }
  // Consider as matched if both the source section and target section
  // within range match.
  if (std::find_if(regional_map_trimmed_section_ids.begin(),
                   regional_map_trimmed_section_ids.end(),
                   [target_section](const int64_t section_id) {
                     return section_id == target_section->id();
                   }) == regional_map_trimmed_section_ids.end()) {
    return false;
  }
  return true;
}

std::string GetDataSourceTypeString(
    hdmap::EmpiricalDataSourceType source_type) {
  switch (source_type) {
    case hdmap::DATA_SOURCE_TYPE_APOLLO_CONFIG:
      return "APOLLO_CONFIG";
    case hdmap::DATA_SOURCE_TYPE_VEHICLE:
      return "VEHICLE";
    default:
      return "UNKNOWN";
  }
}
// Returns true if current state matches the fix point.
bool IsFixPointMatched(
    const WaypointAssistFixPoint& fix_point, const pnc_map::Lane* current_lane,
    const double pose_arc_length,
    const std::vector<const pnc_map::Section*>& regional_map_trimmed_sections) {
  if (!IsSectionSequencesMatched(fix_point.regional_map_trimmed_section_ids,
                                 regional_map_trimmed_sections,
                                 current_lane->section())) {
    return false;
  }
  for (const auto& lane_segment : fix_point.lane_segments) {
    if (lane_segment.lane == nullptr ||
        lane_segment.lane->id() != current_lane->id()) {
      continue;
    }
    if (math::IsInRange(pose_arc_length,
                        lane_segment.arc_length_range_m.start_pos(),
                        lane_segment.arc_length_range_m.end_pos())) {
      return true;
    }
  }
  return false;
}

WaypointAssistFixPoint ConvertPbToWaypointAssistFixPoint(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const hdmap::WaypointAssistFixPoint& fix_point_pb) {
  WaypointAssistFixPoint waypoint_assist_fix_point;
  const std::vector<const pnc_map::Lane*> lanes =
      joint_pnc_map_service.GetLaneSequence(fix_point_pb.lane_ids());
  const math::geometry::Point2d point(fix_point_pb.position().x(),
                                      fix_point_pb.position().y());
  for (const pnc_map::Lane* lane : lanes) {
    if (lane == nullptr) {
      continue;
    }
    const double arc_length_in_lane = lane->GetArclength(point);
    waypoint_assist_fix_point.lane_segments.emplace_back(
        lane, std::max(0.0, arc_length_in_lane - kMaxDistForNearFixPoint),
        std::min(arc_length_in_lane + kMaxDistForNearFixPoint, lane->length()));
  }
  for (const auto section_id :
       fix_point_pb.regional_map_trimmed_section_ids()) {
    waypoint_assist_fix_point.regional_map_trimmed_section_ids.push_back(
        section_id);
  }
  for (const auto& point : fix_point_pb.reference_points()) {
    waypoint_assist_fix_point.reference_points.push_back(point);
  }
  return waypoint_assist_fix_point;
}
}  // namespace

void WaypointAssistFixPointChecker::LoadState(
    const pnc_map::PncMapService& pnc_map_service,
    const pb::RelaxLaneSequenceScenarioParam& from_pb) {
  StuckScenarioBaseChecker::LoadState(pnc_map_service, from_pb);
  if (from_pb.has_fix_point_from_cloud()) {
    for (const auto& fix_point_pb :
         from_pb.fix_point_from_cloud().waypoint_assist_fix_points()) {
      WaypointAssistFixPoint waypoint_assist_fix_point;
      waypoint_assist_fix_point.LoadFromPB(pnc_map_service, fix_point_pb);
      waypoint_assist_fix_points_from_cloud_.push_back(
          std::move(waypoint_assist_fix_point));
    }
    latest_fix_points_from_cloud_timestamp_ =
        from_pb.fix_point_from_cloud().latest_timestamp();
  }
}

void WaypointAssistFixPointChecker::DumpState(
    pb::StuckScenarioChecker& to_pb) const {
  StuckScenarioBaseChecker::DumpState(to_pb);
  pb::RelaxLaneSequenceScenarioParam* scenario_param_pb =
      to_pb.mutable_scenario_param();
  if (!waypoint_assist_fix_points_from_cloud_.empty()) {
    for (const auto& fix_point : waypoint_assist_fix_points_from_cloud_) {
      fix_point.DumpToPB(scenario_param_pb->mutable_fix_point_from_cloud()
                             ->mutable_waypoint_assist_fix_points()
                             ->Add());
    }
    scenario_param_pb->mutable_fix_point_from_cloud()->set_latest_timestamp(
        latest_fix_points_from_cloud_timestamp_);
  }
}

void WaypointAssistFixPointChecker::UpdateData(
    const StuckScenarioCheckMetaData& meta_data) {
  meta_data_ = &meta_data;
  std::vector<WaypointAssistFixPoint> fix_point_msg_from_cloud;
  const bool success = LoadFixPointMsgFromCloud(&fix_point_msg_from_cloud);
  if (success) {
    waypoint_assist_fix_points_from_cloud_ = fix_point_msg_from_cloud;
    latest_fix_points_from_cloud_timestamp_ = base::Now();
  } else {
    // Reset the data when the empirical publish data is abnormal for a long
    // time.
    if (!waypoint_assist_fix_points_from_cloud_.empty() &&
        (base::Now() - latest_fix_points_from_cloud_timestamp_) / 1000 >
            kEffectiveTimeIntervalInSecond) {
      waypoint_assist_fix_points_from_cloud_ = {};
      latest_fix_points_from_cloud_timestamp_ = base::Now();
    }
  }
}

void WaypointAssistFixPointChecker::UpdateScenario() {
  switch (scenario_param_.stage) {
    case pb::StuckAvoidanceStage::kStuckAvoidanceIdle: {
      if (scenario_param_.terminate_reason !=
          pb::StuckAvoidanceParameter_TerminateReason_kNone) {
        scenario_param_.terminate_reason =
            pb::StuckAvoidanceParameter_TerminateReason_kNone;
      }
      const auto old_stage = scenario_param_.stage;
      ReasonNewScenarioParam();
      if (old_stage != scenario_param_.stage) {
        UpdateMonitorEvent(
            scenario_param_, "RouteUnstuckScenarioTriggered",
            meta_data_->global_route_solution->route_solution().mission_id());
      }
      break;
    }
    case pb::StuckAvoidanceStage::kStuckAvoidancePreparing: {
      UpdateMatchedFixPoints();
      if (matched_fix_points_.empty()) {
        ConstructParamForTermination(
            pb::StuckAvoidanceParameter_TerminateReason_kLeaveStuckRegion);
        break;
      }
      if (ShouldExecuteStuckAvoidance()) {
        scenario_param_.stage =
            pb::StuckAvoidanceStage::kStuckAvoidanceProcessing;
        if (!meta_data_->has_started_remote_assist) {
          scenario_param_.waypoint_assist_request
              .expect_trigger_waypoint_assist = true;
        }
      }
      break;
    }
    case pb::StuckAvoidanceStage::kStuckAvoidanceProcessing: {
      UpdateWaypointAssistRequest();
      break;
    }
    default:
      LOG(ERROR) << "Invalid stage.";
      break;
  }
}

bool WaypointAssistFixPointChecker::ShouldDumpState() const {
  if (StuckScenarioBaseChecker::ShouldDumpState()) {
    return true;
  }
  return !waypoint_assist_fix_points_from_cloud_.empty();
}

void WaypointAssistFixPointChecker::ReasonNewScenarioParam() {
  if (!PreCheck()) {
    return;
  }
  UpdateMatchedFixPoints();
  if (!matched_fix_points_.empty()) {
    scenario_param_.stage = pb::StuckAvoidanceStage::kStuckAvoidancePreparing;
    scenario_param_.start_stuck_timestamp_ms = base::Now();
    scenario_param_.scenario_type =
        pb::RelaxLaneSequenceScenarioType::kStuckAtFixPoint;

    if (ShouldExecuteStuckAvoidance()) {
      scenario_param_.stage =
          pb::StuckAvoidanceStage::kStuckAvoidanceProcessing;
      scenario_param_.waypoint_assist_request.expect_trigger_waypoint_assist =
          true;
    }
  }
}

bool WaypointAssistFixPointChecker::PreCheck() const {
  return FLAGS_planning_enable_waypoint_assist_request_to_avoid_stuck &&
         (!waypoint_assist_fix_points_from_config_.empty() ||
          !waypoint_assist_fix_points_from_cloud_.empty()) &&
         meta_data_->waypoint_assist_phase == pb::AssistManeuverPhase::kIdle &&
         meta_data_->robot_state_snapshot->speed() <=
             constants::kDefaultAgentNearStaticSpeedInMps;
}

void WaypointAssistFixPointChecker::UpdateMatchedFixPoints() {
  matched_fix_points_.clear();
  for (const auto& fix_point : waypoint_assist_fix_points_from_config_) {
    if (IsFixPointMatched(fix_point, meta_data_->current_lane_candidate->lane(),
                          meta_data_->current_lane_candidate
                              ->position_arclength_from_lane_start(),
                          *meta_data_->regional_map_trimmed_sections)) {
      matched_fix_points_.push_back(fix_point);
    }
  }
  for (const auto& fix_point : waypoint_assist_fix_points_from_cloud_) {
    if (IsFixPointMatched(fix_point, meta_data_->current_lane_candidate->lane(),
                          meta_data_->current_lane_candidate
                              ->position_arclength_from_lane_start(),
                          *meta_data_->regional_map_trimmed_sections)) {
      matched_fix_points_.push_back(fix_point);
    }
  }
}

void WaypointAssistFixPointChecker::ConstructNewWaypointAssistRequest() {
  WaypointAssistRequest& request = scenario_param_.waypoint_assist_request;
  if (!request.waypoint_assist_responses.empty()) {
    request.waypoint_assist_responses.clear();
  }
  request.waypoint_assist_responses.reserve(matched_fix_points_.size());
  pb::AssistResponse response;
  response.set_is_unidirectional(true);
  pb::WaypointBasedAssistCommand* assist_command =
      response.mutable_waypoint_based_assist_command();
  assist_command->set_timestamp(math::Ns2Ms(base::Now()));
  assist_command->set_operation_command(
      pb::OperationCommand::kEnterWayPointAssistMode);
  assist_command->set_sender(pb::Sender::kRouting);
  for (size_t idx = 0; idx < matched_fix_points_.size(); ++idx) {
    request.waypoint_assist_responses.emplace_back(response);
  }
}

void WaypointAssistFixPointChecker::UpdateWaypointAssistRequest() {
  WaypointAssistRequest& request = scenario_param_.waypoint_assist_request;
  switch (meta_data_->waypoint_assist_phase) {
    case pb::AssistManeuverPhase::kIdle:
      UpdateMatchedFixPoints();
      if (matched_fix_points_.empty()) {
        ConstructParamForTermination(
            pb::StuckAvoidanceParameter_TerminateReason_kLeaveStuckRegion);
        return;
      }
      if (meta_data_->has_started_remote_assist) {
        request.expect_trigger_waypoint_assist = false;
        if (FLAGS_planning_enable_reason_reference_points_to_avoid_stuck &&
            meta_data_->has_tele_ops_accpeted_remote_assist_req) {
          ConstructNewWaypointAssistRequest();
        }
      }
      break;
    case pb::AssistManeuverPhase::kWaitingPoints:
      if (request.waypoint_assist_responses.empty()) {
        break;
      }
      DCHECK(matched_fix_points_.size() ==
             request.waypoint_assist_responses.size());
      for (size_t idx = 0; idx < matched_fix_points_.size(); ++idx) {
        DCHECK(idx < request.waypoint_assist_responses.size());
        if (idx >= request.waypoint_assist_responses.size()) {
          break;
        }
        pb::AssistResponse& response = request.waypoint_assist_responses[idx];
        if (response.waypoint_based_assist_command().operation_command() ==
            pb::OperationCommand::kPrepareReferencePoints) {
          break;
        }
        pb::WaypointBasedAssistCommand* assist_command =
            response.mutable_waypoint_based_assist_command();
        assist_command->set_operation_command(
            pb::OperationCommand::kPrepareReferencePoints);
        for (const auto& point : matched_fix_points_[idx].reference_points) {
          *assist_command->add_reference_points() = point;
        }
      }
      UpdateMonitorEvent(
          scenario_param_, "SendWaypointAssistRequest",
          meta_data_->global_route_solution->route_solution().mission_id());
      break;
    case pb::AssistManeuverPhase::kPreparingPath:
    case pb::AssistManeuverPhase::kWaitingConfirm:
    case pb::AssistManeuverPhase::kFollowingPath:
    case pb::AssistManeuverPhase::kExitReasoning:
      request.waypoint_assist_responses.clear();
      break;
    default:
      break;
  }
  if (!request.expect_trigger_waypoint_assist &&
      request.waypoint_assist_responses.empty()) {
    ConstructParamForTermination(
        pb::StuckAvoidanceParameter::kFinishWaypointAssist);
  }
}

std::vector<WaypointAssistFixPoint>
WaypointAssistFixPointChecker::LoadFixPointMsgFromConfig(
    const pnc_map::PncMapService& pnc_map_service,
    const pb::StuckAvoidanceConfig& stuck_avoidance_config) const {
  if (stuck_avoidance_config.region_fix_points().empty()) {
    return {};
  }
  pnc_map::JointPncMapService joint_pnc_map_service(&pnc_map_service);
  std::vector<WaypointAssistFixPoint> waypoint_assist_fix_points;
  for (const auto& region_fix_point :
       stuck_avoidance_config.region_fix_points()) {
    if (region_fix_point.region().compare(pnc_map_service.map_region()) != 0) {
      continue;
    }
    for (const auto& fix_point :
         region_fix_point.waypoint_assist_fix_points()) {
      const WaypointAssistFixPoint waypoint_assist_fix_point =
          ConvertPbToWaypointAssistFixPoint(joint_pnc_map_service, fix_point);
      if (!waypoint_assist_fix_point.lane_segments.empty()) {
        waypoint_assist_fix_points.push_back(waypoint_assist_fix_point);
      }
    }
  }
  return waypoint_assist_fix_points;
}

bool WaypointAssistFixPointChecker::LoadFixPointMsgFromCloud(
    std::vector<WaypointAssistFixPoint>* fix_points_from_cloud) const {
  if (meta_data_ == nullptr || meta_data_->joint_pnc_map_service == nullptr ||
      meta_data_->empirical_publish_data_from_cloud == nullptr ||
      fix_points_from_cloud == nullptr) {
    return {};
  }
  if (meta_data_->empirical_publish_data_from_cloud->timestamp() <=
      latest_fix_points_from_cloud_timestamp_) {
    return false;
  }

  const auto order_info = GetOrderInfoFromMetaData(meta_data_);
  for (const auto& publish_data : meta_data_->empirical_publish_data_from_cloud
                                      ->empirical_info_publish_data_list()) {
    if (publish_data.data_type() !=
        hdmap::PUBLISH_DATA_TYPE_FIX_POINT_AUTO_UNSTUCK) {
      continue;
    }
    if (publish_data.distribute_time() <=
        latest_fix_points_from_cloud_timestamp_) {
      continue;
    }

    if (publish_data.has_fix_point_auto_unstuck_publish_msg()) {
      // Record data source type with order info
      routing::utility::AddRtEventAsJson<rt_event::planner::RouteUnstuck>(
          order_info, "event_type", kFixPointSourceEvent, "source_type",
          GetDataSourceTypeString(publish_data.data_source_type()), "timestamp",
          std::to_string(publish_data.distribute_time()));
      const WaypointAssistFixPoint waypoint_assist_fix_point =
          ConvertPbToWaypointAssistFixPoint(
              *meta_data_->joint_pnc_map_service,
              publish_data.fix_point_auto_unstuck_publish_msg());
      if (!waypoint_assist_fix_point.lane_segments.empty()) {
        fix_points_from_cloud->push_back(waypoint_assist_fix_point);
      }
    }
  }
  return true;
}

}  // namespace lane_selection
}  // namespace planner
