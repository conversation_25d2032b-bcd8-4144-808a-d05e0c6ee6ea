package(default_visibility = ["//visibility:public"])

cc_library(
    name = "gpiod",
    srcs = [
        "src/acu_orin_module.cc",
        "src/acu_orin_pciesw.cc",
    ],
    hdrs = [
        "include/acu_dv_protocal.hpp",
        "include/acu_mt_protocal.hpp",
        "include/acu_orin_module.h",
        "include/acu_orin_pciesw.h",
        "include/base_tools.hpp",
        "include/gc_callback.hpp",
        "include/gpiod_interface.hpp",
        "include/serial_port.hpp",
        "include/vh_glog.hpp",
    ],
    includes = ["include"],
    strip_include_prefix = "include",
    linkopts = [
        "-lgpiod",
    ],
    deps = [
        "@voy-sdk//:glog",
    ],
    target_compatible_with = select({
    "@//bazel/platforms:is_ubuntu22_arm64": [],
    "//conditions:default": ["@platforms//:incompatible"],
}),
)