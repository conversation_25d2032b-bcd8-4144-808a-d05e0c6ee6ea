#include "planner/path/reasoning/agent_intention/reasoner/pedestrian_path_reasoner/pedestrian_path_reasoner.h"

#include <algorithm>
#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "planner/path/reasoning/agent_intention/reasoner/reasoner_util.h"
#include "trace/trace.h"
#include "voy_trace/trace_planner.h"

namespace planner {
namespace path {
namespace {

// When ped speed is less than this, we can consider it as static.
constexpr double kMaxStaticPedSpeedMps = 1.3;

/*
-----------____----------  left boundary
      ego /____/
                ____
~~~~~~~~~~~~~~ |____|~~~~~~~~  reference_line
           hard_blocking agent
-------------------------  right boundary
*/
// Returns true when Ego should nudge even if the pedestrian is hard blocking.
// Now we consider two cases:
// (1) As shown in the figure above, the ped is beside ego vehicle.
// (2) For child who is in front of <PERSON><PERSON> and <PERSON><PERSON> can not safely yield (for
// example, crossing child from occluded region), we should nudge to avoid
// collision.
bool ShouldNudgeForHardBlockingSequences(
    const pb::TrajectoryReasoningInfo& reasoning_info,
    const double ego_along_track_speed_mps,
    const double agent_along_track_speed_mps) {
  if (reasoning_info.tracked_side_to_ego() != math::pb::kOn) return true;

  // For child that's in front of Ego, nudge if Ego can not safely yield to it.
  if (reasoning_info.is_agent_in_front_of_front_bumper() &&
      reasoning_info.is_child_ped()) {
    const double safe_longitudinal_distance = CalculateSafeLongitudinalDistance(
        ego_along_track_speed_mps, agent_along_track_speed_mps,
        /*is_front_of_ego=*/true, planner::path::RssConfig());
    return reasoning_info.plan_init_state_distance() <
           safe_longitudinal_distance;
  }

  return false;
}

}  // namespace

bool PedestrianPathReasoner::PreReason(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const AgentSLBoundary& /* object_sl_boundary */,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  trajectory_intention_data.reasoner_id = reasoner_id_;

  switch (blocking_state_data.blocking_sequence_type) {
    case pb::BlockingSequenceType::kNonBlocking:
      return PreReasonForNonBlockingSequences(
          agent_inlane_state, blocking_state_data, trajectory_intention_data);
    case pb::BlockingSequenceType::kPartialBlocking:
      return PreReasonForSoftBlockingSequences(
          agent_inlane_state, blocking_state_data, trajectory_intention_data);
    case pb::BlockingSequenceType::kCompleteBlocking:
      return PreReasonForHardBlockingSequences(
          agent_inlane_state, blocking_state_data, trajectory_intention_data);
    case pb::BlockingSequenceType::kMixedBlocking:
      return PreReasonForMixedBlockingSequences(
          agent_inlane_state, blocking_state_data, trajectory_intention_data);
    default:
      DCHECK(false) << "unsupported blocking sequence type";
      return PreReasonForNonBlockingSequences(
          agent_inlane_state, blocking_state_data, trajectory_intention_data);
  }
}

bool PedestrianPathReasoner::PreReason(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const AgentSLBoundary& /* object_sl_boundary */,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  trajectory_intention_data.reasoner_id = reasoner_id_;

  switch (blocking_state_data.blocking_sequence_type) {
    case pb::BlockingSequenceType::kNonBlocking:
      return PreReasonForNonBlockingSequences(object_occupancy_state,
                                              blocking_state_data,
                                              trajectory_intention_data);
    case pb::BlockingSequenceType::kPartialBlocking:
      return PreReasonForSoftBlockingSequences(object_occupancy_state,
                                               blocking_state_data,
                                               trajectory_intention_data);
    case pb::BlockingSequenceType::kCompleteBlocking:
      return PreReasonForHardBlockingSequences(object_occupancy_state,
                                               blocking_state_data,
                                               trajectory_intention_data);
    case pb::BlockingSequenceType::kMixedBlocking:
      return PreReasonForMixedBlockingSequences(object_occupancy_state,
                                                blocking_state_data,
                                                trajectory_intention_data);
    default:
      DCHECK(false) << "unsupported blocking sequence type";
      return PreReasonForNonBlockingSequences(object_occupancy_state,
                                              blocking_state_data,
                                              trajectory_intention_data);
  }
}

bool PedestrianPathReasoner::PreReasonForHardBlockingSequences(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  pb::BlockingSequence blocking_sequence = GetStartEndBlockingSequence(
      blocking_state_data.blocking_sequences,
      /*blocking_state=*/pb::LateralBlockingState::HARD_BLOCKING);
  trajectory_intention_data.blocking_sequence_type =
      pb::BlockingSequenceType::kCompleteBlocking;
  trajectory_intention_data.blocking_sequences =
      std::vector<pb::BlockingSequence>{blocking_sequence};

  const double ped_cross_track_speed_mps =
      agent_inlane_state.tracked_state.inlane_param.cross_track_speed_mps;

  // For ped, when (1) we think Ego can nudge even if it's hard blocking,
  // or (2) in dangerous scenarios. We give static nudge decision based on
  // cross track speed.
  if (ShouldNudgeForHardBlockingSequences(
          blocking_state_data.reasoning_info, ego_param_.along_track_speed_mps,
          agent_inlane_state.tracked_state.inlane_param
              .along_track_speed_mps) ||
      ((blocking_state_data.reasoning_info.is_cut_in() ||
        blocking_state_data.reasoning_info.is_crossing()) &&
       (ShouldTreatAgentAsStationary(blocking_state_data.tracked_blocking_info,
                                     blocking_state_data.reasoning_info)))) {
    trajectory_intention_data.is_static =
        abs(ped_cross_track_speed_mps) < kMaxStaticPedSpeedMps;
    trajectory_intention_data.intention_type = pb::TrajectoryState::MIXED;
    return true;
  }

  return true;
}

bool PedestrianPathReasoner::PreReasonForHardBlockingSequences(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  pb::BlockingSequence blocking_sequence = GetStartEndBlockingSequence(
      blocking_state_data.blocking_sequences,
      /*blocking_state=*/pb::LateralBlockingState::HARD_BLOCKING);
  trajectory_intention_data.blocking_sequence_type =
      pb::BlockingSequenceType::kCompleteBlocking;
  trajectory_intention_data.blocking_sequences =
      std::vector<pb::BlockingSequence>{blocking_sequence};

  const double ped_cross_track_speed_mps =
      object_occupancy_state.current_snapshot_info()
          .object_occupancy_param()
          .cross_track_speed_mps;

  // For ped, when (1) we think Ego can nudge even if it's hard blocking,
  // or (2) in dangerous scenarios. We give static nudge decision based on
  // cross track speed.
  if (ShouldNudgeForHardBlockingSequences(
          blocking_state_data.reasoning_info, ego_param_.along_track_speed_mps,
          object_occupancy_state.current_snapshot_info()
              .object_occupancy_param()
              .along_track_speed_mps) ||
      ((blocking_state_data.reasoning_info.is_cut_in() ||
        blocking_state_data.reasoning_info.is_crossing()) &&
       (ShouldTreatAgentAsStationary(blocking_state_data.tracked_blocking_info,
                                     blocking_state_data.reasoning_info)))) {
    trajectory_intention_data.is_static =
        abs(ped_cross_track_speed_mps) < kMaxStaticPedSpeedMps;
    trajectory_intention_data.intention_type = pb::TrajectoryState::MIXED;
    return true;
  }

  return true;
}

bool PedestrianPathReasoner::PreReasonForMixedBlockingSequences(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  // TODO(yongbing): add more logic for cut-in, merge and crosssing scenarios
  // for pedestrian. Try to nudge the first soft_blocking part.
  const std::vector<pb::BlockingSequence>& blocking_sequences =
      blocking_state_data.blocking_sequences;
  if (blocking_sequences.front().blocking_state() ==
      pb::LateralBlockingState::SOFT_BLOCKING) {
    // We add the front part of soft blocking states for nudge, and add the back
    // part of hard blocking for punish nudge in the future.
    const int start_soft_index = GetStartIndexForState(
        blocking_sequences,
        /*blocking_state=*/pb::LateralBlockingState::SOFT_BLOCKING);
    const int end_soft_index = GetEndIndexForState(
        blocking_sequences,
        /*blocking_state=*/pb::LateralBlockingState::SOFT_BLOCKING);
    const int start_hard_index = GetStartIndexForState(
        blocking_sequences,
        /*blocking_state=*/pb::LateralBlockingState::HARD_BLOCKING);
    const int end_index = blocking_sequences.back().range().end_index();
    pb::BlockingSequence soft_blocking_sequence = GetBlockingSequence(
        start_soft_index, std::min(end_soft_index, start_hard_index - 1),
        /*blocking_state=*/pb::LateralBlockingState::SOFT_BLOCKING);
    pb::BlockingSequence hard_blocking_sequence = GetBlockingSequence(
        start_hard_index, end_index,
        /*blocking_state=*/pb::LateralBlockingState::HARD_BLOCKING);
    trajectory_intention_data.blocking_sequence_type =
        pb::BlockingSequenceType::kMixedBlocking;
    trajectory_intention_data.blocking_sequences =
        std::vector<pb::BlockingSequence>{soft_blocking_sequence,
                                          hard_blocking_sequence};
    trajectory_intention_data.intention_type = pb::TrajectoryState::MIXED;
    return true;
  }

  std::vector<pb::BlockingSequence> merged_blocking_sequences =
      GetMixedBlockingSequences(blocking_sequences);
  trajectory_intention_data.blocking_sequence_type =
      pb::BlockingSequenceType::kMixedBlocking;
  trajectory_intention_data.blocking_sequences =
      std::move(merged_blocking_sequences);

  const double ped_cross_track_speed_mps =
      agent_inlane_state.tracked_state.inlane_param.cross_track_speed_mps;

  // If dangerous scenarios, we give static nudge decision.
  if (ShouldNudgeForHardBlockingSequences(
          blocking_state_data.reasoning_info, ego_param_.along_track_speed_mps,
          agent_inlane_state.tracked_state.inlane_param
              .along_track_speed_mps) ||
      ((blocking_state_data.reasoning_info.is_cut_in() ||
        blocking_state_data.reasoning_info.is_crossing()) &&
       (ShouldTreatAgentAsStationary(blocking_state_data.tracked_blocking_info,
                                     blocking_state_data.reasoning_info)))) {
    trajectory_intention_data.is_static =
        abs(ped_cross_track_speed_mps) < kMaxStaticPedSpeedMps;
    trajectory_intention_data.intention_type = pb::TrajectoryState::MIXED;
    return true;
  }

  return true;
}

bool PedestrianPathReasoner::PreReasonForMixedBlockingSequences(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  // TODO(yongbing): add more logic for cut-in, merge and crosssing scenarios
  // for pedestrian. Try to nudge the first soft_blocking part.
  const std::vector<pb::BlockingSequence>& blocking_sequences =
      blocking_state_data.blocking_sequences;
  if (blocking_sequences.front().blocking_state() ==
      pb::LateralBlockingState::SOFT_BLOCKING) {
    // We add the front part of soft blocking states for nudge, and add the back
    // part of hard blocking for punish nudge in the future.
    const int start_soft_index = GetStartIndexForState(
        blocking_sequences,
        /*blocking_state=*/pb::LateralBlockingState::SOFT_BLOCKING);
    const int end_soft_index = GetEndIndexForState(
        blocking_sequences,
        /*blocking_state=*/pb::LateralBlockingState::SOFT_BLOCKING);
    const int start_hard_index = GetStartIndexForState(
        blocking_sequences,
        /*blocking_state=*/pb::LateralBlockingState::HARD_BLOCKING);
    const int end_index = blocking_sequences.back().range().end_index();
    pb::BlockingSequence soft_blocking_sequence = GetBlockingSequence(
        start_soft_index, std::min(end_soft_index, start_hard_index - 1),
        /*blocking_state=*/pb::LateralBlockingState::SOFT_BLOCKING);
    pb::BlockingSequence hard_blocking_sequence = GetBlockingSequence(
        start_hard_index, end_index,
        /*blocking_state=*/pb::LateralBlockingState::HARD_BLOCKING);
    trajectory_intention_data.blocking_sequence_type =
        pb::BlockingSequenceType::kMixedBlocking;
    trajectory_intention_data.blocking_sequences =
        std::vector<pb::BlockingSequence>{soft_blocking_sequence,
                                          hard_blocking_sequence};
    trajectory_intention_data.intention_type = pb::TrajectoryState::MIXED;
    return true;
  }

  std::vector<pb::BlockingSequence> merged_blocking_sequences =
      GetMixedBlockingSequences(blocking_sequences);
  trajectory_intention_data.blocking_sequence_type =
      pb::BlockingSequenceType::kMixedBlocking;
  trajectory_intention_data.blocking_sequences =
      std::move(merged_blocking_sequences);

  const double ped_cross_track_speed_mps =
      object_occupancy_state.current_snapshot_info()
          .object_occupancy_param()
          .cross_track_speed_mps;

  // If dangerous scenarios, we give static nudge decision.
  if (ShouldNudgeForHardBlockingSequences(
          blocking_state_data.reasoning_info, ego_param_.along_track_speed_mps,
          object_occupancy_state.current_snapshot_info()
              .object_occupancy_param()
              .along_track_speed_mps) ||
      ((blocking_state_data.reasoning_info.is_cut_in() ||
        blocking_state_data.reasoning_info.is_crossing()) &&
       (ShouldTreatAgentAsStationary(blocking_state_data.tracked_blocking_info,
                                     blocking_state_data.reasoning_info)))) {
    trajectory_intention_data.is_static =
        abs(ped_cross_track_speed_mps) < kMaxStaticPedSpeedMps;
    trajectory_intention_data.intention_type = pb::TrajectoryState::MIXED;
    return true;
  }

  return true;
}

bool PedestrianPathReasoner::PostReason(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const TrajectoryIntentionMetaData& /* trajectory_intention_data */,
    IntentionResultMetaData& intention_result) const {
  DCHECK(!intention_result.is_static);
  intention_result.reasoner_id = reasoner_id_;
  const pb::SnapshotIntention::PassState tracked_pass_decision =
      GetPassStateWithAgentTrackedState(
          agent_inlane_state.tracked_state.inlane_param.center_line_side,
          blocking_state_data.reasoning_info.tracked_side_to_ego());

  MaybeAdjustLateralGapBasedOnTtc(
      blocking_state_data.reasoning_info,
      active_lat_gap_adjust_meta_.ego_speed_mps,
      agent_inlane_state.agent_metadata.agent_type,
      traffic_rule_reasoning_info_, intention_result.lateral_decision,
      intention_result.required_lat_gap, &(intention_result.debug_str));

  AdjustLateralGapBasedOnLateralMoveDirection(
      lane_change_info_, blocking_state_data.object_id,
      intention_result.lateral_decision, active_lat_gap_adjust_meta_,
      agent_inlane_state.tracked_state.inlane_param.speed_mps,
      blocking_state_data.reasoning_info
          .is_interested_agent_during_lane_change_or_abort(),
      intention_result.required_lat_gap, &intention_result.debug_str);

  // Update ped RLG based on speed.
  const double buffer_before_agent_meters =
      ego_param_.rear_axle_to_front_bumper_m;
  std::optional<double> ego_speed_near_agent = GetMaxEgoSpeedNearAgent(
      reusable_last_speed_profile_, agent_inlane_state, ego_param_.arclength_m,
      buffer_before_agent_meters);
  MaybeAdjustPedLateralGapBasedOnSpeed(
      traffic_rule_reasoning_info_, active_lat_gap_adjust_meta_,
      /*ego_speed=*/ego_speed_near_agent, intention_result.lateral_decision,
      agent_inlane_state.agent_metadata.agent_type,
      intention_result.required_lat_gap, &(intention_result.debug_str));

  if (intention_result.lateral_decision != pb::SnapshotIntention::IGNORE &&
      ShouldCrossLane(blocking_state_data)) {
    intention_result.solid_line_lane_boundary_decision =
        LaneBoundaryDecision::kCrossLane;
    intention_result.is_lane_encroach_intent_agent = true;
  }
  if (intention_result.lateral_decision == pb::SnapshotIntention::IGNORE &&
      tracked_pass_decision != pb::SnapshotIntention::IGNORE &&
      (ShouldTreatAgentAsStationary(blocking_state_data.tracked_blocking_info,
                                    blocking_state_data.reasoning_info))) {
    intention_result.is_overtaken = false;
    intention_result.is_static = true;
    intention_result.lateral_decision = tracked_pass_decision;
    absl::StrAppend(&intention_result.debug_str,
                    "lat_dec ignore, treat as static; ");
    AddCurrentPoseNominalConstraint(agent_inlane_state,
                                    intention_result.lateral_decision,
                                    intention_result);
    return true;
  }

  AddMultipleNominalNudgeConstraintOnPrediction(
      agent_inlane_state, intention_result.lateral_decision,
      intention_result.nudge_index_ranges, intention_result.required_lat_gap,
      intention_result);
  return true;
}

bool PedestrianPathReasoner::PostReason(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const TrajectoryIntentionMetaData& /* trajectory_intention_data */,
    IntentionResultMetaData& intention_result) const {
  DCHECK(!intention_result.is_static);
  intention_result.reasoner_id = reasoner_id_;
  const pb::SnapshotIntention::PassState tracked_pass_decision =
      GetPassStateWithAgentTrackedState(
          object_occupancy_state.current_snapshot_info()
              .object_occupancy_param()
              .center_line_side,
          blocking_state_data.reasoning_info.tracked_side_to_ego());

  MaybeAdjustLateralGapBasedOnTtc(
      blocking_state_data.reasoning_info,
      active_lat_gap_adjust_meta_.ego_speed_mps,
      object_occupancy_state.object_type(), traffic_rule_reasoning_info_,
      intention_result.lateral_decision, intention_result.required_lat_gap,
      &(intention_result.debug_str));

  AdjustLateralGapBasedOnLateralMoveDirection(
      lane_change_info_, blocking_state_data.object_id,
      intention_result.lateral_decision, active_lat_gap_adjust_meta_,
      object_occupancy_state.current_snapshot_info()
          .object_occupancy_param()
          .speed_mps,
      blocking_state_data.reasoning_info
          .is_interested_agent_during_lane_change_or_abort(),
      intention_result.required_lat_gap, &intention_result.debug_str);

  // Update ped RLG based on speed.
  const double buffer_before_agent_meters =
      ego_param_.rear_axle_to_front_bumper_m;
  std::optional<double> ego_speed_near_agent = GetMaxEgoSpeedNearAgent(
      reusable_last_speed_profile_, object_occupancy_state,
      ego_param_.arclength_m, buffer_before_agent_meters);
  MaybeAdjustPedLateralGapBasedOnSpeed(
      traffic_rule_reasoning_info_, active_lat_gap_adjust_meta_,
      /*ego_speed=*/ego_speed_near_agent, intention_result.lateral_decision,
      object_occupancy_state.object_type(), intention_result.required_lat_gap,
      &(intention_result.debug_str));
  if (intention_result.lateral_decision != pb::SnapshotIntention::IGNORE &&
      ShouldCrossLane(blocking_state_data)) {
    intention_result.solid_line_lane_boundary_decision =
        LaneBoundaryDecision::kCrossLane;
    intention_result.is_lane_encroach_intent_agent = true;
  }
  if (intention_result.lateral_decision == pb::SnapshotIntention::IGNORE &&
      tracked_pass_decision != pb::SnapshotIntention::IGNORE &&
      (ShouldTreatAgentAsStationary(blocking_state_data.tracked_blocking_info,
                                    blocking_state_data.reasoning_info))) {
    intention_result.is_overtaken = false;
    intention_result.is_static = true;
    intention_result.lateral_decision = tracked_pass_decision;
    absl::StrAppend(&intention_result.debug_str,
                    "lat_dec ignore, treat as static; ");
    AddCurrentPoseNominalConstraint(object_occupancy_state,
                                    intention_result.lateral_decision,
                                    intention_result);
    return true;
  }
  AddMultipleNominalNudgeConstraintOnPrediction(
      object_occupancy_state, intention_result.lateral_decision,
      intention_result.nudge_index_ranges, intention_result.required_lat_gap,
      intention_result);
  return true;
}

std::unique_ptr<RepulsionMetaData> PedestrianPathReasoner::PreRepulsionReason(
    const RobotStateSnapshot& robot_state_snapshot,
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const std::unordered_map<int, LateralBlockingStateMetaData>&
        trajectory_blocking_state_data_map,
    const std::optional<pb::Path>& reusable_last_path,
    const std::optional<speed::Profile>& reusable_last_speed_profile,
    double repulsion_required_lat_gap, std::string& debug_str) const {
  (void)robot_state_snapshot;
  (void)agent_inlane_state;
  (void)blocking_state_data;
  (void)trajectory_blocking_state_data_map;
  (void)reusable_last_path;
  (void)reusable_last_speed_profile;
  (void)repulsion_required_lat_gap;
  (void)debug_str;
  TRACE_EVENT_SCOPE(planner,
                    RunPathReasoning_PedestrianPathReasoner_PreRepulsionReason);
  return nullptr;
}

std::unique_ptr<RepulsionMetaData> PedestrianPathReasoner::PreRepulsionReason(
    const RobotStateSnapshot& robot_state_snapshot,
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const std::optional<pb::Path>& reusable_last_path,
    const std::optional<speed::Profile>& reusable_last_speed_profile,
    double repulsion_required_lat_gap, std::string& debug_str) const {
  (void)robot_state_snapshot;
  (void)object_occupancy_state;
  (void)blocking_state_data;
  (void)reusable_last_path;
  (void)reusable_last_speed_profile;
  (void)repulsion_required_lat_gap;
  (void)debug_str;
  TRACE_EVENT_SCOPE(planner,
                    RunPathReasoning_PedestrianPathReasoner_PreRepulsionReason);
  return nullptr;
}

}  // namespace path
}  // namespace planner
