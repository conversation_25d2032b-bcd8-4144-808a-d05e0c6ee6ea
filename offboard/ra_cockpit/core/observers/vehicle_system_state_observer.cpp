#include "vehicle_system_state_observer.h"

#include <utility>

#include "av_comm/topics.h"
#include "core/dotting.h"
#include "glog/logging.h"

namespace remote_assist {

VehicleSystemStateObserver::VehicleSystemStateObserver(
    VehicleSystemStateCallback callback)
    : vehicle_system_state_callback_(std::move(callback)) {}

bool VehicleSystemStateObserver::IsInterestedIn(
    const teleassist::pb::TeleassistRequest& message) {
  return (message.message_type() == kVehicleSystemState);
}

std::shared_ptr<google::protobuf::Message>
VehicleSystemStateObserver::OnMessage(
    const teleassist::pb::TeleassistRequest& message,
    DataChannel data_channel) {
  auto vehicle_system_state =
      std::make_shared<teleassist::pb::VehicleSystemState>();
  if (!vehicle_system_state->ParseFromString(message.message_data())) {
    LOG(ERROR) << "Received error VehicleSystemState message format, len = "
               << message.message_data().length();
    return nullptr;
  }

  const bool is_teleassist_abnormal =
      vehicle_system_state->is_teleassist_abnormal();
  CheckIfTeleassistAbnormal(is_teleassist_abnormal);

  if (vehicle_system_state_callback_) {
    vehicle_system_state_callback_(vehicle_system_state, data_channel);
  }
  return vehicle_system_state;
}

void VehicleSystemStateObserver::CheckIfTeleassistAbnormal(
    const bool is_teleassist_abnormal) {
  if (is_teleassist_abnormal == is_teleassist_abnormal_) return;

  if (is_teleassist_abnormal) {
    gDotting.DottingKV("ra_waypoint_abnormal", "ra_waypoint_abnormal",
                       {{"status", 0}, {"reason", "teleassist"}});
  }
  is_teleassist_abnormal_ = is_teleassist_abnormal;
}

}  // namespace remote_assist
