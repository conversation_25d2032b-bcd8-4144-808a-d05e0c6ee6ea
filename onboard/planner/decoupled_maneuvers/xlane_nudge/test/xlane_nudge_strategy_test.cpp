#include "math/pose_2d.h"
#include "planner/decoupled_maneuvers/xlane_nudge/base_xlane_nudge_strategy.h"

#include <vector>

#include <gtest/gtest.h>

#include "geometry/model/polyline_curve.h"
#include "mrc_protos/mrc_request.pb.h"
#include "planner/behavior/util/lane_sequence/test/test_utility.h"
#include "planner/behavior/util/lane_sequence_geometry/lane_sequence_geometry_utility.h"
#include "planner/decoupled_maneuvers/xlane_nudge/opposite_lane_nudge_strategy.h"
#include "planner/speed/reasoning/test/reasoning_test_fixture.h"
#include "planner/utility/config_center/planner_config_center.h"
#include "planner/utility/unstuck/stuck_region/stuck_region.h"
#include "planner/utility/unstuck/stuck_region/stuck_region_generator.h"
#include "planner_protos/agent_intention_config.pb.h"
#include "planner_protos/behavior_reasoners_debug.pb.h"
#include "planner_protos/hazardous_state.pb.h"
#include "planner_protos/lane_blockage.pb.h"
#include "planner_protos/path_reasoning_seed.pb.h"
#include "pnc_map_service/map_elements/lane.h"
#include "prediction_protos/agent.pb.h"
#include "voy_protos/traffic_light.pb.h"

namespace planner::xlane_nudge {

class XLaneNudgeStrategyTest : public testing::Test,
                               public speed::ReasoningTestFixture {
 public:
  XLaneNudgeStrategyTest()
      : config_(PlannerConfigCenter::GetInstance()
                    .GetAgentIntentionConfig(pb::LANE_KEEP)
                    .path_reasoning_config()
                    .lane_keep_config()
                    .xlane_nudge_decider_config()),
        generator_(config_.agent_grouping_config()) {
    SetUpRegionMap(hdmap::test_util::kFremontData);
    SetEgoPose(/*lane_id=*/97151, /*portion=*/0.01);
    canbus_.set_mode(voy::VehicleMode::AUTO_FULL);
    CreateWorldModelWithStuckRegion({97151});
  }

  void CreateWorldModelWithStuckRegion(const std::vector<int64_t>& lane_ids) {
    agent_list_.Clear();
    SetEgoPose(lane_ids.front(), /*portion=*/0.01);
    // Create lane sequence.
    lane_sequence_ =
        test_utility::ConstructLaneSequence(lane_ids, map_test_util());
    ls_geo_.nominal_path = lane_selection::GetLaneSequenceCurve(
        lane_sequence_, lane_selection::LaneCurveType::kCenterLine);
    ls_geo_.left_lane_boundary = lane_selection::GetLaneSequenceCurve(
        lane_sequence_, lane_selection::LaneCurveType::kLeftBoundary);
    ls_geo_.right_lane_boundary = lane_selection::GetLaneSequenceCurve(
        lane_sequence_, lane_selection::LaneCurveType::kRightBoundary);
    ls_geo_.lane_sequence_contour = lane_selection::GetLaneBoundaryBorder(
        ls_geo_.left_lane_boundary, ls_geo_.right_lane_boundary,
        ls_geo_.nominal_path);

    // Get dummy world model.
    prediction::pb::Agent& agent_1 = AddAgent(
        /*agent_type=*/voy::perception::ObjectType::VEHICLE, /*id=*/1,
        /*lane_id=*/DCHECK_NOTNULL(lane_sequence_[0])->id(),
        /*portion=*/0.2, /*length=*/5.0, /*width=*/2.0, /*velocity=*/0.0);
    agent_1.mutable_tracked_object()->add_attributes(
        voy::perception::Attribute::PARKED_CAR);
    AddStationaryPredictedTrajectory(
        DCHECK_NOTNULL(lane_sequence_[0])->id(), /*portion=*/0.2,
        /*likelihood=*/1.0, /*traj_id=*/1, agent_1);

    UpdateWorldModel();
  }

  void CreateUncrossableStuckRegionScenario() {
    SetUpSceneMap(hdmap::test_util::SceneType::kJunction);
    SetEgoPose(/*lane_id=*/9109, /*portion=*/0.01);
    agent_list_.Clear();
    // Create lane sequence.
    lane_sequence_ =
        test_utility::ConstructLaneSequence({9109, 10997}, map_test_util());
    ls_geo_.nominal_path = lane_selection::GetLaneSequenceCurve(
        lane_sequence_, lane_selection::LaneCurveType::kCenterLine);
    ls_geo_.left_lane_boundary = lane_selection::GetLaneSequenceCurve(
        lane_sequence_, lane_selection::LaneCurveType::kLeftBoundary);
    ls_geo_.right_lane_boundary = lane_selection::GetLaneSequenceCurve(
        lane_sequence_, lane_selection::LaneCurveType::kRightBoundary);
    ls_geo_.lane_sequence_contour = lane_selection::GetLaneBoundaryBorder(
        ls_geo_.left_lane_boundary, ls_geo_.right_lane_boundary,
        ls_geo_.nominal_path);

    // Place traffic cones on the lane marking 15475
    prediction::pb::Agent& cone_1 = AddAgent(
        /*agent_type=*/voy::perception::ObjectType::TRAFFIC_CONE, /*id=*/1,
        /*pose=*/
        math::Pose2d(/*x_in=*/-8922.51, /*y_in=*/-33484.46, /*yaw_in=*/0.0),
        /*length=*/0.4, /*width=*/0.4, /*velocity=*/0.0);
    AddStationaryPredictedTrajectory(
        math::Pose2d(/*x_in=*/-8922.51, /*y_in=*/-33484.46, /*yaw_in=*/0.0),
        /*likelihood=*/1.0, /*traj_id=*/1, cone_1);
    prediction::pb::Agent& cone_2 = AddAgent(
        /*agent_type=*/voy::perception::ObjectType::TRAFFIC_CONE, /*id=*/2,
        /*pose=*/
        math::Pose2d(/*x_in=*/-8917.25, /*y_in=*/-33483.13, /*yaw_in=*/0.0),
        /*length=*/0.4, /*width=*/0.4, /*velocity=*/0.0);
    AddStationaryPredictedTrajectory(
        math::Pose2d(/*x_in=*/-8917.25, /*y_in=*/-33483.13, /*yaw_in=*/0.0),
        /*likelihood=*/1.0, /*traj_id=*/1, cone_2);
    prediction::pb::Agent& cone_3 = AddAgent(
        /*agent_type=*/voy::perception::ObjectType::TRAFFIC_CONE, /*id=*/3,
        /*pose=*/
        math::Pose2d(/*x_in=*/-8911.75, /*y_in=*/-33481.69, /*yaw_in=*/0.0),
        /*length=*/0.4, /*width=*/0.4, /*velocity=*/0.0);
    AddStationaryPredictedTrajectory(
        math::Pose2d(/*x_in=*/-8911.75, /*y_in=*/-33481.69, /*yaw_in=*/0.0),
        /*likelihood=*/1.0, /*traj_id=*/1, cone_3);
    prediction::pb::Agent& cone_4 = AddAgent(
        /*agent_type=*/voy::perception::ObjectType::TRAFFIC_CONE, /*id=*/4,
        /*pose=*/
        math::Pose2d(/*x_in=*/-8906.03, /*y_in=*/-33480.13, /*yaw_in=*/0.0),
        /*length=*/0.4, /*width=*/0.4, /*velocity=*/0.0);
    AddStationaryPredictedTrajectory(
        math::Pose2d(/*x_in=*/-8906.03, /*y_in=*/-33480.13, /*yaw_in=*/0.0),
        /*likelihood=*/1.0, /*traj_id=*/1, cone_4);
    prediction::pb::Agent& cone_5 = AddAgent(
        /*agent_type=*/voy::perception::ObjectType::TRAFFIC_CONE, /*id=*/5,
        /*pose=*/
        math::Pose2d(/*x_in=*/-8900.89, /*y_in=*/-33479.02, /*yaw_in=*/0.0),
        /*length=*/0.4, /*width=*/0.4, /*velocity=*/0.0);
    AddStationaryPredictedTrajectory(
        math::Pose2d(/*x_in=*/-8900.89, /*y_in=*/-33479.02, /*yaw_in=*/0.0),
        /*likelihood=*/1.0, /*traj_id=*/1, cone_5);
    prediction::pb::Agent& cone_6 = AddAgent(
        /*agent_type=*/voy::perception::ObjectType::TRAFFIC_CONE, /*id=*/6,
        /*pose=*/
        math::Pose2d(/*x_in=*/-8895.08, /*y_in=*/-33477.58, /*yaw_in=*/0.0),
        /*length=*/0.4, /*width=*/0.4, /*velocity=*/0.0);
    AddStationaryPredictedTrajectory(
        math::Pose2d(/*x_in=*/-8895.08, /*y_in=*/-33477.58, /*yaw_in=*/0.0),
        /*likelihood=*/1.0, /*traj_id=*/1, cone_6);
    UpdateWorldModel();
  }

  //
  // Data members
  //
  std::vector<const pnc_map::Lane*> lane_sequence_;
  lane_selection::LaneSequenceGeometry ls_geo_;
  const pb::XLaneNudgeDeciderConfig& config_;
  const StuckRegionGenerator generator_;
  const tbb::concurrent_unordered_map<ObjectId,
                                      std::vector<PredictedTrajectoryWrapper>>
      object_prediction_map_;
};

// Tests the WasXLaneNudging function in the scenario that:
// last cycle: xlane_nudging = true, lane_sequence = {97153}
//             stuck_signal = false
// current   : lane_sequence = {97151}
TEST_F(XLaneNudgeStrategyTest,
       TestWasXLaneNudgingTriggering_DifferentLaneSequence) {
  planner::pb::XLaneNudgeSeed xlane_nudge_seed;
  xlane_nudge_seed.set_overtake_mode(planner::pb::StuckRegion::kOncomingLane);
  xlane_nudge_seed.set_side(planner::pb::StuckRegion::kLeft);
  xlane_nudge_seed.mutable_target_stuck_region()->add_identifier(97153);

  const std::vector<StuckRegion> stuck_regions = generator_.Generate(
      world_model().robot_state(), world_model().planner_object_map(),
      object_prediction_map_, world_model().construction_zones(),
      lane_sequence_, world_model().lane_blockage_detector().lane_blockages(),
      ls_geo_, /*stuck_object_id_opt=*/std::nullopt,
      math::Range1d(0, ls_geo_.nominal_path.GetTotalArcLength()),
      pb::StuckRegion());
  const OppositeLaneNudgeStrategy x_opposite_lane_strategy(
      world_model(), stuck_regions.front(), lane_sequence_, ls_geo_,
      xlane_nudge_seed, /*was_ego_stuck=*/false,
      /*is_last_lane_sequence=*/true, config_);
  XLaneSideParams params = {
      .mode = pb::StuckRegion::kOncomingLane,
      .side = pb::StuckRegion::kLeft,
      .required_offset = 1.0,
  };
  XLaneSideParams opposite_side_params = {
      .mode = pb::StuckRegion::kNeighborLane,
      .side = pb::StuckRegion::kRight,
      .required_offset = 1.0,
  };
  const auto result =
      x_opposite_lane_strategy.PreCheck(opposite_side_params, params);
  EXPECT_FALSE(result.is_ready_to_trigger);
  EXPECT_EQ(result.reason, pb::XLaneNudgeDebug::kNoStuckSignal);
}

// Tests the WasXLaneNudging function in the scenario that:
// last cycle: xlane_nudging = true, lane_sequence = {97151}
//             stuck_signal = false
// current   : lane_sequence = {97151}
TEST_F(XLaneNudgeStrategyTest, TestWasXLaneNudgingTriggering_SameLaneSequence) {
  planner::pb::XLaneNudgeSeed xlane_nudge_seed;
  xlane_nudge_seed.set_overtake_mode(planner::pb::StuckRegion::kOncomingLane);
  xlane_nudge_seed.set_side(planner::pb::StuckRegion::kLeft);
  xlane_nudge_seed.mutable_target_stuck_region()->add_identifier(97151);

  const std::vector<StuckRegion> stuck_regions = generator_.Generate(
      world_model().robot_state(), world_model().planner_object_map(),
      object_prediction_map_, world_model().construction_zones(),
      lane_sequence_, world_model().lane_blockage_detector().lane_blockages(),
      ls_geo_, /*stuck_object_id_opt=*/std::nullopt,
      math::Range1d(0, ls_geo_.nominal_path.GetTotalArcLength()),
      pb::StuckRegion());
  const OppositeLaneNudgeStrategy x_opposite_lane_strategy(
      world_model(), stuck_regions.front(), lane_sequence_, ls_geo_,
      xlane_nudge_seed,
      /*was_ego_stuck=*/false, /*is_last_lane_sequence=*/true, config_);
  XLaneSideParams params = {
      .mode = pb::StuckRegion::kOncomingLane,
      .side = pb::StuckRegion::kLeft,
      .required_offset = 1.0,
  };
  XLaneSideParams opposite_side_params = {
      .mode = pb::StuckRegion::kNeighborLane,
      .side = pb::StuckRegion::kRight,
      .required_offset = 1.0,
  };
  const auto result =
      x_opposite_lane_strategy.PreCheck(opposite_side_params, params);
  EXPECT_TRUE(result.is_ready_to_trigger);
  EXPECT_EQ(result.reason, pb::XLaneNudgeDebug::kSuccess);
}

// Tests MRC request handling during xlane nudge trigger.
TEST_F(XLaneNudgeStrategyTest, TestMRCRelatedCondition) {
  // last cycle: xlane_nudging = false,
  //             stuck_signal = true
  // current   : lane_sequence = {97151}
  planner::pb::XLaneNudgeSeed xlane_nudge_seed;
  xlane_nudge_seed.set_side(::planner::pb::StuckRegion::kNotOvertake);

  const std::vector<StuckRegion> stuck_regions = generator_.Generate(
      world_model().robot_state(), world_model().planner_object_map(),
      object_prediction_map_, world_model().construction_zones(),
      lane_sequence_, world_model().lane_blockage_detector().lane_blockages(),
      ls_geo_, /*stuck_object_id_opt=*/std::nullopt,
      math::Range1d(0, ls_geo_.nominal_path.GetTotalArcLength()),
      pb::StuckRegion());
  mrc::pb::MrcRequest mrc_request;
  mrc_request.set_mrm_type(::mrc::pb::MRM_INLANE);
  UpdateWorldModel(mrc_request);
  SetShouldPlannerRespondMRC(/*should_planner_respond_mrc=*/true);
  const BaseXLaneNudgeStrategy base_x_lane_strategy(
      world_model(), stuck_regions.front(), lane_sequence_, ls_geo_,
      xlane_nudge_seed, /*was_ego_stuck=*/true,
      /*is_last_lane_sequence=*/true, config_);
  XLaneSideParams params = {
      .mode = pb::StuckRegion::kOncomingLane,
      .side = pb::StuckRegion::kLeft,
      .required_offset = 1.0,
  };
  XLaneSideParams opposite_side_params = {
      .mode = pb::StuckRegion::kNeighborLane,
      .side = pb::StuckRegion::kRight,
      .required_offset = 1.0,
  };
  auto result = base_x_lane_strategy.PreCheck(opposite_side_params, params);
  EXPECT_FALSE(result.is_ready_to_trigger);
  EXPECT_EQ(result.reason, pb::XLaneNudgeDebug::kDisabled);

  // last cycle: xlane_nudging = true, lane_sequence = {97151}
  //             stuck_signal = false
  // current   : lane_sequence = {97151}
  xlane_nudge_seed.set_overtake_mode(planner::pb::StuckRegion::kOncomingLane);
  xlane_nudge_seed.set_side(planner::pb::StuckRegion::kLeft);
  xlane_nudge_seed.mutable_target_stuck_region()->add_identifier(97151);
  result = base_x_lane_strategy.PreCheck(opposite_side_params, params);
  EXPECT_TRUE(result.is_ready_to_trigger);
  EXPECT_EQ(result.reason, pb::XLaneNudgeDebug::kSuccess);
}

// Tests the case one side lane marking is blocked by traffic cones.
TEST_F(XLaneNudgeStrategyTest, TestNotCrossableCondition) {
  planner::pb::XLaneNudgeSeed xlane_nudge_seed;
  xlane_nudge_seed.set_side(::planner::pb::StuckRegion::kNotOvertake);
  CreateUncrossableStuckRegionScenario();
  const std::vector<StuckRegion> stuck_regions = generator_.Generate(
      world_model().robot_state(), world_model().planner_object_map(),
      object_prediction_map_, world_model().construction_zones(),
      lane_sequence_, world_model().lane_blockage_detector().lane_blockages(),
      ls_geo_, /*stuck_object_id_opt=*/std::nullopt,
      math::Range1d(0, ls_geo_.nominal_path.GetTotalArcLength()),
      pb::StuckRegion());
  const BaseXLaneNudgeStrategy base_x_lane_strategy(
      world_model(), stuck_regions.front(), lane_sequence_, ls_geo_,
      xlane_nudge_seed, /*was_ego_stuck=*/true,
      /*is_last_lane_sequence=*/true, config_);
  XLaneSideParams params = {
      .mode = pb::StuckRegion::kNeighborLane,
      .side = pb::StuckRegion::kLeft,
      .required_offset = 1.0,
      .dist_to_physical_boundary = 5.0,
  };
  XLaneSideParams opposite_side_params = {
      .mode = pb::StuckRegion::kFreeSpace,
      .side = pb::StuckRegion::kRight,
      .required_offset = 0.1,
  };
  // Ego has no overlap with stuck region, and right side is wide enough. So
  // the left side is also disabled to execute xlane nudge.
  auto result = base_x_lane_strategy.PreCheck(opposite_side_params, params);
  EXPECT_FALSE(result.is_ready_to_trigger);
  EXPECT_EQ(result.reason, pb::XLaneNudgeDebug::kWideEnough);

  // Ego has overlap with stuck region, and left side is not crossable.
  SetEgoPose(/*lane_id=*/9109, /*portion=*/0.5);
  const BaseXLaneNudgeStrategy base_x_lane_strategy_with_overlap(
      world_model(), stuck_regions.front(), lane_sequence_, ls_geo_,
      xlane_nudge_seed, /*was_ego_stuck=*/true,
      /*is_last_lane_sequence=*/true, config_);
  result =
      base_x_lane_strategy_with_overlap.PreCheck(opposite_side_params, params);
  EXPECT_FALSE(result.is_ready_to_trigger);
  EXPECT_EQ(result.reason, pb::XLaneNudgeDebug::kNotCrossable);
}

class XLaneNudgeStrategyForTlTest : public ::testing::Test,
                                    public speed::ReasoningTestFixture {
 public:
  XLaneNudgeStrategyForTlTest()
      : config_(PlannerConfigCenter::GetInstance()
                    .GetAgentIntentionConfig(pb::LANE_KEEP)
                    .path_reasoning_config()
                    .lane_keep_config()
                    .xlane_nudge_decider_config()),
        generator_(config_.agent_grouping_config()) {
    SetUpSceneMap(hdmap::test_util::SceneType::kJunction);
    PlaceBlockageBeforeJunction();
  }

  void PlaceBlockageBeforeJunction() {
    agent_list_.Clear();
    SetEgoPose(9023, /*portion=*/0.75);
    // Create lane sequence.
    lane_sequence_ =
        test_utility::ConstructLaneSequence({9023, 9711}, map_test_util());
    ls_geo_.nominal_path = lane_selection::GetLaneSequenceCurve(
        lane_sequence_, lane_selection::LaneCurveType::kCenterLine);
    ls_geo_.left_lane_boundary = lane_selection::GetLaneSequenceCurve(
        lane_sequence_, lane_selection::LaneCurveType::kLeftBoundary);
    ls_geo_.right_lane_boundary = lane_selection::GetLaneSequenceCurve(
        lane_sequence_, lane_selection::LaneCurveType::kRightBoundary);
    ls_geo_.lane_sequence_contour = lane_selection::GetLaneBoundaryBorder(
        ls_geo_.left_lane_boundary, ls_geo_.right_lane_boundary,
        ls_geo_.nominal_path);

    // Get dummy world model.
    prediction::pb::Agent& agent_1 = AddAgent(
        /*agent_type=*/voy::perception::ObjectType::VEHICLE, /*id=*/1,
        /*lane_id=*/DCHECK_NOTNULL(lane_sequence_[0])->id(),
        /*portion=*/0.9, /*length=*/5.0, /*width=*/2.0, /*velocity=*/0.0);
    agent_1.mutable_tracked_object()->add_attributes(
        voy::perception::Attribute::PARKED_CAR);
    AddStationaryPredictedTrajectory(
        DCHECK_NOTNULL(lane_sequence_[0])->id(), /*portion=*/0.9,
        /*likelihood=*/1.0, /*traj_id=*/1, agent_1);
  }

  // Sets traffic light.
  void SetTrafficLight(voy::TrafficLight::Color tl_color,
                       bool is_flashing = false, bool is_occluded = false,
                       int64_t duration_ms = 0) {
    traffic_lights_.clear_traffic_lights();
    voy::TrafficLight& tl =
        AddTrafficLight(timestamp(), /*sign_id=*/3057, tl_color);
    tl.set_is_flashing(is_flashing);
    tl.set_is_occluded(is_occluded);
    tl.set_duration(duration_ms);
  }

 protected:
  //
  // Data members
  //
  std::vector<const pnc_map::Lane*> lane_sequence_;
  lane_selection::LaneSequenceGeometry ls_geo_;
  const pb::XLaneNudgeDeciderConfig& config_;
  const StuckRegionGenerator generator_;
  const tbb::concurrent_unordered_map<ObjectId,
                                      std::vector<PredictedTrajectoryWrapper>>
      object_prediction_map_;
};

// Tests the IsWaitingForTrafficLight function in the scenario that stuck lane
// is controlled by red traffic light.
TEST_F(XLaneNudgeStrategyForTlTest, TestIsWaitingForRedTrafficLight) {
  SetTrafficLight(voy::TrafficLight::RED);
  UpdateWorldModel();
  planner::pb::XLaneNudgeSeed xlane_nudge_seed;
  xlane_nudge_seed.set_overtake_mode(planner::pb::StuckRegion::kOncomingLane);
  xlane_nudge_seed.set_side(planner::pb::StuckRegion::kLeft);
  xlane_nudge_seed.mutable_target_stuck_region()->add_identifier(97151);

  const std::vector<StuckRegion> stuck_regions = generator_.Generate(
      world_model().robot_state(), world_model().planner_object_map(),
      object_prediction_map_, world_model().construction_zones(),
      lane_sequence_, world_model().lane_blockage_detector().lane_blockages(),
      ls_geo_, /*stuck_object_id_opt=*/std::nullopt,
      math::Range1d(0, ls_geo_.nominal_path.GetTotalArcLength()),
      pb::StuckRegion());
  const BaseXLaneNudgeStrategy base_xlane_nudge_strategy(
      world_model(), stuck_regions.front(), lane_sequence_, ls_geo_,
      xlane_nudge_seed, /*was_ego_stuck=*/true,
      /*is_last_lane_sequence=*/true, config_);
  XLaneSideParams params = {
      .mode = pb::StuckRegion::kNeighborLane,
      .side = pb::StuckRegion::kLeft,
      .dist_to_physical_boundary = 10.0,
  };
  XLaneSideParams opposite_side_params = {
      .side = pb::StuckRegion::kRight,
  };
  const auto result =
      base_xlane_nudge_strategy.PreCheck(opposite_side_params, params);
  EXPECT_FALSE(result.is_ready_to_trigger);
  EXPECT_EQ(result.reason, pb::XLaneNudgeDebug::kTrafficLight);
}

// Tests the IsWaitingForTrafficLight function in the scenario that stuck lane
// is controlled by traffic light, but it's occluded.
TEST_F(XLaneNudgeStrategyForTlTest, TestIsWaitingForOccludedTrafficLight) {
  SetTrafficLight(voy::TrafficLight::UNKNOWN_COLOR, /*is_flashing=*/false,
                  /*is_occluded=*/true);
  UpdateWorldModel();
  planner::pb::XLaneNudgeSeed xlane_nudge_seed;
  xlane_nudge_seed.set_overtake_mode(planner::pb::StuckRegion::kOncomingLane);
  xlane_nudge_seed.set_side(planner::pb::StuckRegion::kLeft);
  xlane_nudge_seed.mutable_target_stuck_region()->add_identifier(97151);

  const std::vector<StuckRegion> stuck_regions = generator_.Generate(
      world_model().robot_state(), world_model().planner_object_map(),
      object_prediction_map_, world_model().construction_zones(),
      lane_sequence_, world_model().lane_blockage_detector().lane_blockages(),
      ls_geo_, /*stuck_object_id_opt=*/std::nullopt,
      math::Range1d(0, ls_geo_.nominal_path.GetTotalArcLength()),
      pb::StuckRegion());
  const BaseXLaneNudgeStrategy base_xlane_nudge_strategy(
      world_model(), stuck_regions.front(), lane_sequence_, ls_geo_,
      xlane_nudge_seed, /*was_ego_stuck=*/true,
      /*is_last_lane_sequence=*/true, config_);
  XLaneSideParams params = {
      .mode = pb::StuckRegion::kNeighborLane,
      .side = pb::StuckRegion::kLeft,
      .dist_to_physical_boundary = 10.0,
  };
  XLaneSideParams opposite_side_params = {
      .side = pb::StuckRegion::kRight,
  };
  const auto result =
      base_xlane_nudge_strategy.PreCheck(opposite_side_params, params);
  EXPECT_FALSE(result.is_ready_to_trigger);
  EXPECT_EQ(result.reason, pb::XLaneNudgeDebug::kTrafficLight);
}

// Tests the IsWaitingForTrafficLight function in the scenario that stuck lane
// is controlled by unknown traffic light, which is not occluded.
TEST_F(XLaneNudgeStrategyForTlTest, TestIsWaitingForUnknownTrafficLight) {
  SetTrafficLight(voy::TrafficLight::UNKNOWN_COLOR, /*is_flashing=*/false,
                  /*is_occluded=*/false);
  UpdateWorldModel();
  planner::pb::XLaneNudgeSeed xlane_nudge_seed;
  xlane_nudge_seed.set_overtake_mode(planner::pb::StuckRegion::kOncomingLane);
  xlane_nudge_seed.set_side(planner::pb::StuckRegion::kLeft);
  xlane_nudge_seed.mutable_target_stuck_region()->add_identifier(97151);

  const std::vector<StuckRegion> stuck_regions = generator_.Generate(
      world_model().robot_state(), world_model().planner_object_map(),
      object_prediction_map_, world_model().construction_zones(),
      lane_sequence_, world_model().lane_blockage_detector().lane_blockages(),
      ls_geo_, /*stuck_object_id_opt=*/std::nullopt,
      math::Range1d(0, ls_geo_.nominal_path.GetTotalArcLength()),
      pb::StuckRegion());
  const BaseXLaneNudgeStrategy base_xlane_nudge_strategy(
      world_model(), stuck_regions.front(), lane_sequence_, ls_geo_,
      xlane_nudge_seed, /*was_ego_stuck=*/true,
      /*is_last_lane_sequence=*/true, config_);
  XLaneSideParams params = {
      .mode = pb::StuckRegion::kNeighborLane,
      .side = pb::StuckRegion::kLeft,
      .dist_to_physical_boundary = 10.0,
  };
  XLaneSideParams opposite_side_params = {
      .side = pb::StuckRegion::kRight,
  };
  const auto result =
      base_xlane_nudge_strategy.PreCheck(opposite_side_params, params);
  EXPECT_FALSE(result.is_ready_to_trigger);
  EXPECT_EQ(result.reason, pb::XLaneNudgeDebug::kTrafficLight);
}

// Tests the IsWaitingForTrafficLight function in the scenario that stuck lane
// is controlled by green traffic light.
TEST_F(XLaneNudgeStrategyForTlTest, TestIsWaitingForLongGreenTrafficLight) {
  SetTrafficLight(voy::TrafficLight::GREEN, /*is_flashing=*/false,
                  /*is_occluded=*/false, /*duration_ms=*/6000);
  UpdateWorldModel();
  planner::pb::XLaneNudgeSeed xlane_nudge_seed;
  xlane_nudge_seed.set_overtake_mode(planner::pb::StuckRegion::kOncomingLane);
  xlane_nudge_seed.set_side(planner::pb::StuckRegion::kLeft);
  xlane_nudge_seed.mutable_target_stuck_region()->add_identifier(97151);

  const std::vector<StuckRegion> stuck_regions = generator_.Generate(
      world_model().robot_state(), world_model().planner_object_map(),
      object_prediction_map_, world_model().construction_zones(),
      lane_sequence_, world_model().lane_blockage_detector().lane_blockages(),
      ls_geo_, /*stuck_object_id_opt=*/std::nullopt,
      math::Range1d(0, ls_geo_.nominal_path.GetTotalArcLength()),
      pb::StuckRegion());
  const BaseXLaneNudgeStrategy base_xlane_nudge_strategy(
      world_model(), stuck_regions.front(), lane_sequence_, ls_geo_,
      xlane_nudge_seed, /*was_ego_stuck=*/true,
      /*is_last_lane_sequence=*/true, config_);
  XLaneSideParams params = {
      .mode = pb::StuckRegion::kNeighborLane,
      .side = pb::StuckRegion::kLeft,
      .required_offset = 2.0,
      .dist_to_physical_boundary = 10.0,
  };
  XLaneSideParams opposite_side_params = {
      .mode = pb::StuckRegion::kNeighborLane,
      .side = pb::StuckRegion::kRight,
      .required_offset = 2.0,
  };
  const auto result =
      base_xlane_nudge_strategy.PreCheck(opposite_side_params, params);
  EXPECT_TRUE(result.is_ready_to_trigger);
  EXPECT_EQ(result.reason, pb::XLaneNudgeDebug::kSuccess);
}

// Tests the IsWaitingForTrafficLight function in the scenario that stuck lane
// is controlled by green traffic light.
TEST_F(XLaneNudgeStrategyForTlTest, TestIsWaitingForFreshGreenTrafficLight) {
  SetTrafficLight(voy::TrafficLight::GREEN, /*is_flashing=*/false,
                  /*is_occluded=*/false, /*duration_ms=*/1000);
  UpdateWorldModel();
  planner::pb::XLaneNudgeSeed xlane_nudge_seed;
  xlane_nudge_seed.set_overtake_mode(planner::pb::StuckRegion::kOncomingLane);
  xlane_nudge_seed.set_side(planner::pb::StuckRegion::kLeft);
  xlane_nudge_seed.mutable_target_stuck_region()->add_identifier(97151);

  const std::vector<StuckRegion> stuck_regions = generator_.Generate(
      world_model().robot_state(), world_model().planner_object_map(),
      object_prediction_map_, world_model().construction_zones(),
      lane_sequence_, world_model().lane_blockage_detector().lane_blockages(),
      ls_geo_, /*stuck_object_id_opt=*/std::nullopt,
      math::Range1d(0, ls_geo_.nominal_path.GetTotalArcLength()),
      pb::StuckRegion());
  const BaseXLaneNudgeStrategy base_xlane_nudge_strategy(
      world_model(), stuck_regions.front(), lane_sequence_, ls_geo_,
      xlane_nudge_seed, /*was_ego_stuck=*/true,
      /*is_last_lane_sequence=*/true, config_);
  XLaneSideParams params = {
      .mode = pb::StuckRegion::kNeighborLane,
      .side = pb::StuckRegion::kLeft,
      .dist_to_physical_boundary = 10.0,
  };
  XLaneSideParams opposite_side_params = {
      .mode = pb::StuckRegion::kNeighborLane,
      .side = pb::StuckRegion::kRight,
      .required_offset = 1.0,
  };
  const auto result =
      base_xlane_nudge_strategy.PreCheck(opposite_side_params, params);
  EXPECT_TRUE(!result.is_ready_to_trigger);
  EXPECT_EQ(result.reason, pb::XLaneNudgeDebug::kTrafficLight);
}
}  // namespace planner::xlane_nudge
