#include <unordered_map>

#include <gtest/gtest.h>

#include "planner/global_object_manager/behavior_relevant_objects_info.h"
#include "planner/global_object_manager/global_object_manager_util.h"

namespace planner {
namespace {
class BehaviorRelevantObjectsInfoTest : public ::testing::Test {
 protected:
  void SetUp() override {
    prediction::pb::Agent agent_proto;
    auto& tracked_object = *agent_proto.mutable_tracked_object();
    tracked_object.set_object_type(voy::perception::ObjectType::VEHICLE);
    tracked_object.set_id(1);
    const TrafficParticipantPose pose_at_plan_init_ts(/*timestamp=*/1,
                                                      tracked_object);
    PlannerObject planner_object(agent_proto, /*timestamp=*/1,
                                 pose_at_plan_init_ts);
    planner_object_map_.emplace(planner_object.id(), planner_object);
    PrincipledRequiredLateralGap principled_required_lateral_gap;
    obj_id_to_required_lat_gaps_.emplace(planner_object.id(),
                                         principled_required_lateral_gap);
    ObjectOccupancyParam object_occupancy_param;
    object_snapshot_info_map_at_plan_init_ts_.emplace(
        planner_object.id(),
        ObjectStampedSnapshotInfo(object_occupancy_param, ego_params_,
                                  pose_at_plan_init_ts,
                                  pb::MotionMode::FORWARD));
  }
  BehaviorRelevantObjectsInfo behavior_relevant_objects_info_;
  std::unordered_map<ObjectId, PlannerObject> planner_object_map_;
  std::unordered_map<ObjectId, PrincipledRequiredLateralGap>
      obj_id_to_required_lat_gaps_;
  EgoInLaneParams ego_params_;
  tbb::concurrent_unordered_map<ObjectId, ObjectStampedSnapshotInfo>
      object_snapshot_info_map_at_plan_init_ts_;
};

TEST_F(BehaviorRelevantObjectsInfoTest, UpdateObjectInformationTest) {
  const auto& need_to_be_considered_czs =
      behavior_relevant_objects_info_.relevant_construction_zones();
  const auto& need_to_be_considered_objects =
      behavior_relevant_objects_info_.relevant_objects();
  EXPECT_TRUE(need_to_be_considered_objects.empty());
  EXPECT_TRUE(need_to_be_considered_czs.empty());
  TypedObjectId planner_object_id;
  planner_object_id.type = pb::ObjectSourceType::kTrackedObject;
  planner_object_id.id = 1001;
  ObjectInformation planner_object_information;

  TypedObjectId cz_id;
  cz_id.type = pb::ObjectSourceType::kConstructionZone;
  cz_id.id = 1002;
  ObjectInformation cz_object_information(pb::ObjectInformation::PRIMARY_ONLY,
                                          nullptr, std::nullopt);

  behavior_relevant_objects_info_.mutable_relevant_construction_zones() = {
      {cz_id, cz_object_information}};

  behavior_relevant_objects_info_.mutable_relevant_objects() = {
      {planner_object_id, planner_object_information}};

  EXPECT_EQ(need_to_be_considered_objects.size(), 1);
  EXPECT_EQ(need_to_be_considered_czs.size(), 1);
}

TEST_F(BehaviorRelevantObjectsInfoTest, ObjectFilterTest) {
  // For degraded driving mode, we only consider near future prediction.
  auto& need_to_be_considered_objects =
      behavior_relevant_objects_info_.mutable_relevant_objects();
  FilterOutUnecessaryObjectsInLaneSequenceInfo(
      planner_object_map_, obj_id_to_required_lat_gaps_,
      object_snapshot_info_map_at_plan_init_ts_,
      /*is_degraded_driving_mode=*/true, need_to_be_considered_objects);
  for (const auto& [typed_object_id, object_information] :
       need_to_be_considered_objects) {
    EXPECT_EQ(object_information.decision,
              pb::ObjectInformation::NEAR_FUTURE_ONLY);
  }
  // For faraway objects, fully_ignore is preferable.
  FilterOutUnecessaryObjectsInLaneSequenceInfo(
      planner_object_map_, obj_id_to_required_lat_gaps_,
      object_snapshot_info_map_at_plan_init_ts_,
      /*is_degraded_driving_mode=*/false, need_to_be_considered_objects);
  for (const auto& [typed_object_id, object_information] :
       need_to_be_considered_objects) {
    if (planner_object_map_.at(typed_object_id.id).l2_distance_to_ego_m() >
        80) {
      EXPECT_EQ(object_information.decision,
                pb::ObjectInformation::FULLY_IGNORE);
    } else {
      EXPECT_EQ(object_information.decision,
                pb::ObjectInformation::FULLY_CONSIDER);
    }
  }
}
}  // namespace
}  // namespace planner
