#ifndef OFFBOARD_FALLBACK_ADS_VEHICLE_SIGNAL_INPUT_VEHICLE_SIGNAL_INPUT_PARAMETER_H_
#define OFFBOARD_FALLBACK_ADS_VEHICLE_SIGNAL_INPUT_VEHICLE_SIGNAL_INPUT_PARAMETER_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <stdbool.h>

#ifndef VSI_TASK_CYCLE_TIME
#define VSI_TASK_CYCLE_TIME (0.01F)
#endif

#ifndef VSI_CAN_TIME_OUT_ARRAY_SIZE
#define VSI_CAN_TIME_OUT_ARRAY_SIZE (15U)
#endif

extern bool GetVsiChooseYawRateFromIMU();
extern bool GetVsiFallbackActiveSwitchOn();
extern float GetVsiKeyCanTimeoutThreshold();
extern float GetVsiCanTimeoutThreshold();
extern float GetVsiEmergencySwitchOnHoldTime();
extern float GetVsiCollisionDetectionTimeoutThreshold();
extern float GetVsiFallbackTakeoverDelayTime();

#ifdef __cplusplus
}
#endif

#endif  // OFFBOARD_FALLBACK_ADS_VEHICLE_SIGNAL_INPUT_VEHICLE_SIGNAL_INPUT_PARAMETER_H_
