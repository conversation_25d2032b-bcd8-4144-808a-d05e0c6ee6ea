#include "planner/speed/reasoning/reasoning_util.h"

#include <algorithm>
#include <cmath>
#include <cstdint>
#include <iterator>
#include <limits>
#include <map>
#include <optional>
#include <string>
#include <unordered_set>
#include <vector>

#include <glog/logging.h>

#include "base/optional_value_accessor.h"
#include "geometry/algorithms/within.h"
#include "geometry/model/point_2d.h"
#include "hdmap_protos/lane.pb.h"
#include "hdmap_protos/zone.pb.h"
#include "log_utils/log_macros.h"
#include "math/constants.h"
#include "math/enum.h"
#include "math/interpolation.h"
#include "math/math_util.h"
#include "math/range.h"
#include "math/unit_conversion.h"
#include "planner/behavior/util/lane_common/lane_sequence_iterator.h"
#include "planner/constants.h"
#include "planner/decoupled_maneuvers/cross_lane/cross_lane_info_manager.h"
#include "planner/decoupled_maneuvers/cross_lane/cross_lane_utils.h"
#include "planner/decoupled_maneuvers/lane_change/lane_change_common_util.h"
#include "planner/decoupled_maneuvers/lane_change/lane_change_env_analyzer_util.h"
#include "planner/decoupled_maneuvers/lane_change/lane_change_object_info.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_route_association/predicted_trajectory_route_association.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_route_association_common.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_wrapper.h"
#include "planner/decoupled_maneuvers/pull_over/destination_meta_data.h"
#include "planner/decoupled_maneuvers/required_lateral_gap/control_error.h"
#include "planner/planning_gflags.h"
#include "planner/speed/constraint/constraint.h"
#include "planner/speed/discomforts/discomforts.h"
#include "planner/speed/overlap/overlap_lib_util.h"
#include "planner/speed/reasoning/reasoning_basic_util.h"
#include "planner/speed/reasoning/route_association_util.h"
#include "planner/speed/reasoning_input/reasoning_object.h"
#include "planner/speed/reasoning_input/traffic_rules/crosswalk_traffic_rule.h"
#include "planner/speed/reasoning_input/traffic_rules/lane_info_traffic_rule.h"
#include "planner/speed/reasoning_input/traffic_rules/traffic_light_info.h"
#include "planner/speed/reasoning_input/traffic_rules/traffic_rule_utility.h"
#include "planner/speed/reasoning_input/traffic_rules/zone_traffic_rule.h"
#include "planner/speed/reasoning_input/trajectory_info.h"
#include "planner/utility/common/common_utility.h"
#include "planner/utility/object_id/typed_object_id.h"
#include "planner/utility/unstuck/unstuck_util.h"
#include "planner/world_model/planner_object/planner_object.h"
#include "planner_protos/agent_intention.pb.h"
#include "planner_protos/behavior_common_type.pb.h"
#include "planner_protos/overlap.pb.h"
#include "planner_protos/speed_generator_config.pb.h"
#include "planner_protos/speed_reasoner.pb.h"
#include "planner_protos/speed_seed.pb.h"
#include "pnc_map_service/map_elements/lane.h"
#include "pnc_map_service/map_elements/road.h"
#include "pnc_map_service/map_elements/zone.h"
#include "prediction/common/definition/prediction_constants.h"
#include "prediction_protos/predicted_trajectory.pb.h"
#include "rt_event/rt_event.h"
#include "voy_protos/math.pb.h"
#include "voy_protos/perception_object_type.pb.h"
#include "voy_rt_event/rt_event_planner.h"

namespace planner {
namespace speed {
namespace reasoning_util {
namespace {
// If an agent's absolute lateral gap with ego (distance between agent contour
// and ego path) is within the threshold, it is likely to tailgate ego. The
// agent may be in the same lane as ego, or lane change to ego lane from behind.
constexpr double kMaxLateralGapForLikelyTailgatingInDrivingLane = 0.0;  // m
constexpr double kMaxLateralGapForLikelyTailgatingInJunction = 1.0;     // m
// If an agent's longitudinal distance with ego is over the threshold,
// the agent is too far away behind ego, and it is unlikely to tailgate ego.
// This threshold should take into account ego ra to rear bumper shift.
constexpr double kMaxLongitudinalGapForLikelyTailgating = 30.0;  // m
// If an agent's absolute lateral gap with ego is within the threshold,
// it is considered as fully behind the Ego. This implies the agent
// is either at the same lane or the neighbor lanes of Ego.
constexpr double kMaxLateralGapForFullyBehindVehicles = 2.0;  // m
// The maximum distance that road exit zone before ego should be considered.
[[maybe_unused]] constexpr double kMaxExitZoneDistanceToEgoForCautionInMeter =
    30.0;
// The minimum relative heading between ego and agent for an agent to be
// considered as exiting from road exit.
constexpr double kMinHeadingDiffForRoadExitVehiclesInRad = M_PI / 6;
// The maximum relative heading between ego and agent for an agent to be
// considered as fully behind.
constexpr double kMaxHeadingDiffForFullyBehindVehicles = M_PI / 6;
// If the current speed of a cyclist is more than this threshold, this cyclist
// is considered as fast cyclist, and more likely to be a motorcycle or e-bike.
constexpr double kMinSpeedForTurboPedInMps = 4.0;
constexpr double kRequiredLateralGapBufferForStationaryObjectInM = 0.05;
// The minimum relative heading between ego path and overlap slice for an
// overlap region to be considered using varying lateral gap to compute the arc
// length.
[[maybe_unused]] constexpr double
    kMinHeadingDiffForLateralGapVaryingOverlapInRad = M_PI / 6;
// The minimum distance of start ra arc length for an overlap region to be
// considered using varying lateral gap to compute the arc length.
[[maybe_unused]] constexpr double
    kMinStartRaArcLengthForLateralGapVaryingOverlapInMeter = 6.0;
// The minimum relative heading between ego and agent for an agent to be
// considered as leading vehicle.
constexpr double kMinHeadingDiffForLeadingVehicleInRad = M_PI / 4;
// The minimum safety distance with leading vehicle. Aligned with the critical
// yield extra buffer for leading agent.
constexpr double kMinSafetyDistanceWithLeadingVehicleInMeter = 1.0;
// The minimum time to collision with leading vehicle.
constexpr double kMinSafetyTtcWithLeadingVehicleInSec = 2.5;
// If the likelihood of yielding to ego car is less than this threshold,
// agent is considered to have no slow down reaction.
// TODO(junying): Remove this config after we have applied the new threshold to
// all scenarios.
constexpr double kMinYieldIntentionLikelihoodForSlowDownReaction = 0.3;
// If the likelihood of yielding to ego car is less than this threshold,
// agent is considered to have no slow down reaction.
// This new config is suggested by prediction team, reference:
// https://cooper.didichuxing.com/docs/document/2200182849097
constexpr double kNoAgentReactionYieldIntentionThreshold = 0.05;
// If ego speed is below this value, we will not use the yield intention
// We distinguish the speed threshold with the one in the EgoAlmostStopped
// because this config is not fixed right now and may need tuning in the future.
[[maybe_unused]] constexpr double kMinEgoSpeedForValidYieldIntention =
    1.0;  // m / s
// We should only allow ego to emergency brake when the agent is within this
// range to avoid FP(brake for too far away on coming agent).
constexpr double kMinAllowEmergencyBrakeAheadDist = 30.0;  // m / s
// We only check critical segments' intersection with overlap regions within the
// temporal horizon threshold, which should be enough for Ego to take action
// against agents and could alleviate prediction flicker.
[[maybe_unused]] constexpr int64_t
    kPredictionHorizonForCriticalRegionCheckingInMSec = 5000;
// The lower bound of relative lateral speed and acceleration for considering
// the lateral moving agent risk.
// The threshold is determined by
// https://cooper.didichuxing.com/docs2/document/2203285965751.
constexpr double kRelativeLateralAccelLowerBoundForConsiderAgentRiskInMpss =
    0.027;
constexpr double kRelativeLateralSpeedLowerBoundForConsiderAgentRiskInMps = 0.1;
// The maximum distance to consider junction ahead for turning large vehicle gap
// align. The distance is estimated by the minimum traveling distance if ego
// brakes from 35km/h (average speed before turning) to 10km/h (yield min v for
// gap align) with a comfort de-acceleration -1m/ss in 8 sec time horizon plus
// ra to fb offset.
constexpr double kMaxDistanceToConsiderJunctionAheadInMeter = 50.0;
// The maximum lateral gap to consider the object could be a potential u-turn
// blockage.
constexpr double kMaxLateralGapForPotentialUTurnBlockageInMeter = 1.5;
// The maximum longitudinal distance from ego vehicle for an object to be
// considered as a potential u-turn blockage.
constexpr double kLookaheadHorizonForPotentialUTurnBlockageInMeter = 15.0;
// The longitudinal buffer used to find objects near u-turn end point.
constexpr double kLongitudinalBufferToFindUTurnNearbyObjectInMeter = 7.0;
// The longitudinal buffer used to check if ego is in u-turn.
constexpr double kLongitudinalBufferToCheckEgoIsInUTurnInMeter = 7.0;
// The conflict between agent and ego path usually happens at the end of merge
// and fork lane or the beginning of its successive lane, so we extend the end
// range of merge and fork lane to check the overlap.
constexpr double kExtendDistToMergeForkLaneEndInMeter = 10;
// The parameters for considering vehicles crossing ego lane in defensive
// driving.
constexpr double kMaxLatGapForConsideringVehiclesCrossingLaneInMeter = 3.0;
constexpr double kAggressiveEncroachmentDistanceThresholdInMeter = 0.3;
constexpr double kMildEncroachingSpeedThresholdInMps = 0.15;
constexpr double kAggressiveEncroachingSpeedThresholdInMps = 1.0;
// The distance threshold of object lateral gap to ego path which ensure better
// cut-out agent recall.
constexpr double kCutOutLateralGapDistanceThresholdInMeter = 0.2;

constexpr double kMinStationaryDurationToNotConsiderReversingInSeconds = 2.0;
constexpr double kLaneAlignmentAngleThresholdToUnstuckInRad =
    math::Degree2Radian(4.0);
constexpr double kMinStationaryDurationToReducePassRLGToUnstuckInSeconds = 2.0;
constexpr double kMaxAllowedEgoSpeedToReducePassRLGToUnstuckInMps =
    math::KmphToMps(6.0);
// The max index for path point search regarding distance from rear axle to read
// bumper
constexpr size_t kMaxEndIndexForPathSearch = 10;

// Configs to estimate the reaction time.
constexpr double kComfortEsitimatedEgoAccelInMpss = 1.2;
constexpr double kUncomfortEsitimatedEgoAccelInMpss = 1.6;
constexpr double kHardBrakeAccelInMpss = -2;

// Parameters considered to further reduce pass RLG for objects shorther than
// the ego side mirror to unstuck.
constexpr double kEgoSideMirrorHeightClearanceOffGroundInMeter = 1.15;
constexpr double kThresholdToConsiderShorterThanEgoSideMirrorInMeter = 0.28;
constexpr double kEgoSideMirrorExtrusionFromBodyInMeter = 0.1;

// Maximum acceleration of a cyclist to consider applying Mild AR. If cyclist's
// acceleration exceeds this limit, Mild AR will not be applied.
constexpr double kMaxAccelToConsiderCyclistMildARInMpss = 1.0;
// Under UPL scenario, we avoid applying mild AR to vehicles that obvious
// accelerating agents(greater than 1.0 m/s^2), since these agents are less
// likely to yield to ego. Additionally,  we will also scope out the low-speed
// start to move agents, since the predicted velocities of these slow-moving
// agents may be inaccurate, and start to move agents are also less likely to
// yield to the UPL ego. Applying mild AR to such agents could have undesirably
// impact their behavior.
constexpr double kUPLAgentMaxAccelToConsiderMildARInMpss = 1.0;
constexpr double kAgentMinVelocityInUPLToConsiderMildARInMps =
    math::KmphToMps(20.0);
constexpr double kUPLAgentStartToMoveAccelLimitInMpss = 0.5;

// Configs to unstuck ego by adjust PLG based on ego agent approaching trending
constexpr double kMinAllowedRLGToUnstuckForFarawayAgentInMeters = 0.1;

// Check if the agent should be considered as leading agent based on the current
// pose and unextended ego path. 1.) Check agent bounding box and unextended ego
// path to address the issue that considering the agent from behind as leader
// caused by inaccurate proximity in curvy road. 2.) Check if one edge is within
// the critical range or the bounding box is overlapping with path centerline.
// To filter out the agent in intermediate state during cut-in and merge.
// TODO(chantong): Add smooth transition if it is necessary.
bool ShouldConsiderCurrentPoseAsLeadingAgent(
    const pb::ObjectCornerPointsProximityInfo& corner_points_proximity_info,
    const TrajectoryInfo& trajectory_info, double required_lateral_gap) {
  // Checks if the corner point proximity is out of unextended ego path range.
  const auto is_out_of_path_range =
      [&trajectory_info](
          const math::pb::ProximityQueryInfo& proximity) -> bool {
    return !math::IsInRange(
        proximity.arc_length() -
            trajectory_info.plan_init_ra_arc_length_on_extended_path(),
        0.0, trajectory_info.unextended_path_for_overlap().GetTotalArcLength());
  };

  // Checks if the corner point is inside the unextened path buffer range
  // which includes the ADV half width and required lateral gap.
  const auto is_within_roi =
      [&trajectory_info, &required_lateral_gap, &is_out_of_path_range](
          const math::pb::ProximityQueryInfo& proximity) -> bool {
    if (is_out_of_path_range(proximity)) {
      return false;
    }
    return proximity.dist() < trajectory_info.unextended_path_for_overlap()
                                      .vehicle_params()
                                      .half_width +
                                  required_lateral_gap;
  };

  // Checks if the given two corner points are located in the different side of
  // the unextened path.
  const auto is_located_on_diff_side =
      [&is_out_of_path_range](
          const math::pb::ProximityQueryInfo& lhs_proximity,
          const math::pb::ProximityQueryInfo& rhs_proximity) -> bool {
    if (is_out_of_path_range(lhs_proximity) ||
        is_out_of_path_range(rhs_proximity)) {
      return false;
    }
    return lhs_proximity.signed_dist() * rhs_proximity.signed_dist() <= 0.0;
  };

  const bool is_left_edge_within_roi =
      is_within_roi(corner_points_proximity_info.front_left_proximity()) &&
      is_within_roi(corner_points_proximity_info.rear_left_proximity());
  const bool is_right_edge_within_roi =
      is_within_roi(corner_points_proximity_info.front_right_proximity()) &&
      is_within_roi(corner_points_proximity_info.rear_right_proximity());
  const bool is_diagonal_crossing =
      is_located_on_diff_side(
          corner_points_proximity_info.front_left_proximity(),
          corner_points_proximity_info.rear_right_proximity()) &&
      is_located_on_diff_side(
          corner_points_proximity_info.front_right_proximity(),
          corner_points_proximity_info.rear_left_proximity());
  if (is_left_edge_within_roi || is_right_edge_within_roi ||
      is_diagonal_crossing) {
    return true;
  }

  return false;
}

// Return true if the agent state is leaving ego path by checking 1)if the two
// front corner points are on the same side of the path. 2) If state heading
// towards the same side of the corner points.
//      |*     |
//     *|  *   |
//      |*   * |
//      |  *   |
//      |      |*
//      |     *|  *
//      |    * | *
//      |   *  |*
//      |     *|
//      | path |
bool IsAgentStateLeavingEgoPath(
    const pb::ObjectCornerPointsProximityInfo& first_strict_slice_proximity,
    double relative_heading) {
  const math::pb::Side front_left_point_side =
      first_strict_slice_proximity.front_left_proximity().side();
  const math::pb::Side front_right_point_side =
      first_strict_slice_proximity.front_right_proximity().side();
  if (front_left_point_side != front_right_point_side) {
    return false;
  }
  DCHECK_NE(front_left_point_side, math::pb::Side::kOn);
  // |fb_on_the_left| indicates the two corner points of the front bumper lies
  // in the left side of the ego path.
  const bool fb_on_the_left = front_left_point_side == math::pb::Side::kLeft;

  return (fb_on_the_left && relative_heading > 0) ||
         (!fb_on_the_left && relative_heading < 0);
}

// Returns true if the time computed by the lateral gap and lateral speed of
// agent's proximity info is less than 1.5 seconds.
bool DoesAgentEnterEgoPathSoonBasedOnProximityInfo(
    const ReasoningObject& reasoning_object) {
  // If the agent's lateral speed with respect to Ego path is less than this
  // threshold, the time entering ego path is not computed and false is
  // returned.
  constexpr double kMinLateralSpeedInMps = 1.0;
  constexpr double kMaxTimeEnteringEgoPathInSeconds = 1.5;
  if (reasoning_object.is_laterally_moving_away()) {
    return false;
  }

  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();
  const double abs_lateral_speed =
      std::abs(object_proximity_info.signed_lateral_speed());
  // For agents parallel with Ego with a small lateral gap and small lateral
  // speed, the time entering Ego path is small but their small lateral movement
  // is usual specially cyclists. Thus, agents with large lateral speed are only
  // considered.
  if (abs_lateral_speed < kMinLateralSpeedInMps) {
    return false;
  }
  const double agent_enter_ego_path_time =
      object_proximity_info.abs_lateral_gap() / abs_lateral_speed;
  // We only consider cyclists entering Ego path soon.
  return agent_enter_ego_path_time < kMaxTimeEnteringEgoPathInSeconds;
}

// Returns true if we should latch the cross intention. Considering the
// instability of prediction module, we should latch the historical result to
// increase decision stability.
bool ShouldLatchAgentConsistentCrossIntentionCount(
    const ReasoningObject& reasoning_object,
    const pb::AgentCrossIntentionTracker&
        previous_iter_agent_cross_intention_tracker,
    const int64_t current_timestamp) {
  // The following two constants are used to determine how long the cross
  // intention should be latched. After agents come to full stop, the predicted
  // trajectories could be very short and do not show cross intention. Thus,
  // the cross intention should be latched for a long while for stationary
  // agents. However, for dynamic agents, we expect that the prediction module
  // only does not give predicted trajectories with cross intention for
  // agents with cross intention for a few frames.
  constexpr int64_t kCrossIntentionLatchingDurationForStationaryAgentInMs =
      2500;
  constexpr int64_t kCrossIntentionLatchingDurationForDynamicAgentInMs = 500;

  const auto& previous_iter_object_id_to_agent_cross_intention_info_map =
      previous_iter_agent_cross_intention_tracker
          .object_id_to_agent_cross_intention_info_map();
  const auto agent_cross_intention_iter =
      previous_iter_object_id_to_agent_cross_intention_info_map.find(
          reasoning_object.id());
  if (agent_cross_intention_iter ==
      previous_iter_object_id_to_agent_cross_intention_info_map.end()) {
    return false;
  }

  // The agent does not have predicted trajectory having cross intention but it
  // has in the last few frames. The cross intention is latched for a short
  // while to absorb the uncertainty of prediction.
  const int64_t cross_intention_latching_duration =
      reasoning_object.IsStationary()
          ? kCrossIntentionLatchingDurationForStationaryAgentInMs
          : kCrossIntentionLatchingDurationForDynamicAgentInMs;
  return agent_cross_intention_iter->second
             .latest_timestamp_having_cross_intention() >=
         current_timestamp - cross_intention_latching_duration;
}

// Searches lanes potentially being encroached around the ego lane along the
// specified direction. Do not consider encroachment caused by conflicting road
// structures (e.g., encroaching to merging lanes).
// NOTE: It does not consider cases where ego encroaches multiple lanes under a
// large nudging behavior. Advanced methods need to be developed to get all
// encroached lanes accurately.
std::vector<const pnc_map::Lane*> SearchCandidateEncroachedLanesOnTargetSide(
    const pnc_map::Lane* source_lane,
    const math::pb::Side target_side_to_source_lane,
    const planner::pb::EgoCurrentLaneRelativeState::Enum ego_relative_state) {
  // We currently only check the right or left adjacent lane.
  // TODO(speed): Cover more general cases, including non-adjacent neighbor
  // lanes, neighbor-or-fork-section and non-conflicting lanes, merging lanes,
  // etc.
  const pnc_map::Lane* left_neighbor = source_lane->adjacent_left_lane();
  const pnc_map::Lane* right_neighbor = source_lane->adjacent_right_lane();
  switch (target_side_to_source_lane) {
    case math::pb::Side::kLeft: {
      if (ego_relative_state !=
              planner::pb::EgoCurrentLaneRelativeState::FullyOnLeftOutside &&
          left_neighbor != nullptr) {
        return {left_neighbor};
      }
      return {};
    }
    case math::pb::Side::kRight: {
      if (ego_relative_state !=
              planner::pb::EgoCurrentLaneRelativeState::FullyOnRightOutside &&
          right_neighbor != nullptr) {
        return {right_neighbor};
      }
      return {};
    }
    case math::pb::Side::kOn:
      return {source_lane};
    default:
      DCHECK(false) << "Not handled side";
      return {};
  }
  return {};
}

bool EgoInUTurnAndHasLaneConflictWithAgent(
    const std::vector<const pnc_map::Lane*>& covered_ego_lanes,
    const std::vector<route_association::MapElementAndPoseInfo>& agent_route) {
  // Find the u_turn lanes with conflicting lanes.
  const auto lane_iter = std::find_if(
      covered_ego_lanes.begin(), covered_ego_lanes.end(),
      [](const pnc_map::Lane* ego_lane) {
        return ego_lane != nullptr && ego_lane->turn() == hdmap::Lane::U_TURN &&
               !ego_lane->conflicting_lanes().empty();
      });
  const pnc_map::Section* conflicting_section_ptr =
      lane_iter != covered_ego_lanes.end() &&
              !(*lane_iter)->conflicting_lanes().empty()
          ? (*lane_iter)->conflicting_lanes().front().lane()->section()
          : nullptr;
  // Early return false, if ego's lane does not conflict with others.
  if (conflicting_section_ptr == nullptr) {
    return false;
  }
  // NOTE: We check predecessors to the conflicting section as well in case the
  // lane association result misses associating the conflicting lane when the
  // last few poses are very close to the predecessor.
  const bool is_agent_through_conflicting_section_or_its_predecessor =
      std::find_if(
          agent_route.begin(), agent_route.end(),
          [conflicting_section_ptr](
              const route_association::MapElementAndPoseInfo& element) {
            return element.lane_ptr != nullptr &&
                   (element.lane_ptr->section()->id() ==
                        conflicting_section_ptr->id() ||
                    element.lane_ptr->section()->IsSuccessor(
                        conflicting_section_ptr->id()));
          }) != agent_route.end();

  return is_agent_through_conflicting_section_or_its_predecessor;
}

bool EgoInUPLAndHasLaneConflictWithAgent(
    const OverlapRegionInfo& overlap_region_info) {
  return overlap_region_info.includes_conflicting_lanes &&
         overlap_region_info.is_in_unprotected_left_turn;
}

bool ShouldConsiderEgoShowIntentionTimeForVRU(
    const ReasoningObject& reasoning_object,
    const OverlapRegionInfo& overlap_region_info) {
  DCHECK(reasoning_object.is_pedestrian_or_cyclist());
  // Ego is in upl and the VRU is not a same direction agent.
  bool is_target_scene = overlap_region_info.is_in_unprotected_left_turn &&
                         (overlap_region_info.includes_conflicting_lanes ||
                          overlap_region_info.is_using_crosswalk);
  if (!is_target_scene) {
    return false;
  }
  const std::vector<OverlappedCrosswalkInfo>& overlapped_crosswalk_infos =
      overlap_region_info.overlapped_crosswalk_infos;
  if (!overlapped_crosswalk_infos.empty() &&
      std::none_of(overlapped_crosswalk_infos.begin(),
                   overlapped_crosswalk_infos.end(),
                   [](const OverlappedCrosswalkInfo& crosswalk) {
                     return crosswalk.is_using_crosswalk &&
                            crosswalk.crosswalk.IsGreenLight();
                   })) {
    // If the agent is on the crosswalk and the crosswalk light is not green,
    // there are two situations:
    // 1. prediction FP
    // 2. the PED/CYC is red light runner
    // We only apply longer reaction time when the crosswalk traffic light
    // is green to avoid over-yield.
    return false;
  }
  return true;
}

bool ShouldConsiderEgoShowIntentionTimeForVehicle(
    const ReasoningObject& reasoning_object,
    const OverlapRegionInfo& overlap_region_info) {
  DCHECK(reasoning_object.is_vehicle());
  // Ego is in UPL and the vehicle is in conflicting lanes rather than in
  // adjacent/same direction lanes.
  return (overlap_region_info.is_in_unprotected_left_turn ||
          overlap_region_info.is_in_u_turn) &&
         overlap_region_info.includes_conflicting_lanes;
}

}  // namespace

bool ShouldConsiderOverlapAsTailgating(
    const WorldContext& world_context,
    const ReasoningObject& /* reasoning_object */,
    const AgentTrajectoryInfo& /* agent_trajectory_info */,
    const pb::OverlapRegion& overlap_region) {
  // Check if the overlap passes through the front axle.
  return reasoning_util::IsStrictOverlapRegionStartingBehind(
      overlap_region,
      world_context.ra_to_front_axle() - world_context.ra_to_leading_bumper());
}

bool IsLeadingAgent(const TrajectoryInfo& trajectory_info,
                    const ReasoningObject& reasoning_object,
                    const double min_ra_arc_length) {
  // Only consider these types of objects to acc.
  if (!CanAccAgent(reasoning_object.object_type())) {
    return false;
  }

  // Agent that Ego‘s path is overtaking shall not be considered as leading
  // agent.
  if (reasoning_object.is_ego_path_overtaking()) {
    return false;
  }

  const speed::pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();

  if (reasoning_object.IsStationary()) {
    // Should not consider agent whose relative heading with Ego path is
    // cross-like as leading agent. Check box heading for stationary reasoning
    // object.
    if (std::fabs(object_proximity_info.relative_box_heading()) >
        kMinHeadingDiffForLeadingVehicleInRad) {
      return false;
    }
  } else {
    // Should not consider oncoming agent as leading agent.
    if (object_proximity_info.signed_longitudinal_speed() < 0.0) {
      return false;
    }

    // Should not consider agent whose relative heading with Ego path is
    // cross-like as leading agent. Check velocity heading for moving reasoning
    // object.
    if (std::fabs(object_proximity_info.relative_motion_heading()) >
        kMinHeadingDiffForLeadingVehicleInRad) {
      return false;
    }
  }

  // Should not consider agent behind if not performing lane change
  if (object_proximity_info.projected_ra_arc_length().start() <
      min_ra_arc_length) {
    return false;
  }

  const pb::ObjectCornerPointsProximityInfo& corner_points_proximity_info =
      object_proximity_info.has_corner_points_proximity_info()
          ? object_proximity_info.corner_points_proximity_info()
          : ComputeBoxCornerPointsProximityInfo(
                trajectory_info.path(), reasoning_object.planner_object()
                                            .pose_at_plan_init_ts()
                                            .oriented_box());

  // Should comprehensively consider agent's heading and lateral gap to make
  // the final decision. Check the edge instead of single contour, in order to
  // filter out the agent who has only one contour immersed into range of
  // interest especially in merge and cut-in scenarios.
  if (ShouldConsiderCurrentPoseAsLeadingAgent(corner_points_proximity_info,
                                              trajectory_info,
                                              /*required_lateral_gap=*/0.0)) {
    return true;
  }

  return false;
}

bool IsSafeWithLeadingAgent(const ReasoningObject& reasoning_object,
                            double ego_speed_mps, double ra_to_front_bumper) {
  DCHECK(reasoning_object.is_leading_agent());

  // Get object_proximity_info for multiple uses.
  const speed::pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();

  const double distance_to_ego_front_bumper =
      object_proximity_info.projected_ra_arc_length().start() -
      ra_to_front_bumper;
  // Return false if ADV immersed into safety distance with leading agent.
  if (distance_to_ego_front_bumper <
      kMinSafetyDistanceWithLeadingVehicleInMeter) {
    return false;
  }

  const double relative_speed_mps =
      ego_speed_mps - object_proximity_info.signed_longitudinal_speed();
  // Return true if ADV will not approach to leading agent in a period.
  if ((relative_speed_mps <= 0.0 ||
       std::abs(distance_to_ego_front_bumper /
                (relative_speed_mps + math::constants::kEpsilon)) >
           kMinSafetyTtcWithLeadingVehicleInSec)) {
    return true;
  }

  return false;
}

bool IsFullyBehind(const ReasoningObject& reasoning_object,
                   double ra_to_rear_bumper_shift) {
  // Get object_proximity_info for multiple uses.
  const speed::pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();

  // Skip agents that are ahead of ego's rear bumper.
  if (object_proximity_info.projected_ra_arc_length().end() >
      ra_to_rear_bumper_shift)
    return false;

  // We trust the lateral gap result only if object is in path range,
  if (!object_proximity_info.out_of_path_range()) {
    if (object_proximity_info.abs_lateral_gap() <
        kMaxLateralGapForFullyBehindVehicles) {
      return true;
    }
    // For those are beyond the lateral gap thresh, keep the ones have small
    // relative heading to ego path (< pi/6 here).
    return object_proximity_info.relative_motion_heading() <
           kMaxHeadingDiffForFullyBehindVehicles;
  }

  // Those out of path range and behind ego's rear bumper are ignored.
  return false;
}

bool IsBehindEgoFrontAxle(const ReasoningObject& reasoning_object,
                          double ra_to_front_axle) {
  const speed::pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();

  return object_proximity_info.projected_ra_arc_length().end() <
         ra_to_front_axle;
}

bool IsBehindEgoLeadingBumper(const ReasoningObject& reasoning_object,
                              double ra_to_leading_bumper) {
  const speed::pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();
  return object_proximity_info.projected_ra_arc_length().end() <
         ra_to_leading_bumper;
}

bool IsFullyAhead(const ReasoningObject& reasoning_object,
                  double ra_to_leading_bumper) {
  const speed::pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();
  return object_proximity_info.projected_ra_arc_length().start() >
         ra_to_leading_bumper;
}

bool IsSynLaneChangeBehind(
    const ReasoningObject& reasoning_object,
    const std::unordered_map<
        int64_t,
        std::unordered_map<std::string, planner::pb::XRegionMotionInfo>>&
        object_xregion_motion_info_map,
    const int64_t plan_init_timestamp) {
  if (!reasoning_object.IsBehindEgoLeadingBumper()) {
    return false;
  }

  auto iter = object_xregion_motion_info_map.find(reasoning_object.id());
  if (iter == object_xregion_motion_info_map.end()) {
    return false;
  }
  const std::unordered_map<std::string, planner::pb::XRegionMotionInfo>&
      cut_motion_info = iter->second;
  auto motion_iter = cut_motion_info.find(planner::pb::XRegionMotionType_Name(
      planner::pb::XRegionMotionType::SOURCE_TO_TARGET));
  if (motion_iter == cut_motion_info.end()) {
    return false;
  }
  const int64_t entering_timestamp = motion_iter->second.entering_timestamp();
  constexpr int64_t kLifeCycleForSynLaneChangeInMSec = 2000;
  return plan_init_timestamp - entering_timestamp <
         kLifeCycleForSynLaneChangeInMSec;
}

bool IsLikelyTailgating(const TrajectoryInfo& trajectory_info,
                        const ReasoningObject& reasoning_object,
                        double /* ra_to_rear_bumper_shift */,
                        const int64_t plan_init_timestamp) {
  // TODO(xinyue): need to carefully consider cyclists.
  if (reasoning_object.object_type() != voy::perception::ObjectType::VEHICLE) {
    return false;
  }
  if (!reasoning_object.is_fully_behind()) {
    return false;
  }
  if (trajectory_info.is_lane_change_in_progress() &&
      (reasoning_util::IsLaneChangeTailingAgent(trajectory_info,
                                                reasoning_object) ||
       IsSynLaneChangeBehind(reasoning_object,
                             trajectory_info.cross_lane_info_manager()
                                 .GetCrossLaneInfo()
                                 .object_id_to_xregion_motion_info,
                             plan_init_timestamp))) {
    return false;
  }
  // Get object_proximity_info for multiple uses.
  const speed::pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();

  DCHECK_LT(object_proximity_info.projected_ra_arc_length().end(), 0);

  // Skip agents that are far away behind ego in longitudinal distance.
  if (-object_proximity_info.projected_ra_arc_length().end() >
      kMaxLongitudinalGapForLikelyTailgating) {
    return false;
  }

  // Skip agents that are not moving in the same direction as ego along ego
  // path.
  if (object_proximity_info.signed_longitudinal_speed() < 0.0) {
    return false;
  }

  // Relax the lateral gap threshold if ego is in junciton.
  // Explanation: In practice, the agent is not necessarily to follow the ego
  // path tightly in the junction, so the lateral gap could be a little bigger
  // than normal.
  // TODO(pengxu): Once we have an inexpensive way to find if the agent is in
  // junction, we shall include the info here for reasoning.
  const double max_lateral_gap_for_likely_tailgating =
      trajectory_info.IsEgoInJunction()
          ? kMaxLateralGapForLikelyTailgatingInJunction
          : kMaxLateralGapForLikelyTailgatingInDrivingLane;

  // Skip agents that are far away from ego in lateral distance.
  if (object_proximity_info.abs_lateral_gap() >
      max_lateral_gap_for_likely_tailgating) {
    return false;
  }

  return true;
}

bool IsDominantStuckObjectForXLaneNudge(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object) {
  // Only conside the normal lane keep behavior with valid xlane nudge meta.
  if (trajectory_info.behavior_type() != planner::pb::BehaviorType::LANE_KEEP ||
      !trajectory_info.xlane_nudge_meta().has_value() ||
      !(trajectory_info.xlane_nudge_meta()->dominant_object_id.has_value()) ||
      trajectory_info.is_pull_out_jump_out() ||
      trajectory_info.is_waypoint_assist_invoked()) {
    return false;
  }

  const TypedObjectId& dominant_object_id =
      trajectory_info.xlane_nudge_meta()->dominant_object_id.value();
  // Return true if the current object is the dominant stuck object for xlane
  // nudge.
  return !dominant_object_id.is_construction_zone() &&
         dominant_object_id.id == reasoning_object.id();
}

bool IsEgoPathOvertaking(const ReasoningObject& reasoning_object,
                         const planner::pb::IntentionResult& ego_intention) {
  if (ego_intention.homotopy() == planner::pb::IntentionResult::IGNORE_ALL)
    return false;

  const auto iter =
      ego_intention.object_intentions().find(reasoning_object.id());
  if (iter == ego_intention.object_intentions().end()) return false;

  return iter->second.is_overtaken();
}

bool IsFoDPreferableToDriveThrough(
    const ReasoningObject& reasoning_object,
    const planner::pb::IntentionResult& ego_intention) {
  if (!reasoning_object.planner_object().is_object_drivable()) return false;

  const auto iter =
      ego_intention.fod_intention_map().find(reasoning_object.id());
  if (iter == ego_intention.fod_intention_map().end()) {
    return false;
  }

  return iter->second == planner::pb::IntentionResult::DRIVE_THROUGH;
}

bool IsAgentInNeighboringLane(const TrajectoryInfo& trajectory_info,
                              const ReasoningObject& reasoning_object) {
  const double signed_lateral_gap =
      reasoning_object.object_proximity_info().signed_lateral_gap();
  const pnc_map::Lane* current_lane =
      *trajectory_info.lane_sequence_iterator().current_lane();
  // adjacent_left_lane() and adjacent_right_lane() refer to the neighboring
  // lanes that share the lane marking with the querying lane, excluding those
  // lanes separated by physical boundaries, such as the fence, the flower bed,
  // etc. left_lane() and right_lane() are not recommended to be used here as
  // they may not have values in such cases, according to the map team.
  const pnc_map::Lane* neighboring_lane =
      (signed_lateral_gap > 0) ? current_lane->adjacent_left_lane()
                               : current_lane->adjacent_right_lane();

  if (neighboring_lane == nullptr) {
    return false;
  }

  const math::geometry::Point2d& object_center =
      reasoning_object.planner_object().center_2d();

  // Is object center in lane border polygon.
  return math::geometry::Within(object_center, neighboring_lane->border());
}

bool IsAgentInNeighboringLaneSequence(const TrajectoryInfo& trajectory_info,
                                      const ReasoningObject& reasoning_object) {
  // TODO(zhihao): Leverage
  // reasoning_object.agent_occupied_map_element_infos_in_seed_ptr() to get
  // reasoning object possible occupied lanes, saving some duplicated
  // computation (need minor refactor of lane-association in a later CR)

  const double signed_lateral_gap =
      reasoning_object.object_proximity_info().signed_lateral_gap();

  const math::geometry::Point2d& object_center =
      reasoning_object.planner_object().center_2d();

  for (auto lane_iter = trajectory_info.lane_sequence_iterator().current_lane();
       lane_iter != trajectory_info.lane_sequence_iterator().end();
       ++lane_iter) {
    const pnc_map::Lane* lane = *lane_iter;
    // adjacent_left_lane() and adjacent_right_lane() refer to the neighboring
    // lanes that share the lane marking with the querying lane, excluding those
    // lanes separated by physical boundaries, such as the fence, the flower
    // bed, etc. left_lane() and right_lane() are not recommended to be used
    // here as they may not have values in such cases, according to the map
    // team.
    const pnc_map::Lane* neighboring_lane = (signed_lateral_gap > 0)
                                                ? lane->adjacent_left_lane()
                                                : lane->adjacent_right_lane();
    if (neighboring_lane == nullptr) {
      continue;
    }
    // Is object center in lane border polygon.
    if (math::geometry::Within(object_center, neighboring_lane->border())) {
      return true;
    }
  }

  return false;
}

bool IsCutOutOverlapRegion(const pb::OverlapRegion& overlap_region) {
  if (!overlap_region.contain_strict_overlap()) {
    return false;
  }

  // Since the overlap region consists of the states that overlap with ego path
  // within a required lateral gap, we deem it is leaving ego path if it is
  // strictly overlapped on first slice and not strictly overlapped on the last
  // slice.
  const bool is_first_slice_strict_overlapped =
      overlap_region.first_strict_agent_state_ix() ==
      overlap_region.first_padded_agent_state_ix();
  const bool is_last_slice_leaving_ego_path =
      overlap_region.last_padded_agent_state_ix() >
      overlap_region.last_strict_agent_state_ix();

  return is_first_slice_strict_overlapped && is_last_slice_leaving_ego_path;
}

bool IsAgentTrajectoryCuttingInEgoPath(
    const AgentTrajectoryInfo& agent_trajectory_info) {
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      agent_trajectory_info.overlap_region_infos();

  // TODO(chantong): Check all overlap regions and their relations because we
  // could not confirm it's a cut-in BP if it has just one cut-in overlap
  // region.
  return std::any_of(overlap_region_infos.begin(), overlap_region_infos.end(),
                     [](const OverlapRegionInfo& overlap_region_info) {
                       return overlap_region_info.is_cut_in;
                     });
}

bool IsEgoAboutToTurnAlongWithLargeVehicleInJunction(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const planner::PredictedTrajectoryWrapper& predicted_trajectory) {
  DCHECK(reasoning_object.planner_object().id() ==
         predicted_trajectory.object_id());
  if (!reasoning_object.IsLargeVehicle()) {
    return false;
  }
  const traffic_rules::JunctionInLaneSequence* first_junction_ahead_ptr =
      GetFirstJunctionAhead(
          trajectory_info.traffic_rules().lane_sequence_junctions);
  if (first_junction_ahead_ptr == nullptr) {
    return false;
  }
  const bool is_ego_about_to_turn =
      first_junction_ahead_ptr->ra_arclength_range_m.start_pos <
          kMaxDistanceToConsiderJunctionAheadInMeter &&
      first_junction_ahead_ptr->ego_lane_in_junction_ptr->turn() !=
          hdmap::Lane_Turn_STRAIGHT;
  if (!is_ego_about_to_turn) {
    return false;
  }
  const bool is_proximity_same_direction =
      std::abs(
          reasoning_object.object_proximity_info().relative_motion_heading()) <
      M_PI_4;
  if (!is_proximity_same_direction) {
    return false;
  }
  const std::optional<std::vector<route_association::MapElementAndPoseInfo>>&
      agent_route_opt = predicted_trajectory.associated_route_opt();
  const std::vector<route_association::MapElementAndPoseInfo>& agent_route =
      agent_route_opt.has_value()
          ? agent_route_opt.value()
          : reasoning_util::GetAdHocAssociatedRouteForPredictedTrajectory(
                reasoning_object, predicted_trajectory,
                *world_context.joint_pnc_map_service(),
                /*allow_using_for_cyclist=*/false);
  const bool is_ego_about_to_turn_along_with_lv =
      route_association_util::IsAgentTurningAlongWithEgoInJunction(
          first_junction_ahead_ptr->ego_lane_in_junction_ptr, agent_route);
  return is_ego_about_to_turn_along_with_lv;
}

bool IsAgentTrajectoryCuttingOutFromEgoPath(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info) {
  if (!trajectory_info.IsLaneKeep()) {
    // TODO(jinhao): Only consider the signal within the lane keep scenario.
    // Will expand the scope if necessary.
    return false;
  }

  if (FLAGS_planning_enable_agent_predicted_trajectory_route_association) {
    const pnc_map::Lane* agent_trajectory_current_lane =
        agent_trajectory_info.agent_trajectory_current_lane();

    if (agent_trajectory_current_lane == nullptr) {
      return false;
    }

    const pnc_map::Lane* ego_current_lane =
        *trajectory_info.lane_sequence_iterator().current_lane();

    if (ego_current_lane->id() != agent_trajectory_current_lane->id() &&
        !ego_current_lane->IsSuccessor(*agent_trajectory_current_lane)) {
      // Only consider the agent trajectory driving in ego's current lane or the
      // successor of current lane.
      return false;
    }
  }

  if (reasoning_object.object_proximity_info().abs_lateral_gap() >
      kCutOutLateralGapDistanceThresholdInMeter) {
    // Only consider the agent with lateral gap close to ego path.
    return false;
  }

  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      agent_trajectory_info.overlap_region_infos();

  return std::any_of(overlap_region_infos.begin(), overlap_region_infos.end(),
                     [](const OverlapRegionInfo& overlap_region_info) {
                       return overlap_region_info.is_cut_out;
                     });
}

bool IsEgoMergingToHigherPriorityConflictingLane(
    const ReasoningObject& reasoning_object,
    const TrajectoryInfo& trajectory_info,
    const AgentTrajectoryInfo& agent_trajectory_info,
    const pb::OverlapRegion& overlap_region) {
  if (!trajectory_info.IsInRightTurn()) {
    return false;
  }

  if (!reasoning_object.is_vehicle_or_cyclist()) {
    // Returns false if the other's object_type is NOT vehicle or cyclist.
    return false;
  }

  if (!HasStrictOverlap(overlap_region)) {
    return false;
  }
  const math::Range1d& strict_overlap_ra_range = {
      GetStrictOverlapStart(overlap_region),
      GetStrictOverlapEnd(overlap_region)};

  if (math::NearZero(strict_overlap_ra_range.start_pos) &&
      math::NearZero(strict_overlap_ra_range.end_pos)) {
    // The strict overlap is invalid.
    return false;
  }

  // Returns true if the current overlap region has overlap with conflicting
  // region.
  // TODO(waylon): Check the conflicting region's precedence is high.
  return std::any_of(
      agent_trajectory_info.associated_conflicting_lanes().begin(),
      agent_trajectory_info.associated_conflicting_lanes().end(),
      [&strict_overlap_ra_range](
          const traffic_rules::ConflictingLaneInLaneSequence&
              conflicting_lane) {
        return conflicting_lane.priority ==
                   planner::pb::RoadPriorityType::HIGHER &&
               math::AreRangesOverlapping(
                   strict_overlap_ra_range,
                   conflicting_lane.conflicting_range_along_track);
      });
}

bool IsLikelyTurboPed(const ReasoningObject& reasoning_object) {
  return reasoning_object.is_cyclist() &&
         reasoning_object.speed() > kMinSpeedForTurboPedInMps;
}

bool WithEBInterestedRange(const WorldContext& world_context,
                           const ReasoningObject& reasoning_object) {
  const speed::pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();
  if (object_proximity_info.out_of_path_range()) {
    return false;
  }

  if (reasoning_object.IsBehindEgoFrontAxle()) {
    return false;
  }
  if (object_proximity_info.projected_ra_arc_length().start() >
      world_context.ra_to_leading_bumper() + kMinAllowEmergencyBrakeAheadDist) {
    // longitudinally too far away
    return false;
  }
  return true;
}

bool ShouldAllowEmergencyBrake(const WorldContext& world_context,
                               const ReasoningObject& reasoning_object) {
  const bool within_interested_range =
      WithEBInterestedRange(world_context, reasoning_object);
  const bool within_interested_object_type =
      reasoning_object.is_vehicle() || reasoning_object.is_pedestrian() ||
      reasoning_object.is_cyclist() || reasoning_object.is_animal();
  return within_interested_range && within_interested_object_type &&
         reasoning_object.IsOnEgoPath();
}

bool ShouldAllowEmergencyBrakeForVRU(const WorldContext& world_context,
                                     const ReasoningObject& reasoning_object,
                                     const pb::OverlapRegion& overlap_region,
                                     const pb::ReasonerId reasoner_in_charge) {
  // TODO(hezhihang): Remove this after collecting enough data.
  if (!FLAGS_planning_enable_enlarging_scope_of_eb_for_vru) {
    return ShouldAllowEmergencyBrakeForCrossVRU(world_context, reasoning_object,
                                                overlap_region,
                                                reasoner_in_charge) ||
           ShouldAllowEmergencyBrakeForMergingCyclist(
               world_context, reasoning_object, overlap_region,
               reasoner_in_charge);
  }

  const bool old_version_allowing_eb_result =
      ShouldAllowEmergencyBrakeForCrossVRU(world_context, reasoning_object,
                                           overlap_region,
                                           reasoner_in_charge) ||
      ShouldAllowEmergencyBrakeForMergingCyclist(
          world_context, reasoning_object, overlap_region, reasoner_in_charge);
  const bool new_version_allowing_eb_result =
      ShouldAllowEmergencyBrakeForVRUWithLargerScope(
          world_context, reasoning_object, overlap_region, reasoner_in_charge);
  if (!old_version_allowing_eb_result && new_version_allowing_eb_result) {
    rt_event::PostRtEvent<rt_event::planner::VRUAllowingEBDiff>();
  }
  return new_version_allowing_eb_result;
}

bool ShouldAllowEmergencyBrakeForVRUWithLargerScope(
    const WorldContext& world_context, const ReasoningObject& reasoning_object,
    const pb::OverlapRegion& overlap_region,
    const pb::ReasonerId reasoner_in_charge) {
  constexpr double kMinTrackedDurationInSeconds = 1.0;
  // The agent should enter Ego path strictly within this threshold.
  constexpr double kMaxTimeAgentToReachEgoPathInSecond = 1.5;

  DCHECK(reasoning_object.is_pedestrian_or_cyclist());
  DCHECK(  // NOLINT, FP when checking macro DCHECK
      reasoner_in_charge == pb::ReasonerId::CROSS_AGENT ||
      reasoner_in_charge == pb::ReasonerId::VRU ||
      reasoner_in_charge == pb::ReasonerId::JAYWALKER ||
      reasoner_in_charge == pb::ReasonerId::ONCOMING_AGENT ||
      reasoner_in_charge == pb::ReasonerId::LEAD_AND_MERGE)
      << "EB is not allowed for VRU in this reasoner, "
      << pb::ReasonerId_Name(reasoner_in_charge) << ".";

  if (!reasoning_object.is_pedestrian_or_cyclist()) {
    return false;
  }

  if (!HasStrictOverlap(overlap_region)) {
    return false;
  }

  if (!WithEBInterestedRange(world_context, reasoning_object)) {
    return false;
  }

  // Only consider agent entering Ego path strictly in the near future to reduce
  // the influence imposed by uncertainty.
  const bool does_agent_enter_ego_path_soon_based_bp =
      overlap_region.start_strict_relative_time_in_sec() <
      kMaxTimeAgentToReachEgoPathInSecond;
  const bool does_agent_enter_ego_path_soon_based_proximity_info =
      DoesAgentEnterEgoPathSoonBasedOnProximityInfo(reasoning_object);
  if (!does_agent_enter_ego_path_soon_based_bp &&
      !does_agent_enter_ego_path_soon_based_proximity_info) {
    return false;
  }

  if (reasoner_in_charge == pb::ReasonerId::CROSS_AGENT ||
      reasoner_in_charge == pb::ReasonerId::VRU ||
      reasoner_in_charge == pb::ReasonerId::JAYWALKER ||
      reasoner_in_charge == pb::ReasonerId::ONCOMING_AGENT) {
    // Avoid allowing EB for VRUs cut-behind.
    const bool is_strict_overlap_behind = IsStrictOverlapRegionStartingBehind(
        overlap_region, /*ra_arclength_threshold=*/0.0);
    return !is_strict_overlap_behind;
  }

  // Exclude the cyclists not tracked for a long while in order to fix the
  // perception FP, like the issue cn10275414. This will cause late-brake for
  // the cyclists running out from occlusion areas.
  // TODO(hezhihang): Add a condition checking whether an agent emerges from an
  // occlusion area.
  if (reasoning_object.TrackedDurationInSeconds() <
      kMinTrackedDurationInSeconds) {
    return false;
  }

  return true;
}

bool ShouldAllowEmergencyBrakeForCrossVRU(
    const WorldContext& world_context, const ReasoningObject& reasoning_object,
    const pb::OverlapRegion& overlap_region,
    const pb::ReasonerId reasoner_in_charge) {
  DCHECK(reasoning_object.is_pedestrian_or_cyclist());
  // The agent should enter Ego path strictly within this threshold.
  constexpr double kMaxTimeAgentToReachEgoPathInSecond = 1.5;
  if (reasoner_in_charge != pb::ReasonerId::CROSS_AGENT &&
      reasoner_in_charge != pb::ReasonerId::VRU &&

      reasoner_in_charge != pb::ReasonerId::JAYWALKER) {
    return false;
  }

  if (!reasoning_object.is_pedestrian_or_cyclist()) {
    return false;
  }

  if (!HasStrictOverlap(overlap_region)) {
    return false;
  }

  if (!WithEBInterestedRange(world_context, reasoning_object)) {
    return false;
  }

  // Only consider agent entering Ego path strictly in the near future to reduce
  // the influence imposed by uncertainty.
  if (overlap_region.start_strict_relative_time_in_sec() >=
      kMaxTimeAgentToReachEgoPathInSecond) {
    return false;
  }

  // Avoid allowing EB for VRUs cut-behind.
  const bool is_strict_overlap_behind =
      IsStrictOverlapRegionStartingBehind(overlap_region,
                                          /*ra_arclength_threshold=*/0.0);
  if (is_strict_overlap_behind) {
    return false;
  }

  if (std::any_of(overlap_region.overlap_slices().begin(),
                  overlap_region.overlap_slices().end(),
                  [](const pb::OverlapSlice& overlap_slice) {
                    return overlap_slice.motion_type() !=
                           pb::OverlapMotionType::OVERLAP_MOTION_CROSSING;
                  })) {
    return false;
  }

  return true;
}

bool ShouldAllowEmergencyBrakeForMergingCyclist(
    const WorldContext& world_context, const ReasoningObject& reasoning_object,
    const pb::OverlapRegion& overlap_region,
    const pb::ReasonerId reasoner_in_charge) {
  constexpr double kMinTrackedDurationInSeconds = 1.0;
  constexpr double kMinLateralSpeedInMps = 1.5;
  constexpr double kMaxTimeEnteringEgoPathInSeconds = 1.5;
  if (!reasoning_object.is_cyclist()) {
    return false;
  }

  if (reasoner_in_charge != pb::ReasonerId::CROSS_AGENT &&
      reasoner_in_charge != pb::ReasonerId::LEAD_AND_MERGE) {
    return false;
  }

  if (!WithEBInterestedRange(world_context, reasoning_object)) {
    return false;
  }

  // Exclude the cyclists not tracked for a long while in order to fix the
  // perception FP, like the issue cn10275414. This will cause late-brake for
  // the cyclists running out from occlusion areas.
  // TODO(hezhihang): Add a condition checking whether an agent emerges from an
  // occlusion area.
  if (reasoning_object.TrackedDurationInSeconds() <
      kMinTrackedDurationInSeconds) {
    return false;
  }

  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();
  const double abs_lateral_speed =
      std::abs(object_proximity_info.signed_lateral_speed());
  // We only consider cyclists with large lateral speed. Meanwhile, it avoids
  // the undefined behavior of dividing zero in the following division.
  if (abs_lateral_speed < kMinLateralSpeedInMps) {
    return false;
  }
  const double agent_enter_ego_path_time =
      object_proximity_info.abs_lateral_gap() / abs_lateral_speed;
  // We only consider cyclists entering Ego path soon.
  if (reasoning_object.is_laterally_moving_away() ||
      agent_enter_ego_path_time > kMaxTimeEnteringEgoPathInSeconds) {
    return false;
  }

  return std::all_of(
      overlap_region.overlap_slices().begin(),
      overlap_region.overlap_slices().end(),
      [](const pb::OverlapSlice& overlap_slice) {
        return overlap_slice.motion_type() ==
                   pb::OverlapMotionType::OVERLAP_MOTION_CROSSING ||
               overlap_slice.motion_type() ==
                   pb::OverlapMotionType::OVERLAP_MOTION_SAME_DIRECTION;
      });
}

DiscomfortVarying ComputeComfortRequiredLateralGapBaseOnOverlapRegion(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const pb::OverlapRegion& overlap_region,
    const double comfort_required_lateral_gap_lower_bound) {
  DCHECK_GE(comfort_required_lateral_gap_lower_bound, 0.0);
  const double min_lateral_gap =
      GetOverlapRegionMinAbsLateralGap(overlap_region);

  const double critical_required_lateral_gap =
      GetPrincipledCriticalRequiredLateralGap(trajectory_info, reasoning_object,
                                              overlap_region);

  // Subtracts a small buffer to account for numerical errors.
  double comfort_required_lateral_gap = std::min(
      reasoning_object.ComfortRequiredLateralGap(),
      min_lateral_gap - kRequiredLateralGapBufferForStationaryObjectInM);
  comfort_required_lateral_gap =
      std::max(comfort_required_lateral_gap, critical_required_lateral_gap);
  comfort_required_lateral_gap = std::max(
      comfort_required_lateral_gap, comfort_required_lateral_gap_lower_bound);
  return {comfort_required_lateral_gap, critical_required_lateral_gap};
}

bool ShouldAddExtraBufferForConsistentYieldObject(
    const WorldContext& world_context,
    const ReasoningObject& reasoning_object) {
  constexpr int kMinDominantRiskObjectDurationToAdjustYieldExtraTime = 3;
  if (reasoning_object.id() ==
          world_context.previous_dominant_risk_object_id() &&
      world_context.previous_dominant_risk_object_duration() >=
          kMinDominantRiskObjectDurationToAdjustYieldExtraTime) {
    return true;
  }
  return false;
}

DiscomfortVarying ComputeYieldExtraDistanceForBlockage(
    const ReasoningObject& reasoning_object, const RobotState& robot_state,
    const double comfort_yield_extra_distance,
    const double critical_yield_extra_distance) {
  DCHECK(reasoning_object.is_blocking_object());

  const double comfort_yield_extra_distance_in_stuck =
      planner::GetYieldDistanceForStuckingObject(
          reasoning_object.planner_object(), robot_state,
          reasoning_object.ComfortRequiredLateralGap());
  const double critical_yield_extra_distance_in_stuck =
      planner::GetYieldDistanceForStuckingObject(
          reasoning_object.planner_object(), robot_state,
          reasoning_object.CriticalRequiredLateralGap());

  DCHECK_LE(critical_yield_extra_distance_in_stuck,
            comfort_yield_extra_distance_in_stuck);
  DCHECK_LE(critical_yield_extra_distance, comfort_yield_extra_distance);

  return {std::max(comfort_yield_extra_distance_in_stuck,
                   comfort_yield_extra_distance),
          std::max(critical_yield_extra_distance_in_stuck,
                   critical_yield_extra_distance)};
}

bool ShouldAdjustYieldExtraDistanceForPullOver(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object) {
  if (!trajectory_info.is_pull_over_jump_in_trajectory()) {
    return false;
  }

  // The blockage agent should be front of the pull over destination.
  const math::geometry::Point2d& pull_over_destination =
      DCHECK_NOTNULL(trajectory_info.pull_over_dest_meta_ptr())
          ->destination_pt();
  const double pullover_dest_ra_arclength =
      trajectory_info.path()
          .QueryProximity(pull_over_destination,
                          math::pb::UseExtensionFlag::kForbid)
          .arc_length -
      trajectory_info.plan_init_ra_arc_length_on_extended_path();

  if (reasoning_object.object_proximity_info()
          .projected_ra_arc_length()
          .start() < pullover_dest_ra_arclength) {
    return false;
  }

  return true;
}

bool ShouldConsiderDefensiveDrivingForReversingVehicles(
    const ReasoningObject& reasoning_object) {
  if (!reasoning_object.is_vehicle()) {
    return false;
  }
  if (!reasoning_object.has_reverse_or_kturn_trajectory()) {
    return false;
  }
  if (reasoning_object.planner_object().is_parked_car()) {
    return false;
  }
  const auto& ref_art_object_info = reasoning_object.art_object_info();
  if (ref_art_object_info.has_value() &&
      ref_art_object_info.value().near_stationary_duration >
          kMinStationaryDurationToNotConsiderReversingInSeconds) {
    return false;
  }
  if (reasoning_object.IsBehindEgoFrontAxle()) {
    return false;
  }
  return true;
}

bool ShouldBeCautiousForBusNearBusBulb(const ReasoningObject& reasoning_object,
                                       const TrajectoryInfo& trajectory_info) {
  if (!reasoning_object.is_bus_near_bus_bulb() ||
      !reasoning_object.IsLargeVehicle()) {
    return false;
  }
  if (!trajectory_info.IsLaneKeep()) {
    return false;
  }
  if (reasoning_object.IsBehindEgoLeadingBumper() ||
      reasoning_object.is_leading_vehicle()) {
    return false;
  }
  const pb::ObjectProximityInfo& proximity_info =
      reasoning_object.object_proximity_info();
  // We currently only consider buses pulling out from the bus bulb on ego's
  // right side.
  constexpr double kLatDistThresholdToBeCautiousInMeter = 3.5;
  if (proximity_info.signed_lateral_gap() > 0.0 ||
      proximity_info.abs_lateral_gap() > kLatDistThresholdToBeCautiousInMeter) {
    return false;
  }
  if (reasoning_object.object_occupancy_param_ptr() == nullptr) {
    return false;
  }
  // When the agent is stationary, the motion heading (used for computing the
  // object_occupancy_param) may be opposite to the box heading, thus we need
  // correct it.
  const bool is_opposite_motion_box_heading =
      reasoning_object.IsStationary() &&
      (reasoning_object.planner_object().heading() *
       reasoning_object.planner_object().box_heading()) < 0.0;
  const double agent_box_relative_heading_to_lane =
      is_opposite_motion_box_heading
          ? math::NormalizeMinusPiToPi(
                reasoning_object.object_occupancy_param_ptr()
                    ->relative_heading_rad +
                M_PI)
          : reasoning_object.object_occupancy_param_ptr()->relative_heading_rad;
  constexpr double kSignedLatSpeedThresholdInMps = 0.15;
  constexpr double kRelHeadingToLaneThresholdInRad = math::Degree2Radian(3.0);
  const bool is_agent_lateral_heading_towards_ego_lane =
      (reasoning_object.object_occupancy_param_ptr()->signed_lateral_speed_mps >
           kSignedLatSpeedThresholdInMps ||
       agent_box_relative_heading_to_lane > kRelHeadingToLaneThresholdInRad);
  return is_agent_lateral_heading_towards_ego_lane;
}

bool IsVehicleEncroachingEgoLane(const ReasoningObject& reasoning_object,
                                 const TrajectoryInfo& trajectory_info) {
  if (!reasoning_object.planner_object().is_vehicle()) {
    return false;
  }
  // Do not consider the agent if it does not have info on how much it
  // encroaches the ego lane.
  if (reasoning_object.object_occupancy_param_ptr() == nullptr) {
    return false;
  }
  // Only consider the case under lane keep maneuver and ego is going straight.
  if (!trajectory_info.IsLaneKeep() || !trajectory_info.IsGoingStraight()) {
    return false;
  }
  if (reasoning_object.is_leading_vehicle()) {
    return false;
  }
  if (reasoning_object.IsLargeVehicle()) {
    if (reasoning_object.IsBehindEgoLeadingBumper()) {
      return false;
    }
    // Do not consider agents that are laterally too far away.
    if (reasoning_object.object_proximity_info().abs_lateral_gap() >
        kMaxLatGapForConsideringVehiclesCrossingLaneInMeter) {
      return false;
    }
    const bool is_agent_lateral_moving_away_from_ego_lane =
        reasoning_object.object_occupancy_param_ptr()
                ->signed_lateral_speed_mps *
            reasoning_object.object_proximity_info().signed_lateral_gap() >
        0.0;
    if (is_agent_lateral_moving_away_from_ego_lane) {
      return false;
    }
    const bool should_consider_as_crossing_lane =
        reasoning_object.object_occupancy_param_ptr()->max_lane_encroachment_m >
            kAggressiveEncroachmentDistanceThresholdInMeter ||
        (reasoning_object.object_occupancy_param_ptr()
                 ->max_lane_encroachment_m >= 0.0 &&
         std::abs(reasoning_object.object_occupancy_param_ptr()
                      ->signed_lateral_speed_mps) >
             kMildEncroachingSpeedThresholdInMps) ||
        (reasoning_object.object_occupancy_param_ptr()
                 ->max_lane_encroachment_m >
             -kAggressiveEncroachmentDistanceThresholdInMeter &&
         std::abs(reasoning_object.object_occupancy_param_ptr()
                      ->signed_lateral_speed_mps) >
             kAggressiveEncroachingSpeedThresholdInMps);
    if (!should_consider_as_crossing_lane) {
      return false;
    }
    // Do not consider agents encroaching virtual lane marking.
    const double object_center_ra_arclength =
        0.5 * (reasoning_object.object_proximity_info()
                   .projected_ra_arc_length()
                   .start() +
               reasoning_object.object_proximity_info()
                   .projected_ra_arc_length()
                   .end());
    const std::optional<traffic_rules::LaneInfoInLaneSequence> lane_info_opt =
        GetLaneInfoInLaneSequenceAtRaArcLength(
            trajectory_info.traffic_rules().lane_sequence_lanes,
            object_center_ra_arclength);
    if (lane_info_opt.has_value() && lane_info_opt.value().lane != nullptr) {
      const pnc_map::Lane* lane = lane_info_opt.value().lane;
      if (reasoning_object.object_proximity_info().signed_lateral_gap() < 0.0) {
        const bool is_ego_lane_of_virtual_right_marking = std::any_of(
            lane->right_marking()->attributes().begin(),
            lane->right_marking()->attributes().end(),
            [](const hdmap::LaneMarking::Attribute& attribute) {
              return attribute.type() == hdmap::LaneMarking::Attribute::VIRTUAL;
            });
        if (is_ego_lane_of_virtual_right_marking) return false;
      }
      if (reasoning_object.object_proximity_info().signed_lateral_gap() > 0.0) {
        const bool is_ego_lane_of_virtual_left_marking = std::any_of(
            lane->left_marking()->attributes().begin(),
            lane->left_marking()->attributes().end(),
            [](const hdmap::LaneMarking::Attribute& attribute) {
              return attribute.type() == hdmap::LaneMarking::Attribute::VIRTUAL;
            });
        if (is_ego_lane_of_virtual_left_marking) return false;
      }
    }
    return true;
  }
  return false;
}

bool ShouldBeCautiousAroundEncroachingEgoLaneVehicle(
    const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info) {
  if (reasoning_object.IsStationary() ||
      reasoning_object.is_slow_moving_vehicle()) {
    return false;
  }
  if (reasoning_object.is_bus_near_bus_bulb()) {
    return false;
  }
  // As defensive driving strategy will increase yield buffer, we do not want to
  // apply it to cases where ego is of lower precedence to avoid yield paradox.
  if (agent_trajectory_info.EgoHasLowerRoadPrecedence()) {
    return false;
  }
  return reasoning_object.is_encroaching_lane_vehicle();
}

bool IsPassRequiredLateralGapResettableForStuckAgent(
    const WorldContext& world_context,
    const ReasoningObject& reasoning_object) {
  if (world_context.ego_speed() >
          kMaxAllowedEgoSpeedToReducePassRLGToUnstuckInMps ||
      reasoning_object.plan_init_pose_overlap_slice_ptr() == nullptr) {
    return false;
  }
  if (!reasoning_object.is_ego_path_overtaking()) {
    return false;
  }
  const pb::OverlapSlice* overlap_slice_ptr =
      reasoning_object.plan_init_pose_overlap_slice_ptr();
  // Currently ego and agent do not have overlap
  if (overlap_slice_ptr->ego_moving_distance_padded().start() > 0 ||
      overlap_slice_ptr->ego_moving_distance_padded().end() < 0) {
    return false;
  }
  const double relative_box_heading =
      reasoning_object.object_proximity_info().relative_box_heading();
  if (overlap_slice_ptr->signed_lateral_gap() >
      kMinAllowedRLGToUnstuckForFarawayAgentInMeters) {  // agent on the left
    if ((relative_box_heading < 0.0 && relative_box_heading > -M_PI_2) ||
        (relative_box_heading > M_PI_2 && relative_box_heading < M_PI)) {
      // ego is approaching the agent, ego can not unstuck
      return false;
    } else if (world_context.robot_state().current_state_snapshot().steering() >
               0.0) {
      // ego can not unstuck if its steering is to the left
      return false;
    }
  } else if (overlap_slice_ptr->signed_lateral_gap() <
             -kMinAllowedRLGToUnstuckForFarawayAgentInMeters) {  // agent on the
    // right
    if ((relative_box_heading > 0.0 && relative_box_heading < M_PI_2) ||
        (relative_box_heading < -M_PI_2 && relative_box_heading > -M_PI)) {
      // ego is approaching the agent, ego can not unstuck
      return false;
    } else if (world_context.robot_state().current_state_snapshot().steering() <
               0.0) {
      // ego can not unstuck if its steering is to the right
      return false;
    }
  }
  if (std::abs(overlap_slice_ptr->signed_lateral_gap()) >
      kMinAllowedRLGToUnstuckForFarawayAgentInMeters) {
    rt_event::PostRtEvent<rt_event::planner::UnstuckForCloseAgentByReducePLG>();
    return true;
  }
  return false;
}

bool ShouldReducePassRequiredLateralGapUnderCriticalToUnstuck(
    const ReasoningObject& reasoning_object,
    const TrajectoryInfo& trajectory_info, const double ego_speed) {
  if (ego_speed > kMaxAllowedEgoSpeedToReducePassRLGToUnstuckInMps) {
    return false;
  }
  if (!reasoning_object.IsStationary() ||
      reasoning_object.plan_init_pose_overlap_slice_ptr() == nullptr) {
    return false;
  }
  const double curvature =
      trajectory_info.GetEgoCurvature(GetPaddedOverlapStart(
          *reasoning_object.plan_init_pose_overlap_slice_ptr()));
  if (std::abs(reasoning_object.plan_init_pose_overlap_slice_ptr()
                   ->signed_lateral_gap()) >=
      reasoning_object.GetPrincipledCriticalRequiredLateralGap(curvature)) {
    return false;
  }
  // Returns true if the agent is in a narrow passage.
  if (reasoning_object.is_longitudinally_in_narrow_passage()) {
    return true;
  }
  // For large vehicles we have addition checks in case they actively approach
  // to ego and stop in non-narrow passages. If the agent is fully ahead, we
  // rely on the path to nudge around and unstuck. Also, if the agent is behind
  // the front axle, we already reduced the pass RLG in L&M, thus no need to do
  // it again.
  if (!trajectory_info.IsLaneKeep()) {
    return false;
  }
  if (!reasoning_object.IsLargeVehicle() || reasoning_object.IsFullyAhead() ||
      reasoning_object.IsBehindEgoFrontAxle()) {
    return false;
  }
  const std::optional<ARTObjectInfo>& art_object_info =
      reasoning_object.art_object_info();
  if (art_object_info.has_value() &&
      art_object_info.value().near_stationary_duration <
          kMinStationaryDurationToReducePassRLGToUnstuckInSeconds) {
    return false;
  }
  const bool is_agent_aligned_with_lane_dir =
      reasoning_object.object_occupancy_param_ptr() != nullptr &&
      math::BiDirectionAngleDiff(
          reasoning_object.object_occupancy_param_ptr()->relative_heading_rad,
          0.0) < kLaneAlignmentAngleThresholdToUnstuckInRad;

  return is_agent_aligned_with_lane_dir;
}

bool IsAgentExtraCautiousOnTrajectory(
    const ReasoningObject& reasoning_object,
    const TrajectoryInfo& trajectory_info,
    const AgentTrajectoryInfo& agent_trajectory_info) {
  if (trajectory_info.is_lane_change_in_progress()) {
    return false;
  }
  // Only consider agents to be extra cautious if ego has absolute higher road
  // precedence.
  if (!agent_trajectory_info.EgoHasHigherRoadPrecedence()) {
    return false;
  }

  // Only consider agents to be extra cautious if ego is moving straight.
  // This is to avoid unnecessary extra cautious agent during multi-lane left
  // turn or multi-lane uturn.
  if (!trajectory_info.IsGoingStraight()) {
    return false;
  }

  const speed::pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();
  if (object_proximity_info.out_of_path_range()) {
    return false;
  }

  // Currently, we only assume extra cautious for agents on ego's right side.
  // Agents that are exiting from an exit zone on ego's left side need to be
  // handled separately.
  if (object_proximity_info.signed_lateral_gap() > 0.0) {
    return false;
  }

  // If the agent is on ego's right side, we would like to exclude the
  // scenario that the agent is moving away from ego, in which case relative
  // heading is less than 0. In addition, except for bus bulb, we do not
  // expect ego to be parallel to agent.
  if (object_proximity_info.relative_motion_heading() <
      kMinHeadingDiffForRoadExitVehiclesInRad) {
    return false;
  }

  // Consider agents to be extra cautious if they are slow moving vehicles
  // that are laterally close to ego, or not slow moving vehicles but are
  // still far enough laterally. Skip cyclists, pedestrians, and fast moving
  // vehicles for now.
  if (!reasoning_object.is_slow_moving_vehicle()) return false;

  // For agents that have very low yield intention, do not consider as extra
  // cautious.
  if (agent_trajectory_info.IsYieldIntentionUsableAndLowerThan(
          reasoning_util::GetYieldIntentionThreshold(reasoning_object,
                                                     agent_trajectory_info))) {
    return false;
  }

  // Consider agent to be extra cautious when merging into ego lane.
  if (agent_trajectory_info.precedence_source() ==
      planner::pb::PrecedenceSource::MergingSection) {
    return true;
  }

  // Consider agent to be extra cautious when leaving exit zones.
  if (agent_trajectory_info.precedence_source() ==
      planner::pb::PrecedenceSource::ExitZone) {
    return true;
  }

  // Consider agent to be extra cautious if ego is protected by traffic light
  // and moving straight.
  if (agent_trajectory_info.precedence_source() ==
      planner::pb::PrecedenceSource::KnownTrafficLight) {
    return true;
  }

  return false;
}

double GetYieldIntentionThreshold(
    const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info) {
  if (!agent_trajectory_info.consider_yield_intention()) {
    return 0;
  }

  return (reasoning_object.is_vehicle() ||
          reasoning_object.is_pedestrian_or_cyclist())
             ? kNoAgentReactionYieldIntentionThreshold
             : kMinYieldIntentionLikelihoodForSlowDownReaction;
}

double ComputeUnstuckPassRequiredLatGapForTrafficCone(
    const ReasoningObject& reasoning_object) {
  DCHECK(reasoning_object.planner_object().object_type() ==
         voy::perception::ObjectType::TRAFFIC_CONE);
  const double control_error_for_straight_path =
      planner::control_error::LateralControlError(/*curvature=*/0.0);
  if (std::abs(reasoning_object.plan_init_pose_overlap_slice_ptr()
                   ->signed_lateral_gap()) > control_error_for_straight_path) {
    return control_error_for_straight_path;
  }
  // If the traffic cone is shorter than the ego side mirror, directly remove
  // the side mirror extrusion length from the buffer.
  if (reasoning_object.planner_object().height() +
          kThresholdToConsiderShorterThanEgoSideMirrorInMeter <
      kEgoSideMirrorHeightClearanceOffGroundInMeter) {
    return std::max(math::constants::kEpsilon,
                    control_error_for_straight_path -
                        kEgoSideMirrorExtrusionFromBodyInMeter);
  }
  // Otherwise, remove the buffer based on the traffic cone geometry by assuming
  // its cross-section is a triangle.
  const double lat_gap_reduction =
      reasoning_util::ComputeAllowedLateralIntrusionIntoConeAtHeight(
          reasoning_object.planner_object(),
          kEgoSideMirrorHeightClearanceOffGroundInMeter -
              kThresholdToConsiderShorterThanEgoSideMirrorInMeter);
  return std::max(
      math::constants::kEpsilon,
      control_error_for_straight_path -
          std::min(kEgoSideMirrorExtrusionFromBodyInMeter, lat_gap_reduction));
}

bool ShouldConsiderYieldIntention(
    const ReasoningObject& reasoning_object,
    const TrajectoryInfo& /* ego_trajectory_info */,
    const AgentTrajectoryInfo& agent_trajectory_info) {
  // UNKNOWN type means this scenario is out of prediction's scope.
  if (agent_trajectory_info.yield_intention().yield_intention_type() ==
      prediction::pb::YieldIntentionType::UNKNOWN_YIELD_INTENTION) {
    return false;
  }
  // For vehicle, we only use the yield intention in our interested scenarios.
  // TODO(zhihanghe): Add interested scenarios for VRUs when needed.
  if (reasoning_object.is_vehicle()) {
    const planner::pb::PrecedenceSceneType::Enum precedence_scene_type =
        agent_trajectory_info.precedence_scene_type();
    return precedence_scene_type ==
               planner::pb::PrecedenceSceneType::InJunction ||
           precedence_scene_type ==
               planner::pb::PrecedenceSceneType::InExitZone ||
           precedence_scene_type ==
               planner::pb::PrecedenceSceneType::InMergeSection;
  }
  return true;
}

bool IsAgentExitingRoundaboutFromInnerLanesOfEgo(
    const ReasoningObject& reasoning_object,
    const TrajectoryInfo& trajectory_info,
    const AgentTrajectoryInfo& agent_trajectory_info) {
  const pnc_map::Lane* ego_current_lane =
      *trajectory_info.lane_sequence_iterator().current_lane();
  // Ego must be going through a roundabout, i.e., current road and its
  // predecessor as well as successor are all in a roundabout.
  if (!ego_current_lane->section()->road()->is_through_roundabout()) {
    return false;
  }
  // Only agent on ego's left is considered.
  if (reasoning_object.object_proximity_info().signed_lateral_gap() < 0.0) {
    return false;
  }
  // Get the first exiting road ahead.
  const pnc_map::Road* exiting_road_ahead =
      GetFirstExitingRoadAheadLaneInRoundabout(ego_current_lane);
  DCHECK(exiting_road_ahead != nullptr);

  // If ego is exiting roundabout along with the agent, we do not consider the
  // agent being exiting from inner lanes. To exclude this case, we need to
  // check if ego is taking the first exit ahead to leave the roundabout without
  // passing other exits (in case the ego's lane sequence circles back in
  // roundabout, though it is rare to happen).
  const pnc_map::Road* ego_exiting_road = nullptr;
  bool has_passed_nearest_exit = false;
  for (auto iter = trajectory_info.lane_sequence_iterator().current_lane();
       iter != trajectory_info.lane_sequence_iterator().end(); ++iter) {
    if (!has_passed_nearest_exit) {
      const pnc_map::Road* fork_brother_road_exiting_roundabout =
          GetForkBrotherRoadExitingRoundabout(*iter);
      has_passed_nearest_exit =
          fork_brother_road_exiting_roundabout != nullptr &&
          fork_brother_road_exiting_roundabout->id() ==
              exiting_road_ahead->id();
    }
    if ((*iter)->section()->road()->is_exiting_roundabout()) {
      ego_exiting_road = (*iter)->section()->road();
      break;
    }
  }
  if (ego_exiting_road != nullptr &&
      ego_exiting_road->id() == exiting_road_ahead->id() &&
      !has_passed_nearest_exit) {
    return false;
  }

  // The agent is explicitly taking the first exiting road to leave roundabout
  // when its BP end pose is in the exiting and on the right side of ego path.
  math::ProximityQueryInfo proximity = trajectory_info.path().QueryProximity(
      agent_trajectory_info.predicted_trajectory()
          .predicted_poses()
          .back()
          .center_2d(),
      math::pb::UseExtensionFlag::kForbid);

  math::ProximityQueryInfo exit_road_proximity =
      exiting_road_ahead->reference_line().GetProximity(
          agent_trajectory_info.predicted_trajectory()
              .predicted_poses()
              .back()
              .center_2d(),
          math::pb::UseExtensionFlag::kForbid);

  const bool is_agent_exiting_roundabout =
      ((math::geometry::Within(agent_trajectory_info.predicted_trajectory()
                                   .predicted_poses()
                                   .back()
                                   .center_2d(),
                               exiting_road_ahead->border()) &&
        proximity.side == math::pb::kRight)) ||
      exit_road_proximity.relative_position == math::RelativePosition::kAfter;

  return is_agent_exiting_roundabout;
}

bool IsAgentAheadDrivingInSameLaneSectionAlongEgoLaneSequence(
    const ReasoningObject& reasoning_object,
    const TrajectoryInfo& trajectory_info,
    const bool include_agent_in_junction) {
  if (!include_agent_in_junction && reasoning_object.is_in_junction()) {
    return false;
  }
  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();
  if (object_proximity_info.out_of_path_range()) {
    return false;
  }
  if (reasoning_object.IsBehindEgoLeadingBumper()) {
    return false;
  }
  if (reasoning_object.planner_object().associated_lanes().empty()) {
    return false;
  }
  const std::optional<traffic_rules::LaneInfoInLaneSequence>&
      closest_ego_lane_info_to_agent =
          traffic_rules::GetLaneInfoInLaneSequenceAtRaArcLength(
              trajectory_info.traffic_rules().lane_sequence_lanes,
              0.5 * (object_proximity_info.projected_ra_arc_length().start() +
                     object_proximity_info.projected_ra_arc_length().end()));
  if (!closest_ego_lane_info_to_agent.has_value()) {
    return false;
  }
  const std::unordered_map<int64_t, double>&
      center_arclength_on_associated_lane_ids =
          reasoning_object.planner_object()
              .center_arclength_on_associated_lane_ids();

  return std::any_of(
      reasoning_object.planner_object().associated_lanes().begin(),
      reasoning_object.planner_object().associated_lanes().end(),
      [&closest_ego_lane_info_to_agent, &reasoning_object,
       &center_arclength_on_associated_lane_ids](
          const pnc_map::Lane* agent_lane) {
        if (agent_lane == nullptr) {
          DCHECK(false);
          return false;
        }
        const auto it =
            center_arclength_on_associated_lane_ids.find(agent_lane->id());
        if (it == center_arclength_on_associated_lane_ids.end()) {
          return false;
        }
        return closest_ego_lane_info_to_agent->lane->section()->id() ==
                   agent_lane->section()->id() &&
               std::abs(math::AngleDiff(
                   agent_lane->center_line().GetInterpTheta(it->second),
                   reasoning_object.IsStationary()
                       ? reasoning_object.planner_object().box_heading()
                       : reasoning_object.planner_object().heading())) < M_PI_4;
      });
}

bool IsOverlapSliceFullyOnTheEncroachedSideOfEgo(
    const TrajectoryInfo& trajectory_info,
    const math::geometry::OrientedBox2d& bounding_box,
    const bool is_left_encroach) {
  // Use the lane boundary curve at encroachment side as the reference.
  const auto& lane_boundary_pair = trajectory_info.lane_boundary_pair();
  const math::geometry::PolylineCurve2d& lane_boundary_curve =
      is_left_encroach ? lane_boundary_pair.left : lane_boundary_pair.right;
  const auto ego_center_proximity =
      is_left_encroach ? lane_boundary_pair.ego_left_boundary_proximity
                             .center_point_proximity
                       : lane_boundary_pair.ego_right_boundary_proximity
                             .center_point_proximity;

  int closest_seg_idx = ego_center_proximity.closest_segment_index;
  const auto dist_to_boundary =
      [&lane_boundary_curve, &closest_seg_idx](
          const math::geometry::Point2d& corner_point) -> double {
    return lane_boundary_curve
        .GetProximityWithHint(corner_point, math::pb::UseExtensionFlag::kForbid,
                              /*csi_hint=*/closest_seg_idx)
        .signed_dist;
  };
  // Compute the distance of bounding box to the lane boundary. Note we only
  // consider the corner points on the other side of the encroachment to save
  // computation.
  double closest_signed_dist = is_left_encroach
                                   ? std::numeric_limits<double>::infinity()
                                   : -std::numeric_limits<double>::infinity();
  for (const math::geometry::Point2d& corner_point :
       bounding_box.CornerPoints()) {
    closest_signed_dist =
        is_left_encroach
            ? std::min(closest_signed_dist, dist_to_boundary(corner_point))
            : std::max(closest_signed_dist, dist_to_boundary(corner_point));
  }

  const double ego_center_signed_dist = ego_center_proximity.signed_dist;

  return is_left_encroach ? closest_signed_dist > ego_center_signed_dist
                          : closest_signed_dist < ego_center_signed_dist;
}

double ComputeYieldDistanceToAgentRearBumper(
    const TrajectoryInfo& trajectory_info,
    const AgentTrajectoryInfo& agent_trajectory_info,
    const int overlap_region_ix, const double ra_to_leading_bumper) {
  const pb::OverlapRegion& overlap_region =
      agent_trajectory_info.overlap_region(overlap_region_ix);
  DCHECK(!overlap_region.overlap_slices().empty());
  // Using the first overlap slice with the smallest lateral gap to estimate the
  // yielding distance to the agent rear bumper for all overlap slices.
  const speed::pb::OverlapSlice& first_overlap_slice_with_smallest_lat_gap =
      overlap_region.contain_strict_overlap()
          ? overlap_region.overlap_slices(
                reasoning_util::GetNearestStrictOverlapSliceIdx(overlap_region))
          : *std::min_element(overlap_region.overlap_slices().begin(),
                              overlap_region.overlap_slices().end(),
                              [](const speed::pb::OverlapSlice& left,
                                 const speed::pb::OverlapSlice& right) {
                                return std::abs(left.signed_lateral_gap()) <
                                       std::abs(right.signed_lateral_gap());
                              });
  const pb::ObjectCornerPointsProximityInfo overlap_slice_proximity =
      reasoning_util::ComputeBoxCornerPointsProximityInfo(
          trajectory_info.path(),
          agent_trajectory_info.predicted_trajectory()
              .predicted_poses()[first_overlap_slice_with_smallest_lat_gap
                                     .timestamp_index()]
              .oriented_box());
  // Find the arc length of the longitudinally closest corner point to the ego.
  double closest_corner_arc_length =
      overlap_slice_proximity.front_left_proximity().arc_length();
  math::UpdateMin(overlap_slice_proximity.front_right_proximity().arc_length(),
                  closest_corner_arc_length);
  math::UpdateMin(overlap_slice_proximity.rear_left_proximity().arc_length(),
                  closest_corner_arc_length);
  math::UpdateMin(overlap_slice_proximity.rear_right_proximity().arc_length(),
                  closest_corner_arc_length);
  // Return the arc length difference between the padded overlap slice start
  // point and the closest corner point as the yield extra distance.
  const double padded_overlar_start_ra_arclength =
      GetPaddedOverlapStart(first_overlap_slice_with_smallest_lat_gap) +
      ra_to_leading_bumper;
  const double yield_extra_distance_to_rear_bumper = std::max(
      0.0, padded_overlar_start_ra_arclength +
               trajectory_info.plan_init_ra_arc_length_on_extended_path() -
               closest_corner_arc_length);
  return yield_extra_distance_to_rear_bumper;
}

speed::OverlapRegionEncroachmentInfo ComputeOverlapRegionEncroachmentInfo(
    const TrajectoryInfo& trajectory_info,
    const planner::RobotState& /* robot_state */,
    const planner::PredictedTrajectoryWrapper& predicted_trajectory,
    const pb::OverlapRegion& overlap_region) {
  const std::vector<CriticalEncroachmentRangeInfo>&
      critical_encroachment_range_infos =
          trajectory_info.critical_encroachment_range_infos();

  for (const CriticalEncroachmentRangeInfo& critical_encroachment_range :
       critical_encroachment_range_infos) {
    const math::pb::Side encroach_side = critical_encroachment_range.side;
    const std::optional<math::Range1d> encroach_range =
        std::make_optional<math::Range1d>(
            critical_encroachment_range.ra_arclength_range);

    // Find the first slice that overlaps with the encroachment region on the
    // same side.
    const auto first_overlapped_slice_iter = std::find_if(
        overlap_region.overlap_slices().begin(),
        overlap_region.overlap_slices().end(),
        [range = critical_encroachment_range.ra_arclength_range](
            const pb::OverlapSlice& overlap_slice) {
          return std::min(range.end_pos,
                          GetPaddedOverlapEnd(overlap_slice,
                                              /*use_original=*/true)) >
                 std::max(range.start_pos,
                          GetPaddedOverlapStart(overlap_slice,
                                                /*use_original=*/true));
        });

    // The overlap region does not overlap with this encroachment region.
    if (first_overlapped_slice_iter == overlap_region.overlap_slices().end()) {
      continue;
    }

    // For oncoming overlaps, we only consider it will be encroached when ego
    // drives to the lane on the opposite direction (aka cross lane nudge to the
    // opposite lane).
    const bool is_oncoming_overlap_region =
        std::all_of(overlap_region.overlap_slices().begin(),
                    overlap_region.overlap_slices().end(),
                    [](const pb::OverlapSlice& overlap_slice) {
                      return overlap_slice.motion_type() ==
                             pb::OverlapMotionType::OVERLAP_MOTION_ONCOMING;
                    });
    if (is_oncoming_overlap_region) {
      if (critical_encroachment_range.encroach_oncoming_lane) {
        return {encroach_side, encroach_range, true,
                reasoning_util::ComputeIndexRangeInWrongWayEncroachmentRegions(
                    overlap_region, encroach_range.value())};
      }

      return {math::pb::Side::kOn, std::nullopt,
              critical_encroachment_range.encroach_oncoming_lane,
              /*overlap_segments_in_wrong_way_encroachment_region=*/{}};
    }

    DCHECK_LT(first_overlapped_slice_iter->timestamp_index(),
              predicted_trajectory.predicted_poses().size());

    // Take the lane boundary as the reference, if the first overlapped slice is
    // not fully on the encroached side of the ego center, we do not consider it
    // as being encroached. As shown in the figure below, the trajectory of
    // overlap C1 will not be considered as encroached while the trajectories of
    // overlap C2 and C3 will be considered as encroached cuz the slice is fully
    // on the right side of the ego center.
    //     |l|            ^ |r|    *^    | |
    //     |l|            ^ |r|  * __    | | A: ego car
    //     |l|            ^ |r|*  |  |   | | B: encroached agent
    //     |l|            ^ |r|   |C3|   | | C: first overlapped slice
    //     |l|  ^         ^*|r|   |__|   | | *: ego path
    //     |l|  ^         * |r|     ^    | | ^: agent's trajectory
    //     |l|  ^        *^ |r|     ^    | | l/r: left/right boundary
    //     |l|  ^       * ^ |r|     ^    | |
    //     |l|  ^____  * __ |r|     ^    | |
    //     |l|  | A  |  |  ||r|     ^    | |
    //     |l|  |    |  |C2||r|     ^    | |
    //     |l|  |    |  |__||r|     ^    | |
    //     |l|  |____|    ^ |r|     ^    | |
    //     |l|  ^         ^ |r|    __    | |
    //     |l|  ^__      ^  |r|   |  |   | |
    //     |l|  |  |    ^   |r|   |B2|   | |
    //     |l|  |C1|   ^    |r|   |__|   | |
    //     |l|  |__|  ^     |r|          | |
    //     |l|    ^__^      |r|          | |
    //     |l|    |  |      |r|          | |
    //     |l|    |B1|      |r|          | |
    //     |l|    |__|      |r|          | |
    // TODO(jinhao): Refine the scoping of identification with better approach.
    if (!IsOverlapSliceFullyOnTheEncroachedSideOfEgo(
            trajectory_info,
            /*bounding_box=*/
            predicted_trajectory
                .predicted_poses()[first_overlapped_slice_iter
                                       ->timestamp_index()]
                .oriented_box(),
            /*is_left_encroach=*/encroach_side == math::pb::Side::kLeft)) {
      continue;
    }
    return {encroach_side, encroach_range,
            critical_encroachment_range.encroach_oncoming_lane,
            /*overlap_segments_in_wrong_way_encroachment_region=*/{}};
  }

  return {math::pb::Side::kOn, std::nullopt, false,
          /*overlap_segments_in_wrong_way_encroachment_region=*/{}};
}

std::vector<route_association::MapElementAndPoseInfo>
GetAdHocAssociatedRouteForPredictedTrajectory(
    const planner::PredictedTrajectoryWrapper& predicted_trajectory,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const planner::pb::AgentMapElementOccupancySeeds&
        agent_map_element_occupancy_seed,
    const bool allow_using_for_cyclist) {
  // Ad hoc calling is limited to predicted trajectories whose associated route
  // field is not being globally computed in world model yet.
  DCHECK(!predicted_trajectory.associated_route_opt().has_value());
  // Lane association is expected to work for vehicles and cyclist only.
  DCHECK(  // NOLINT, FP when checking macro DCHECK
      predicted_trajectory.tracked_object().object_type() ==
          voy::perception::ObjectType::VEHICLE ||
      predicted_trajectory.tracked_object().object_type() ==
          voy::perception::ObjectType::CYCLIST);

  if (!allow_using_for_cyclist &&
      predicted_trajectory.tracked_object().object_type() ==
          voy::perception::ObjectType::CYCLIST) {
    return {};
  }

  route_association::PredictedTrajectoryRouteAssociator association_result{
      predicted_trajectory, joint_pnc_map_service,
      agent_map_element_occupancy_seed,
      /*debug_proto_ptr=*/nullptr};
  return association_result.most_likely_route();
}

std::vector<route_association::MapElementAndPoseInfo>
GetAdHocAssociatedRouteForPredictedTrajectory(
    const ReasoningObject& reasoning_object,
    const planner::PredictedTrajectoryWrapper& predicted_trajectory,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    bool allow_using_for_cyclist) {
  if (!reasoning_object.is_vehicle_or_cyclist()) return {};

  planner::pb::AgentMapElementOccupancySeeds mock_seed;
  if (reasoning_object.agent_occupied_map_element_infos_in_seed_ptr() !=
      nullptr) {
    (*mock_seed.mutable_agent_element_occupancy_map())[reasoning_object.id()] =
        *reasoning_object.agent_occupied_map_element_infos_in_seed_ptr();
  }

  return GetAdHocAssociatedRouteForPredictedTrajectory(
      predicted_trajectory, joint_pnc_map_service, mock_seed,
      allow_using_for_cyclist);
}

bool IsLaneChangeLeadingAgent(const TrajectoryInfo& trajectory_info,
                              const ReasoningObject& reasoning_object) {
  // If the flag is not enabled, no leading agent should be identified.
  return trajectory_info.lane_change_status().leading_object_id != 0 &&
         reasoning_object.id() ==
             trajectory_info.lane_change_status().leading_object_id;
}

bool IsLaneChangeLeadingAgentInProgress(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object) {
  return IsLaneChangeLeadingAgent(trajectory_info, reasoning_object) &&
         trajectory_info.is_lane_change_in_progress();
}

bool IsLaneChangeTailingAgent(const TrajectoryInfo& trajectory_info,
                              const ReasoningObject& reasoning_object) {
  return trajectory_info.lane_change_status().tailing_object_id != 0 &&
         reasoning_object.id() ==
             trajectory_info.lane_change_status().tailing_object_id;
}

bool IsLaneChangeTailingAgentInProgress(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object) {
  return IsLaneChangeTailingAgent(trajectory_info, reasoning_object) &&
         trajectory_info.is_lane_change_in_progress();
}

bool IsTargetRegionAgentDuringLaneChange(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object) {
  if (!trajectory_info.is_lane_change_in_progress()) {
    return false;
  }
  const CrossLaneObjectInfoList* target_region_object_infos =
      trajectory_info.cross_lane_info_manager()
          .GetTargetRegionObjectInfoListPtr();
  return (target_region_object_infos != nullptr) &&
         (target_region_object_infos->Find(reasoning_object.id()) != nullptr);
}

bool IsSourceRegionAgentDuringLaneChangeAbort(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object) {
  if (!trajectory_info.is_lane_change_in_abort()) {
    return false;
  }
  const CrossLaneObjectInfoList* source_region_object_infos =
      trajectory_info.cross_lane_info_manager()
          .GetSourceRegionObjectInfoListPtr();
  return (source_region_object_infos != nullptr) &&
         (source_region_object_infos->Find(reasoning_object.id()) != nullptr);
}

bool IsSourceRegionAgentDuringLaneChange(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object) {
  if (!trajectory_info.is_lane_change_in_progress()) {
    return false;
  }
  const CrossLaneObjectInfoList* source_region_object_infos =
      trajectory_info.cross_lane_info_manager()
          .GetSourceRegionObjectInfoListPtr();
  return (source_region_object_infos != nullptr) &&
         (source_region_object_infos->Find(reasoning_object.id()) != nullptr);
}

bool IsInterestedAgentForCautiousDrivingDuringLaneChange(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object) {
  if (!trajectory_info.is_lane_change_in_progress()) {
    return false;
  }

  const std::vector<CrossLaneRegionType> interested_region_list = {
      CrossLaneRegionType::TARGET, CrossLaneRegionType::SOURCE,
      CrossLaneRegionType::SOURCE_NEIGHBOR};
  return std::any_of(
      interested_region_list.begin(), interested_region_list.end(),
      [&trajectory_info,
       &reasoning_object](const CrossLaneRegionType region_type) {
        const CrossLaneObjectInfoList* region_object_infos =
            trajectory_info.cross_lane_info_manager()
                .GetObjectInfoListPtrByRegionType(region_type);
        return (region_object_infos != nullptr &&
                region_object_infos->Find(reasoning_object.id()) != nullptr);
      });
}

bool IsLaneChangeInterestedAgentDuringLaneChangeOrLaneChangeAbort(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object) {
  return IsTargetRegionAgentDuringLaneChange(trajectory_info,
                                             reasoning_object) ||
         IsSourceRegionAgentDuringLaneChangeAbort(trajectory_info,
                                                  reasoning_object);
}

bool IsTargetRegionAgentDuringLaneChangeCrawl(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object) {
  return trajectory_info.is_lane_change_crawl() &&
         IsTargetRegionAgentDuringLaneChange(trajectory_info, reasoning_object);
}

bool IsLaneChangeAgent(const TrajectoryInfo& trajectory_info,
                       const ReasoningObject& reasoning_object) {
  return IsLaneChangeLeadingAgent(trajectory_info, reasoning_object) ||
         IsLaneChangeTailingAgent(trajectory_info, reasoning_object);
}

bool IsLaneChangeAgentInProgress(const TrajectoryInfo& trajectory_info,
                                 const ReasoningObject& reasoning_object) {
  return IsLaneChangeLeadingAgentInProgress(trajectory_info,
                                            reasoning_object) ||
         IsLaneChangeTailingAgentInProgress(trajectory_info, reasoning_object);
}

bool ShouldEncourageHighDiscomfortProgressDuringLaneChange(
    const TrajectoryInfo& trajectory_info, const double ego_speed) {
  if (!trajectory_info.is_lane_change_in_progress()) {
    return false;
  }

  if (trajectory_info.GetCrossLaneInfo().should_creep) {
    return true;
  }

  constexpr double kMaxSpeedConsiderDiscomfortForProgressDuringCrawlLcInMps =
      6.0;
  constexpr double kMaxSpeedConsiderDiscomfortForProgressDuringNormalLcInMps =
      1.0;
  const double max_speed_for_discomfort_for_progress =
      trajectory_info.is_lane_change_crawl()
          ? kMaxSpeedConsiderDiscomfortForProgressDuringCrawlLcInMps
          : kMaxSpeedConsiderDiscomfortForProgressDuringNormalLcInMps;
  return ego_speed < max_speed_for_discomfort_for_progress;
}

std::optional<double> ComputeGlobalDiscomfortForProgressForLaneChange(
    const TrajectoryInfo& trajectory_info, const double ego_speed) {
  if (!trajectory_info.is_lane_change_in_progress()) {
    return std::nullopt;
  }

  // Compute discomfort for progress for crawl lane change and normal lane
  // change at low speed.
  constexpr double kHighGlobalDiscomfortForProgressDuringLaneChange = 0.5;
  if (ShouldEncourageHighDiscomfortProgressDuringLaneChange(trajectory_info,
                                                            ego_speed)) {
    return kHighGlobalDiscomfortForProgressDuringLaneChange;
  }

  if (trajectory_info.is_lane_change_crawl()) {
    return std::nullopt;
  }

  // Compute discomfort for progress during normal lane change to avoid decision
  // flipping.
  constexpr double kMinGlobalDiscomfortForProgressDuringNormalLaneChange = 0.25;
  const std::optional<double> speed_discomfort_for_progress =
      trajectory_info.lane_change_status().speed_discomfort_for_progress;
  const double speed_discomfort_for_progress_value =
      speed_discomfort_for_progress.has_value()
          ? std::max(speed_discomfort_for_progress.value(),
                     kMinGlobalDiscomfortForProgressDuringNormalLaneChange)
          : kMinGlobalDiscomfortForProgressDuringNormalLaneChange;

  return speed_discomfort_for_progress_value;
}

bool ShouldReduceYieldHeadwayDuringLaneChange(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const pb::OverlapRegion& overlap_region) {
  DCHECK(trajectory_info.is_lane_change_in_progress());

  if (!reasoning_util::IsTargetRegionAgentDuringLaneChange(trajectory_info,
                                                           reasoning_object) &&
      !reasoning_util::IsSourceRegionAgentDuringLaneChange(trajectory_info,
                                                           reasoning_object)) {
    return false;
  }

  if (!reasoning_object.IsFullyAhead()) {
    return false;
  }

  // For agents with only padded overlap, reduce yield_headway to avoid
  // unnecessary extra yield.
  if (!overlap_region.contain_strict_overlap()) {
    return true;
  }

  constexpr double kYieldHeadwayAccelerationInMpss = -2.0;
  constexpr double kInteractionCheckTimeInSec = 3.0;
  constexpr double kYieldSafeDistanceInMeter = 3.0;
  constexpr double kMinEgoSpeedToIncreaseSafeYieldBufferInMps = 6.0;
  constexpr double kMaxEgoSpeedToIncreaseSafeYieldBufferInMps = 10.0;

  DCHECK(!overlap_region.overlap_slices().empty());
  const double agent_interaction_start_relative_time_in_sec =
      overlap_region.start_padded_relative_time_in_sec();
  if (agent_interaction_start_relative_time_in_sec >
      kInteractionCheckTimeInSec - math::constants::kEpsilon) {
    return true;
  }

  // Compute if the agent and ego will collide in the near future assuming that
  // the agent and ego brake with a constant acceleration from the timestamp
  // when there is padded overlap.
  const double remaining_interaction_duration =
      kInteractionCheckTimeInSec - agent_interaction_start_relative_time_in_sec;
  const double interaction_start_agent_rb_arc_length =
      GetPaddedOverlapStart(overlap_region);

  auto compute_brake_distance = [](const double speed, const double max_decel,
                                   const double duration) -> double {
    if (speed < 0.0 || duration < 0.0 || max_decel > 0.0) {
      return 0.0;
    }

    const double brake_duration = std::min(-speed / max_decel, duration);
    return speed * brake_duration +
           0.5 * max_decel * brake_duration * brake_duration;
  };

  const double agent_brake_distance = compute_brake_distance(
      overlap_region.overlap_slices(0).signed_longitudinal_speed(),
      kYieldHeadwayAccelerationInMpss, remaining_interaction_duration);
  const double ego_brake_distance = compute_brake_distance(
      world_context.ego_speed(), kYieldHeadwayAccelerationInMpss,
      kInteractionCheckTimeInSec);
  const double yield_safe_distance = math::GetLinearInterpolatedY(
      kMinEgoSpeedToIncreaseSafeYieldBufferInMps,
      kMaxEgoSpeedToIncreaseSafeYieldBufferInMps, kYieldSafeDistanceInMeter,
      kYieldSafeDistanceInMeter * 2.0, world_context.ego_speed());

  return interaction_start_agent_rb_arc_length + agent_brake_distance >
         world_context.ra_to_leading_bumper() + ego_brake_distance +
             yield_safe_distance;
}

bool ShouldEnableDiscomfortVaryingSoftYieldExtraDistanceForAgentAheadDuringLc(
    const WorldContext& world_context, const ReasoningObject& reasoning_object,
    std::ostringstream& debug_oss) {
  constexpr double kMinTtcForSoftYieldAgentAheadInSec = 3.0;
  constexpr double kMinSafeDistanceForSoftYieldAgentAheadInMeter = 0.75;
  constexpr double kMaxSafeDistanceForSoftYieldAgentAheadInMeter = 2.0;
  constexpr double kSoftYieldEgoSpeedThresholdInMps = 2.78;  // 10kph

  // When the agent is fully ahead, we take the minimal distance needed
  // (according to the speed diff and the ttc) into account to compute if we
  // should use discomfort varying yield extra distance to be able to get closer
  // to the agent.
  DCHECK(reasoning_object.IsFullyAhead());
  const double ego_agent_speed_diff =
      world_context.ego_speed() -
      reasoning_object.object_proximity_info().signed_longitudinal_speed();
  if (ego_agent_speed_diff > 0.0 &&
      (reasoning_object.object_proximity_info()
               .projected_ra_arc_length()
               .start() -
           world_context.ra_to_leading_bumper() <
       abs(ego_agent_speed_diff) * kMinTtcForSoftYieldAgentAheadInSec +
           math::GetLinearInterpolatedY(
               0.0, kSoftYieldEgoSpeedThresholdInMps,
               kMinSafeDistanceForSoftYieldAgentAheadInMeter,
               kMaxSafeDistanceForSoftYieldAgentAheadInMeter,
               world_context.ego_speed()))) {
    // Keep larger distance when the agent is slower than and close to ego.
    debug_oss << "\nagent ahead; slower and close;\n";
    return false;
  }

  // Keep small distance to keep up with the agent when it is faster or far
  // away.
  debug_oss << "\nagent ahead; faster or far away;\n";
  return true;
}

bool ShouldEnableDiscomfortVaryingSoftYieldExtraDistanceForAgentNonAheadDuringLc(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info,
    std::ostringstream& debug_oss) {
  DCHECK(!reasoning_object.IsFullyAhead());

  // Compute the lateral relative states between ego and the agent to check if
  // there is enough space for ego to move a bit forward longitudinally when the
  // agent is not fully ahead. This could make ego come close to the target lane
  // slowly and is able to start up faster when there is a good lane change
  // chance.
  const CrossLaneInfoManager& cross_lane_info_manager =
      trajectory_info.cross_lane_info_manager();
  const CrossLaneInfo& cross_lane_info =
      cross_lane_info_manager.GetCrossLaneInfo();
  const bool is_left_lane_change =
      cross_lane_info.lane_change_instance.direction() ==
      planner::pb::LaneChangeMode::LEFT_LANE_CHANGE;

  const CrossLaneObjectInfoList* target_region_object_info_list =
      cross_lane_info_manager.GetTargetRegionObjectInfoListPtr();
  const CrossLaneObjectInfo* object_info =
      target_region_object_info_list->Find(reasoning_object.id());
  if (object_info == nullptr) {
    return false;
  }

  // Compute lateral relative distance between the agent and ego.
  const double agent_lateral_distance =
      object_info->agent_closest_corner_signed_lat_dist_to_cross_lane_curve;
  const double ego_lateral_distance =
      cross_lane_info.cross_lane_meta
          .ego_corner_signed_lat_dist_to_cross_lane_curve();

  const double agent_ego_lateral_distance =
      (agent_lateral_distance - ego_lateral_distance) *
      (is_left_lane_change ? 1.0 : -1.0);

  constexpr double kSoftYieldLateralDistanceMinBufferInMeter = 0.3;
  constexpr double kSoftYieldLateralDistanceMaxBufferInMeter = 0.8;
  constexpr double kSoftYieldLongitudinalDistanceBufferInMeter = 0.25;
  constexpr double kSoftYieldMaxLateralInteractionTimeInSec = 3.0;
  constexpr double kSoftYieldMaxLongitudinalInteractionTimeInSec = 5.0;
  constexpr double kSoftYieldEgoSpeedThresholdInMps = 2.78;  // 10kph

  // Compute the lateral safe distance w.r.t. ego's speed.
  const double safe_lateral_buffer_wrt_speed = math::GetLinearInterpolatedY(
      0.0, kSoftYieldEgoSpeedThresholdInMps,
      kSoftYieldLateralDistanceMinBufferInMeter,
      kSoftYieldLateralDistanceMaxBufferInMeter, world_context.ego_speed());

  debug_oss << DUMP_TO_STREAM(agent_ego_lateral_distance,
                              safe_lateral_buffer_wrt_speed,
                              agent_lateral_distance, ego_lateral_distance);

  // Disable discomfort varying soft yield when the agent and ego are close
  // laterally.
  if (agent_ego_lateral_distance < safe_lateral_buffer_wrt_speed) {
    debug_oss << "\nagent laterally close;\n";
    return false;
  }

  // Compute lateral relative speed between the agent and ego.
  const double agent_lateral_speed =
      object_info->agent_signed_lat_speed_wrt_cross_lane_curve;
  const double ego_lateral_speed =
      cross_lane_info.cross_lane_meta
          .ego_signed_lat_speed_wrt_cross_lane_curve();

  // Compute lateral time to collision between the agent and ego.
  const double lateral_ttc =
      (agent_ego_lateral_distance - safe_lateral_buffer_wrt_speed) /
      std::max(abs(agent_lateral_speed - ego_lateral_speed),
               math::constants::kEpsilon);
  // We limit the max lateral interaction time here considering the large
  // uncertainty of the far future.
  const double lateral_interaction_time =
      std::min(lateral_ttc, kSoftYieldMaxLateralInteractionTimeInSec);

  debug_oss << "\n"
            << DUMP_TO_STREAM(lateral_interaction_time, lateral_ttc,
                              agent_lateral_speed, ego_lateral_speed);

  // Check the lateral relative distance at the interaction timestamp.
  const planner::pb::TrajectoryPose& agent_pose_in_future =
      agent_trajectory_info.predicted_trajectory().ComputePoseAtTimestamp(
          world_context.robot_state().plan_init_state_snapshot().timestamp() +
          math::Sec2Ms(lateral_interaction_time));
  const math::geometry::Point2d agent_center_point_in_future(
      agent_pose_in_future.x_pos(), agent_pose_in_future.y_pos());
  const double agent_lateral_distance_in_future = ComputeSignedDistanceToCurve(
      /*oriented_box=*/{agent_center_point_in_future, reasoning_object.length(),
                        reasoning_object.width(),
                        agent_pose_in_future.heading()},
      /*cross_lane_curve=*/
      cross_lane_info.lane_change_instance.GetCrossLaneCurve(),
      /*update_max=*/!is_left_lane_change);
  const double agent_ego_lateral_distance_in_future =
      (agent_lateral_distance_in_future - ego_lateral_distance) *
          (is_left_lane_change ? 1.0 : -1.0) -
      abs(ego_lateral_speed) * lateral_interaction_time;

  // Disable discomfort varying yield extra distance for soft constraints when
  // the lateral distance is small at the interaction timestamp.
  const double ego_speed_in_future =
      world_context.ego_speed() +
      std::max(world_context.ego_acceleration(), 0.0) *
          lateral_interaction_time;
  const double safe_lateral_buffer_wrt_future_speed =
      math::GetLinearInterpolatedY(0.0, kSoftYieldEgoSpeedThresholdInMps,
                                   kSoftYieldLateralDistanceMinBufferInMeter,
                                   kSoftYieldLateralDistanceMaxBufferInMeter,
                                   ego_speed_in_future);

  debug_oss << "\nFuture info:\n"
            << DUMP_TO_STREAM(agent_ego_lateral_distance_in_future,
                              safe_lateral_buffer_wrt_future_speed,
                              agent_lateral_distance_in_future,
                              ego_speed_in_future, world_context.ego_speed(),
                              world_context.ego_acceleration());

  if (agent_ego_lateral_distance_in_future <
      safe_lateral_buffer_wrt_future_speed) {
    debug_oss << "\nagent laterally close in the future;\n";
    return false;
  }

  // When the lateral distance is large at the interaction timestamp, check the
  // relative longitudinal states in the near future. And we could extend the
  // longitudinal interaction time here if the lateral ttc is large, which means
  // we may move a bit forward when there is no lateral risk to prepare for the
  // following lane change.
  const double extra_lon_interaction_time =
      std::min(lateral_ttc, kSoftYieldMaxLongitudinalInteractionTimeInSec) -
      lateral_interaction_time;
  DCHECK_GE(extra_lon_interaction_time, 0.0);
  const double agent_rb_arc_length_in_future =
      trajectory_info.path()
          .QueryProximity(agent_center_point_in_future,
                          math::pb::UseExtensionFlag::kForbid)
          .arc_length -
      reasoning_object.length() * 0.5 +
      agent_pose_in_future.speed() * extra_lon_interaction_time;

  const double ego_fb_arc_length_in_future =
      trajectory_info.plan_init_ra_arc_length_on_extended_path() +
      world_context.ra_to_leading_bumper() +
      world_context.ego_speed() *
          (lateral_interaction_time + extra_lon_interaction_time);

  // Enable discomfort varying yield extra distance for soft constraints when
  // the agent has already overtaken ego at the longitudinal interaction
  // timestamp. Otherwise, disable it since the agent and ego may collide so
  // that we need a larger buffer.
  const double agent_rb_ego_fb_distance_in_future =
      agent_rb_arc_length_in_future - ego_fb_arc_length_in_future -
      kSoftYieldLongitudinalDistanceBufferInMeter;

  debug_oss << "\n"
            << DUMP_TO_STREAM(agent_rb_ego_fb_distance_in_future,
                              extra_lon_interaction_time,
                              agent_rb_arc_length_in_future,
                              ego_fb_arc_length_in_future,
                              kSoftYieldLongitudinalDistanceBufferInMeter);

  if (agent_rb_ego_fb_distance_in_future > 0.0) {
    debug_oss << "\nagent fully ahead in the future;\n";
    return true;
  }

  debug_oss << "\nagent not fully ahead in the future;\n";
  return false;
}

bool ShouldEnableDiscomfortVaryingSoftYieldExtraDistanceDuringLaneChange(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info,
    const pb::OverlapRegion& overlap_region, std::ostringstream& debug_oss) {
  if (!IsTargetRegionAgentDuringLaneChange(trajectory_info, reasoning_object)) {
    return false;
  }

  if (trajectory_info.GetCrossLaneInfo().should_creep &&
      !reasoning_object.IsBehindEgoLeadingBumper()) {
    return true;
  }

  // Disable discomfort varying soft yield for overlap regions with padded
  // overlap only.
  if (!overlap_region.contain_strict_overlap()) {
    return false;
  }

  debug_oss << "Soft yield extra distance debug info during lc:\n";

  if (reasoning_object.IsFullyAhead()) {
    return ShouldEnableDiscomfortVaryingSoftYieldExtraDistanceForAgentAheadDuringLc(
        world_context, reasoning_object, debug_oss);
  }

  return ShouldEnableDiscomfortVaryingSoftYieldExtraDistanceForAgentNonAheadDuringLc(
      world_context, trajectory_info, reasoning_object, agent_trajectory_info,
      debug_oss);
}

double ComputeExtraYieldExtraDistanceForLaneChange(
    const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info, const double ego_speed) {
  if (reasoning_object.IsBehindEgoFrontAxle()) {
    return 0.0;
  }

  // Compute the extra yield extra distance during lane change according to ego
  // speed and the agent's acceleration.
  constexpr double kMaxExtraYieldExtraDistanceInMeter = 1.5;
  constexpr double kMinEgoSpeedToIncreaseYieldBufferInMps = 6.0;
  constexpr double kMaxEgoSpeedToIncreaseYieldBufferInMps = 8.0;
  constexpr double kMinAgentAccelToIncreaseYieldBufferInMpss = -1.0;
  constexpr double kMaxAgentAccelToIncreaseYieldBufferInMpss = -0.3;

  // The faster ego is, the larger yield buffer we need.
  const double extra_yield_extra_dist_by_ego_speed =
      math::GetLinearInterpolatedY(kMinEgoSpeedToIncreaseYieldBufferInMps,
                                   kMaxEgoSpeedToIncreaseYieldBufferInMps, 0.0,
                                   kMaxExtraYieldExtraDistanceInMeter,
                                   ego_speed);

  // The harder the agent brakes, the larger yield buffer we need.
  const double object_min_accel =
      std::min(reasoning_object.object_proximity_info()
                   .signed_longitudinal_acceleration(),
               agent_trajectory_info.predicted_average_acceleration());
  return math::GetLinearInterpolatedY(kMaxAgentAccelToIncreaseYieldBufferInMpss,
                                      kMinAgentAccelToIncreaseYieldBufferInMpss,
                                      0.0, extra_yield_extra_dist_by_ego_speed,
                                      object_min_accel);
}

bool ShouldAddYieldExtraTimeDuringLaneChange(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object) {
  if (reasoning_object.id() !=
      world_context.previous_dominant_risk_object_id()) {
    return false;
  }

  // Return false when there are agents behind and close to ego to avoid hard
  // brake due to the increased yield extra time.
  const CrossLaneInfoManager& cross_lane_info_manager =
      trajectory_info.cross_lane_info_manager();
  const CrossLaneInfo& cross_lane_info =
      cross_lane_info_manager.GetCrossLaneInfo();
  const CrossLaneRegionInfo* target_region_info_ptr = GetRegionInfoPtrByType(
      CrossLaneRegionType::TARGET, cross_lane_info.region_infos);
  if (target_region_info_ptr == nullptr) {
    return true;
  }

  constexpr double kRearClearDistanceDuringLaneChangeInMeter = 18.0;
  const std::vector<int64_t>& behind_and_close_object_ids =
      GetObjectIdsInArclengthRange(
          target_region_info_ptr->object_info_list,
          target_region_info_ptr->ego_ra_arc_length -
              kRearClearDistanceDuringLaneChangeInMeter,
          target_region_info_ptr->ego_ra_arc_length,
          /*consider_leaving_object=*/true);
  if (!behind_and_close_object_ids.empty()) {
    return false;
  }

  // TODO(elliot): Don't filter out these agents in lane change env analyzer and
  // then remove the following logics.
  // Get back crossing region agents. Don't add yield extra time for the
  // dominant object when there is other agent behind doing cross lane behavior.
  auto is_object_behind_and_close =
      [](const int64_t object_id,
         const std::unordered_map<std::string, planner::pb::XRegionMotionInfo>&
             xregion_motion_info,
         const planner::pb::XRegionMotionType& motion_type,
         const CrossLaneObjectInfoList* to_region_object_info_list) -> bool {
    const auto& iter = xregion_motion_info.find(
        planner::pb::XRegionMotionType_Name(motion_type));
    if (iter == xregion_motion_info.end() ||
        to_region_object_info_list == nullptr) {
      return false;
    }

    const CrossLaneObjectInfo* object_info =
        to_region_object_info_list->Find(object_id);
    if (object_info == nullptr) {
      return false;
    }

    return (object_info->object_ego_dist < 0.0 &&
            object_info->object_ego_dist >
                -kRearClearDistanceDuringLaneChangeInMeter);
  };

  const std::unordered_map<
      int64_t, std::unordered_map<std::string, planner::pb::XRegionMotionInfo>>&
      object_xregion_motion_info_map =
          cross_lane_info.object_id_to_xregion_motion_info;
  return !std::any_of(
      object_xregion_motion_info_map.begin(),
      object_xregion_motion_info_map.end(),
      [&cross_lane_info_manager, is_object_behind_and_close](const auto& iter) {
        return is_object_behind_and_close(
                   iter.first, iter.second,
                   planner::pb::XRegionMotionType::TARGET_TO_SOURCE,
                   cross_lane_info_manager
                       .GetSourceRegionObjectInfoListPtr()) ||
               is_object_behind_and_close(
                   iter.first, iter.second,
                   planner::pb::XRegionMotionType::TARGET_TO_TARGET_NEIGHBOR,
                   cross_lane_info_manager
                       .GetTargetNeighborRegionObjectInfoListPtr());
      });
}

bool ShouldBeCautiousWithLateralMovingAgentDuringLaneChange(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object) {
  if (!trajectory_info.is_lane_change_in_progress() &&
      !trajectory_info.is_lane_change_in_abort()) {
    return false;
  }

  if (!reasoning_object.is_vehicle_or_cyclist()) {
    return false;
  }

  if (reasoning_object.IsStationary()) {
    return false;
  }

  if (reasoning_object.is_laterally_moving_away()) {
    return false;
  }

  if (reasoning_object.IsBehindEgoFrontAxle()) {
    return false;
  }

  // The lower bound of lateral speed to consider the laterally moving agent
  // risk during lane change or abort.
  constexpr double kMinLateralSpeedToConsiderAgentRiskDuringLcInMps = 0.1;

  // Consider the agent's lateral speed from proximity info during lane change
  // abort.
  if (trajectory_info.is_lane_change_in_abort()) {
    return std::abs(reasoning_object.object_proximity_info()
                        .signed_lateral_speed()) >
           kMinLateralSpeedToConsiderAgentRiskDuringLcInMps;
  }

  // Consider the agent's signed lateral speed w.r.t the cross lane curve during
  // lane change in progress to filter out agents moving in the opposite
  // direction.
  const CrossLaneObjectInfoList* target_region_object_info_list =
      trajectory_info.cross_lane_info_manager()
          .GetTargetRegionObjectInfoListPtr();
  const CrossLaneObjectInfoList* source_region_object_info_list =
      trajectory_info.cross_lane_info_manager()
          .GetSourceRegionObjectInfoListPtr();
  if (target_region_object_info_list == nullptr ||
      source_region_object_info_list == nullptr) {
    return false;
  }

  // Get valid object info.
  const CrossLaneObjectInfo* object_info =
      target_region_object_info_list->Find(reasoning_object.id());
  object_info =
      object_info == nullptr
          ? source_region_object_info_list->Find(reasoning_object.id())
          : object_info;
  if (object_info == nullptr) {
    return false;
  }

  const double signed_lateral_speed =
      object_info->agent_signed_lat_speed_wrt_cross_lane_curve;
  if (std::abs(signed_lateral_speed) <
      kMinLateralSpeedToConsiderAgentRiskDuringLcInMps) {
    return false;
  }

  const double signed_lateral_dist =
      object_info->agent_closest_corner_signed_lat_dist_to_cross_lane_curve;

  // Be cautious when the agent is moving laterally towards the lane change
  // direction, or the agent is laterally moving in the lane change direction
  // from the source lane side.
  const double lane_change_direction_unit =
      trajectory_info.GetCrossLaneInfo().lane_change_instance.direction() ==
              planner::pb::LaneChangeMode::LEFT_LANE_CHANGE
          ? 1.0
          : -1.0;
  return (signed_lateral_speed * lane_change_direction_unit < 0.0) ||
         (signed_lateral_dist * lane_change_direction_unit < 0.0);
}

bool IsXLaneNudgeInterestedTrajectory(
    const TrajectoryInfo& trajectory_info,
    const bool is_overlap_region_in_encroachment_regions) {
  const planner::pb::IntentionResult::IntentionHomotopy homotopy =
      trajectory_info.ego_intention().homotopy();
  return (homotopy == planner::pb::IntentionResult::XLANE_PASS_FROM_LEFT ||
          homotopy == planner::pb::IntentionResult::XLANE_PASS_FROM_RIGHT) &&
         is_overlap_region_in_encroachment_regions;
}

bool ShouldEnableLateralGapVaryingOverlap(
    const ReasoningObject& reasoning_object,
    const pb::ReasonerId reasoner_in_charge,
    const TrajectoryInfo& trajectory_info,
    const pb::OverlapRegion& overlap_region,
    const OverlapRegionInfo& overlap_region_info,
    pb::GenerativeConstraintType::Enum generative_constraint_type,
    const bool yield_always_possible) {
  constexpr double kMaxOverlapStartXForLatGapVarOverlapInMeter = 3.0;
  constexpr int64_t kMaxOverlapStartTimeForLatGapVarOverlapInMSec = 3000;
  const double start_position =
      overlap_region.contain_strict_overlap()
          ? overlap_region.ego_moving_distance_strict().start()
          : overlap_region.ego_moving_distance_padded().start();
  const bool is_close_ahead = math::IsInRange(
      start_position, 0.0, kMaxOverlapStartXForLatGapVarOverlapInMeter);
  const bool is_in_near_future =
      overlap_region.start_padded_relative_time_in_msec() <
      kMaxOverlapStartTimeForLatGapVarOverlapInMSec;
  if (yield_always_possible && !(is_close_ahead && is_in_near_future)) {
    // Not enable for the overlap that is not temporally and spatially close to
    // ego.
    return false;
  }
  // Only enable the feature for the nominal constraint.
  // TODO(jinhao): Refactor or remove the logic here if we can migrate all the
  // nominal constraints to the strict and soft constraints.
  if (reasoner_in_charge == pb::ReasonerId::LEAD_AND_MERGE) {
    if (generative_constraint_type ==
            pb::GenerativeConstraintType::NOMINAL_WITH_SOFT ||
        generative_constraint_type == pb::GenerativeConstraintType::NOMINAL) {
      if (trajectory_info.is_lane_change_in_progress() ||
          trajectory_info.is_lane_change_in_abort() ||
          trajectory_info.is_lane_change_preparation()) {
        return true;
      }

      if (reasoning_object.IsStationary()) {
        return true;
      }

      if (overlap_region_info.is_cut_in || overlap_region_info.is_cut_out) {
        return true;
      }

      // The leading agent's start_x and end_x in PaddedOverlap and
      // discomfort-varying overlap should be almost the same, so disable
      // discomfort-varying overlap computation for leading agents in L&M.
      return !reasoning_object.is_leading_agent();
    }

    return false;
  }
  if (generative_constraint_type != pb::GenerativeConstraintType::NOMINAL) {
    return false;
  }
  if (!FLAGS_planning_enable_soft_strict_constraint_during_lane_change &&
      trajectory_info.is_lane_change_in_progress()) {
    return true;
  }
  return false;
}

bool IsNotFacingEgoVehicle(const ReasoningObject& reasoning_object) {
  // Following current contract, only use box heading to do this check for
  // vehicles.
  if (!reasoning_object.is_vehicle()) return false;

  const double signed_lateral_gap =
      reasoning_object.object_proximity_info().signed_lateral_gap();
  const double heading_diff =
      reasoning_object.object_proximity_info().relative_box_heading();

  return (signed_lateral_gap < 0. && heading_diff <= -M_PI / 4. &&
          heading_diff >= -3. * M_PI / 4.) ||
         (signed_lateral_gap > 0. && heading_diff >= M_PI / 4. &&
          heading_diff <= 3. * M_PI / 4.);
}

double ComputeAgentToLaneBoundaryMinDistance(
    const LaneBoundaryPair& lane_boundary_pair,
    const math::geometry::PolygonWithCache2d& agent_contour,
    math::pb::Side side) {
  DCHECK(!agent_contour.points().empty());
  DCHECK_NE(side, math::pb::Side::kOn);
  // we consider an object encroaches a lane boundary when it is on the left
  // of right lane boundary or on the right of left lane boundary
  auto is_encroached = [](const math::pb::Side proximity_side,
                          const math::pb::Side boundary_side) {
    return proximity_side == math::pb::kOn ||
           (proximity_side == math::pb::kLeft &&
            boundary_side == math::pb::Side::kRight) ||
           (proximity_side == math::pb::kRight &&
            boundary_side == math::pb::Side::kLeft);
  };
  const math::geometry::PolylineCurve2d& lane_boundary_curve =
      side == math::pb::Side::kLeft ? lane_boundary_pair.left
                                    : lane_boundary_pair.right;
  DCHECK(!lane_boundary_curve.empty());
  const math::ProximityQueryInfo first_proximity =
      lane_boundary_curve.GetProximity(agent_contour.points()[0],
                                       math::pb::UseExtensionFlag::kForbid);
  int csi_hint = first_proximity.closest_segment_index;
  double min_dist = std::numeric_limits<double>::infinity();
  if (first_proximity.relative_position == math::RelativePosition::kWithIn) {
    min_dist = is_encroached(first_proximity.side, side) ? -first_proximity.dist
                                                         : first_proximity.dist;
  }
  for (size_t i = 1; i < agent_contour.size(); i++) {
    const math::ProximityQueryInfo proximity =
        lane_boundary_curve.GetProximityWithHint(
            agent_contour.points()[i], math::pb::UseExtensionFlag::kForbid,
            csi_hint);
    // if the point is out of path's range, then we cannot decide the point is
    // on which side of the lane boundary
    if (proximity.relative_position != math::RelativePosition::kWithIn) {
      continue;
    }
    double cur_dist =
        is_encroached(proximity.side, side) ? -proximity.dist : proximity.dist;
    min_dist = std::min(min_dist, cur_dist);
  }
  return min_dist;
}

pb::ObjectCornerPointsProximityInfo ComputeBoxCornerPointsProximityInfo(
    const adv_geom::Path2dWithJuke& ego_path,
    const math::geometry::OrientedBox2d& box) {
  pb::ObjectCornerPointsProximityInfo proximity;

  const auto proximity_to_ego_path =
      [&ego_path](const math::geometry::Point2d& point,
                  const std::optional<int>& closest_seg_idx =
                      std::nullopt) -> math::pb::ProximityQueryInfo {
    return closest_seg_idx.has_value()
               ? ego_path.polyline_curve()
                     .GetProximityWithFixedHint(
                         point, math::pb::UseExtensionFlag::kForbid,
                         closest_seg_idx.value())
                     .ToProto()
               : ego_path
                     .QueryProximity(point, math::pb::UseExtensionFlag::kForbid)
                     .ToProto();
  };

  *proximity.mutable_front_left_proximity() =
      proximity_to_ego_path(box.FrontLeftPoint());
  const int closest_seg_idx =
      proximity.front_left_proximity().closest_segment_index();
  *proximity.mutable_rear_left_proximity() =
      proximity_to_ego_path(box.RearLeftPoint(), closest_seg_idx);
  *proximity.mutable_rear_right_proximity() =
      proximity_to_ego_path(box.RearRightPoint(), closest_seg_idx);
  *proximity.mutable_front_right_proximity() =
      proximity_to_ego_path(box.FrontRightPoint(), closest_seg_idx);

  return proximity;
}

std::vector<const pnc_map::Lane*> GetOverlapCoveredEgoLanes(
    const TrajectoryInfo& trajectory_info,
    const pb::OverlapRegion& overlap_region) {
  const double overlap_region_start_arclength =
      overlap_region.contain_strict_overlap()
          ? GetStrictOverlapStart(overlap_region)
          : GetPaddedOverlapStart(overlap_region);
  const double overlap_region_end_ra_arclength =
      overlap_region.contain_strict_overlap()
          ? GetStrictOverlapEnd(overlap_region)
          : GetPaddedOverlapEnd(overlap_region);
  DCHECK(overlap_region_start_arclength < overlap_region_end_ra_arclength);
  const std::vector<traffic_rules::LaneInfoInLaneSequence>& lanes_info =
      trajectory_info.traffic_rules().lane_sequence_lanes;

  std::vector<const pnc_map::Lane*> covered_ego_lanes;
  for (auto iter = lanes_info.begin(); iter < lanes_info.end(); ++iter) {
    if (overlap_region_end_ra_arclength < iter->start_ra_arclength) {
      break;
    }
    if (overlap_region_start_arclength <= iter->end_ra_arclength &&
        overlap_region_end_ra_arclength >= iter->start_ra_arclength) {
      covered_ego_lanes.push_back(iter->lane);
    }
  }
  return covered_ego_lanes;
}

double GetPrincipledCriticalRequiredLateralGap(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const pb::OverlapRegion& overlap_region) {
  const double overlap_ra_arclength =
      overlap_region.contain_strict_overlap()
          ? GetStrictOverlapStart(overlap_region)
          : GetPaddedOverlapStart(overlap_region);

  return reasoning_object.GetPrincipledCriticalRequiredLateralGap(
      trajectory_info.GetEgoCurvature(overlap_ra_arclength));
}

bool IsValidSqueezeRegionBySide(
    const CriticalRange1d& critical_region,
    const TrajectoryInfo& trajectory_info,
    const pb::ObjectProximityInfo& agent_proximity_info,
    const math::pb::Side squeeze_side) {
  DCHECK(squeeze_side != math::pb::kOn);

  // TODO(chantong): If the agent has strict overlap with ADV's path at current
  // timestamp, the signal will be false negative. And we could not reuse the
  // method in encroachment (check first overlapslice and lane boundary).
  // However, it will not make MPCI issue because in that case, ADV could only
  // yield to the strict constraint.
  // Check if the agent is on the squeeze region side.
  if (!(squeeze_side == math::pb::kLeft &&
        agent_proximity_info.signed_lateral_gap() > 0.0 &&
        critical_region.tag ==
            planner::pb::CriticalRegionType::SqueezeLeftNudge) &&
      !(squeeze_side == math::pb::kRight &&
        agent_proximity_info.signed_lateral_gap() < 0.0 &&
        critical_region.tag ==
            planner::pb::CriticalRegionType::SqueezeRightNudge)) {
    return false;
  }

  // Check if the squeeze region overlaps with the virtual lane. If true, do not
  // consider the squeeze regions because the virtual lane is not a confident
  // reference.
  return std::none_of(
      trajectory_info.traffic_rules().lane_sequence_lanes.begin(),
      trajectory_info.traffic_rules().lane_sequence_lanes.end(),
      [&critical_region,
       squeeze_side](const traffic_rules::LaneInfoInLaneSequence& lane_info) {
        const bool squeeze_region_overlap_with_lane =
            (critical_region.range.start_pos <= lane_info.end_ra_arclength) &&
            (lane_info.start_ra_arclength <= critical_region.range.end_pos);
        return squeeze_region_overlap_with_lane &&
               lane_info.lane->type() ==
                   hdmap::Lane::LaneType::Lane_LaneType_VIRTUAL &&
               HasAnyVirtualLaneMarkingBySide(lane_info.lane, squeeze_side);
      });
}

bool IsRangeInNarrowPassages(
    const std::vector<speed::pb::NarrowPassageInfo>& narrow_passage_infos,
    double range_start, double range_end) {
  if (narrow_passage_infos.empty() || range_start > range_end) {
    return false;
  }
  // Check if the range is in a narrow passage.
  const auto narrow_passage_overlap_check =
      [range_start, range_end](
          const speed::pb::NarrowPassageInfo& narrow_passage_info) -> bool {
    return std::max(range_start,
                    narrow_passage_info.ego_moving_distance_range().start()) <=
           std::min(range_end,
                    narrow_passage_info.ego_moving_distance_range().end());
  };
  return std::any_of(narrow_passage_infos.begin(), narrow_passage_infos.end(),
                     narrow_passage_overlap_check);
}

bool IsOverlapRegionInSqueezeRegions(
    const TrajectoryInfo& trajectory_info,
    const pb::ObjectProximityInfo& agent_proximity_info,
    const pb::OverlapRegion& overlap_region) {
  if (overlap_region.overlap_slices().empty()) {
    return false;
  }

  const math::Range1d overlap_region_range =
      math::Range1d(GetPaddedOverlapStart(overlap_region),
                    GetPaddedOverlapEnd(overlap_region));

  // Check if the overlap has intersections with squeeze ranges.
  const auto squeeze_overlapping_check =
      [&overlap_region_range, &trajectory_info,
       &agent_proximity_info](const CriticalRange1d& critical_region) -> bool {
    return (IsValidSqueezeRegionBySide(critical_region, trajectory_info,
                                       agent_proximity_info,
                                       /*squeeze_side=*/math::pb::kLeft) ||
            IsValidSqueezeRegionBySide(critical_region, trajectory_info,
                                       agent_proximity_info,
                                       /*squeeze_side=*/math::pb::kRight)) &&
           math::AreRangesOverlapping(overlap_region_range,
                                      critical_region.range);
  };

  const std::vector<CriticalRange1d>& critical_regions =
      trajectory_info.critical_regions();
  return std::any_of(critical_regions.begin(), critical_regions.end(),
                     squeeze_overlapping_check);
}

bool IsCutInOverlapRegion(
    const TrajectoryInfo& trajectory_info,
    const planner::PredictedTrajectoryWrapper& predicted_trajectory,
    const PlannerObject& planner_object,
    const pb::OverlapRegion& overlap_region) {
  if (!trajectory_info.IsLaneKeep()) {
    // TODO(waylon): For now, limit the use of this signal to lane keep
    // scenario. We can consider expanding its scope in the future if necessary.
    return false;
  }

  if (!reasoning_util::IsSameDirectionOverlapRegion(overlap_region)) {
    return false;
  }

  if (!planner_object.is_vehicle()) {
    // Return early if the agent is NOT a vehicle.
    return false;
  }

  const ::google::protobuf::RepeatedPtrField<pb::OverlapSlice>& overlap_slices =
      overlap_region.overlap_slices();
  DCHECK(!overlap_slices.empty());
  const auto first_strict_slice = std::find_if(
      overlap_slices.begin(), overlap_slices.end(),
      [](const pb::OverlapSlice& slice) { return HasStrictOverlap(slice); });
  if (first_strict_slice == overlap_slices.end()) {
    // No strict overlap in the overlap region, the trajectory only touches ego
    // path rather then cut in.
    return false;
  }

  if (GetStrictOverlapStart(*first_strict_slice) < 0.0) {
    // Return false if the strict overlap region start behind front bumper.
    return false;
  }

  if (first_strict_slice != overlap_slices.begin()) {
    // Return true if the overlap is not start from a strict slice, which means
    // it first has padded slice and gradually cutting in ego path.
    return true;
  }

  DCHECK_LT(first_strict_slice->timestamp_index(),
            predicted_trajectory.predicted_poses().size());
  const pb::ObjectCornerPointsProximityInfo first_strict_slice_proximity =
      ComputeBoxCornerPointsProximityInfo(
          trajectory_info.path(),
          predicted_trajectory
              .predicted_poses()[first_strict_slice->timestamp_index()]
              .oriented_box());

  if (IsAgentStateLeavingEgoPath(first_strict_slice_proximity,
                                 first_strict_slice->relative_heading())) {
    // Return false if ego is cutting out from ego path.
    return false;
  }

  // Return false if the first strict overlap is considered as a leading agent
  // state.
  return !ShouldConsiderCurrentPoseAsLeadingAgent(first_strict_slice_proximity,
                                                  trajectory_info,
                                                  /*required_lateral_gap=*/0.0);
}

bool IsTrajectoryCrossingEgoPath(const pb::OverlapRegion& overlap_region) {
  const ::google::protobuf::RepeatedPtrField<pb::OverlapSlice>& overlap_slices =
      overlap_region.overlap_slices();
  DCHECK(!overlap_slices.empty());

  const bool has_slice_left_side =
      std::any_of(overlap_slices.begin(), overlap_slices.end(),
                  [](const pb::OverlapSlice& slice) {
                    return slice.signed_lateral_gap() > 0;
                  });
  const bool has_slice_right_side =
      std::any_of(overlap_slices.begin(), overlap_slices.end(),
                  [](const pb::OverlapSlice& slice) {
                    return slice.signed_lateral_gap() < 0;
                  });

  // TODO(waylon): The overlap region might terminate at the path, and we need
  // to consider the nearly vertical overlap region as crossing scenarios.
  return has_slice_left_side && has_slice_right_side;
}

bool IsOverlapRegionInFirstJunction(const TrajectoryInfo& trajectory_info,
                                    const pb::OverlapRegion& overlap_region) {
  return trajectory_info.IsInFirstJunctionAheadEgo(
      GetPaddedOverlap(overlap_region));
}

bool DoesVRUWantToCutBehindEgo(const WorldContext& world_context,
                               const TrajectoryInfo& trajectory_info,
                               const ReasoningObject& reasoning_object,
                               const pb::OverlapRegion& overlap_region) {
  // For curvy Ego path, there could be a sharpe change of strict start Ego
  // moving distance. For example, the strict start ego moving distance of the
  // first strict overlap slice could be 1 meters, while the second one could
  // become -0.9 meters. In order to make our strategy more robust, we should
  // consider more strict overlap slice.
  constexpr int64_t kTimeBufferToConsiderMoreStrictOverlapSliceInMs = 300;
  // This function is only used to VRU.
  if (!reasoning_object.is_pedestrian_or_cyclist()) {
    DCHECK(false) << "This function should only be used to VRU.";
    return false;
  }

  // If agents want to cut behind Ego, it should have seen Ego.
  if (!reasoning_object.HasSeenEgo()) {
    return false;
  }

  if (world_context.EgoAlmostStopped()) {
    return false;
  }

  const bool is_strict_overlap_behind_leading_bumper =
      IsStrictOverlapRegionStartingBehind(
          overlap_region, /*ra_arclength_threshold=*/0.0,
          kTimeBufferToConsiderMoreStrictOverlapSliceInMs);
  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();
  const bool is_object_proximity_start_arclength_behind_leading_bumper =
      object_proximity_info.projected_ra_arc_length().start() <
      world_context.ra_to_leading_bumper();

  const bool is_oncoming_overlap_region =
      std::all_of(overlap_region.overlap_slices().begin(),
                  overlap_region.overlap_slices().end(),
                  [](const pb::OverlapSlice& overlap_slice) {
                    return overlap_slice.motion_type() ==
                           pb::OverlapMotionType::OVERLAP_MOTION_ONCOMING;
                  });
  if (is_oncoming_overlap_region || reasoning_object.IsMovingAgainstEgo()) {
    return is_strict_overlap_behind_leading_bumper ||
           is_object_proximity_start_arclength_behind_leading_bumper;
  }

  // As VRU normally would like to cut behind ego, so if agent's overlap has
  // cross intention we will considered as a cross-overlap case.
  if (reasoning_util::IsCrossingOverlapRegion(overlap_region) ||
      (reasoning_object.is_cyclist() &&
       reasoning_util::IsOverlapRegionWithCrossIntention(overlap_region))) {
    return is_strict_overlap_behind_leading_bumper ||
           reasoning_object.is_fully_behind();
  }

  if (reasoning_util::IsSameDirectionOverlapRegion(overlap_region) &&
      trajectory_info.IsLaneKeep()) {
    return is_strict_overlap_behind_leading_bumper &&
           world_context.ego_speed() >
               object_proximity_info.signed_longitudinal_speed() &&
           reasoning_object.IsBehindEgoLeadingBumper();
  }
  // Identify is_cut_behind as true when the padded overlap is fully behind ego
  // and it does not have the strict overlap with ego
  if ((!speed::HasStrictOverlap(overlap_region)) &&
      reasoning_util::IsPaddedOverlapRegionFullyBehind(
          overlap_region, world_context.ra_to_front_axle() -
                              world_context.ra_to_leading_bumper())) {
    return true;
  }
  return false;
}

bool DoesVehicleWantToCutBehindEgo(const WorldContext& world_context,
                                   const TrajectoryInfo& ego_trajectory_info,
                                   const ReasoningObject& reasoning_object,
                                   const pb::OverlapRegion& overlap_region) {
  DCHECK(reasoning_object.is_vehicle());
  // Exclude non-lane-keep or turning scenarios.
  if (!ego_trajectory_info.IsLaneKeep() ||
      !ego_trajectory_info.IsGoingStraight()) {
    return false;
  }
  // Exclude non-cut-behind like overlap.
  if (!reasoning_util::IsOncomingOverlapRegion(overlap_region) &&
      !reasoning_util::IsCrossingOverlapRegion(overlap_region) &&
      !reasoning_util::IsOncomingCrossingOverlapRegion(overlap_region)) {
    return false;
  }
  // We only consider cut-behind from left for now.
  bool is_agent_on_left_side =
      reasoning_object.object_proximity_info().signed_lateral_gap() > 0.0;
  // Further checking corner points if the proximity signed lateral gap is
  // zero.
  if (reasoning_object.object_proximity_info().signed_lateral_gap() == 0.0) {
    const pb::ObjectCornerPointsProximityInfo& corner_proximity_info =
        reasoning_object.object_proximity_info().corner_points_proximity_info();
    is_agent_on_left_side =
        corner_proximity_info.front_right_proximity().side() ==
            math::pb::kLeft ||
        corner_proximity_info.front_left_proximity().side() ==
            math::pb::kLeft ||
        corner_proximity_info.rear_right_proximity().side() ==
            math::pb::kLeft ||
        corner_proximity_info.rear_left_proximity().side() == math::pb::kLeft;
  }
  if (!is_agent_on_left_side) {
    return false;
  }
  // For agents cut behind, their heading is usually towards ego. Thus we
  // check whether the relative box heading is being greater than M_PI_2 or not
  const bool is_heading_toward_ego =
      std::abs(
          reasoning_object.object_proximity_info().relative_box_heading()) >
      M_PI_2;
  const bool is_agent_behind_front_axle =
      reasoning_object.object_proximity_info()
          .projected_ra_arc_length()
          .start() < world_context.ra_to_front_axle();
  if (!is_heading_toward_ego || !is_agent_behind_front_axle) {
    return false;
  }

  const bool is_strict_overlap_start_behind_front_axle =
      reasoning_util::IsStrictOverlapRegionStartingBehind(
          overlap_region, world_context.ra_to_front_axle() -
                              world_context.ra_to_leading_bumper());
  // Identify is_cut_behind as true when the padded overlap is fully behind ego
  // and it does not have the strict overlap with ego
  if (!speed::HasStrictOverlap(overlap_region) &&
      reasoning_util::IsPaddedOverlapRegionFullyBehind(
          overlap_region, world_context.ra_to_front_axle() -
                              world_context.ra_to_leading_bumper())) {
    return true;
  }
  return is_strict_overlap_start_behind_front_axle;
}

bool IsVRUOnSidewalkBesideEgoLaneSequence(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object) {
  if (!reasoning_object.is_pedestrian_or_cyclist()) {
    return false;
  }

  const std::vector<traffic_rules::ZoneInLaneSequence>&
      zones_along_lane_sequence = trajectory_info.traffic_rules().zones;
  const pb::LongitudinalRange& object_arclength_range =
      reasoning_object.object_proximity_info().projected_ra_arc_length();

  for (const auto& iter : zones_along_lane_sequence) {
    if (iter.zone_ptr->type() == hdmap::Zone::SIDEWALK &&
        (std::max(iter.start_ra_arclength, object_arclength_range.start()) <=
         std::min(iter.end_ra_arclength, object_arclength_range.end()))) {
      if (math::geometry::Within(reasoning_object.planner_object().center_2d(),
                                 iter.zone_ptr->border())) {
        return true;
      }
    }
  }

  return false;
}

double ComputeYieldPositionForOncomingAgent(
    const ReasoningObject& reasoning_object, double ra_to_leading_bumper,
    double ego_speed) {
  const double agent_to_ego_distance = reasoning_object.object_proximity_info()
                                           .projected_ra_arc_length()
                                           .start() -
                                       ra_to_leading_bumper;
  const double agent_speed =
      reasoning_object.object_proximity_info().signed_longitudinal_speed() < 0.0
          ? abs(reasoning_object.object_proximity_info()
                    .signed_longitudinal_speed())
          : 0.0;
  // If ego and agent are both stationary, set the yield stop point at the
  // middle point.
  if (math::NearZero(ego_speed + agent_speed)) {
    return agent_to_ego_distance * 0.5;
  }

  DCHECK(ego_speed + agent_speed != 0.0);
  return agent_to_ego_distance * ego_speed / (ego_speed + agent_speed);
}

bool IsEgoPassiveInteractionScene(
    const TrajectoryInfo& trajectory_info,
    const AgentTrajectoryInfo& agent_trajectory_info,
    const ReasoningObject& reasoning_object, int overlap_region_ix,
    bool is_in_considered_pinch_region) {
  // Filter out the acc scene.
  if (reasoning_object.is_leading_vehicle()) {
    return false;
  }

  const pb::OverlapRegion& overlap_region =
      agent_trajectory_info.overlap_region(overlap_region_ix);
  const OverlapRegionInfo& overlap_region_info =
      agent_trajectory_info.overlap_region_info(overlap_region);

  // Filter out the lane change agents in progress.
  const bool is_lane_change_agent_in_progress =
      reasoning_util::IsLaneChangeAgentInProgress(trajectory_info,
                                                  reasoning_object);
  if (is_lane_change_agent_in_progress) {
    return false;
  }

  // These are the special cases for cyclists. There could not be vehicles going
  // straight on Ego right side when Ego turns right. It's same when turning
  // left.
  if (reasoning_object.is_cyclist() &&
      (overlap_region_info.is_crossing_at_right_hook ||
       overlap_region_info.is_crossing_at_left_hook)) {
    return false;
  }

  // We consider a scene the passive interaction, if agent is behind and not of
  // higher precedence, except for (1) agent is in pinch region; (2) ego is
  // super nudging along with agent. Both exceptions are involving intensive
  // lateral interaction.
  const bool is_xlane_nudge_encroaching_opposite_way =
      !overlap_region_info.covered_ego_lanes.empty() &&
      overlap_region_info.covered_ego_lanes.front()->IsLeftmostDrivableLane() &&
      trajectory_info.ego_intention().homotopy() ==
          planner::pb::IntentionResult::XLANE_PASS_FROM_LEFT;
  if (reasoning_object.IsBehindEgoFrontAxle() &&
      !is_in_considered_pinch_region &&
      (agent_trajectory_info.EgoHasHigherRoadPrecedence() ||
       (agent_trajectory_info.EgoHasEqualRoadPrecedence() &&
        !is_xlane_nudge_encroaching_opposite_way))) {
    return true;
  }

  // Filter out the merge scene.
  // For merge lanes, Ego lane intersects with other lanes, so Ego should be
  // careful about agents behind Ego. When Ego lane is only a fork lane, Ego
  // lane does not intersects with other lane. The vehicles whose overlap
  // regions are on Ego fork lane avoid traffic rule and are aggressive. Thus,
  // Ego should also keep cautious about them.
  if (overlap_region_info.is_in_merge_regions ||
      (!reasoning_object.is_cyclist() &&
       overlap_region_info.is_in_fork_regions)) {
    return false;
  }

  // Filter out the encroachment regions such as pull out / pull over.
  if (overlap_region_info.is_in_encroachment_regions()) {
    return false;
  }

  if (overlap_region_info.is_in_squeeze_regions) {
    return false;
  }

  return true;
}

bool IsSceneVulnerableToYieldParadox(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const pb::OverlapRegion& overlap_region,
    const OverlapRegionInfo& overlap_region_info,
    const std::vector<route_association::MapElementAndPoseInfo>& agent_route) {
  // Do not include the reversing maneuver.
  if (trajectory_info.IsReverseDriving()) {
    return false;
  }
  // Do not include the case where there is no strict overlap or the agent is
  // quite near to the ego path.
  if (!overlap_region.contain_strict_overlap() ||
      reasoning_object.plan_init_pose_overlap_slice_ptr() != nullptr) {
    return false;
  }
  return EgoInUPLAndHasLaneConflictWithAgent(overlap_region_info) ||
         EgoInUTurnAndHasLaneConflictWithAgent(
             overlap_region_info.covered_ego_lanes, agent_route);
}

std::vector<const pnc_map::Lane*> GetCandidateLanesEncroachedByEgo(
    const TrajectoryInfo& trajectory_info,
    const OverlapRegionInfo& overlap_region_info) {
  if (!overlap_region_info.encroachment_segment_opt.has_value()) {
    return {};
  }
  const bool is_left_encroach =
      overlap_region_info.encroachment_side == math::pb::Side::kLeft;
  const std::optional<traffic_rules::LaneInfoInLaneSequence>
      ego_lane_info_containing_encroachment =
          traffic_rules::GetLaneInfoInLaneSequenceAtRaArcLength(
              trajectory_info.traffic_rules().lane_sequence_lanes,
              overlap_region_info.encroachment_segment_opt->start_pos);
  if (!ego_lane_info_containing_encroachment.has_value()) {
    // The start of the encroachment segment does not lies in the range of lane
    // sequence.
    return {};
  }
  // As the lane sequence will be truncated in LC, we handle it separately. As
  // long as the encroachment occurs in target lane sequence and the LC
  // direction is the same as encroachment side, we return it as the
  // encroached lane.
  if (trajectory_info.is_lane_change_in_progress()) {
    const std::vector<const pnc_map::Lane*>& target_lane_sequence =
        trajectory_info.lane_change_status()
            .lane_change_instance.target_lane_sequence();
    const bool is_encroachment_in_target_sequence = std::any_of(
        target_lane_sequence.begin(), target_lane_sequence.end(),
        [&ego_lane_info_containing_encroachment](
            const pnc_map::Lane* target_lane) {
          return target_lane->id() == ego_lane_info_containing_encroachment->id;
        });
    const bool is_encroachment_starting_at_source_lane =
        trajectory_info.lane_change_status()
            .lane_change_instance.source_lane()
            .id() == ego_lane_info_containing_encroachment->id;
    const auto iter = std::find_if(
        trajectory_info.traffic_rules().lane_sequence_lanes.begin(),
        trajectory_info.traffic_rules().lane_sequence_lanes.end(),
        [&trajectory_info](
            const traffic_rules::LaneInfoInLaneSequence& lane_info) {
          return lane_info.id == trajectory_info.lane_change_status()
                                     .lane_change_instance.target_lane()
                                     .id();
        });
    const bool is_encroachment_continuing_to_the_target_lane =
        is_encroachment_starting_at_source_lane &&
        iter != trajectory_info.traffic_rules().lane_sequence_lanes.end() &&
        iter->start_ra_arclength <
            overlap_region_info.encroachment_segment_opt->end_pos;
    if ((is_encroachment_in_target_sequence ||
         is_encroachment_continuing_to_the_target_lane) &&
        (is_left_encroach
             ? trajectory_info.lane_change_status().direction ==
                   planner::pb::LaneChangeMode::LEFT_LANE_CHANGE
             : trajectory_info.lane_change_status().direction ==
                   planner::pb::LaneChangeMode::RIGHT_LANE_CHANGE)) {
      if (is_encroachment_in_target_sequence) {
        return {ego_lane_info_containing_encroachment->lane};
      }
      if (is_encroachment_continuing_to_the_target_lane) {
        return {&trajectory_info.lane_change_status()
                     .lane_change_instance.target_lane()};
      }
    }
  }
  // The following code is only applicable to cases where the whole lane
  // sequence is connected by predecessor-successor lanes (aka. lane keep).
  switch (trajectory_info.ego_current_lane_relative_state()) {
    case planner::pb::EgoCurrentLaneRelativeState::FullyOnTheLane:
      return SearchCandidateEncroachedLanesOnTargetSide(
          ego_lane_info_containing_encroachment->lane,
          is_left_encroach ? math::pb::Side::kLeft : math::pb::Side::kRight,
          trajectory_info.ego_current_lane_relative_state());
    case planner::pb::EgoCurrentLaneRelativeState::FullyOnLeftOutside:
    case planner::pb::EgoCurrentLaneRelativeState::CrossOutFromLeftMarking:
    case planner::pb::EgoCurrentLaneRelativeState::CrossInFromLeftMarking:
      return SearchCandidateEncroachedLanesOnTargetSide(
          ego_lane_info_containing_encroachment->lane,
          is_left_encroach ? math::pb::Side::kLeft : math::pb::Side::kOn,
          trajectory_info.ego_current_lane_relative_state());
    case planner::pb::EgoCurrentLaneRelativeState::FullyOnRightOutside:
    case planner::pb::EgoCurrentLaneRelativeState::CrossOutFromRightMarking:
    case planner::pb::EgoCurrentLaneRelativeState::CrossInFromRightMarking:
      return SearchCandidateEncroachedLanesOnTargetSide(
          ego_lane_info_containing_encroachment->lane,
          is_left_encroach ? math::pb::Side::kOn : math::pb::Side::kRight,
          trajectory_info.ego_current_lane_relative_state());
    default:
      DCHECK(false) << "Unhandled ego current lane relative state";
      return {};
  }
  return {};
}

const pnc_map::Lane* GetEncroachmentSourceLane(
    const TrajectoryInfo& trajectory_info,
    const math::Range1d& encroachment_segment, bool is_left_encroach) {
  const std::optional<traffic_rules::LaneInfoInLaneSequence>
      encroach_source_lane_info =
          traffic_rules::GetLaneInfoInLaneSequenceAtRaArcLength(
              trajectory_info.traffic_rules().lane_sequence_lanes,
              encroachment_segment.start_pos);
  if (!encroach_source_lane_info.has_value()) {
    // The start of the encroachment segment does not lies in the range of lane
    // sequence.
    return nullptr;
  }
  const pnc_map::Lane* encroach_source_lane = encroach_source_lane_info->lane;
  const pnc_map::Lane* left_neighbor =
      encroach_source_lane->adjacent_left_lane();
  const pnc_map::Lane* right_neighbor =
      encroach_source_lane->adjacent_right_lane();
  // The source lane should consider the relative state of ego and the current
  // lane when ego is not fully on the lane sequence. When ego is on the outside
  // of the lane sequence, the encroached_lane_in_lane_sequence is the lane to
  // be encroached and its neighbor is the source lane.
  switch (trajectory_info.ego_current_lane_relative_state()) {
    case planner::pb::EgoCurrentLaneRelativeState::FullyOnTheLane:
      return encroach_source_lane;
    case planner::pb::EgoCurrentLaneRelativeState::FullyOnLeftOutside:
      return left_neighbor;
    case planner::pb::EgoCurrentLaneRelativeState::FullyOnRightOutside:
      return right_neighbor;
    case planner::pb::EgoCurrentLaneRelativeState::CrossOutFromLeftMarking:
    case planner::pb::EgoCurrentLaneRelativeState::CrossInFromLeftMarking:
      return is_left_encroach ? encroach_source_lane : left_neighbor;
    case planner::pb::EgoCurrentLaneRelativeState::CrossOutFromRightMarking:
    case planner::pb::EgoCurrentLaneRelativeState::CrossInFromRightMarking:
      return is_left_encroach ? right_neighbor : encroach_source_lane;
    default:
      DCHECK(false) << "Unhandled ego current lane relative state";
      return nullptr;
  }
}

bool IsCyclistGoingStraightAtHookRegion(
    const TrajectoryInfo& trajectory_info, const PlannerObject& planner_object,
    const planner::PredictedTrajectoryWrapper& predicted_trajectory,
    const pb::OverlapRegion& overlap_region, const bool check_right_hook) {
  constexpr double kMinLateralGapExitingEgoPathInMeter = 0.6;
  if (!planner_object.is_cyclist()) {
    return false;
  }

  // NOTE: Ego is actively shifting laterally during pull over/out, so we
  // consider it as right/left hook case as well.
  const auto is_in_ego_turning_region_checker =
      [&trajectory_info,
       check_right_hook](const pb::OverlapSlice& overlap_slice) {
        const double start_ra_arclength =
            GetPaddedOverlapStart(overlap_slice, /*use_original=*/true);
        const double end_ra_arclength =
            GetPaddedOverlapEnd(overlap_slice, /*use_original=*/true);
        if (check_right_hook) {
          return trajectory_info.IsInRightTurn(start_ra_arclength,
                                               end_ra_arclength) ||
                 trajectory_info.is_pull_over_cross_lane_jump_in_trajectory();
        }
        return trajectory_info.IsInLeftTurn(start_ra_arclength,
                                            end_ra_arclength) ||
               trajectory_info.IsInUTurn(start_ra_arclength,
                                         end_ra_arclength) ||
               trajectory_info.is_pull_out_jump_out();
      };

  // If cyclist goes straight, the overlap region should cross Ego path
  // obviously and has a large lateral gap as exiting Ego path. Thus, padded
  // overlap is used to recognize this characteristic of overlap region.
  const auto begin_iter_in_ego_turning_region = std::find_if(
      overlap_region.overlap_slices().begin(),
      overlap_region.overlap_slices().end(), is_in_ego_turning_region_checker);
  if (begin_iter_in_ego_turning_region ==
      overlap_region.overlap_slices().end()) {
    return false;
  }
  const auto end_iter_in_ego_turning_region = std::find_if(
      overlap_region.overlap_slices().rbegin(),
      overlap_region.overlap_slices().rend(), is_in_ego_turning_region_checker);

  // Ego should pay more attention to and proactively yield to cyclists which
  // are on Ego right side and go straight when Ego turns right. when Ego turns
  // left, cyclists on Ego left side and going straight should be handled in the
  // same way. For the situation that Ego turns right, the overlap region have
  // these characteristics： 1）the start overlap slice is on Ego right
  // side with positive lateral gap; 2) the end overlap slice is on Ego left
  // side with large negative lateral gap. It's same for the situation that Ego
  // turns left,
  if (check_right_hook) {
    return begin_iter_in_ego_turning_region->signed_lateral_gap() <= 0.0 &&
           (end_iter_in_ego_turning_region->signed_lateral_gap() >
                kMinLateralGapExitingEgoPathInMeter ||
            !predicted_trajectory.IsTurningRight());
  }
  // Check left hook.
  return begin_iter_in_ego_turning_region->signed_lateral_gap() >= 0.0 &&
         (end_iter_in_ego_turning_region->signed_lateral_gap() <
              -kMinLateralGapExitingEgoPathInMeter ||
          !predicted_trajectory.IsTurningLeft());
}

bool IsInsideOvertaking(const WorldContext& world_context,
                        const TrajectoryInfo& trajectory_info,
                        const ReasoningObject& reasoning_object,
                        const std::vector<OverlapRegionReference>&
                            predicted_trajectory_overlap_region) {
  const speed::pb::ObjectProximityInfo& agent_proximity_info =
      reasoning_object.object_proximity_info();

  if (!reasoning_object.planner_object().is_vehicle()) {
    return false;
  }

  if (agent_proximity_info.out_of_path_range()) {
    return false;
  }

  // If the agent intends to inside-pass Ego, it should have a high speed.
  if (world_context.ego_speed() > reasoning_object.planner_object().speed()) {
    return false;
  }

  if (!trajectory_info.IsInLeftTurn()) {
    return false;
  }

  // TODO(yanlong): We currently only consider the leftmost lane to avoid FP
  // signals caused by prediction. We may extend the scope later.
  if (!(*trajectory_info.lane_sequence_iterator().current_lane())
           ->IsLeftmostDrivableLane()) {
    return false;
  }

  // In a left-turn scenario, the agent should be positioned on the left side of
  // Ego for inside passing.
  if (agent_proximity_info.signed_lateral_gap() <= 0) {
    return false;
  }

  DCHECK(!predicted_trajectory_overlap_region.empty());
  if (std::none_of(predicted_trajectory_overlap_region.begin(),
                   predicted_trajectory_overlap_region.end(),
                   [](const OverlapRegionReference& overlap_region) {
                     return reasoning_util::IsSameDirectionOverlapRegion(
                         overlap_region);
                   })) {
    return false;
  }

  const traffic_rules::LaneInfoInLaneSequence& current_lane_info =
      trajectory_info.EgoCurrentLaneInfo();
  const pb::OverlapRegion& first_overlap_region =
      predicted_trajectory_overlap_region.front();
  // The minimum distance to check overlap for the inside passing scenario.
  constexpr double kMinDistOffsetToCheckOverlapWithinLaneEndSegmentInMeter =
      10.0;
  const bool is_overlap_within_left_turn_end_segment =
      math::AreRangesOverlapping(
          math::Range1d(GetPaddedOverlapStart(first_overlap_region),
                        GetPaddedOverlapEnd(first_overlap_region)),
          math::Range1d(
              std::max(
                  current_lane_info.end_ra_arclength -
                      kMinDistOffsetToCheckOverlapWithinLaneEndSegmentInMeter,
                  current_lane_info.start_ra_arclength),
              current_lane_info.end_ra_arclength +
                  kMinDistOffsetToCheckOverlapWithinLaneEndSegmentInMeter));
  if (!is_overlap_within_left_turn_end_segment) {
    // The agent should be close to ego at the end of left-turn lane in inside
    // passing scenario.
    return false;
  }

  // TODO(waylon): We could verify whether agent is from the same lane or a
  // nearby lane before entering the intersection when lane-association is
  // landing.

  return true;
}

double ComputeAverageAccelerationOnPredictedTrajectory(
    const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info) {
  const planner::PredictedTrajectoryWrapper& predicted_trajectory =
      agent_trajectory_info.predicted_trajectory();
  if (predicted_trajectory.empty()) {
    return reasoning_object.acceleration();
  }

  // Computes the average acceleration in the first 3 seconds of the prediction
  // trajectory.
  constexpr int kPredictedAvgAccelPoseIndexHorizon = 30;
  const size_t check_ix = std::min(predicted_trajectory.size() - 1,
                                   kPredictedAvgAccelPoseIndexHorizon);
  return check_ix == 0
             ? predicted_trajectory.pose(0).accel()
             : ((predicted_trajectory.pose(check_ix).speed() -
                 predicted_trajectory.pose(0).speed()) /
                math::Ms2Sec(predicted_trajectory.pose(check_ix).timestamp() -
                             predicted_trajectory.pose(0).timestamp()));
}

double GetEgoTrajectoryMaxOutLaneDistSoFarBeforeArclength(
    const TrajectoryInfo& trajectory_info, double arclength) {
  const pb::PathOutOfLaneInfo* out_lane_encroachment_info_ptr =
      trajectory_info.out_lane_encroachment_info_ptr();
  if (out_lane_encroachment_info_ptr == nullptr ||
      out_lane_encroachment_info_ptr->lateral_encroachment_distance().empty()) {
    return 0.0;
  }
  double max_out_lane_dist =
      out_lane_encroachment_info_ptr->lateral_encroachment_distance().at(0);
  for (int index = 0;
       index < out_lane_encroachment_info_ptr->out_of_lane_segments().size();
       ++index) {
    if (out_lane_encroachment_info_ptr->out_of_lane_segments()
            .at(index)
            .start() > arclength) {
      break;
    }
    math::UpdateMax(
        out_lane_encroachment_info_ptr->lateral_encroachment_distance().at(
            index),
        max_out_lane_dist);
  }
  // Get the max out-lane encroachment distance so far by extracting seed info.
  return std::max(
      trajectory_info.max_out_lane_encroached_distance_on_lane_marking()
          .value_or(0.0),
      max_out_lane_dist);
}

bool ShouldEnlargeRequiredLateralGapForLateralApproachAgent(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info, int overlap_region_ix) {
  // Return false if the agent is NOT small vehicle.
  // TODO(chantong): Apply this adjustment to large vehicle as well.
  if (!reasoning_object.planner_object().is_small_vehicle()) {
    return false;
  }

  // Return false if the agent is NOT moving or laterally moving away from ego
  // path.
  if (reasoning_object.IsStationary() ||
      reasoning_object.is_laterally_moving_away()) {
    return false;
  }

  if (reasoning_object.IsOnEgoPath()) {
    return false;
  }

  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();

  // Return false if the agent is no obvious lateral moving intention.
  const bool is_lateral_accelerating_agent =
      (object_proximity_info.signed_lateral_gap() *
       object_proximity_info.signed_lateral_acceleration()) < 0 &&
      std::abs(object_proximity_info.signed_lateral_acceleration()) >
          kRelativeLateralAccelLowerBoundForConsiderAgentRiskInMpss;
  if (std::abs(object_proximity_info.signed_lateral_speed()) <
          kRelativeLateralSpeedLowerBoundForConsiderAgentRiskInMps &&
      !is_lateral_accelerating_agent) {
    return false;
  }

  // From the perspective of the motivation cases, the agent risk of lateral
  // movement mainly occurs when the agent is in front of the ADV and the ADV is
  // continuously making PASS decision on the agent.
  if (reasoning_object.IsBehindEgoFrontAxle()) {
    return false;
  }

  // Excluding the scenarios when ADV is perform proactive behavior such as lane
  // change, x-lane nudge, pull-over/out, etc. But in-lane nudge is included.
  if (!trajectory_info.IsLaneKeep()) {
    return false;
  }

  const pb::OverlapRegion& overlap_region =
      agent_trajectory_info.overlap_region(overlap_region_ix);
  // Excluding the scenarios when ego is making turning and the start overlap
  // region is in turning segment.
  const bool is_ego_in_straight_segment = trajectory_info.IsGoingStraight();
  const bool is_agent_overlap_in_straight_segment =
      trajectory_info.IsGoingStraight(
          (overlap_region.contain_strict_overlap()
               ? speed::GetStrictOverlapStart(overlap_region)
               : speed::GetPaddedOverlapStart(overlap_region)));
  if (!is_ego_in_straight_segment && !is_agent_overlap_in_straight_segment) {
    return false;
  }

  // Return false if it is not ACC scenario.
  if (!reasoning_util::IsSameDirectionOverlapRegion(overlap_region)) {
    return false;
  }

  return true;
}

bool HasCuttingInIntentionBaseOnCyclistTrackedState(
    const ReasoningObject& reasoning_object) {
  DCHECK(reasoning_object.is_cyclist());

  if (reasoning_object.IsOnEgoPath()) {
    return true;
  }

  // The agent is moving towards Ego path laterally.
  const pb::ObjectProximityInfo& proximity_info =
      reasoning_object.object_proximity_info();
  const double signed_lateral_gap = proximity_info.signed_lateral_gap();
  if (signed_lateral_gap * proximity_info.signed_lateral_speed() < 0.0 &&
      std::abs(signed_lateral_gap) <
          reasoning_object.CriticalRequiredLateralGap()) {
    return true;
  }

  return false;
}

bool ShouldReduceRequiredLateralGapForSlowMovingVehicle(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info, int overlap_region_ix) {
  // Return false if the agent is NOT vehicle.
  if (!reasoning_object.planner_object().is_vehicle()) {
    return false;
  }

  if (reasoning_object.IsLargeVehicle()) {
    // For large vehicles, do not adjust down the pass lat gap when they are
    // behind ego front axle as the pass lat gap has already been adjusted. We
    // exclude turning large vehicles as well to adapt to under-estimated
    // lateral risks in prediction.
    if (reasoning_object.IsBehindEgoFrontAxle() ||
        agent_trajectory_info.IsTurning()) {
      return false;
    }
  }

  const pb::OverlapRegion& overlap_region =
      agent_trajectory_info.overlap_region(overlap_region_ix);

  // Should not decrease required lateral gap directly because it is safety
  // critical for agent with strict overlaps. Such as cut-in, passive merge
  // scenarios.
  if (overlap_region.contain_strict_overlap()) {
    return false;
  }

  // Return false if actual lateral gap is less than critical required lateral
  // gap or given lower bound.
  const pb::ObjectProximityInfo& proximity_info =
      reasoning_object.object_proximity_info();
  if (std::abs(proximity_info.signed_lateral_gap()) <
      reasoning_object.CriticalRequiredLateralGap()) {
    return false;
  }

  // Returns false if the agent has intention that moving towards ego path.
  // TODO(chantong): Need a more general agent intention model. It has been
  // observed from the motivation case that for an agent with no lateral
  // movement, the lateral speed fluctuates within a range of 0.15 m/s.
  constexpr double kRelativeLateralSpeedToConsiderLateralMovementInMps = 0.15;
  if (!reasoning_object.is_laterally_moving_away() &&
      (abs(proximity_info.signed_lateral_speed()) >
       kRelativeLateralSpeedToConsiderLateralMovementInMps)) {
    return false;
  }

  // Excluding the scenarios when ADV is performing proactive behavior such as
  // merge-fork, lane change, x-lane nudge and pull-over/out, etc.
  if (!trajectory_info.IsLaneKeep() ||
      !reasoning_util::IsEgoPassiveInteractionScene(
          trajectory_info, agent_trajectory_info, reasoning_object,
          overlap_region_ix)) {
    return false;
  }

  return true;
}

bool ShouldEnlargeRequiredLateralGapForAgentInSqueezeRegion(
    const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info, int overlap_region_ix) {
  return agent_trajectory_info.overlap_region_info(overlap_region_ix)
             .is_in_squeeze_regions &&
         !reasoning_object.IsStationary();
}

bool ShouldAllowEmergencyBrakeForLeadAndMergeAgentReasoner(
    const WorldContext& world_context, const ReasoningObject& reasoning_object,
    const TrajectoryInfo& trajectory_info,
    const AgentTrajectoryInfo& agent_trajectory_info,
    const int overlap_region_ix,
    pb::LeadAndMergeEBDebug* emergency_brake_debug) {
  DCHECK(!reasoning_object.is_pedestrian());
  if (!reasoning_object.is_vehicle() && !reasoning_object.is_cyclist()) {
    // TODO(waylon): We would only enable Emergency Braking (EB) for vehicles
    // and cyclists, excluding unknown-type objects. One corner case is
    // cn8783766, in which we need to yield to a small unknown object.
    if (emergency_brake_debug != nullptr) {
      emergency_brake_debug->set_reason(
          pb::LeadAndMergeEBDebug::UNMATCHED_AGENT_TYPE);
      emergency_brake_debug->set_should_allow(false);
    }
    return false;
  }

  // Enable EB if the front agent is already blocking ego path.
  if (ShouldAllowEmergencyBrake(world_context, reasoning_object)) {
    if (emergency_brake_debug != nullptr) {
      emergency_brake_debug->set_reason(pb::LeadAndMergeEBDebug::BLOCKING_PATH);
      emergency_brake_debug->set_should_allow(true);
    }
    return true;
  }

  // Enable EB if the agent is encroaching ego's lane.
  if (ShouldBeCautiousAroundEncroachingEgoLaneVehicle(reasoning_object,
                                                      agent_trajectory_info)) {
    return true;
  }

  const pb::OverlapRegion& overlap_region =
      agent_trajectory_info.overlap_region(overlap_region_ix);
  // TODO(waylon): The impact of perception errors of cyclists on EB is
  // significant, and we have to filter out the cyclist type now.
  if (reasoning_object.is_cyclist()) {
    if (!FLAGS_planning_enable_enlarging_scope_of_eb_for_vru) {
      return ShouldAllowEmergencyBrakeForMergingCyclist(
          world_context, reasoning_object, overlap_region,
          pb::ReasonerId::LEAD_AND_MERGE);
    }

    return ShouldAllowEmergencyBrakeForVRU(world_context, reasoning_object,
                                           overlap_region,
                                           pb::ReasonerId::LEAD_AND_MERGE);
  }

  std::string emergency_brake_debug_str;
  const OverlapRegionInfo& overlap_region_info =
      agent_trajectory_info.overlap_region_info(overlap_region);
  const pnc_map::Lane* current_ego_lane =
      *(trajectory_info.lane_sequence_iterator().current_lane());
  const bool is_ego_in_roundabout =
      current_ego_lane->section()->road()->is_associated_with_roundabout();
  if ((overlap_region_info.is_crossing_ego_path && is_ego_in_roundabout) ||
      (overlap_region_info.is_crossing_ego_path &&
       !reasoning_object.IsBehindEgoLeadingBumper())) {
    // L&M handles all agents in roundabout scenes and certain agents gradually
    // crossing Ego path. We will allow emergency brake in roundabout scene even
    // if the agent is projected behind ego bumper.
    absl::StrAppend(&emergency_brake_debug_str, "crossing agent");
    if (emergency_brake_debug != nullptr) {
      emergency_brake_debug->set_reason(
          pb::LeadAndMergeEBDebug::RULE_BASED_HIGH_RISK);
      emergency_brake_debug->set_should_allow(true);
      emergency_brake_debug->set_emergency_brake_debug_str(
          std::move(emergency_brake_debug_str));
    }
    return true;
  }

  if (!WithEBInterestedRange(world_context, reasoning_object)) {
    if (emergency_brake_debug != nullptr) {
      emergency_brake_debug->set_reason(
          pb::LeadAndMergeEBDebug::OUT_OF_SPACIAL_CONTEXT);
      emergency_brake_debug->set_should_allow(false);
    }
    return false;
  }

  if (reasoning_util::IsEgoPassiveInteractionScene(
          trajectory_info, agent_trajectory_info, reasoning_object,
          overlap_region_ix) &&
      reasoning_object.IsBehindEgoLeadingBumper()) {
    // Early return if the agent is behind ego leading bumper in passive
    // interactive scene for avoiding Emergency Brake false positive.
    if (emergency_brake_debug != nullptr) {
      emergency_brake_debug->set_reason(
          pb::LeadAndMergeEBDebug::BEHIND_LEAD_BUMPER_IN_PASSIVE_SCENE);
      emergency_brake_debug->set_should_allow(false);
    }
    return false;
  }

  const bool is_collided_at_ego_side =
      IsStrictOverlapRegionStartingBehind(overlap_region,
                                          /*ra_arclength_threshold=*/0.0);
  if (is_collided_at_ego_side) {
    // Early return if the agent hit the ego's left/right side or cut-behind.
    if (emergency_brake_debug != nullptr) {
      emergency_brake_debug->set_reason(
          pb::LeadAndMergeEBDebug::COLLIDED_AT_EGO_SIDE);
      emergency_brake_debug->set_should_allow(false);
    }
    return false;
  }

  constexpr double kMaxTimeEnteringEgoPathInSeconds = 1.5;
  if (HasStrictOverlap(overlap_region)) {
    absl::StrAppendFormat(&emergency_brake_debug_str, "start_strict_time:%.2f",
                          overlap_region.start_strict_relative_time_in_sec());
    if (overlap_region.start_strict_relative_time_in_sec() <
        kMaxTimeEnteringEgoPathInSeconds) {
      if (emergency_brake_debug != nullptr) {
        emergency_brake_debug->set_reason(
            pb::LeadAndMergeEBDebug::RULE_BASED_HIGH_RISK);
        emergency_brake_debug->set_should_allow(true);
        emergency_brake_debug->set_emergency_brake_debug_str(
            emergency_brake_debug_str);
      }
      return true;
    }
  }

  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();
  // We will enable emergency brake if an agent approaches Ego path at a
  // significant angle and is not stationary. The threshold is determined by
  // https://cooper.didichuxing.com/docs2/document/2203285965751.
  constexpr double kRelativeHeadingLowerBoundForConsiderLargeAngleAgent = 0.10;
  if (!reasoning_object.IsStationary() &&
      std::abs(
          reasoning_object.object_proximity_info().relative_motion_heading()) >
          kRelativeHeadingLowerBoundForConsiderLargeAngleAgent &&
      std::abs(object_proximity_info.signed_lateral_gap()) <
          reasoning_object.required_lateral_gap()(Discomforts::kMin)) {
    absl::StrAppendFormat(
        &emergency_brake_debug_str,
        "non-stationary and close to ego path with large angle");
    if (emergency_brake_debug != nullptr) {
      emergency_brake_debug->set_reason(
          pb::LeadAndMergeEBDebug::RULE_BASED_HIGH_RISK);
      emergency_brake_debug->set_should_allow(true);
      emergency_brake_debug->set_emergency_brake_debug_str(
          emergency_brake_debug_str);
    }

    return true;
  }

  if (reasoning_object.is_laterally_moving_away()) {
    // TODO(chantong): Consider using ObjectOccupancyParamInfo instead.
    if (emergency_brake_debug != nullptr) {
      emergency_brake_debug->set_reason(
          pb::LeadAndMergeEBDebug::LATERALLY_MOVING_AWAY);
      emergency_brake_debug->set_should_allow(false);
    }
    return false;
  }

  const double abs_lateral_speed =
      std::abs(object_proximity_info.signed_lateral_speed());
  const double agent_enter_ego_path_time =
      math::NearZero(abs_lateral_speed)
          ? std::numeric_limits<double>::infinity()
          : object_proximity_info.abs_lateral_gap() / abs_lateral_speed;
  constexpr double kMaxTimeProximityEnteringEgoPathInSeconds = 0.6;
  absl::StrAppendFormat(&emergency_brake_debug_str,
                        "proximity_enter_path_time:%.2f",
                        agent_enter_ego_path_time);
  if (agent_enter_ego_path_time > kMaxTimeProximityEnteringEgoPathInSeconds) {
    // For agent who is moving towards the ego path suddenly, the prediction
    // might not recall the trajectory and the strict overlap might far away
    // from the ground truth. Thus, the proximity info based estimated time to
    // conflict is added to allow emergency brake in advance. As the proximity
    // based estimation has large uncertainty, only the near field entering path
    // time is considered.
    if (emergency_brake_debug != nullptr) {
      emergency_brake_debug->set_reason(
          pb::LeadAndMergeEBDebug::PROXIMITY_REACH_EGO_PATH);
      emergency_brake_debug->set_should_allow(false);
      emergency_brake_debug->set_emergency_brake_debug_str(
          emergency_brake_debug_str);
    }
    return false;
  }

  if (emergency_brake_debug != nullptr) {
    emergency_brake_debug->set_reason(
        pb::LeadAndMergeEBDebug::PROXIMITY_REACH_EGO_PATH);
    emergency_brake_debug->set_should_allow(true);
    emergency_brake_debug->set_emergency_brake_debug_str(
        emergency_brake_debug_str);
  }
  return true;
}

bool IsPullOverTailingAgent(const TrajectoryInfo& trajectory_info,
                            const ReasoningObject& reasoning_object) {
  if (!trajectory_info.is_pull_over_jump_in_trajectory()) {
    return false;
  }
  const std::vector<pull_over::PullOverObjectInfo>& trailing_objects =
      base::CheckAndGetValue(
          DCHECK_NOTNULL(trajectory_info.pull_over_dest_meta_ptr())
              ->jump_in_guidance)
          .trailing_objects();

  return !trailing_objects.empty() &&
         trailing_objects.front().typed_object_id().id == reasoning_object.id();
}

// Returns true if the
// lateral gap between object's proximity info and Ego path is larger than the
// lateral gap between Ego path and the Ego road's physical boundary.
bool IsObjectOutsidePhysicalBoundaryBaseOnProximityInfo(
    const pb::ObjectProximityInfo& object_proximity_info,
    const pb::PhysicalBoundaryProximityInfo& boundary_proximity_info) {
  const auto start_iter =
      std::lower_bound(boundary_proximity_info.ra_arc_length_on_path().begin(),
                       boundary_proximity_info.ra_arc_length_on_path().end(),
                       object_proximity_info.projected_ra_arc_length().start());
  if (start_iter == boundary_proximity_info.ra_arc_length_on_path().end()) {
    return false;
  }

  auto end_iter = std::upper_bound(
      start_iter, boundary_proximity_info.ra_arc_length_on_path().end(),
      object_proximity_info.projected_ra_arc_length().end());

  if (end_iter == start_iter) {
    end_iter = std::next(start_iter);
  }

  for (auto iter = start_iter; iter != end_iter; iter++) {
    const int index = std::distance(
        boundary_proximity_info.ra_arc_length_on_path().begin(), iter);
    if (object_proximity_info.abs_lateral_gap() <
        boundary_proximity_info.abs_distance(index)) {
      return false;
    }
  }

  return true;
}

bool IsObjectOutsidePhysicalBoundary(
    const ReasoningObject& reasoning_object,
    const speed::pb::PhysicalBoundaryProximityInfoPair&
        physical_boundary_proximity_info) {
  if (reasoning_object.IsOnEgoPath()) {
    return false;
  }

  if (reasoning_object.is_fully_behind()) {
    return false;
  }

  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();
  if (object_proximity_info.signed_lateral_gap() < 0.0) {
    // Agent is on Ego right side.
    return IsObjectOutsidePhysicalBoundaryBaseOnProximityInfo(
        object_proximity_info, physical_boundary_proximity_info.right());
  }

  // Agent is on Ego left side.
  return IsObjectOutsidePhysicalBoundaryBaseOnProximityInfo(
      object_proximity_info, physical_boundary_proximity_info.left());
}

double FindEarliestStrictRelativeTime(
    const AgentTrajectoryInfo& agent_trajectory_info) {
  for (unsigned i = 0; i < agent_trajectory_info.overlap_regions().size();
       ++i) {
    const pb::OverlapRegion& overlap = agent_trajectory_info.overlap_region(i);
    if (!overlap.contain_strict_overlap()) continue;
    return overlap.start_strict_relative_time_in_sec();
  }
  return std::numeric_limits<double>::infinity();
}

bool IsPotentialUTurnBlockage(const TrajectoryInfo& trajectory_info,
                              const ReasoningObject& reasoning_object) {
  const std::vector<const pnc_map::Lane*>& uturn_lane_sequence =
      trajectory_info.closest_uturn_lane_sequence();
  if (uturn_lane_sequence.empty()) {
    // Ensure uturn_lane_sequence is not empty to guarantee the execution of
    // subsequent calculations.
    return false;
  }
  DCHECK(trajectory_info.closest_uturn_lane_sequence_center_line().has_value());
  DCHECK(trajectory_info.uturn_end_point_arclength_on_path().has_value());

  if (!trajectory_info.IsInUTurn(
          -kLongitudinalBufferToCheckEgoIsInUTurnInMeter,
          kLongitudinalBufferToCheckEgoIsInUTurnInMeter)) {
    // EGO is not doing a u-turn.
    return false;
  }

  if (!reasoning_object.IsStationary()) {
    // The object is not stationary.
    return false;
  }

  constexpr double kMaxLaneEncroachmentThresholdInMeter = 0.7;
  if (!reasoning_object.is_blocking_object() &&
      !(reasoning_object.object_occupancy_param_ptr() != nullptr &&
        reasoning_object.object_occupancy_param_ptr()->max_lane_encroachment_m <
            kMaxLaneEncroachmentThresholdInMeter)) {
    // The object is not a blocking object and it is in ego's lane.
    return false;
  }

  const speed::pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();
  if (object_proximity_info.out_of_path_range()) {
    // When this field is true, all other fields in object_proximity_info is not
    // usable.
    return false;
  }

  if (object_proximity_info.signed_lateral_gap() > 0.0) {
    // The object is on the left side.
    return false;
  }

  if (object_proximity_info.projected_ra_arc_length().start() >
      kLookaheadHorizonForPotentialUTurnBlockageInMeter) {
    // The object is far away along the longitudinal direction.
    return false;
  }

  if (object_proximity_info.abs_lateral_gap() >
      kMaxLateralGapForPotentialUTurnBlockageInMeter) {
    // The object is far away along the lateral direction.
    return false;
  }

  const std::optional<math::geometry::PolylineCurve2d>&
      ref_closest_uturn_lane_sequence_center_line =
          trajectory_info.closest_uturn_lane_sequence_center_line();
  DCHECK(ref_closest_uturn_lane_sequence_center_line.has_value());

  const std::optional<double>& ref_uturn_end_point_arclength_on_path =
      trajectory_info.uturn_end_point_arclength_on_path();
  DCHECK(ref_uturn_end_point_arclength_on_path.has_value());
  const double uturn_end_point_arclength_on_path =
      ref_uturn_end_point_arclength_on_path.value();

  const math::Range1d arclength_of_interst_for_object_search{
      uturn_end_point_arclength_on_path -
          kLongitudinalBufferToFindUTurnNearbyObjectInMeter,
      uturn_end_point_arclength_on_path +
          kLongitudinalBufferToFindUTurnNearbyObjectInMeter};
  const math::Range1d arclength_of_object_on_path{
      object_proximity_info.projected_ra_arc_length().start(),
      object_proximity_info.projected_ra_arc_length().end()};

  if (!IsValidRange(arclength_of_object_on_path) ||
      !math::AreRangesOverlapping(arclength_of_object_on_path,
                                  arclength_of_interst_for_object_search)) {
    // The object is out of longitudinal range.
    return false;
  }

  return true;
}

ObjectId PopulateMergeLeaderOrTailgatorInfo(
    const speed::SpeedResult& speed_result,
    const std::map<ObjectId, speed::pb::ObjectProximityInfo>&
        object_proximity_info_map,
    const google::protobuf::RepeatedField<google::protobuf::int64>&
        merge_gap_candidates,
    const bool for_pass, double& pass_or_yield_state_upper_bound,
    double& pass_or_yield_state_lower_bound) {
  ObjectId merge_object_id = -1;
  for (const auto& [object_id, object_proximity] : object_proximity_info_map) {
    if (object_proximity.out_of_path_range()) continue;

    // Skip objects that have no overlaps with merge regions in reasoning
    // pipeline.
    if (std::none_of(merge_gap_candidates.begin(), merge_gap_candidates.end(),
                     [object_id = object_id](
                         const google::protobuf::int64& candidate_id) {
                       return object_id == candidate_id;
                     })) {
      continue;
    }

    // Check whether the object should be considered as leader or tailgator
    // according to the pass and yield decisions.
    bool should_consider_pass = false;
    bool should_consider_yield = false;
    for (size_t ix = 0; ix < speed_result.constraints().size(); ++ix) {
      if (speed_result.constraints()[ix].obj_id != object_id) continue;
      if (speed_result.decisions()[ix] == speed::pb::SpeedDecision::YIELD) {
        // The object can only be considered as the leader if one of the
        // decisions for multiple constraints is YIELD.
        should_consider_pass = false;
        should_consider_yield = true;
      } else if (speed_result.decisions()[ix] ==
                 speed::pb::SpeedDecision::PASS) {
        // The object can be considered as the tailgator if none of the
        // decisions for multiple constraints are YIELD and at least one of the
        // decisions is PASS.
        should_consider_pass = !should_consider_yield;
      }
    }
    DCHECK(  // NOLINT, FP when checking macro DCHECK
        !should_consider_pass || !should_consider_yield)
        << "should not consider an agent as both leader and tailgator.";

    // Update the leader and tailgator info in merge gap.
    // TODO(chantong): Maybe projected lon and lat speed should be put into
    // consideration.
    const double projected_state_x =
        for_pass ? object_proximity.projected_ra_arc_length().end()
                 : object_proximity.projected_ra_arc_length().start();
    if (  // For pass, use the highest projected state end x as tailgator.
        (for_pass && should_consider_pass &&
         projected_state_x <= pass_or_yield_state_upper_bound &&
         math::UpdateMax(projected_state_x, pass_or_yield_state_lower_bound)) ||
        // For yield, use the lowest projected state start x as leader.
        (!for_pass && should_consider_yield &&
         projected_state_x >= pass_or_yield_state_lower_bound &&
         math::UpdateMin(projected_state_x, pass_or_yield_state_upper_bound))) {
      merge_object_id = object_id;
    }
  }
  DCHECK_LE(pass_or_yield_state_lower_bound, pass_or_yield_state_upper_bound);
  return merge_object_id;
}

double GetPointArcLengthWithOffset(
    const math::geometry::Point2d& query_point,
    const math::geometry::PolylineCurve2d& reference_path,
    const double arclength_offset) {
  return reference_path
             .GetProximity(query_point, math::pb::UseExtensionFlag::kAllow)
             .arc_length +
         arclength_offset;
}

std::optional<std::pair<int, int>> GetOverlapSegmentWithinNearFutureForSafety(
    const pb::OverlapRegion& overlap_region,
    const ReasoningObject& reasoning_object) {
  constexpr double kTimeHorizonConsideredForSafetyInSeconds = 1.5;
  constexpr double kIncreasedTimeHorizonConsideredForSafetyInSeconds = 2.5;
  // We should consider a longer time horizon to ensure minimum-level safety
  // for large vehicles which may take longer time to steer back to its own
  // lane.
  const bool should_use_increased_time_horizon =
      reasoning_object.IsLargeVehicle() && reasoning_object.IsOnEgoPath();
  const double considered_time_horizon =
      should_use_increased_time_horizon
          ? kIncreasedTimeHorizonConsideredForSafetyInSeconds
          : kTimeHorizonConsideredForSafetyInSeconds;
  const auto& overlap_slices = overlap_region.overlap_slices();
  DCHECK_GT(overlap_slices.size(), 0);
  if (overlap_slices.begin()->relative_time_in_sec() >
      considered_time_horizon) {
    return std::nullopt;
  }

  std::pair<int, int> segment{0, -1};
  for (int ii = 1; ii < overlap_slices.size(); ++ii) {
    if (overlap_slices.Get(ii).relative_time_in_sec() >
        considered_time_horizon) {
      segment.second = ii;
      break;
    }
  }
  if (segment.second < 0) {
    segment.second = overlap_slices.size();
  }
  DCHECK_LT(segment.first, segment.second);

  return segment;
}

const pnc_map::HardBoundaryInfo* GetFirstHardBoundaryAhead(
    const std::vector<pnc_map::HardBoundaryInfo>& hard_boundary_infos,
    const double ego_ra_arclength_in_lane) {
  if (hard_boundary_infos.empty()) return nullptr;

  const pnc_map::HardBoundaryInfo* first_boundary = nullptr;
  double first_boundary_min_arclength = std::numeric_limits<double>::max();

  for (const pnc_map::HardBoundaryInfo& hard_boundary : hard_boundary_infos) {
    if (hard_boundary.range_on_center_line.end_pos <=
        ego_ra_arclength_in_lane) {
      continue;
    }
    if (hard_boundary.range_on_center_line.start_pos <
        first_boundary_min_arclength) {
      first_boundary = &hard_boundary;
      first_boundary_min_arclength =
          hard_boundary.range_on_center_line.start_pos;
    }
  }

  return first_boundary;
}

const traffic_rules::YieldZoneInfo* GetFirstYieldZoneAhead(
    const std::vector<traffic_rules::YieldZoneInfo>& yield_zone_infos) {
  if (yield_zone_infos.empty()) return nullptr;

  const traffic_rules::YieldZoneInfo* first_yield_zone = nullptr;
  double first_yield_zone_min_arclength = std::numeric_limits<double>::max();

  for (const traffic_rules::YieldZoneInfo& yield_zone : yield_zone_infos) {
    // ego_arclength is 0 in yield_zone_infos
    if (yield_zone.ra_arclength_range.end_pos <= 0.0) {
      continue;
    }
    if (yield_zone.ra_arclength_range.start_pos <
        first_yield_zone_min_arclength) {
      first_yield_zone = &yield_zone;
      first_yield_zone_min_arclength = yield_zone.ra_arclength_range.start_pos;
    }
  }

  return first_yield_zone;
}

const traffic_rules::CrosswalkInLaneSequence* GetFirstCrosswalkAhead(
    const std::vector<traffic_rules::CrosswalkInLaneSequence>& crosswalks) {
  // The crosswalk list in the lane sequence is said to be sorted, but somehow
  // the crosswalks in the same junction may not order along the lane sequence.
  // That is why we end up with going through all the crosswalks.
  const traffic_rules::CrosswalkInLaneSequence* first_crosswalk = nullptr;
  double crosswalk_min_arclength = std::numeric_limits<double>::max();
  double crosswalk_max_arclength = std::numeric_limits<double>::lowest();
  double first_crosswalk_min_arclength = std::numeric_limits<double>::max();
  for (const traffic_rules::CrosswalkInLaneSequence& crosswalk : crosswalks) {
    for (const traffic_rules::YieldZoneInfo& yield_zone_info :
         crosswalk.yield_zone_infos) {
      crosswalk_min_arclength =
          std::min(crosswalk_min_arclength,
                   yield_zone_info.ra_arclength_range.start_pos);
      crosswalk_max_arclength = std::max(
          crosswalk_max_arclength, yield_zone_info.ra_arclength_range.end_pos);
    }
    if (crosswalk_max_arclength > 0.0) {
      if (first_crosswalk == nullptr ||
          crosswalk_min_arclength < first_crosswalk_min_arclength) {
        first_crosswalk = &crosswalk;
        first_crosswalk_min_arclength = crosswalk_min_arclength;
      }
    }
  }

  return first_crosswalk;
}

const traffic_rules::TrafficLightInfo* GetFirstTrafficLightAhead(
    const std::vector<traffic_rules::TrafficLightInfo>& traffic_lights) {
  if (traffic_lights.empty()) {
    return nullptr;
  }
  // Because the traffic lights in the lane sequence are ordered according to
  // their controlled lanes from the current lane to the end, the first in
  // distance is the first in the vector.
  return &traffic_lights.front();
}

bool IsOverlapRegionInMergeForkLanes(
    const std::vector<traffic_rules::EgoLaneMergeForkLanesInfo>&
        ego_merge_fork_lanes_info,
    const pb::OverlapRegion& overlap_region, const bool is_checking_merge,
    bool is_active_type) {
  if (overlap_region.overlap_slices().empty()) {
    return false;
  }

  const math::Range1d overlap_region_range =
      math::Range1d(GetPaddedOverlapStart(overlap_region),
                    GetPaddedOverlapEnd(overlap_region));
  // TODO(waylon): We determine the agent's side relative to the path based on
  // the first overlap slice and need to refine our approach based on the issues
  // encountered. We could also consider the overlap from prediction horizon
  // within a threshold.
  // TODO(waylon): Move the agent side check to overlap computation.
  const math::pb::Side agent_side =
      overlap_region.overlap_slices().at(0).signed_lateral_gap() < 0
          ? math::pb::kRight
          : math::pb::kLeft;

  return is_active_type ? IsOverlapRegionInActiveMergeForkLanes(
                              ego_merge_fork_lanes_info, overlap_region_range,
                              agent_side, is_checking_merge)
                        : IsOverlapRegionInPassiveMergeForkLanes(
                              ego_merge_fork_lanes_info, overlap_region_range,
                              agent_side, is_checking_merge);
}

bool IsOverlapRegionInActiveMergeForkLanes(
    const std::vector<traffic_rules::EgoLaneMergeForkLanesInfo>&
        ego_merge_fork_lanes_info,
    const math::Range1d& overlap_region_range, const math::pb::Side agent_side,
    bool is_checking_merge) {
  const auto merge_fork_overlapping_check =
      [&overlap_region_range, agent_side](
          const traffic_rules::MergeForkLaneInLaneSequence& merge_fork_lane)
      -> bool {
    // Filter out the merge fork lane behind Ego rear axle.
    if (merge_fork_lane.overlap_ra_range.end_pos < 0.0) {
      return false;
    }
    // We need pay more attention to left-side agent when ego is merging
    // from left side (kLeftMerge) or forking to the right side (kRightFork),
    // and vice versa.
    return ((agent_side == math::pb::kRight &&
             (merge_fork_lane.scene_type ==
                  traffic_rules::MergeForkSceneType::kLeftMerge ||
              merge_fork_lane.scene_type ==
                  traffic_rules::MergeForkSceneType::kRightFork)) ||
            (agent_side == math::pb::kLeft &&
             (merge_fork_lane.scene_type ==
                  traffic_rules::MergeForkSceneType::kRightMerge ||
              merge_fork_lane.scene_type ==
                  traffic_rules::MergeForkSceneType::kLeftFork))) &&
           math::AreRangesOverlapping(
               overlap_region_range,
               math::Range1d(merge_fork_lane.overlap_ra_range.start_pos,
                             merge_fork_lane.overlap_ra_range.end_pos +
                                 kExtendDistToMergeForkLaneEndInMeter));
  };

  return std::any_of(
      ego_merge_fork_lanes_info.begin(), ego_merge_fork_lanes_info.end(),
      [&merge_fork_overlapping_check,
       is_checking_merge](const traffic_rules::EgoLaneMergeForkLanesInfo&
                              ego_merge_fork_lanes_info) {
        if (is_checking_merge) {
          return std::any_of(
              ego_merge_fork_lanes_info.sorted_merge_lanes.begin(),
              ego_merge_fork_lanes_info.sorted_merge_lanes.end(),
              merge_fork_overlapping_check);
        }
        return std::any_of(ego_merge_fork_lanes_info.sorted_fork_lanes.begin(),
                           ego_merge_fork_lanes_info.sorted_fork_lanes.end(),
                           merge_fork_overlapping_check);
      });
}

bool IsOverlapRegionInPassiveMergeForkLanes(
    const std::vector<traffic_rules::EgoLaneMergeForkLanesInfo>&
        ego_passive_merge_fork_lanes_infos,
    const math::Range1d& overlap_region_range, const math::pb::Side agent_side,
    bool is_checking_merge) {
  const auto passive_merge_fork_overlapping_check =
      [&overlap_region_range, agent_side](
          const traffic_rules::MergeForkLaneInLaneSequence& merge_fork_lane)
      -> bool {
    // Filter out the merge fork lane behind Ego rear axle.
    if (merge_fork_lane.overlap_ra_range.end_pos < 0.0) {
      return false;
    }
    // Two scenarios are considered as passive merge:
    // 1) right-side agent is merging from the right into the ADV's lane.
    // 2) left-side agent is merging from the left into the ADV's lane.
    return ((agent_side == math::pb::kRight &&
             merge_fork_lane.scene_type ==
                 traffic_rules::MergeForkSceneType::kPassiveRightMerge) ||
            (agent_side == math::pb::kLeft &&
             merge_fork_lane.scene_type ==
                 traffic_rules::MergeForkSceneType::kPassiveLeftMerge)) &&
           math::AreRangesOverlapping(
               overlap_region_range,
               math::Range1d(merge_fork_lane.overlap_ra_range.start_pos,
                             merge_fork_lane.overlap_ra_range.end_pos +
                                 kExtendDistToMergeForkLaneEndInMeter));
  };

  return std::any_of(
      ego_passive_merge_fork_lanes_infos.begin(),
      ego_passive_merge_fork_lanes_infos.end(),
      [&passive_merge_fork_overlapping_check,
       is_checking_merge](const traffic_rules::EgoLaneMergeForkLanesInfo&
                              ego_passive_merge_fork_lanes_info) {
        if (is_checking_merge) {
          return std::any_of(
              ego_passive_merge_fork_lanes_info.sorted_merge_lanes.begin(),
              ego_passive_merge_fork_lanes_info.sorted_merge_lanes.end(),
              passive_merge_fork_overlapping_check);
        }
        DCHECK(false) << "Passive fork is not supported now.";
        return false;
      });
}

double ComputeExtraYieldExtraTimeForYieldAlwaysPossible(
    const WorldContext& world_context, const pb::OverlapRegion& overlap_region,
    const double yield_dist_buffer) {
  // The extra yield_extra_time to compensate for the potential control phase
  // error when ego stay stopped.
  constexpr double kExtraYieldExtraTimeForStayStoppedInSec = 0.2;
  // Add an extra buffer if ego should stay stopped for the overlap for a while
  // to compensate for the potential control phase error.
  if (reasoning_util::WillEgoKeepStoppedForOverlapToCompensateControlError(
          overlap_region, world_context.ra_to_leading_bumper(),
          yield_dist_buffer)) {
    return kExtraYieldExtraTimeForStayStoppedInSec;
  }

  return 0.0;
}

bool IsEgoTrajectoryStraightFromRearAxleToRearBumper(
    const TrajectoryInfo& trajectory_info, const double ra_to_rear_bumper,
    const double path_straight_threshold, std::string* debug_str) {
  double total_curvature = 0.0;
  int sample_count = 0;
  if (trajectory_info.unextended_path().arc_lengths().size() < 2) {
    DCHECK(false) << "Arc lengths size is less than 2, cannot compute arc "
                     "length difference.";
    return false;
  }
  for (size_t index = 0; index <= kMaxEndIndexForPathSearch; index++) {
    const double curvature =
        std::abs(trajectory_info.unextended_path().curvatures()[index]);
    total_curvature += curvature;
    sample_count++;
    if ((trajectory_info.path().arc_lengths()[index] -
         trajectory_info.path().arc_lengths()[0]) > ra_to_rear_bumper)
      break;
  }
  // Calculate average curvature
  const double avg_curvature = total_curvature / sample_count;
  absl::StrAppendFormat(debug_str, "avg curve: %.2f;", avg_curvature);
  return (avg_curvature < path_straight_threshold);
}

bool IsPullOverGapAlignTrailingAgent(const TrajectoryInfo& trajectory_info,
                                     const ReasoningObject& reasoning_object) {
  if (!trajectory_info.is_pull_over_cross_lane_jump_in_trajectory()) {
    return false;
  }
  const std::unordered_set<int64>& trailing_agent_ids =
      trajectory_info.pull_over_gap_align_trailing_agent_ids();
  return trailing_agent_ids.find(reasoning_object.id()) !=
         trailing_agent_ids.end();
}

bool HasYieldSign(const pnc_map::Lane* lane,
                  const std::vector<traffic_rules::TrafficSignInLaneSequence>&
                      traffic_signs) {
  return std::any_of(
      traffic_signs.begin(), traffic_signs.end(),
      [&lane](const traffic_rules::TrafficSignInLaneSequence& traffic_sign) {
        return lane->id() == traffic_sign.controlled_lane->id() &&
               traffic_sign.traffic_sign_ptr->type() ==
                   pnc_map::TrafficSignType::kYieldToVehicle;
      });
}

bool IsUnprotectedLeftTurn(
    const pnc_map::Lane* query_lane,
    const std::vector<traffic_rules::EgoLaneConflictingLanesInfo>&
        conflicting_lanes) {
  if (query_lane == nullptr || !query_lane->IsInJunction() ||
      query_lane->turn() != hdmap::Lane::LEFT) {
    return false;
  }

  // Find the associated conflicting lane of the query turn lane.
  const auto conflicting_lane_iter =
      std::find_if(conflicting_lanes.begin(), conflicting_lanes.end(),
                   [query_lane_id = query_lane->id()](
                       const traffic_rules::EgoLaneConflictingLanesInfo&
                           ego_conflicting_lane) {
                     return ego_conflicting_lane.ego_lane.id() == query_lane_id;
                   });
  if (conflicting_lane_iter == conflicting_lanes.end()) {
    return false;
  }

  return conflicting_lane_iter->scene_type ==
         traffic_rules::ConflictingLaneType::kUnprotectedLeftTurn;
}

bool IsRightTurn(const pnc_map::Lane* query_lane) {
  return query_lane != nullptr && query_lane->IsInJunction() &&
         query_lane->turn() == hdmap::Lane::RIGHT;
}

bool IsUTurn(const pnc_map::Lane* query_lane) {
  return query_lane != nullptr && query_lane->turn() == hdmap::Lane::U_TURN;
}

bool HasSpecifiedTurnTypeInSequence(
    const std::vector<const pnc_map::Lane*>& lanes,
    const hdmap::Lane::Turn turn_type, const bool consider_regular_lane) {
  return std::any_of(
      lanes.begin(), lanes.end(),
      [consider_regular_lane, turn_type](const pnc_map::Lane* lane) {
        DCHECK(lane != nullptr);
        return (consider_regular_lane
                    ? true
                    : lane->type() ==
                          hdmap::Lane::LaneType::Lane_LaneType_VIRTUAL) &&
               lane->turn() == turn_type;
      });
}

bool IsStraightLane(const pnc_map::Lane* query_lane) {
  switch (query_lane->turn()) {
    case hdmap::Lane_Turn_STRAIGHT_RIGHT:
    case hdmap::Lane_Turn_STRAIGHT:
    case hdmap::Lane_Turn_LEFT_STRAIGHT:
    case hdmap::Lane_Turn_LEFT_STRAIGHT_RIGHT:
    case hdmap::Lane_Turn_U_TURN_AND_STRAIGHT:
    case hdmap::Lane_Turn_U_TURN_AND_LEFT_AND_STRAIGHT:
      return true;
    case hdmap::Lane_Turn_UNKNOWN_TURN:
    case hdmap::Lane_Turn_RIGHT:
    case hdmap::Lane_Turn_LEFT:
    case hdmap::Lane_Turn_LEFT_RIGHT:
    case hdmap::Lane_Turn_U_TURN_AND_LEFT:
    case hdmap::Lane_Turn_U_TURN:
    case hdmap::Lane_Turn_ALL_TURNS:
      return false;
    default:
      DCHECK(false) << absl::StrFormat(
          "Lane %ld turn type %s is NOT handled.", query_lane->id(),
          hdmap::Lane_Turn_Name(query_lane->turn()));
  }

  return false;
}

bool IsUnprotectedRightTurn(
    const pnc_map::Lane* query_lane,
    const voy::TrafficLights& traffic_light_detection,
    const pnc_map::JointPncMapService& joint_pnc_map_service) {
  // Check if the query lane is a right turn lane unprotected.
  if (!IsRightTurn(query_lane)) {
    return false;
  }

  const auto& traffic_signals = traffic_rules::GetAssociatedTrafficSignals(
      joint_pnc_map_service, *query_lane);
  if (!traffic_signals.empty()) {
    const traffic_rules::MatchedTrafficLightInfo matched_traffic_info =
        traffic_rules::GetMatchedTrafficLight(
            joint_pnc_map_service, *query_lane, traffic_light_detection);
    if (matched_traffic_info.associate_traffic_signal &&
        matched_traffic_info.traffic_signal_detection) {
      return matched_traffic_info.traffic_signal_detection->color() ==
                 voy::TrafficLight::YELLOW &&
             matched_traffic_info.traffic_signal_detection->is_flashing();
    }
    return false;
  }

  // Check the traffic light state in the neighboring lane.
  const voy::TrafficLight* traffic_light_for_unprotected_right_turn =
      traffic_rules::ComputeTrafficLightForUnprotectedRightTurn(
          joint_pnc_map_service, traffic_light_detection, *query_lane);

  return traffic_light_for_unprotected_right_turn != nullptr &&
         traffic_light_for_unprotected_right_turn->color() ==
             voy::TrafficLight::RED;
}

bool IsRoadPrecedenceLowerAgainstEgoLane(
    const pnc_map::Lane* ego_lane_in_junction,
    const pnc_map::Lane* conflicting_lane,
    const std::vector<traffic_rules::TrafficSignInLaneSequence>&
        traffic_signs) {
  // A turning lane has a lower road precedence to a straight lane.
  if (ego_lane_in_junction->turn() == hdmap::Lane_Turn_STRAIGHT &&
      conflicting_lane->turn() != hdmap::Lane_Turn_STRAIGHT) {
    return true;
  }

  // A conflicting lane with a yield sign has a lower road precedence.
  if (HasYieldSign(conflicting_lane, traffic_signs)) {
    return true;
  }

  // A conflicting lane with a stop sign (while ego lane hasn't) has a lower
  // road precedence.
  if (!traffic_rules::IsControlledByStopSign(ego_lane_in_junction) &&
      traffic_rules::IsControlledByStopSign(conflicting_lane)) {
    return true;
  }

  return false;
}

const pnc_map::Lane* GetPredecessorLane(const pnc_map::Lane* lane,
                                        bool prefer_straight_lane) {
  if (lane == nullptr || lane->predecessors().empty()) {
    return nullptr;
  }

  if (!prefer_straight_lane) {
    return lane->predecessors().front();
  }

  auto predecessor_lane_it =
      std::find_if(lane->predecessors().begin(), lane->predecessors().end(),
                   [](const pnc_map::Lane* predecessor_lane) {
                     return predecessor_lane->turn() == hdmap::Lane::STRAIGHT;
                   });
  if (predecessor_lane_it == lane->predecessors().end()) {
    return lane->predecessors().front();
  }

  return *predecessor_lane_it;
}

int ComputeAgentConsistentCrossIntentionCountForVRU(
    const ReasoningObject& reasoning_object,
    const std::vector<planner::PredictedTrajectoryWrapper>&
        predicted_trajectories,
    const std::vector<speed::pb::OverlapRegion>& overlap_regions,
    const pb::AgentCrossIntentionTracker&
        previous_iter_agent_cross_intention_tracker,
    const int64_t current_timestamp) {
  const auto& previous_iter_object_id_to_agent_cross_intention_info_map =
      previous_iter_agent_cross_intention_tracker
          .object_id_to_agent_cross_intention_info_map();
  const auto agent_cross_intention_iter =
      previous_iter_object_id_to_agent_cross_intention_info_map.find(
          reasoning_object.id());
  if (DoesVRUHasCrossIntention(reasoning_object.planner_object(),
                               predicted_trajectories, overlap_regions)) {
    if (agent_cross_intention_iter ==
        previous_iter_object_id_to_agent_cross_intention_info_map.end()) {
      // This is the first time that the agent has cross intention after an
      // enough duration without cross intention.
      return 1;
    }

    // This is not the first time that the agent has cross intention. The agent
    // should have cross intention at certain not very old timestamp, so we
    // should increase the count.
    return agent_cross_intention_iter->second
               .consistent_cross_intention_count() +
           1;
  }

  if (ShouldLatchAgentConsistentCrossIntentionCount(
          reasoning_object, previous_iter_agent_cross_intention_tracker,
          current_timestamp)) {
    // At the current timestamp, the agent does not have cross intention and we
    // should latch the result if the latest timestamp having crossing timestamp
    // is not too old.
    DCHECK(agent_cross_intention_iter !=
           previous_iter_object_id_to_agent_cross_intention_info_map.end());
    return agent_cross_intention_iter->second
        .consistent_cross_intention_count();
  }

  // In the other case, the agent does not have cross intention and we should
  // not latch the historical result, so the count and the latest timestamp
  // having cross intention are set to zero.
  return 0;
}

bool IsSlowCutInAgent(const WorldContext& world_context,
                      const TrajectoryInfo& trajectory_info,
                      const ReasoningObject& reasoning_object,
                      const AgentTrajectoryInfo& agent_trajectory_info,
                      std::string* debug_string_ptr) {
  if (debug_string_ptr != nullptr) {
    absl::StrAppend(debug_string_ptr, " IsSlowCutInAgent debug:");
  }

  if (reasoning_object.object_type() != voy::perception::ObjectType::VEHICLE) {
    return false;
  }

  if (!trajectory_info.IsLaneKeep()) {
    if (debug_string_ptr != nullptr) {
      absl::StrAppend(debug_string_ptr, "NOT lane keep;");
    }

    return false;
  }

  if (reasoning_object.IsOnEgoPath()) {
    return false;
  }

  if (reasoning_object.IsBehindEgoLeadingBumper()) {
    return false;
  }

  // TODO(waylon): We determined this threshold by analyzing relevant cases, and
  // we can fine-tune it based on specific issues.
  constexpr double kMaxSpeedForSlowCutInAgent = 2.0;
  if (reasoning_object.planner_object().speed() > kMaxSpeedForSlowCutInAgent) {
    if (debug_string_ptr != nullptr) {
      absl::StrAppendFormat(debug_string_ptr,
                            " speed %.2f exceeds threshold %.2f;",
                            reasoning_object.planner_object().speed(),
                            kMaxSpeedForSlowCutInAgent);
    }

    return false;
  }

  if (reasoning_object.planner_object().speed() <
          prediction::constants::kMaxSpeedForVehicleEgoAwareSlowScenario &&
      !agent_trajectory_info.predicted_trajectory().MightStartToMoveSoon()) {
    return false;
  }

  const ObjectOccupancyParamInfo* object_occupancy_param =
      reasoning_object.object_occupancy_param_ptr();
  if (object_occupancy_param == nullptr) {
    return false;
  }

  // The width of single lane is 3.5 meters, and treat vehicles outside of one
  // lane as non-consideration objects.
  constexpr double kMaxAgentConsiderableLateralDistToLaneBoundary = 2.0;
  if (!reasoning_object.is_in_neighboring_lane() &&
      (object_occupancy_param->max_lane_encroachment_m <
       -kMaxAgentConsiderableLateralDistToLaneBoundary)) {
    // Skip if agents outside of neighboring lanes or exceed laterally
    // threshould.
    if (debug_string_ptr != nullptr) {
      absl::StrAppend(debug_string_ptr, "NOT proximity laterally;");
    }

    return false;
  }

  if (!agent_trajectory_info.EgoHasHigherRoadPrecedence()) {
    if (debug_string_ptr != nullptr) {
      absl::StrAppend(debug_string_ptr, " Agent has high precedence");
    }

    return false;
  }

  constexpr double kMaxArclengthForSharpCutIn = 10.0;
  const speed::pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();
  // NOTE: To enhance signal precision, we initially restrict cut-in scenes to
  // straight roads with short cut-in arclengths, similar to sharp turns into
  // Ego lane.
  const bool is_ego_going_straight_when_cut_in = std::any_of(
      agent_trajectory_info.overlap_region_infos().begin(),
      agent_trajectory_info.overlap_region_infos().end(),
      [&trajectory_info, &agent_trajectory_info, &object_proximity_info,
       &world_context](const OverlapRegionInfo& overlap_region_info) {
        double overlap_region_start_arclength =
            GetPaddedOverlapStart(agent_trajectory_info.overlap_region(
                overlap_region_info.overlap_region_ix));
        overlap_region_start_arclength += world_context.ra_to_leading_bumper();
        return overlap_region_info.is_cut_in &&
               trajectory_info.IsGoingStraight(
                   overlap_region_start_arclength) &&
               (overlap_region_start_arclength -
                    object_proximity_info.projected_ra_arc_length().start() <
                kMaxArclengthForSharpCutIn) &&
               !trajectory_info.IsInJunction(overlap_region_start_arclength);
      });

  if (debug_string_ptr != nullptr) {
    absl::StrAppendFormat(debug_string_ptr,
                          " is_ego_going_straight_when_cut_in %d;",
                          is_ego_going_straight_when_cut_in);
  }

  if (is_ego_going_straight_when_cut_in) {
    return true;
  }

  const bool is_agent_heading_toward_path =
      (object_occupancy_param->center_line_side == math::pb::kLeft &&
       object_occupancy_param->relative_heading_rad < (-M_PI / 30.0) &&
       object_occupancy_param->relative_heading_rad > -M_PI_2) ||
      (object_occupancy_param->center_line_side == math::pb::kRight &&
       object_occupancy_param->relative_heading_rad > (M_PI / 30.0) &&
       object_occupancy_param->relative_heading_rad < M_PI_2);
  if (debug_string_ptr != nullptr) {
    absl::StrAppendFormat(debug_string_ptr, " is_agent_heading_toward_path %d;",
                          is_agent_heading_toward_path);
  }

  if (is_agent_heading_toward_path) {
    return true;
  }

  return false;
}

bool ShouldAllowMaxSpeedForCyclist(const WorldContext& world_context,
                                   const ReasoningObject& reasoning_object) {
  constexpr double kShortestDurationSeeingEgoInSeconds = 2.0;
  constexpr double kShortestDurationEgoHasTrackedItInSeconds = 3.0;
  if (!reasoning_object.is_cyclist()) {
    return false;
  }

  if (world_context.EgoAlmostStopped()) {
    return false;
  }

  if (reasoning_object.was_yield()) {
    return false;
  }

  if (reasoning_object.IsOnEgoPath()) {
    return false;
  }

  // In order to disable max speed for cyclists running out of occlusion areas.
  if (reasoning_object.has_seen_ego_time() >
          -kShortestDurationSeeingEgoInSeconds ||
      reasoning_object.TrackedDurationInSeconds() <
          kShortestDurationEgoHasTrackedItInSeconds) {
    return false;
  }

  return true;
}

planner::pb::AssistManeuverPhase::Enum WaypointAssistPhase(
    const WorldContext& world_context) {
  // Attempt to retrieve the WaypointAssistTracker or
  // WaypointAssistTrackerSpeedData type from the variant
  const auto* waypoint_assist_tracker = std::get_if<WaypointAssistTracker>(
      &world_context.waypoint_assist_tracker());
  const auto* waypoint_assist_tracker_speed_data =
      std::get_if<WaypointAssistTrackerSpeedData>(
          &world_context.waypoint_assist_tracker());
  if (waypoint_assist_tracker != nullptr) {
    return waypoint_assist_tracker->waypoint_assist_phase();
  } else if (waypoint_assist_tracker_speed_data != nullptr) {
    return waypoint_assist_tracker_speed_data->waypoint_assist_phase();
  }

  return planner::pb::AssistManeuverPhase::kIdle;
}

bool ShouldAdjustARForPotentialNonreactiveAgent(
    const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info) {
  // currently we only apply mild ar for vehicles and cyclists when ART has
  // recalled. For pedestrain we still disable AR.
  if (!reasoning_object.is_vehicle_or_cyclist()) {
    return false;
  }
  const std::optional<ARTObjectInfo>& art_object_info =
      reasoning_object.art_object_info();
  return agent_trajectory_info.agent_reaction_tracker_result() ==
             pb::AgentReactionTrackerResult::NONREACTIVE &&
         art_object_info.has_value() &&
         art_object_info.value().latest_distance_gap.has_value() &&
         art_object_info.value().latest_distance_gap.value() < 0;
}

double EstimateEgoShowIntentionTime(const double x_target_position,
                                    const double v_0, const double accel,
                                    const double brake, const double v_max) {
  DCHECK_GE(x_target_position, 0);
  DCHECK_GT(accel, 0);
  DCHECK_GE(v_0, 0);
  DCHECK_LT(brake, 0);
  if (v_0 >= v_max) {
    // if ego is already beyond speed limit,(which is almost unlikely), then ego
    // won't speed up anymore.
    return (x_target_position + v_0 * v_0 / (2 * brake)) / v_0;
  }
  // The motion is accelerate for t and then hard brake to stop, and the total
  // displacement is x_target_position.
  // So the equation is:
  // v_0 * t + 0.5 * accel * t ^ 2 - (v_0 + accel * t) ^ 2 / (2 * brake) =
  // x_target_position, and we need to solve the t here.
  // We can rearrange this equation to a quadratic equation:
  // Quadratic coefficient is (0.5 * accel - accel ^ 2 / (2 * brake)).
  // Linear coefficient:(v_0 - v_0 * accel /brake).
  // Constant coefficient: (-v_0 ^ 2 /(2 * brake) - x_target_position).
  const double c_coeff = -v_0 * v_0 / (2 * brake) - x_target_position;
  if (c_coeff >= 0) {
    // If ego needs to yield the agent then ego must brake immediately, so ego
    // has already show intention.
    return 0.0;
  }
  const double a_coeff = 0.5 * accel - accel * accel / (2 * brake);
  const double b_coeff = v_0 - v_0 * accel / brake;
  const double t =
      (-b_coeff + std::sqrt(b_coeff * b_coeff - 4 * a_coeff * c_coeff)) /
      (2 * a_coeff);
  if (v_0 + accel * t > v_max) {
    // the total motion has three parts:
    // 1. constant acceleration motion until the v reaches upper bound.
    // 2. constant speed motion with v_max
    // 3. hard brake to stop
    const double t_1 = (v_max - v_0) / accel;
    const double x_1 = v_0 * t_1 + 0.5 * accel * t_1 * t_1;
    const double x_3 = -v_max * v_max / (2 * brake);
    const double t_2 = (x_target_position - x_1 - x_3) / v_max;
    return t_1 + t_2;
  }
  return t;
}

std::pair<double, double> EstimatedEgoShowIntentionTime(
    const TrajectoryInfo& trajectory_info,
    const pb::OverlapRegion& overlap_region, const double ego_speed) {
  double comfort_ego_show_intention_time =
      -std::numeric_limits<double>::infinity();
  double uncomfort_ego_show_intention_time =
      -std::numeric_limits<double>::infinity();
  const double target_position =
      overlap_region.contain_strict_overlap()
          ? overlap_region.ego_moving_distance_strict().start()
          : overlap_region.ego_moving_distance_padded().start();
  DCHECK(!std::isinf(target_position));
  if (target_position <= 0) {
    comfort_ego_show_intention_time = 0;
    uncomfort_ego_show_intention_time = 0;
  } else {
    const double road_speed_limit =
        trajectory_info.GetEgoMaxSpeedAtArclength(target_position);
    comfort_ego_show_intention_time = EstimateEgoShowIntentionTime(
        /*x=*/target_position, /*v_0=*/ego_speed,
        /*accel=*/kComfortEsitimatedEgoAccelInMpss,
        /*brake=*/kHardBrakeAccelInMpss, /*v_max=*/road_speed_limit);
    uncomfort_ego_show_intention_time = EstimateEgoShowIntentionTime(
        /*x=*/target_position, /*v_0=*/ego_speed,
        /*accel=*/kUncomfortEsitimatedEgoAccelInMpss,
        /*brake=*/kHardBrakeAccelInMpss, /*v_max=*/road_speed_limit);
    DCHECK_GE(comfort_ego_show_intention_time,
              uncomfort_ego_show_intention_time);
  }
  return {comfort_ego_show_intention_time, uncomfort_ego_show_intention_time};
}

bool ShouldConsiderEgoShowIntentionTime(
    const ReasoningObject& reasoning_object,
    const OverlapRegionInfo& overlap_region_info) {
  if (reasoning_object.is_vehicle()) {
    return ShouldConsiderEgoShowIntentionTimeForVehicle(reasoning_object,
                                                        overlap_region_info);
  }
  if (reasoning_object.is_pedestrian_or_cyclist()) {
    return ShouldConsiderEgoShowIntentionTimeForVRU(reasoning_object,
                                                    overlap_region_info);
  }
  return false;
}

std::vector<planner::pb::MapElementInfo> GetAgentOccupiedMapElements(
    const ReasoningObject& reasoning_object,
    const planner::pb::MapElementType::Enum map_element_type) {
  std::vector<planner::pb::MapElementInfo> occupied_map_elements;
  const planner::pb::AgentOccupiedMapElementInfos*
      agent_occupied_map_element_info_ptr =
          reasoning_object.agent_occupied_map_element_infos_in_seed_ptr();
  if (agent_occupied_map_element_info_ptr == nullptr) {
    return occupied_map_elements;
  }
  std::for_each(
      agent_occupied_map_element_info_ptr->occupied_map_elements().begin(),
      agent_occupied_map_element_info_ptr->occupied_map_elements().end(),
      [&occupied_map_elements,
       &map_element_type](const planner::pb::MapElementInfo& map_element_info) {
        if (map_element_info.element_type() == map_element_type) {
          occupied_map_elements.emplace_back(map_element_info);
        }
      });
  return occupied_map_elements;
}

std::vector<route_association::MapElementAndPoseInfo>
GetAgentOccupiedMapElements(
    const std::vector<PredictedTrajectoryWrapper>& predicted_trajectories,
    const planner::pb::MapElementType::Enum map_element_type) {
  std::vector<route_association::MapElementAndPoseInfo> occupied_map_elements;
  for (const PredictedTrajectoryWrapper& predicted_trajectory :
       predicted_trajectories) {
    std::for_each(
        predicted_trajectory.current_occupied_map_elements().begin(),
        predicted_trajectory.current_occupied_map_elements().end(),
        [&occupied_map_elements, &map_element_type](
            const route_association::MapElementAndPoseInfo& map_element_info) {
          if (map_element_info.type == map_element_type) {
            occupied_map_elements.emplace_back(map_element_info);
          }
        });
  }
  return occupied_map_elements;
}

bool IsUturnTrajectory(const AgentTrajectoryInfo& agent_trajectory_info) {
  if (!agent_trajectory_info.associated_route_opt().has_value()) {
    // if the agent trajectory info doesn't have lane association result,
    // then the two signals is default false.
    DCHECK(!agent_trajectory_info.is_on_u_turn_lane() &&
           !agent_trajectory_info.is_crossing_opposite_road_blocks());
    return false;
  }
  const std::vector<route_association::MapElementAndPoseInfo>&
      agent_associated_route =
          agent_trajectory_info.associated_route_opt().value();
  if (agent_trajectory_info.is_on_u_turn_lane()) {
    return true;
  }
  const route_association::MapElementAndPoseInfo& curr_map_ele =
      agent_associated_route.front();
  // If the agent is u turning in the middle of the road, its relative
  // heading with its current lane is within 90 degrees. If it's relative
  // heading with its current lane
  // is larger than 90 degrees, then it has finished the first half of
  // the u turn and becomes a normal crossing agents.
  if (agent_trajectory_info.is_crossing_opposite_road_blocks() &&
      curr_map_ele.lane_ptr != nullptr &&
      curr_map_ele.heading_diff_opt.has_value() &&
      std::abs(curr_map_ele.heading_diff_opt.value()) < M_PI_2) {
    return true;
  }
  return false;
}

bool IsLowPrecedenceSlowCutInVehicleInFrontOfEgo(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info,
    std::string* debug_string_ptr) {
  DCHECK(debug_string_ptr != nullptr);
  absl::StrAppend(debug_string_ptr,
                  " \n IsLowPrecedenceSlowCutInVehicleInFrontOfEgo debug:");

  if (!reasoning_object.is_vehicle()) {
    return false;
  }

  if (agent_trajectory_info.road_precedence() !=
      planner::pb::RoadPrecedence::LOWER) {
    absl::StrAppend(debug_string_ptr, " NOT lower precedence.");
    return false;
  }

  if (reasoning_object.IsBehindEgoFrontAxle()) {
    return false;
  }

  if (!IsSlowCutInAgent(world_context, trajectory_info, reasoning_object,
                        agent_trajectory_info, debug_string_ptr)) {
    absl::StrAppend(debug_string_ptr, " NOT slow-cut-in agent.");

    return false;
  }

  // TODO(waylon): Integrate with ART.
  constexpr double kMaxAccelForExtraCautiousInMpss = 0.5;
  if (reasoning_object.acceleration() > kMaxAccelForExtraCautiousInMpss) {
    absl::StrAppendFormat(
        debug_string_ptr, " accel %.2f exceeds threshold %.2f;",
        reasoning_object.acceleration(), kMaxAccelForExtraCautiousInMpss);

    return false;
  }

  const ObjectOccupancyParamInfo* object_occupancy_param =
      reasoning_object.object_occupancy_param_ptr();
  if (object_occupancy_param == nullptr) {
    return false;
  }

  constexpr double kMaxEncroachmentDistForExtraCautiousInMeter = 0.3;
  if (object_occupancy_param->max_lane_encroachment_m >
      kMaxEncroachmentDistForExtraCautiousInMeter) {
    absl::StrAppendFormat(debug_string_ptr,
                          " lane_encroachment %.2f exceeds threshold %.2f;",
                          object_occupancy_param->max_lane_encroachment_m,
                          kMaxEncroachmentDistForExtraCautiousInMeter);

    // Agent has encrochment ego lane more and is NOT likely to yield Ego.
    return false;
  }

  // Use agent speed and heading to identify and filter out aggressive agents.
  // Our guiding principle is that extra cautious agents require slower speeds
  // as the relative heading diff with Ego increases. As an example, if an agent
  // is fully aligned with Ego, the speed threshold for an extra cautious agent
  // can be higher.
  constexpr double kMaxSpeedForExtraCautiousAgentInSameDirectionInMps = 1.5;
  constexpr double kMaxSpeedForExtraCautiousAgentInLargeAngleInMps = 1.0;
  const double agent_relative_heading_rad_to_center_line =
      object_occupancy_param->relative_heading_rad;
  const double max_speed_for_agent = math::GetLinearInterpolatedY(
      /*x1=*/0.0,
      /*x2=*/M_PI_4,
      /*y1=*/kMaxSpeedForExtraCautiousAgentInSameDirectionInMps,
      /*y2=*/kMaxSpeedForExtraCautiousAgentInLargeAngleInMps,
      /*x_eval=*/std::abs(agent_relative_heading_rad_to_center_line),
      /*allow_extension=*/false);

  const bool is_agent_extra_cautious_for_slow_cut_in_in_front =
      reasoning_object.speed() < max_speed_for_agent;
  absl::StrAppendFormat(debug_string_ptr,
                        " speed %.2f, max_speed_for_agent %.2f, "
                        "is_agent_extra_cautious_for_slow_cut_in_in_front %d;",
                        reasoning_object.speed(), max_speed_for_agent,
                        is_agent_extra_cautious_for_slow_cut_in_in_front);

  return is_agent_extra_cautious_for_slow_cut_in_in_front;
}

bool IsTrafficLightJustTurnedGreen(
    const traffic_rules::TrafficLightInfo& traffic_light_info,
    const int64_t elapsed_time_thresh) {
  return traffic_light_info.is_pre_state_red() &&
         traffic_light_info.is_green() &&
         traffic_light_info.GetColorDuration() < elapsed_time_thresh;
}

// This function doesn't double check whether ego is in upl and has lower road
// precedence, so make sure to call this function when ego is in upl. This
// function is more like a helper function, the reason to store it in the
// reasoning util is both UrgencyInfo & AgentReactionStrategy share this
// function.
bool ShouldBeCautiousDuringUPL(const WorldContext& world_context,
                               const TrajectoryInfo& trajectory_info,
                               const ReasoningObject& reasoning_object) {
  constexpr int64_t kUPLBeingCautiousTimeAfterLightTurnsGreenInMs = 1500;
  constexpr double kMinSpeedToRushDuringUPLInMps = math::KmphToMps(6);
  const traffic_rules::TrafficLightInfo* first_traffic_light =
      GetFirstTrafficLightAhead(trajectory_info.traffic_rules().traffic_lights);
  if (first_traffic_light != nullptr &&
      IsTrafficLightJustTurnedGreen(
          *first_traffic_light,
          kUPLBeingCautiousTimeAfterLightTurnsGreenInMs) &&
      world_context.ego_speed() < kMinSpeedToRushDuringUPLInMps &&
      !reasoning_object.IsStationary()) {
    rt_event::PostRtEvent<rt_event::planner::AvoidRushingInUPL>();
    return true;
  }
  return false;
}

bool ShouldConsiderMildAR(const WorldContext& world_context,
                          const ReasoningObject& reasoning_object,
                          const TrajectoryInfo& trajectory_info,
                          const AgentTrajectoryInfo& agent_trajectory_info,
                          const OverlapRegionInfo& overlap_region_info) {
  // Do not apply Mild AR if the agent is a delivery robot. Since it may not
  // actively respond to ego.
  if (reasoning_object.planner_object().is_delivery_minicar()) {
    return false;
  }
  if (!reasoning_object.is_vehicle_or_cyclist()) {
    // We haven't handled other object types.
    return false;
  }
  // Don't rush when ego is in upl and the traffic light just turns green.
  if (overlap_region_info.is_in_unprotected_left_turn &&
      ShouldBeCautiousDuringUPL(world_context, trajectory_info,
                                reasoning_object)) {
    return false;
  }
  if (reasoning_object.is_cyclist()) {
    // Do not apply Mild AR if the cyclist has a high acceleration.
    if (reasoning_object.filtered_acceleration() >
        kMaxAccelToConsiderCyclistMildARInMpss) {
      return false;
    }

    // Do not apply Mild AR if the cyclist is in front of the ego vehicle and
    // moving in the same direction, as they may not be aware of the ego
    // vehicle.
    if (agent_trajectory_info.is_overtaken_by_ego()) {
      return false;
    }
  }
  if (reasoning_object.is_vehicle()) {
    // Do not apply Mild AR to stationary agents as truely stationary agent
    // should
    // have no overlap with ego and for STM agents should be considered with the
    // scenario scope as different logic.
    if (reasoning_object.IsStationary()) {
      return false;
    }
    // For UPL scenarios we will not apply Mild AR to agents starting to move or
    // accelerating at low speed, since start to move or accelerating agents are
    // less likely to yield to ego, and apply mild AR to low speed agent may
    // have inaccurate significantly impact.
    bool is_agent_low_speed_accelerating =
        reasoning_object.speed() <=
            kAgentMinVelocityInUPLToConsiderMildARInMps &&
        reasoning_object.filtered_acceleration() >
            kUPLAgentStartToMoveAccelLimitInMpss;
    // Update: previously we was using trajectory_info.IsInUnprotectedLeftTurn()
    // to determine UPL scenarios, this could sometimes lead to incorrect
    // behavior: If the ego vehicle was still in the straight lane (not yet in
    // the UPL intersection), we wouldn't consider agent interactions under UPL
    // conditions. This could result in the apply Mild AR to low-speed start to
    // move agents, potentially leading to aggressive ego behavior and increased
    // risk. To improve accuracy, we now use the overlap region to determine if
    // the interaction occurs within the UPL scenario.
    if (overlap_region_info.is_in_unprotected_left_turn &&
        (is_agent_low_speed_accelerating ||
         reasoning_object.filtered_acceleration() >
             kUPLAgentMaxAccelToConsiderMildARInMpss)) {
      return false;
    }
  }
  return true;
}

}  // namespace reasoning_util
}  // namespace speed
}  // namespace planner
