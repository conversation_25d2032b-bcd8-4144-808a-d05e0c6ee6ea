#ifndef VEHICLE_SIGNAL_INPUT_VEHICLE_SIGNAL_INPUT_PARAMETER_H_
#define VEHICLE_SIGNAL_INPUT_VEHICLE_SIGNAL_INPUT_PARAMETER_H_

#include "vehicle_signal_input/vehicle_signal_input_parameter.h"

#include <stdbool.h>

// choose yaw rate from IMU
const volatile bool kVsiChooseYawRateFromIMU = true;

// fallback active switch on, just for test or debug
const volatile bool kVsiFallbackActiveSwitchOn = false;

// fallback active can timeout threshold
const volatile float kVsiKeyCanTimeoutThreshold = 0.5F;  // seconds

// can timeout threshold
const volatile float kVsiCanTimeoutThreshold = 5.0F;  // seconds

// emergency switch on hold time
const volatile float kVsiEmergencySwitchOnHoldTime = 600.0F;  // seconds

// collision detection timeout threshold
const volatile float kVsiCollisionDetectionTimeoutThreshold = 0.5F;  // seconds

// Fallback takeover delay time, seconds.
const volatile float kVsiFallbackTakeoverDelayTime = 0.15F;  // seconds

// parameter interface
bool GetVsiChooseYawRateFromIMU() { return kVsiChooseYawRateFromIMU; }

bool GetVsiFallbackActiveSwitchOn() { return kVsiFallbackActiveSwitchOn; }

float GetVsiKeyCanTimeoutThreshold() { return kVsiKeyCanTimeoutThreshold; }

float GetVsiCanTimeoutThreshold() { return kVsiCanTimeoutThreshold; }

float GetVsiEmergencySwitchOnHoldTime() {
  return kVsiEmergencySwitchOnHoldTime;
}

float GetVsiCollisionDetectionTimeoutThreshold() {
  return kVsiCollisionDetectionTimeoutThreshold;
}

float GetVsiFallbackTakeoverDelayTime() {
  return kVsiFallbackTakeoverDelayTime;
}

#endif  // VEHICLE_SIGNAL_INPUT_VEHICLE_SIGNAL_INPUT_PARAMETER_H_
