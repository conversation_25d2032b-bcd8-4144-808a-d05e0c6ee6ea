#ifndef ONBOARD_PLANNER_OPS_WARNING_OPS_WARNING_UTILITY_H_
#define ONBOARD_PLANNER_OPS_WARNING_OPS_WARNING_UTILITY_H_

#include <unordered_map>

#include "planner/behavior/types/directive.h"
#include "planner/world_model/planner_object/planner_object.h"
#include "voy_protos/pose.pb.h"
#include "voy_protos/trajectory.pb.h"

namespace planner {
namespace ops_warning {

// Evaluates a given trajectory by checking for collisions with provided traffic
// participants. It takes the following steps:
// 1. samples the poses of trajectory based on a fixed deceleration
// 2. samples the poses of traffic participants based on a fixed deceleration
// 3. checks whether the sampled poses will collide with the robot. If there is
// a collision then return true.
bool CheckRearEnding(
    const voy::Pose& curr_pose,
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const pb::Trajectory& trajectory);

// Checks whether the ego vehicle is near road boundary.
bool IsNearRoadBoundary(
    const planner::LaneGeometryDirective& lane_geometry_directive,
    const math::geometry::OrientedBox2d& ego_bounding_box);

}  // namespace ops_warning
}  // namespace planner

#endif  // ONBOARD_PLANNER_OPS_WARNING_OPS_WARNING_UTILITY_H_
