#ifndef ONBOARD_PLANNER_OPEN_SPACE_UNSTUCK_PLANNER_BASE_UNSTUCK_PLANNER_H_
#define ONBOARD_PLANNER_OPEN_SPACE_UNSTUCK_PLANNER_BASE_UNSTUCK_PLANNER_H_

#include <algorithm>
#include <vector>

#include "math/math_util.h"
#include "onboard/planner/open_space/constraints/motion_plan_problem_manager.h"
#include "planner/behavior/util/lane_common/lane_sequence_result_definition.h"
#include "planner/behavior/util/lane_sequence_geometry/lane_sequence_geometry.h"
#include "planner/selection/trajectory_meta.h"
#include "planner/world_model/speed_world_model.h"
#include "planner_protos/behavior_reasoner_seed.pb.h"
#include "planner_protos/unstuck_planner_seed.pb.h"

namespace planner {

namespace unstuck {

// Class BaseUnstuckPlanner defines the base virtual class for all
// scenario-based UnstuckPlanners (U-Turn, PullOut, etc.) to inherit and derive
// scenario-specific unstuck handling motion plans. In this base class, we
// define the core unstuck motion plan generation pipeline and state transition
// during the unstuck progress.
class BaseUnstuckPlanner {
 public:
  BaseUnstuckPlanner(
      const SpeedWorldModel& world_model,
      const LaneSequenceResult& lane_sequence_result,
      const lane_selection::LaneSequenceGeometry& lane_sequence_geometry,
      const std::vector<selection::TrajectoryMetaData*>& trajectory_candidates,
      const pb::UnstuckPlannerSeed& prev_seed, pb::MotionMode motion_mode,
      pb::UnstuckPlannerSeed* curr_seed,
      std::optional<double>* dry_steering_angle)
      : world_model_(world_model),
        lane_sequence_result_(lane_sequence_result),
        lane_sequence_geometry_(lane_sequence_geometry),
        motion_plan_problem_manager_(world_model.robot_state()),
        trajectory_candidates_(trajectory_candidates),
        motion_mode_(motion_mode),
        prev_seed_(prev_seed),
        curr_seed_(curr_seed),
        dry_steering_angle_ptr_(dry_steering_angle) {
    DCHECK_NE(curr_seed_, nullptr);
    DCHECK_NE(dry_steering_angle_ptr_, nullptr);
    // Intialize the current cycle seed from the prev cycle.
    InitializeCurrentSeed();
  }

  virtual ~BaseUnstuckPlanner() {}

  // Updates the UnstuckPlanner's state following the transition logic
  // described in https://cooper.didichuxing.com/docs2/document/2202299026285
  // and updates the unstuck motion plan as appropriate.
  void Update();

  // Accessor of unstuck planner state in the current-cycle seed.
  pb::UnstuckPlannerState state() const { return curr_seed().current_state(); }

 protected:
  const SpeedWorldModel& world_model() const { return world_model_; }

  const LaneSequenceResult& lane_sequence_result() const {
    return lane_sequence_result_;
  }

  const lane_selection::LaneSequenceGeometry& lane_sequence_geometry() const {
    return lane_sequence_geometry_;
  }

  // Returns true if ego has fully stopped.
  bool HasEgoFullyStopped() const {
    return world_model_.robot_state().current_state_snapshot().speed() <
           constants::kDefaultEgoNearStaticSpeedInMps;
  }

  // Accessor of the previous unstuck planner seed.
  const pb::UnstuckPlannerSeed& prev_seed() const { return prev_seed_; }
  // Accessor and mutator of the current unstuck planner seed.
  const pb::UnstuckPlannerSeed& curr_seed() const {
    return *DCHECK_NOTNULL(curr_seed_);
  }
  pb::UnstuckPlannerSeed& mutable_curr_seed() {
    return *DCHECK_NOTNULL(curr_seed_);
  }

  // Accessors and mutators of the current plan and in-progress motion segment.
  const pb::MotionPlan& motion_plan() const {
    // When accessing the current motion plan, guard that the plan is non-empty,
    // and the current motion segment idx is valid.
    DCHECK(curr_seed().has_current_plan());
    DCHECK_GT(curr_seed().current_plan().motion_segments().size(), 0);
    DCHECK_LE(curr_seed().current_plan().current_segment_idx(),
              curr_seed().current_plan().motion_segments().size());

    return curr_seed().current_plan();
  }
  open_space::MotionPlanProblemManager& motion_plan_problem_manager() {
    return motion_plan_problem_manager_;
  }
  pb::MotionPlan& mutable_motion_plan() {
    return *DCHECK_NOTNULL(mutable_curr_seed().mutable_current_plan());
  }
  const ::google::protobuf::RepeatedPtrField<pb::MotionSegment>&
  motion_segments() const {
    return motion_plan().motion_segments();
  }
  int current_motion_segment_idx() const {
    return motion_plan().current_segment_idx();
  }
  pb::MotionSegmentExecutionStatus current_motion_segment_execution_status()
      const {
    DCHECK_LT(current_motion_segment_idx(), motion_segments().size());
    return motion_plan().current_segment_execution_status();
  }
  const pb::MotionSegment& current_motion_segment() const {
    DCHECK_LT(current_motion_segment_idx(), motion_segments().size());
    return motion_segments().Get(current_motion_segment_idx());
  }
  bool is_current_motion_segment_pure_dry_steering() const {
    return !current_motion_segment().has_reference_curve();
  }

  const std::vector<selection::TrajectoryMetaData*>& trajectory_candidates()
      const {
    return trajectory_candidates_;
  }

  // Returns true if, during execution of the current motion plan, some replan
  // conditions are met to update the motion plan WITHOUT quitting the unstuck
  // planner execution and a new motion plan is successfully generated and
  // updated to the unstuck planner seed.
  virtual bool CheckReplanConditionAndUpdateMotionPlan() { return false; }

  // Returns true if the current motion plan is constantly stuck for longer than
  // the input time out threshold in ms. If ego never reach the unstuck speed
  // threshold, use starting_stuck_time_out_thresh_ms as the time out thresh
  // to prevent ego stuck at the begin of motion plan. Start normal timer after
  // the ego first time reach the unstuck speed threshold.
  // TODO(Chester, Zixuan): Refactor this function to make its more clear, like
  // users could simply input the required threshold and return whether ego
  // get stuck.
  bool IsMotionPlanStuckAndTimeOut(int64_t stuck_timeout_thresh_ms,
                                   int64_t stuck_at_starting_timeout_thresh_ms);

  // Returns true if a replan is needed and succeeds after the current motion
  // plan is aborted.
  virtual bool ReplanAfterAbort() { return false; }

 private:
  // Initialize current cycle seed from the previous cycle seed.
  void InitializeCurrentSeed();

  // UnstuckPlanner logic at IDLE state. Checks if the UnstuckPlanner should be
  // triggered and returns the transferred motion planner's state from IDLE.
  // IDLE -> EXECUTING if unstuck behavior is triggered and a motion plan is
  // successfully proposed.
  // IDLE -> IDLE otherwise.
  // NOTE: Logic not implemented in this base class. Exact logic to be
  // determined by each derived scenario-specific UnstuckPlanners.
  virtual pb::UnstuckPlannerState
  CheckTriggerConditionAndProposeMotionPlan() = 0;

  // Returns true if any exception is detected and the current plan is no longer
  // valid, indicating that the current plan should be aborted from execution.
  virtual bool ShouldAbortMotionPlan() = 0;

  // Resets scenario specific unstuck planner states and data with resetting
  // logic is aligned with base unstuck planner state transition.
  virtual void ResetScenarioSpecificStatesAndData() {}

  // Returns true if the general check of early termination condition satisfied.
  // E.g. there's at least one structured driving trajectory stably unstuck.
  bool IsEarlyTerminationPrerequisiteSatisfied();

  // Returns true if the scenario specific early termination condition
  // satisfied, it's the necessary condition for triggering early termination.
  virtual bool CheckScenarioSpecificEarlyTerminationCondition() = 0;

  // Returns true if the current motion plan has not yet been completed but the
  // remaining execution is considered unnecessary, such that the motion plan
  // should be early terminated.
  bool ShouldEarlyTerminateMotionPlan() {
    return IsEarlyTerminationPrerequisiteSatisfied() &&
           CheckScenarioSpecificEarlyTerminationCondition();
  }

  // Returns true if the lowest-cost structured driving trajectory candidates
  // have been stably unstuck for multiple cycles.
  bool IsStructuredDrivingStablyUnstuck();

  // UnstuckPlanner logic at EXECUTING state. Returns the transferred motion
  // planner's state from EXECUTING.
  // EXECUTING -> IDLE if the current plan is completed.
  // EXECUTING -> ABORTING if the current plan should be aborted.
  // EXECUTING -> EXECUTING if continue to execute the current plan.
  pb::UnstuckPlannerState ExecuteMotionPlan();

  // The lower-level state transition of excuting a motion segment. Updates the
  // internal execution status transfer of motion segment execution and updates
  // the motion plan's tracking index of in-progress motion segment once a
  // segment is completed.
  void ExecuteMotionSegment();

  // Motion segment execution logic at NO_EXECUTION status, which indicates the
  // first cycle the current motion segment idx has turned to this segment.
  // Returns the transferred execution status from NO_EXECUTION, and initiates
  // all required trigger info for executing this segment.
  // NO_EXECUTION -> DRY_STEER once initialization is done.
  pb::MotionSegmentExecutionStatus InitializeMotionSegmentExecution();

  // Motion segment execution logic at DRY_STEER status.
  // Returns the transferred execution status from DRY_STEER, and sets the
  // target dry steering angle if needed.
  // DRY_STEER -> DRY_STEER if dry steering has not finished or the current
  // motion segment is pure dry steering.
  // DRY_STEER -> MOTION_IN_PROGRESS otherwise.
  pb::MotionSegmentExecutionStatus ExecuteMotionSegmentDrySteering();

  // UnstuckPlanner logic at ABORTING state. Returns the transferred motion
  // planner's state from ABORTING.
  // ABORTING -> ABORTING if ego has not fully stopped.
  // ABORTING -> EXECUTING if a replan is needed.
  // ABORTING -> IDLE otherwise.
  pb::UnstuckPlannerState AbortMotionPlan();

  // Returns true if EGO reaches the end of motion segment.
  bool DoesEgoReachEndOfCurrentMotionSegment() const;

  // Returns true if the current motion segment has been completed.
  bool IsCurrentMotionSegmentCompleted() const;

  // Returns true if the current motion plan has been completed (or should be
  // completed).
  bool IsMotionPlanCompleted() {
    return motion_segments().size() == current_motion_segment_idx() ||
           ShouldEarlyTerminateMotionPlan();
  }

  // Returns true if ego's current steering angle has reached the current motion
  // segment's dry steering angle.
  bool IsDrySteeringCompleted() const;

  // Resets all unstuck planner seeded states, motion plan together with
  // scenario specific unstuck planner states.
  void Reset() {
    // Reset fundamental unstuck planner states and seeded data.
    mutable_curr_seed().set_unstuck_scene_type(pb::UnstuckSceneType::NOT_STUCK);
    mutable_curr_seed().set_current_state(pb::UnstuckPlannerState::IDLE);
    // NOTE(Jiakai): The previous_state should NOT be set, otherwise, it will
    // lose track of the previous state at the frame at which the motion plan is
    // finished or aborted. And the motion mode will NOT be set correctly.
    mutable_curr_seed().clear_current_plan();
    mutable_curr_seed().set_structured_driving_consecutive_unstuck_cyc_cnt(0);

    // Reset scenario specific unstuck planner states that should be reset along
    // with the unstuck planner state transition.
    ResetScenarioSpecificStatesAndData();

    // Reset target dry steering angle to ensure no output is generated from the
    // unstuck planner.
    *dry_steering_angle_ptr_ = std::nullopt;
  }

  const SpeedWorldModel& world_model_;
  [[maybe_unused]] const LaneSequenceResult& lane_sequence_result_;
  [[maybe_unused]] const lane_selection::LaneSequenceGeometry&
      lane_sequence_geometry_;

  // The motion plan problem manager for populating the motion plan problem
  // required for motion plan generation.
  // TODO(Zixuan, Liwen, Jiakai): Migrate existing motion plan generation
  // algorithm to using the motion plan problem manager API.
  open_space::MotionPlanProblemManager motion_plan_problem_manager_;

  // All trajectory candidates, both structured driving and open-space, with
  // selection cost already populated.
  const std::vector<selection::TrajectoryMetaData*>& trajectory_candidates_;

  // The current-cycle trajectory's motion mode.
  [[maybe_unused]] pb::MotionMode motion_mode_ = pb::MotionMode::FORWARD;

  // Const reference to the previous unstuck planner seed.
  const pb::UnstuckPlannerSeed& prev_seed_;
  // Pointer to the current unstuck planner seed, which is part of the
  // current-cycle DecoupledManeuverSeed. Its seeded unstuck planner state and
  // motion plan will be updated by the scenario-based UnstuckPlanner invoked in
  // current-cycle.
  pb::UnstuckPlannerSeed* curr_seed_ = nullptr;

  // Pointer to the selected trajectory meta data's target dry steering angle.
  // This value is mutated if the unstuck planner's state is in EXECUTING, and
  // the currently in-progress motion segment is in dry steering preparation
  // stage.
  std::optional<double>* dry_steering_angle_ptr_ = nullptr;

  // The flag is true if the lowest-cost structured driving trajectory candidate
  // has been stably unstuck for multiple cycles.
  bool is_structured_driving_stably_unstuck_ = false;
};

}  // namespace unstuck

}  // namespace planner

#endif  // ONBOARD_PLANNER_OPEN_SPACE_UNSTUCK_PLANNER_BASE_UNSTUCK_PLANNER_H_
