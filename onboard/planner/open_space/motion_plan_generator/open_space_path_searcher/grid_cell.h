#ifndef ONBOARD_PLANNER_OPEN_SPACE_MOTION_PLAN_GENERATOR_OPEN_SPACE_PATH_SEARCHER_GRID_CELL_H_
#define ONBOARD_PLANNER_OPEN_SPACE_MOTION_PLAN_GENERATOR_OPEN_SPACE_PATH_SEARCHER_GRID_CELL_H_

#include <algorithm>
#include <limits>
#include <memory>
#include <utility>
#include <vector>

#include <gtest/gtest.h>

#include "math/interpolation.h"
#include "math/range.h"
#include "planner/open_space/motion_plan_generator/open_space_path_searcher/grid_util.h"
#include "planner/open_space/motion_plan_generator/open_space_path_searcher/open_space_node.h"

namespace planner {

namespace open_space {

// Struct GridCellVertex defines an xy sample point within the grid area
// together with the associated OccupancyClearanceInfo and grid area heuristic
// distance to target pose.
struct GridCellVertex {
  GridCellVertex() = default;
  GridCellVertex(double x_in, double y_in, int x_idx_in, int y_idx_in)
      : key(x_idx_in, y_idx_in), xy_point(x_in, y_in) {
    DCHECK(!key.IsNullKey());
  }

  GridKey key;
  math::geometry::Point2d xy_point;

  // Boolean flag indicating whether this vertex has been occupied by obstacle
  // contours or on the forbidden side of open space boundaries.
  bool is_available = true;

  // The closest/dominant clearance to an occupancy constraint at this sampled
  // xy point that effectively determines the availability of this vertex.
  OccupancyClearanceInfo dominant_clearance_info;
  // The map of constraint ids to clearance infos for all occupancies within the
  // motion plan problem.
  // NOTE: The map data is only valid when the vectex's position is available.
  OccupancyClearanceInfoMap clearance_info_map;

  // The non-holonomic heuristic distance to target pose.
  double heuristic_dist_to_target = kMaxOpenSpaceDistanceInMeter;
  // Flag for heuristic dp search. Only used for heuristic distance to target of
  // a grid vertex during OpenSpaceGrid construction.
  bool is_closed_for_heuristic_dp = false;

  // Const accessors.
  double x() const { return xy_point.x(); }
  double y() const { return xy_point.y(); }
  bool is_drivable() const { return dominant_clearance_info.is_drivable; }
  double dominant_clearance() const {
    return dominant_clearance_info.clearance;
  }
};

// Struct HeadingSearchSpaceInterval defines a sampled range of heading value
// that discretizes the continuous heading value range and stores an explored
// search node with its heading falling into this range.
struct HeadingSearchSpaceInterval {
  int interval_idx = 0;
  math::Range1d heading_range{};
  OpenSpaceNode searched_node;
};

// Struct DiscretizedHeadingSearchSpace defines a sequence of consecutive
// SearchedNodeHeadingSlots sampled from the overall heading value range from
// -pi to pi.
struct DiscretizedHeadingSearchSpace {
  DiscretizedHeadingSearchSpace(int x_idx_in, int y_idx_in,
                                double heading_res_rad_in);

  math::Range1d overall_heading_range;
  double heading_res_rad = 0.0;
  std::vector<HeadingSearchSpaceInterval> heading_intervals;

  // Returns the index of the heading interval that contains the input heading.
  int GetIntervalIndex(double heading_rad) const;
};

// Class GridCell defines one of the elements consisting four neighbouring
// GridCellVertices in the OpenSpace::Grid.
//     __________________________________    Grid
//     |  .    .    .    .    .    .    |
//     |  .    .    .    .    .    .    |    x  x   GridCell with 4
//     |  .    .    .    .    .    .    |    x  x   bounding vertices
//     |  .    .    x    x    .    .    |
//     |  .    .    x    x    .    .    |    .      GridCellVertex
//     |  .    .    .    .    .    .    |
//     |  .    .    .    .    .    .    |
//     |  .    .    .    .    .    .    |
//     |  .    .    .    .    .    .    |
//     |  .    .    .    .    .    .    |
//     |________________________________|
// y ^
//   |__> x
class GridCell {
 public:
  GridCell() = default;
  GridCell(GridCellVertex* lower_left_in, GridCellVertex* lower_right_in,
           GridCellVertex* upper_left_in, GridCellVertex* upper_right_in,
           double heading_res_rad)
      : lower_left_ptr_(DCHECK_NOTNULL(lower_left_in)),
        lower_right_ptr_(DCHECK_NOTNULL(lower_right_in)),
        upper_left_ptr_(DCHECK_NOTNULL(upper_left_in)),
        upper_right_ptr_(DCHECK_NOTNULL(upper_right_in)),
        x_range_(lower_left_ptr_->x(), lower_right_ptr_->x()),
        y_range_(lower_left_ptr_->y(), upper_left_ptr_->y()),
        heading_res_rad_(heading_res_rad) {
    DCHECK_GT(heading_res_rad, 0.0);
    DCHECK(math::IsApprox(upper_left().x(), lower_left().x()));
    DCHECK(math::IsApprox(upper_right().x(), lower_right().x()));
    DCHECK(math::IsApprox(upper_left().y(), upper_right().y()));
    DCHECK(math::IsApprox(lower_left().y(), lower_right().y()));

    DCHECK_LT(lower_left().y(), upper_left().y());
    DCHECK_LT(lower_left().x(), lower_right().x());
  }

  // Constructs the discretized heading search space by making the corresponding
  // unique ptr.
  void ConstructHeadingSearchSpaceIfUninitialized() {
    if (heading_search_space_ != nullptr) {
      return;
    }
    heading_search_space_ = std::make_unique<DiscretizedHeadingSearchSpace>(
        key().x_idx, key().y_idx, heading_res_rad_);
  }

  // Returns the index of the heading interval that contains the input heading.
  int GetHeadingIntervalIndex(double heading_rad) const;

  // Returns the dominant OccupancyClearanceInfo regarding an input query point.
  // NOTE: drivable_clearance_thresh indicates an user-specified threshold for
  // determining the minimum required clearance to occupancy for a queried
  // position to be considered drivable.
  OccupancyClearanceInfo GetDominantOccupancyClearanceInfo(
      const math::geometry::Point2d& pt,
      double drivable_clearance_thresh) const;

  // Returns the heuristic distance to target pose given a queried position.
  double GetHeuristicDistanceToTarget(const math::geometry::Point2d& pt) const;

  // Returns an array of four GridCellVertices at the GridCell's four corners.
  std::array<const GridCellVertex*, 4> CornerVertices() const {
    return {lower_left_ptr_, lower_right_ptr_, upper_left_ptr_,
            upper_right_ptr_};
  }

  // Returns mutable pointer to the grid cell's heading search space.
  DiscretizedHeadingSearchSpace* mutable_heading_search_space() {
    return heading_search_space_.get();
  }

  // Const accessors.
  const GridCellVertex& lower_left() const { return *lower_left_ptr_; }
  const GridCellVertex& lower_right() const { return *lower_right_ptr_; }
  const GridCellVertex& upper_left() const { return *upper_left_ptr_; }
  const GridCellVertex& upper_right() const { return *upper_right_ptr_; }
  const DiscretizedHeadingSearchSpace* heading_search_space() const {
    return heading_search_space_.get();
  }

  // Returns the lower left corner vertex key of the GridCell as the
  // GridCellKey.
  const GridKey& key() const { return lower_left().key; }

 private:
  // Returns the quadratic interpolation ratio on the x, y dimension given a
  // pair of x and y query. The first returned element is the x interpolation
  // ratio; second is the y interpolation ratio.
  std::pair<double, double> GetQuadraticInterpolationRatio(double x,
                                                           double y) const;

  // Returns the quadratic interpolation value of the cell's clearance to a
  // dominant occupancy constraint given a pair of queried x and y.
  double GetInterpDominantOccupancyClearance(double x, double y) const;

  // The four corners of the GridCell.
  GridCellVertex* lower_left_ptr_ = nullptr;
  GridCellVertex* lower_right_ptr_ = nullptr;
  GridCellVertex* upper_left_ptr_ = nullptr;
  GridCellVertex* upper_right_ptr_ = nullptr;

  // The x, y span of this GridCell.
  math::Range1d x_range_;
  math::Range1d y_range_;

  // The heading resolution of the discretized heading search space.
  double heading_res_rad_ = 0.0;

  // Vector of explored open space search nodes whose xy fall into this
  // GridCell, yet with different headings.
  // NOTE: For run-time concerns, this data is not constructed at the same time
  // when a grid cell is constructed. Rather, this is constructed whenever the
  // open space path searcher make a query.
  std::unique_ptr<DiscretizedHeadingSearchSpace> heading_search_space_;

  FRIEND_TEST(OpenSpaceGridCellTest, GridCellTest);
  FRIEND_TEST(OpenSpaceGridUpdateTest, GridUpdateTest);
};

}  // namespace open_space

}  // namespace planner

#endif  // ONBOARD_PLANNER_OPEN_SPACE_MOTION_PLAN_GENERATOR_OPEN_SPACE_PATH_SEARCHER_GRID_CELL_H_
