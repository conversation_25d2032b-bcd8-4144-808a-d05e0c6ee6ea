
#ifndef OFFBOARD_FALLBACK_ADS_LATERAL_FUNCTION_LATERAL_FUNCTION_TYPE_H_
#define OFFBOARD_FALLBACK_ADS_LATERAL_FUNCTION_LATERAL_FUNCTION_TYPE_H_

#include <stdint.h>

typedef struct {
  float distance_error;
  float heading_error;
  float distance_error_rate;
  float heading_error_rate;
  float distance_error_weight;
  float heading_error_weight;
  float distance_error_rate_weight;
  float heading_error_rate_weight;
  uint16_t num_iteration;
  float diff_iteration;
  float feedback_steer_wheel_angle;
  float feedforward_steer_wheel_angle;
  float steer_wheel_angle;
  float limit_steer_wheel_angle;
  float max_steer_wheel_angle;
  float max_steer_wheel_angle_rate;
} LqrControllerDebug;

#endif  // OFFBOARD_FALLBACK_ADS_LATERAL_FUNCTION_LATERAL_FUNCTION_TYPE_H_
