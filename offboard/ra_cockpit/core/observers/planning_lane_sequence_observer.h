#ifndef OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_PLANNING_LANE_SEQUENCE_OBSERVER_H_
#define OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_PLANNING_LANE_SEQUENCE_OBSERVER_H_

#include <functional>
#include <memory>

#include "../data_observer.h"
#include "planner_protos/planning_lane_sequence.pb.h"
#include "teleassist_protos/teleassist.pb.h"

namespace remote_assist {

class PlanningLaneSequenceObserver : public IDataObserver {
 public:
  using PlanningLaneSequenceCallback = std::function<void(
      std::shared_ptr<planner::pb::PlanningLaneSequence>, DataChannel)>;

  explicit PlanningLaneSequenceObserver(PlanningLaneSequenceCallback callback);

  bool IsInterestedIn(
      const teleassist::pb::TeleassistRequest& message) override;

  std::shared_ptr<google::protobuf::Message> OnMessage(
      const teleassist::pb::TeleassistRequest& message,
      DataChannel data_channel) override;

 private:
  PlanningLaneSequenceCallback callback_;
};

}  // namespace remote_assist

#endif  // OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_PLANNING_LANE_SEQUENCE_OBSERVER_H_
