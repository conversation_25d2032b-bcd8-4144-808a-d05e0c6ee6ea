#include "nominal_path.h"

#include <algorithm>
#include <utility>

#include "planner/constants.h"
#include "planner/planning_gflags.h"
#include "planner/utility/physics/motion_model_1d.h"

namespace planner {
namespace lane_selection {
namespace {
// Small distance to avoid nominal path too close to max driving distance
constexpr double kForwardAdditionDistanceInMeter = 5.0;
// Default distance from the ego's arc-length to the path starting point.
constexpr double kDefaultDistanceBehindEgoInMeter = 50.0;
// Default distance from the ego's arc-length to the path end point.
// The actual distance may be longer if the ego is traveling at high speed.
constexpr double kDefaultDistanceAheadOfEgoInMeter = 200.0;

math::geometry::PolylineCurve2d TrimPath(
    const math::geometry::PolylineCurve2d& original_path,
    const RobotStateSnapshot* robot_state_snapshot,
    double max_distance_behind_ego_in_meter,
    double max_distance_ahead_of_ego_in_meter,
    double& path_start_arclength_on_original_path,
    double& path_end_arclength_on_original_path, bool& is_trimmed) {
  if (!FLAGS_planning_enable_reference_path_trimming || !robot_state_snapshot) {
    path_start_arclength_on_original_path = 0;
    path_end_arclength_on_original_path = original_path.GetTotalArcLength();
    is_trimmed = false;
    return original_path;
  }

  const double max_speed = robot_state_snapshot->car_model_with_shape()
                               .param()
                               .limit()
                               .speed_limit()
                               .max_val();
  const double cur_speed = robot_state_snapshot->speed();
  const double max_accel = robot_state_snapshot->car_model_with_shape()
                               .param()
                               .limit()
                               .accel_limit()
                               .max_val();
  const double max_decel = robot_state_snapshot->car_model_with_shape()
                               .param()
                               .limit()
                               .accel_limit()
                               .min_val();

  const double furthest_driving_distance =
      physics::GetFurthestDrivingDistance(cur_speed, max_speed, max_accel,
                                          max_decel,
                                          constants::kTrajectoryHorizonInSec) +
      kForwardAdditionDistanceInMeter;

  max_distance_ahead_of_ego_in_meter =
      std::max(max_distance_ahead_of_ego_in_meter, furthest_driving_distance);

  // Calculate the arc length of the ego position on the original path.
  const double ego_arc_length =
      original_path
          .GetProximity({robot_state_snapshot->x(), robot_state_snapshot->y()},
                        math::pb::UseExtensionFlag::kForbid)
          .arc_length;

  // Calculate the start and end arc lengths of the trimmed path on the original
  // path based on ego position.
  path_start_arclength_on_original_path =
      std::max(0.0, ego_arc_length - max_distance_behind_ego_in_meter);
  path_end_arclength_on_original_path =
      std::min(original_path.GetTotalArcLength(),
               ego_arc_length + max_distance_ahead_of_ego_in_meter);

  // If the original path is short enough, no need to trim it.
  if (path_start_arclength_on_original_path <= 0.0 &&
      path_end_arclength_on_original_path >=
          original_path.GetTotalArcLength()) {
    path_start_arclength_on_original_path = 0;
    path_end_arclength_on_original_path = original_path.GetTotalArcLength();
    return original_path;
  }

  is_trimmed = true;

  // Return the trimmed path.
  return math::geometry::PolylineCurve2d(
      original_path.GetSkeletonInRange(path_start_arclength_on_original_path,
                                       path_end_arclength_on_original_path));
}

}  // namespace

NominalPath::NominalPath(math::geometry::PolylineCurve2d original_path,
                         const RobotStateSnapshot* robot_state_snapshot)
    : math::geometry::PolylineCurve2d(TrimPath(
          original_path, robot_state_snapshot, kDefaultDistanceBehindEgoInMeter,
          kDefaultDistanceAheadOfEgoInMeter,
          path_start_arclength_on_original_path_,
          path_end_arclength_on_original_path_, is_trimmed_)),
      original_path_(std::move(original_path)) {}

math::ProximityQueryInfo NominalPath::GetProximity(
    const math::geometry::Point2d& point,
    math::pb::UseExtensionFlag allow_extension_flag) const {
  // If the path is not trimmed, use the proximity information from the entire
  // path.
  if (!is_trimmed_) {
    return math::geometry::PolylineCurve2d::GetProximity(point,
                                                         allow_extension_flag);
  }

  // Get the proximity information from the trimmed path.
  auto trimmed_path_proximity = math::geometry::PolylineCurve2d::GetProximity(
      point, allow_extension_flag);

  // If the point is within the trimmed path, return the proximity information
  // directly.
  // NOTE: CANNOT use trimmed_path_proximity.relative_position here,
  // as in U-turn case, the trimmed_path_proximity.relative_position may !=
  // math::RelativePositionKWithin but that point still can be projected within
  // path
  if (trimmed_path_proximity.arc_length > 0 &&
      trimmed_path_proximity.arc_length < GetTotalArcLength()) {
    return trimmed_path_proximity;
  }

  // The point is outside the trimmed path; get proximity from the original
  // (full) path.
  auto original_path_proximity =
      original_path_.GetProximity(point, allow_extension_flag);

  // If the point projects before the start of the trimmed path.
  if (original_path_proximity.arc_length <
      path_start_arclength_on_original_path_) {
    original_path_proximity.relative_position = math::RelativePosition::kBefore;
    // If extensions are not allowed, clamp to the start point of the trimmed
    // path.
    if (allow_extension_flag == math::pb::UseExtensionFlag::kForbid) {
      original_path_proximity.x = GetStartPoint().x();
      original_path_proximity.y = GetStartPoint().y();
      original_path_proximity.arc_length = 0.0;
    } else {
      // Adjust arc length relative to the start of the trimmed path.
      original_path_proximity.arc_length -=
          path_start_arclength_on_original_path_;
    }

    // Recompute the closest segment index based on the adjusted arc length.
    // This is necessary because the original path may have a different number
    // of segments compared to the trimmed path.
    original_path_proximity.closest_segment_index =
        GetSegmentIndex(original_path_proximity.arc_length);
    return original_path_proximity;
  }

  // If the point projects beyond the end of the trimmed path.
  if (original_path_proximity.arc_length >
      path_end_arclength_on_original_path_) {
    original_path_proximity.relative_position = math::RelativePosition::kAfter;
    // If extensions are not allowed, clamp to the end point of the trimmed
    // path.
    if (allow_extension_flag == math::pb::UseExtensionFlag::kForbid) {
      original_path_proximity.x = GetEndPoint().x();
      original_path_proximity.y = GetEndPoint().y();
      original_path_proximity.arc_length = GetTotalArcLength();
    } else {
      // Adjust arc length relative to the start of the trimmed path.
      original_path_proximity.arc_length =
          original_path_proximity.arc_length -
          path_start_arclength_on_original_path_;
    }

    // Recompute the closest segment index based on the adjusted arc length.
    // This is necessary because the original path may have a different number
    // of segments compared to the trimmed path.
    original_path_proximity.closest_segment_index =
        GetSegmentIndex(original_path_proximity.arc_length);
    return original_path_proximity;
  }

  // If the point is within the range of the trimmed path, adjust the arc
  // length.
  original_path_proximity.arc_length -= path_start_arclength_on_original_path_;

  // Recompute the closest segment index based on the adjusted arc length.
  // This is necessary because the original path may have a different number
  // of segments compared to the trimmed path.
  original_path_proximity.closest_segment_index =
      GetSegmentIndex(original_path_proximity.arc_length);
  return original_path_proximity;
}

}  // namespace lane_selection
}  // namespace planner
