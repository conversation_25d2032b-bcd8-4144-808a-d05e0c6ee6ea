#include "pnc_exception_request_observer.h"

#include <utility>

#include "av_comm/topics.h"
#include "glog/logging.h"

namespace remote_assist {

PncExceptionHandlerObserver::PncExceptionHandlerObserver(
    PncExceptionHandlerCallback callback)
    : pnc_excep_requst_callback_(std::move(callback)) {}

bool PncExceptionHandlerObserver::IsInterestedIn(
    const teleassist::pb::TeleassistRequest& message) {
  return (message.message_type() == kPncExceptionHandler);
}

std::shared_ptr<google::protobuf::Message>
PncExceptionHandlerObserver::OnMessage(
    const teleassist::pb::TeleassistRequest& message,
    DataChannel data_channel) {
  auto pnc_excep_requst =
      std::make_shared<teleassist::pb::PncExceptionHandler>();
  if (!pnc_excep_requst->ParseFromString(message.message_data())) {
    LOG(ERROR) << "Received error PncExceptionHandler message format, len = "
               << message.message_data().length();
    return nullptr;
  }

  if (pnc_excep_requst_callback_) {
    pnc_excep_requst_callback_(pnc_excep_requst, data_channel);
  }
  return pnc_excep_requst;
}

}  // namespace remote_assist
