
#ifndef ONBOARD_PREDICTION_VECTORNET_VECTORNET_SAMPLE_H_
#define ONBOARD_PREDICTION_VECTORNET_VECTORNET_SAMPLE_H_

#include <map>
#include <memory>
#include <utility>
#include <vector>

#include "prediction/common/definition/prediction_constants.h"
#include "prediction/concept/trajectory/predicted_trajectory.h"
#include "prediction_protos/map_object.pb.h"
#include "voy_protos/perception_object_type.pb.h"

namespace prediction {

using VectorFeatures = std::vector<std::vector<float>>;

struct AgentKey {
  voy::perception::ObjectType agent_type =
      voy::perception::ObjectType::NOT_GIVEN;
  int64_t agent_id = constants::kInvalidObjId;

  AgentKey(voy::perception::ObjectType object_type, int64_t object_id)
      : agent_type(object_type), agent_id(object_id) {}
  bool operator<(const <PERSON><PERSON><PERSON>& rhs) const { return agent_id < rhs.agent_id; }

  bool operator==(const AgentKey& rhs) const {
    return (agent_type == rhs.agent_type) && (agent_id == rhs.agent_id);
  }
};

struct MapObjectKey {
  pb::MapObject::MapObjectType map_object_type = pb::MapObject::UNKNOWN_TYPE;
  int64_t map_object_id = -1;
  int segment_index = -1;
  int64_t timestamp = constants::kInvalidTimestamp;
  int64_t map_object_id_for_traffic_light = -1;

  bool operator<(const MapObjectKey& rhs) const {
    if (map_object_type != rhs.map_object_type) {
      return map_object_type < rhs.map_object_type;
    }

    if (map_object_id != rhs.map_object_id) {
      return map_object_id < rhs.map_object_id;
    }

    if (segment_index != rhs.segment_index) {
      return segment_index < rhs.segment_index;
    }

    return timestamp < rhs.timestamp;
  }

  friend std::ostream& operator<<(std::ostream& os, const MapObjectKey& key) {
    os << pb::MapObject::MapObjectType_Name(key.map_object_type) << "_"
       << key.map_object_id << "_" << key.segment_index << "_" << key.timestamp
       << "_" << key.map_object_id_for_traffic_light;
    return os;
  }
};

struct ObstacleClusterKey {
  int cluster_id = -1;
  int segment_idx = -1;
  // Large obstacle's cluster would be partition to a few small cluster.
  // here is represent the start index and end index for partition clusters.
  int partition_start_idx = -1;
  int partition_end_idx = -1;

  bool operator<(const ObstacleClusterKey& rhs) const {
    if (cluster_id != rhs.cluster_id) {
      return cluster_id < rhs.cluster_id;
    }

    return segment_idx < rhs.segment_idx;
  }
};

struct TrafficLightKey {
  int64_t map_object_id = -1;
  hdmap::Lane::Turn lane_turn_type = hdmap::Lane::UNKNOWN_TURN;

  bool operator<(const TrafficLightKey& rhs) const {
    if (map_object_id != rhs.map_object_id) {
      return map_object_id < rhs.map_object_id;
    }
    return lane_turn_type < rhs.lane_turn_type;
  }

  bool operator==(const TrafficLightKey& rhs) const {
    return (map_object_id == rhs.map_object_id) &&
           (lane_turn_type == rhs.lane_turn_type);
  }
};

struct VectornetOutput {
  // The means and standard deviation of Time-To-Move by predefined target
  // speeds. For now, they are [0.5m/s, 1.0m/s, 3.0m/s]. An agent is predicted
  // as "start-to-move" if it will reach some pre-defined target speeds in few
  // seconds.
  struct StartToMoveOutput {
    double stm_prob = 0.0;          // Start-to-move likelihood [0.0-1.0].
    std::vector<double> ttm_means;  // in seconds.
    std::vector<double> ttm_standard_deviations;  // in seconds.
  };

  VectornetOutput() = default;

  // Multi-modal predicted trajectories ignoring ego.
  std::vector<PredictedTrajectory> ignore_ego_trajectories;
  // Multi-modal predicted trajectories.
  std::vector<PredictedTrajectory> trajectories;
  // Map object keys sorted by attention score. It could be non-empty only in
  // simulation.
  std::vector<MapObjectKey> map_object_keys;
  float yield_probability = constants::kInvalidYieldProbability;
  // Agent's importance rank when predicting the ego in joint prediction.
  // This rank is based on the attention score of the transformer layer.
  int64_t importance_rank_to_ego = constants::kInvalidImportanceRankToEgo;
  // The output of start-to-move head.
  StartToMoveOutput start_to_move_output;
  // Agent's feature embedding from the joint prediction model.
  // This could be empty if one agent is not covered by the joint prediction
  // model.
  std::vector<float> feature_embedding;
  // Agent's attentioned_agent_ids is from attention score of the joint model.
  // This stores the important agents ids for this agent.
  // Order is useful. The earlier it is, the more important it is.
  std::vector<int64_t> attentioned_agent_ids;

  // The yield probablity if ego changed lane to the current lane of the target
  // agent.
  float cbp_lane_change_yield_probability = constants::kInvalidYieldProbability;
  // The ego trajectory if ego managed to make the lane-change.
  PredictedTrajectory cbp_lane_change_ego_trajectory;
  // The target agent yield trajectory if ego managed to make the lane-change.
  PredictedTrajectory cbp_lane_change_yield_trajectory;
  // The ego trajectory if ego managed to make the lane-change to the rear of
  // the target agent.
  PredictedTrajectory cbp_lane_change_behind_ego_trajectory;
  // The target agent pass trajectory if ego managed to make the lane-change.
  PredictedTrajectory cbp_lane_change_pass_trajectory;

  // The anomaly score of target agent.
  double vae_score = 0.0;
  double anomaly_score = 0.0;
};

}  // namespace prediction

#endif  // ONBOARD_PREDICTION_VECTORNET_VECTORNET_SAMPLE_H_
