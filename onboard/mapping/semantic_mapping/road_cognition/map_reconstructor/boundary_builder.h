#ifndef ONBOARD_MAPPING_SEMANTIC_MAPPING_ROAD_COGNITION_MAP_RECONSTRUCTOR_BOUNDARY_BUILDER_H_
#define ONBOARD_MAPPING_SEMANTIC_MAPPING_ROAD_COGNITION_MAP_RECONSTRUCTOR_BOUNDARY_BUILDER_H_

#include <map>
#include <memory>
#include <string>
#include <vector>

#include <Eigen/Core>
#include <glm/vec3.hpp>

#include "boundary_tracker.h"
#include "perception/sensing/lidar/scan_channel.h"
#include "road_cognition/common/data_types.hpp"
#include "road_cognition/common/pose_types.hpp"
#include "road_cognition/utils/voxel_util.hpp"
#include "slam_protos/grid.pb.h"
#include "slam_protos/online_map_tracking.pb.h"
#include "voy_protos/obstacle.pb.h"
#include "voy_protos/tracked_objects.pb.h"

namespace slam {
namespace semantic_mapping {
namespace road_cognition {

class BoundaryBuilder {
 public:
  BoundaryBuilder() = default;

  bool Init();

  /**
   * Process function use other msg to post-process online map elements
   * @param pose: pose from ego_tf.
   * @param percp_map: map elements from online-tracking.
   * @param obstacles: obstacles info from lidar
   * @param scan_msg: scan_msg from lidar
   * @param tracked_objects: moving objects from tracking node
   * @param filtered_percp_map: transport percp_map after post-process
   * @return
   */
  void Process(
      const PosePtr& pose,
      const std::shared_ptr<const proto::TrackedMapElementList>& percp_map,
      const std::shared_ptr<const voy::Obstacles>& obstacles,
      const std::shared_ptr<const perception::ScanMessage>& scan_msg,
      const std::shared_ptr<const voy::TrackedObjectList>& tracked_objects,
      std::shared_ptr<proto::TrackedMapElementList>& filtered_percp_map,
      std::shared_ptr<proto::HeightDiffGrid>& output_grid_ptr);

 private:
  /**
   * Check Pose status and localization fault. And downsample with distance
   * @param pose: pose from ego_tf.
   */
  [[nodiscard]] bool PreProcessPose(const PosePtr& pose);
  /**
   * Use other msg cal existence; Cal index(distance); Trans Coord from body to
   * global
   * @param pose: pose from ego_tf.
   * @param percp_map: map elements from online-tracking.
   * @param obstacles: obstacles info from lidar
   * @param tracked_objects: moving objects from tracking node
   * @return preprocessed single frame boundaries
   */
  [[nodiscard]] std::vector<LaneBoundarySinglePtr> PreProcessFrame(
      const PosePtr& pose,
      const std::shared_ptr<const proto::TrackedMapElementList>& percp_map,
      const Voxel2DUtilPtr& voxel_util_ptr) const;
  /**
   * Use other msg cal existence voxel
   * @param pose: pose from ego_tf.
   * @param scan_msg: scan message from perception
   * @param tracked_objects: moving objects from tracking node
   * @param obstacles: obstacles info from lidar
   * @return Voxel2DUtilPtr
   */
  [[nodiscard]] Voxel2DUtilPtr CreateExistenceVoxel(
      const PosePtr& pose,
      const std::shared_ptr<const perception::ScanMessage>& scan_msg,
      const std::shared_ptr<const voy::TrackedObjectList>& tracked_objects,
      const std::shared_ptr<const voy::Obstacles>& obstacles) const;
  /**
   * Calculate index of every point.
   * @param pose: pose from ego_tf.
   * @param car_center_points: geometry of elements.
   * @param is_horizontal: whether this elements is horizontal along the moving
   * direction.
   * @return std::vector<double>: index of every point.
   */
  [[nodiscard]] std::vector<double> GetIndexOfPolyline(
      const PosePtr& pose, const std::vector<glm::dvec3>& car_center_points,
      bool is_horizontal = false) const;

  /**
   * Judge whether the element is reconstruct target element or not.
   * @param type: type of element.
   * @return bool: is target element or not.
   */
  [[nodiscard]] bool IsTargetElement(
      const slam::proto::TrackedMapElement& element) const;

  /**
   * Judge whether the geometry is horizontal along the moving direct
   * @param car_center_points: geometry of elements.
   * @return bool:horizontal along the moving direct
   */
  [[nodiscard]] bool IsHorizontal(
      const std::vector<glm::dvec3>& car_center_points) const;

  /**
   * Calculate element direction.
   * @param car_center_points: geometry of elements.
   * @return ElementDirection: element direction.
   */
  [[nodiscard]] ElementDirection CalElementDirection(
      const std::vector<glm::dvec3>& car_center_points) const;

  /**
   * Calculate angle of observation, unit: rad.
   * @param car_center_points: geometry of elements.
   * @return double:angle of observation, unit: rad.
   */
  [[nodiscard]] double GetObserveAngleRad(
      const std::vector<glm::dvec3>& car_center_points) const;

  /**
   * Cut geometry at the end of boundary according to other infos.
   * @param boundary: preprocessed boundary.
   */
  void CutBoundaryEndByStatus(LaneBoundarySinglePtr& boundary) const;

  /**
   * Use scan msg to fill properties in a local voxel grid.
   * @param pose: pose from ego_tf.
   * @param scan_msg: scan msg from scan conversion node
   * @param voxel_util: was operated by obstacles msg
   * @return
   */
  void ExtractToVoxel(
      const PosePtr& pose,
      const std::shared_ptr<const perception::ScanMessage>& scan_msg,
      Voxel2DUtilPtr& voxel_util) const;

  /**
   * Use obstacle msg to fill properties in a local voxel grid.
   * @param pose: pose from ego_tf.
   * @param obstacles: obstacles info from lidar
   * @param voxel_util: was operated by obstacles msg
   * @return
   */
  void ExtractToVoxel(const PosePtr& pose,
                      const std::shared_ptr<const voy::Obstacles>& obstacles,
                      Voxel2DUtilPtr& voxel_util) const;

  /**
   * Use moving objs msg to fill properties in a local voxel grid.
   * @param pose: pose from ego_tf.
   * @param tracked_objs: tracked moving objects
   * @param voxel_util: was operated by tracked_objects msg
   * @return
   */
  void ExtractToVoxel(
      const PosePtr& pose,
      const std::shared_ptr<const voy::TrackedObjectList>& tracked_objs,
      Voxel2DUtilPtr& voxel_util) const;
  /**
   * Use moving objs msg to fill properties in a local voxel grid.
   * @param pose: pose from ego_tf.
   * @param tracked_obj: tracked moving object
   * @param extract_history_centers: need to extract history centers or not
   * @return vector of <x_center(m), y_center(m), z_center(m), obj_yaw(rad)> in
   * body frame.
   */
  [[nodiscard]] std::vector<Eigen::Vector4d> ExtractObjectVehCenters(
      const PosePtr& pose, const voy::TrackedObject& tracked_obj,
      bool extract_history_centers = false) const;

  /**
   * Track boundary by track_id.
   * @param single_frame_boundaries: boundaries of single frame
   */
  void TrackBoundary(
      std::vector<LaneBoundarySinglePtr>& single_frame_boundaries);

  /**
   * When tracked obj geometry has large diff. Need reset instance.
   * @param src_boundary: geometry of source boundary
   * @param tgt_boundary: geometry of target boundary
   * @return need reset boundary instance.
   */
  [[nodiscard]] bool IsFixBoundaryNeedReset(
      const int64_t& track_id, const std::map<double, ObsPoint>& src_boundary,
      const std::map<double, ObsPoint>& tgt_boundary);

  /**
   * Calculate project distance.
   * @param src_boundary: geometry of source boundary
   * @param tgt_boundary: geometry of target boundary
   * @param dis: project distance
   * @return is matched or not.
   */
  [[nodiscard]] bool CalProjDistance(
      const std::map<double, ObsPoint>& src_boundary,
      const std::map<double, ObsPoint>& tgt_boundary, double& dis) const;

  /**
   * Update boundary geometry and features after single frames added.
   * @param current_pose: geometry of source boundary
   * @return Updated boundaries.
   */
  std::vector<ReconBoundaryPtr> UpdateBoundary(const PosePtr& current_pose);

  /**
   * Convert struct to proto msg.
   * @param pose: current pose.
   * @param percp_map: raw percp map from tracking
   * @param filtered_percp_map: processed percp_map.
   * @param output_boundaries: reconstructed boundaries
   */
  void ConvertToProto(
      const std::shared_ptr<const proto::TrackedMapElementList>& percp_map,
      std::shared_ptr<proto::TrackedMapElementList>& filtered_percp_map,
      const std::vector<ReconBoundaryPtr>& output_boundaries) const;

  /**
   * Convert struct to proto msg.
   * @param voxel_util_ptr: Voxel2DUtilPtr
   * @param grid_proto_ptr: output height diffgrid proto ptr
   */
  static void ConvertToProto(
      const Voxel2DUtilPtr& voxel_util_ptr,
      std::shared_ptr<proto::HeightDiffGrid>& grid_proto_ptr);

  std::shared_ptr<Pose> prev_pose_{nullptr};  // Previous pose of keyframe
  std::map<int64_t, BoundaryTrackerPtr>
      tracker_map_;                         // <track_id, tracked boundary>
  Voxel2DUtilPtr voxel_util_ptr_{nullptr};  // Voxel util

  /**
   * Just for debug. Plot to pointcloud.
   * @param pose: current pose.
   * @param obstacles: current obstacle.
   */
  void OutputDebugPly(
      const PosePtr& pose,
      const std::shared_ptr<const voy::Obstacles>& obstacles) const;

  /**
   * Just for debug. Plot to pointcloud.
   * @param pose: current pose.
   * @param percp_map: current percp_map.
   */
  void OutputDebugPly(const Voxel2DUtilPtr& voxel_util,
                      const std::shared_ptr<const proto::TrackedMapElementList>&
                          percp_map) const;
  std::string debug_outpath_;  // Output debug file dir. From
                               // Flags_road_cognition_debug_outpath
};
}  // namespace road_cognition
}  // namespace semantic_mapping
}  // namespace slam
#endif  // ONBOARD_MAPPING_SEMANTIC_MAPPING_ROAD_COGNITION_MAP_RECONSTRUCTOR_BOUNDARY_BUILDER_H_
