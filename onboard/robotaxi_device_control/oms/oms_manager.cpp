#include "oms_manager.h"

#include <list>
#include <memory>
#include <string>
#include <utility>

#include <boost/asio/io_service.hpp>
#include <glog/logging.h>
#include <google/protobuf/util/json_util.h>

#include "av_comm/onboard_config.h"
#include "av_comm/topics.h"
#include "json/json.hpp"
#include "strings/stringprintf.h"
#include "util/rtevent_post_util.h"
#include "voy_protos/canbus.pb.h"

namespace robotaxi_device_control {

namespace {
constexpr int kTimeoutMs = 3000;
constexpr int kNetworkErrorInSeconds = 3;
constexpr double kMaxSpeedFor21stAlgorithm = 7.0;  // km/h

constexpr int kDefaultBeltListSize = 3;
constexpr int kPeopleWithSeatBelt = 1;
constexpr int kPeopleWithoutSeatBelt = 0;
constexpr int kOMSAlgorithmEnable = 1;

constexpr char kTAITUUrl[] = "/api/algorithm/query";
constexpr char kTAITUHost[] = "*************";
constexpr uint16_t kTAITUPort = 8090;
constexpr char kTAITUConnectType[] = "application/json;charset=utf-8";
constexpr char kTAITUHttpBody[] = "{\"deviceId\":\"1\"}";

constexpr char kJUSHIXDEVICEUrl[] = "/api/algorithm/query";
constexpr char kJUSHIXDEVICEHost[] = "*************";
constexpr uint16_t kJUSHIXDEVICEPort = 9999;
constexpr char kJUSHIXDEVICEConnectType[] = "application/x-www-form-urlencoded";
constexpr char kJUSHIX100HttpBody[] =
    "params={\"detectId\":35,\"endTime\":-1,\"startTime\":-1}&params={"
    "\"detectId\":28,\"endTime\":-1,\"startTime\":-1}&params={\"detectId\":21,"
    "\"endTime\":-1,\"startTime\":-1}&params={\"detectId\":1002,\"endTime\":-1,"
    "\"startTime\":-1}";
constexpr char kJUSHIX100AHttpBody[] =
    "params={\"detectId\":10005,\"endTime\":-1,"
    "\"startTime\":-1}&params={\"detectId\":10006,\"endTime\":-1,\"startTime\":"
    "-1}&params={\"detectId\":10007,\"endTime\":-1,\"startTime\":-1}";
using json = nlohmann::json;
using OmsIdentifyInfo = robotaxi::pb::OmsIdentifyInfo;
using TAITUIdentifyInfo = robotaxi::pb::TAITUIdentifyInfo;

// Mark OcclusionInfo object as invalid with specified value
void MarkOcclusionInfoAsInvalid(robotaxi::pb::OcclusionInfo* info,
                                int value = -2) {
  info->set_type(value);
  info->set_score(std::to_string(value));
}

// Mark all fields in OmsIdentifyInfo as invalid with specified value
void MarkOmsIdentifyInfoAsInvalid(OmsIdentifyInfo* info, int value = -2) {
  auto* people_info = info->mutable_people_num_info();
  people_info->set_driver(value);
  people_info->set_front_passenger(value);
  people_info->set_rear_left(value);
  people_info->set_rear_middle(value);
  people_info->set_rear_right(value);
  people_info->set_rear_sum(value);

  auto* seatbelt_info = info->mutable_seatbelt_info();
  seatbelt_info->set_rear(value);
  seatbelt_info->set_driver(value);
  seatbelt_info->set_copilot(value);
  seatbelt_info->set_rear_left(value);
  seatbelt_info->set_rear_middle(value);
  seatbelt_info->set_rear_right(value);
  seatbelt_info->set_object_num(value);

  auto* underage_info = info->mutable_underage_info();
  underage_info->set_people_num(value);
  underage_info->set_underage_num(value);

  auto* occlusion_info = info->mutable_occlusion_info();
  occlusion_info->set_type(value);
  occlusion_info->set_score(std::to_string(value));

  auto* lost_items_info = info->mutable_lost_items_info();
  lost_items_info->clear_lost_items();
  robotaxi::pb::LostItem lost_item;
  lost_item.set_lost_type(robotaxi::pb::LOST_ERROR_TYPE);
  lost_item.set_lost_number(value);
  lost_items_info->add_lost_items()->Swap(&lost_item);
}

void FillLostItemsInfo(const json& content, OmsIdentifyInfo* out) {
  robotaxi::pb::LostItemsList LostItemsListInfo;
  const auto& backThingsList = content.at("backThingsList");

  const bool has_back_switch =
      content.count("backSwitch") != 0 && content.at("backSwitch").is_array();
  const json back_switch = has_back_switch ? content.at("backSwitch") : json{};

  for (size_t j = 0; j < backThingsList.size(); ++j) {
    const auto& lostitem = backThingsList.at(j);
    int thing_type = lostitem.at("thingType").get<int>();

    if (has_back_switch && thing_type >= 0 &&
        static_cast<size_t>(thing_type) < back_switch.size()) {
      if (back_switch.at(thing_type).get<int>() != kOMSAlgorithmEnable) {
        continue;
      }
    }

    robotaxi::pb::LostItem LostItem_info;
    if (static_cast<int>(robotaxi::pb::LostType_MAX) < thing_type ||
        static_cast<int>(robotaxi::pb::LostType_MIN) > thing_type) {
      LOG(ERROR) << "Lost type is undefine ";
      throw "Parse josn error: Lost type  ";
    }
    LostItem_info.set_lost_type((robotaxi::pb::LostType)thing_type);
    LostItem_info.set_lost_number(lostitem.at("number").get<int>());
    LostItemsListInfo.add_lost_items()->Swap(&LostItem_info);
  }
  out->mutable_lost_items_info()->Swap(&LostItemsListInfo);
}

void FillSeatbeltInfoFromBeltList(const json& content, OmsIdentifyInfo* out) {
  robotaxi::pb::SeatbeltInfo seatbelt_info;
  const auto& beltList = content.at("beltList");
  if (beltList.size() < kDefaultBeltListSize) {
    LOG(ERROR) << "beltList size < 3 ";
    throw "Parse josn error: beltList size";
  }

  bool has_belt_switch = false;
  json belt_switch;
  if (content.count("beltSwitch") != 0 && content.at("beltSwitch").is_array()) {
    belt_switch = content.at("beltSwitch");
    has_belt_switch = true;
  }

  int rearbeltN = 0;
  if (has_belt_switch && belt_switch.size() > 0 &&
      belt_switch.at(0).get<int>() != kOMSAlgorithmEnable) {
    seatbelt_info.set_rear_right(-1);
  } else if (beltList.at(0) == kPeopleWithSeatBelt) {
    seatbelt_info.set_rear_right(1);
    rearbeltN++;
  }
  if (has_belt_switch && belt_switch.size() > 1 &&
      belt_switch.at(1).get<int>() != kOMSAlgorithmEnable) {
    seatbelt_info.set_rear_middle(-1);
  } else if (beltList.at(1) == kPeopleWithSeatBelt) {
    seatbelt_info.set_rear_middle(1);
    rearbeltN++;
  }
  if (has_belt_switch && belt_switch.size() > 2 &&
      belt_switch.at(2).get<int>() != kOMSAlgorithmEnable) {
    seatbelt_info.set_rear_left(-1);
  } else if (beltList.at(2) == kPeopleWithSeatBelt) {
    seatbelt_info.set_rear_left(1);
    rearbeltN++;
  }

  seatbelt_info.set_rear(rearbeltN);
  out->mutable_seatbelt_info()->Swap(&seatbelt_info);
}

void FillPeopleNumInfoFromBeltList(const json& content, OmsIdentifyInfo* out) {
  robotaxi::pb::PeopleNumInfo people_num_info;

  bool people_detection_enabled = true;
  if (content.count("peopleSwitch") != 0 &&
      content.at("peopleSwitch").is_array()) {
    const auto& people_switch = content.at("peopleSwitch");
    if (!people_switch.empty() &&
        people_switch.at(0).get<int>() != kOMSAlgorithmEnable) {
      people_detection_enabled = false;
    }
  }
  if (!people_detection_enabled) {
    people_num_info.set_rear_right(0);
    people_num_info.set_rear_middle(0);
    people_num_info.set_rear_left(0);
    people_num_info.set_rear_sum(0);
  } else {
    const auto& beltList = content.at("beltList");
    if (beltList.at(0) == kPeopleWithSeatBelt ||
        beltList.at(0) == kPeopleWithoutSeatBelt) {
      people_num_info.set_rear_right(1);
    }
    if (beltList.at(1) == kPeopleWithSeatBelt ||
        beltList.at(1) == kPeopleWithoutSeatBelt) {
      people_num_info.set_rear_middle(1);
    }
    if (beltList.at(2) == kPeopleWithSeatBelt ||
        beltList.at(2) == kPeopleWithoutSeatBelt) {
      people_num_info.set_rear_left(1);
    }
    people_num_info.set_rear_sum(content.at("peopleNum").get<int>());
  }

  out->mutable_people_num_info()->Swap(&people_num_info);
}

void FillPeopleSeatbeltItemInfo(const json& content, OmsIdentifyInfo* out) {
  // Log switch states only once for debug
  static bool switch_logged = false;
  if (!switch_logged) {
    std::string switch_info = "10007 OMS Switch Configuration: ";
    if (content.count("peopleSwitch") != 0) {
      switch_info += "peopleSwitch=" + content.at("peopleSwitch").dump() + " ";
    }
    if (content.count("backSwitch") != 0) {
      switch_info += "backSwitch=" + content.at("backSwitch").dump() + " ";
    }
    if (content.count("beltSwitch") != 0) {
      switch_info += "beltSwitch=" + content.at("beltSwitch").dump();
    }
    LOG_IF(INFO, !switch_logged) << switch_info;
    switch_logged = true;
  }

  FillLostItemsInfo(content, out);

  FillSeatbeltInfoFromBeltList(content, out);

  FillPeopleNumInfoFromBeltList(content, out);
}

void FillOcclusionInfo(const json& content, OmsIdentifyInfo* out) {
  robotaxi::pb::OcclusionInfo occlusion_info;
  occlusion_info.set_type(content.at("type").get<int>());
  occlusion_info.set_score(content.at("score").get<std::string>());
  out->mutable_occlusion_info()->Swap(&occlusion_info);
}

void FillSeatbeltInfo(const json& content, OmsIdentifyInfo* out) {
  robotaxi::pb::SeatbeltInfo seatbelt_info;
  if (content.count("objectNum") != 0) {
    seatbelt_info.set_object_num(content.at("objectNum").get<int>());
  }
  seatbelt_info.set_rear(content.at("rear").get<int>());
  seatbelt_info.set_driver(content.at("driver").get<int>());
  seatbelt_info.set_copilot(content.at("copilot").get<int>());
  out->mutable_seatbelt_info()->Swap(&seatbelt_info);
}

void FillUnderageInfo(const json& content, OmsIdentifyInfo* out) {
  robotaxi::pb::UnderageInfo underage_info;
  underage_info.set_people_num(content.at("peopleNum").get<int>());
  int underage_num = 0;
  const auto& age_results = content.at("ageResults");
  for (size_t j = 0; j < age_results.size(); ++j) {
    const auto& age_result = age_results.at(j);
    if (age_result.at("prob_underage").get<float>() > 0.5) {
      underage_num += 1;
    }
  }
  underage_info.set_underage_num(underage_num);
  out->mutable_underage_info()->Swap(&underage_info);
}

void FillPeopleNumInfo(const json& content, OmsIdentifyInfo* out) {
  robotaxi::pb::PeopleNumInfo people_num_info;
  people_num_info.set_driver(content.at("driver").get<int>());
  people_num_info.set_front_passenger(content.at("frontPassenger").get<int>());
  people_num_info.set_rear_left(content.at("rearLeft").get<int>());
  people_num_info.set_rear_middle(content.at("rearMiddle").get<int>());
  people_num_info.set_rear_right(content.at("rearRight").get<int>());
  people_num_info.set_rear_sum(content.at("rearSum").get<int>());
  out->mutable_people_num_info()->Swap(&people_num_info);
}

// Add the ability to enable/disable of the OMS algorithm
// If moduleSwitch is 0, set type to 0 (OMS detection disabled)
// If moduleSwitch is 1, use original type (OMS detection enabled)
robotaxi::pb::OcclusionInfo GetOcclusionInfo(const json& content) {
  robotaxi::pb::OcclusionInfo occlusion_info;
  int mutable_type = content.at("type").get<int>();

  if (content.count("moduleSwitch") != 0 &&
      content.at("moduleSwitch").is_array()) {
    const auto& module_switch = content.at("moduleSwitch");
    if (mutable_type >= 0 &&
        static_cast<size_t>(mutable_type) < module_switch.size()) {
      if (module_switch.at(mutable_type).get<int>() != kOMSAlgorithmEnable) {
        mutable_type = 0;
      }
    }
  }

  occlusion_info.set_type(mutable_type);
  return occlusion_info;
}

void FillCamera1OcclusionInfo(const json& content, OmsIdentifyInfo* out) {
  // Log switch states only once for debug
  static bool switch_logged = false;
  if (!switch_logged) {
    std::string switch_info = "10005 OMS Switch Configuration: ";
    if (content.count("moduleSwitch") != 0) {
      switch_info += "moduleSwitch=" + content.at("moduleSwitch").dump() + " ";
    }
    LOG_IF(INFO, !switch_logged) << switch_info;
    switch_logged = true;
  }

  *out->mutable_camera_1_occlusion_info() = GetOcclusionInfo(content);
}

void FillCamera2OcclusionInfo(const json& content, OmsIdentifyInfo* out) {
  // Log switch states only once for debug
  static bool switch_logged = false;
  if (!switch_logged) {
    std::string switch_info = "10006 OMS Switch Configuration: ";
    if (content.count("moduleSwitch") != 0) {
      switch_info += "moduleSwitch=" + content.at("moduleSwitch").dump() + " ";
    }
    LOG_IF(INFO, !switch_logged) << switch_info;
    switch_logged = true;
  }

  *out->mutable_camera_2_occlusion_info() = GetOcclusionInfo(content);
}

bool TryToFillOMSPb(const json& content, int detect_id, OmsIdentifyInfo* out,
                    std::function<void(const json&, OmsIdentifyInfo*)> f) {
  try {
    f(content, out);
  } catch (...) {
    LOG(ERROR) << "detectId:[" << detect_id << "] content_to_pb error:\n"
               << content.dump();
    return false;
  }
  return true;
}

bool ParseContent(const json& content_str, int detect_id,
                  OmsIdentifyInfo* out) {
  if (!content_str.is_string()) {
    LOG(ERROR) << "detectId:[" << detect_id << "] content type error:\n"
               << content_str.dump();
    return false;
  }

  json content;
  try {
    content = json::parse(content_str.get<std::string>());
  } catch (...) {
    LOG(ERROR) << "detectId:[" << detect_id << "] fail to parse content:\n"
               << content_str.dump();
    return false;
  }

  if ((detect_id != 28 && !content.is_object()) ||
      (detect_id == 28 && (!content.is_array() || content.empty()))) {
    LOG(ERROR) << "detectId:[" << detect_id << "] content error:\n"
               << content.dump();
    return false;
  }

  switch (detect_id) {
    // Seatbelt algorithm identification results, detectId 35.
    case 35:
      return TryToFillOMSPb(content, 35, out, FillSeatbeltInfo);
    // Underage algorithm identification results, detectId 28.
    case 28:
      return TryToFillOMSPb(content.at(0), 28, out, FillUnderageInfo);
    // Number of people algorithm identify results, detectId 21.
    case 21:
      return TryToFillOMSPb(content, 21, out, FillPeopleNumInfo);
    // Occlusion algorithm identification results, detectId 1002.
    case 1002:
      return TryToFillOMSPb(content, 1002, out, FillOcclusionInfo);
    // Occlusion algorithm identification results, 10005, 10006.
    case 10005:
      return TryToFillOMSPb(content, 10005, out, FillCamera1OcclusionInfo);
    case 10006:
      return TryToFillOMSPb(content, 10006, out, FillCamera2OcclusionInfo);
    case 10007:
      return TryToFillOMSPb(content, 10007, out, FillPeopleSeatbeltItemInfo);
    default:
      // Do nothing, should never run into this.
      LOG(ERROR) << "unknown detectId: " << detect_id;
      return false;
  }

  // Should never run into this.
  LOG(ERROR) << "Parse detectId:[" << detect_id << "] failed, content_str:\n"
             << content_str.dump();
  return false;
}

bool ParseOMSAlgorithmResult(int64_t timestamp,
                             const json& algorithm_result_arr,
                             double vehicle_speed, OmsIdentifyInfo* out) {
  if (!algorithm_result_arr.is_array() || algorithm_result_arr.empty()) {
    LOG(ERROR) << "|algorithm_result| is empty or is not array:\n"
               << algorithm_result_arr.dump();
    return false;
  }

  const auto& algorithm_result = algorithm_result_arr.at(0);

  if (!algorithm_result.is_object()) {
    LOG(ERROR) << "|algorithm_result| is not object:\n"
               << algorithm_result_arr.dump();
    return false;
  }

  if (algorithm_result.count("code") == 0 ||
      !algorithm_result.at("code").is_number_integer()) {
    LOG(ERROR) << "code error:\n" << algorithm_result_arr.dump();
    return false;
  }

  if (algorithm_result.count("detectId") == 0 ||
      !algorithm_result.at("detectId").is_number_integer()) {
    LOG(ERROR) << "detectId error:\n" << algorithm_result_arr.dump();
    return false;
  }

  const auto detect_id = algorithm_result.at("detectId").get<int>();

  // In some cases, algorithm 28 and 1002  is not triggered, so content is null.
  // It's a normal state, the value of "code" is -1.
  if (algorithm_result.at("code").get<int>() == -1) {
    if (detect_id == 28 || detect_id == 1002) {
      return true;
    } else {
      LOG(ERROR) << "code = -1:\n" << algorithm_result_arr.dump();
      return false;
    }
  }

  if (algorithm_result.count("picTime") == 0 ||
      !algorithm_result.at("picTime").is_number_integer()) {
    LOG(ERROR) << "picTime error:\n" << algorithm_result_arr.dump();
    return false;
  }

  // Result of 28 may not be instant, when it is not triggered.
  // Result of algorithm 21 may not be instant, when the vehicle_speed is
  // greater than 10km/h, the algorithm will not run.
  if (timestamp - algorithm_result.at("picTime").get<int64_t>() > kTimeoutMs) {
    if (detect_id == 28) {
      return true;
    } else if (detect_id == 21 && vehicle_speed > kMaxSpeedFor21stAlgorithm) {
      return true;
    } else {
      switch (detect_id) {
        case 10005:
          // Set camera_1_occlusion_info values to -2 when timeout
          MarkOcclusionInfoAsInvalid(out->mutable_camera_1_occlusion_info());
          out->set_is_algorithm_available(false);
          break;
        case 10006:
          // Set camera_2_occlusion_info values to -2 when timeout
          MarkOcclusionInfoAsInvalid(out->mutable_camera_2_occlusion_info());
          out->set_is_algorithm_available(false);
          break;
        case 10007:
          // 10007 saves information on the number of seats, items,
          // and seat belts. If TimeOut, it will affect the flow of
          // orders and require special handling
          MarkOmsIdentifyInfoAsInvalid(out);
          out->set_is_algorithm_available(false);
          break;
        default:
          break;
      }

      nlohmann::json payload_json;
      payload_json["timestamp"] = base::Now();
      payload_json["app_version"] =
          utils::RTEventProcessor::GetInstance().GetVersion();
      payload_json["detect_id"] = detect_id;

      utils::RTEventProcessor::GetInstance()
          .PostRtEvent<
              rt_event::robotaxi_device_control::RobotaxiOmsAlgorithmTimeout>(
              payload_json.dump());
      LOG(ERROR) << "detect_id:" << detect_id << " picTime timeout:\n"
                 << algorithm_result_arr.dump();
      return false;
    }
  }

  if (algorithm_result.count("content") == 0) {
    LOG(ERROR) << "Not find content:\n" << algorithm_result_arr.dump();
    return false;
  }

  const auto& content_result_str = algorithm_result.at("content");
  if (!ParseContent(content_result_str, detect_id, out)) {
    LOG(ERROR) << "Parse content failed:\n" << algorithm_result_arr.dump();
    return false;
  }

  return true;
}

bool JUSHIXDeviceJsonToPb(const json& oms_json, double vehicle_speed,
                          int64_t* last_oms_timestamp, OmsIdentifyInfo* out) {
  if (oms_json.count("status") == 0 ||
      !oms_json.at("status").is_number_integer() ||
      oms_json.at("status").get<int>() != 200) {
    LOG(ERROR) << "OMS response error:\n" << oms_json.dump();
    return false;
  }

  if (oms_json.count("data") == 0) {
    LOG(ERROR) << "Not find data:\n" << oms_json.dump();
    return false;
  }

  if (oms_json.count("timestamp") == 0 ||
      !oms_json.at("timestamp").is_number_integer()) {
    LOG(ERROR) << "timestamp error:\n" << oms_json.dump();
    return false;
  }

  const auto oms_timestamp = oms_json.at("timestamp").get<int64_t>();
  if (*last_oms_timestamp > oms_timestamp) {
    LOG(ERROR) << "Network error, throw out old oms response.";
    return false;
  }
  *last_oms_timestamp = oms_timestamp;

  const auto& data = oms_json.at("data");
  if (!data.is_array() || data.empty()) {
    LOG(ERROR) << "data error:\n" << oms_json.dump();
    return false;
  }

  bool ret = true;
  for (size_t i = 0; i < data.size(); ++i) {
    const auto& algorithm_result_arr = data.at(i);
    if (!ParseOMSAlgorithmResult(oms_timestamp, algorithm_result_arr,
                                 vehicle_speed, out)) {
      ret = false;
      LOG(ERROR) << "Parse OMS algorithm result failed.";
      continue;
    }
  }

  return ret;
}

bool ParseBelonging(const json& belongling, TAITUIdentifyInfo* out) {
  try {
    robotaxi::pb::ItemLostInfo item_lost_info;
    item_lost_info.set_category(belongling.at("category").get<std::string>());
    item_lost_info.set_position(belongling.at("position").get<std::string>());
    out->add_lost_items()->Swap(&item_lost_info);
  } catch (...) {
    LOG(ERROR) << "Item lost result error:\n" << belongling.dump();
    return false;
  }
  return true;
}

bool TAITUJsonToPb(const json& oms_json, TAITUIdentifyInfo* out) {
  if (oms_json.count("code") == 0 || !oms_json.at("code").is_number_integer() ||
      oms_json.at("code").get<int>() != 0) {
    LOG(ERROR) << "TAITU response error:\n" << oms_json.dump();
    return false;
  }

  if (oms_json.count("data") == 0 || !oms_json.at("data").is_object()) {
    LOG(ERROR) << "TAITU data error:\n" << oms_json.dump();
    return false;
  }

  const auto& data = oms_json.at("data");
  if (data.count("belongings") == 0) {
    LOG(ERROR) << "TAITU no find belongings:\n" << data.dump();
    return false;
  }

  const auto& belongings = data.at("belongings");
  if (!belongings.is_array()) {
    LOG(ERROR) << "TAITU belongings error:\n" << belongings.dump();
    return false;
  }

  // It means no item lost.
  if (belongings.empty()) {
    return true;
  }

  bool ret = true;
  for (size_t i = 0; i < belongings.size(); ++i) {
    const auto& belongling = belongings.at(i);
    if (!ParseBelonging(belongling, out)) {
      ret = false;
      LOG(ERROR) << "Parse belonging failed.";
      continue;
    }
  }

  return ret;
}

}  // namespace

OmsManagerVNode::OmsManagerVNode()
    : fault_reporter_(
          node::FaultReporter(av_comm::component::kRobotaxiDeviceControl)),
      jushi_identify_pub_(nullptr),
      taitu_identify_pub_(nullptr),
      vehicle_speed_(0.0),
      vehicle_status_(VehicleOrderStatus::kUnknownOrderStatus) {}

bool OmsManagerVNode::Init() {
  if (!av_comm::GetHardwareConfig().robotaxi_device().oms_types_size()) {
    LOG(ERROR) << "|oms_types_size| is 0";
    return false;
  }

  const auto& config = av_comm::GetHardwareConfig().robotaxi_device();
  for (int i = 0; i < config.oms_types_size(); ++i) {
    switch (config.oms_types(i)) {
      case onboard_config::RobotaxiDevice_OmsType_JUSHI_X100: {
        auto callback = [this](StatusCode code, const json& oms_json) {
          CumulateNetworkError(code);
          if (code == StatusCode::kSuccess) {
            HandleJUSHIDeviceResponse(oms_json);
          } else {
            LOG(ERROR) << "Fail to get response from jushi_x100 server!";
          }
        };
        oms_client_vec_.push_back(
            {std::make_unique<OmsClient>(
                 &io_service_, kJUSHIXDEVICEHost, kJUSHIXDEVICEPort,
                 kJUSHIXDEVICEUrl, kJUSHIX100HttpBody, kJUSHIXDEVICEConnectType,
                 std::move(callback)),
             []() { return true; }});
        break;
      }
      case onboard_config::RobotaxiDevice_OmsType_TAITU: {
        auto callback = [this](StatusCode code, const json& oms_json) {
          CumulateNetworkError(code);
          if (code == StatusCode::kSuccess) {
            HandleTAITUResponse(oms_json);
          } else {
            LOG(ERROR) << "Fail to get response from taitu server!";
          }
        };
        oms_client_vec_.push_back(
            {std::make_unique<OmsClient>(
                 &io_service_, kTAITUHost, kTAITUPort, kTAITUUrl,
                 kTAITUHttpBody, kTAITUConnectType, std::move(callback)),
             [this]() {
               return vehicle_status_ == VehicleOrderStatus::kFinishingRide ||
                      vehicle_status_ == VehicleOrderStatus::kCanceling;
             }});
        break;
      }
      case onboard_config::RobotaxiDevice_OmsType_JUSHI_X100A: {
        auto callback = [this](StatusCode code, const json& oms_json) {
          CumulateNetworkError(code);
          if (code == StatusCode::kSuccess) {
            HandleJUSHIDeviceResponse(oms_json);
          } else {
            LOG(ERROR) << "Fail to get response from jushi_x100A server!";
          }
        };
        oms_client_vec_.push_back(
            {std::make_unique<OmsClient>(
                 &io_service_, kJUSHIXDEVICEHost, kJUSHIXDEVICEPort,
                 kJUSHIXDEVICEUrl, kJUSHIX100AHttpBody,
                 kJUSHIXDEVICEConnectType, std::move(callback)),
             []() { return true; }});
        break;
      }
      default: {
        LOG(ERROR) << "Unknown oms type.";
        break;
      }
    }
  }

  background_thread_ = std::make_unique<std::thread>([this]() {
    boost::asio::io_service::work work(io_service_);
    io_service_.run();
  });

  return true;
}

OmsManagerVNode::~OmsManagerVNode() {
  if (background_thread_ != nullptr) {
    io_service_.stop();
    background_thread_->join();
  }
}

void OmsManagerVNode::Callback(
    std::list<std::shared_ptr<const voy::Canbus>>& canbus_list,
    std::list<std::shared_ptr<const order::pb::OrderService>>& order_list) {
  if (!canbus_list.empty()) {
    vehicle_speed_ = canbus_list.back()->vehicle_speed();
  }
  if (!order_list.empty()) {
    vehicle_status_ = order_list.back()->status();
  }
  PostToOMSClient();
}

void OmsManagerVNode::PostToOMSClient() {
  for (size_t i = 0; i < oms_client_vec_.size(); ++i) {
    const auto& f = oms_client_vec_[i].second;
    if (f && f()) oms_client_vec_[i].first->PostToOMS();
  }
}

void OmsManagerVNode::CumulateNetworkError(StatusCode status_code) {
  const auto unavailable_time =
      network_recorder_.Record(status_code != StatusCode::kSuccess);
  if (unavailable_time > kNetworkErrorInSeconds) {
    const std::string error_msg = strings::StringPrintf(
        "Network unavailable, time: [%f] seconds.", unavailable_time);
    fault_reporter_.AddFault(pb::RobotaxiDeviceControlFaultCode::NETWORK_ERROR,
                             error_msg);
    // Post RTEvent when oms network error
    utils::RTEventProcessor::GetInstance()
        .PostRtEvent<
            rt_event::robotaxi_device_control::RobotaxiOMSConnectionLost>();
    LOG(ERROR) << error_msg;
  } else {
    fault_reporter_.RemoveFault(
        pb::RobotaxiDeviceControlFaultCode::NETWORK_ERROR);
  }
}

void OmsManagerVNode::HandleJUSHIDeviceResponse(const json& oms_json) {
  OmsIdentifyInfo oms_identify_info;
  if (!JUSHIXDeviceJsonToPb(oms_json, vehicle_speed_, &last_oms_timestamp_,
                            &oms_identify_info)) {
    fault_reporter_.AddFault(
        pb::RobotaxiDeviceControlFaultCode::PARSE_OMS_JSON_FAIL, "");
    LOG(ERROR) << "JUSHI derive json to pb failed.";
  } else {
    fault_reporter_.RemoveFault(
        pb::RobotaxiDeviceControlFaultCode::PARSE_OMS_JSON_FAIL);
    oms_identify_info.set_is_algorithm_available(true);
  }

  // For debug.
  {
    std::string jushi_device_msg;
    google::protobuf::util::JsonPrintOptions options;
    options.always_print_primitive_fields = true;
    google::protobuf::util::MessageToJsonString(oms_identify_info,
                                                &jushi_device_msg, options);
    if (last_jushi_device_msg_ != jushi_device_msg) {
      LOG(INFO) << "jushi msg: " << jushi_device_msg;
      std::swap(last_jushi_device_msg_, jushi_device_msg);
    }
  }

  oms_identify_info.set_timestamp(base::Now());

  if (!jushi_identify_pub_) {
    jushi_identify_pub_ =
        GetPublisherByName<OmsIdentifyInfo>("robotaxi_jushi_identify_pub");
  }

  if (jushi_identify_pub_) {
    jushi_identify_pub_->Publish(oms_identify_info);
  } else {
    LOG(ERROR) << "Failed to get |jushi_identify_pub|.";
  }
}

void OmsManagerVNode::HandleTAITUResponse(const json& oms_json) {
  TAITUIdentifyInfo taitu_identify_info;
  if (!TAITUJsonToPb(oms_json, &taitu_identify_info)) {
    fault_reporter_.AddFault(
        pb::RobotaxiDeviceControlFaultCode::TAITU_RESPONSE_ERROR, "");
    LOG(ERROR) << "TAITU json to pb failed.";
  } else {
    fault_reporter_.RemoveFault(
        pb::RobotaxiDeviceControlFaultCode::TAITU_RESPONSE_ERROR);
  }

  // For debug.
  {
    std::string taitu_msg;
    google::protobuf::util::JsonPrintOptions options;
    options.always_print_primitive_fields = true;
    google::protobuf::util::MessageToJsonString(taitu_identify_info, &taitu_msg,
                                                options);
    if (last_taitu_msg_ != taitu_msg) {
      LOG(INFO) << "taitu msg: " << taitu_msg;
      std::swap(last_taitu_msg_, taitu_msg);
    }
  }

  taitu_identify_info.set_timestamp(base::Now());

  if (!taitu_identify_pub_) {
    taitu_identify_pub_ =
        GetPublisherByName<TAITUIdentifyInfo>("robotaxi_taitu_identify_pub");
  }

  if (taitu_identify_pub_) {
    taitu_identify_pub_->Publish(taitu_identify_info);
  } else {
    LOG(ERROR) << "Failed to get |taitu_identify_pub|.";
  }
}

REGISTER_VNODE(OmsManagerVNode)
}  // namespace robotaxi_device_control
