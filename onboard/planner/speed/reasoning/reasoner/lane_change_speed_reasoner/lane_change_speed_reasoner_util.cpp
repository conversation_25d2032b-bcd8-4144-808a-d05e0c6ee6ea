#include "planner/speed/reasoning/reasoner/lane_change_speed_reasoner/lane_change_speed_reasoner_util.h"

#include <algorithm>
#include <limits>
#include <optional>
#include <unordered_map>
#include <vector>

#include "log_utils/log_macros.h"
#include "planner/elective_lane_change/elective_lane_change_decider_utils.h"

namespace planner {
namespace speed {
namespace reasoner {
namespace {
constexpr double kStopLineDistanceBufferForSoftConstraintInMeter = 3.0;
constexpr double kStopLineDistanceBufferForHardConstraintInMeter = 9.0;

// Gets the yield arc length for a overlap_slice considering reasoning buffers.
double GetYieldArcLength(const pb::OverlapSlice& overlap_slice,
                         const double yield_extra_distance,
                         const double yield_extra_time) {
  const double overlap_slice_start_arclength =
      GetPaddedOverlapStart(overlap_slice);
  return overlap_slice_start_arclength - yield_extra_distance -
         yield_extra_time * overlap_slice.signed_longitudinal_speed();
}

// Gets the remaining lane change distance from ego pose.
// NOTE: We only check the distance from current ego pose to the next mandatory
// LC point. We do not get the distance from now to next junction. This is to
// avoid LC into slow moving object due to being close to the next junction
// while ego can still go straight.
double GetRemainingLaneChangeDistance(
    const bool is_in_junction,
    const std::optional<double>& distance_to_last_lc_chance_point,
    const double average_remaining_distance_per_lane_change) {
  constexpr double kMaxRemainingLaneChangeDistanceFromEgoInMeter = 500.0;
  const double last_lc_point_dist = distance_to_last_lc_chance_point.value_or(
      kMaxRemainingLaneChangeDistanceFromEgoInMeter);
  if (is_in_junction) {
    // When ego is in junction, distance_to_last_lc_chance_point can be 0.
    // TODO(lane-change): Revisit if this is a bug or contract from routing.
    return average_remaining_distance_per_lane_change;
  }

  return std::min(last_lc_point_dist,
                  average_remaining_distance_per_lane_change);
}

// Returns true if the object encroaches ego LK trajectory through proximity
// info.
bool DoesObjectEncroachEgoLKTrajectory(
    const std::vector<PredictionDecisionMakerInput>& reasoning_inputs,
    const int64_t object_id) {
  for (const PredictionDecisionMakerInput& reasoning_input : reasoning_inputs) {
    if (reasoning_input.reasoning_object.id() != object_id) {
      continue;
    }

    return std::abs(reasoning_input.reasoning_object.object_proximity_info()
                        .signed_lateral_gap()) < math::constants::kEpsilon;
  }

  return false;
}

// Returns true if lane change now may trigger ELC to change back due to being
// behind a low moving agent. If so, we should lane change later.
bool CurrentLaneChangeMayCauseELCBack(
    const WorldContext& world_context,
    const LaneChangeObjectInfoList& lane_change_object_info_list,
    const LaneChangeObjectInfo& lane_change_object_info,
    const PlannerObject& planner_object,
    const double remaining_lane_change_distance_from_ego_pose,
    const bool has_queue_object_in_front, const bool is_source_region_slower,
    const bool has_red_light_ahead, const bool is_ego_in_junction,
    const bool is_ego_near_merge_lane, std::ostringstream& debug_oss) {
  const pnc_map::Lane* route_associated_lane =
      lane_change_object_info.agent_associated_lane_on_target_region;
  if (route_associated_lane == nullptr &&
      planner_object.associated_lanes().empty()) {
    debug_oss << __func__
              << ": false (Unknown agent associated current lane.)\n";
    return false;
  }
  const pnc_map::Lane& object_current_lane =
      route_associated_lane != nullptr
          ? *route_associated_lane
          : *planner_object.associated_lanes().front();
  if (!ShouldCheckELCScoreForTargetRegionAgent(
          lane_change_object_info_list, lane_change_object_info, planner_object,
          remaining_lane_change_distance_from_ego_pose,
          has_queue_object_in_front, world_context.heading(),
          is_source_region_slower, has_red_light_ahead, is_ego_near_merge_lane,
          debug_oss)) {
    debug_oss << __func__ << ": false (Should not check ELC score)\n";
    return false;
  }

  const double elc_score = ComputeELCScoreForGapAlign(
      planner_object, lane_change_object_info, &object_current_lane,
      world_context.order_start_time_info(),
      world_context.robot_state().plan_init_state_snapshot().speed(),
      remaining_lane_change_distance_from_ego_pose, is_ego_in_junction,
      debug_oss);
  // Currently ELC score is nearly binary, so this threshold does not matter as
  // long as it is smaller than 1.0.
  constexpr double kElectiveLaneChangeScoreThreshold = 0.8;
  if (elc_score < kElectiveLaneChangeScoreThreshold) {
    debug_oss << __func__ << ": false (ELC score " << elc_score
              << " lower than threshold: " << kElectiveLaneChangeScoreThreshold
              << ")\n";
    return false;
  }
  return true;
}

// Returns true if we should decrease yield kinematic params.
bool ShouldDecreaseYieldKinematicParams(
    const PlannerObject& planner_object,
    const LaneChangeObjectInfo& object_info,
    const std::optional<double>& dist_to_last_lc_chance_point) {
  if (object_info.object_ego_dist < 0.0) {
    return false;
  }

  if (planner_object.is_cyclist()) {
    return false;
  }

  // Return true directly if the object is in queue.
  if (object_info.is_in_queue) {
    return true;
  }

  if (!dist_to_last_lc_chance_point.has_value()) {
    return false;
  }

  // Return false if the distance to last lc chance point is more than 300m.
  constexpr double kMaxDistanceToLastLCChancePointInMeter = 300.0;
  if (dist_to_last_lc_chance_point.value() >
      kMaxDistanceToLastLCChancePointInMeter) {
    return false;
  }

  // Return false if there is a slow vehicle having no vehicle ahead before 50m
  // from last lc chance point.
  constexpr double kMaxSpeedToConsiderAsSlowVehicleInMps = 5.0;
  constexpr double kMinDistanceToConsiderSlowVehicleInMeter = 50.0;
  if (dist_to_last_lc_chance_point.value() >
          kMinDistanceToConsiderSlowVehicleInMeter &&
      planner_object.speed() < kMaxSpeedToConsiderAsSlowVehicleInMps &&
      !object_info.has_congestion_ahead) {
    return false;
  }

  return true;
}

// Returns true if we should increase yield min speed.
bool ShouldIncreaseYieldMinSpeed(
    const LaneChangeObjectInfo& object_info,
    const bool has_enough_room_for_speed_up,
    const bool is_ego_overtaking_nearby_static_vehicle) {
  if (object_info.object_ego_dist > 0.0) {
    return false;
  }

  // Do not increase yield min v if speed up room is limited.
  // TODO(Huoliang): Consider update this condition since we already have larger
  // delta v when urgency score is high.
  if (!has_enough_room_for_speed_up) {
    return false;
  }

  // Do not increase yield min v if ego is overtaking a static agent. This
  // avoids ego squeeze with the agents on the target lanes.
  if (is_ego_overtaking_nearby_static_vehicle) {
    return false;
  }

  return true;
}

}  // namespace

double GetSpeedUsingConstantAccelModel(const double init_speed,
                                       const double target_speed,
                                       const double a, const double t) {
  DCHECK_NE(a, 0.0);
  const double target_speed_time = (target_speed - init_speed) / a;
  DCHECK_GE(target_speed_time, 0.0);

  return t < target_speed_time ? (init_speed + a * t) : target_speed;
}

double GetOdomUsingConstantAccelModel(const double init_speed,
                                      const double target_speed, const double a,
                                      const double t) {
  DCHECK_NE(a, 0.0);
  const double target_speed_time = (target_speed - init_speed) / a;
  DCHECK_GE(target_speed_time, 0.0);

  return t < target_speed_time
             ? (init_speed * t + 0.5 * a * t * t)
             : (init_speed * target_speed_time +
                0.5 * a * target_speed_time * target_speed_time +
                target_speed * (t - target_speed_time));
}

double GetAccelUsingConstantAccelModel(const double init_speed,
                                       const double target_speed,
                                       const double target_time,
                                       const double target_odom) {
  if (target_speed * target_time == target_odom) {
    return 0.0;
  }

  return 0.5 * math::Sqr(init_speed - target_speed) /
         (target_speed * target_time - target_odom);
}

double GetTargetYieldSpeedUsingConstantAccelModel(
    const double init_speed, const double target_yield_time,
    const double target_odom, const double a) {
  DCHECK_NE(a, 0.0);
  const double T = target_yield_time;
  const double delta = T * T + 2 * (init_speed * T - target_odom) / a;

  double target_yield_min_speed = 0.0;
  if (delta < 0) {
    target_yield_min_speed = init_speed + a * T;
  } else {
    const double t1 = T - std::sqrt(delta);
    DCHECK_GT(t1, -math::constants::kEpsilon);
    target_yield_min_speed = init_speed + a * t1;
  }

  return std::max(target_yield_min_speed, math::constants::kEpsilon);
}

bool IsSafeToYieldForGapAlign(const double ego_acceleration,
                              const double safe_yield_acceleration,
                              const double generic_urgency_score,
                              const bool was_safe_to_yield) {
  // Return the maximum yield max active time when the safe yield
  // acceleration is inf.
  if (std::isinf(safe_yield_acceleration)) {
    return true;
  }

  // The delta_safe_yield_acceleration measures how close ego's current
  // acceleration is close to the safe_yield_acceleration.
  const double delta_safe_yield_acceleration =
      ego_acceleration - safe_yield_acceleration;

  // Calculate the delta safe yield acceleration threshold. Owing to the control
  // inertia, the ego_acceleration should not be smaller than
  // safe_yield_acceleration to avoid rear-bumper collision risk. According to
  // the urgency score, the threshold has three level to interpolate. For
  // example, if urgency score is 0.0, the boundary is [0.5, 0.8]. If the
  // urgency score is 1.0, the boundary is [0.2, 0.5]. The larger urgency score,
  // the smaller boundary is.
  constexpr double kMinimumDeltaSafeYieldAccelerationThresholdInMpss = 0.2;
  constexpr double kMediumDeltaSafeYieldAccelerationThresholdInMpss = 0.5;
  constexpr double kMaximumDeltaSafeYieldAccelerationThresholdInMpss = 0.8;
  const double threshold_to_continue_yield = math::LinearInterpolate(
      kMediumDeltaSafeYieldAccelerationThresholdInMpss,
      kMinimumDeltaSafeYieldAccelerationThresholdInMpss, generic_urgency_score);
  const double threshold_to_start_yield = math::LinearInterpolate(
      kMaximumDeltaSafeYieldAccelerationThresholdInMpss,
      kMediumDeltaSafeYieldAccelerationThresholdInMpss, generic_urgency_score);
  const double delta_safe_yield_acceleration_threshold =
      was_safe_to_yield ? threshold_to_continue_yield
                        : threshold_to_start_yield;

  // Return true if the delta safe yield acceleration is larger than the
  // threshold.
  return delta_safe_yield_acceleration >
         delta_safe_yield_acceleration_threshold;
}

// API to get the suggested pass maximum accel.
double GetSuggestedPassMaximumAccel(const double generic_urgency_score) {
  // Suggested pass maximum accel is closely related to urgency score, which
  // following the mapping f: [0.0, 1.0] --> [1.0, 1.5].
  constexpr double kLeastUrgencyGapAlignPassMaxAccelInMpss = 1.0;
  constexpr double kMostUrgencyGapAlignPassMaxAccelInMpss = 1.5;
  return math::LinearInterpolate(kLeastUrgencyGapAlignPassMaxAccelInMpss,
                                 kMostUrgencyGapAlignPassMaxAccelInMpss,
                                 generic_urgency_score);
}

// TODO(lane change): Improve the logic for better behavior according to
// motivation cases.
// TODO(lane change): Consider using source lane leading vehicle's trajectory to
// clamp the pass_max_v of the gap align constraints in front of ego on the
// target lane. It helps to avoid unreasonable speeding before gap is aligned.
double GetSuggestedPassMaximumSpeed(const double ego_speed,
                                    const double agent_will_see_ego_time,
                                    const double agent_max_speed) {
  double suggested_pass_max_speed = std::numeric_limits<double>::infinity();
  if (math::NearZero(agent_will_see_ego_time)) {
    // We allow 6 km/h extra speed more than agent max speed.
    constexpr double kAlignPassExtraSpeedInMps = 1.67;
    // If the agent speed is relatively lower, we use current ego speed to
    // pass the agent.
    suggested_pass_max_speed =
        std::max(ego_speed, agent_max_speed + kAlignPassExtraSpeedInMps);
  } else {
    // Agent will see ego car in the future. We do not want to speed up to
    // aggressively overtake the agent.
    suggested_pass_max_speed = ego_speed;
  }
  return std::max(math::constants::kEpsilon, suggested_pass_max_speed);
}

double GetSuggestedPassMaximumJerk(const bool ahead_of_last_tail,
                                   const double generic_urgency_score) {
  constexpr double kDefaultGapAlignPassMaximumJerk = 2.0;
  constexpr double kDecreasedGapAlignPassMaximumJerk = 1.5;
  return ahead_of_last_tail
             ? math::LinearInterpolate(kDecreasedGapAlignPassMaximumJerk,
                                       kDefaultGapAlignPassMaximumJerk,
                                       generic_urgency_score)
             : kDefaultGapAlignPassMaximumJerk;
}

// API to get the suggested Pass extra distance.
double GetSuggestedPassExtraDistance(const double generic_urgency_score) {
  constexpr double kLeastUrgencyPassExtraDistanceForNormalUrgencyInMeter = 0.1;
  constexpr double kLeastUrgencyPassExtraDistanceForGenericUrgencyInMeter =
      0.15;
  constexpr double kMostUrgencyPassExtraDistanceInMeter = 0.0;
  return math::LinearInterpolate(
      FLAGS_planning_enable_gap_align_urgency_score_for_lane_change
          ? kLeastUrgencyPassExtraDistanceForGenericUrgencyInMeter
          : kLeastUrgencyPassExtraDistanceForNormalUrgencyInMeter,
      kMostUrgencyPassExtraDistanceInMeter, generic_urgency_score);
}

// API to get the suggested pass extra time.
double GetSuggestedPassExtraTime(const double generic_urgency_score) {
  constexpr double kLeastUrgencyPassExtraTimeForNormalUrgencyInSec = 0.1;
  constexpr double kLeastUrgencyPassExtraTimeForGenericUrgencyInSec = 0.15;
  constexpr double kMostUrgencyPassExtraTimeInSec = 0.0;
  return math::LinearInterpolate(
      FLAGS_planning_enable_gap_align_urgency_score_for_lane_change
          ? kLeastUrgencyPassExtraTimeForGenericUrgencyInSec
          : kLeastUrgencyPassExtraTimeForNormalUrgencyInSec,
      kMostUrgencyPassExtraTimeInSec, generic_urgency_score);
}

// API to get the suggested yield minimum speed.
double GetSuggestedYieldMinimumSpeed(const double ego_speed,
                                     const double agent_speed,
                                     const double generic_urgency_score) {
  // Calculate the delta speed for the smaller speed agent. In the most urgency
  // case, ego should slow down to the clamped yield min v so that ego can yield
  // to a stationary agent on the target lane. The clamped yield min v is
  // defined as the smallest yield speed that ego can achieve.
  constexpr double kLeastUrgencyYieldDeltaSpeedForSmallerSpeedAgentInMps = 3.0;
  const double most_urgency_yield_delta_speed_for_smaller_speed_agent =
      std::max(ego_speed,
               kLeastUrgencyYieldDeltaSpeedForSmallerSpeedAgentInMps);
  const double delta_speed_for_smaller_speed_agent = math::LinearInterpolate(
      kLeastUrgencyYieldDeltaSpeedForSmallerSpeedAgentInMps,
      most_urgency_yield_delta_speed_for_smaller_speed_agent,
      generic_urgency_score);

  // Calculate the delta speed for the larger speed agent. In the most urgency
  // case, ego expands the valid agent speed range. The same high speed agent
  // will make ego slow down a litter bit more. But still no need for huge delta
  // speed.
  constexpr double kLeastUrgencyYieldDeltaSpeedForLargerSpeedAgentInMps = 4.5;
  constexpr double kMostUrgencyYieldDeltaSpeedForLargerSpeedAgentInMps = 6.0;
  const double delta_speed_for_larger_speed_agent = math::LinearInterpolate(
      kLeastUrgencyYieldDeltaSpeedForLargerSpeedAgentInMps,
      kMostUrgencyYieldDeltaSpeedForLargerSpeedAgentInMps,
      generic_urgency_score);

  // Calculate the clamped yield min v. In the most urgency case, the clamped
  // yield minimum speed should be related to buffer which is set by the initial
  // decision provider.
  constexpr double kLeastUrgencyYieldWithMinSpeedInMps = 3.0;
  constexpr double kMostUrgencyYieldWithMinSpeedInMps = 0.0;
  const double clamped_yield_minimum_speed = math::LinearInterpolate(
      kLeastUrgencyYieldWithMinSpeedInMps, kMostUrgencyYieldWithMinSpeedInMps,
      generic_urgency_score);

  // Ego should not slow down a lot if agent speed is larger than ego speed with
  // a upper bound buffer.
  const math::Range1d valid_agent_speed_range{
      0.0, ego_speed + delta_speed_for_larger_speed_agent};
  const double clamped_agent_speed =
      std::min(agent_speed, valid_agent_speed_range.end_pos);
  const math::Range1d delta_speed_range{delta_speed_for_smaller_speed_agent,
                                        delta_speed_for_larger_speed_agent};

  const double delta_speed = math::GetLinearInterpolatedY(
      valid_agent_speed_range.start_pos, valid_agent_speed_range.end_pos,
      delta_speed_range.start_pos, delta_speed_range.end_pos,
      clamped_agent_speed);

  // Return the maximum value between the suggested speed and baseline speed.
  // The suggested speed consider the maximum speed between ego and agent
  // because ego should slow down smaller if agent speed is larger than ego.
  return std::max(std::max(ego_speed, clamped_agent_speed) - delta_speed,
                  clamped_yield_minimum_speed);
}

// API to get the suggested yield minimum accel.
double GetSuggestedYieldMinimumAccel(const double safe_yield_acceleration,
                                     const double generic_urgency_score) {
  // Suggested yield minimum accel is closely related to urgency score, which
  // following the mapping f: [0.0, 1.0] --> [-1.0, -2.0]. It means that the
  // allowed minimum and maximum deceleration, in mpss, are -2.0 and -1.0,
  // respectively. As urgency score increases, the yield minimum
  // accel increases with 0.5mpss simultaneously.
  // TODO(howardgao): explore dynamic range here.
  constexpr double kLeastUrgencyGapAlignYieldMinAccelInMpss = -1.0;
  constexpr double kMostUrgencyGapAlignYieldMinAccelInMpss = -2.0;
  const double normal_yield_accel = math::LinearInterpolate(
      kLeastUrgencyGapAlignYieldMinAccelInMpss,
      kMostUrgencyGapAlignYieldMinAccelInMpss, generic_urgency_score);

  return std::max(safe_yield_acceleration, normal_yield_accel);
}

double GetSuggestedYieldMinimumJerk(const bool behind_last_lead,
                                    const double generic_urgency_score) {
  constexpr double kDefaultGapAlignYieldMinJerkInMpss = -1.0;
  constexpr double kIncreasedGapAlignYieldMinJerkInMpss = -0.5;
  return behind_last_lead
             ? math::LinearInterpolate(kIncreasedGapAlignYieldMinJerkInMpss,
                                       kDefaultGapAlignYieldMinJerkInMpss,
                                       generic_urgency_score)
             : kDefaultGapAlignYieldMinJerkInMpss;
}

// API to get the suggested yield extra distance.
double GetSuggestedYieldExtraDistance(const double generic_urgency_score) {
  constexpr double kLeastUrgencyYieldExtraDistanceForNormalUrgencyInMeter =
      0.25;
  constexpr double kLeastUrgencyYieldExtraDistanceForGenericUrgencyInMeter =
      0.4;
  constexpr double kMostUrgencyYieldExtraDistanceInMeter = 0.0;
  return math::LinearInterpolate(
      FLAGS_planning_enable_gap_align_urgency_score_for_lane_change
          ? kLeastUrgencyYieldExtraDistanceForGenericUrgencyInMeter
          : kLeastUrgencyYieldExtraDistanceForNormalUrgencyInMeter,
      kMostUrgencyYieldExtraDistanceInMeter, generic_urgency_score);
}

// API to get the suggested yield extra time.
double GetSuggestedYieldExtraTime(const double generic_urgency_score) {
  constexpr double kLeastUrgencyYieldExtraTimeForNormalUrgencyInSec = 0.2;
  constexpr double kLeastUrgencyYieldExtraTimeForGenericUrgencyInSec = 0.3;
  constexpr double kMostUrgencyYieldExtraTimeInSec = 0.0;
  return math::LinearInterpolate(
      FLAGS_planning_enable_gap_align_urgency_score_for_lane_change
          ? kLeastUrgencyYieldExtraTimeForGenericUrgencyInSec
          : kLeastUrgencyYieldExtraTimeForNormalUrgencyInSec,
      kMostUrgencyYieldExtraTimeInSec, generic_urgency_score);
}

void SetPassWithMaxSpeedAccelJerk(const double pass_max_v,
                                  const double pass_max_a,
                                  const double pass_max_j, const bool is_soft,
                                  ConstraintMutator& mutator) {
  is_soft ? mutator.set_soft_pass_with_max_speed_accel_jerk(
                pass_max_v, pass_max_a, pass_max_j)
          : mutator.set_pass_if_possible_with_max_speed_accel_jerk(
                pass_max_v, pass_max_a, pass_max_j);
}

void SetYieldWithMinSpeedAccelJerk(const double yield_min_v,
                                   const double yield_min_a,
                                   const double yield_min_j, const bool is_soft,
                                   ConstraintMutator& mutator) {
  is_soft ? mutator.set_soft_yield_with_min_speed_accel_jerk(
                yield_min_v, yield_min_a, yield_min_j)
          : mutator.set_yield_if_possible_with_min_speed_accel_jerk(
                yield_min_v, yield_min_a, yield_min_j);
}

std::optional<double> GetStopConstraintDistanceFromRAForUrgencyInfo(
    const bool can_trigger_crawl, const bool is_inexecutable_lc_signal,
    const LaneChangeUrgencyInfo& urgency_info,
    const RobotStateSnapshot& snapshot, const double safe_yield_acceleration,
    std::ostringstream& debug_oss) {
  const double urgency_dist_from_ra = urgency_info.urgency_dist();
  const bool is_soft = urgency_info.stop_line_type() ==
                       planner::pb::LaneChangeStopLineType::SOFT_STOP_LINE;
  // We set the stop constraint to earlier than the urgency point to increase
  // lane change success rate.
  const double earlier_stop_constraint_buffer =
      (!is_soft && can_trigger_crawl)
          ? kStopLineDistanceBufferForHardConstraintInMeter
          : kStopLineDistanceBufferForSoftConstraintInMeter;
  const vehicle_model::pb::AxleRectangularMeasurement& shape_measurement =
      snapshot.car_model_with_shape().shape_measurement();
  const double ra_to_fb =
      shape_measurement.length() - shape_measurement.rear_bumper_to_rear_axle();
  const double nominal_stop_line_distance_from_fb =
      urgency_dist_from_ra - earlier_stop_constraint_buffer - ra_to_fb;
  debug_oss << "\n" << __func__ << ": ";
  // For soft urgency info, when ego is close to the urgency point and still
  // hasn't entered lane change homotopy, we deem the lane change as too
  // difficult and stop adding the constraint.
  constexpr double kDistanceToRemoveForSoftConstraintInM = 10.0;
  if (is_soft && nominal_stop_line_distance_from_fb <
                     kDistanceToRemoveForSoftConstraintInM) {
    debug_oss << "Ego front bumper is within "
              << kDistanceToRemoveForSoftConstraintInM
              << "m to the stop constraint ("
              << nominal_stop_line_distance_from_fb
              << "m), removing stop constraint for soft urgency info.\n";
    return std::nullopt;
  }

  // The strategy of braking in gap-align mode has 3 branches:
  // - When ego can brake smoothly to stop before the nominal stop line, set the
  // stop constraint at the nominal stop line.
  // - When ego can't stop before the nominal stop line with smooth stopping but
  // can stop *near* the nominal stop line, set the stop constraint at the
  // smooth stopping distance.
  // - When ego can't stop *near* the nominal stop line with smooth stopping,
  // for soft urgency info, don't add stop constraint. For hard urgency info,
  // set the stop constraint at the smooth stopping distance.

  // Calculate the stopping distance with a set of smooth stopping parameters.
  constexpr double kSoftSmoothStoppingMinAccelInMpss = -1.5;
  constexpr double kHardSmoothStoppingMinAccelInMpss = -2.5;
  const double smoothing_min_accel = is_soft
                                         ? kSoftSmoothStoppingMinAccelInMpss
                                         : kHardSmoothStoppingMinAccelInMpss;
  constexpr double kSmoothStoppingMinJerkInMpsss = -1.5;
  constexpr double kSmoothStoppingMaxJerkInMpsss = 1.5;
  const physics::MM1dLimitInfo smooth_limit{
      .a_min = std::max(safe_yield_acceleration, smoothing_min_accel),
      .j_min = kSmoothStoppingMinJerkInMpsss,
      .j_max = kSmoothStoppingMaxJerkInMpsss};
  const std::optional<physics::ExtremeStoppingInfo> smooth_stopping_info =
      physics::ComputeExtremeStoppingInfo(
          snapshot.speed(), /*a0=*/
          std::max(snapshot.acceleration(),
                   safe_yield_acceleration + math::constants::kEpsilon),
          smooth_limit);
  if (!smooth_stopping_info.has_value()) {
    debug_oss << "Unable to calculate smooth stopping distance.\n";
    return std::nullopt;
  }
  double smooth_stopping_distance = smooth_stopping_info.value().distance_m;
  if (is_inexecutable_lc_signal) {
    static constexpr double kDistBufferForInexecutableLcInMeter = 2.0;
    smooth_stopping_distance += kDistBufferForInexecutableLcInMeter;
    debug_oss << "Add stopping distance buffer "
              << kDistBufferForInexecutableLcInMeter
              << "m for inexecutable lc.\n";
  }
  if (smooth_stopping_distance < nominal_stop_line_distance_from_fb) {
    debug_oss << "Smooth stopping distance " << smooth_stopping_distance
              << "m is less than the nominal stop line distance from fb "
              << nominal_stop_line_distance_from_fb << "m.\n";
    return nominal_stop_line_distance_from_fb + ra_to_fb;
  }

  constexpr double kNearNominalStopLineBufferInM = 2.0;
  if (smooth_stopping_distance <
      nominal_stop_line_distance_from_fb + kNearNominalStopLineBufferInM) {
    debug_oss << "Smooth stopping distance " << smooth_stopping_distance
              << "m is within " << kNearNominalStopLineBufferInM
              << "m buffer from the nominal stop line distance from fb "
              << nominal_stop_line_distance_from_fb
              << "m. Using smooth stopping distance.\n";
    return smooth_stopping_distance + ra_to_fb;
  }

  if (is_soft) {
    debug_oss << "Don't add stop constraint for soft urgency info. smooth "
                 "stopping distance: "
              << smooth_stopping_distance
              << "m, buffer: " << kNearNominalStopLineBufferInM
              << "m, stop line distance from fb: "
              << nominal_stop_line_distance_from_fb << "m.\n";
    return std::nullopt;
  }

  debug_oss << "Using " << DUMP_TO_STREAM(smooth_stopping_distance)
            << "m for hard urgency constraint when it exceeds nominal stop "
               "line distance from fb "
            << nominal_stop_line_distance_from_fb << "m.\n";
  return smooth_stopping_distance + ra_to_fb;
}

bool GetSpeedLimitInfoFromUrgencyInfo(
    const LaneChangeUrgencyInfo& urgency_info, const double safe_yield_acc,
    const double ego_speed, double& limit_arc_length, double& min_comfort_speed,
    double& min_acc, std::ostringstream& debug_oss) {
  constexpr double kImplicitLaneChangeEndMinSpeedInMps = 5.0;
  constexpr double kSoftLaneChangeEndMinSpeedInMps = 3.0;
  const double lane_change_end_min_speed =
      urgency_info.stop_line_type() ==
              planner::pb::LaneChangeStopLineType::IMPLICIT_STOP_LINE
          ? kImplicitLaneChangeEndMinSpeedInMps
          : kSoftLaneChangeEndMinSpeedInMps;

  constexpr double kLaneChangeInProgressDurationInSec = 3.0;
  constexpr double kLaneChangePreparationDurationInSec = 6.0;

  const double total_remaining_distance = urgency_info.urgency_dist();
  const double clamped_ego_speed =
      std::max(ego_speed, lane_change_end_min_speed);
  constexpr double kLaneChangeTotalDuration =
      kLaneChangeInProgressDurationInSec + kLaneChangePreparationDurationInSec;
  const double comfortable_total_span =
      clamped_ego_speed * kLaneChangeTotalDuration;

  if (total_remaining_distance > comfortable_total_span) {
    debug_oss << __func__ << ": Total remaining distance "
              << total_remaining_distance
              << "m is enough to finish comfortable total span "
              << comfortable_total_span << "m.\n";
    return false;
  }

  constexpr double kLaneChangeEndMinAccInMpss = -1.5;
  // Ego's min speed when entering lane change in progress state.
  const double lane_change_in_progress_min_speed =
      lane_change_end_min_speed -
      kLaneChangeEndMinAccInMpss * kLaneChangeInProgressDurationInSec;
  [[maybe_unused]] const double clamped_ego_speed_in_progress =
      std::max(ego_speed, lane_change_in_progress_min_speed);
  const double min_span_for_lane_change_in_progress =
      0.5 * (lane_change_in_progress_min_speed + lane_change_end_min_speed) *
      kLaneChangeInProgressDurationInSec;

  constexpr double kImplicitLaneChangeMinAccForPreparationInMpss = -0.5;
  constexpr double kSoftLaneChangeMinAccForPreparationInMpss = -1.2;
  const double lane_change_preparation_min_acc =
      urgency_info.stop_line_type() ==
              planner::pb::LaneChangeStopLineType::IMPLICIT_STOP_LINE
          ? kImplicitLaneChangeMinAccForPreparationInMpss
          : kSoftLaneChangeMinAccForPreparationInMpss;
  const double clamped_min_acc =
      std::max(safe_yield_acc, lane_change_preparation_min_acc);
  constexpr double kMinDistanceForDeceleration = 10.0;
  if (total_remaining_distance < min_span_for_lane_change_in_progress) {
    debug_oss << __func__ << ": Total remaining distance "
              << total_remaining_distance
              << "m is not enough to finish min span for in progress "
              << min_span_for_lane_change_in_progress << "m.\n";
    limit_arc_length = kMinDistanceForDeceleration;
    min_comfort_speed = lane_change_end_min_speed;
    min_acc = clamped_min_acc;
    return true;
  }

  const double preparation_remaining_distance =
      total_remaining_distance - min_span_for_lane_change_in_progress;
  limit_arc_length =
      std::max(preparation_remaining_distance, kMinDistanceForDeceleration);
  min_comfort_speed = lane_change_in_progress_min_speed;
  min_acc = clamped_min_acc;
  return true;
}

bool IsBlockageObjectBeforeArcLength(
    const double obj_source_region_arc_length,
    const double lc_start_source_region_arc_length,
    const planner::PlannerObject& planner_object) {
  if (!planner_object.is_blockage_object()) {
    return false;
  }
  return obj_source_region_arc_length < lc_start_source_region_arc_length;
}

bool IsLikelyParkedCar(
    const planner::LaneChangeObjectInfo& object_info,
    const planner::PlannerObject& planner_object,
    const std::optional<double>& distance_to_last_lc_chance_point,
    const bool is_lc_backup_sequence, std::ostringstream& debug_oss) {
  // Ignore non-stationary agents.
  if (!planner_object.is_primary_stationary()) {
    return false;
  }

  // Don't delay LC for agents beyond last lane change point.
  // We only ignore agents that are fully beyond the last lane change point, so
  // we use their rear bumper s here.
  if (distance_to_last_lc_chance_point.has_value() &&
      object_info.object_ego_dist - 0.5 * planner_object.length() >
          distance_to_last_lc_chance_point) {
    return false;
  }

  // Return false if LC scene understanding thinks the agent is in a queue.
  if (object_info.is_in_queue) {
    return false;
  }

  // Return false if the agent is not in the rightmost lane.
  if (!object_info.is_in_rightmost_lane) {
    return false;
  }

  // Return true when the parked car confidence is high.
  // NOTE: Because this signal may have FP and cause lane change late, we put it
  // behind the queue signal and rightmost lane scenario only.
  if (object_info.is_likely_parked_car_from_score) {
    debug_oss << __func__ << ": " << object_info.id
              << " is likely a parked car from score "
              << object_info.is_likely_parked_car_from_score << ".\n";
    return true;
  }

  if (planner_object.HasPerceptionAttribute(
          voy::perception::Attribute::VEHICLE_BRAKE_SIGNAL_OFF)) {
    debug_oss << __func__ << ": " << object_info.id << " brake signal is off"
              << ".\n";
    return true;
  }

  if (planner_object.is_blockage_object() && is_lc_backup_sequence) {
    // TODO(Tingran/Elliot): Consider a more robust logic to resolve the back up
    // sequence problem.
    debug_oss << __func__ << ": " << planner_object.id()
              << " is blockage object and is is_lc_backup_sequence which may "
                 "lead to a parked car. \n";
    return true;
  }

  // Return true if agent is far away from the ego. Perception won't output
  // parked car signal when this happens.
  constexpr double kMinDistanceForStaticObjectFarAwayInMeter = 50.0;
  if (object_info.object_ego_dist > kMinDistanceForStaticObjectFarAwayInMeter) {
    debug_oss << __func__ << ": " << object_info.id
              << " is likely a parked car as it's "
              << object_info.object_ego_dist
              << "m away from ego, not in queue, and on the rightmost lane.\n";
    return true;
  }

  return false;
}

// Returns true if we should not lane change behind a slow pedestrian.
bool ShouldAvoidLaneChangeBehindSlowPed(
    const std::tm* order_start_time_info, const PlannerObject& planner_object,
    const LaneChangeObjectInfo& lane_change_object_info,
    const planner::pb::LaneChangeMode lc_direction,
    const std::optional<double>& distance_to_last_lc_chance_point,
    std::ostringstream& debug_oss) {
  if (!IsNightScene(order_start_time_info)) {
    debug_oss << __func__ << ": false (Not night scene.)\n";
    return false;
  }
  if (!planner_object.is_pedestrian()) {
    debug_oss << __func__ << ": false (Not pedestrian.)\n";
    return false;
  }
  constexpr double kSlowPedSpeedThresholdInMps = 0.3;
  if (planner_object.speed() > kSlowPedSpeedThresholdInMps) {
    debug_oss << __func__ << ": false (Not slow pedestrian.)\n";
    return false;
  }
  if (lc_direction != planner::pb::LaneChangeMode::RIGHT_LANE_CHANGE) {
    debug_oss << __func__ << ": false (Not right lane change.)\n";
    return false;
  }

  constexpr double kSlowPedDistanceThresholdInMeter = 30.0;
  if (!distance_to_last_lc_chance_point.has_value() ||
      distance_to_last_lc_chance_point.value() -
              lane_change_object_info.object_ego_dist <
          kSlowPedDistanceThresholdInMeter) {
    debug_oss << __func__ << ": false (Close to last lane change point.)\n";
    return false;
  }

  debug_oss << __func__ << ": true\n";
  return true;
}

bool ShouldCheckELCScoreForTargetRegionAgent(
    const LaneChangeObjectInfoList& lane_change_object_info_list,
    const LaneChangeObjectInfo& lane_change_object_info,
    const PlannerObject& planner_object,
    const double remaining_lane_change_distance_from_ego_pose,
    const bool has_queue_object_in_front, const double ego_heading,
    const bool is_source_region_slower, const bool has_red_light_ahead,
    const bool is_ego_near_merge_lane, std::ostringstream& debug_oss) {
  // Returns false if the target region has queue
  if (has_queue_object_in_front) {
    debug_oss << __func__ << ": false (Queue in front.)\n";
    return false;
  }

  // Return false if the object is behind ego.
  if (lane_change_object_info.object_ego_dist < 0.0) {
    debug_oss << __func__ << ": false (Object is behind ego.)\n";
    return false;
  }

  constexpr double kMaxTimeToConsiderObjectForReasoningGapInSec = 3.0;
  if (!lane_change_object_info.EncroachRegionBeforeTime(
          /*time=*/kMaxTimeToConsiderObjectForReasoningGapInSec)) {
    debug_oss << __func__ << ": false (Object will NOT enter region soon.)\n";
    return false;
  }

  // Return false is there is any vehicle around object.
  if (lane_change_object_info.has_vehicle_around()) {
    debug_oss << __func__ << ": false (Has vehicle around object.)\n";
    return false;
  }

  if (is_ego_near_merge_lane) {
    debug_oss << __func__
              << ": false (Potentially necessary LC before merge lane.)\n";
    return false;
  }

  // Always consider checking ELC API for cyclist and large vehicle.
  if (planner_object.is_cyclist() || planner_object.is_large_vehicle()) {
    debug_oss << __func__ << ": true\n";
    return true;
  }

  if (has_red_light_ahead) {
    debug_oss << __func__
              << ": false (Ego has red light ahead. Agent likely has the same "
                 "red traffic light color.)\n ";
    return false;
  }

  // For small vehicle.
  constexpr double kMinRemainingDistanceForNotCheckingSmallVehicle = 120.0;
  constexpr double kMaxRemainingDistanceForAlwayCheckingSmallVehicle = 300.0;
  if (remaining_lane_change_distance_from_ego_pose >
      kMaxRemainingDistanceForAlwayCheckingSmallVehicle) {
    // When the remaining distance is abundant, we should always check ELC.
    debug_oss << __func__ << ": true\n";
    return true;
  } else if (remaining_lane_change_distance_from_ego_pose <
             kMinRemainingDistanceForNotCheckingSmallVehicle) {
    // When the remaining distance is limited, we should NOT check ELC.
    debug_oss << __func__ << ": false ("
              << DUMP_TO_STREAM(remaining_lane_change_distance_from_ego_pose)
              << ")\n";
    return false;
  }

  // When the remaining lane change distance is intermediate, we add a few more
  // exclusions to reduce ELC FP signal.
  // TODO(Tingran): Consider remove this condition as we are using agent speed
  // at sixth second for checking ELC API.
  constexpr double kAgentSpeedChangeForSignificantAcceleratingInMps = 5.0;
  const bool is_agent_accelerating =
      lane_change_object_info.agent_speed_at_sixth_second -
          lane_change_object_info.speed >
      kAgentSpeedChangeForSignificantAcceleratingInMps;
  if (is_agent_accelerating) {
    debug_oss << __func__ << ": false (Small vehicle is accelerating.)\n ";
    return false;
  }

  // When the remaining distance is intermediate, check the traffic flow about
  // ego.
  if (HasNominalAgentBetweenEgoAndObject(lane_change_object_info_list,
                                         lane_change_object_info, ego_heading,
                                         debug_oss)) {
    debug_oss << __func__
              << ": false (Has agent between object and ego that may increase "
                 "lc difficulty.)\n ";
    return false;
  }

  if (is_source_region_slower) {
    debug_oss << __func__
              << ": false (Source region has no progress advantage.)\n ";
    return false;
  }

  debug_oss << __func__ << ": true\n";
  return true;
}

bool ShouldSetNoYieldForObject(
    const WorldContext& world_context, const LaneChangeInfo& lane_change_info,
    const LaneChangeObjectInfoList& lane_change_object_info_list,
    const LaneChangeObjectInfo& lane_change_object_info,
    const PlannerObject& planner_object,
    const std::optional<double>& distance_to_last_lc_chance_point,
    const bool has_queue_object_in_front,
    const bool is_reversing_or_kturn_large_vehicle,
    const bool is_ego_in_junction, const bool has_red_light_ahead,
    std::ostringstream& debug_oss) {
  debug_oss << "----------" << __func__ << "----------\n";
  // Set no yield for the objects to avoid rear end collision.
  const std::unordered_map<int64_t, ObjectCollisionInfo>&
      object_collision_info_map =
          lane_change_info.lane_change_env_info.object_collision_info_map;
  if (object_collision_info_map.find(lane_change_object_info.id) !=
          object_collision_info_map.end() &&
      object_collision_info_map.at(lane_change_object_info.id)
          .has_rear_end_collision) {
    debug_oss << "Set no yield because of rear end collision risk.\n";
    return true;
  }

  // Set no yield for the elc trigger object to avoid inefficient lane change
  // back.
  if (lane_change_info.should_set_no_yield_for_elc_triggered_object &&
      lane_change_info.elc_triggered_object_id == lane_change_object_info.id) {
    debug_oss << "Set no yield to avoid lane change back behind elc triggered "
                 "object.\n";
    return true;
  }

  if (ShouldAvoidLaneChangeBehindSlowPed(
          world_context.order_start_time_info(), planner_object,
          lane_change_object_info,
          lane_change_info.lane_change_instance.direction(),
          distance_to_last_lc_chance_point, debug_oss)) {
    debug_oss << "Set no yield to avoid lane change behind slow pedestrian.\n";
    return true;
  }

  const auto& lane_change_metadata = lane_change_info.lane_change_metadata;
  const double average_remaining_distance_per_lane_change =
      lane_change_metadata.remaining_distance_for_consecutive_lane_change() /
      lane_change_metadata.consecutive_lane_change_count();
  const double remaining_lane_change_distance_from_ego_pose =
      GetRemainingLaneChangeDistance(
          is_ego_in_junction, distance_to_last_lc_chance_point,
          average_remaining_distance_per_lane_change);
  bool is_source_region_slower = false;
  const LaneChangeRegionInfo* source_region_info =
      GetLaneChangeRegionInfoPtrByType(
          /*region_type=*/planner::pb::LaneChangeRegionType::SOURCE,
          lane_change_info.region_infos());
  const LaneChangeRegionInfo* target_region_info =
      GetLaneChangeRegionInfoPtrByType(
          /*region_type=*/planner::pb::LaneChangeRegionType::TARGET,
          lane_change_info.region_infos());
  constexpr double kSpeedDiffThresholdInMps = 1.5;
  std::optional<double> source_traffic_speed;
  std::optional<double> target_traffic_speed;
  bool is_ego_near_merge_lane = false;
  if (source_region_info != nullptr) {
    source_traffic_speed =
        source_region_info->traffic_density_info_wo_interaction
            .average_traffic_agents_speed;
    is_ego_near_merge_lane = WillEgoMergeLeft(
        lane_change_info.lane_change_instance, *source_region_info);
  }
  if (target_region_info != nullptr) {
    target_traffic_speed =
        target_region_info->traffic_density_info_wo_interaction
            .average_traffic_agents_speed;
  }
  if (source_traffic_speed.has_value() && target_traffic_speed.has_value() &&
      source_traffic_speed.value() <
          target_traffic_speed.value() + kSpeedDiffThresholdInMps) {
    is_source_region_slower = true;
  }
  // If lane change now may trigger ELC to change back due to progress
  // check, we should lane change later.
  if (CurrentLaneChangeMayCauseELCBack(
          world_context, lane_change_object_info_list, lane_change_object_info,
          planner_object, remaining_lane_change_distance_from_ego_pose,
          has_queue_object_in_front, is_source_region_slower,
          has_red_light_ahead, is_ego_in_junction, is_ego_near_merge_lane,
          debug_oss)) {
    debug_oss << "Set no yield because lane changing behind it may result in "
                 "ELC to change back.\n";
    return true;
  }
  // Set no yield for the blockage object before arc length.
  if (IsBlockageObjectBeforeArcLength(
          lane_change_object_info.arc_length_on_region,
          lane_change_info.lane_change_instance
                  .start_arclength_on_source_lane() +
              lane_change_info.lane_change_instance
                  .source_region_length_before_source_lane(),
          planner_object)) {
    debug_oss << "Set no yield because it's a blockage object before routing "
                 "start arc length.\n";
    return true;
  }

  // Set no yield for the static object on the right most lane which is far away
  // from ego.
  if (IsLikelyParkedCar(lane_change_object_info, planner_object,
                        distance_to_last_lc_chance_point,
                        lane_change_info.is_lc_backup_sequence, debug_oss)) {
    debug_oss << "Set no yield because it's likely a parked car.\n";
    return true;
  }

  if (is_reversing_or_kturn_large_vehicle) {
    debug_oss
        << "Set no yield because it's a reversing or kturn large vehicle.\n";
    return true;
  }

  return false;
}

bool DoAllConstraintsMatchDecision(const pb::SpeedSeed& last_speed_seed,
                                   const ObjectId object_id,
                                   const ConstraintType constraint_type,
                                   const pb::SpeedDecision decision) {
  const auto& constraint_results = last_speed_seed.constraint_results();

  return std::any_of(constraint_results.begin(), constraint_results.end(),
                     [object_id, constraint_type,
                      decision](const pb::SpeedConstraintResult& result) {
                       return result.constraint().obj_id() == object_id &&
                              result.constraint().type() == constraint_type &&
                              result.decision() == decision;
                     }) &&
         std::none_of(constraint_results.begin(), constraint_results.end(),
                      [object_id, constraint_type,
                       decision](const pb::SpeedConstraintResult& result) {
                        return result.constraint().obj_id() == object_id &&
                               result.constraint().type() == constraint_type &&
                               result.decision() != decision;
                      });
}

void AddHysteresisForLastSelectedGap(const pb::SpeedSeed& last_speed_seed,
                                     const ObjectId object_id,
                                     const double ego_speed,
                                     ConstraintMutator& mutator) {
  if (last_speed_seed.lc_guide_seed().empty() ||
      !last_speed_seed.lc_guide_seed(0).is_valid()) {
    return;
  }
  const int64_t lead_obj_id = last_speed_seed.lc_guide_seed(0).lead_obj_id();
  const int64_t tail_obj_id = last_speed_seed.lc_guide_seed(0).tail_obj_id();

  // For passing last lead agent:
  // We were yielding to 'object_id'. To avoid gap flipping, add hysteresis by
  // making passing this object harder.
  if (object_id == lead_obj_id ||
      DoAllConstraintsMatchDecision(
          last_speed_seed, object_id,
          /*constraint_type=*/ConstraintType::Constraint_Type_GAP_ALIGN,
          /*decision=*/pb::SpeedDecision::YIELD)) {
    mutator.set_gap_align_pass_max_active_time(
        kMaxAllowedPassActiveTimeWhenGapFlickersInSec);
  }

  // For yielding to last tail agent:
  // 50km/h.
  constexpr double kMaxAllowedEgoSpeedToEncouragePassingTailAgent = 13.8889;
  if (ego_speed > kMaxAllowedEgoSpeedToEncouragePassingTailAgent) {
    // When ego speed is higher than 50km/h, latching a bad gap could result in
    // racing with agent if the agent does not respond to ego and slow down.
    // Also when ego is near 60km/h, ego does not have enough room to
    // speed up and pass an agent. So we should stop latching the gap and
    // restore the regular yield max allowed active time.
    return;
  }

  // Ego speed is lower than 50kmh.
  if (object_id == tail_obj_id ||
      DoAllConstraintsMatchDecision(
          last_speed_seed, object_id,
          /*constraint_type=*/ConstraintType::Constraint_Type_GAP_ALIGN,
          /*decision=*/pb::SpeedDecision::PASS)) {
    // When we were passing 'object_id', we want to make yielding to this object
    // at this iter harder in order to latch the old gap.
    mutator.set_gap_align_yield_max_active_time(
        kMaxAllowedYieldActiveTimeWhenGapFlickersInSec);
  }

  // TODO(Tingran): Stop latching if there is risk in LC candidate from
  // selection in the past cycles.
}

bool HasEnoughRoomForSpeedUpBeforeLaneChange(
    const planner::pb::LaneSequenceCostFactors& lane_sequence_cost_factors) {
  // TODO(Tingran): Use dist from ego to solid lane marking instead when routing
  // provides this info.
  constexpr double kMinDistToJunctionInMeter = 100.0;
  const double ego_to_junction_dist =
      lane_sequence_cost_factors.distance_to_next_junction_m();
  // |distance_to_next_junction_m| will be negative if ego is in junction.
  if (ego_to_junction_dist > 0.0 &&
      ego_to_junction_dist < kMinDistToJunctionInMeter) {
    return false;
  }

  constexpr double kMinDistToLastLaneChangePointInMeter = 50.0;
  if (lane_sequence_cost_factors.has_last_lane_change_chance_point() &&
      lane_sequence_cost_factors.last_lane_change_chance_point()
              .dist_from_ego() < kMinDistToLastLaneChangePointInMeter) {
    return false;
  }
  return true;
}

bool IsEgoOvertakingNearbyStaticVehicle(
    const std::vector<PredictionDecisionMakerInput>& reasoning_inputs) {
  constexpr double kMaxDistToConsiderInMeter = 25.0;
  return std::any_of(reasoning_inputs.begin(), reasoning_inputs.end(),
                     [](const PredictionDecisionMakerInput& reasoning_input) {
                       const ReasoningObject& reasoning_object =
                           reasoning_input.reasoning_object;
                       return reasoning_object.is_ego_path_overtaking() &&
                              reasoning_object.IsStationary() &&
                              reasoning_object.is_vehicle() &&
                              reasoning_object.object_proximity_info()
                                      .projected_ra_arc_length()
                                      .start() < kMaxDistToConsiderInMeter;
                     });
}

bool CheckYieldPosition(
    const std::vector<speed::pb::OverlapRegion>& overlap_regions,
    const double init_speed, const double target_speed, const double accel,
    const double yield_extra_distance, const double yield_extra_time,
    std::optional<double>& agent_first_overlap_slice_lon_speed) {
  bool can_yield = false;
  for (const pb::OverlapRegion& overlap_region : overlap_regions) {
    for (const pb::OverlapSlice& overlap_slice :
         overlap_region.overlap_slices()) {
      const double t = overlap_slice.relative_time_in_sec();
      const double ego_odom =
          GetOdomUsingConstantAccelModel(init_speed, target_speed, accel, t);
      if (!agent_first_overlap_slice_lon_speed.has_value()) {
        agent_first_overlap_slice_lon_speed =
            overlap_slice.signed_longitudinal_speed();
      }
      if (ego_odom > GetYieldArcLength(overlap_slice, yield_extra_distance,
                                       yield_extra_time)) {
        continue;
      }
      // Ego physically yields to the agent.
      can_yield = true;
      if (t > kThresholdForYieldActiveTimeInSecond) {
        // If the yield time is after the expected yield active time, we do not
        // need to further increase it.
        return false;
      }
      if (GetSpeedUsingConstantAccelModel(init_speed, target_speed, accel, t) >
          overlap_slice.signed_longitudinal_speed() -
              kDeltaSpeedToYieldToAgentBehindEgoInMps) {
        // If the ego speed is not significantly slower than the agent. Do not
        // further increase yield min v.
        return false;
      }
      break;
    }
  }
  // Return whether the ego can yield using the suggested yield min v.
  return can_yield;
}

bool PopulateYieldInfoAtExpectedTime(
    const std::vector<speed::pb::OverlapRegion>& overlap_regions,
    const double yield_extra_distance, const double yield_extra_time,
    const double expected_yield_time, double& target_odom, double& target_speed,
    double& target_yield_time) {
  double closest_overlap_slice_relative_time =
      std::numeric_limits<double>::infinity();
  const pb::OverlapSlice* closest_overlap_slice_ptr = nullptr;
  for (const pb::OverlapRegion& overlap_region : overlap_regions) {
    for (const pb::OverlapSlice& overlap_slice :
         overlap_region.overlap_slices()) {
      const double relative_time =
          std::abs(overlap_slice.relative_time_in_sec() - expected_yield_time);
      if (relative_time < 0.1) {
        target_odom = GetYieldArcLength(overlap_slice, yield_extra_distance,
                                        yield_extra_time);
        target_speed = overlap_slice.signed_longitudinal_speed();
        target_yield_time = expected_yield_time;
        return true;
      }

      if (math::UpdateMin(relative_time, closest_overlap_slice_relative_time)) {
        closest_overlap_slice_ptr = &overlap_slice;
      }
    }
  }

  if (closest_overlap_slice_ptr == nullptr) {
    return false;
  }

  target_odom = GetYieldArcLength(*closest_overlap_slice_ptr,
                                  yield_extra_distance, yield_extra_time);
  target_speed = closest_overlap_slice_ptr->signed_longitudinal_speed();
  target_yield_time = closest_overlap_slice_ptr->relative_time_in_sec();

  return true;
}

bool ShouldDecreaseYieldKinematicParamsForAgent(
    const PlannerObject& planner_object,
    const LaneChangeObjectInfo& object_info,
    const std::optional<double>& dist_to_last_lc_chance_point,
    const double ego_speed, const double suggested_yield_min_v,
    const double suggested_yield_min_a, const double suggested_yield_min_j,
    const double yield_extra_distance, const double yield_extra_time,
    double& decreased_yield_min_v, double& decreased_yield_min_a,
    double& decreased_yield_min_j) {
  if (!ShouldDecreaseYieldKinematicParams(planner_object, object_info,
                                          dist_to_last_lc_chance_point)) {
    return false;
  }

  constexpr double kDeltaSpeedToDecreaseYieldMinVInMps = 3.0;
  constexpr double kMinDecreasedYieldMinAccelInMpss = -1.8;
  const double min_yield_min_v =
      math::Clamp(object_info.agent_speed_at_sixth_second -
                      kDeltaSpeedToDecreaseYieldMinVInMps,
                  0.0, suggested_yield_min_v);
  const double min_yield_min_a =
      std::min(kMinDecreasedYieldMinAccelInMpss, suggested_yield_min_a);

  double target_odom = std::numeric_limits<double>::infinity();
  double target_speed = std::numeric_limits<double>::infinity();
  double target_yield_time = std::numeric_limits<double>::infinity();
  PopulateYieldInfoAtExpectedTime(object_info.overlap_regions,
                                  yield_extra_distance, yield_extra_time,
                                  kExpectedYieldActiveTimeInSecond, target_odom,
                                  target_speed, target_yield_time);

  const bool is_yield_time_valid =
      target_yield_time > math::constants::kEpsilon &&
      target_yield_time <
          kExpectedYieldActiveTimeInSecond + math::constants::kEpsilon;
  constexpr double kDeltaSpeedBufferInMps = 1.0;
  constexpr double kDeltaAccelBufferInMpss = 0.1;
  constexpr double kMinAccelToYieldInMpss = -3.0;
  decreased_yield_min_v =
      is_yield_time_valid ? math::Clamp(target_speed - kDeltaSpeedBufferInMps,
                                        0.0, min_yield_min_v)
                          : min_yield_min_v;
  decreased_yield_min_a =
      is_yield_time_valid
          ? math::Clamp(GetAccelUsingConstantAccelModel(
                            ego_speed, decreased_yield_min_v, target_yield_time,
                            target_odom) -
                            kDeltaAccelBufferInMpss,
                        kMinAccelToYieldInMpss,
                        std::max(kMinAccelToYieldInMpss, min_yield_min_a))
          : min_yield_min_a;
  // TODO(Huoliang): Research and decrease j.
  decreased_yield_min_j = suggested_yield_min_j;
  return true;
}

bool ShouldIncreaseYieldMinSpeedForAgent(
    const LaneChangeObjectInfo& object_info, const double generic_urgency_score,
    const double ego_speed, const double suggested_yield_min_v,
    const double suggested_yield_min_a, const bool has_enough_room_for_speed_up,
    const bool is_ego_overtaking_nearby_static_vehicle,
    const double yield_extra_distance, const double yield_extra_time,
    double& increased_yield_min_v) {
  /* Step 1: Exclude the scenarios where we do not want to increase yield min v.
   */

  if (!ShouldIncreaseYieldMinSpeed(object_info, has_enough_room_for_speed_up,
                                   is_ego_overtaking_nearby_static_vehicle)) {
    return false;
  }

  /* Step 2: Check the yield position using suggested yield min v. */

  // When ego firstly yield to the agent and ego is not significantly slower
  // than the agent, we do not further adjust.
  const double accel_for_suggested_yield_miv_v =
      ego_speed < suggested_yield_min_v ? kEgoMaxAccelWhenYieldInMpss
                                        : suggested_yield_min_a;
  std::optional<double> agent_first_overlap_slice_lon_speed;
  // TODO(Huoliang): After we replace preference yield min v&a, we may have
  // milder params, which are not enough for some cases.
  if (!CheckYieldPosition(
          object_info.overlap_regions, ego_speed, suggested_yield_min_v,
          accel_for_suggested_yield_miv_v, yield_extra_distance,
          yield_extra_time, agent_first_overlap_slice_lon_speed)) {
    return false;
  }

  /* Step 3: Get the adjusted yield min v. */

  // Get the target odom, speed and yield time.
  DCHECK(agent_first_overlap_slice_lon_speed.has_value());
  double target_odom = std::numeric_limits<double>::infinity();
  double target_speed = std::numeric_limits<double>::infinity();
  double target_yield_time = std::numeric_limits<double>::infinity();
  if (!PopulateYieldInfoAtExpectedTime(
          object_info.overlap_regions, yield_extra_distance, yield_extra_time,
          kExpectedYieldActiveTimeInSecond, target_odom, target_speed,
          target_yield_time)) {
    return false;
  }

  // Get the increased yield min v.
  const double accel_for_increased_yield_min_v =
      target_yield_time * ego_speed < target_odom ? kEgoMaxAccelWhenYieldInMpss
                                                  : suggested_yield_min_a;
  double target_yield_v = GetTargetYieldSpeedUsingConstantAccelModel(
      ego_speed, target_yield_time, target_odom,
      accel_for_increased_yield_min_v);
  // Clamp target_yield_v with minimum of the max speed at the target yield
  // time and the max speed at the agent first overlap slice speed.
  const double delta_speed_to_yield = math::LinearInterpolate(
      kMinDeltaSpeedToYieldAtTargetYieldTimeInMps,
      kMaxDeltaSpeedToYieldAtTargetYieldTimeInMps, generic_urgency_score);
  const double clamped_speed =
      std::min(target_speed - delta_speed_to_yield,
               agent_first_overlap_slice_lon_speed.value() -
                   kDeltaSpeedToYieldToAgentBehindEgoInMps);
  math::UpdateMin(std::max(clamped_speed, 0.0), target_yield_v);
  increased_yield_min_v = std::max(target_yield_v, suggested_yield_min_v);

  return true;
}

bool ShouldAddPassHeadwayForAgent(
    const PlannerObject& planner_object,
    const LaneChangeObjectInfo& object_info,
    const planner::pb::SelectedLaneChangeGapInfo& last_selected_gap_info,
    const std::optional<double>& dist_to_last_lc_chance_point,
    const double ego_speed, std::ostringstream& debug_oss) {
  // We only add pass headway for the tail object.
  if (object_info.id != last_selected_gap_info.tail_obj_id()) {
    return false;
  }

  constexpr double kMinDistToLastLcChancePointInMeter = 100.0;
  if (dist_to_last_lc_chance_point.has_value() &&
      dist_to_last_lc_chance_point.value() <
          kMinDistToLastLcChancePointInMeter) {
    debug_oss << __func__ << ": Distance to last lc chance point is too small: "
              << dist_to_last_lc_chance_point.value() << ".\n";
    return false;
  }

  constexpr int kMinNumOfGapSelectedCycles = 3;
  if (last_selected_gap_info.gap_selected_cycles() <
      kMinNumOfGapSelectedCycles) {
    debug_oss << __func__ << ": Gap selected cycles are not enough: "
              << last_selected_gap_info.gap_selected_cycles() << ".\n";
    return false;
  }

  constexpr double kMaxAccelInMpss = 0.3;
  if (planner_object.acceleration() > kMaxAccelInMpss) {
    debug_oss << __func__ << ": Object acceleration is too large: "
              << planner_object.acceleration() << ".\n";
    return false;
  }

  constexpr double kMinTravelTimeForGapLengthInSec = 2.0;
  constexpr double kMinGapLengthInMeter = 20.0;
  if (last_selected_gap_info.gap_length() <
      std::max(ego_speed * kMinTravelTimeForGapLengthInSec,
               kMinGapLengthInMeter)) {
    debug_oss << __func__ << ": Gap length is not enough: "
              << last_selected_gap_info.gap_length() << ".\n";
    return false;
  }

  constexpr double kMaxDeltaSpeedInMps = 1.0;
  if (std::abs(ego_speed - planner_object.speed()) > kMaxDeltaSpeedInMps) {
    debug_oss << __func__ << ": Speed is not similar.\n";
    return false;
  }

  constexpr double kMaxTravelTimeForObjectEgoDistInSec = 1.5;
  constexpr double kMaxDistanceInMeter = 15.0;
  if (object_info.object_ego_dist <
      std::min(-(ego_speed * kMaxTravelTimeForObjectEgoDistInSec),
               -kMaxDistanceInMeter)) {
    debug_oss << __func__ << ": Ego is not close to tail.\n";
    return false;
  }

  return true;
}

double GetSafePassMaxSpeed(
    const std::vector<PredictionDecisionMakerInput>& reasoning_inputs,
    const LaneChangeRegionInfo* source_region_info_ptr,
    const double ego_speed) {
  double safe_pass_max_v = std::numeric_limits<double>::infinity();

  if (source_region_info_ptr == nullptr) {
    return safe_pass_max_v;
  }

  constexpr double kYieldSourceLeadAccelInMpss = 1.2;
  constexpr double kYieldSourceLeadDecelInMpss = -1.0;
  constexpr double kYieldSourceLeadExtraDistanceBufferInMeter = 0.25;
  constexpr double kYieldSourceLeadExtraTimeBufferInSecond = 0.2;
  constexpr double kMinSpeedToCheckSafeInMps = 5.0;

  const LaneChangeObjectInfoList& source_region_object_list =
      source_region_info_ptr->object_info_list;
  for (const int64_t object_id : source_region_object_list.non_leaving_ids()) {
    // Filter the object which does not encroach ego LK trajectory.
    if (!DoesObjectEncroachEgoLKTrajectory(reasoning_inputs, object_id)) {
      continue;
    }

    const LaneChangeObjectInfo* object_info =
        source_region_object_list.Find(object_id);
    DCHECK(object_info != nullptr);

    // Ignore the object is behind ego or very slow.
    if (object_info->object_ego_dist < 0.0 ||
        object_info->speed < kMinSpeedToCheckSafeInMps) {
      continue;
    }

    double target_odom = std::numeric_limits<double>::infinity();
    double target_speed = std::numeric_limits<double>::infinity();
    double target_yield_time = std::numeric_limits<double>::infinity();
    if (!PopulateYieldInfoAtExpectedTime(
            object_info->overlap_regions,
            kYieldSourceLeadExtraDistanceBufferInMeter,
            kYieldSourceLeadExtraTimeBufferInSecond,
            kExpectedYieldActiveTimeInSecond, target_odom, target_speed,
            target_yield_time)) {
      continue;
    }

    if (target_yield_time < math::constants::kEpsilon ||
        target_yield_time > kExpectedYieldActiveTimeInSecond) {
      continue;
    }

    const double max_accel_odom = GetOdomUsingConstantAccelModel(
        ego_speed, ego_speed + kYieldSourceLeadAccelInMpss * target_yield_time,
        kYieldSourceLeadAccelInMpss, target_yield_time);
    // Do nothing if ego keeps acceleration and still yields the target odom.
    if (max_accel_odom < target_odom) {
      continue;
    }

    const double accel_for_safe_pass_max_v =
        target_yield_time * ego_speed < target_odom
            ? kYieldSourceLeadAccelInMpss
            : kYieldSourceLeadDecelInMpss;
    math::UpdateMin(GetTargetYieldSpeedUsingConstantAccelModel(
                        ego_speed, target_yield_time, target_odom,
                        accel_for_safe_pass_max_v),
                    safe_pass_max_v);

    LOG(ERROR) << "id: " << object_info->id << ", odom: " << target_odom
               << ", time: " << target_yield_time
               << ", ego_speed: " << ego_speed
               << ", safe_pass_max_v : " << safe_pass_max_v;
  }

  return safe_pass_max_v;
}

bool IsReversingOrKTurnLargeVehicle(
    const std::vector<PredictionDecisionMakerInput>& reasoning_inputs,
    const int64_t object_id) {
  for (const PredictionDecisionMakerInput& reasoning_input : reasoning_inputs) {
    const ReasoningObject& reasoning_object = reasoning_input.reasoning_object;
    if (reasoning_object.id() != object_id) {
      continue;
    }
    return reasoning_object.has_reverse_or_kturn_trajectory() &&
           reasoning_object.IsLargeVehicle();
  }
  return false;
}

bool WillEgoMergeLeft(const LaneChangeInstance& lc_instance,
                      const LaneChangeRegionInfo& source_region_info) {
  if (lc_instance.direction() !=
      planner::pb::LaneChangeMode::LEFT_LANE_CHANGE) {
    return false;
  }
  if (!lc_instance.is_lane_change_to_merge() &&
      !lc_instance.is_lane_change_in_merge()) {
    return false;
  }
  constexpr double kEgoNearMergeLaneThresholdInMeter = 100.0;
  return source_region_info.total_arc_length -
             source_region_info.ego_ra_arc_length <
         kEgoNearMergeLaneThresholdInMeter;
}

bool IsNominalAgent(const LaneChangeObjectInfo& object_info,
                    const double ego_heading, std::ostringstream& debug_oss) {
  const double ego_agent_heading =
      std::abs(math::AngleDiff(ego_heading, object_info.heading));
  // TODO(Tingran): Maybe also integrate the temp vehicle signal.
  if (object_info.is_parked_car || object_info.is_primary_stationary ||
      ego_agent_heading > M_PI_2) {
    debug_oss << __func__ << "ID: " << object_info.id
              << ", is parked car: " << object_info.is_parked_car
              << ", is primary stationary: "
              << object_info.is_primary_stationary
              << ", ego agent heading: " << ego_agent_heading;
    return false;
  }
  if (std::any_of(object_info.region_motion_infos.begin(),
                  object_info.region_motion_infos.end(),
                  [](const planner::pb::RegionMotionInfo& region_motion_info) {
                    return region_motion_info.type() ==
                           planner::pb::RegionMotionInfo::LEAVING;
                  })) {
    debug_oss << __func__ << "ID: " << object_info.id << ", "
              << "Leaving";
    return false;
  }
  return true;
}

bool HasNominalAgentBetweenEgoAndObject(
    const LaneChangeObjectInfoList& object_info_list,
    const LaneChangeObjectInfo& object_info, const double ego_heading,
    std::ostringstream& debug_oss) {
  // Start Checking the agent behind ego for 10 meters.
  constexpr double kMinObjectToEgoDistInMeter = -10.0;
  const std::vector<int64_t>& object_ids = object_info_list.non_leaving_ids();
  std::vector<int64_t>::const_iterator cur_iter = object_ids.begin();

  if (cur_iter == object_ids.end()) {
    return false;
  }
  // Ignore the behind objects larger than 10 meters.
  while (cur_iter != object_ids.end() &&
         object_info_list.Find(*cur_iter)->object_ego_dist <
             kMinObjectToEgoDistInMeter) {
    ++cur_iter;
  }

  while (cur_iter != object_ids.end()) {
    const LaneChangeObjectInfo& curr_object_info =
        *DCHECK_NOTNULL(object_info_list.Find(*cur_iter));
    if (curr_object_info.id == object_info.id ||
        curr_object_info.object_ego_dist > object_info.object_ego_dist) {
      break;
    }

    if (IsNominalAgent(curr_object_info, ego_heading, debug_oss)) {
      return true;
    }
    ++cur_iter;
  }
  return false;
}
// Returns true if a red traffic light is associated with |trajectory_info|.
bool HasRedLightAhead(const TrajectoryInfo& trajectory_info) {
  const std::vector<traffic_rules::TrafficLightInfo>& traffic_lights =
      trajectory_info.traffic_rules().traffic_lights;
  return std::any_of(
      traffic_lights.begin(), traffic_lights.end(),
      [](const traffic_rules::TrafficLightInfo& traffic_light_info) {
        return traffic_light_info.color() == voy::TrafficLight::RED;
      });
}
}  // namespace reasoner
}  // namespace speed
}  // namespace planner
