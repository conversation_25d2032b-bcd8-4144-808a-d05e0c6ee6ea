"""map model deploy"""
import os
import time
import tempfile
from string import Template
import logging
from functools import partial

from airflow.utils.task_group import TaskGroup
from airflow.exceptions import AirflowSkipException
from airflow.operators.python import PythonOperator

from dag_generation.constants import TaskType
from dag_generation.constants import LUBAN_RESOURCE_UUID
from dag_generation.constants import ModelPlatform
from dag_generation.tasks.task_factory import register
from dag_generation.tasks.abstract_task import AbstractTask

from dag_generation.tasks.map_pipeline.utils import gen_luban_task_conf
from dag_generation.tasks.map_pipeline.utils import (
    on_jenkis_success,
    on_jenkis_fail,
)
from dag_generation.tasks.map_pipeline.checkpoint import Checkpoint
from dataloop.utils.luban_utils import LubanTaskOperator
from dataloop.utils.trail_utils import push_file_to_cloud
from dag_generation.utils.trail_utils import TrailRpcClient

from orion_cloud.flower.airflow.ops.jenkins_job_op import <PERSON><PERSON>ob<PERSON><PERSON>ator

from perception_training import luban_client

_GROUP_NAME = TaskType.MAP_MODEL_DEPLOY

MLP_LUBAN_CT_ROBOT = "mlp_pipeline_robot_v"
_JENKINS_JOB_NAME = "voyager_ifxruntime_trt_cached_engines_generator_ov23_trt10_dev"
_JENKINS_CONNECTION_ID = "jenkinsvoyager"


@register(TaskType.MAP_MODEL_DEPLOY)
class MapModelDeploy(AbstractTask):
    """map model deploy"""

    def _is_skip(self, **context):
        model_deploy = context["dag_run"].conf.get("model_deploy")
        if not model_deploy:
            raise AirflowSkipException

    def _gen_convert_to_onnx_conf(self, context):
        user_name = context["dag_run"].conf.get("user_name")
        branch = context["dag_run"].conf.get("branch")
        if not branch:
            branch = "master"

        convert_onnx = context["dag_run"].conf.get("model_deploy")
        cfg_file = convert_onnx["cfg_file"]
        ckpt_path = context["ti"].xcom_pull(
            task_ids=f"{TaskType.MAP_MODEL_TRAIN}.pick_train_ckpt", key="ckpt_path"
        )
        if not ckpt_path:
            ckpt_path = convert_onnx["ckpt_path"]
        save_dir = convert_onnx["save_dir"]
        exp_id = f'ddp_{convert_onnx["exp_id"]}'
        onnx_file_name = convert_onnx["onnx_file_name"]

        lane_detector_ckpt_path = context["ti"].xcom_pull(
            task_ids=f"{TaskType.MAP_MODEL_TRAIN}.pick_lane_detector_ckpt",
            key="ckpt_path",
        )
        if not lane_detector_ckpt_path:
            lane_detector_ckpt_path = convert_onnx["lane_detector_ckpt_path"]

        # get epoch
        luban_cli = luban_client.LubanClient(MLP_LUBAN_CT_ROBOT)
        commond = f"basename $(readlink -f {ckpt_path}) | grep -oP 'epoch_\K\d+'"
        logging.info(commond)
        epoch_num = luban_cli.check_output(commond).strip()
        context["ti"].xcom_push(key="epoch_num", value=epoch_num)

        script_cmd = [
            "convert_to_onnx",
            cfg_file,
            ckpt_path,
            save_dir,
            exp_id,
            onnx_file_name,
            lane_detector_ckpt_path,
        ]
        return gen_luban_task_conf(
            user_name,
            script_cmd,
            "convert_to_onnx",
            LUBAN_RESOURCE_UUID["H20_8"],
            branch,
        )

    def _push_onnx(self, **context):
        convert_onnx = context["dag_run"].conf.get("model_deploy")
        save_dir = convert_onnx["save_dir"].strip()
        exp_id = f'ddp_{convert_onnx["exp_id"].strip()}'
        onnx_file_name = convert_onnx["onnx_file_name"]

        # get epoch
        epoch_num = context["ti"].xcom_pull(
            task_ids=f"{_GROUP_NAME}.convert_onnx", key="epoch_num"
        )
        onnx_path = f"onnx_export/epoch_{epoch_num}/Train"
        onnx_path = os.path.join(save_dir, exp_id, onnx_path)
        cpu_onnx = f"{onnx_path}/{onnx_file_name}_cpu.onnx"
        gpu_onnx = f"{onnx_path}/{onnx_file_name}_gpu.onnx"

        precision_test_file = f"{onnx_path}/precision_test.zip"

        luban_cli = luban_client.LubanClient(MLP_LUBAN_CT_ROBOT)
        zip_commond = (
            f"cd {onnx_path} && zip -j precision_test.zip precision_test/data/*"
        )
        logging.info(zip_commond)
        luban_cli.check_run(zip_commond)

        def _get_truck_pull_arg(ofs_file, model_name="map.model-files"):
            with tempfile.TemporaryDirectory() as tmp_dir:
                local_file = os.path.join(tmp_dir, os.path.basename(ofs_file))
                luban_cli.download_file(ofs_file, local_file)
                file_info = push_file_to_cloud(local_file, model_name)
                truck_pull_arg = f'{file_info["module"]} {file_info["name"]} -v {file_info["version"]}'
                return truck_pull_arg

        truck_cpu_onnx_arg = _get_truck_pull_arg(cpu_onnx)
        context["ti"].xcom_push(key="truck_cpu_onnx_arg", value=truck_cpu_onnx_arg)
        truck_gpu_onnx_arg = _get_truck_pull_arg(gpu_onnx)
        context["ti"].xcom_push(key="truck_gpu_onnx_arg", value=truck_gpu_onnx_arg)

        truck_precision_test_arg = _get_truck_pull_arg(
            precision_test_file, "ifx-precision-test"
        )

        context["ti"].xcom_push(
            key="truck_precision_test_arg", value=truck_precision_test_arg
        )

        context["ti"].xcom_push(key="model_file_label", value=str(int(time.time())))

    def _ifx_mapping(self, **context):
        label = context["ti"].xcom_pull(
            task_ids=f"{_GROUP_NAME}.push_onnx", key="model_file_label"
        )
        _TRAIL_RPC_CLI = TrailRpcClient()
        ifx_file_infos = _TRAIL_RPC_CLI.get_file_infos(label=label)
        if ifx_file_infos is None:
            raise RuntimeError(f"Can not find ifx file with label {label}")
        ifx_mapping = {}
        model_platforms = [
            ModelPlatform.FP16_L20.value,
            ModelPlatform.FP16_3060.value,
            ModelPlatform.FP16_GEN3.value,
            ModelPlatform.FP16_GEN4.value,
            ModelPlatform.FP32_X86.value,
        ]
        for file_info in ifx_file_infos:
            for platform in model_platforms:
                if platform in file_info["name"]:
                    ifx_mapping[platform] = {
                        "name": file_info["name"],
                        "version": file_info["version"],
                    }
        truck_onnx_arg = context["ti"].xcom_pull(
            task_ids=f"{_GROUP_NAME}.push_onnx", key="truck_cpu_onnx_arg"
        )
        truck_onnx_info = truck_onnx_arg.split()
        assert len(truck_onnx_info) == 4
        ifx_mapping["onnx"] = {
            "name": truck_onnx_info[1],
            "version": truck_onnx_info[3],
        }
        context["ti"].xcom_push(key="ifx_mapping", value=ifx_mapping)

    def gen_airflow_ops(self, dag, *args, **kwargs):
        with TaskGroup(_GROUP_NAME, dag=dag) as tg:
            is_skip_op = PythonOperator(
                task_id="is_skip",
                dag=dag,
                python_callable=self._is_skip,
                provide_context=True,
            )

            convert_onnx_op = LubanTaskOperator(
                task_id="convert_onnx",
                dag=dag,
                gen_offline_task_conf=self._gen_convert_to_onnx_conf,
            )

            push_onnx_op = PythonOperator(
                task_id="push_onnx",
                dag=dag,
                python_callable=self._push_onnx,
                provide_context=True,
            )

            truck_cpu_onnx_arg = Template(
                '{{ ti.xcom_pull(task_ids="$task_id", key="truck_cpu_onnx_arg") }}'
            ).substitute(task_id=f"{_GROUP_NAME}.push_onnx")

            truck_gpu_onnx_arg = Template(
                '{{ ti.xcom_pull(task_ids="$task_id", key="truck_gpu_onnx_arg") }}'
            ).substitute(task_id=f"{_GROUP_NAME}.push_onnx")

            model_file_label = Template(
                '{{ ti.xcom_pull(task_ids="$task_id", key="model_file_label") }}'
            ).substitute(task_id=f"{_GROUP_NAME}.push_onnx")

            truck_precision_test_arg = Template(
                ' {{ ti.xcom_pull(task_ids="$task_id", key="truck_precision_test_arg") }}'
            ).substitute(task_id=f"{_GROUP_NAME}.push_onnx")

            cpu_onnx2ifx_op = JenkinsJobOperator(
                task_id="cpu_onnx2ifx",
                dag=dag,
                job_name=_JENKINS_JOB_NAME,
                jenkins_params={
                    "username": '{{ dag_run.conf.get("user_name") }}',
                    "truck_py_arguments_of_onnx": truck_cpu_onnx_arg,
                    "convert_mode": "cpu_regression",
                    "x86_convert": "openvino",
                    "use_trt": False,
                    "precision_convert": "FP16",
                    "user_params": "--use_timing_cache",
                    "h20_trigger": False,
                    "load_data_online": "none",
                    "label": model_file_label,
                    "precision_test_file": truck_precision_test_arg,
                },
                jenkins_connection_id=_JENKINS_CONNECTION_ID,
                on_success_callback=partial(
                    on_jenkis_success,
                    task_id=f"{_GROUP_NAME}.cpu_onnx2ifx",
                    scene="cpu",
                ),
                on_failure_callback=partial(
                    on_jenkis_fail,
                    task_id=f"{_GROUP_NAME}.cpu_onnx2ifx",
                    scene="cpu",
                ),
            )

            gpu_onnx2ifx_op = JenkinsJobOperator(
                task_id="gpu_onnx2ifx",
                dag=dag,
                job_name=_JENKINS_JOB_NAME,
                jenkins_params={
                    "username": '{{ dag_run.conf.get("user_name") }}',
                    "truck_py_arguments_of_onnx": truck_gpu_onnx_arg,
                    "convert_mode": "gpu_regression",
                    "x86_convert": "openvino",
                    "use_trt": False,
                    "precision_convert": "FP16",
                    "user_params": "--use_timing_cache",
                    "h20_trigger": False,
                    "load_data_online": "none",
                    "label": model_file_label,
                    "precision_test_file": truck_precision_test_arg,
                },
                jenkins_connection_id=_JENKINS_CONNECTION_ID,
                on_success_callback=partial(
                    on_jenkis_success,
                    task_id=f"{_GROUP_NAME}.gpu_onnx2ifx",
                    scene="gpu",
                ),
                on_failure_callback=partial(
                    on_jenkis_fail, task_id=f"{_GROUP_NAME}.gpu_onnx2ifx", scene="gpu"
                ),
            )

            ifx_mapping_op = PythonOperator(
                task_id="ifx_mapping",
                dag=dag,
                python_callable=self._ifx_mapping,
                provide_context=True,
            )

            checkpoint_op = Checkpoint(_GROUP_NAME).op(dag)

            is_skip_op >> convert_onnx_op >> push_onnx_op
            push_onnx_op >> cpu_onnx2ifx_op >> ifx_mapping_op
            push_onnx_op >> gpu_onnx2ifx_op >> ifx_mapping_op
            ifx_mapping_op >> checkpoint_op
            return tg
