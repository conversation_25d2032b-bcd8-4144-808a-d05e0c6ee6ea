#include "planner/path/reasoning/agent_intention/agent_intention_searcher/agent_intention_searcher.h"

#include <algorithm>
#include <limits>
#include <memory>
#include <optional>
#include <set>
#include <sstream>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>

#include <glog/logging.h>
#include <tbb/tbb.h>

#include "base/optional_value_accessor.h"
#include "hdmap/lib/hdmap_singleton.h"
#include "latency/latency_stat.h"
#include "log_utils/log_macros.h"
#include "math/curve2d.h"
#include "math/math_util.h"
#include "math/unit_conversion.h"
#include "planner/behavior/st_planner/rollout/st_rollout.h"
#include "planner/behavior/util/agent_state/agent_in_lane_state_utility.h"
#include "planner/constants.h"
#include "planner/path/reasoning/agent_intention/agent_constraint_utility.h"
#include "planner/path/reasoning/agent_intention/agent_intention_constants.h"
#include "planner/path/reasoning/agent_intention/agent_intention_searcher/agent_intention_evaluator.h"
#include "planner/path/reasoning/agent_intention/agent_intention_searcher/agent_intention_utility.h"
#include "planner/path/reasoning/agent_intention/agent_intention_state.h"
#include "planner/path/reasoning/agent_intention/drivable_lane_corridor_generator/drivable_lane_corridor_generator_utility.h"
#include "planner/path/reasoning/agent_intention/intention_generator_util.h"
#include "planner/path/reasoning/agent_intention/object_occupancy_state/object_occupancy_util.h"
#include "planner/path/reasoning/agent_intention/reasoner/reasoner_util.h"
#include "planner/path/reasoning/agent_intention/trajectory_reasoner/trajectory_reasoner.h"
#include "planner/path/reasoning/cross_intention_evaluation/path_intentions_cross_evaluator.h"
#include "planner/planning_gflags.h"
#include "planner/speed/profile/profile.h"
#include "planner/speed/profile/profile_util.h"
#include "planner/speed/profile/profile_util2.h"
#include "planner/speed/reference/reference_generator.h"
#include "planner/utility/object_id/typed_object_id.h"
#include "planner_protos/agent_inlane_state.pb.h"
#include "planner_protos/agent_intention.pb.h"
#include "planner_protos/agent_intention_generator_debug.pb.h"
#include "pnc_map_service/map_elements/lane.h"
#include "rt_event/rt_event.h"
#include "trace/trace.h"
#include "voy_protos/trajectory.pb.h"
#include "voy_rt_event/rt_event_planner.h"
#include "voy_trace/trace_planner.h"

namespace planner {
namespace {
// Sets agent intention result for a static object (agent and construct zone) in
// tunnel searcher.
void SetTunnelSearcherObjectDecision(
    const std::map<TypedObjectId, ObjectIntentionInfo>&
        object_intention_info_map,
    const TrajectoryReasoner& trajectory_reasoner,
    const std::set<AgentTrajectoryId>& agent_ids, bool is_left_side,
    double max_arc_length_m,
    std::map<TypedObjectId, IntentionResultMetaData>& intention_meta_data_map) {
  for (const auto& augmented_id : agent_ids) {
    const TypedObjectId typed_object_id =
        ConvertAgentTrajectoryIdToTypedObjectId(augmented_id);

    const ObjectIntentionInfo& object_intention_info =
        gtl::FindOrDieNoPrint(object_intention_info_map, typed_object_id);
    const path::ObjectReasoningInfo& object_reasoning_info =
        object_intention_info.object_reasoning_info;

    if (object_intention_info.trajectory_intention_data.is_static &&
        object_reasoning_info.object_attributes()
            .tracked_pose_can_cause_emergency_swerve()) {
      // Such agents are already considered in ST planner, no longer need to
      // consider them.
      continue;
    } else {
      DCHECK(!gtl::ContainsKey(intention_meta_data_map, typed_object_id));
    }

    // Ignore the static agent which is out of speed horizon, except for the
    // potential stuck objects and xlane nudge objects.
    if (object_reasoning_info.tracked_sl_boundary().start_s >
            max_arc_length_m &&
        !object_reasoning_info.object_attributes().is_xlane_nudge_object()) {
      continue;
    }

    IntentionResultMetaData meta_data(
        typed_object_id, /*blocking_state_size=*/-1,
        /*is_static=*/true, /*is_overtaken=*/true,
        /*lateral_decision=*/
        is_left_side ? pb::SnapshotIntention::PASS_RIGHT
                     : pb::SnapshotIntention::PASS_LEFT,
        /*lon_decision=*/speed::pb::SpeedDecision::PASS,
        /*nudge_index_ranges=*/std::vector<math::Range<int>>{},
        /*required_lat_gap=*/
        object_intention_info.trajectory_intention_data.required_lat_gap);

    if (typed_object_id.is_construction_zone()) {
      trajectory_reasoner.PostProcessAgentIntentionResult(
          *(object_reasoning_info.cz_state_ptr()),
          object_reasoning_info.blocking_state_data(),
          object_intention_info.trajectory_intention_data, meta_data);
    } else {
      // For static agent, we still want to do post process. For example, for
      // static large vehicle inside bus bulb.
      if (FLAGS_planning_enable_object_occupancy_state) {
        trajectory_reasoner.PostProcessAgentIntentionResult(
            *(object_reasoning_info.object_occupancy_state_ptr()),
            object_reasoning_info.blocking_state_data(),
            object_intention_info.trajectory_intention_data, meta_data);
      } else {
        trajectory_reasoner.PostProcessAgentIntentionResult(
            *(object_reasoning_info.agent_state_ptr()),
            object_reasoning_info.blocking_state_data(),
            object_intention_info.trajectory_intention_data, meta_data);
      }
    }
    intention_meta_data_map.emplace(typed_object_id, std::move(meta_data));
  }
}

// Sets agent intention result for a static/dynamic object in st planner.
void SetSTPlannerObjectDecision(
    const std::map<TypedObjectId, ObjectIntentionInfo>&
        object_intention_info_map,
    const std::map<AgentTrajectoryId, AgentSTDecision>& agent_decision_map,
    const st_planner::STRollout& best_st_rollout,
    const TrajectoryReasoner& trajectory_reasoner,
    std::map<TypedObjectId, IntentionResultMetaData>& intention_meta_data_map) {
  for (const auto& [agent_id_pair, agent_decision] : agent_decision_map) {
    const TypedObjectId typed_object_id = agent_decision.object_id;
    DCHECK(!gtl::ContainsKey(intention_meta_data_map, typed_object_id));
    const auto& object_intention_info =
        gtl::FindOrDieNoPrint(object_intention_info_map, typed_object_id);
    const path::ObjectReasoningInfo& object_reasoning_info =
        object_intention_info.object_reasoning_info;
    const bool is_static_object = agent_decision.is_static_object ||
                                  typed_object_id.is_construction_zone();
    const int blocking_state_size =
        is_static_object
            ? -1
            : (FLAGS_planning_enable_object_occupancy_state
                   ? GetObjectTrajectoryStates(
                         *object_reasoning_info.object_occupancy_state_ptr(),
                         agent_id_pair.second)
                         .predicted_states()
                         .size()
                   : GetAgentTrajectoryInLaneStates(
                         *(object_reasoning_info.agent_state_ptr()),
                         agent_id_pair.second)
                         .predicted_states.size());
    std::vector<math::Range<int>> nudge_index_ranges;
    if (!is_static_object) {
      nudge_index_ranges.push_back(
          agent_decision.effective_intention_index_range);
    }

    // Previously we set is_overtaken to
    // "agent_decision.lateral_decision != pb::SnapshotIntention::IGNORE", and
    // we want to change it to "agent_decision.lateral_decision !=
    // pb::SnapshotIntention::IGNORE && agent_decision.longitudinal_decision ==
    // speed::pb::SpeedDecision::PASS". Now only apply this change to vehicle,
    // need apply to all agent types in the future.
    const bool is_overtaken =
        object_reasoning_info.is_vehicle()
            ? (agent_decision.lateral_decision !=
                   pb::SnapshotIntention::IGNORE &&
               agent_decision.longitudinal_decision ==
                   speed::pb::SpeedDecision::PASS)
            : agent_decision.lateral_decision != pb::SnapshotIntention::IGNORE;

    IntentionResultMetaData meta_data(
        typed_object_id, blocking_state_size,
        /*is_static=*/is_static_object,
        /*is_overtaken=*/is_overtaken,
        /*lateral_decision=*/agent_decision.lateral_decision,
        /*lon_decision=*/agent_decision.longitudinal_decision,
        nudge_index_ranges,
        /*required_lat_gap=*/
        object_intention_info.trajectory_intention_data.required_lat_gap);
    meta_data.is_emergency_swerve_agent =
        agent_decision.is_emergency_swerve_agent;
    meta_data.best_st_rollout = &best_st_rollout;

    // After st_planner, there might still be agents that we still
    // should (or not) consider. Suppose an agent's current pose is very close
    // to ego current pose, we still should nudge for it. Beside, the lead
    // vehicle should not nudge with Incomplete st rollout sampling.
    if (typed_object_id.is_construction_zone()) {
      trajectory_reasoner.PostProcessAgentIntentionResult(
          *(object_reasoning_info.cz_state_ptr()),
          object_reasoning_info.blocking_state_data(),
          object_intention_info.trajectory_intention_data, meta_data);
    } else {
      if (FLAGS_planning_enable_object_occupancy_state) {
        trajectory_reasoner.PostProcessAgentIntentionResult(
            *(object_reasoning_info.object_occupancy_state_ptr()),
            object_reasoning_info.blocking_state_data(),
            object_intention_info.trajectory_intention_data, meta_data);
      } else {
        trajectory_reasoner.PostProcessAgentIntentionResult(
            *(object_reasoning_info.agent_state_ptr()),
            object_reasoning_info.blocking_state_data(),
            object_intention_info.trajectory_intention_data, meta_data);
      }
    }
    intention_meta_data_map.emplace(typed_object_id, std::move(meta_data));
  }
}

// Gets speed profile with st rollout.
speed::Profile GetSpeedProfileFromSTRollout(
    const st_planner::STRollout& st_rollout, const EgoInLaneParams& ego_param) {
  DCHECK(!st_rollout.st_curve.empty());
  speed::Profile speed_profile;
  speed_profile.reserve(st_rollout.st_curve.size() + 1);
  const double plan_init_time = math::Ms2Sec(ego_param.plan_start_timestamp_ms);
  speed::State state;
  // Add front state.
  constexpr double kSpeedProfileSamplingIntervalInSec = 0.25;
  for (size_t idx = 0; idx < st_rollout.st_curve.size(); ++idx) {
    const int num_of_sampled_point =
        std::max(math::FloorToIntWithTol((st_rollout.st_curve[idx].x_max() -
                                          st_rollout.st_curve[idx].x_min()) /
                                         kSpeedProfileSamplingIntervalInSec),
                 1);  // at least, we should pick one point
    for (int idx_p = 0; idx_p < num_of_sampled_point; ++idx_p) {
      const double relative_t =
          std::min(st_rollout.st_curve[idx].x_max(),
                   st_rollout.st_curve[idx].x_min() +
                       idx_p * kSpeedProfileSamplingIntervalInSec);
      state.t = relative_t + plan_init_time;
      state.x =
          base::CheckAndGetValue(st_rollout.st_curve[idx].Value(relative_t)) +
          ego_param.arclength_m;
      state.v =
          base::CheckAndGetValue(st_rollout.vt_curve[idx].Value(relative_t));
      if (!speed_profile.empty() &&
          (state.t < speed_profile.back().t ||
           state.x < speed_profile.back().x || state.v < 0)) {
        continue;
      }
      state.a =
          base::CheckAndGetValue(st_rollout.at_curve[idx].Value(relative_t));
      state.j = st_rollout.at_curve[idx].a();
      speed_profile.push_back(state);
    }
  }
  // Add last state.
  state.t = st_rollout.st_curve.back().x_max() + plan_init_time;
  state.x = st_rollout.st_curve.back().EndPointValue() + ego_param.arclength_m;
  state.v = st_rollout.vt_curve.back().EndPointValue();
  state.a = st_rollout.at_curve.back().EndPointValue();
  state.j = st_rollout.at_curve.back().a();
  if (!speed_profile.empty() &&
      (state.t < speed_profile.back().t || state.x < speed_profile.back().x ||
       state.v < 0)) {
    return speed_profile;
  }
  speed_profile.push_back(state);

  return speed_profile;
}

// Generates agent intention result meta data under PostReason process.
std::map<TypedObjectId, IntentionResultMetaData>
GenerateAgentIntentionResultMetaDataMap(
    const StaticObjectDecision& path_decision,
    const STRolloutDecision* speed_decision,
    const std::map<TypedObjectId, ObjectIntentionInfo>&
        object_intention_info_map,
    const EgoInLaneParams& ego_param,
    const path::ScenarioIdentifierResult& /* scenario_identify_result */,
    const pb::TrafficRuleReasoningInfoDebug& /* traffic_rule_reasoning_info */,
    const math::Range1d& planning_horizon_range,
    const TrajectoryReasoner& trajectory_reasoner) {
  std::map<TypedObjectId, IntentionResultMetaData> intention_meta_data_map;
  if (speed_decision != nullptr) {
    SetSTPlannerObjectDecision(object_intention_info_map,
                               speed_decision->agent_decision_map,
                               speed_decision->st_rollout, trajectory_reasoner,
                               intention_meta_data_map);
  }
  const double max_arc_length_m =
      speed_decision != nullptr
          ? (speed_decision->st_rollout.st_curve.back().EndPointValue() +
             ego_param.front_bumper_arclength)
          : planning_horizon_range.end_pos;
  SetTunnelSearcherObjectDecision(
      object_intention_info_map, trajectory_reasoner, path_decision.left_agents,
      /*is_left_side=*/true, max_arc_length_m, intention_meta_data_map);
  SetTunnelSearcherObjectDecision(
      object_intention_info_map, trajectory_reasoner,
      path_decision.right_agents,
      /*is_left_side=*/false, max_arc_length_m, intention_meta_data_map);
  trajectory_reasoner.AdjustSolidLineConstraint(intention_meta_data_map);
  path::AddExtraNeedsToBeConsideredIntention(object_intention_info_map,
                                             intention_meta_data_map);
  return intention_meta_data_map;
}

// Gets homotopy type for agent intention.
pb::IntentionResult::IntentionHomotopy GetHomotopy(
    int num_left_agents, int num_right_agents,
    pb::LaneKeepBehaviorType lane_keep_behavior_type,
    bool extend_boundary_for_xlane) {
  if (num_left_agents == 0 && num_right_agents == 0) {
    return pb::IntentionResult::IGNORE_ALL;
  }

  // Override to x-lane homotopy according to lane keep behavior type.
  if (extend_boundary_for_xlane) {
    return lane_keep_behavior_type == pb::LK_LEFT_XLANE
               ? pb::IntentionResult::XLANE_PASS_FROM_LEFT
               : pb::IntentionResult::XLANE_PASS_FROM_RIGHT;
  }

  if (num_left_agents == 0 && num_right_agents != 0) {
    return pb::IntentionResult::PASS_FROM_LEFT;
  } else if (num_left_agents != 0 && num_right_agents == 0) {
    return pb::IntentionResult::PASS_FROM_RIGHT;
  } else {
    return pb::IntentionResult::PASS_THROUGH;
  }
}

void PopulateEsFieldAndEmitRtEvent(
    const std::unordered_set<std::string>& es_object_types,
    const bool is_emergency_swerve, pb::IntentionResult& result) {
  if (!is_emergency_swerve || es_object_types.empty()) {
    return;
  }

  std::stringstream es_object_types_string;
  // Loop through es_object_types, check if there are only ped or cyc. Also
  // convert it to a string to use in RT event.
  bool only_cyc_or_ped = true;
  bool is_first = true;
  for (const std::string& str : es_object_types) {
    if (str != "PED" && str != "CYCLIST") {
      only_cyc_or_ped = false;
    }

    if (!is_first) {
      es_object_types_string << "_";
    }
    es_object_types_string << str;
    is_first = false;
  }

  // Always populate RT event regardless the flag or agent types.
  rt_event::PostRtEvent<rt_event::planner::GenerateEmergencySwerveIntention>(
      es_object_types_string.str());
  // Only populate ES field when flag is on and only_cyc_or_ped.
  if (planner::FLAGS_planning_enable_path_emergency_swerve && only_cyc_or_ped) {
    result.set_is_emergency_swerve(is_emergency_swerve);
  }
}

// Adds fod intention to intention result.
void AddFodIntentionMap(
    const IntentionResultMetaData& intention_result_metadata,
    pb::IntentionResult& intention_result) {
  const pb::IntentionResult::FoDIntention fod_intention =
      intention_result_metadata.fod_intention;
  if (fod_intention != pb::IntentionResult::NOT_APPLICABLE &&
      intention_result.fod_intention_map().find(
          intention_result_metadata.object_id.id) ==
          intention_result.fod_intention_map().end()) {
    intention_result.mutable_fod_intention_map()->emplace(
        intention_result_metadata.object_id.id, fod_intention);
  }
}

// Generates agent intention result for a trajectory.
pb::IntentionResult GenerateAgentIntentionResult(
    const std::map<TypedObjectId, IntentionResultMetaData>&
        intention_meta_data_map,
    const std::map<TypedObjectId, ObjectIntentionInfo>&
        object_intention_info_map,
    const speed::pb::Profile& guided_speed_profiles,
    const double path_total_cost, const double speed_total_cost,
    const pb::AgentIntentionSearcherConfig& config,
    const pb::LaneKeepBehaviorType lane_keep_behavior_type,
    const pb::BehaviorType behavior_type,
    const pb::ScenarioRecognition::ScenarioType scenario_type,
    const pb::SnapshotIntention::PassState safe_pass_side,
    const double ego_speed_mps) {
  pb::IntentionResult result;
  int num_left_agents = 0;
  int num_right_agents = 0;
  bool is_emergency_cross_lane_nudge = false;

  // If this intention result is an ES intention. Will only be true when there
  // is at least one agent that meta_data.is_emergency_swerve_agent is true.
  bool is_emergency_swerve = false;
  // Record all object types that caused ES, so we can better triage rt event.
  std::unordered_set<std::string> es_object_types;
  const bool behavior_type_allows_es =
      behavior_type == pb::LANE_KEEP &&
      lane_keep_behavior_type == pb::LK_DEFAULT;

  for (const auto& [object_id, meta_data] : intention_meta_data_map) {
    if (object_id.is_construction_zone()) {
      (*result.mutable_construction_zone_intentions())[object_id.id] =
          GenerateIntention(meta_data);
    } else {
      (*result.mutable_object_intentions())[object_id.id] =
          GenerateIntention(meta_data);
    }

    if (meta_data.lateral_decision == pb::SnapshotIntention::PASS_RIGHT) {
      ++num_left_agents;
    } else if (meta_data.lateral_decision == pb::SnapshotIntention::PASS_LEFT) {
      ++num_right_agents;
    }

    is_emergency_cross_lane_nudge |= meta_data.is_lane_encroach_intent_agent;

    // Note: now we only enable ES when Ego speed less than 60km/h.
    constexpr double kMaxEgoSpeedMpsForEs = 16.6;
    const bool should_es_for_agent =
        ego_speed_mps < kMaxEgoSpeedMpsForEs && behavior_type_allows_es &&
        meta_data.is_emergency_swerve_agent &&
        (safe_pass_side == pb::SnapshotIntention::PASS_BOTH ||
         safe_pass_side == meta_data.lateral_decision);
    is_emergency_swerve |= should_es_for_agent;
    // Add fod intention to intention result.
    AddFodIntentionMap(meta_data, result);
    // Get ES object types for record and debug purpose.
    const auto object_intention_iter = std::find_if(
        object_intention_info_map.begin(), object_intention_info_map.end(),
        [object_id = object_id](const auto& it) {
          return it.first == object_id;
        });
    if (object_intention_iter != object_intention_info_map.end() &&
        should_es_for_agent) {
      es_object_types.insert(
          object_id.is_construction_zone()
              ? "CZ"
              : voy::perception::ObjectType_Name(
                    object_intention_iter->second.object_reasoning_info
                        .object_type()));
    }
  }
  // Add ref speed profile.
  *result.mutable_selected_speed_profile() = guided_speed_profiles;

  result.set_is_emergency_cross_lane_nudge(is_emergency_cross_lane_nudge);

  // Set ES field and emit related RT event.
  PopulateEsFieldAndEmitRtEvent(es_object_types, is_emergency_swerve, result);

  result.set_is_searched(true);
  // TODO(tianxi): simplify the xlane nudge signal.
  const bool extend_boundary_for_xlane =
      (lane_keep_behavior_type == pb::LK_LEFT_XLANE ||
       lane_keep_behavior_type == pb::LK_RIGHT_XLANE) &&
      scenario_type == pb::ScenarioRecognition::XLANE_NUDGE;
  result.set_homotopy(GetHomotopy(num_left_agents, num_right_agents,
                                  lane_keep_behavior_type,
                                  extend_boundary_for_xlane));
  result.set_is_boundary_extended(extend_boundary_for_xlane);
  // Update total cost.
  const double path_ratio = config.proportion_of_path_in_trajectory_cost();
  result.set_total_cost(path_ratio * path_total_cost +
                        (1.0 - path_ratio) * speed_total_cost);
  result.set_will_modify_path_for_safety(false);
  return result;
}

// Adds nudge back constraint before yield arclength.
void AddNudgeBackConstraint(const NudgeBackConstraintMetaData& nudge_back_meta,
                            const double ego_front_bumper_arclength,
                            path::PathReasoningResult& path_reasoning_result) {
  constexpr double kNudgeBackBufferAheadEgoInM = 1.0;
  constexpr double kNudgeBackLeftOffsetInM = 1.0;
  constexpr double kNudgeBackRightOffsetInM = -1.0;
  constexpr double kNudgeBackBufferBeforeYieldArclengthInM = 5.0;

  if (nudge_back_meta.agent_arclength_m <
      ego_front_bumper_arclength + kNudgeBackBufferAheadEgoInM) {
    // If yield arclength is already close to Ego, don't add nudge back
    // constraint to avoid harsh swerve.
    return;
  }

  std::vector<path::FrenetPoint> left_repulsion_for_agent;
  left_repulsion_for_agent.reserve(2);
  left_repulsion_for_agent.emplace_back(
      std::max(ego_front_bumper_arclength + kNudgeBackBufferAheadEgoInM,
               nudge_back_meta.agent_arclength_m -
                   kNudgeBackBufferBeforeYieldArclengthInM),
      kNudgeBackLeftOffsetInM);
  left_repulsion_for_agent.emplace_back(nudge_back_meta.agent_arclength_m,
                                        kNudgeBackLeftOffsetInM);

  std::vector<path::FrenetPoint> right_repulsion_for_agent;
  right_repulsion_for_agent.reserve(2);
  right_repulsion_for_agent.emplace_back(
      std::max(ego_front_bumper_arclength + kNudgeBackBufferAheadEgoInM,
               nudge_back_meta.agent_arclength_m -
                   kNudgeBackBufferBeforeYieldArclengthInM),
      kNudgeBackRightOffsetInM);
  right_repulsion_for_agent.emplace_back(nudge_back_meta.agent_arclength_m,
                                         kNudgeBackRightOffsetInM);

  if (nudge_back_meta.possible_pass_states.count(
          pb::SnapshotIntention::PASS_LEFT) > 0 ||
      nudge_back_meta.possible_pass_states.count(
          pb::SnapshotIntention::PASS_BOTH) > 0) {
    rt_event::PostRtEvent<
        rt_event::planner::AddNudgeBackConstraintOnYieldHomotopy>("");
    path_reasoning_result.homotopic_constraint_manager->AddRepulsion(
        left_repulsion_for_agent, path::BoundaryStrength::kModerate,
        /*max_abs_lateral_accel=*/std::nullopt,
        /*max_abs_lateral_juke=*/std::nullopt,
        /*is_left=*/true,
        absl::StrCat("Left Nudge Back Repulsion For ",
                     std::to_string(nudge_back_meta.nudge_back_agent_id.id)),
        /*object_id=*/std::nullopt, /*trajectory_id=*/std::nullopt);
  }

  if (nudge_back_meta.possible_pass_states.count(
          pb::SnapshotIntention::PASS_RIGHT) > 0 ||
      nudge_back_meta.possible_pass_states.count(
          pb::SnapshotIntention::PASS_BOTH) > 0) {
    rt_event::PostRtEvent<
        rt_event::planner::AddNudgeBackConstraintOnYieldHomotopy>("");
    path_reasoning_result.homotopic_constraint_manager->AddRepulsion(
        right_repulsion_for_agent, path::BoundaryStrength::kModerate,
        /*max_abs_lateral_accel=*/std::nullopt,
        /*max_abs_lateral_juke=*/std::nullopt,
        /*is_left=*/false,
        absl::StrCat("Right Nudge Back Repulsion For ",
                     std::to_string(nudge_back_meta.nudge_back_agent_id.id)),
        /*object_id=*/std::nullopt, /*trajectory_id=*/std::nullopt);
  }
}

// Generates IGNORE_ALL path reasoning result.
path::PathReasoningResult GenerateIgnoreAllPathReasoningResult(
    const EgoInLaneParams& ego_param,
    const std::map<TypedObjectId, ObjectIntentionInfo>&
        object_intention_info_map,
    const std::map<TypedObjectId, ObjectIntentionInfo>&
        imaginary_object_intention_info_map,
    const std::map<TypedObjectId, IntentionResultMetaData>&
        min_cost_intention_meta_data_map,
    const math::geometry::PolylineCurve2d& reference_path,
    const speed::pb::Profile& guided_speed_profiles,
    const speed::Profile& reusable_speed_profile,
    const speed::Profile& lower_bound_speed_profile,
    const path::ConstraintManager& geo_guidance_constraint_manager,
    const pb::AgentIntentionSearcherConfig& config,
    const LateralClearanceCorridorDirective&
        lateral_clearance_corridor_directive,
    const pb::LaneKeepBehaviorType lane_keep_behavior_type,
    const pb::BehaviorType behavior_type,
    const pb::ScenarioRecognition::ScenarioType scenario_type,
    const pb::SnapshotIntention::PassState safe_pass_side,
    const pb::PathReasonerSeed& path_reasoner_seed) {
  const bool need_pure_ignore_all_homotopy =
      (lane_keep_behavior_type == pb::LaneKeepBehaviorType::LK_PULL_OUT);
  std::map<TypedObjectId, IntentionResultMetaData> intention_meta_data_map =
      need_pure_ignore_all_homotopy
          ? std::map<TypedObjectId, IntentionResultMetaData>{}
          : GenerateIgnoreAllIntentionResultMetaDataMap(
                ego_param, object_intention_info_map,
                min_cost_intention_meta_data_map);
  path::AddExtraNeedsToBeConsideredIntention(object_intention_info_map,
                                             intention_meta_data_map);
  path::PathReasoningResult path_reasoning_result;
  path_reasoning_result.intention_result = GenerateAgentIntentionResult(
      intention_meta_data_map, object_intention_info_map, guided_speed_profiles,
      /*path_total_cost=*/0.0, /*speed_total_cost=*/0.0, config,
      lane_keep_behavior_type, behavior_type, scenario_type, safe_pass_side,
      ego_param.speed_mps);

  path_reasoning_result.homotopic_constraint_manager =
      std::make_unique<path::ConstraintManager>(
          geo_guidance_constraint_manager);

  // TODO(Harry): Add repulsion debug for ignore homotopy.
  RepulsionMetaManager::AppendRepulsions(
      min_cost_intention_meta_data_map, object_intention_info_map,
      reference_path, ego_param, speed::FromProto(guided_speed_profiles),
      reusable_speed_profile, lower_bound_speed_profile,
      /*is_from_ignore_homotopy=*/true, lateral_clearance_corridor_directive,
      *path_reasoning_result.homotopic_constraint_manager);
  RepulsionMetaManager::AppendRepulsions(
      min_cost_intention_meta_data_map, imaginary_object_intention_info_map,
      reference_path, ego_param, speed::FromProto(guided_speed_profiles),
      reusable_speed_profile, lower_bound_speed_profile,
      /*is_from_ignore_homotopy=*/true, lateral_clearance_corridor_directive,
      *path_reasoning_result.homotopic_constraint_manager);

  // Flat the path constraints from intention meta datas.
  FlattenAgentConstraints(intention_meta_data_map, object_intention_info_map,
                          path_reasoning_result.agent_constraints);
  path_reasoning_result.path_reasoner_seed = path_reasoner_seed;
  return path_reasoning_result;
}

// Generates agent constraint for the xlane nudge object
AgentConstraint GenerateXLaneObjectConstraint(
    const path::ObjectReasoningInfo& object_reasoning_info,
    pb::SnapshotIntention::PassState pass_state) {
  const bool is_tracked_object = !object_reasoning_info.is_construction_zone();
  ObjectSnapshot object_snapshot;
  if (object_reasoning_info.is_construction_zone()) {
    object_snapshot = GenerateObjectSnapShot(
        *object_reasoning_info.cz_state_ptr(), /*timestamp=*/-1,
        pb::ObjectSourceType::kConstructionZone);
  } else if (FLAGS_planning_enable_object_occupancy_state) {
    const ObjectOccupancyState& occupancy_state =
        *object_reasoning_info.object_occupancy_state_ptr();
    object_snapshot = GenerateObjectSnapShot(
        occupancy_state.current_snapshot_info().object_occupancy_param(),
        /*timestamp=*/-1, occupancy_state.pose(),
        pb::ObjectSourceType::kTrackedObject, /*snapshot_index=*/0);
  } else {
    object_snapshot = GenerateObjectSnapShot(
        (*object_reasoning_info.agent_state_ptr()).tracked_state.inlane_param,
        /*timestamp=*/-1, pb::ObjectSourceType::kTrackedObject,
        /*snapshot_index=*/0);
  }

  AgentConstraint constraint = GetAgentConstraintForCurrenPose(
      object_reasoning_info.object_id().id,
      is_tracked_object
          ? std::make_optional(object_reasoning_info.object_type())
          : std::nullopt,
      is_tracked_object ? AgentConstraint::Type::kStaticAgent
                        : AgentConstraint::Type::kConstructionZone,
      AgentConstraint::NudgeOption::kHard,
      RequiredLateralGap(
          /*critical_required_lateral_gap=*/
          object_reasoning_info.required_lat_gap()
              .critical_required_lateral_gap,
          /*comfort_required_lateral_gap=*/1.0),
      std::move(object_snapshot), pass_state, is_tracked_object);
  constraint.agent_reasoning_type = pb::AgentReasonerId::NON;
  constraint.response_intensity = ResponseIntensity::kHigh;

  return constraint;
}

// Generates agent intention result for a trajectory.
void GenerateXLaneNudgeIntentionResult(
    const std::map<TypedObjectId, ObjectIntentionInfo>&
        object_intention_info_map,
    const speed::Profile& upper_bound_speed,
    const path::ScenarioIdentifierResult& scenario_identify_result,
    pb::IntentionResult* intention_result,
    std::vector<AgentConstraint>* agent_constraints) {
  DCHECK(scenario_identify_result.xlane_nudge_scene.has_value());
  const path::XLaneNudgeScene& xlane_nudge_scene =
      scenario_identify_result.xlane_nudge_scene.value();
  DCHECK(!xlane_nudge_scene.is_overtake_dynamic_agent);
  const auto& nudge_object_ids =
      xlane_nudge_scene.type == pb::XLaneNudgeSceneType::kCrossLeft
          ? scenario_identify_result.inlane_nudge_objects_info
                .pass_left_object_ids
          : scenario_identify_result.inlane_nudge_objects_info
                .pass_right_object_ids;
  const pb::SnapshotIntention::PassState lateral_decision =
      xlane_nudge_scene.type == pb::XLaneNudgeSceneType::kCrossLeft
          ? pb::SnapshotIntention::PASS_LEFT
          : pb::SnapshotIntention::PASS_RIGHT;
  for (const auto& object_id : nudge_object_ids) {
    const auto& object_info =
        gtl::FindOrDieNoPrint(object_intention_info_map, object_id);
    // To make xlane nudge be stable, raw nudge object ids might contain some
    // static agents faraway behind. We should ignore then in the intention
    // result.
    if (!object_info.object_reasoning_info.is_stationary() ||
        object_info.object_reasoning_info.object_attributes()
            .is_far_away_behind()) {
      continue;
    }

    if (object_id.is_construction_zone()) {
      (*intention_result
            ->mutable_construction_zone_intentions())[object_id.id] =
          GenerateIntention(/*effective_intention_index_ranges=*/{{-1, -1}},
                            lateral_decision, object_id.id,
                            /*is_static=*/true, /*blocking_state_size=*/1,
                            /*is_overtaken=*/true);
    } else {
      (*intention_result->mutable_object_intentions())[object_id.id] =
          GenerateIntention(/*effective_intention_index_ranges=*/{{-1, -1}},
                            lateral_decision, object_id.id,
                            /*is_static=*/true, /*blocking_state_size=*/1,
                            /*is_overtaken=*/true);
    }
    const auto& fod_intentions =
        object_info.trajectory_intention_data.fod_intentions;
    if (!fod_intentions.empty() &&
        !path::HasFodIntention(fod_intentions,
                               pb::IntentionResult::NOT_APPLICABLE) &&
        path::HasFodIntention(fod_intentions, pb::IntentionResult::NUDGE)) {
      intention_result->mutable_fod_intention_map()->emplace(
          object_id.id, pb::IntentionResult::NUDGE);
    }
    agent_constraints->emplace_back(GenerateXLaneObjectConstraint(
        object_info.object_reasoning_info, lateral_decision));
  }

  // Add ref speed profile.
  *intention_result->mutable_selected_speed_profile() =
      speed::ToProto(upper_bound_speed);
  intention_result->set_is_emergency_cross_lane_nudge(false);
  intention_result->set_is_searched(false);
  intention_result->set_homotopy(
      xlane_nudge_scene.type == pb::XLaneNudgeSceneType::kCrossLeft
          ? pb::IntentionResult::XLANE_PASS_FROM_LEFT
          : pb::IntentionResult::XLANE_PASS_FROM_RIGHT);
  intention_result->set_is_boundary_extended(true);
  // Set total cost to maximum.
  intention_result->set_total_cost(std::numeric_limits<double>::max());
  intention_result->set_will_modify_path_for_safety(false);
}

// Tries to generate a pure xlane nudge intention result which only nudges the
// objects in the stuck region.
void MaybeGeneratePureXLaneNudgeIntention(
    const std::map<TypedObjectId, ObjectIntentionInfo>&
        object_intention_info_map,
    const EgoInLaneParams& ego_param, const speed::Profile& upper_bound_speed,
    const path::ConstraintManager& geo_guidance_constraint_manager,
    const path::ScenarioIdentifierResult& scenario_identify_result,
    const pb::PathReasonerSeed& path_reasoner_seed,
    std::vector<path::PathReasoningResult>* results) {
  // No need to generate pure xlane nudge homotopy for the non-static-xlane
  // scene.
  if (scenario_identify_result.scenario_type !=
          pb::ScenarioRecognition::XLANE_NUDGE ||
      !scenario_identify_result.xlane_nudge_scene.has_value() ||
      scenario_identify_result.xlane_nudge_scene.value()
          .is_overtake_dynamic_agent) {
    return;
  }

  const double ego_length = ego_param.rear_axle_to_rear_bumper_m +
                            ego_param.rear_axle_to_front_bumper_m;
  const double critical_unstuck_range_start_pos =
      scenario_identify_result.xlane_nudge_scene->stuck_region_range.start_pos -
      ego_length;
  const double critical_unstuck_range_end_pos =
      scenario_identify_result.xlane_nudge_scene->stuck_region_range.end_pos +
      ego_length;
  // If ego has not entered the critical unstuck range, don't generate pure
  // xlane nudge homotopy.
  if (!math::IsInRange(ego_param.front_bumper_arclength,
                       critical_unstuck_range_start_pos,
                       critical_unstuck_range_end_pos)) {
    return;
  }

  path::PathReasoningResult path_reasoning_result;
  GenerateXLaneNudgeIntentionResult(object_intention_info_map,
                                    upper_bound_speed, scenario_identify_result,
                                    &path_reasoning_result.intention_result,
                                    &path_reasoning_result.agent_constraints);
  path_reasoning_result.homotopic_constraint_manager =
      std::make_unique<path::ConstraintManager>(
          geo_guidance_constraint_manager);
  path_reasoning_result.path_reasoner_seed = path_reasoner_seed;
  results->emplace_back(std::move(path_reasoning_result));
}

pb::ReasoningObject GenerateReasoningObject(
    const IntentionResultMetaData& intention_result) {
  pb::ReasoningObject reasoning_object;
  reasoning_object.set_reasoner_id(intention_result.reasoner_id);
  reasoning_object.set_is_overtaken(intention_result.is_overtaken);
  reasoning_object.set_debug_str(intention_result.debug_str);
  if (intention_result.agent_constraints.empty()) {
    return reasoning_object;
  }
  reasoning_object.mutable_agent_constraint_debug()->Reserve(
      intention_result.agent_constraints.size());
  for (const auto& agent_constraint : intention_result.agent_constraints) {
    GenerateAgentConstraintDebug(agent_constraint,
                                 reasoning_object.add_agent_constraint_debug());
  }
  return reasoning_object;
}

void PopulateIntentionMetaDataDebug(
    const std::map<TypedObjectId, IntentionResultMetaData>&
        intention_meta_data_map,
    google::protobuf::Map<std::string, pb::ReasoningObject>*
        reasoning_objects_map) {
  if (intention_meta_data_map.empty() || reasoning_objects_map == nullptr) {
    return;
  }
  for (const auto& object_intention : intention_meta_data_map) {
    (*reasoning_objects_map)[object_intention.first.ToString()] =
        GenerateReasoningObject(object_intention.second);
  }
}

void PopulateRepulsionsDebug(
    const path::ConstraintManager& constraint_manager,
    google::protobuf::RepeatedPtrField<pb::RepulsionReasoningObject>* result) {
  for (const auto& [object_id_and_reasoner_id,
                    single_object_repulsions_generating_info] :
       constraint_manager.object_repulsions_generating_infos()) {
    planner::pb::RepulsionReasoningObject& debug_proto = *result->Add();
    debug_proto.set_object_id(object_id_and_reasoner_id.first.ToString());
    debug_proto.set_reasoner_id(object_id_and_reasoner_id.second);
    debug_proto.set_debug_str(
        single_object_repulsions_generating_info.debug_str);
    if (single_object_repulsions_generating_info.final_direction.has_value()) {
      debug_proto.set_final_direction(base::CheckAndGetValue(
          single_object_repulsions_generating_info.final_direction));
    }
    if (single_object_repulsions_generating_info
            .collision_arclength_range_on_polygon_center_path_end_pos
            .has_value()) {
      debug_proto.set_collision_arclength_range_on_polygon_center_path_end_pos(
          base::CheckAndGetValue(
              single_object_repulsions_generating_info
                  .collision_arclength_range_on_polygon_center_path_end_pos));
    }
    if (single_object_repulsions_generating_info
            .collision_arclength_range_on_polygon_center_path_start_pos
            .has_value()) {
      debug_proto.set_collision_arclength_range_on_polygon_center_path_start_pos(
          base::CheckAndGetValue(
              single_object_repulsions_generating_info
                  .collision_arclength_range_on_polygon_center_path_start_pos));
    }
    for (const auto& point :
         single_object_repulsions_generating_info.lower_bound_st_region) {
      auto* add_point = debug_proto.add_lower_bound_st_region();
      add_point->set_x(point.x());
      add_point->set_y(point.y());
    }
    for (const auto& point :
         single_object_repulsions_generating_info.upper_bound_st_region) {
      auto* add_point = debug_proto.add_upper_bound_st_region();
      add_point->set_x(point.x());
      add_point->set_y(point.y());
    }
    for (const auto& point :
         single_object_repulsions_generating_info.guided_st_curve.points()) {
      auto* add_point = debug_proto.add_guided_st_curve();
      add_point->set_x(point.x());
      add_point->set_y(point.y());
    }
  }
}

// Update debug for DrivableTunnelDecision.
void PopulateDrivableTunnelDecisionDebug(
    const DrivableTunnel& drivable_tunnel,
    pb::SearchBasedDecisionResult::DrivableTunnelDecision* decision_debug) {
  if (decision_debug == nullptr) {
    return;
  }

  // drivable tunnel decision
  // lateral corridor
  *(decision_debug->mutable_drivable_lateral_corridor()) =
      drivable_tunnel.drivable_corridor.driving_lateral_corridor.ToProto();
  // agent decision
  const StaticObjectDecision& path_decision = drivable_tunnel.agent_decisions;
  decision_debug->mutable_agent_decisions()->Reserve(
      path_decision.left_agents.size() + path_decision.right_agents.size());
  for (const auto& [object_id, trajectory_id] : path_decision.left_agents) {
    auto* decision_ptr = decision_debug->add_agent_decisions();
    decision_ptr->mutable_id()->set_object_id(object_id);
    decision_ptr->mutable_id()->set_trajectory_id(trajectory_id);
    decision_ptr->set_pass_state(pb::SnapshotIntention::PASS_RIGHT);
    decision_ptr->set_longitudinal_decision(speed::pb::SpeedDecision::PASS);
    decision_ptr->mutable_effective_range()->set_start_index(-1);
    decision_ptr->mutable_effective_range()->set_end_index(-1);
  }
  for (const auto& [object_id, trajectory_id] : path_decision.right_agents) {
    auto* decision_ptr = decision_debug->add_agent_decisions();
    decision_ptr->mutable_id()->set_object_id(object_id);
    decision_ptr->mutable_id()->set_trajectory_id(trajectory_id);
    decision_ptr->set_pass_state(pb::SnapshotIntention::PASS_LEFT);
    decision_ptr->set_longitudinal_decision(speed::pb::SpeedDecision::PASS);
    decision_ptr->mutable_effective_range()->set_start_index(-1);
    decision_ptr->mutable_effective_range()->set_end_index(-1);
  }
  for (const auto& [object_id, trajectory_id] : path_decision.yield_agents) {
    auto* decision_ptr = decision_debug->add_agent_decisions();
    decision_ptr->mutable_id()->set_object_id(object_id);
    decision_ptr->mutable_id()->set_trajectory_id(trajectory_id);
    decision_ptr->set_pass_state(pb::SnapshotIntention::YIELD);
    decision_ptr->set_longitudinal_decision(speed::pb::SpeedDecision::YIELD);
    decision_ptr->mutable_effective_range()->set_start_index(-1);
    decision_ptr->mutable_effective_range()->set_end_index(-1);
  }
  // cost items
  decision_debug->mutable_cost_items()->Reserve(
      drivable_tunnel.cost_items.size());
  for (const double cost : drivable_tunnel.cost_items) {
    decision_debug->add_cost_items(cost);
  }
  // total cost
  decision_debug->set_total_cost(drivable_tunnel.total_cost);
}

// Deduplicates intention with the same constraints.
void DedupePathReasoningResults(
    std::vector<path::PathReasoningResult>& path_reasoning_results) {
  if (path_reasoning_results.size() < 2) {
    return;
  }
  std::vector<std::unordered_map<std::string, const AgentConstraint*>>
      id_to_constraint_vector;
  id_to_constraint_vector.resize(path_reasoning_results.size());
  for (size_t i = 0; i < id_to_constraint_vector.size(); ++i) {
    for (const auto& constraint : path_reasoning_results[i].agent_constraints) {
      id_to_constraint_vector[i][constraint.constraint_id] = &constraint;
    }
  }
  for (size_t i = 0; i < path_reasoning_results.size() - 1; ++i) {
    if (path_reasoning_results[i].is_duplicate) {
      continue;
    }
    for (size_t j = i + 1; j < path_reasoning_results.size(); ++j) {
      if (path_reasoning_results[i].agent_constraints.size() !=
              path_reasoning_results[j].agent_constraints.size() ||
          path_reasoning_results[i]
                  .homotopic_constraint_manager->repulsions() !=
              path_reasoning_results[j]
                  .homotopic_constraint_manager->repulsions()) {
        // Different number of constraints or repulsions. Definitely
        // non-equivalent homotopy.
        continue;
      }
      bool found_different_constraint = false;
      for (const auto& lhs_constraint :
           path_reasoning_results[i].agent_constraints) {
        const auto it =
            id_to_constraint_vector[j].find(lhs_constraint.constraint_id);
        if (it == id_to_constraint_vector[j].end()) {
          found_different_constraint = true;
          break;
        }
        const auto& rhs_constraint = *it->second;
        if (lhs_constraint != rhs_constraint) {
          found_different_constraint = true;
          break;
        }
      }
      if (!found_different_constraint) {
        path_reasoning_results[j].is_duplicate = true;
        if (auto* debug = path_reasoning_results[j].decision_result_debug;
            debug != nullptr) {
          debug->set_is_duplicate_with_another(true);
          path_reasoning_results[j]
              .intention_decision_debug->set_is_duplicate_with_another(true);
        }
      }
    }
  }
}

std::optional<NudgeBackConstraintMetaData> ObtainOptionalNudgeBackConstraint(
    const DrivableTunnel& drivable_tunnel,
    const path::PathReasoningResult& path_reasoning_result,
    const std::map<TypedObjectId, ObjectIntentionInfo>&
        object_intention_info_map,
    const std::unordered_map<
        TypedObjectId, std::unordered_set<pb::SnapshotIntention::PassState>>&
        object_possible_pass_states_map) {
  if (drivable_tunnel.agent_decisions.yield_agents.empty()) {
    return std::nullopt;
  }

  NudgeBackConstraintMetaData nudge_back_meta;
  nudge_back_meta.agent_arclength_m = std::numeric_limits<double>::max();
  for (const auto& yield_agent : drivable_tunnel.agent_decisions.yield_agents) {
    const int64_t object_id = yield_agent.first;
    const auto object_intention_iter = std::find_if(
        object_intention_info_map.begin(), object_intention_info_map.end(),
        [&object_id](const auto& it) { return it.first.id == object_id; });

    if (object_intention_iter != object_intention_info_map.end()) {
      const auto& object_reasoning_info =
          object_intention_iter->second.object_reasoning_info;
      const TypedObjectId typed_object_id = object_intention_iter->first;

      double object_arclength_m = 0.0;
      if (object_reasoning_info.is_construction_zone()) {
        object_arclength_m =
            object_reasoning_info.cz_state_ptr()->start_arclength_m;
      } else {
        object_arclength_m =
            FLAGS_planning_enable_object_occupancy_state
                ? object_reasoning_info.object_occupancy_state_ptr()
                      ->current_snapshot_info()
                      .object_occupancy_param()
                      .full_body_start_arclength_m
                : object_reasoning_info.agent_state_ptr()
                      ->tracked_state.inlane_param.full_body_start_arclength_m;
      }

      if (nudge_back_meta.agent_arclength_m > object_arclength_m) {
        nudge_back_meta.agent_arclength_m = object_arclength_m;
        nudge_back_meta.nudge_back_agent_id = typed_object_id;
      }
    }
  }

  const auto& agent_constraints = path_reasoning_result.agent_constraints;
  const auto& agent_constraint_iter = std::find_if(
      agent_constraints.begin(), agent_constraints.end(),
      [&nudge_back_meta](const auto& iter) {
        return iter.object_id == nudge_back_meta.nudge_back_agent_id.id;
      });
  if (agent_constraint_iter != agent_constraints.end() &&
      agent_constraint_iter->pass_state != pb::SnapshotIntention::IGNORE) {
    // There is a valid pass constraint for the selected nudge back agent that
    // we want to yield in the path reasoning homotopy. If we add nudge back to
    // yield for it, it will cause conflicts to the pass constraint, so don't
    // add it.
    return std::nullopt;
  }

  if (object_possible_pass_states_map.find(
          nudge_back_meta.nudge_back_agent_id) ==
      object_possible_pass_states_map.end()) {
    return std::nullopt;
  }

  nudge_back_meta.possible_pass_states =
      object_possible_pass_states_map.at(nudge_back_meta.nudge_back_agent_id);
  return std::make_optional(std::move(nudge_back_meta));
}

}  // namespace

std::vector<path::PathReasoningResult> AgentIntentionSearcher::Execute(
    const std::map<TypedObjectId, ObjectIntentionInfo>&
        object_intention_info_map,
    const std::map<TypedObjectId, ObjectIntentionInfo>&
        imaginary_object_intention_info_map,
    const RobotStateSnapshot& robot_state_snapshot,
    const EgoInLaneParams& ego_param,
    const math::geometry::PolylineCurve2d& reference_path,
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const math::Range1d& planning_horizon_range,
    const speed::Profile& lower_bound_speed,
    const speed::Profile& upper_bound_speed,
    const pb::TrafficRuleReasoningInfoDebug& traffic_rule_reasoning_info,
    const path::ScenarioIdentifierResult& scenario_identify_result,
    const std::vector<path::FrameAnalyzerResult>& frame_analyzer_results,
    const DrivableSpaceCorridor& drivable_space_corridor,
    const pb::TrajectoryGuiderOutput* trajectory_guider_output,
    const std::optional<pb::Path>& reusable_last_path,
    const std::optional<speed::Profile>& reusable_last_speed_profile,
    const speed::pb::SpeedSeed& last_speed_seed,
    const std::optional<double> optional_destination_arclength_m,
    const TrajectoryReasoner& trajectory_reasoner,
    const path::ConstraintManager& geo_guidance_constraint_manager,
    const std::optional<math::geometry::Point2d>& pull_over_destination,
    const pb::PathReasonerSeed& path_reasoner_seed,
    const std::string& current_region, const bool is_high_crossing_vru_scenario,
    pb::AgentIntentionSearcherDebug* debug) {
  TRACE_EVENT_SCOPE(planner, AgentIntentionSearcher_Execute);
  VOY_LATENCY_STAT_RECORD_PLANNER_S1(LAT_STAT_AgentIntentionSearcher_Execute);
  if (debug) {
    *(debug->mutable_lane_lateral_corridor()) =
        drivable_space_corridor.driving_lateral_corridor.ToProto();
  }
  std::vector<path::PathReasoningResult> path_reasoning_results;

  const pb::SnapshotIntention::PassState safe_pass_side =
      traffic_rule_reasoning_info.safe_pass_state_for_adjacent_lanes();

  if (FLAGS_planning_enable_ignore_only_in_dense_crossing_vru_scenario &&
      is_high_crossing_vru_scenario) {
    path_reasoning_results.emplace_back(GenerateIgnoreAllPathReasoningResult(
        ego_param, object_intention_info_map,
        imaginary_object_intention_info_map,
        /*intention_meta_data_map=*/
        std::map<TypedObjectId, IntentionResultMetaData>{}, reference_path,
        speed::ToProto(upper_bound_speed),
        reusable_last_speed_profile.value_or(upper_bound_speed),
        lower_bound_speed, geo_guidance_constraint_manager, config_,
        drivable_space_corridor.driving_lateral_corridor,
        lane_keep_behavior_type_, behavior_type_,
        scenario_identify_result.scenario_type, safe_pass_side,
        path_reasoner_seed));
    return path_reasoning_results;
  }

  // 1. Drivable tunnel sequence serach for static objects.
  tunnel_searcher_.set_space_lateral_horizon(
      GetDrivableTunnelSearchSpaceHalfHorizon(scenario_identify_result));
  const std::vector<DrivableTunnel> tunnel_sequence_vec =
      tunnel_searcher_.Search(
          object_intention_info_map, robot_state_snapshot, ego_param,
          reference_path, drivable_space_corridor, planning_horizon_range,
          /*debug=*/debug ? debug->mutable_tunnel_searcher_debug() : nullptr);
  if ((ego_param.arclength_m >=
       planning_horizon_range.end_pos - math::constants::kEpsilon) ||
      tunnel_sequence_vec.empty()) {
    path_reasoning_results.reserve(2);
    if (FLAGS_planning_enable_extra_pure_xlane_nudge_homotopy) {
      MaybeGeneratePureXLaneNudgeIntention(
          object_intention_info_map, ego_param, upper_bound_speed,
          geo_guidance_constraint_manager, scenario_identify_result,
          path_reasoner_seed, &path_reasoning_results);
    }
    path_reasoning_results.emplace_back(GenerateIgnoreAllPathReasoningResult(
        ego_param, object_intention_info_map,
        imaginary_object_intention_info_map,
        /*intention_meta_data_map=*/
        std::map<TypedObjectId, IntentionResultMetaData>{}, reference_path,
        speed::ToProto(upper_bound_speed),
        reusable_last_speed_profile.value_or(upper_bound_speed),
        lower_bound_speed, geo_guidance_constraint_manager, config_,
        drivable_space_corridor.driving_lateral_corridor,
        lane_keep_behavior_type_, behavior_type_,
        scenario_identify_result.scenario_type, safe_pass_side,
        path_reasoner_seed));
    return path_reasoning_results;
  }
  const auto& hard_blocking_agents = tunnel_searcher_.hard_blocking_agents();

  // 2. Gets smooth reference path for curvature.
  DCHECK_LT(ego_param.arclength_m, planning_horizon_range.end_pos);
  const math::Curve2d smooth_reference_path(
      reference_path.GetSampledPoints(ego_param.arclength_m,
                                      planning_horizon_range.end_pos,
                                      kNominalPathSamplingIntervalInMeter,
                                      math::pb::UseExtensionFlag::kForbid),
      math::pb::Interpolation1dType::kCSpline);
  // 2.a generate const jerk rollouts
  // Run the following logic in parallel to reduce latency.
  tbb::task_group st_rollout_curves_with_const_jerk_tg;
  std::vector<st_planner::STRollout> st_rollout_curves;

  st_rollout_curves_with_const_jerk_tg.run([&]() {
    auto st_rollout_curves_with_const_jerk =
        speed_decider_.GenerateSTRolloutsWithConstJerk(
            ego_param, lower_bound_speed, upper_bound_speed);
    static constexpr int STRolloutLackThreshold = 15;
    if (st_rollout_curves_with_const_jerk.size() < STRolloutLackThreshold) {
      rt_event::PostRtEvent<rt_event::planner::UseNewSTRolloutGeneratingMethod>(
          "");
      st_rollout_curves_with_const_jerk =
          speed_decider_.GenerateSTRolloutsWithConstJerkWithRelaxedLimits(
              ego_param, lower_bound_speed, upper_bound_speed);
    }
    st_rollout_curves.insert(st_rollout_curves.end(),
                             st_rollout_curves_with_const_jerk.begin(),
                             st_rollout_curves_with_const_jerk.end());
  });

  // 2.b generate rollouts from speed search
  const speed::pb::SpeedGeneratorConfig& speed_generator_config =
      PlannerConfigCenter::GetInstance()
          .GetDecoupledForwardManeuverConfig()
          .speed_generator_config();
  const double ego_speed = std::max(ego_param.speed_mps, 0.0);
  const speed::State ego_ra_speed_state(
      math::Ms2Sec(ego_param.plan_start_timestamp_ms), /*x_in=*/0.0, ego_speed,
      std::max(ego_param.accleration_mpss,
               speed::MinAccelToAvoidNegativeSpeed(
                   std::max(speed_generator_config.for_search().dt(),
                            math::constants::kEpsilon),
                   /*jerk=*/0.0, ego_speed)),
      /*j_in=*/0.0);

  if (FLAGS_planning_enable_speed_search_in_path_reasoning &&
      reusable_last_path.has_value()) {
    TRACE_EVENT_SCOPE(planner, AgentIntentionSearcher_RunSpeedSearch);
    // TODO(speed, harryguo): Integrate path_reasoning_urgency_score and speed
    // discomfort levels so that we don't need to call speed search for multiple
    // times.
    const std::map<AgentTrajectoryId, AgentSTRegion> id_st_region_map =
        speed_decider_.GenerateAgentSTRegion(
            hard_blocking_agents, object_intention_info_map, ego_param,
            drivable_space_corridor, reference_path);

    std::vector<std::vector<st_planner::STRollout>>
        st_rollout_curves_from_speed_search_vector(
            constants::kPathReasoningUrgencyScoreLevels);

    tbb::parallel_for(
        0, constants::kPathReasoningUrgencyScoreLevels,
        [this, &ego_param, &reference_path, &smooth_reference_path,
         &object_intention_info_map, &hard_blocking_agents,
         &robot_state_snapshot, &reusable_last_path, &last_speed_seed,
         &lower_bound_speed, &upper_bound_speed, &id_st_region_map,
         &lane_sequence, &trajectory_guider_output, &speed_generator_config,
         &ego_ra_speed_state, &st_rollout_curves_from_speed_search_vector,
         &current_region](int idx) {
          speed::ReferenceGenerator reference_generator =
              speed::ReferenceGenerator::MakeCenterLineReferenceGenerator(
                  speed_generator_config.reference(), ego_ra_speed_state,
                  smooth_reference_path,
                  speed_generator_config.for_car_type().limits(), lane_sequence,
                  trajectory_guider_output, current_region);
          reference_generator.RegulateInitialStateAndUpdateImmutableTime(
              speed_generator_config.for_search().dt());
          reference_generator.UpdateExcludingCautious();
          const double path_reasoning_urgency_score =
              constants::kMinPathReasoningUrgencyScore +
              idx * constants::kPathReasoningUrgencyScoreStep;
          st_rollout_curves_from_speed_search_vector[idx] =
              speed_decider_.GenerateSTRolloutBySpeedSearch(
                  ego_param, reference_path, smooth_reference_path,
                  object_intention_info_map, hard_blocking_agents,
                  robot_state_snapshot, *reusable_last_path, last_speed_seed,
                  lower_bound_speed, upper_bound_speed,
                  path_reasoning_urgency_score, id_st_region_map,
                  reference_generator);
        });

    // As the st_rollout_curves_with_const_jerk are inserted firstly, we need to
    // ensure their completion.
    st_rollout_curves_with_const_jerk_tg.wait();

    for (const auto& st_rollout_curves_from_speed_search :
         st_rollout_curves_from_speed_search_vector) {
      st_rollout_curves.insert(st_rollout_curves.end(),
                               st_rollout_curves_from_speed_search.begin(),
                               st_rollout_curves_from_speed_search.end());
    }
    // Configure debug setting for future calls to speed_decider_.Decide.
    speed_decider_.DebugSpeedSearchIDToSTRegion() = std::move(id_st_region_map);
  } else {
    // Ensure the st_rollout_curves_with_const_jerk are inserted into
    // st_rollout_curves.
    st_rollout_curves_with_const_jerk_tg.wait();
  }

  const size_t point_count = smooth_reference_path.polyline().size();
  std::vector<ReferencePathFeatures> reference_path_features_list;
  reference_path_features_list.reserve(point_count);
  double arc_length = 0.0;
  for (size_t i = 0; i < point_count; ++i) {
    const double curvature =
        smooth_reference_path.GetInterpCurvature(arc_length);
    reference_path_features_list.emplace_back(arc_length, curvature);
    arc_length += kNominalPathSamplingIntervalInMeter;
  }

  // 4. Combine path and speed decisions.
  // Create a concurrent queue to store results during parallel processing
  tbb::concurrent_queue<IntentionDecisionResult> result_queue;
  // Preset the multi tunnel_sequence generation debug for thread safe.
  const int tunnel_number = static_cast<int>(tunnel_sequence_vec.size());
  if (debug != nullptr) {
    debug->mutable_search_based_decision_results()->Reserve(tunnel_number);
    for (int i = 0; i < tunnel_number; ++i) {
      debug->add_search_based_decision_results();
    }
  }
  tbb::parallel_for(
      0, tunnel_number,
      [this, &debug, &result_queue, &tunnel_sequence_vec, &st_rollout_curves,
       &object_intention_info_map, &imaginary_object_intention_info_map,
       &reference_path, &reference_path_features_list, &ego_param,
       &planning_horizon_range, &upper_bound_speed,
       &reusable_last_speed_profile, &scenario_identify_result,
       &lower_bound_speed, &traffic_rule_reasoning_info,
       optional_destination_arclength_m, &trajectory_reasoner,
       &geo_guidance_constraint_manager, &path_reasoner_seed,
       &safe_pass_side](int idx) {
        TRACE_EVENT_SCOPE(planner,
                          AgentIntentionSearcher_GenerateDynamicDecision);
        auto* search_based_decision_debug =
            debug ? debug->mutable_search_based_decision_results(idx) : nullptr;
        auto rollouts_to_use = st_rollout_curves;
        if (FLAGS_planning_enable_static_agent_decision_branching &&
            tunnel_sequence_vec[idx].is_shortest_tunnel &&
            !tunnel_sequence_vec[idx].agent_decisions.yield_agents.empty()) {
          DCHECK(!lower_bound_speed.empty());
          const double init_x = lower_bound_speed[0].x;
          speed::Profile new_profile;
          new_profile.reserve(lower_bound_speed.size());
          for (const auto& state : lower_bound_speed) {
            new_profile.push_back(state);
            new_profile.back().x -= init_x;
          }
          // For shortest tunnel, too fast a speed profile could cause us to not
          // yield the closest object. We will add it to help with decision.
          rollouts_to_use.push_back(
              speed_decider_.FromSpeedProfileToSTRollout(new_profile));
        }
        std::vector<STRolloutDecision> speed_decisions = speed_decider_.Decide(
            tunnel_sequence_vec[idx].agent_decisions, rollouts_to_use,
            object_intention_info_map, ego_param, reference_path,
            reference_path_features_list,
            tunnel_sequence_vec[idx].drivable_corridor,
            optional_destination_arclength_m,
            search_based_decision_debug
                ? search_based_decision_debug->mutable_speed_decider_debug()
                : nullptr);

        if (std::any_of(
                speed_decisions.begin(), speed_decisions.end(),
                [](const auto& speed_decision) {
                  return speed_decision.st_rollout.is_from_speed_search &&
                         !speed_decision.agent_decision_map.empty();
                })) {
          rt_event::PostRtEvent<rt_event::planner::SpeedSearchRolloutSelected>(
              "");
        }

        if (speed_decisions.empty()) {
          auto* decision_result_debug =
              search_based_decision_debug
                  ? search_based_decision_debug->add_decision_results()
                  : nullptr;
          if (decision_result_debug != nullptr) {
            decision_result_debug->set_search_based_decision_result_index(idx);
          }
          auto constraint_manager = std::make_unique<path::ConstraintManager>(
              geo_guidance_constraint_manager);
          // When there is no speed decision, we will generate a default
          // intention result.
          auto intention_decision_result = GenerateIntenionDecision(
              /*speed_decision=*/nullptr, tunnel_sequence_vec[idx],
              object_intention_info_map, imaginary_object_intention_info_map,
              ego_param, scenario_identify_result, traffic_rule_reasoning_info,
              planning_horizon_range, trajectory_reasoner, safe_pass_side,
              reference_path, reusable_last_speed_profile, upper_bound_speed,
              lower_bound_speed, path_reasoner_seed,
              std::move(constraint_manager), search_based_decision_debug,
              decision_result_debug);
          intention_decision_result.drivable_tunnel_index = idx;
          result_queue.push(std::move(intention_decision_result));
          return;
        }

        for (const auto& speed_decision : speed_decisions) {
          auto* decision_result_debug =
              search_based_decision_debug
                  ? search_based_decision_debug->add_decision_results()
                  : nullptr;
          if (decision_result_debug != nullptr) {
            decision_result_debug->set_search_based_decision_result_index(idx);
          }
          auto constraint_manager = std::make_unique<path::ConstraintManager>(
              geo_guidance_constraint_manager);
          auto intention_decision_result = GenerateIntenionDecision(
              &speed_decision, tunnel_sequence_vec[idx],
              object_intention_info_map, imaginary_object_intention_info_map,
              ego_param, scenario_identify_result, traffic_rule_reasoning_info,
              planning_horizon_range, trajectory_reasoner, safe_pass_side,
              reference_path, reusable_last_speed_profile, upper_bound_speed,
              lower_bound_speed, path_reasoner_seed,
              std::move(constraint_manager), search_based_decision_debug,
              decision_result_debug);
          intention_decision_result.drivable_tunnel_index = idx;
          result_queue.push(std::move(intention_decision_result));
        }
      });
  // Collect and sort results from the queue, sort is mainly to avoid AA noise.
  std::vector<IntentionDecisionResult> sorted_results;
  sorted_results.reserve(result_queue.unsafe_size());
  IntentionDecisionResult result;
  while (result_queue.try_pop(result)) {
    sorted_results.push_back(std::move(result));
  }
  std::sort(sorted_results.begin(), sorted_results.end(),
            [](const auto& a, const auto& b) {
              return a.path_reasoning_result.intention_result.total_cost() <
                     b.path_reasoning_result.intention_result.total_cost();
            });

  // In the future, there could be multiple path reasoning results from the
  // same drivable tunnel. For now, it should be okay to use any one, but in
  // the future, might be reasonable to consider the slowest one.
  const auto shortest_tunnel_intention_decision_result_iter = std::find_if(
      sorted_results.begin(), sorted_results.end(),
      [&tunnel_sequence_vec](const auto& intention_decision_result) {
        return tunnel_sequence_vec[intention_decision_result
                                       .drivable_tunnel_index]
            .is_shortest_tunnel;
      });
  std::optional<int> shortest_tunnel_index =
      shortest_tunnel_intention_decision_result_iter == sorted_results.end()
          ? std::nullopt
          : std::make_optional(shortest_tunnel_intention_decision_result_iter
                                   ->drivable_tunnel_index);
  std::optional<int> shortest_tunnel_path_reasoning_result_index =
      shortest_tunnel_intention_decision_result_iter == sorted_results.end()
          ? std::nullopt
          : std::make_optional(
                std::distance(sorted_results.begin(),
                              shortest_tunnel_intention_decision_result_iter));
  // We sort based on total cost, thus the first one is the lowest cost one. If
  // there is no shortest tunnel, we use the lowest cost one.
  int path_reasoning_result_index_for_ignore_all =
      shortest_tunnel_path_reasoning_result_index.value_or(0);
  int shortest_tunnel_index_for_ignore_all =
      sorted_results[path_reasoning_result_index_for_ignore_all]
          .drivable_tunnel_index;

  const int intention_size = static_cast<int>(sorted_results.size());
  std::vector<std::map<TypedObjectId, IntentionResultMetaData>>
      intention_meta_data_map_vec;
  intention_meta_data_map_vec.reserve(intention_size);
  path_reasoning_results.reserve(intention_size);
  // Populate the final vectors
  for (auto& result : sorted_results) {
    intention_meta_data_map_vec.emplace_back(
        std::move(result.intention_meta_data_map));
    path_reasoning_results.emplace_back(
        std::move(result.path_reasoning_result));
  }

  // Try to generate a pure xlane nudge homotopy which only nudge stuck objects
  // in stuck region.
  if (FLAGS_planning_enable_extra_pure_xlane_nudge_homotopy) {
    MaybeGeneratePureXLaneNudgeIntention(
        object_intention_info_map, ego_param, upper_bound_speed,
        geo_guidance_constraint_manager, scenario_identify_result,
        path_reasoner_seed, &path_reasoning_results);
  }

  // TODO(yongbing): Trim the excess homotopy.
  // Add ignore all homotopy.
  DCHECK(!path_reasoning_results.empty());
  pb::IgnoreAllHomotopyDebug ignore_all_debug;
  bool has_generated_ignore_all_homotopy = false;
  bool should_generate_ignore_all = false;
  // Check if IGNORE_All has been generated in agent_intention_searcher.
  has_generated_ignore_all_homotopy =
      std::any_of(path_reasoning_results.begin(), path_reasoning_results.end(),
                  [](const auto& agent_constraint) {
                    return agent_constraint.intention_result.homotopy() ==
                           pb::IntentionResult::IGNORE_ALL;
                  });
  // If IGNORE_ALL has not been generated, then check if an additional one needs
  // to be generated.
  if (!FLAGS_path_planning_skip_ignore_homotopy &&
      !has_generated_ignore_all_homotopy) {
    should_generate_ignore_all =
        ShouldGenerateIgnoreAllIntentionResultMetaDataMap(
            ego_param, intention_meta_data_map_vec, object_intention_info_map,
            traffic_rule_reasoning_info, scenario_identify_result,
            frame_analyzer_results, robot_state_snapshot, pull_over_destination,
            reference_path, lane_sequence, behavior_type_, ignore_all_debug);
  }

  const PathIntentionsCrossEvaluator path_intentions_cross_evaluator(
      path_reasoning_results);
  std::vector<TypedObjectId> object_id_vec;
  object_id_vec.reserve(object_intention_info_map.size());
  for (const auto& object_intention_info : object_intention_info_map) {
    object_id_vec.push_back(object_intention_info.first);
  }

  const std::unordered_map<TypedObjectId,
                           std::unordered_set<pb::SnapshotIntention::PassState>>
      object_possible_pass_states_map =
          path_intentions_cross_evaluator.EvaluateObjectsPossiblePassState(
              object_id_vec);

  if (planner::FLAGS_planning_enable_path_nudge_back) {
    // Apply nudge back constraint to yield tunnel trajectory.
    if (shortest_tunnel_path_reasoning_result_index.has_value() &&
        !tunnel_sequence_vec[*shortest_tunnel_index]
             .agent_decisions.yield_agents.empty()) {
      // Find valid yield tunnel. Add nudge back constraint to path reasoning
      // result generated by this tunnel.
      const int yield_candidate_index =
          *shortest_tunnel_path_reasoning_result_index;
      std::optional<NudgeBackConstraintMetaData> optional_nudge_back_meta =
          ObtainOptionalNudgeBackConstraint(
              tunnel_sequence_vec[*shortest_tunnel_index],
              path_reasoning_results[yield_candidate_index],
              object_intention_info_map, object_possible_pass_states_map);
      if (optional_nudge_back_meta.has_value()) {
        // Optional yield arclength is valid, we want to add a nudge back
        // constraint to make sure we will laterally yield at the arclength.
        AddNudgeBackConstraint(optional_nudge_back_meta.value(),
                               ego_param.front_bumper_arclength,
                               path_reasoning_results[yield_candidate_index]);
      }
    }
  }

  // Deduplicate identical intentions
  DedupePathReasoningResults(path_reasoning_results);

  // Populate debug info about the reasons for generating IGNORE_ALL or not.
  ignore_all_debug.set_has_generated_ignore_all_homotopy(
      has_generated_ignore_all_homotopy);
  ignore_all_debug.set_should_generate_ignore_all(should_generate_ignore_all);
  if (debug) {
    debug->mutable_ignore_all_homotopy_debug()->Swap(&ignore_all_debug);
  }
  if (!has_generated_ignore_all_homotopy && !should_generate_ignore_all) {
    rt_event::PostRtEvent<
        rt_event::planner::PlannerTrimAdditionalIgnoreHomotopy>();
  }

  if (!FLAGS_path_planning_skip_ignore_homotopy &&
      !config_.skip_back_up_ignore_all_intention() &&
      !has_generated_ignore_all_homotopy && should_generate_ignore_all) {
    TRACE_EVENT_SCOPE(planner, AgentIntentionSearcher_GenerateBackupIntention);
    DCHECK(!intention_meta_data_map_vec.empty());
    // All path reasonings results are sorted by total cost, so the first one is
    // the shortest one.
    const int min_cost_idx = 0;
    const int path_reasoning_result_index_to_use =
        FLAGS_planning_enable_static_agent_decision_branching
            ? path_reasoning_result_index_for_ignore_all
            : min_cost_idx;
    DCHECK_LT(path_reasoning_result_index_to_use,
              intention_meta_data_map_vec.size());
    path_reasoning_results.push_back(GenerateIgnoreAllPathReasoningResult(
        ego_param, object_intention_info_map,
        imaginary_object_intention_info_map,
        intention_meta_data_map_vec[path_reasoning_result_index_to_use],
        reference_path,
        path_reasoning_results[path_reasoning_result_index_to_use]
            .intention_result.selected_speed_profile(),
        reusable_last_speed_profile.value_or(upper_bound_speed),
        lower_bound_speed, geo_guidance_constraint_manager, config_,
        drivable_space_corridor.driving_lateral_corridor,
        lane_keep_behavior_type_, behavior_type_,
        scenario_identify_result.scenario_type, safe_pass_side,
        path_reasoner_seed));
    auto* mutable_search_based_decision_results =
        debug ? debug->add_search_based_decision_results() : nullptr;
    pb::SearchBasedDecisionResult::DecisionResult* decision_result_debug =
        nullptr;
    if (mutable_search_based_decision_results) {
      decision_result_debug =
          mutable_search_based_decision_results->add_decision_results();
      PopulateRepulsionsDebug(
          *path_reasoning_results.back().homotopic_constraint_manager,
          mutable_search_based_decision_results
              ->mutable_repulsion_reasoning_object_list());
      PopulateRepulsionsDebug(
          *path_reasoning_results.back().homotopic_constraint_manager,
          decision_result_debug->mutable_repulsion_reasoning_object_list());
      PopulateDrivableTunnelDecisionDebug(
          tunnel_sequence_vec[shortest_tunnel_index_for_ignore_all],
          mutable_search_based_decision_results
              ->mutable_drivable_tunnel_decision());
      (*mutable_search_based_decision_results->mutable_intention_result()) =
          path_reasoning_results.back().intention_result;
      PopulateDrivableTunnelDecisionDebug(
          tunnel_sequence_vec[shortest_tunnel_index_for_ignore_all],
          decision_result_debug->mutable_drivable_tunnel_decision());
      (*decision_result_debug->mutable_intention_result()) =
          path_reasoning_results.back().intention_result;
    }
    path_reasoning_results.back().intention_decision_debug =
        mutable_search_based_decision_results;
    path_reasoning_results.back().decision_result_debug = decision_result_debug;
  }
  return path_reasoning_results;
}

IntentionDecisionResult AgentIntentionSearcher::GenerateIntenionDecision(
    const STRolloutDecision* speed_decision, const DrivableTunnel& tunnel,
    const std::map<TypedObjectId, ObjectIntentionInfo>&
        object_intention_info_map,
    const std::map<TypedObjectId, ObjectIntentionInfo>&
        imaginary_object_intention_info_map,
    const EgoInLaneParams& ego_param,
    const path::ScenarioIdentifierResult& scenario_identify_result,
    const pb::TrafficRuleReasoningInfoDebug& traffic_rule_reasoning_info,
    const math::Range1d& planning_horizon_range,
    const TrajectoryReasoner& trajectory_reasoner,
    const pb::SnapshotIntention::PassState safe_pass_side,
    const math::geometry::PolylineCurve2d& reference_path,
    const std::optional<speed::Profile>& reusable_last_speed_profile,
    const speed::Profile& upper_bound_speed,
    const speed::Profile& lower_bound_speed,
    const pb::PathReasonerSeed& path_reasoner_seed,
    std::unique_ptr<path::ConstraintManager> constraint_manager,
    pb::SearchBasedDecisionResult* search_based_decision_debug,
    pb::SearchBasedDecisionResult::DecisionResult* decision_result_debug) {
  IntentionDecisionResult result;
  speed::Profile speed_decision_profile = upper_bound_speed;
  if (speed_decision != nullptr) {
    speed_decision_profile =
        GetSpeedProfileFromSTRollout(speed_decision->st_rollout, ego_param);
  }

  result.intention_meta_data_map = GenerateAgentIntentionResultMetaDataMap(
      /*path_decision=*/tunnel.agent_decisions, speed_decision,
      object_intention_info_map, ego_param, scenario_identify_result,
      traffic_rule_reasoning_info, planning_horizon_range, trajectory_reasoner);

  // TODO(wanghao): Identify the scenario based on intention_meta_data_map
  result.path_reasoning_result.intention_result = GenerateAgentIntentionResult(
      result.intention_meta_data_map, object_intention_info_map,
      speed::ToProto(speed_decision_profile),
      /*path_decision_cost=*/tunnel.total_cost,
      /*speed_decision_cost=*/
      (speed_decision != nullptr ? speed_decision->total_cost : 0.0), config_,
      lane_keep_behavior_type_, behavior_type_,
      scenario_identify_result.scenario_type, safe_pass_side,
      ego_param.speed_mps);

  RepulsionMetaManager::AppendRepulsions(
      result.intention_meta_data_map, object_intention_info_map, reference_path,
      ego_param, speed_decision_profile,
      reusable_last_speed_profile.value_or(upper_bound_speed),
      lower_bound_speed,
      /*is_from_ignore_homotopy=*/false,
      tunnel.drivable_corridor.driving_lateral_corridor, *constraint_manager);
  RepulsionMetaManager::AppendRepulsions(
      result.intention_meta_data_map, imaginary_object_intention_info_map,
      reference_path, ego_param, speed_decision_profile,
      reusable_last_speed_profile.value_or(upper_bound_speed),
      lower_bound_speed,
      /*is_from_ignore_homotopy=*/false,
      tunnel.drivable_corridor.driving_lateral_corridor, *constraint_manager);

  // debug for path reasoning.
  if (decision_result_debug != nullptr) {
    PopulateIntentionMetaDataDebug(
        result.intention_meta_data_map,
        search_based_decision_debug->mutable_reasoning_objects());
    PopulateIntentionMetaDataDebug(
        result.intention_meta_data_map,
        decision_result_debug->mutable_reasoning_objects());

    PopulateRepulsionsDebug(
        *constraint_manager,
        search_based_decision_debug->mutable_repulsion_reasoning_object_list());
    PopulateRepulsionsDebug(
        *constraint_manager,
        decision_result_debug->mutable_repulsion_reasoning_object_list());
    if (speed_decision != nullptr) {
      decision_result_debug->set_rollout_index(
          speed_decision->st_rollout_index);
    }
  }

  // Flat the path constraints from intention meta datas.
  FlattenAgentConstraints(result.intention_meta_data_map,
                          object_intention_info_map,
                          result.path_reasoning_result.agent_constraints);
  result.path_reasoning_result.homotopic_constraint_manager =
      std::move(constraint_manager);
  // populate risky agents id set.
  PopulateRiskyAgentsIdSet(
      result.intention_meta_data_map,
      result.path_reasoning_result.risky_agents_id_set,
      search_based_decision_debug
          ? search_based_decision_debug->mutable_risky_agents_id_set()
          : nullptr);
  PopulateRiskyAgentsIdSet(
      result.intention_meta_data_map,
      result.path_reasoning_result.risky_agents_id_set,
      decision_result_debug
          ? decision_result_debug->mutable_risky_agents_id_set()
          : nullptr);

  // debug for downstream.
  if (search_based_decision_debug != nullptr) {
    PopulateDrivableTunnelDecisionDebug(
        tunnel,
        search_based_decision_debug->mutable_drivable_tunnel_decision());
    (*search_based_decision_debug->mutable_intention_result()) =
        result.path_reasoning_result.intention_result;

    PopulateDrivableTunnelDecisionDebug(
        tunnel, decision_result_debug->mutable_drivable_tunnel_decision());
    (*decision_result_debug->mutable_intention_result()) =
        result.path_reasoning_result.intention_result;
  }
  result.path_reasoning_result.intention_decision_debug =
      search_based_decision_debug ? search_based_decision_debug : nullptr;
  result.path_reasoning_result.decision_result_debug = decision_result_debug;
  result.path_reasoning_result.path_reasoner_seed = path_reasoner_seed;
  return result;
}

}  // namespace planner
