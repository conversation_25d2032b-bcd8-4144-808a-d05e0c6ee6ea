package(default_visibility = ["//visibility:public"])

cc_library(
    name = "overlap",
    srcs = [
        "ego_lateral_clearance.cpp",
        "object_proximity_computer.cpp",
        "overlap_computer.cpp",
        "overlap_lib_util.cpp",
        "path_out_of_lane_lib.cpp",
    ],
    hdrs = [
        "ego_lateral_clearance.h",
        "object_proximity_computer.h",
        "overlap_computer.h",
        "overlap_lib_util.h",
        "path_out_of_lane_lib.h",
    ],
    copts = select({
        "@//bazel/platforms:is_ubuntu18_amd64": ["-mavx2 -mfma"],
        "@//bazel/platforms:is_ubuntu22_amd64": ["-mavx2 -mfma"],
        "//conditions:default": [],
    }),
    include_prefix = "planner/speed/overlap",
    local_defines = [
        "EIGEN_MALLOC_ALREADY_ALIGNED",
    ],
    deps = [
        "//onboard/common/adv_geom",
        "//onboard/common/latency",
        "//onboard/common/math",
        "//onboard/common/xbase:geometry",
        "//onboard/planner:constants",
        "//onboard/planner/decoupled_maneuvers/predicted_trajectory_wrapper",
        "//onboard/planner/path/path_solver/constraint_manager:road_boundaries",
        "//onboard/planner/world_model/construction_zone",
        "//protobuf_cpp:protos_cpp",
    ],
)
