package(default_visibility = ["//visibility:public"])

cc_library(
    name = "lateral_function_main",
    srcs = [
        "lateral_function_main.c",
    ],
    hdrs = [
        "lateral_function_main.h",
    ],
    copts = [
        "-x",
        "c",
        "-std=c11",
        "-Wno-missing-field-initializers",
    ],
    include_prefix = "lateral_function/",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_lateral_function",
        "//offboard/fallback_ads/common_math_library:common_method",
        "//offboard/fallback_ads/common_math_library:debounce_method",
        "//offboard/fallback_ads/common_math_library:vehicle_parameters",
        "//offboard/fallback_ads/lateral_function:lateral_function_parameter",
        "//offboard/fallback_ads/lateral_function/boundary_data_processing",
        "//offboard/fallback_ads/lateral_function/lane_center:check_lane_center",
        "//offboard/fallback_ads/lateral_function/lateral_control:lqr_controller",
        "@voy-sdk//:glog",
    ],
)

cc_library(
    name = "lateral_function_parameter",
    srcs = [
        "lateral_function_parameter.cpp",
    ],
    hdrs = [
        "lateral_function_parameter.h",
    ],
    copts = [
        "-x",
        "c++",
        "-std=c++14",
    ],
    include_prefix = "lateral_function/",
    deps = [
        "//offboard/fallback_ads/common_math_library:interpolation",
    ],
)

cc_library(
    name = "lateral_function_type",
    hdrs = [
        "lateral_function_type.h",
    ],
    copts = [
        "-x",
        "c",
        "-std=c11",
    ],
    include_prefix = "lateral_function/",
)
