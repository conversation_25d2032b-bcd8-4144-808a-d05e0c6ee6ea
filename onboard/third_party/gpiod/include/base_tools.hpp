/**
 * @file base_tools.hpp
 * <AUTHOR>
 * @brief
 * @version 0.1
 * @date 2024-06-18
 *
 * @copyright Copyright (c) 2024
 *
 */

#ifndef __BASE_TOOLS_HPP__
#define __BASE_TOOLS_HPP__

#include <sys/stat.h>

#include <atomic>
#include <cstring>
#include <ctime>
#include <iomanip>
#include <sstream>
#include <string>
#include <vector>

namespace voyager {
namespace hardware {
inline int64_t get_timestamp_ms() {
  struct timespec spec;
  if (clock_gettime(CLOCK_REALTIME, &spec) < 0) return 0;
  int64_t s = spec.tv_sec;
  int64_t ms = spec.tv_nsec;
  return (s * 1000 + ms / 1000000);
}
inline int get_system_output(const char *cmd, char *output, size_t size) {
  FILE *fp = popen(cmd, "r");
  if (fp == nullptr) return -1;

  std::string out;
  while (true) {
    char arr[1024] = {0};
    if (fgets(arr, sizeof(arr), fp) == nullptr) break;
    out += arr;
  }

  if (out.size() == 0) goto err_exit;
  if (out.size() > size) goto err_exit;
  memmove(output, out.c_str(), out.size());
  // if (output[strlen(output) - 1] == '\n') output[strlen(output) - 1] = '\0';

  pclose(fp);
  return 0;
err_exit:
  pclose(fp);
  return -1;
}
inline size_t string_split(const std::string &str, const std::string &delim,
                           std::vector<std::string> &substr,
                           std::string &remain) {
  std::size_t pos = 0;
  while (true) {
    std::size_t n = str.find(delim, pos);
    if (n == std::string::npos) {
      remain = std::string(str, pos, str.size() - pos);
      break;
    }
    substr.emplace_back(str, pos, n - pos);
    pos = (n + delim.size());
  }
  // for (auto &iter : substr) std::cout << "substr:" << iter << std::endl;
  // std::cout << "remain:" << remain << std::endl;
  return pos;
}
inline std::string bin_to_hex(const unsigned char *data, size_t size) {
  std::ostringstream strHex;
  strHex << std::hex << std::setfill('0');
  for (size_t i = 0; i < size; ++i) {
    strHex << std::setw(2) << static_cast<unsigned int>(data[i]);
    strHex << " ";
  }
  return strHex.str();
}

}  // namespace hardware
}  // namespace voyager

#endif  // __BASE_TOOLS_HPP__
