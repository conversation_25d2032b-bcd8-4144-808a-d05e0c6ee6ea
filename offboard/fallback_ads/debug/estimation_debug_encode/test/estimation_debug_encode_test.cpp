#include "debug/estimation_debug_encode/estimation_debug_encode.h"

#include <gtest/gtest.h>

#include <cmath>
#include <cstdint>

#include "_out/Appl/GenData/Components/Rte_Debug_Type.h"
#include "_out/Appl/GenData/Rte_Type.h"
#include "common_math_library/can_util.h"
#include "offboard/fallback_ads/fallback_simulation.h"

namespace fallback_adas {
namespace debug {

TEST(EstimationDebugEncode, TimestampTest) {
  auto& raw_estimation_debug =
      Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms;

  // Create a ControlDebug object
  EstimationDebug estimation_debug = {};
  // estimation_debug.timestamp = 20015998341291.
  estimation_debug.timestamp_ms = 0x1234567890AB;

  EstimationDebugEncode(estimation_debug);

  auto& timestamp_bytes =
      raw_estimation_debug.Rte_FallbackPoseDebug_PoseDebugTimeStamp.value;
  EXPECT_EQ(timestamp_bytes[0], 0x12);
  EXPECT_EQ(timestamp_bytes[1], 0x34);
  EXPECT_EQ(timestamp_bytes[2], 0x56);
  EXPECT_EQ(timestamp_bytes[3], 0x78);
  EXPECT_EQ(timestamp_bytes[4], 0x90);
  EXPECT_EQ(timestamp_bytes[5], 0xAB);

  uint64_t timestamp_ms = 0;
  common::BytesToUint64(timestamp_bytes,
                        sizeof(timestamp_bytes) / sizeof(timestamp_bytes[0]),
                        &timestamp_ms, true);
  EXPECT_EQ(timestamp_ms, estimation_debug.timestamp_ms);
}

TEST(EstimationDebug, EstimationDebugTest) {
  auto& raw_estimation_debug =
      Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms;

  // Create a ControlDebug object
  EstimationDebug estimation_debug = {};
  estimation_debug.state.x = 0.1F;
  estimation_debug.state.y = 0.2F;
  estimation_debug.state.yaw = 0.3F;
  estimation_debug.state.vel = 0.4F;
  estimation_debug.state.x_std = 0.5F;
  estimation_debug.state.y_std = 0.6F;
  estimation_debug.state.yaw_std = 0.7F;
  estimation_debug.state.vel_std = 0.8F;
  estimation_debug.state_type = kMcuPosePassThrough;
  estimation_debug.is_stationary = static_cast<boolean>(true);
  estimation_debug.sideslip_angle = 0.9F;

  EstimationDebugEncode(estimation_debug);

  EXPECT_EQ(
      raw_estimation_debug.Rte_FallbackPoseDebug_PoseDebugPositionX.value,
      static_cast<int32_t>(std::roundf(estimation_debug.state.x / 0.001F)));
  EXPECT_EQ(
      raw_estimation_debug.Rte_FallbackPoseDebug_PoseDebugPositionY.value,
      static_cast<int32_t>(std::roundf(estimation_debug.state.y / 0.001F)));
  EXPECT_EQ(
      raw_estimation_debug.Rte_FallbackPoseDebug_PoseDebugYaw.value,
      static_cast<int32_t>(std::roundf(estimation_debug.state.yaw / 1e-6F)));
  EXPECT_EQ(
      raw_estimation_debug.Rte_FallbackPoseDebug_PoseDebugSpeed.value,
      static_cast<int32_t>(std::roundf(estimation_debug.state.vel / 0.001F)));
  EXPECT_EQ(
      raw_estimation_debug.Rte_FallbackPoseDebug_PoseDebugPositionXStd.value,
      static_cast<int32_t>(std::roundf(estimation_debug.state.x_std / 1e-5F)));
  EXPECT_EQ(
      raw_estimation_debug.Rte_FallbackPoseDebug_PoseDebugPositionYStd.value,
      static_cast<int32_t>(std::roundf(estimation_debug.state.y_std / 1e-5F)));
  EXPECT_EQ(raw_estimation_debug.Rte_FallbackPoseDebug_PoseDebugYawStd.value,
            static_cast<int32_t>(
                std::roundf(estimation_debug.state.yaw_std / 1e-5F)));
  EXPECT_EQ(raw_estimation_debug.Rte_FallbackPoseDebug_PoseDebugSpeedStd.value,
            static_cast<int32_t>(
                std::roundf(estimation_debug.state.vel_std / 1e-5F)));

  EXPECT_EQ(raw_estimation_debug.Rte_FallbackPoseDebug_PoseDebugStateType.value,
            static_cast<EstimationStateType>(estimation_debug.state_type));

  EXPECT_EQ(
      raw_estimation_debug.Rte_FallbackPoseDebug_PoseDebugIsStationary.value,
      static_cast<boolean>(estimation_debug.is_stationary));

  EXPECT_EQ(
      raw_estimation_debug.Rte_FallbackPoseDebug_PoseDebugSideSlipAngle.value,
      static_cast<int32_t>(
          std::roundf(estimation_debug.sideslip_angle / 1e-6F)));
}

TEST(EstimationDebug, EstimationStateTypeTest) {
  auto& raw_estimation_debug =
      Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms;

  // Create a ControlDebug object
  EstimationDebug estimation_debug = {};

  // kMcuUnknownEstimationState
  estimation_debug.state_type = kMcuUnknownEstimationState;
  EstimationDebugEncode(estimation_debug);
  EXPECT_EQ(raw_estimation_debug.Rte_FallbackPoseDebug_PoseDebugStateType.value,
            kMcuUnknownEstimationState);

  // kMcuPosePassThrough
  estimation_debug.state_type = kMcuPosePassThrough;
  EstimationDebugEncode(estimation_debug);
  EXPECT_EQ(raw_estimation_debug.Rte_FallbackPoseDebug_PoseDebugStateType.value,
            kMcuPosePassThrough);

  // kMcuCanbusGnssFusion
  estimation_debug.state_type = kMcuCanbusGnssFusion;
  EstimationDebugEncode(estimation_debug);
  EXPECT_EQ(raw_estimation_debug.Rte_FallbackPoseDebug_PoseDebugStateType.value,
            kMcuCanbusGnssFusion);

  // kMcuCanbusDeadReckoning
  estimation_debug.state_type = kMcuCanbusDeadReckoning;
  EstimationDebugEncode(estimation_debug);
  EXPECT_EQ(raw_estimation_debug.Rte_FallbackPoseDebug_PoseDebugStateType.value,
            kMcuCanbusDeadReckoning);
}

}  // namespace debug
}  // namespace fallback_adas
