#include "planner/path/path_solver/nominal_path_searcher/nominal_path_searcher.h"

#include <utility>
#include <vector>

#include "geometry/model/polyline_curve.h"
#include "planner_protos/path_searcher_debug.pb.h"
#include "planner_protos/path_seed.pb.h"

using adv_geom::PathCurve2d;
using math::geometry::PolylineCurve2d;

namespace planner {
namespace path {
namespace {

void PopulatePathSearcherInputDebug(
    const path_problem::PathProblem& path_problem,
    pb::PathSearcherInputDebug* debug) {
  // Populate reference path.
  const PathCurve2d& reference_path = path_problem.reference_path();
  reference_path.ToProto(debug->mutable_reference_path());
  // Populate hard boundaries.
  const std::vector<BoundaryElement>& left_hard_boundaries =
      path_problem.road_boundaries()
          .path_search_boundaries.left_boundary_elements;
  for (const BoundaryElement& boundary_element : left_hard_boundaries) {
    boundary_element.boundary_line.ToProto(debug->add_left_hard_boundaries());
  }
  const std::vector<BoundaryElement>& right_hard_boundaries =
      path_problem.road_boundaries()
          .path_search_boundaries.right_boundary_elements;
  for (const BoundaryElement& boundary_element : right_hard_boundaries) {
    boundary_element.boundary_line.ToProto(debug->add_right_hard_boundaries());
  }
  // Populate attraction segments.
  const std::vector<AttractionSegment>& attraction_segments =
      path_problem.constraints().attractions().attraction_segments();
  for (const AttractionSegment& att_seg : attraction_segments) {
    att_seg.raw_attraction_polyline().ToProto(debug->add_attraction_segments());
  }
}

}  // namespace

std::optional<PathSearchResult> NominalPathSearcher::Search(
    const path_problem::PathProblem& path_problem,
    const pb::StateLatticeDrivableSpaceSeed& prev_lattice_space_seed,
    pb::StateLatticeDrivableSpaceSeed* cur_lattice_space_seed,
    pb::PathSearcherDebug* path_searcher_debug) {
  const auto& meta_data = path_problem.meta_data();

  const auto& robot_snapshot =
      meta_data.robot_state->plan_init_state_snapshot();
  const auto& reference_path = path_problem.reference_path();
  const auto& ego_start_position =
      meta_data.motion_mode == pb::MotionMode::BACKWARD
          ? robot_snapshot.rear_bumper_position()
          : robot_snapshot.front_bumper_position();
  const double ego_start_arclength =
      reference_path
          .GetProximity(ego_start_position, math::pb::UseExtensionFlag::kForbid)
          .arc_length;
  if (math::NearZero(reference_path.GetTotalArcLength() -
                     ego_start_arclength)) {
    // Ego is already at the reference path end.
    return std::nullopt;
  }

  // Early search failure if ego is ahead of the end of reference path.
  if (path_problem.meta_data().robot_in_lane_param.arclength_m >=
      reference_path.GetTotalArcLength()) {
    LOG(ERROR) << "Search failed because ego is ahead of the end of "
                  "reference path: ego_arclength="
               << path_problem.meta_data().robot_in_lane_param.arclength_m
               << ", reference_path_total_arclength="
               << reference_path.GetTotalArcLength();
    return std::nullopt;
  }

  // Populate path input debug.
  if (path_searcher_debug) {
    PopulatePathSearcherInputDebug(
        path_problem, path_searcher_debug->mutable_path_searcher_input_debug());
  }

  return GeneratePathWithStateLatticeSearch(
      path_problem, prev_lattice_space_seed, cur_lattice_space_seed,
      path_searcher_debug
          ? path_searcher_debug->mutable_state_lattice_search_debug()
          : nullptr);
}

std::optional<PathSearchResult>
NominalPathSearcher::GeneratePathWithStateLatticeSearch(
    const path_problem::PathProblem& path_problem,
    const pb::StateLatticeDrivableSpaceSeed& prev_lattice_space_seed,
    pb::StateLatticeDrivableSpaceSeed* cur_lattice_space_seed,
    pb::StateLatticeSearchDebug* path_searcher_debug) {
  TRACE_EVENT_SCOPE(planner, NominalPathSearcher_StateLatticeSearch);

  // Updates the StateLatticeDrivableSpace.
  state_lattice_drivable_space_.Update(
      path_problem, prev_lattice_space_seed, cur_lattice_space_seed,
      path_searcher_debug
          ? path_searcher_debug->mutable_state_lattice_drivable_space_debug()
          : nullptr);

  lattice_search::NodeTrajectoryStateInfo* final_trajectory_state_info =
      state_lattice_path_search_.Search(path_problem);
  if (!final_trajectory_state_info) {
    return std::nullopt;
  }

  return state_lattice_search_result_collector_.Collect(
      state_lattice_drivable_space_.search_start_trajectory_state_info(),
      path_problem, final_trajectory_state_info,
      path_searcher_debug
          ? path_searcher_debug->mutable_state_lattice_searcher_debug()
          : nullptr);
}

}  // namespace path
}  // namespace planner
