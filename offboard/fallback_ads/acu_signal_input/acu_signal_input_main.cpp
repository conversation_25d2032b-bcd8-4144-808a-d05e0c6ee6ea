#include "acu_signal_input/acu_signal_input_main.h"

#include <cstdint>

#include "_out/Appl/GenData/Components/Rte_AcuSigInput.h"
#include "_out/Appl/GenData/Rte_Type.h"
#include "acu_signal_input/pose_decode/pose_decode.h"
#include "acu_signal_input/time_sync/time_sync.h"
#include "acu_signal_input/trajectory_decode/trajectory_decode.h"

extern "C" void AcuSigInput_Init() {
  TrajectoryInput trajectory = {0};
  Rte_IWrite_AcuSigInput_Init_Trajectory_Trajectory(&trajectory);

  LocalizationPoseInput pose = {0};
  Rte_IWrite_AcuSigInput_Init_LocalizationPose_LocalizationPose(&pose);

  GnssPoseInput gnss_pose = {0};
  Rte_IWrite_AcuSigInput_Init_GnssPose_GnssPose(&gnss_pose);

  AcuControl acu_control = {0};
  Rte_IWrite_AcuSigInput_Init_AcuControl_AcuControl(&acu_control);
}

extern "C" void PoseInput_10ms() {
  LocalizationPoseInput pose = {0};
  fallback_adas::acu_signal_input::LocalizationPoseDecode(&pose);
  Rte_IWrite_PoseInput_10ms_LocalizationPose_LocalizationPose(&pose);

  GnssPoseInput gnss_pose = {0};
  fallback_adas::acu_signal_input::GnssPoseDecode(&gnss_pose);
  Rte_IWrite_PoseInput_10ms_GnssPose_GnssPose(&gnss_pose);

  // Get timestamp.
  uint8_t acu_pose_counter =
      Rte_IRead_PoseInput_10ms_AcuPose_AcuPoseRollingCounter();
  uint32_t can_timestamp_ms =
      Rte_IRead_PoseInput_10ms_AcuPose_GnssPoseReserve1();
  uint32_t timestamp_ms = fallback_adas::acu_signal_input::GetCurrentTimestamp(
      acu_pose_counter, pose.timestamp_ms, can_timestamp_ms);
  Rte_IWrite_PoseInput_10ms_Timestamp_Timestamp(timestamp_ms);
}

extern "C" void TrajectoryInput_60ms() {
  // trajectory input decode.
  TrajectoryInput trajectory = {0};
  fallback_adas::acu_signal_input::TrajectoryDecode(&trajectory);
  Rte_IWrite_TrajectoryInput_60ms_Trajectory_Trajectory(&trajectory);

  // acu control input decode.
  // TODO(lifanjie): Acu control has an update frequency of 10ms, currently only
  // 100ms is available.
  AcuControl acu_control = {0};
  fallback_adas::acu_signal_input::AcuControlDecode(&acu_control);
  Rte_IWrite_TrajectoryInput_60ms_AcuControl_AcuControl(&acu_control);
}
