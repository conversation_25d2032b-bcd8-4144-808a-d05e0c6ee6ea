#include "debug/estimation_debug_encode/estimation_debug_encode.h"

#include <algorithm>
#include <cmath>
#include <cstddef>
#include <cstdint>

#include "_out/Appl/GenData/Components/Rte_Debug.h"
#include "_out/Appl/GenData/Components/Rte_Debug_Type.h"
#include "_out/Appl/GenData/Rte_Type.h"
#include "common_math_library/can_util.h"
#include "common_math_library/common_method.h"

namespace fallback_adas {
namespace debug {

void EstimationStateDebugEncode(
    const EstimationStateDebug& estimation_state_debug) {
  // position x
  float pos_x = std::roundf(estimation_state_debug.x / 0.001F);
  pos_x = CML_Limit(pos_x, PoseDebugPositionX_adt_LowerLimit,
                    static_cast<float>(PoseDebugPositionX_adt_UpperLimit));
  Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugPositionX(
      static_cast<int32_t>(pos_x));

  // position y
  float pos_y = std::roundf(estimation_state_debug.y / 0.001F);
  pos_y = CML_Limit(pos_y, PoseDebugPositionY_adt_LowerLimit,
                    static_cast<float>(PoseDebugPositionY_adt_UpperLimit));
  Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugPositionY(
      static_cast<int32_t>(pos_y));

  // yaw
  float yaw = std::roundf(estimation_state_debug.yaw / 1e-6F);
  yaw =
      CML_Limit(yaw, PoseDebugYaw_adt_LowerLimit, PoseDebugYaw_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugYaw(
      static_cast<int32_t>(yaw));

  // velocity
  float vel = std::roundf(estimation_state_debug.vel / 0.001F);
  vel = CML_Limit(vel, PoseDebugSpeed_adt_LowerLimit,
                  PoseDebugSpeed_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugSpeed(
      static_cast<int32_t>(vel));

  // position x standard deviation
  float pos_x_std = std::roundf(estimation_state_debug.x_std / 1e-5F);
  pos_x_std = CML_Limit(pos_x_std, PoseDebugPositionXStd_adt_LowerLimit,
                        PoseDebugPositionXStd_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugPositionXStd(
      static_cast<uint32_t>(pos_x_std));

  // position y standard deviation
  float pos_y_std = std::roundf(estimation_state_debug.y_std / 1e-5F);
  pos_y_std = CML_Limit(pos_y_std, PoseDebugPositionYStd_adt_LowerLimit,
                        PoseDebugPositionYStd_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugPositionYStd(
      static_cast<uint32_t>(pos_y_std));

  // yaw standard deviation
  float yaw_std = std::roundf(estimation_state_debug.yaw_std / 1e-5F);
  yaw_std = CML_Limit(yaw_std, PoseDebugYawStd_adt_LowerLimit,
                      PoseDebugYawStd_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugYawStd(
      static_cast<uint32_t>(yaw_std));

  // velocity standard deviation
  float vel_std = std::roundf(estimation_state_debug.vel_std / 1e-5F);
  vel_std = CML_Limit(vel_std, PoseDebugSpeedStd_adt_LowerLimit,
                      PoseDebugSpeedStd_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugSpeedStd(
      static_cast<uint32_t>(vel_std));
}

void EstimationDebugEncode(const EstimationDebug& estimation_debug) {
  // timestamp
  uint8_t timestamp_bytes[6] = {0};
  common::Uint64ToBytes(estimation_debug.timestamp_ms, timestamp_bytes,
                        sizeof(timestamp_bytes), true);
  Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugTimeStamp(timestamp_bytes);

  // estimation state debug
  EstimationStateDebugEncode(estimation_debug.state);

  // state type
  Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugStateType(
      static_cast<uint8_t>(estimation_debug.state_type & 0xf));

  // is stationary
  Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugIsStationary(
      static_cast<uint8_t>(estimation_debug.is_stationary));

  // sideslip angle
  float sideslip_angle = std::roundf(estimation_debug.sideslip_angle / 1e-6F);
  sideslip_angle =
      CML_Limit(sideslip_angle, PoseDebugSideSlipAngle_adt_LowerLimit,
                PoseDebugSideSlipAngle_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugSideSlipAngle(
      static_cast<int32_t>(sideslip_angle));

  // TODO(lifanjie): Add "dead_reckoning_state" later
}
}  // namespace debug
}  // namespace fallback_adas
