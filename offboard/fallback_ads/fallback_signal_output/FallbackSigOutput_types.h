/*
 * File: FallbackSigOutput_types.h
 *
 * Code generated for Simulink model 'FallbackSigOutput'.
 *
 * Model version                  : 1.318
 * Simulink Coder version         : 9.5 (R2021a) 14-Nov-2020
 * C/C++ source code generated on : Sun Jun  8 12:40:32 2025
 *
 * Target selection: autosar.tlc
 * Embedded hardware selection: Infineon->TriCore
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_FallbackSigOutput_types_h_
#define RTW_HEADER_FallbackSigOutput_types_h_
#include "common_math_library/rtwtypes.h"
#include "_out/Appl/GenData/Rte_Type.h"

/* Model Code Variants */
#ifndef DEFINED_TYPEDEF_FOR_CAN_MESSAGE_BUS_
#define DEFINED_TYPEDEF_FOR_CAN_MESSAGE_BUS_

typedef struct {
  uint8 Extended;
  uint8 Length;
  uint8 Remote;
  uint8 Error;
  uint32 ID;
  float64 Timestamp;
  uint8 Data[8];
} CAN_MESSAGE_BUS;

#endif

/* Parameters (default storage) */
typedef struct P_FallbackSigOutput_T_ P_FallbackSigOutput_T;

#endif                               /* RTW_HEADER_FallbackSigOutput_types_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
