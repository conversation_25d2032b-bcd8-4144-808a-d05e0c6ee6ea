#include "common_math_library/interpolation.h"

#include <gtest/gtest.h>

TEST(InterpolationTest, LinearInterpolation) {
  CML_Table1D table[] = {{0.0F, 0.0F}, {1.0F, 10.0F}};
  EXPECT_FLOAT_EQ(CML_Interpolation1D(0.5F, table, 2), 5.0F);
}

TEST(InterpolationTest, ExtrapolationBelow) {
  CML_Table1D table[] = {{0.0F, 0.0F}, {1.0F, 1.0F}};
  EXPECT_FLOAT_EQ(CML_Interpolation1D(-0.5F, table, 2), 0.0F);
}

TEST(InterpolationTest, ExtrapolationAbove) {
  CML_Table1D table[] = {{0.0F, 0.0F}, {1.0F, 1.0F}};
  EXPECT_FLOAT_EQ(CML_Interpolation1D(1.5F, table, 2), 1.0F);
}

TEST(InterpolationTest, SinglePoint) {
  CML_Table1D table[] = {{1.0F, 2.0F}};
  EXPECT_FLOAT_EQ(CML_Interpolation1D(1.0F, table, 1), 2.0F);
}
