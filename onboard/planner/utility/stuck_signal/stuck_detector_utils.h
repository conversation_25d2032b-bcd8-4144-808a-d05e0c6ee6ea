#ifndef ONBOARD_PLANNER_UTILITY_STUCK_SIGNAL_STUCK_DETECTOR_UTILS_H_
#define ONBOARD_PLANNER_UTILITY_STUCK_SIGNAL_STUCK_DETECTOR_UTILS_H_

#include <cstdint>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "math/range.h"
#include "planner/selection/trajectory_meta.h"
#include "planner/speed/discomforts/discomfort_varying_min_range.h"
#include "planner/speed/solver/speed_result.h"
#include "planner/utility/object_id/typed_object_id.h"
#include "planner/world_model/planner_object/planner_object.h"
#include "planner/world_model/world_model.h"
#include "planner_protos/hazardous_state.pb.h"
#include "planner_protos/hazardous_state_seed.pb.h"

namespace planner {

// Stores meta data related to stuck in traffic jam.
struct TrafficJamStuckMetaData {
  // Boolean indicating whether <PERSON><PERSON> waiting in traffic jam.
  // If ego is waiting in traffic jam, then ego is not stuck.
  bool is_ego_waiting_in_traffic_jam = false;

  // Boolean indicating whether <PERSON><PERSON> is stuck inside junction.
  bool is_ego_stuck_inside_junction = false;
};

// Stores occlusion information along current lane sequence
// based on potential waiting in traffic jam vehicles
struct TrafficJamOcclusionMetaData {
  // The optional of the occlusion vehicle id.
  // If there is no occlusion, it is invalid.
  std::optional<int64_t> optional_occlusion_vehicle_id = std::nullopt;

  // Boolean indicating whether occlusion happens due to the
  // first stuck object in front of Ego or not.
  bool occlusion_on_first_vehicle = false;
};

// Stores traffic density on neighbor lanes.
struct NeighborLaneDensity {
  // Left neighbor lane density.
  std::optional<double> optional_left_lane_density = std::nullopt;
  // Right neighbor lane density.
  std::optional<double> optional_right_lane_density = std::nullopt;
};

// Stores vehicles information in lane sequence.
struct TrafficJamVehicleMetaData {
  // Arclength of the slow vehicle along nominal path.
  double arclength_m;
  // Pointer to the planner object, guaranteed to be not null.
  const PlannerObject* object;
  // Left and right in lane clearance.
  double left_inlane_clearance_m;
  double right_inlane_clearance_m;
};

// Gets the yield constraint ID that causes stuck.
std::optional<TypedObjectId> GetStuckConstraintId(
    const selection::TrajectoryMetaData& candidate,
    const speed::DiscomfortVaryingMinRange& min_range,
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    double min_range_max_required_lat_gap, double ra_to_fb_shift);

// Find if ego at end of lane
bool IsEgoAtEndOfLaneSequence(const selection::TrajectoryMetaData& candidate,
                              const voy::Pose& pose,
                              const pb::StuckDetectorConfig& stuck_parameters);

// Gets the contour of the object causing stuck, return nullopt if failed.
std::optional<math::geometry::PolygonWithCache2d> GetObjectContour(
    const SpeedWorldModel& world_model,
    const std::optional<TypedObjectId>& object_id);

// Judge if the object is slower than a threshold for a persistent time
// duration.
bool IsObjectPersistentSlow(const PlannerObject& object,
                            const pb::StuckDetectorSeed& seed,
                            double slow_speed_threshold_mps,
                            int64_t persistent_duration_ms,
                            int64_t current_timestamp);

// Generate a vehicles_in_ROI_ vector to detect whether or not ego is in traffic
// jam. The first element in the pair represent the vehicle's arclength and
// second is the planner object.
std::vector<TrafficJamVehicleMetaData> GenerateVehiclesOnLaneSequence(
    const SpeedWorldModel& world_model,
    const selection::TrajectoryMetaData& candidate,
    const pb::StuckDetectorConfig& stuck_parameters);

// Checks if the agent is waiting for the traffic light or in traffic jam.
TrafficJamStuckMetaData IsWaitingInTrafficJam(
    const SpeedWorldModel& world_model, const double distance_to_junction,
    const voy::TrafficLights& traffic_lights,
    const selection::TrajectoryMetaData& candidate,
    const pb::StuckDetectorConfig& stuck_parameters,
    const std::vector<TrafficJamVehicleMetaData>& vehicles_on_lane_sequence,
    bool is_stuck_object, const PlannerObject& stuck_object,
    const RobotStateSnapshot& ego_state_snapshot,
    const math::geometry::PolygonWithCache2d& object_contour,
    const lane_selection::LaneSequenceCandidates& lane_sequence_candidates,
    const speed::DiscomfortVaryingMinRange& min_range,
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const pb::AgentMapElementOccupancySeeds& agent_map_element_occupancy_seeds,
    const pb::StuckDetectorSeed& last_iteration_seed,
    std::optional<pb::StuckDebug>& optional_stuck_debug);

// Adds stuck signal debug info to stuck debug.
void AddStuckSignalDebugInfo(
    const std::string& debug_name, const std::string& debug_value,
    const std::vector<std::pair<std::string, std::string>>&
        sub_level_debug_list,
    pb::StuckDebug& stuck_debug);

// TODO(jinghuang1): Add unit tests.
// Checks if Ego is stuck by NonBlockage vehicle inside junction.
bool IsEgoStuckInsideJunction(
    const selection::TrajectoryMetaData& candidate,
    const std::vector<TrafficJamVehicleMetaData>& vehicles_on_lane_sequence,
    bool is_initial_stuck_object_persistent_slow,
    const PlannerObject& initial_stuck_object,
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const pb::StuckDetectorSeed& last_iteration_seed,
    const speed::DiscomfortVaryingMinRange& min_range, double ra_to_fb_shift,
    const RobotStateSnapshot& ego_state_snapshot);

// Evaluate whether the traffic jam ahead Ego causes occlusion or not.
// If the road ahead Ego is curvy, the evaluation might not be accurate.
TrafficJamOcclusionMetaData EvaluateTrafficJamOcclusion(
    const std::vector<TrafficJamVehicleMetaData>& vehicles_on_lane_sequence,
    const AgentInLaneStatesMap& agent_in_lane_states_map,
    const EgoInLaneParams& ego_inlane_param, const pnc_map::Lane* current_lane,
    double end_arclength_m);

TrafficJamOcclusionMetaData EvaluateTrafficJamOcclusion(
    const std::vector<TrafficJamVehicleMetaData>& vehicles_on_lane_sequence,
    const ObjectOccupancyStateMap& object_occupancy_state_map,
    const EgoInLaneParams& ego_inlane_param, const pnc_map::Lane* current_lane,
    double end_arclength_m);

// Checks whether there are drivable gap ahead potential temp parking stuck
// object in a distance threshold. This help determine whether the stuck object
// is a temperary parking vehicle or not.
bool HasDrivableGapAheadStuckObject(
    const EgoInLaneParams& ego_inlane_param,
    const std::vector<TrafficJamVehicleMetaData>& vehicles_on_lane_sequence,
    const TrafficJamOcclusionMetaData& occlusion_data,
    double drivable_gap_evaluation_distance_m,
    const TrafficJamVehicleMetaData& stuck_object_pair);

// Judge if the stuck object is a temp parking car.
bool IsStuckObjectTempParking(
    const PlannerObject& stuck_object, const SpeedWorldModel& world_model,
    const selection::TrajectoryMetaData& candidate,
    const std::vector<TrafficJamVehicleMetaData>& vehicles_on_lane_sequence,
    double ego_distance_to_junction);

// Calculate the Ego distance to junction area or to
// the U-turn area.
double CalculateEgoDistanceToJunction(
    const RobotStateSnapshot& ego_state_snapshot,
    const selection::TrajectoryMetaData& candidate);

// Determine whether the traffic light in front just turned
// green or not. Returns true if it just turns green.
bool FirstTrafficLightJustTurnedGreen(
    const std::vector<speed::traffic_rules::TrafficLightInfo>&
        traffic_light_info);

std::optional<double> EvaluateTrafficDensity(
    const SpeedWorldModel& world_model,
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const pnc_map::Lane* current_lane,
    const lane_selection::LaneSequenceCandidates& lane_sequence_candidates,
    const math::geometry::Point2d& ego_ra_position,
    const pb::StuckDetectorConfig& stuck_parameters,
    const pb::AgentMapElementOccupancySeeds& agent_map_element_occupancy_seeds);

// Calculate density on the neighbor lanes.
NeighborLaneDensity EvaluateNeighborLanesTrafficDensity(
    const SpeedWorldModel& world_model,
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const pnc_map::Lane* current_lane,
    const lane_selection::LaneSequenceCandidates& lane_sequence_candidates,
    const math::geometry::Point2d& ego_ra_position,
    const pb::StuckDetectorConfig& stuck_parameters,
    const pb::AgentMapElementOccupancySeeds& agent_map_element_occupancy_seeds);

// Return the pointer to the U-turn lane ahead of Ego. If there is
// no such lane, return nullptr.
const pnc_map::Lane* ObtainUTurnLane(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const pnc_map::Lane* current_lane);

// Identify the stuck object that is not the domiant constraint. For
// the first version we will only return potential stuck construction zone.
std::optional<TypedObjectId> IdentifyNonDominantStuckObject(
    const speed::SpeedResult& speed_result,
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const pb::StuckDetectorSeed& seed, double stuck_speed_mps);

static constexpr const char* kLogHeader = "[stuck signal] ";
}  // namespace planner

#endif  // ONBOARD_PLANNER_UTILITY_STUCK_SIGNAL_STUCK_DETECTOR_UTILS_H_
