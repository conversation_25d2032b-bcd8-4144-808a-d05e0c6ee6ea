#include "debug/control_debug_encode/control_debug_encode.h"

#include <algorithm>
#include <cmath>
#include <cstddef>
#include <cstdint>

#include "_out/Appl/GenData/Components/Rte_Debug.h"
#include "_out/Appl/GenData/Components/Rte_Debug_Type.h"
#include "_out/Appl/GenData/Rte_Type.h"
#include "common_math_library/can_util.h"
#include "common_math_library/common_method.h"

namespace fallback_adas {
namespace debug {

void MetricsEncode(const Metrics& metrics) {
  // error_long
  float error_longitudinal = std::roundf(metrics.error_longitudinal / 0.01F);
  error_longitudinal =
      CML_Limit(error_longitudinal, ControlErrorLongitudinal_adt_LowerLimit,
                ControlErrorLongitudinal_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_ControlErrorLongitudinal(
      static_cast<int16_t>(error_longitudinal));

  // error_velocity
  float error_velocity = std::roundf(metrics.error_velocity / 0.01F);
  error_velocity =
      CML_Limit(error_velocity, ControlErrorVelocity_adt_LowerLimit,
                ControlErrorVelocity_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_ControlErrorVelocity(
      static_cast<int16_t>(error_velocity));

  // error_heading
  float error_heading = std::roundf(metrics.error_heading / 0.001F);
  error_heading = CML_Limit(error_heading, ControlErrorHeading_adt_LowerLimit,
                            ControlErrorHeading_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_ControlErrorHeading(
      static_cast<int16_t>(error_heading));

  // error lateral distance
  float error_lateral = std::roundf(metrics.error_lateral / 0.01F);
  error_lateral = CML_Limit(error_lateral, ControlErrorLateral_adt_LowerLimit,
                            ControlErrorLateral_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_ControlErrorLateral(
      static_cast<int16_t>(error_lateral));
}

void LateralPIDDistanceEncode(const PIDDebug& pid_distance) {
  // pid total
  float pid_total = std::roundf(pid_distance.pid_total / 0.001F);
  pid_total = CML_Limit(pid_total, LatPidDistanceTotal_adt_LowerLimit,
                        LatPidDistanceTotal_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LatPidDistanceTotal(
      static_cast<int16_t>(pid_total));

  // pid p component
  float p_component = std::roundf(pid_distance.p_component / 0.001F);
  p_component = CML_Limit(p_component, LatPidDistancePComponent_adt_LowerLimit,
                          LatPidDistancePComponent_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LatPidDistancePComponent(
      static_cast<int16_t>(p_component));

  // pid i component
  float i_component = std::roundf(pid_distance.i_component / 0.001F);
  i_component = CML_Limit(i_component, LatPidDistanceIComponent_adt_LowerLimit,
                          LatPidDistanceIComponent_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LatPidDistanceIComponent(
      static_cast<int16_t>(i_component));

  // pid d componet
  float d_component = std::roundf(pid_distance.d_component / 0.001F);
  d_component = CML_Limit(d_component, LatPidDistanceDComponent_adt_LowerLimit,
                          LatPidDistanceDComponent_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LatPidDistanceDComponent(
      static_cast<int16_t>(d_component));
}

void LateralPIDHeadingEncode(const PIDDebug& pid_heading) {
  // pid total
  float pid_total = std::roundf(pid_heading.pid_total / 0.001F);
  pid_total = CML_Limit(pid_total, LatPidHeadingTotal_adt_LowerLimit,
                        LatPidHeadingTotal_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LatPidHeadingTotal(
      static_cast<int16_t>(pid_total));

  // pid p component
  float p_component = std::roundf(pid_heading.p_component / 0.001F);
  p_component = CML_Limit(p_component, LatPidHeadingPComponent_adt_LowerLimit,
                          LatPidHeadingPComponent_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LatPidHeadingPComponent(
      static_cast<int16_t>(p_component));

  // pid i component
  float i_component = std::roundf(pid_heading.i_component / 0.001F);
  i_component = CML_Limit(i_component, LatPidHeadingIComponent_adt_LowerLimit,
                          LatPidHeadingIComponent_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LatPidHeadingIComponent(
      static_cast<int16_t>(i_component));

  // pid d componet
  float d_component = std::roundf(pid_heading.d_component / 0.001F);
  d_component = CML_Limit(d_component, LatPidHeadingDComponent_adt_LowerLimit,
                          LatPidHeadingDComponent_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LatPidHeadingDComponent(
      static_cast<int16_t>(d_component));
}

void LateralPIDEncode(const LateralPIDDebug& lateral_pid) {
  // steering feedforward
  float steering_feedforward =
      std::roundf(lateral_pid.steering_feedforward / 0.001F);
  steering_feedforward =
      CML_Limit(steering_feedforward, LatSteeringFeedforward_adt_LowerLimit,
                LatSteeringFeedforward_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LatSteeringFeedforward(
      static_cast<int16_t>(steering_feedforward));

  // steering feedback
  // TODO(lifanjie): dbc signal "_LatSteeringFeeback" misspelling.
  float steering_feedback = std::roundf(lateral_pid.steering_feedback / 0.001F);
  steering_feedback =
      CML_Limit(steering_feedback, LatSteeringFeeback_adt_LowerLimit,
                LatSteeringFeeback_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LatSteeringFeeback(
      static_cast<LatSteeringFeeback>(steering_feedback));

  // lateral pid distance
  LateralPIDDistanceEncode(lateral_pid.pid_distance);

  // lateral pid heading
  LateralPIDHeadingEncode(lateral_pid.pid_heading);
}

void LongitudinalPIDSpeedEncode(const PIDDebug& pid_distance) {
  // pid total
  float pid_total = std::roundf(pid_distance.pid_total / 0.001F);
  pid_total = CML_Limit(pid_total, LongPidSpeedTotal_adt_LowerLimit,
                        LongPidSpeedTotal_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LongPidSpeedTotal(
      static_cast<int16_t>(pid_total));

  // pid p component
  float p_component = std::roundf(pid_distance.p_component / 0.001F);
  p_component = CML_Limit(p_component, LongPidSpeedPComponent_adt_LowerLimit,
                          LongPidSpeedPComponent_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LongPidSpeedPComponent(
      static_cast<int16_t>(p_component));

  // pid i component
  float i_component = std::roundf(pid_distance.i_component / 0.001F);
  i_component = CML_Limit(i_component, LongPidSpeedIComponent_adt_LowerLimit,
                          LongPidSpeedIComponent_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LongPidSpeedIComponent(
      static_cast<int16_t>(i_component));

  // pid d componet
  float d_component = std::roundf(pid_distance.d_component / 0.001F);
  d_component = CML_Limit(d_component, LongPidSpeedDComponent_adt_LowerLimit,
                          LongPidSpeedDComponent_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LongPidSpeedDComponent(
      static_cast<int16_t>(d_component));
}

void LongitudinalPIDDistanceEncode(const PIDDebug& pid_distance) {
  // pid total
  float pid_total = std::roundf(pid_distance.pid_total / 0.001F);
  pid_total = CML_Limit(pid_total, LongPidDistanceTotal_adt_LowerLimit,
                        LongPidDistanceTotal_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LongPidDistanceTotal(
      static_cast<int16_t>(pid_total));

  // pid p component
  float p_component = std::roundf(pid_distance.p_component / 0.001F);
  p_component = CML_Limit(p_component, LongPidDistancePComponent_adt_LowerLimit,
                          LongPidDistancePComponent_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LongPidDistancePComponent(
      static_cast<int16_t>(p_component));

  // pid i component
  float i_component = std::roundf(pid_distance.i_component / 0.001F);
  i_component = CML_Limit(i_component, LongPidDistanceIComponent_adt_LowerLimit,
                          LongPidDistanceIComponent_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LongPidDistanceIComponent(
      static_cast<int16_t>(i_component));

  // pid d componet
  float d_component = std::roundf(pid_distance.d_component / 0.001F);
  d_component = CML_Limit(d_component, LongPidDistanceDComponent_adt_LowerLimit,
                          LongPidDistanceDComponent_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LongPidDistanceDComponent(
      static_cast<int16_t>(d_component));
}

void LongitudinalPIDEncode(const LongitudinalPIDDebug& longitudinal_pid) {
  // acceleration feedforward
  float feedforward_acceleration =
      std::roundf(longitudinal_pid.feedforward_acceleration / 0.001F);
  feedforward_acceleration = CML_Limit(
      feedforward_acceleration, LongAccelerationFeedforward_adt_LowerLimit,
      LongAccelerationFeedforward_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LongAccelerationFeedforward(
      static_cast<int16_t>(feedforward_acceleration));

  // acceleration feedback
  // TODO(lifanjie): dbc signal "_LongAccelerationFeeback" misspelling.
  float feedback_acceleration =
      std::roundf(longitudinal_pid.feedback_acceleration / 0.001F);
  feedback_acceleration =
      CML_Limit(feedback_acceleration, LongAccelerationFeeback_adt_LowerLimit,
                LongAccelerationFeeback_adt_UpperLimit);
  Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LongAccelerationFeeback(
      static_cast<LongAccelerationFeeback>(feedback_acceleration));

  // longitudinal pid distance
  LongitudinalPIDDistanceEncode(longitudinal_pid.pid_distance);

  // longitudinal pid speed
  LongitudinalPIDSpeedEncode(longitudinal_pid.pid_speed);
}

void ControllerEncode(const ControllerDebug& controller) {
  // lateral_pid
  LateralPIDEncode(controller.lateral_pid);

  // longitudinal pid
  LongitudinalPIDEncode(controller.longitudinal_pid);
}

void ControlDebugEncode(const ControlDebug& control_debug) {
  // timestamp
  uint8_t timestamp_bytes[6] = {0};
  common::Uint64ToBytes(control_debug.timestamp_ms, timestamp_bytes,
                        sizeof(timestamp_bytes), true);

  Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_ControlTimeStamp(
      timestamp_bytes);

  // metrics
  MetricsEncode(control_debug.metrics);

  // controller
  ControllerEncode(control_debug.controller);
}

}  // namespace debug
}  // namespace fallback_adas
