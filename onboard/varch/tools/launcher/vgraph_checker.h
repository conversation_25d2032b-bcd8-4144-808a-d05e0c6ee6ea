#ifndef ONBOARD_VARCH_TOOLS_LAUNCHER_VGRAPH_CHECKER_H_
#define ONBOARD_VARCH_TOOLS_LAUNCHER_VGRAPH_CHECKER_H_

#include <map>
#include <string>

#include "varch/protos/vnode/vgraph_conf.pb.h"

namespace varch {
namespace tools {
namespace launcher {
using VForestConfig = varch::protos::vnode::VForestConfig;

class VGraphChecker {
 public:
  VGraphChecker() = default;
  ~VGraphChecker() = default;

  bool IsVGraphValid(const std::string& vgraph_path);

 private:
  bool IsValid();
  void SetIsInValid();

  VForestConfig VGraphParserChecker(const std::string& vgraph_path);

  VGraphChecker& NoDuplicatePublisherNameChecker(
      const VForestConfig& vforest_config, const std::string& vgraph_path);
  VGraphChecker& NoDuplicatePubTopicNameChecker(
      const VForestConfig& vforest_config, const std::string& vgraph_path);
  VGraphChecker& NoDuplicateSubTopicNameChecker(
      const VForestConfig& vforest_config, const std::string& vgraph_path);
  VGraphChecker& NoDuplicateVNodeNameChecker(
      const VForestConfig& vforest_config, const std::string& vgraph_path);

  bool is_valid_ = true;
  std::map<std::string, std::string> vnode_name_vgraph_map_;
};

}  // namespace launcher
}  // namespace tools
}  // namespace varch

#endif  // ONBOARD_VARCH_TOOLS_LAUNCHER_VGRAPH_CHECKER_H_
