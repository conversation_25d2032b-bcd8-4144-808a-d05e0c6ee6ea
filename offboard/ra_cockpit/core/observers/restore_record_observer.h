#ifndef OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_RESTORE_RECORD_OBSERVER_H_
#define OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_RESTORE_RECORD_OBSERVER_H_

#include <functional>
#include <memory>

#include "../data_observer.h"
#include "restart_protos/restart.pb.h"
#include "teleassist_protos/teleassist.pb.h"

namespace remote_assist {

class RestoreRecordObserver : public IDataObserver {
 public:
  using RestoreRecordCallback = std::function<void(
      std::shared_ptr<service_restore::pb::RestoreRecord>, DataChannel)>;

  explicit RestoreRecordObserver(RestoreRecordCallback callback);

  bool IsInterestedIn(
      const teleassist::pb::TeleassistRequest& message) override;

  std::shared_ptr<google::protobuf::Message> OnMessage(
      const teleassist::pb::TeleassistRequest& message,
      DataChannel data_channel) override;

 private:
  RestoreRecordCallback callback_;
};

}  // namespace remote_assist

#endif  // OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_RESTORE_RECORD_OBSERVER_H_
