#include "acu_signal_input/time_sync/time_sync.h"

#include <cstdint>

#include "_out/Appl/GenData/Components/Rte_AcuSigInput.h"
#include "acu_signal_input/acu_signal_input_parameter.h"
#include "common_math_library/debounce_method.h"

namespace fallback_adas {
namespace acu_signal_input {
namespace {

bool CheckAcuPoseUpdate(uint8_t counter) {
  static uint8_t last_cycle_counter = 0;
  static bool can_update_is_ok = false;
  static float can_update_timer = 0.0F;

  bool cur_can_update_is_ok = false;
  // If the counter changes, it is considered that CAN communication is okay.
  if (acu_signal_input::GetOnlyCheckCounterChange()) {
    cur_can_update_is_ok = (counter != last_cycle_counter);
  } else {
    cur_can_update_is_ok = ((counter == last_cycle_counter + 1) ||
                            (counter == 0x00 && last_cycle_counter == 0xFF));
  }

  CML_TurnOnDelay(
      cur_can_update_is_ok, acu_signal_input::GetPoseUpdateOKTimeThreshold(),
      POSE_INPUT_TASK_CYCLE_TIME, &can_update_timer, &can_update_is_ok);
  last_cycle_counter = counter;

  return can_update_is_ok;
}

}  // namespace

// The current time of the ACU sent by Canbus, with a cycle of 10ms.
uint32_t GetCurrentTimestamp(uint8_t acu_pose_counter,
                             uint32_t pose_timestamp_ms,
                             uint32_t can_timestamp_ms) {
  static uint32_t mcu_timestamp_ms = 0;

  // Check if the AcuPose message is updated.
  if (CheckAcuPoseUpdate(acu_pose_counter)) {
    can_timestamp_ms = can_timestamp_ms + 10;
    // pose_timestamp_ms is pose timestamp_ms % 1000000000L
    // can_timestamp_ms is can timestamp_ms % 1000000L
    mcu_timestamp_ms = pose_timestamp_ms / 1000000 * 1000000 + can_timestamp_ms;
  } else {
    mcu_timestamp_ms = mcu_timestamp_ms + 10;
  }

  return mcu_timestamp_ms;
}

}  // namespace acu_signal_input
}  // namespace fallback_adas
