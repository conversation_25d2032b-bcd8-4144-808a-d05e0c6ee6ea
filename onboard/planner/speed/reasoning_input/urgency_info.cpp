#include "planner/speed/reasoning_input/urgency_info.h"

#include <algorithm>
#include <cmath>
#include <map>
#include <string>
#include <utility>

#include "math/math_util.h"
#include "math/range.h"
#include "math/unit_conversion.h"
#include "planner/speed/reasoning/prediction_decision_maker_input.h"
#include "planner/speed/reasoning/reasoning_basic_util.h"
#include "planner/speed/reasoning_input/traffic_rules/crosswalk_traffic_rule.h"
#include "planner/speed/reasoning_input/traffic_rules/lane_info_traffic_rule.h"
#include "planner/speed/reasoning_input/trajectory_info.h"
#include "planner/speed/reasoning_input/util/creep_gap_util.h"
#include "planner/speed/reasoning_input/world_context.h"
#include "rt_event/rt_event.h"
#include "voy_rt_event/rt_event_planner.h"

namespace planner {
namespace speed {
namespace {
// Longitudinal distance to estimate the density of traffic flows.
constexpr double kLongitudinalDistanceToConsiderDenseTrafficInMeter = 20.0;
// Lateral distance to estimate the density of the traffic.
constexpr double kLateralDistanceToConsiderDenseTrafficInMeter = 10;
// The max duration Ego keeps a low speed before creeping for progress with
// vehicles following behind Ego.
constexpr double kMaxEgoStoppedDurationWithWaitingVehiclesBehindEgoInMs =
    5000;  // 5 seconds.
// The max duration Ego keeps a low speed in dense traffic without vehicles
// following behind Ego.
constexpr double kMaxEgoStoppedDurationInMs = 10000;  // 10 seconds.
constexpr double kMaxSpeedToProceedUrgentlyInMps = math::KmphToMps(25);
constexpr int kNumAgentToConsiderAsDenseTraffic = 3;
// The maximum searching radius for identifying the agent's occupied lane.
constexpr double kMaxLaneSearchingRadiusInMeter = 1.0;
// The maximum length of merge lane sequence to estimate the density of merge
// traffic flow.
constexpr double kMaxTotalLengthForMergeLanesInMeter = 40.0;
// The minimum distance ego front bumper passes that we will consider ego has
// entered the conflicting lane of the u-turn and need to be more aggressive to
// unstuck.
constexpr double kMinDistConsiderEnterInConflictLaneOfUTurnInMeter = 1.0;
// The max duration Ego stops before adding no block constraint for progress
// with vehicles waiting behind Ego.
constexpr int kMaxEgoStoppedDurationWithTrafficFlowMovingAheadInMs =
    500;  // 0.5 seconds.
// When agent ahead of ego keep moving for this duration, it will be considered
// as moving agent when ego is stopped.
constexpr double kDurationForAgentToExitKeepClearInSecond = 2.0;

// Returns the lane that the agent most likely occupied. Returns nullopt if
// there is no lane associated with the agent. Note we try to get the lane from
// the |latest_occupied_isolated_lane| first if agent is within the regular
// lane, otherwise, will directly call the API from pnc map service to get the
// associated lane.
// TODO(jinhao): Refactor corresponding logic to store the occupied virtual lane
// in |agent_map_element_occupancy_seeds| to avoid duplicate computations.
std::optional<int64_t> GetAgentCurrentLaneId(
    const planner::pb::DecoupledManeuverSeed& previous_iter_seed,
    const PlannerObject& planner_object,
    const pnc_map::JointPncMapService& joint_pnc_map_service) {
  // Gets the result from agent element occupancy seed.
  const auto& agent_element_occupancy_map =
      previous_iter_seed.agent_map_element_occupancy_seeds()
          .agent_element_occupancy_map();
  const auto agent_element_occupancy_iter =
      agent_element_occupancy_map.find(planner_object.id());
  if (agent_element_occupancy_iter != agent_element_occupancy_map.end() &&
      agent_element_occupancy_iter->second
          .has_latest_occupied_isolated_lane()) {
    DCHECK(agent_element_occupancy_iter->second.latest_occupied_isolated_lane()
               .element_type() == planner::pb::MapElementType::LANE);
    const int64_t lane_id =
        agent_element_occupancy_iter->second.latest_occupied_isolated_lane()
            .element_id();
    const std::vector<const pnc_map::Lane*> lane_vec =
        joint_pnc_map_service.GetLaneSequence({lane_id});
    if (!lane_vec.empty() && lane_vec.front() != nullptr &&
        math::geometry::Within(planner_object.center_2d(),
                               lane_vec.front()->border())) {
      return lane_id;
    }
  }

  // Gets the result directly from pnc map service.
  voy::Pose agent_pos;
  agent_pos.set_x(planner_object.center_3d().x());
  agent_pos.set_y(planner_object.center_3d().y());
  agent_pos.set_z(planner_object.center_3d().z());
  agent_pos.set_yaw(planner_object.heading());

  const std::vector<const pnc_map::Lane*> nearby_lanes =
      joint_pnc_map_service.GetNearLanesWithPose(
          agent_pos, /*lane_searching_radius_m=*/kMaxLaneSearchingRadiusInMeter,
          M_PI_4,
          /*prefer_overlapped_lanes=*/true);
  const auto virtual_lane_iter = std::find_if(
      nearby_lanes.begin(), nearby_lanes.end(), [](const pnc_map::Lane* lane) {
        return lane->type() == hdmap::Lane_LaneType_VIRTUAL;
      });
  if (virtual_lane_iter != nearby_lanes.end()) {
    return (*virtual_lane_iter)->id();
  }

  return std::nullopt;
}

// If at least one overlap region happens when ego is in upl, then the
// trajectory interacts with ego when ego is in upl.
bool DoesTrajectoryInteractWithEgoWhenEgoIsInUPL(
    const AgentTrajectoryInfo& agent_trajectory_info) {
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      agent_trajectory_info.overlap_region_infos();
  return std::any_of(overlap_region_infos.begin(), overlap_region_infos.end(),
                     [](const OverlapRegionInfo& overlap_region_info) {
                       return overlap_region_info.is_in_unprotected_left_turn;
                     });
}

// If at least one trajectory interacts with ego when ego is in upl, and the
// agent is on the right side of the path, then the agent is an
// oncoming-crossing agent, which is within our interest. The reason we need to
// check whether the agent is on the right is that, there exists a condition
// that ego has enter the junction and the light turns red, the left side agents
// start to move, these agents should not trigger urgency reason.
bool IsOncomingCrossingAgentWhenEgoIsInUPL(
    const PredictionDecisionMakerInput& input) {
  const std::vector<AgentTrajectoryInfo>& agent_trajectory_infos =
      input.agent_trajectory_infos;
  const pb::ObjectProximityInfo& object_proximity_info =
      input.reasoning_object.object_proximity_info();
  return object_proximity_info.signed_lateral_gap() < 0 &&
         std::any_of(agent_trajectory_infos.begin(),
                     agent_trajectory_infos.end(),
                     [](const AgentTrajectoryInfo& agent_trajectory_info) {
                       return DoesTrajectoryInteractWithEgoWhenEgoIsInUPL(
                           agent_trajectory_info);
                     });
}
// This function estimates the ego future waiting time, i.e.
// agent_arrives_conflicting_time - ego_stop_moving_time.
std::optional<double> EstimatedEgoFutureWaitTimeForAgent(
    const WorldContext& world_context,
    const ReasoningObject& reasoning_object) {
  const std::optional<ARTObjectInfo>& art_object_info =
      reasoning_object.art_object_info();
  if (!art_object_info.has_value() ||
      !art_object_info.value()
           .min_agent_arrives_time_from_prediction_in_last_frame.has_value()) {
    return std::nullopt;
  }
  world_context.robot_state().plan_init_state_snapshot().timestamp();
  if (!world_context.ego_come_to_full_stop_timestamp().has_value()) {
    return 0.0;
  }
  const double ego_stop_moving_time_in_sec =
      math::Ms2Sec(world_context.ego_come_to_full_stop_timestamp().value());
  return std::max(
      0.0,
      art_object_info.value()
              .min_agent_arrives_time_from_prediction_in_last_frame.value() -
          ego_stop_moving_time_in_sec);
}

// Get the lane agent occupancy map from the input lane and its predecessors.
LaneAgentOccupancyMap GetLaneAgentOccupancyMap(const pnc_map::Lane* lane) {
  LaneAgentOccupancyMap lane_agent_occupancy_map;
  DCHECK(lane != nullptr);
  lane_agent_occupancy_map[lane->id()] = {};
  for (const pnc_map::Lane* predecessor : lane->predecessors()) {
    lane_agent_occupancy_map[predecessor->id()] = {};
  }

  return lane_agent_occupancy_map;
}

// Add the merge lanes to merge lane clusters to estimate the density of the
// merge traffic flow. The total length of the connected merge lane sequence
// will not exceed |kMaxTotalLengthForMergeLanesInMeter|.
void AddMergeLaneClusters(const pnc_map::Lane* lane, const double length,
                          LaneAgentOccupancyMap& merge_lane_clusters) {
  if (length > kMaxTotalLengthForMergeLanesInMeter) {
    return;
  }
  merge_lane_clusters[lane->id()] = {};

  for (const pnc_map::Lane* predecessor : lane->predecessors()) {
    AddMergeLaneClusters(predecessor, length + lane->length(),
                         merge_lane_clusters);
  }
}

// Returns true if it is a non leading agent whose trajectory interacting with
// ego.
bool IsNonLeadingAgentInteractingWithEgo(
    const PredictionDecisionMakerInput& input) {
  if (input.reasoning_object.is_leading_agent()) {
    return false;
  }

  return std::any_of(
      input.agent_trajectory_infos.begin(), input.agent_trajectory_infos.end(),
      [](const AgentTrajectoryInfo& agent_trajectory_info) {
        const std::vector<OverlapRegionReference>& overlap_regions =
            agent_trajectory_info.overlap_regions();
        return std::any_of(
            overlap_regions.begin(), overlap_regions.end(),
            [](const OverlapRegionReference& region) {
              return region.get().contain_strict_overlap() &&
                     !reasoning_util::IsStrictOverlapRegionStartingBehind(
                         region.get(), /*ra_arclength_threshold=*/0.0);
            });
      });
}

void PopulateLaneAgentOccupancyMapDebug(
    const LaneAgentOccupancyMap& lane_agent_occupancy_map,
    pb::LaneSequenceAgentOccupancyDebug* debug) {
  for (const auto& [lane_id, occupied_agent_ids] : lane_agent_occupancy_map) {
    pb::LaneAgentOccupancyDebug* lane_agent_occupancy_debug =
        debug->add_lane_agent_occupancy();
    lane_agent_occupancy_debug->set_lane_id(lane_id);
    for (const int64_t agent_id : occupied_agent_ids) {
      lane_agent_occupancy_debug->add_agent_id(agent_id);
    }
  }
}

// If at least one overlap slice's ego_moving_distance_strict covers 0, then the
// overlap region has conflict with ego's current pose.
bool HasConflictWithEgoCurrentPose(const pb::OverlapRegion& overlap_region) {
  const auto& overlap_slices = overlap_region.overlap_slices();
  return std::any_of(
      overlap_slices.begin(), overlap_slices.end(),
      [](const speed::pb::OverlapSlice& overlap_slice) {
        return overlap_slice.has_ego_moving_distance_strict() &&
               overlap_slice.ego_moving_distance_strict().start() <= 0 &&
               overlap_slice.ego_moving_distance_strict().end() >= 0;
      });
}

// Returns true if the overlap starts with non-zero lateral gap and becomes zero
// lateral lap.
bool IsApproachingEgoPath(const pb::OverlapRegion& overlap_region) {
  const auto& overlap_slices = overlap_region.overlap_slices();
  DCHECK(!overlap_slices.empty());
  return std::abs(overlap_slices.begin()->signed_lateral_gap()) > 0 &&
         std::any_of(overlap_slices.begin() + 1, overlap_slices.end(),
                     [](const speed::pb::OverlapSlice& overlap_slice) {
                       return std::abs(overlap_slice.signed_lateral_gap()) <
                              math::constants::kEpsilon;
                     });
}

// Returns true if ego's current pose has blocked agent's trajectory.
bool IsAgentTrajectoryBlockedByEgoCurrentPose(
    const speed::AgentTrajectoryInfo& agent_trajectory_info) {
  const std::vector<OverlapRegionReference>& overlap_regions =
      agent_trajectory_info.overlap_regions();
  for (const OverlapRegionReference& overlap_region : overlap_regions) {
    if ((agent_trajectory_info.overlap_region_info(overlap_region)
             .is_crossing_ego_path ||
         IsApproachingEgoPath(overlap_region)) &&
        HasConflictWithEgoCurrentPose(overlap_region)) {
      return true;
    }
  }
  return false;
}

// Returns true if the agent will leave the keep clear zone.
bool WillAgentLeaveKeepClearZone(
    const PredictionDecisionMakerInput& reasoning_input,
    const math::Range1d& keep_clear_zone_range) {
  const ReasoningObject& reasoning_object = reasoning_input.reasoning_object;
  if (!reasoning_object.is_vehicle()) {
    // Only consider vehicle.
    return false;
  }
  const speed::pb::ObjectProximityInfo& proximity =
      reasoning_object.object_proximity_info();
  if (!math::IsInRange(proximity.projected_ra_arc_length().start(),
                       keep_clear_zone_range.start_pos,
                       keep_clear_zone_range.end_pos) &&
      !math::IsInRange(proximity.projected_ra_arc_length().end(),
                       keep_clear_zone_range.start_pos,
                       keep_clear_zone_range.end_pos)) {
    // The vehicle should be in keep clear zone.
    return false;
  }

  if (!std::any_of(reasoning_input.agent_trajectory_infos.begin(),
                   reasoning_input.agent_trajectory_infos.end(),
                   [](const AgentTrajectoryInfo& agent_trajectory_info) {
                     return agent_trajectory_info.is_same_direction();
                   })) {
    // The vehicle should exit the keep clear zone from the same direction.
    return false;
  }

  // Check if the agent is able to leave zone in 2 seconds with const accel.
  const double agent_move_dist =
      proximity.signed_longitudinal_speed() *
          kDurationForAgentToExitKeepClearInSecond +
      0.5 * proximity.signed_longitudinal_acceleration() *
          kDurationForAgentToExitKeepClearInSecond *
          kDurationForAgentToExitKeepClearInSecond;

  const bool will_exit_keep_clear_zone =
      proximity.projected_ra_arc_length().start() + agent_move_dist >
      keep_clear_zone_range.end_pos;

  return will_exit_keep_clear_zone;
}

// Judge if the dominant constraint is stationary or not.
bool IsDominantConstraintStationary(
    const std::unordered_map<ObjectId, planner::PlannerObject>&
        planner_object_map,
    const std::unordered_map<ConstructionZoneId, const ConstructionZone*>&
        cz_ptr_map,
    const speed::pb::SpeedSeed& previous_speed_seed) {
  if (previous_speed_seed.dominant_object_history().empty()) {
    return false;
  }

  const ObjectId previous_dominant_risk_object_id =
      previous_speed_seed.dominant_object_history()
          .rbegin()
          ->dominant_object_id();

  // Dominant constraint is a cz.
  const ConstructionZone* const* dominant_constraint_cz_ptr =
      gtl::FindOrNull(cz_ptr_map, previous_dominant_risk_object_id);
  if (dominant_constraint_cz_ptr != nullptr &&
      *dominant_constraint_cz_ptr != nullptr) {
    return true;
  }

  // Dominant constraint is a stationary object.
  const PlannerObject* dominant_constraint_obj =
      gtl::FindOrNull(planner_object_map, previous_dominant_risk_object_id);
  return dominant_constraint_obj != nullptr &&
         dominant_constraint_obj->is_stationary();
}

// Returns true if ego has stuck in the junction since the red light in last
// traffic light cycle. A traffic light cycle refers to the sequence of signal
// states, e.g., Red -> Green -> Yellow -> Red.
bool HasEgoStuckInJunctionSinceLastRedLight(
    const WorldContext& world_context,
    const traffic_rules::TrafficLightInfo* tl_info_ptr,
    const pb::UrgencyInfoSeed& previous_urgency_info_seed) {
  if (previous_urgency_info_seed.has_stuck_in_junction_since_last_red_light()) {
    return true;
  }

  if (tl_info_ptr->is_using_watch_line()) {
    // Stopping in the waiting area is not considered as stuck.
    return false;
  }

  constexpr int64_t kMinColorDurationToAvoidFPRedLightInMSec = 2000;
  // Returns true if ego stuck during the red light. An additional 2s duration
  // check is to avoid red light FP from perception.
  return world_context.EgoAlmostStopped() &&
         tl_info_ptr->is_red(/*sustained=*/true) &&
         tl_info_ptr->GetColorDuration() >
             kMinColorDurationToAvoidFPRedLightInMSec;
}

// Returns true if we think the cross-traffic is flowing in junction.
// TODO(jinhao): Currently we implicitly infer this from traffic light state and
// ego state. We can better estimate this when we have actual traffic flow
// infomation.
bool IsCrossTrafficFlowingInJunction(
    const WorldContext& world_context,
    const traffic_rules::TrafficLightInfo* tl_info_ptr) {
  constexpr double kMaxRedLightDurationToConsiderCrossTrafficInMsec = 5000;
  const std::optional<int64_t> red_light_duration_in_ms =
      tl_info_ptr->GetRedLightDuration();
  return red_light_duration_in_ms.has_value() &&
         red_light_duration_in_ms.value() >
             kMaxRedLightDurationToConsiderCrossTrafficInMsec &&
         world_context.EgoAlmostStopped();
}

// Returns true if the nearest agent on ego path is a close leading agent.
// TODO(jinhao): Store this as a bool variable if we have more usage in the
// future.
bool HasLeadingVehicleToFollow(
    const WorldContext& world_context,
    const std::vector<PredictionDecisionMakerInput>& reasoning_inputs) {
  if (reasoning_inputs.empty()) {
    return false;
  }

  const ReasoningObject& nearest_agent =
      std::min_element(reasoning_inputs.begin(), reasoning_inputs.end(),
                       [](const PredictionDecisionMakerInput& lhs,
                          const PredictionDecisionMakerInput& rhs) {
                         if (!lhs.reasoning_object.IsOnEgoPath() ||
                             !lhs.reasoning_object.IsFullyAhead()) {
                           return false;
                         }
                         const double lhs_proximity_start =
                             lhs.reasoning_object.object_proximity_info()
                                 .projected_ra_arc_length()
                                 .start();
                         const double rhs_proximity_start =
                             rhs.reasoning_object.object_proximity_info()
                                 .projected_ra_arc_length()
                                 .start();
                         return lhs_proximity_start < rhs_proximity_start;
                       })
          ->reasoning_object;

  constexpr double kMaxDistanceToFollowTheLeadingAgentInMeter = 10.0;
  const double dist_to_front_bumper =
      nearest_agent.object_proximity_info().projected_ra_arc_length().start() -
      world_context.ra_to_leading_bumper();

  return nearest_agent.is_leading_agent() &&
         dist_to_front_bumper < kMaxDistanceToFollowTheLeadingAgentInMeter;
}

// Returns the right-turn lanes which consist of the first right hook.
std::vector<const traffic_rules::LaneInfoInLaneSequence*>
GetFirstRightHookLanes(const TrajectoryInfo& trajectory_info) {
  std::vector<const traffic_rules::LaneInfoInLaneSequence*> lanes;
  const std::vector<traffic_rules::LaneInfoInLaneSequence>& lane_infos =
      trajectory_info.traffic_rules().lane_sequence_lanes;
  auto first_right_turn_lane_iter =
      std::find_if(lane_infos.begin(), lane_infos.end(),
                   [](const traffic_rules::LaneInfoInLaneSequence& lane) {
                     return lane.lane->type() == hdmap::Lane::VIRTUAL &&
                            lane.lane->turn() == hdmap::Lane::RIGHT &&
                            lane.lane->section()->road()->side_road_type() !=
                                hdmap::Road::EXCLUSIVE_RIGHT_TURN;
                   });
  for (auto lane_iter = first_right_turn_lane_iter;
       lane_iter != lane_infos.end(); lane_iter++) {
    if (lane_iter->lane->type() == hdmap::Lane::VIRTUAL &&
        lane_iter->lane->turn() == hdmap::Lane::RIGHT) {
      lanes.push_back(&(*first_right_turn_lane_iter));
      continue;
    }
    break;
  }

  return lanes;
}

// Returns cyclists who are going through right hook.
CreepTimeGapInfo::SelectedAgents GetCyclistsGoingThroughRightHook(
    const WorldContext& world_context,
    const std::vector<PredictionDecisionMakerInput>& pdm_inputs,
    const pb::ReasoningConfig& reasoning_config,
    const math::Range1d right_hook_range) {
  // The minimum likelihood of predicted trajectory used to compute right hook
  // occupancy. Because the lateral gap for agents on Ego right side is
  // negative, we also set this threshold to negative.
  constexpr double kOnRightSideLateralGapThresholdInMeter = -0.2;

  CreepTimeGapInfo::SelectedAgents considered_agents;
  for (const PredictionDecisionMakerInput& one_agent_pdm_input : pdm_inputs) {
    const ReasoningObject& reasoning_object =
        one_agent_pdm_input.reasoning_object;
    if (!reasoning_object.is_cyclist() || reasoning_object.IsStationary()) {
      continue;
    }

    const pb::ObjectProximityInfo& object_proximity_info =
        reasoning_object.object_proximity_info();
    const bool is_same_direction_cyclist_on_right_rear_of_ego =
        !reasoning_object.IsFullyAhead() &&
        object_proximity_info.signed_lateral_gap() <
            kOnRightSideLateralGapThresholdInMeter &&
        std::abs(object_proximity_info.relative_motion_heading()) < M_PI_4;
    if (!is_same_direction_cyclist_on_right_rear_of_ego) {
      continue;
    }

    const double nominal_likelihood_threshold =
        speed::reasoning_util::GetLikelihoodThreshold(
            reasoning_object.object_type(),
            reasoning_config.prediction_decision_maker())
            .min_likelihood_for_nominal;
    for (const AgentTrajectoryInfo& agent_trajectory_info :
         one_agent_pdm_input.agent_trajectory_infos) {
      // We only consider trajectories whose likelihood is larger than
      // |kMinLikelihoodForComputingCrosswalkOccupancy| or which is primary.
      if (!agent_trajectory_info.predicted_trajectory()
               .is_primary_trajectory() &&
          agent_trajectory_info.likelihood() < nominal_likelihood_threshold) {
        continue;
      }

      if (agent_trajectory_info.IsTurning()) {
        continue;
      }

      for (const pb::OverlapRegion& overlap_region :
           agent_trajectory_info.overlap_regions()) {
        if (!overlap_region.contain_strict_overlap()) {
          continue;
        }

        const auto& overlap_region_range =
            overlap_region.ego_moving_distance_padded();
        if (!math::IsInRange(overlap_region_range.start() +
                                 world_context.ra_to_leading_bumper(),
                             right_hook_range.start_pos,
                             right_hook_range.end_pos) &&
            !math::IsInRange(overlap_region_range.end() -
                                 world_context.robot_state().GetLength(),
                             right_hook_range.start_pos,
                             right_hook_range.end_pos)) {
          continue;
        }

        considered_agents[reasoning_object.id()].push_back(&overlap_region);
      }
    }
  }

  return considered_agents;
}

// Returns the intersected agents as triggering crosswalk creep.
CreepTimeGapInfo::SelectedAgents SelectAgentsForCrosswalkCreep(
    const traffic_rules::CrosswalkInLaneSequence& target_crosswalk,
    const std::vector<PredictionDecisionMakerInput>& pdm_inputs) {
  CreepTimeGapInfo::SelectedAgents considered_agents;
  for (const PredictionDecisionMakerInput& one_agent_pdm_input : pdm_inputs) {
    const ReasoningObject& reasoning_object =
        one_agent_pdm_input.reasoning_object;
    if (!reasoning_object.is_pedestrian_or_cyclist()) {
      continue;
    }

    const double nominal_likelihood_threshold =
        speed::reasoning_util::GetLikelihoodThreshold(
            reasoning_object.object_type(),
            PlannerConfigCenter::GetInstance()
                .GetDecoupledForwardManeuverConfig()
                .speed_generator_config()
                .reasoning()
                .prediction_decision_maker())
            .min_likelihood_for_nominal;
    for (const AgentTrajectoryInfo& agent_trajectory_info :
         one_agent_pdm_input.agent_trajectory_infos) {
      // We only consider trajectories whose likelihood is larger than
      // |kMinLikelihoodForComputingCrosswalkOccupancy| or which is primary.
      if (!agent_trajectory_info.predicted_trajectory()
               .is_primary_trajectory() &&
          agent_trajectory_info.likelihood() < nominal_likelihood_threshold) {
        continue;
      }

      for (const OverlapRegionInfo& overlap_region_info :
           agent_trajectory_info.overlap_region_infos()) {
        const pb::OverlapRegion& overlap_region =
            agent_trajectory_info.overlap_region(
                overlap_region_info.overlap_region_ix);
        if (!overlap_region.has_ego_moving_distance_strict()) {
          continue;
        }
        if (std::any_of(
                overlap_region_info.overlapped_crosswalk_infos.begin(),
                overlap_region_info.overlapped_crosswalk_infos.end(),
                [&target_crosswalk](const OverlappedCrosswalkInfo& crosswalk) {
                  return crosswalk.is_using_crosswalk &&
                         target_crosswalk.crosswalk_ptr->id() ==
                             crosswalk.crosswalk.crosswalk_ptr->id();
                })) {
          considered_agents[reasoning_object.id()].emplace_back(
              &overlap_region);
        }
      }
    }
  }

  return considered_agents;
}

}  // namespace

UrgencyInfo::UrgencyInfo(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const std::unordered_map<ObjectId, planner::PlannerObject>&
        planner_object_map,
    const std::unordered_map<ConstructionZoneId, const ConstructionZone*>&
        cz_ptr_map,
    const std::vector<PredictionDecisionMakerInput>& reasoning_inputs,
    const planner::pb::DecoupledManeuverSeed& previous_iter_seed,
    const pb::ReasoningConfig& reasoning_config,
    pb::UrgencyInfoSeed& current_seed, pb::UrgencyInfoDebug* debug) {
  // Update whether ego stuck in the traffic for a long time.
  has_ego_stuck_for_long_time_ =
      (world_context.has_vehicle_waiting_behind_ego() &&
       world_context.ego_stuck_duration_ms() >
           kMaxEgoStoppedDurationWithWaitingVehiclesBehindEgoInMs) ||
      world_context.ego_stuck_duration_ms() > kMaxEgoStoppedDurationInMs;

  is_ego_in_low_speed_ =
      world_context.robot_state().current_state_snapshot().speed() <
      kMaxSpeedToProceedUrgentlyInMps;

  was_proceeding_urgently_ = previous_iter_seed.speed_seed()
                                 .urgency_info_seed()
                                 .ego_was_proceeding_urgently();

  was_discomfort_for_progress_in_upl_ =
      previous_iter_seed.speed_seed()
          .urgency_info_seed()
          .was_discomfort_for_progress_in_upl();

  was_discomfort_for_progress_in_u_turn_ =
      previous_iter_seed.speed_seed()
          .urgency_info_seed()
          .was_discomfort_for_progress_in_u_turn();

  is_dominant_constraint_stationary_ = IsDominantConstraintStationary(
      planner_object_map, cz_ptr_map, previous_iter_seed.speed_seed());

  was_discomfort_for_progress_in_right_hook_ =
      previous_iter_seed.speed_seed()
          .urgency_info_seed()
          .was_discomfort_for_progress_in_right_hook();

  UpdateTrafficFlowContext(
      trajectory_info, *DCHECK_NOTNULL(world_context.joint_pnc_map_service()),
      previous_iter_seed, reasoning_inputs);

  UpdateUrgencyReasons();

  UpdateUPLUrgencyReasons(world_context, trajectory_info, reasoning_inputs);

  UpdateUTurnUrgencyReasons(world_context, trajectory_info);

  UpdateUrgencyReasonsForTheEndOfTrafficLight(
      world_context, trajectory_info, reasoning_inputs,
      previous_iter_seed.speed_seed().urgency_info_seed(),
      debug != nullptr ? debug->mutable_debug_str() : nullptr);

  const auto target_right_turn_lanes = GetFirstRightHookLanes(trajectory_info);
  std::string* debug_str_ptr =
      debug != nullptr ? debug->mutable_debug_str() : nullptr;
  UpdateRightHookUrgencyReasons(world_context, target_right_turn_lanes,
                                reasoning_inputs, reasoning_config,
                                debug_str_ptr);
  // NOTE: we must update the above reasons firstly.
  UpdateCrosswalkUrgencyReasons(
      world_context, trajectory_info,
      previous_iter_seed.speed_seed().urgency_info_seed(),
      (HasUrgencyReason(planner::pb::UrgencyReason::RightHookDenseTraffic)
           ? &target_right_turn_lanes
           : nullptr),
      debug_str_ptr);

  UpdateUrgencyReasonsForKeepClearZone(world_context, trajectory_info,
                                       reasoning_inputs);

  UpdateWaypointUrgencyReasons(world_context);

  if (ShouldSetDiscomfortForProgressInDenseTraffic() &&
      !was_proceeding_urgently_) {
    // Only post RT event at the first frame that ego start to proceed urgently
    // to avoid duplicated records. Note the RT event does not support multiple
    // payloads, we prioritize recording merge cases because they occur less
    // frequently..
    DCHECK(!urgency_reasons_.empty());
    rt_event::PostRtEvent<rt_event::planner::CreepInDenseTraffic>(
        (HasUrgencyReason(planner::pb::UrgencyReason::MergeInDenseTraffic)
             ? planner::pb::UrgencyReason::Enum_Name(
                   planner::pb::UrgencyReason::MergeInDenseTraffic)
             : planner::pb::UrgencyReason::Enum_Name(
                   planner::pb::UrgencyReason::AvoidCutInInDenseTraffic)));
    if (HasUrgencyReason(
            planner::pb::UrgencyReason::UnstuckFromKeepClearZone)) {
      rt_event::PostRtEvent<rt_event::planner::KeepLearZoneUnstuck>();
    }
  }

  UpdateCurrentIterSeed(current_seed);

  if (debug != nullptr) {
    PopulateDebug(*debug);
  }
}

void UrgencyInfo::UpdateCrosswalkTrafficFlowContext(
    const TrajectoryInfo& trajectory_info,
    const std::vector<PredictionDecisionMakerInput>& reasoning_inputs) {
  for (const traffic_rules::CrosswalkInLaneSequence& crosswalk :
       trajectory_info.traffic_rules().crosswalks) {
    const CreepTimeGapInfo::SelectedAgents selected_agents =
        SelectAgentsForCrosswalkCreep(crosswalk, reasoning_inputs);
    crosswalk_id_to_time_gap_info_.insert(
        std::make_pair(crosswalk.crosswalk_ptr->id(),
                       CreepTimeGapInfo::ComputeTimeGapInfo(selected_agents)));
  }
}

void UrgencyInfo::UpdateTrafficFlowContext(
    const TrajectoryInfo& trajectory_info,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const planner::pb::DecoupledManeuverSeed& previous_iter_seed,
    const std::vector<PredictionDecisionMakerInput>& reasoning_inputs) {
  UpdateCrosswalkTrafficFlowContext(trajectory_info, reasoning_inputs);

  // Add the interested lane clusters around ego to estimate the density of the
  // traffic flow.
  AddInterestedLaneClusters(trajectory_info);

  // Go through the objects to update the occupancy info on the interested
  // lanes.
  for (const auto& reasoning_input : reasoning_inputs) {
    const ReasoningObject& reasoning_object = reasoning_input.reasoning_object;
    if (!reasoning_object.is_vehicle_or_cyclist() ||
        reasoning_object.planner_object().is_parked_car()) {
      // Currently we only count the vehicle that is not a parked car.
      continue;
    }

    const speed::pb::ObjectProximityInfo& proximity =
        reasoning_object.object_proximity_info();

    if (!math::IsInRange(proximity.projected_ra_arc_length().end(),
                         -kLongitudinalDistanceToConsiderDenseTrafficInMeter,
                         kLongitudinalDistanceToConsiderDenseTrafficInMeter)) {
      // The agent is longitudinally far away.
      // TODO(jinhao): Comprehensively consider the size and the scope to count
      // the agent, e.g. large car will cover a large longitudinal range.
      continue;
    }

    if (proximity.abs_lateral_gap() >
        kLateralDistanceToConsiderDenseTrafficInMeter) {
      // The agent is laterally far away.
      continue;
    }

    const int64_t obj_id = reasoning_object.id();
    if (IsNonLeadingAgentInteractingWithEgo(reasoning_input)) {
      non_leading_interacting_agent_ids_.push_back(obj_id);
    }

    // Get the lane that occupied by the agent.
    const std::optional<int64_t> agent_current_lane_id = GetAgentCurrentLaneId(
        previous_iter_seed, reasoning_object.planner_object(),
        joint_pnc_map_service);

    if (!agent_current_lane_id.has_value()) {
      continue;
    }
    for (LaneAgentOccupancyMap& lane_agent_occupancy_map :
         neighbor_lanes_agent_occ_maps_) {
      if (lane_agent_occupancy_map.find(agent_current_lane_id.value()) !=
          lane_agent_occupancy_map.end()) {
        lane_agent_occupancy_map[agent_current_lane_id.value()].push_back(
            obj_id);
      }
    }
    for (LaneAgentOccupancyMap& lane_agent_occupancy_map :
         merge_lanes_agent_occ_maps_) {
      if (lane_agent_occupancy_map.find(agent_current_lane_id.value()) !=
          lane_agent_occupancy_map.end()) {
        lane_agent_occupancy_map[agent_current_lane_id.value()].push_back(
            obj_id);
      }
    }
  }
}

void UrgencyInfo::AddInterestedLaneClusters(
    const TrajectoryInfo& trajectory_info) {
  AddInterestedNeighborLaneClusters(trajectory_info);
  AddInterestedMergeLaneClusters(trajectory_info);
}

void UrgencyInfo::AddInterestedNeighborLaneClusters(
    const TrajectoryInfo& trajectory_info) {
  auto add_lane_clusters =
      [this](const std::vector<const pnc_map::Lane*>& neighbor_lanes) {
        for (const pnc_map::Lane* lane : neighbor_lanes) {
          if (lane != nullptr) {
            neighbor_lanes_agent_occ_maps_.push_back(
                GetLaneAgentOccupancyMap(lane));
          }
        }
      };
  const pnc_map::Lane* current_lane =
      *trajectory_info.lane_sequence_iterator().current_lane();
  const pnc_map::Lane* left_neighbor = current_lane->adjacent_left_lane();
  const pnc_map::Lane* right_neighbor = current_lane->adjacent_right_lane();
  switch (trajectory_info.ego_current_lane_relative_state()) {
    case planner::pb::EgoCurrentLaneRelativeState::FullyOnTheLane:
      add_lane_clusters({left_neighbor, right_neighbor});
      break;
    case planner::pb::EgoCurrentLaneRelativeState::CrossOutFromLeftMarking:
    case planner::pb::EgoCurrentLaneRelativeState::CrossInFromLeftMarking:
      add_lane_clusters({left_neighbor, current_lane});
      break;
    case planner::pb::EgoCurrentLaneRelativeState::CrossOutFromRightMarking:
    case planner::pb::EgoCurrentLaneRelativeState::CrossInFromRightMarking:
      add_lane_clusters({right_neighbor, current_lane});
      break;
    case planner::pb::EgoCurrentLaneRelativeState::FullyOnLeftOutside:
    case planner::pb::EgoCurrentLaneRelativeState::FullyOnRightOutside:
      // If ego is fully outside the ego lane sequence, it is actually merging
      // back to ego lane sequence, should be considered as a dense merge case.
      break;
    default:
      DCHECK(false) << "Unhandled ego current lane relative state";
  }
}

void UrgencyInfo::AddInterestedMergeLaneClusters(
    const TrajectoryInfo& trajectory_info) {
  // Adds the merge lane and its predecessors.
  LaneAgentOccupancyMap merge_lane_clusters;
  const LaneSequenceIterator& lane_sequence_iterator =
      trajectory_info.lane_sequence_iterator();
  const pnc_map::Lane* current_lane = *lane_sequence_iterator.current_lane();
  const std::vector<pnc_map::BrotherLane>& cur_lane_merge_brothers =
      current_lane->GetBrothers(pnc_map::BrotherLane::RelationType::kMerge);
  if (trajectory_info.ego_current_lane_relative_state() ==
          planner::pb::EgoCurrentLaneRelativeState::FullyOnRightOutside ||
      trajectory_info.ego_current_lane_relative_state() ==
          planner::pb::EgoCurrentLaneRelativeState::FullyOnLeftOutside) {
    // If ego is full outside of the lane sequence, ego is merging back to the
    // current lane.
    AddMergeLaneClusters(current_lane, /*length=*/0.0, merge_lane_clusters);
    merge_lanes_agent_occ_maps_.push_back(merge_lane_clusters);
    return;
  }

  if (!cur_lane_merge_brothers.empty()) {
    for (const pnc_map::BrotherLane& merge_lane : cur_lane_merge_brothers) {
      AddMergeLaneClusters(merge_lane.lane(), /*length=*/0.0,
                           merge_lane_clusters);
    }
  } else {
    // If there is no lane merging with current lane, check the lane after the
    // current lane in lane sequence.
    const auto next_lane_iter =
        std::next(lane_sequence_iterator.current_lane());
    if (next_lane_iter == lane_sequence_iterator.end()) {
      return;
    }
    const std::vector<pnc_map::BrotherLane>& next_lane_merge_brothers =
        (*next_lane_iter)
            ->GetBrothers(pnc_map::BrotherLane::RelationType::kMerge);
    if (next_lane_merge_brothers.empty()) {
      return;
    }
    for (const pnc_map::BrotherLane& merge_lane : next_lane_merge_brothers) {
      AddMergeLaneClusters(merge_lane.lane(), /*length=*/0.0,
                           merge_lane_clusters);
    }
  }
  merge_lanes_agent_occ_maps_.push_back(merge_lane_clusters);
}

void UrgencyInfo::UpdateUrgencyReasons() {
  if (!has_ego_stuck_for_long_time_) {
    // Currently we only update the urgency reason when ego has stuck for a long
    // time.
    return;
  }

  // The urgency reason |StuckInDynamicDenseTraffic| is based on the agent's
  // overlap, couting the the number of dynamic interacting agents in front of
  // ego front bumper to estimate if ego got stuck by dense traffic.
  if (non_leading_interacting_agent_ids_.size() >=
      kNumAgentToConsiderAsDenseTraffic) {
    urgency_reasons_.insert(
        planner::pb::UrgencyReason::StuckInDynamicDenseTraffic);
  }

  const auto has_dense_traffic =
      [](const std::vector<LaneAgentOccupancyMap>& lane_seqs_agent_occ_map)
      -> bool {
    return std::any_of(
        lane_seqs_agent_occ_map.begin(), lane_seqs_agent_occ_map.end(),
        [](const LaneAgentOccupancyMap& lane_agent_occupancy_map) {
          int num_agents_in_lane_sequence = 0;
          std::for_each(
              lane_agent_occupancy_map.begin(), lane_agent_occupancy_map.end(),
              [&num_agents_in_lane_sequence](const auto& lane_agents_pair) {
                num_agents_in_lane_sequence += lane_agents_pair.second.size();
              });
          return num_agents_in_lane_sequence >=
                 kNumAgentToConsiderAsDenseTraffic;
        });
  };

  // The urgency reason |AvoidCutInInDenseTraffic| and |MergeInDenseTraffic| is
  // based on the lane association result of neighbor/merge traffic flow,
  // couting the number of agents in the neighbor/merge lanes to recognize the
  // static dense traffic who have no overlap with ego path.
  if (has_dense_traffic(neighbor_lanes_agent_occ_maps_)) {
    urgency_reasons_.insert(
        planner::pb::UrgencyReason::AvoidCutInInDenseTraffic);
  }

  if (has_dense_traffic(merge_lanes_agent_occ_maps_)) {
    urgency_reasons_.insert(planner::pb::UrgencyReason::MergeInDenseTraffic);
  }
}

// Update urgency reasons when ego is in upl.
void UrgencyInfo::UpdateUPLUrgencyReasons(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const std::vector<PredictionDecisionMakerInput>& reasoning_inputs) {
  bool should_be_cautious_for_upl = false;
  std::unordered_set<int64_t> attention_id_set;
  for (const PredictionDecisionMakerInput& reasoning_input : reasoning_inputs) {
    if (IsOncomingCrossingAgentWhenEgoIsInUPL(reasoning_input)) {
      should_be_cautious_for_upl |= reasoning_util::ShouldBeCautiousDuringUPL(
          world_context, trajectory_info, reasoning_input.reasoning_object);
      attention_id_set.insert(reasoning_input.reasoning_object.id());
    }
  }
  should_latch_discomfort_for_progress_in_upl_ =
      !should_be_cautious_for_upl && attention_id_set.size() > 0;

  if (should_be_cautious_for_upl) {
    // Don't trigger discomfort for progress when ego should be cautious,
    // currently, ShouldBeCautiousDuringUPL only consider the situation when
    // the traffic light just turns green and ego's speed is lower than 6km/h
    // and the agent is not stationary.
    return;
  }

  const auto is_attention_agent =
      [](const std::unordered_set<int64_t>& attention_id_set,
         int64_t id) -> bool {
    return attention_id_set.find(id) != attention_id_set.end();
  };
  // check whether we have blocked upl oncoming traffic.
  for (const PredictionDecisionMakerInput& reasoning_input : reasoning_inputs) {
    if (!is_attention_agent(attention_id_set,
                            reasoning_input.reasoning_object.id())) {
      continue;
    }
    const std::vector<AgentTrajectoryInfo>& agent_trajectory_infos =
        reasoning_input.agent_trajectory_infos;
    if (reasoning_input.reasoning_object.is_vehicle() &&
        std::any_of(agent_trajectory_infos.begin(),
                    agent_trajectory_infos.end(),
                    [](const AgentTrajectoryInfo& agent_trajectory_info) {
                      return IsAgentTrajectoryBlockedByEgoCurrentPose(
                          agent_trajectory_info);
                    })) {
      if (!was_discomfort_for_progress_in_upl_) {
        rt_event::PostRtEvent<rt_event::planner::DFPBlockingTraffic>(
            std::to_string(reasoning_input.reasoning_object.id()));
      }
      urgency_reasons_.insert(planner::pb::UrgencyReason::BlockingTrafficInUPL);
      break;
    }
  }

  // check whether ego has waited too long or may wait too long in the future.
  constexpr double kMaxWaitingTimeToStartDFPWithAgentsBehindInSec = 6.0;
  constexpr double kMaxWaitingTimeToStartDFPWithoutAgentsBehindInSec = 10.0;
  const double waiting_time_tolerance =
      world_context.has_vehicle_waiting_behind_ego()
          ? kMaxWaitingTimeToStartDFPWithAgentsBehindInSec
          : kMaxWaitingTimeToStartDFPWithoutAgentsBehindInSec;
  const double has_waited_time =
      math::Ms2Sec(world_context.ego_stuck_duration_ms());
  for (const PredictionDecisionMakerInput& reasoning_input : reasoning_inputs) {
    const ReasoningObject& reasoning_object = reasoning_input.reasoning_object;
    if (!is_attention_agent(attention_id_set, reasoning_object.id())) {
      continue;
    }
    std::optional<double> potential_future_wait_time =
        EstimatedEgoFutureWaitTimeForAgent(world_context, reasoning_object);
    if (!potential_future_wait_time.has_value()) {
      continue;
    }
    if (has_waited_time + potential_future_wait_time.value() >
        waiting_time_tolerance) {
      if (!was_discomfort_for_progress_in_upl_) {
        rt_event::PostRtEvent<rt_event::planner::DFPWaitingTooLong>(
            absl::StrCat(std::to_string(reasoning_input.reasoning_object.id()),
                         "_",
                         std::to_string(has_waited_time +
                                        potential_future_wait_time.value())));
      }
      urgency_reasons_.insert(planner::pb::UrgencyReason::UPLWaitingTooLong);
      break;
    }
  }
}

// Update urgency reasons when ego is in upl.
void UrgencyInfo::UpdateUTurnUrgencyReasons(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info) {
  const auto& current_lane_info = trajectory_info.EgoCurrentLaneInfo();
  if (current_lane_info.lane_turn_type != hdmap::Lane::U_TURN ||
      current_lane_info.lane->IsInJunction()) {
    // Only consider the u turns in non junction scene.
    return;
  }

  if (was_discomfort_for_progress_in_u_turn_) {
    // Latch the urgency reason until ego finish the u turn.
    urgency_reasons_.insert(planner::pb::UrgencyReason::BlockingUTurn);
    return;
  }

  const auto& conflicting_lanes_in_lane_vector =
      trajectory_info.traffic_rules().lane_sequence_conflicting_lanes;

  const auto u_turn_conflicting_lanes = std::find_if(
      conflicting_lanes_in_lane_vector.begin(),
      conflicting_lanes_in_lane_vector.end(),
      [ego_lane_id = current_lane_info.lane->id()](
          const traffic_rules::EgoLaneConflictingLanesInfo&
              ego_lane_conflicting_lanes_info) {
        return ego_lane_conflicting_lanes_info.ego_lane.id() == ego_lane_id;
      });

  const bool has_ego_entered_conflicting_lane =
      (u_turn_conflicting_lanes != conflicting_lanes_in_lane_vector.end()) &&
      std::any_of(
          u_turn_conflicting_lanes->conflicting_lanes.begin(),
          u_turn_conflicting_lanes->conflicting_lanes.end(),
          [ra_to_leading_bumper = world_context.ra_to_leading_bumper()](
              const traffic_rules::ConflictingLaneInLaneSequence&
                  conflict_lane) {
            return conflict_lane.intersection_range_with_path.has_value() &&
                   conflict_lane.intersection_range_with_path->start_pos +
                           kMinDistConsiderEnterInConflictLaneOfUTurnInMeter <
                       ra_to_leading_bumper;
          });

  if (has_ego_entered_conflicting_lane || has_ego_stuck_for_long_time_) {
    urgency_reasons_.insert(planner::pb::UrgencyReason::BlockingUTurn);
  }
}

void UrgencyInfo::UpdateUrgencyReasonsForKeepClearZone(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const std::vector<PredictionDecisionMakerInput>& reasoning_inputs) {
  const std::optional<math::Range1d> keep_clear_zone_range =
      traffic_rules::GetNearestKeepLearZoneRange(
          trajectory_info.traffic_rules().zones,
          world_context.ra_to_leading_bumper());
  if (!keep_clear_zone_range.has_value()) {
    // There is no keep clear zone nearby or ego has passed the keep clear zone.
    return;
  }

  if (was_proceeding_urgently_) {
    // Keep progressing until ego has passed the keep clear zone.
    urgency_reasons_.insert(
        planner::pb::UrgencyReason::UnstuckFromKeepClearZone);
    return;
  }

  // Only consider the ego fully stopped scenario.
  if (world_context.ego_stuck_duration_ms() <
      kMaxEgoStoppedDurationWithTrafficFlowMovingAheadInMs) {
    return;
  }

  if (std::any_of(reasoning_inputs.begin(), reasoning_inputs.end(),
                  [&keep_clear_zone_range](
                      const PredictionDecisionMakerInput& reasoning_input) {
                    return WillAgentLeaveKeepClearZone(
                        reasoning_input, keep_clear_zone_range.value());
                  })) {
    urgency_reasons_.insert(
        planner::pb::UrgencyReason::UnstuckFromKeepClearZone);
  }
}

void UrgencyInfo::UpdateUrgencyReasonsForTheEndOfTrafficLight(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const std::vector<PredictionDecisionMakerInput>& reasoning_inputs,
    const pb::UrgencyInfoSeed& previous_urgency_info_seed,
    std::string* debug_str_ptr) {
  if (debug_str_ptr != nullptr) {
    absl::StrAppend(debug_str_ptr, "\n[End Of TL Debug]");
  }
  // Get the junction ego currently in.
  const traffic_rules::JunctionInLaneSequence* ego_current_junction_ptr =
      traffic_rules::GetEgoCurrentJunction(
          trajectory_info.EgoCurrentLaneInfo().lane,
          trajectory_info.traffic_rules().lane_sequence_junctions);

  if (ego_current_junction_ptr == nullptr) {
    // Early return and clear the flag once ego has exited the junction.
    if (debug_str_ptr != nullptr) {
      absl::StrAppend(debug_str_ptr, "exit junction;");
    }
    has_stuck_in_junction_since_last_red_light_ = false;
    return;
  }

  const traffic_rules::TrafficLightInfo* ego_lane_traffic_light =
      traffic_rules::ComputeEgoLaneInJunctionTrafficLight(
          *ego_current_junction_ptr,
          trajectory_info.traffic_rules().traffic_lights);

  if (ego_lane_traffic_light == nullptr) {
    // Not consider junction without traffic light so far.
    has_stuck_in_junction_since_last_red_light_ = false;
    return;
  }

  if (ego_lane_traffic_light->is_broken() ||
      ego_lane_traffic_light->is_unreliable_broken() ||
      ego_lane_traffic_light->is_using_watch_line()) {
    // Not consider the broken light and the light with watchline.
    has_stuck_in_junction_since_last_red_light_ = false;
    return;
  }

  has_stuck_in_junction_since_last_red_light_ =
      HasEgoStuckInJunctionSinceLastRedLight(
          world_context, ego_lane_traffic_light, previous_urgency_info_seed);

  // Condition 1: We have triggered discomfort for progress for this, will latch
  // it until ego exit the junction.
  if (previous_urgency_info_seed.was_discomfort_for_progress_at_end_of_tl()) {
    if (debug_str_ptr != nullptr) {
      absl::StrAppend(debug_str_ptr, "latched;");
    }
    urgency_reasons_.insert(planner::pb::UrgencyReason::EndOfTrafficLight);
    return;
  }

  // Condition 2: If ego has stuck in junction since the last red light, we
  // should be more aggressive to proceed the junction.
  if (has_stuck_in_junction_since_last_red_light_ &&
      !ego_lane_traffic_light->is_red(/*sustained=*/true)) {
    if (debug_str_ptr != nullptr) {
      absl::StrAppend(debug_str_ptr, "stuck over a cycle;");
    }
    urgency_reasons_.insert(planner::pb::UrgencyReason::EndOfTrafficLight);
    return;
  }

  // Condition 3: The light turns to yellow and red.
  // However, if the light has been red for a while and ego remains stopped,
  // we assume cross-traffic is flowing through the junction and will not
  // perform the aggressive behavior, unless we have a leading car to follow.
  const bool is_yellow_or_red_light =
      ego_lane_traffic_light->is_yellow_non_flashing(/*sustained=*/true) ||
      ego_lane_traffic_light->is_red(/*sustained=*/true);
  const bool is_cross_traffic_flowing =
      IsCrossTrafficFlowingInJunction(world_context, ego_lane_traffic_light);
  const bool has_leading_vehicle_to_follow =
      HasLeadingVehicleToFollow(world_context, reasoning_inputs);
  if (is_yellow_or_red_light &&
      (!is_cross_traffic_flowing || has_leading_vehicle_to_follow)) {
    if (debug_str_ptr != nullptr) {
      absl::StrAppend(
          debug_str_ptr,
          (has_leading_vehicle_to_follow ? "follow leading" : "end of light"));
    }
    urgency_reasons_.insert(planner::pb::UrgencyReason::EndOfTrafficLight);
    return;
  }
}

void UrgencyInfo::UpdateRightHookUrgencyReasons(
    const WorldContext& world_context,
    const std::vector<const traffic_rules::LaneInfoInLaneSequence*>&
        target_right_turn_lanes,
    const std::vector<PredictionDecisionMakerInput>& pdm_inputs,
    const pb::ReasoningConfig& reasoning_config, std::string* debug_str_ptr) {
  constexpr int64_t kMaxEgoWaitingTimeAtRightHookInMs = 1000;
  constexpr double kMaxDistanceStoppingBeforeRightHookInMeter = 5.0;
  constexpr int64_t kMinLengthOfTimeGapForTriggeringCreepInMs = 1000;
  constexpr int64_t kMaxPreviewTimeForTriggeringCreepInMs = 2000;

  if (debug_str_ptr != nullptr) {
    absl::StrAppend(debug_str_ptr, "\n[Right Hook] ");
  }

  for (const auto* lane : target_right_turn_lanes) {
    if (right_hook_ra_arclength_range_.has_value()) {
      math::UpdateMin(lane->start_ra_arclength,
                      right_hook_ra_arclength_range_->start_pos);
      math::UpdateMax(lane->end_ra_arclength,
                      right_hook_ra_arclength_range_->end_pos);
      continue;
    }
    right_hook_ra_arclength_range_ =
        math::Range1d(lane->start_ra_arclength, lane->end_ra_arclength);
  }

  if (!right_hook_ra_arclength_range_.has_value() ||
      world_context.ra_to_leading_bumper() +
              kMaxDistanceStoppingBeforeRightHookInMeter <
          right_hook_ra_arclength_range_.value().start_pos) {
    if (debug_str_ptr != nullptr) {
      absl::StrAppend(debug_str_ptr, "Ego not at right hook; ");
    }
    return;
  }

  if (was_discomfort_for_progress_in_right_hook_) {
    if (debug_str_ptr != nullptr) {
      absl::StrAppend(debug_str_ptr, "latch; ");
    }
    urgency_reasons_.insert(planner::pb::UrgencyReason::RightHookDenseTraffic);
    return;
  }

  if (!world_context.EgoAlmostStopped()) {
    if (debug_str_ptr != nullptr) {
      absl::StrAppend(debug_str_ptr, "Ego not stop at right hook; ");
    }
    return;
  }

  if (world_context.ego_stuck_duration_ms() <
      kMaxEgoWaitingTimeAtRightHookInMs) {
    if (debug_str_ptr != nullptr) {
      absl::StrAppend(debug_str_ptr, "Ego not stuck too long; ");
    }
    return;
  }

  const CreepTimeGapInfo::SelectedAgents considered_agents =
      GetCyclistsGoingThroughRightHook(
          world_context, pdm_inputs, reasoning_config,
          ::base::CheckAndGetValue(right_hook_ra_arclength_range_));
  if (considered_agents.empty()) {
    if (debug_str_ptr != nullptr) {
      absl::StrAppend(debug_str_ptr, "No agent; ");
    }
    return;
  }
  const CreepTimeGapInfo right_hook_gap_info =
      CreepTimeGapInfo::ComputeTimeGapInfo(considered_agents);
  if (debug_str_ptr != nullptr) {
    right_hook_gap_info.PopulateDebugString(*debug_str_ptr);
  }

  for (const auto& gap : right_hook_gap_info.gaps()) {
    const int64_t gap_length =
        gap.second - std::max(kMaxPreviewTimeForTriggeringCreepInMs, gap.first);
    if (gap_length > kMinLengthOfTimeGapForTriggeringCreepInMs &&
        gap.first < kMaxPreviewTimeForTriggeringCreepInMs) {
      if (debug_str_ptr != nullptr) {
        absl::StrAppend(debug_str_ptr, "Creep; ");
      }
      urgency_reasons_.insert(
          planner::pb::UrgencyReason::RightHookDenseTraffic);
      return;
    }
  }

  if (debug_str_ptr != nullptr) {
    absl::StrAppend(debug_str_ptr, "No suitable gap, not creep; ");
  }
}

void UrgencyInfo::UpdateCrosswalkUrgencyReasons(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const pb::UrgencyInfoSeed& previous_iter_seed,
    const std::vector<const traffic_rules::LaneInfoInLaneSequence*>*
        target_right_turn_lanes,
    std::string* debug_str_ptr) {
  if (debug_str_ptr != nullptr) {
    absl::StrAppend(debug_str_ptr, "\n[Crosswalk Creeping Debug]");
  }
  const auto& previous_iter_triggered_crosswalks =
      previous_iter_seed.creeping_crosswalk_ids();
  // The crosswalks are sorted by arc-length so we only need to store the last
  // one crosswalk for which creep is triggered.
  const traffic_rules::CrosswalkInLaneSequence* last_triggered_crosswalk_ptr =
      nullptr;
  for (const traffic_rules::CrosswalkInLaneSequence& crosswalk :
       trajectory_info.traffic_rules().crosswalks) {
    if (debug_str_ptr != nullptr) {
      absl::StrAppendFormat(debug_str_ptr,
                            "\nCrosswalk %ld: ", crosswalk.crosswalk_ptr->id());
    }
    const bool is_triggered_in_previous_iter =
        std::find(previous_iter_triggered_crosswalks.begin(),
                  previous_iter_triggered_crosswalks.end(),
                  crosswalk.crosswalk_ptr->id()) !=
        previous_iter_triggered_crosswalks.end();

    if (is_triggered_in_previous_iter) {
      creeping_crosswalk_ids_.insert(crosswalk.crosswalk_ptr->id());
      // Considering that we could read seed directly from bag, the logic of
      // checking waiting duration could not be reached while the crosswalk id
      // has been in the seed.
      last_triggered_crosswalk_ptr = &crosswalk;
      if (debug_str_ptr != nullptr) {
        absl::StrAppend(debug_str_ptr, "latch creep;");
      }
      continue;
    }

    if (trajectory_info.is_ego_current_lane_red_light()) {
      continue;
    }

    // Ego has stopped for a long duration. Or, there are vehicles waiting
    // behind Ego and the traffic could be blocked by Ego. In both cases, Ego
    // should take steps to make progress.
    if (ShouldEgoCreepOnCrosswalkWithConsideringGap(
            world_context, crosswalk,
            GetCrosswalkTimeGapInfo(crosswalk.crosswalk_ptr->id()),
            last_triggered_crosswalk_ptr, target_right_turn_lanes,
            urgency_reasons_, debug_str_ptr)) {
      rt_event::PostRtEvent<
          rt_event::planner::TriggerCrosswalkCreepingConsideringGap>(
          std::to_string(crosswalk.crosswalk_ptr->id()));
      creeping_crosswalk_ids_.insert(crosswalk.crosswalk_ptr->id());
      last_triggered_crosswalk_ptr = &crosswalk;
    }
  }

  if (!creeping_crosswalk_ids_.empty()) {
    urgency_reasons_.insert(planner::pb::UrgencyReason::CrosswalkWithDenseVRU);
  }
}

void UrgencyInfo::UpdateWaypointUrgencyReasons(
    const WorldContext& world_context) {
  const planner::pb::AssistManeuverPhase::Enum waypoint_assist_phase =
      reasoning_util::WaypointAssistPhase(world_context);
  // Not in waypoint assist.
  if (waypoint_assist_phase != planner::pb::AssistManeuverPhase::kIdle) {
    urgency_reasons_.insert(planner::pb::UrgencyReason::Waypoint);
  }
}

void UrgencyInfo::UpdateCurrentIterSeed(pb::UrgencyInfoSeed& current_seed) {
  current_seed.set_ego_was_proceeding_urgently(
      ShouldSetDiscomfortForProgressInDenseTraffic());

  current_seed.set_was_discomfort_for_progress_in_upl(
      ShouldSetDiscomfortForProgressInUPL());

  current_seed.set_was_discomfort_for_progress_in_u_turn(
      HasUrgencyReason(planner::pb::UrgencyReason::BlockingUTurn));

  current_seed.set_was_discomfort_for_progress_at_end_of_tl(
      HasUrgencyReason(planner::pb::UrgencyReason::EndOfTrafficLight));

  current_seed.set_has_stuck_in_junction_since_last_red_light(
      has_stuck_in_junction_since_last_red_light_);
  current_seed.set_was_discomfort_for_progress_in_right_hook(
      HasUrgencyReason(planner::pb::UrgencyReason::RightHookDenseTraffic));

  for (const int64_t creeping_crosswalk_id : creeping_crosswalk_ids_) {
    current_seed.add_creeping_crosswalk_ids(creeping_crosswalk_id);
  }
}

void UrgencyInfo::PopulateDebug(
    pb::UrgencyInfoDebug& urgency_info_debug) const {
  urgency_info_debug.set_should_creep(ShouldProceedUrgently());
  urgency_info_debug.set_has_ego_stuck_for_long_time(
      has_ego_stuck_for_long_time_);
  for (const planner::pb::UrgencyReason::Enum urgency_reason :
       urgency_reasons_) {
    urgency_info_debug.add_urgency_reason(urgency_reason);
  }

  for (const LaneAgentOccupancyMap& lane_agent_occupancy_map :
       neighbor_lanes_agent_occ_maps_) {
    PopulateLaneAgentOccupancyMapDebug(
        lane_agent_occupancy_map,
        urgency_info_debug.add_neighbor_lane_clusters_occupancy());
  }

  for (const LaneAgentOccupancyMap& lane_agent_occupancy_map :
       merge_lanes_agent_occ_maps_) {
    PopulateLaneAgentOccupancyMapDebug(
        lane_agent_occupancy_map,
        urgency_info_debug.add_merge_lane_clusters_occupancy());
  }

  urgency_info_debug.mutable_non_leading_interacting_agent_ids()->Add(
      non_leading_interacting_agent_ids_.begin(),
      non_leading_interacting_agent_ids_.end());

  for (const auto& [crosswalk_id, crosswalk_gap_info] :
       crosswalk_id_to_time_gap_info_) {
    absl::StrAppendFormat(urgency_info_debug.mutable_debug_str(),
                          "\nCrosswalk %ld gap info: ", crosswalk_id);
    crosswalk_gap_info.PopulateDebugString(
        *(urgency_info_debug.mutable_debug_str()));
  }

  for (const int64_t creeping_crosswalk_id : creeping_crosswalk_ids_) {
    urgency_info_debug.add_creeping_crosswalk_ids(creeping_crosswalk_id);
  }
}

}  // namespace speed
}  // namespace planner
