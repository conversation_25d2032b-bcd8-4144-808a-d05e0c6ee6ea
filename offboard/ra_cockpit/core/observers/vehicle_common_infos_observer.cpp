#include "vehicle_common_infos_observer.h"

#include <utility>

#include "av_comm/topics.h"
#include "glog/logging.h"

namespace remote_assist {

VehicleCommonInfosObserver::VehicleCommonInfosObserver(
    VehicleCommonInfosCallback callback)
    : vehicle_common_infos_callback_(std::move(callback)) {}

bool VehicleCommonInfosObserver::IsInterestedIn(
    const teleassist::pb::TeleassistRequest& message) {
  return (message.message_type() == kVehicleCommonInfos);
}

std::shared_ptr<google::protobuf::Message>
VehicleCommonInfosObserver::OnMessage(
    const teleassist::pb::TeleassistRequest& message,
    DataChannel data_channel) {
  auto vehicle_common_infos =
      std::make_shared<teleassist::pb::VehicleCommonInfos>();
  if (!vehicle_common_infos->ParseFromString(message.message_data())) {
    LOG(ERROR) << "Received error VehicleCommonInfos message format, len = "
               << message.message_data().length();
    return nullptr;
  }
  if (vehicle_common_infos_callback_) {
    vehicle_common_infos_callback_(vehicle_common_infos, data_channel);
  }
  return vehicle_common_infos;
}

}  // namespace remote_assist
