// 滴滴出行 location service服务 公共架构 2016.1

namespace cpp  didi.locsvr
namespace java didi.locsvr
namespace go   didi.locsvr
namespace py   locsvr

enum Error {
    LOCSVR_COUNTRY_ID_NOT_FOUND        = -1,
    ///< success
    LOCSVR_RET_OK                      = 0,
    ///< 无效坐标
    CLIENT_INVALID_COORD               = 1,
    ///< 无效参数
    CLIENT_INVALID_ARGUMENT            = 2,
    ///< Server错误
    SERVER_ERROR                       = 3,
    ///< 超过单次请求最大坐标数限制
    LOCSVR_RET_COORD_SIZE_EXCEED_LIMIT = 4,
    ///< 传入坐标数为0
    LOCSVR_RET_COORD_SIZE_ZERO         = 5,
    ///< 非法坐标类型
    LOCSVR_RET_INVALID_COORD_TYPE      = 6,
    ///< 超过单次请求最大uid限制
    LOCSVR_RET_UID_SIZE_EXCEED_LIMIT   = 7,
    ///< 传入uid数为0
    LOCSVR_RET_UID_SIZE_ZERO           = 8,
    ///< 非法token
    LOCSVR_RET_INVALID_TOKEN           = 9,
    ///< 流量超过quota，被限流
    LOCSVR_RET_EXCEED_RATE_LIMIT       = 10,
    ///< 非法ip
    LOCSVR_RET_IP_INVALID              = 11
}

enum CoordType {
    BAIDU                       = 1,
    SOSOGCJ                     = 2,
    WGS84                       = 3,
    NONE                        = 4
}

enum ExtraFieldsSpec {
    ///< gps_info
    EXTRA_FIELDS_GPS_INFO           = 0,
    ///< 实时记录司机／乘客状态变化
    EXTRA_FIELDS_RT_STATUS          = 1,
    ///< 司机端上听单状态
    EXTRA_FIELDS_LISTEN_STATUS      = 2,
    ///< 航向夹角
    EXTRA_FIELDS_ANGLE              = 3,
    ///< 格子id
    EXTRA_FIELDS_GRID_ID            = 4,
    ///< 等同os_utc_offset，根据I18n规范重现命名，新系统不要使用该字段，请用下方os_utc_offset字段
    EXTRA_FIELDS_UTC_OFFSET         = 5,
    ///< 手机本机基于china的时区，基于china的offset，精确到分钟
    EXTRA_FIELDS_CHINA_APP_OFFSET   = 6,
    ///< 真实坐标（城市经纬度／司乘经纬度）反解的时区，基于china的offset，精确到分钟
    EXTRA_FIELDS_CHINA_RT_OFFSET    = 7,
    ///< 真实坐标（城市经纬度／司乘经纬度）反解的时区，基于utc的offset，精确到分钟
    EXTRA_LOCATION_UTC_OFFSET       = 8,
    ///< 手机本机的时区,  基于utc的offset，精确到分钟
    EXTRA_OS_UTC_OFFSET             = 9
}

struct TimeZone {
    1: optional i32 utc_offset;         // 不再维护，请勿使用，时区信息请接入国际化 elvish
    2: optional i32 china_app_offset;   // 不再维护，请勿使用，时区信息请接入国际化 elvish
    3: optional i32 china_rt_offset;    // 不再维护，请勿使用，时区信息请接入国际化 elvish
    4: optional i32 location_utc_offset;    // 不再维护，请勿使用，时区信息请接入国际化 elvish
    5: optional i32 os_utc_offset;      // 不再维护，请勿使用，时区信息请接入国际化 elvish
    6: optional string name;            // 时区名称
}

struct Gpsinfo {
    ///< 精度
    1: required double                       accuracy;
    ///< 头方向
    2: required double                       direction;
    ///< 速度
    3: required double                       speed;
    ///< 航向夹角
    4: optional double                       angle;
}

struct Coordinate {
    ///< 经度
    1: required double                       lng;
    ///< 纬度
    2: required double                       lat;
    ///< 原始坐标系
    3: optional CoordType                    org_coord_type;
    ///< 坐标时间戳
    4: optional i64                          timestamp;
    ///< GPS信息
    5: optional Gpsinfo                      gps_info;
    ///< 实时记录司机／乘客状态变化
    6: optional i32                          rt_status;
    ///< 司机端上听单状态
    7: optional i32                          listen_status;
    ///< 航向夹角
    8: optional double                       angle;
    ///< 格子id
    9: optional string                       grid_id;
    ///< 时区信息
    10: optional TimeZone                    time_zone;
}

struct Cityinfo {
    ///< 城市id
    1: required i32                          cityid;
    ///< 城市名称
    2: required string                       city_desc;
    ///< 区域id
    3: required i32                          countyid;
    ///< 区域名称
    4: required string                       county_desc;
    ///< 国家/地区id
    5: optional i32                          countryid;
    ///< 国家/地区名称
    6: optional string                       country_desc;

    7: optional i32                          provinceid;    // 省/州行政编号
    8: optional string                       province_desc; // 省/州行政名称
    9: optional string                       country_code;  // 国家/地区三字码
    10: optional string                      district_code; // 城市区号
    11: optional TimeZone                    time_zone;     // 时区信息
    12: optional string                      canonical_country_code;  // 国家/地区2字码
}

struct Filter {
    ///< 指定需要返回的字段
    1: optional i64                          extra_fields_spec;
    ///< 返回timeout秒内更新过的坐标
    2: optional i32                          timeout;
}

enum LANGUAGE_TYPE {
    LNG_LOCAL           = 0,        // 本地语言,比如大陆就是简体中文,巴西就是葡萄牙语,美国就是英语
    LNG_SIMP_CHINESE    = 1,        // 简体中文
    LNG_US_ENGLISH      = 2,        // 美国英语
    LNG_TRAD_CHINESE    = 3,        // 繁体中文
}

enum Area_RET_TYPE {
    AREA_ADMIN_SIMPLE           = 1, //返回国家/地区,省、市的简要信息
    AREA_ADMIN_DETAIL           = 2  //返回国家/地区,省、市、区的简要信息
}

struct AreaFilter {
    1: optional Area_RET_TYPE area_type;        // 裁剪返回城市信息,默认返回国家/地区,省、市的简要信息（不包含名称）
    2: optional LANGUAGE_TYPE lan_type;         // 返回城市信息的多语言版本,默认本地语言
}

struct MultiCoordRequest {
    1: required i64 log_id;                 // 用于上下游case分析
    2: required string token;               // 调用方凭据
    3: required list<Coordinate> coords,    // 请求坐标
    4: required CoordType req_coord_type    // 请求坐标的坐标类型
    5: optional AreaFilter area_filter;     // 裁剪返回城市信息,默认返回国家/地区,省、市的简要信息（不包含名称）,默认返回本地语言
    6: optional Filter coord_filter         // 返回结果为坐标有效
}

struct MultiAreaInfoResponse {
    1: required i32 ret_code;                   // 表示计算是否成功,0表示成功,其他字段表示失败
    2: required string ret_msg;                 // 错误信息,正常返回OK,其他情况返回相应错误信息
    3: required map<i64, Cityinfo> city_infos;  // 命中的城市信息, key为uid或者下标
}

service LocService {

    ///< 批量根据坐标查cid，返回值顺序与输入坐标顺序一致 最多支持50个
    MultiAreaInfoResponse MultiAreaInfoByCoord(1:MultiCoordRequest req);
}