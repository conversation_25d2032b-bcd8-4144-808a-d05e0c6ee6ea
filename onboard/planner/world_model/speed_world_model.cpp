#include "planner/world_model/speed_world_model.h"

#include <utility>

namespace planner {

SpeedWorldModel::SpeedWorldModel(
    const std::shared_ptr<const pnc_map::JointPncMapService>&
        joint_pnc_map_service,
    std::unique_ptr<const Snapshot> latest_snapshot_ptr,
    std::optional<prediction::pb::AgentList>&& agent_list,
    std::vector<TrackedObject>&& tracked_objects,
    std::vector<ConstructionZone>&& construction_zones,
    bool should_ignore_map_change_traffic_lights,
    std::unordered_map<ConstructionZoneId, const ConstructionZone*>&&
        construction_zone_ptr_map,
    tbb::concurrent_unordered_map<ObjectId,
                                  std::vector<PredictedTrajectoryWrapper>>&&
        object_prediction_map,
    tbb::concurrent_unordered_map<ObjectId,
                                  ConditionalPredictedTrajectoryWrapper>&&
        object_conditional_prediction_map,
    const RegionalMap& regional_map,
    const routing::pb::RouteQuery& replan_route_query,
    const std::tm* order_start_time_info,
    bool should_generate_pull_out_jump_out_sequence, bool is_high_agent_density,
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequences,
    std::optional<double> order_start_position_distance,
    bool should_planner_respond_mrc,
    bool is_ego_position_on_route_ready_for_pull_out,
    bool should_trigger_pull_out_by_mrc, bool has_pull_out_finished,
    const RobotState& robot_state,
    const TrafficSignalReasoner& traffic_signal_reasoner,
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const std::shared_ptr<std::unordered_map<ObjectId, PlannerObject>>&
        planner_object_map_ptr,
    const PlannerObjectsHistory& planner_objects_history,
    const std::vector<PickupDropoffZoneInfo>& pickup_dropoff_zone_infos,
    const pull_over::PullOverRequestInfo& pull_over_request_info,
    const lane_selection::MapChangeAreaHandleParameter&
        map_change_area_handle_param,
    LaneBlockageDetector& lane_blockage_detector,
    LaneCongestionDetector& lane_congestion_detector,
    std::unique_ptr<planner::RouteModelSpeedData>& route_model_speed_data,
    CurrentLaneAssociator& current_lane_associator,
    CurrentLaneReasoner& current_lane_reasoner,
    WaypointAssistTracker& waypoint_assist_tracker,
    ImmediatePullOverStateManager& immediate_pull_over_state_manager,
    AssistDirectiveGenerator& assist_directive_generator,
    lane_selection::StuckAvoidanceReasoner& stuck_avoidance_reasoner,
    AssistTaskScheduler& assist_task_scheduler,
    lane_selection::ChangedLaneStructureReasoner&
        changed_lane_structure_reasoner)
    : joint_pnc_map_service_(joint_pnc_map_service),
      latest_snapshot_ptr_(std::move(latest_snapshot_ptr)),
      agent_list_(std::move(agent_list)),
      tracked_objects_(std::move(tracked_objects)),
      construction_zones_(std::move(construction_zones)),
      should_ignore_map_change_traffic_lights_(
          should_ignore_map_change_traffic_lights),
      construction_zone_ptr_map_(std::move(construction_zone_ptr_map)),
      object_prediction_map_(std::move(object_prediction_map)),
      object_conditional_prediction_map_(
          std::move(object_conditional_prediction_map)),
      order_start_position_distance_(std::move(order_start_position_distance)),
      regional_map_(regional_map),
      replan_route_query_(replan_route_query),
      order_start_time_info_(order_start_time_info),
      should_generate_pull_out_jump_out_sequence_(
          should_generate_pull_out_jump_out_sequence),
      is_high_agent_density_(is_high_agent_density),
      last_selected_lane_sequences_(last_selected_lane_sequences),
      should_planner_respond_mrc_(should_planner_respond_mrc),
      is_ego_position_on_route_ready_for_pull_out_(
          is_ego_position_on_route_ready_for_pull_out),
      should_trigger_pull_out_by_mrc_(should_trigger_pull_out_by_mrc),
      has_pull_out_finished_(has_pull_out_finished),
      robot_state_(robot_state),
      traffic_signal_reasoner_(traffic_signal_reasoner),
      drivable_lane_reasoner_(drivable_lane_reasoner,
                              &traffic_signal_reasoner_),
      planner_object_map_ptr_(planner_object_map_ptr),
      planner_objects_history_(planner_objects_history),
      pickup_dropoff_zone_infos_(pickup_dropoff_zone_infos),
      pull_over_request_info_speed_data_(
          pull_over_request_info.CreatePullOverRequestInfoSpeedData()),
      lane_blockage_detector_speed_data_(
          lane_blockage_detector.CreateLaneBlockageDetectorSpeedData()),
      lane_congestion_detector_speed_data_(
          lane_congestion_detector.CreateLaneCongestionDetectorSpeedData()),
      route_model_speed_data_(std::move(route_model_speed_data)),
      current_lanes_speed_data_(
          FLAGS_planning_enable_current_lane_reasoner
              ? current_lane_reasoner.CreateCurrentLanesSpeedData()
              : current_lane_associator.CreateCurrentLanesSpeedData()),
      waypoint_assist_tracker_speed_data_(
          waypoint_assist_tracker.CreateWaypointAssistTrackerSpeedData()),
      immediate_pull_over_state_manager_speed_data_(
          immediate_pull_over_state_manager
              .CreateImmediatePullOverStateManagerSpeedData()),
      assist_directive_generator_speed_data_(
          assist_directive_generator.CreateAssistDirectiveGeneratorSpeedData()),
      assist_task_scheduler_speed_data_(
          assist_task_scheduler.CreateAssistTaskSchedulerSpeedData()),
      map_change_area_handle_param_(map_change_area_handle_param),
      stuck_avoidance_reasoner_speed_data_(
          stuck_avoidance_reasoner.CreateStuckAvoidanceReasonerSpeedData()),
      changed_lane_structure_reasoner_speed_data_(
          changed_lane_structure_reasoner
              .CreateChangedLaneStructureReasonerSpeedData()) {}

bool SpeedWorldModel::ShouldRequestRaUnstuckGivenMrcState() const {
  // Unblock publishing RA request if there is no latest snapshot.
  if (latest_snapshot_ptr_ == nullptr) {
    return true;
  }

  // Send RA if no MRC is requested.
  if (latest_snapshot_ptr_->mrc_request_ptr == nullptr) {
    return true;
  }

  // Do not send RA if ego enters MRC.
  const auto& mrc_request = *latest_snapshot_ptr_->mrc_request_ptr;
  if (mrc_request.mrm_progress() == mrc::pb::MrcProgress::FINISHED) {
    return false;
  }
  // Do not send RA if MRM_INLANE is ongoing, since ego is expected to enter MRC
  // and keep static in next cycle.
  if (mrc_request.mrm_progress() == mrc::pb::MrcProgress::ONGOING &&
      mrc_request.mrm_type() == mrc::pb::MrmType::MRM_INLANE) {
    return false;
  }
  return true;
}

void SpeedWorldModel::UpdateLightAssistAvailability(
    const BehaviorDecision& behavior_decision, const RobotState& robot_state,
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence,
    const CurrentLanesSpeedData& current_lanes_speed_data,
    const AssistDirectiveGeneratorSpeedData& assist_directive_generator,
    const pb::LightAssistSeed& light_assist_seed,
    pb::PlanningDebug* planning_debug) {
  assist_availability_analyzer_.UpdateLightAssistAvailability(
      behavior_decision, robot_state, last_selected_lane_sequence,
      current_lanes_speed_data,
      FLAGS_planning_enable_remote_assist_connection
          ? assist_task_scheduler()
                .remote_assist_signal()
                .HasPlannerReceivedTeleassistResponse()
          : assist_directive_generator.has_started_remote_assist(),
      light_assist_seed, planning_debug);
}

}  // namespace planner
