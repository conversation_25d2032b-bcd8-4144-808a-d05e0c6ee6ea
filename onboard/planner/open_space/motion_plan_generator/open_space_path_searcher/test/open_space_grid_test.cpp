#include "planner/open_space/motion_plan_generator/open_space_path_searcher/grid.h"

#include <unordered_map>
#include <vector>

#include <glog/logging.h>
#include <google/protobuf/text_format.h>
#include <gtest/gtest.h>

#include "onboard/planner/open_space/constraints/motion_plan_problem_manager.h"
#include "planner/behavior/util/lane_sequence/test/test_utility.h"

namespace planner {

namespace open_space {

using ::google::protobuf::TextFormat;

TEST(OpenSpaceGridTest, GridParamTest) {
  const math::geometry::Point2d ego_ra_center(0.0, 0.0);
  const math::geometry::Point2d target_xy_pt(4.8, 3.6);
  const double ego_length = 5.0;
  const MotionPlanROI motion_plan_roi(ego_ra_center, target_xy_pt,
                                      /*pos_1_xy_buffer=*/2.0 * ego_length,
                                      /*pos_2_xy_buffer=*/2.0 * ego_length);

  const double xy_res_m = 0.5;
  const GridParams grid_params(motion_plan_roi, xy_res_m);
  EXPECT_EQ(grid_params.xy_res_m, xy_res_m);

  // Validate x dim.
  EXPECT_EQ(grid_params.x_cell_size, 50);
  EXPECT_EQ(grid_params.x_range_length, 50 * xy_res_m);
  EXPECT_EQ(grid_params.x_vertices_size, 51);
  EXPECT_EQ(grid_params.x_range.start_pos, -10.0);
  EXPECT_EQ(grid_params.x_range.end_pos, 15.0);

  // Validate y dim.
  EXPECT_EQ(grid_params.y_cell_size, 48);
  EXPECT_EQ(grid_params.y_range_length, 48 * xy_res_m);
  EXPECT_EQ(grid_params.y_vertices_size, 49);
  EXPECT_EQ(grid_params.y_range.start_pos, -10.0);
  EXPECT_EQ(grid_params.y_range.end_pos, 14.0);
}

class OpenSpaceGridUpdateTest : public ::testing::Test {
 public:
  OpenSpaceGridUpdateTest() = default;

 protected:
  void SetUp() override {
    CHECK(
        TextFormat::ParseFromString(R"pb(
                                      xy_grid_resolution_in_meters: 0.5
                                      heading_slot_resolution_in_rad: 0.1)pb",
                                    &config_));
  }

  pb::OpenSpaceGridConfig config_;
};

TEST_F(OpenSpaceGridUpdateTest, GridUpdateTest) {
  const RobotState robot_state = test_utility::ConstructRobotState(
      /*robot_x=*/0.0, /*robot_y=*/0.0, /*vel_x=*/0.0, /*vel_y=*/0.0);
  MotionPlanProblemManager motion_plan_problem_manager(robot_state);
  motion_plan_problem_manager.SetStartPoseInfoFromRobotState(
      /*initial_motion_mode_guidance=*/std::nullopt);
  motion_plan_problem_manager.AppendTargetPoseInfo(
      /*x=*/10.0, /*y=*/15.0, /*heading=*/0.0,
      /*terminal_motion_mode_guidance=*/std::nullopt,
      /*xy_tolerance_roi=*/std::nullopt,
      /*heading_tolerance_range=*/std::nullopt);
  motion_plan_problem_manager.SetMotionPlanROIFromStartAndTargetPoses(
      /*start_pose_xy_buffer=*/2.0 * robot_state.GetLength(),
      /*target_pose_xy_buffer=*/2.0 * robot_state.GetLength());

  math::geometry::PolylineCurve2d right_hard_boundary(
      std::vector<math::geometry::Point2d>{{0.0, -10.0},
                                           {1.0, -8.0},
                                           {2.0, -6.0},
                                           {3.0, -4.0},
                                           {4.0, -2.0},
                                           {5.0, 0.0},
                                           {6.0, 2.0},
                                           {7.0, 4.0},
                                           {8.0, 6.0},
                                           {9.0, 8.0},
                                           {10.0, 10.0}});
  motion_plan_problem_manager.AppendBoundary(std::move(right_hard_boundary),
                                             RequiredClearance(
                                                 /*critical_clearance=*/0.4,
                                                 /*comfort_clearance=*/0.75),
                                             ViolationTolerance::FORBID,
                                             Boundary::PassSide::LEFT,
                                             ViolationPenaltyLevel::CRITICAL);

  const MotionPlanProblem& motion_plan_problem =
      motion_plan_problem_manager.motion_plan_problem();
  Grid grid(config_);
  grid.Update(motion_plan_problem);

  // Check grid params.
  const GridParams& grid_params = grid.grid_params();
  EXPECT_EQ(grid_params.x_cell_size, 60);
  EXPECT_EQ(grid_params.x_vertices_size, 61);
  EXPECT_EQ(grid_params.x_range.start_pos, -9.9072);
  EXPECT_EQ(grid_params.x_range.end_pos, 20.0928);
  EXPECT_EQ(grid_params.y_cell_size, 70);
  EXPECT_EQ(grid_params.y_vertices_size, 71);
  EXPECT_EQ(grid_params.y_range.start_pos, -9.9072);
  EXPECT_EQ(grid_params.y_range.end_pos, 25.0928);

  // Check grid cells and vertices size.
  const std::vector<GridCell>& grid_cells = grid.grid_cells();
  const std::vector<GridCellVertex>& grid_cell_vertices =
      grid.grid_cell_vertices();
  EXPECT_EQ(grid_cells.size(), 4200);
  EXPECT_EQ(grid_cell_vertices.size(), 4331);

  // Verify heuristic cost samples.
  ASSERT_TRUE(motion_plan_problem.start_pose_info.has_value());
  ASSERT_TRUE(!motion_plan_problem.target_pose_infos.empty());
  const GridCell& ego_pose_grid = grid.GetGridCell(grid.SnapToGrid(
      motion_plan_problem.start_pose_info->start_pose.position()));
  const GridCell& target_pose_grid = grid.GetGridCell(grid.SnapToGrid(
      motion_plan_problem.target_pose_infos.front().target_pose().position()));

  EXPECT_NEAR(target_pose_grid.lower_left().heuristic_dist_to_target, 0.575868,
              math::constants::kEpsilon);
  EXPECT_NEAR(target_pose_grid.lower_right().heuristic_dist_to_target, 0.417641,
              math::constants::kEpsilon);
  EXPECT_NEAR(target_pose_grid.upper_left().heuristic_dist_to_target, 0.417641,
              math::constants::kEpsilon);
  EXPECT_NEAR(target_pose_grid.upper_right().heuristic_dist_to_target, 0.131239,
              math::constants::kEpsilon);

  EXPECT_NEAR(ego_pose_grid.lower_left().heuristic_dist_to_target,
              19.718003386329269, math::constants::kEpsilon);
  EXPECT_NEAR(ego_pose_grid.lower_right().heuristic_dist_to_target,
              19.510896605142722, math::constants::kEpsilon);
  EXPECT_NEAR(ego_pose_grid.upper_left().heuristic_dist_to_target,
              19.218003386329272, math::constants::kEpsilon);
  EXPECT_NEAR(ego_pose_grid.upper_right().heuristic_dist_to_target,
              19.010896605142722, math::constants::kEpsilon);
}

}  // namespace open_space

}  // namespace planner
