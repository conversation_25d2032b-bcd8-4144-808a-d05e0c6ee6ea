load("//bazel:defs.bzl", "voy_cc_test")

package(default_visibility = ["//visibility:public"])

voy_cc_test(
    name = "estimation_debug_encode_test",
    srcs = ["estimation_debug_encode_test.cpp"],
    copts = [
        "-x",
        "c++",
        "-std=c++14",
    ],
    deps = [
        "//offboard/fallback_ads:fallback_l2_simulation",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_debug_type",
        "//offboard/fallback_ads/common_math_library:can_util",
        "//offboard/fallback_ads/debug/estimation_debug_encode",
        "@voy-sdk//:gtest",
    ],
)
