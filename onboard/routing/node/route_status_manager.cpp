#include "routing/node/route_status_manager.h"

#include <algorithm>
#include <cstdint>
#include <set>
#include <string>
#include <utility>
#include <vector>

#include <strings/stringprintf.h>

#include "base/optional_value_accessor.h"
#include "hdmap/lib/geometry_util.h"
#include "hdmap/lib/hdmap.h"
#include "hdmap/lib/point_util.h"
#include "proto_util/proto_compare.h"
#include "routing/engine/engine_utils.h"
#include "routing/engine/route_display_util.h"
#include "routing/engine/route_point_util.h"
#include "routing/engine/utils.h"
#include "routing/node/defines.h"
#include "routing/node/state/utils.h"
#include "routing/node/utils.h"
#include "routing/utility/rt_event_util.h"
#include "routing_protos/constraint_boundary.pb.h"
#include "voy_protos/routing.pb.h"

namespace routing {
namespace {
constexpr double kWaypointArrivalThreshold = 5.0;  // in meters.
constexpr double kRadiusToKeepCostMap = 1000.0;    // in meters.
constexpr double kRadiusToKeepCostMapSqr =
    kRadiusToKeepCostMap * kRadiusToKeepCostMap;               // in meters.
constexpr double kRadiusToKeepLightSuccessorLaneMap = 2000.0;  // in meters.
constexpr double kRadiusToKeepLightCostMapSqr =
    kRadiusToKeepLightSuccessorLaneMap *
    kRadiusToKeepLightSuccessorLaneMap;  // in meters.
constexpr double kMaxDistToDestinationDiffBetweenTwoCycles =
    80.0;  // in meters.
constexpr double kMaxTimeToDestinationDiffBetweenTwoCycles = 10.0;  // in s.
constexpr int64_t kMaxCostMapSizeNoNeedTrim = 5000;
// The max threshold of distance to destination to not display the route of
// current route status.
constexpr double kMaxDistToDestToNotDisplayRouteM = 30.0;
// The max threshold of accumulated distance of pullover to not display the
// route of current route status.
constexpr double kMaxPulloverDistToNotDisplayRouteM = 100.0;

// Updates the cost map to only keep the cost map around current pose within
// |kRadiusToKeepCostMap|, only when replan failed.
void UpdateCostMap(
    const hdmap::HdMap& hdmap,
    const std::optional<CurrentPoseAndLane>& current_pose_info,
    const ::google::protobuf::Map<::google::protobuf::int64,
                                  ::routing::pb::CostToDestination>&
        whole_cost_map,
    pb::RouteSolution* proposed_route_solution) {
  // No update when input params is not ready.
  if (current_pose_info == std::nullopt || whole_cost_map.empty() ||
      proposed_route_solution == nullptr) {
    return;
  }

  // Clear previous cost_map.
  auto* proposed_cost_map = proposed_route_solution->mutable_cost_map();
  proposed_cost_map->clear();

  // Keep cost_map around current pose.
  const hdmap::Point ego_pose_point =
      hdmap::point_util::PoseToHdmapPoint((*current_pose_info).pose);
  for (const auto& cost_pair : whole_cost_map) {
    const hdmap::LaneInfo& lane_info =
        hdmap.GetLaneInfo(cost_pair.second.lane_id());
    const hdmap::Point& anchor_point =
        *(DCHECK_NOTNULL(lane_info.lane)->assistant_line().points().begin());
    if (hdmap::geometry_util::DistanceSqr2D(anchor_point, ego_pose_point) <=
        kRadiusToKeepCostMapSqr) {
      (*proposed_cost_map)[cost_pair.first] = cost_pair.second;
    }
  }
}

// Trims the cost map to only keep the cost map around current pose within
// |kRadiusToKeepCostMap|, only for wide cost map.
void TrimCostMap(const hdmap::HdMap& hdmap,
                 const std::optional<CurrentPoseAndLane>& current_pose_info,
                 int32_t waypoint_count, pb::RouteSolution* route_solution) {
  // No trim when current pose is not ready and narrow cost.
  if (!current_pose_info ||
      !DCHECK_NOTNULL(route_solution)->is_wide_cost_map() ||
      (route_solution->total_dist_m() <= kRadiusToKeepCostMap &&
       route_solution->cost_map().size() <= kMaxCostMapSizeNoNeedTrim)) {
    return;
  }

  const hdmap::Point point =
      hdmap::point_util::PoseToHdmapPoint((*current_pose_info).pose);
  ::google::protobuf::Map<::google::protobuf::int64,
                          ::routing::pb::CostToDestination>* cost_map =
      route_solution->mutable_cost_map();
  auto cost_it = cost_map->begin();
  while (cost_it != cost_map->end()) {
    const hdmap::LaneInfo& lane_info =
        hdmap.GetLaneInfo(cost_it->second.lane_id());
    // If can not find a cost map lane in hdmap, erase it from cost map.
    if (lane_info.lane == nullptr) {
      cost_it = cost_map->erase(cost_it);
      continue;
    }
    const hdmap::Point& anchor_point =
        *(lane_info.lane->assistant_line().points().begin());
    if (hdmap::geometry_util::DistanceSqr2D(anchor_point, point) >
        kRadiusToKeepCostMapSqr) {
      bool is_predecessor_in_cost_map_range = false;
      for (int64_t predecessor_id : lane_info.lane->predecessors()) {
        const int64_t cost_map_id =
            utils::GetCostMapId(predecessor_id, waypoint_count);
        if (cost_map->find(cost_map_id) == cost_map->end()) {
          continue;
        }
        const hdmap::Lane* predecessor =
            DCHECK_NOTNULL(hdmap.GetLanePtrById(predecessor_id));
        const hdmap::Point& predecessor_anchor_point =
            *predecessor->assistant_line().points().begin();
        if (hdmap::geometry_util::DistanceSqr2D(
                predecessor_anchor_point, point) <= kRadiusToKeepCostMapSqr) {
          is_predecessor_in_cost_map_range = true;
          break;
        }
      }
      // When the current lane and all its predecessors are not in cost map
      // range, erase it from cost map. In this way, we also add all successor
      // lanes of exit section in trimmed cost map.
      if (!is_predecessor_in_cost_map_range) {
        cost_it = cost_map->erase(cost_it);
        continue;
      }
    }
    ++cost_it;
  }
}

// Updates the to only keep the light_successor_lane_map around current pose
// within |kRadiusToKeepLightSuccessorLaneMap|, only when replan failed.
void UpdateLightSuccessorLaneMap(
    const hdmap::HdMap& hdmap,
    const std::optional<CurrentPoseAndLane>& current_pose_info,
    const ::google::protobuf::Map<::google::protobuf::int64,
                                  ::google::protobuf::int64>&
        whole_light_successor_lane_map,
    pb::RouteSolution* proposed_route_solution) {
  // No update when input params is not ready.
  if (current_pose_info == std::nullopt ||
      whole_light_successor_lane_map.empty() ||
      proposed_route_solution == nullptr) {
    return;
  }

  // Clear previous light_successor_lane_map.
  auto* proposed_light_successor_lane_map =
      proposed_route_solution->mutable_light_successor_lane_map();
  proposed_light_successor_lane_map->clear();

  // Keep light_successor_lane_map around current pose.
  const hdmap::Point ego_pose_point =
      hdmap::point_util::PoseToHdmapPoint((*current_pose_info).pose);
  for (const auto& cost_pair : whole_light_successor_lane_map) {
    const int64_t lane_id =
        utils::DecodeAndGetLaneIdFromLaneNodeId(cost_pair.first);
    const hdmap::LaneInfo& lane_info = hdmap.GetLaneInfo(lane_id);
    const hdmap::Point& anchor_point =
        *(DCHECK_NOTNULL(lane_info.lane)->assistant_line().points().begin());
    if (hdmap::geometry_util::DistanceSqr2D(anchor_point, ego_pose_point) <=
        kRadiusToKeepLightCostMapSqr) {
      (*proposed_light_successor_lane_map)[cost_pair.first] = cost_pair.second;
    }
  }
}

// Trims to only keep the light_successor_lane_map around current pose within
// |kRadiusToKeepLightSuccessorLaneMap|, only for wide cost map.
void TrimLightSuccessorLaneMap(
    const hdmap::HdMap& hdmap,
    const std::optional<CurrentPoseAndLane>& current_pose_info,
    int32_t waypoint_count, pb::RouteSolution* route_solution) {
  // No trim when current pose is not ready and narrow cost.
  if (!current_pose_info ||
      !DCHECK_NOTNULL(route_solution)->is_wide_cost_map() ||
      route_solution->total_dist_m() <= kRadiusToKeepLightSuccessorLaneMap) {
    return;
  }

  const hdmap::Point point =
      hdmap::point_util::PoseToHdmapPoint((*current_pose_info).pose);
  auto* mutable_light_successor_lane_map =
      route_solution->mutable_light_successor_lane_map();
  auto cost_it = mutable_light_successor_lane_map->begin();
  while (cost_it != mutable_light_successor_lane_map->end()) {
    const int64_t lane_id =
        utils::DecodeAndGetLaneIdFromLaneNodeId(cost_it->first);
    const hdmap::LaneInfo& lane_info = hdmap.GetLaneInfo(lane_id);
    // If can not find lane in hdmap, erase it.
    if (lane_info.lane == nullptr) {
      cost_it = mutable_light_successor_lane_map->erase(cost_it);
      continue;
    }
    const hdmap::Point& anchor_point =
        *(lane_info.lane->assistant_line().points().begin());
    if (hdmap::geometry_util::DistanceSqr2D(anchor_point, point) >
        kRadiusToKeepLightCostMapSqr) {
      bool is_predecessor_in_cost_map_range = false;
      for (int64_t predecessor_id : lane_info.lane->predecessors()) {
        const int64_t cost_map_id =
            utils::GetCostMapId(predecessor_id, waypoint_count);
        if (mutable_light_successor_lane_map->find(cost_map_id) ==
            mutable_light_successor_lane_map->end()) {
          continue;
        }
        const hdmap::Lane* predecessor =
            DCHECK_NOTNULL(hdmap.GetLanePtrById(predecessor_id));
        const hdmap::Point& predecessor_anchor_point =
            *predecessor->assistant_line().points().begin();
        if (hdmap::geometry_util::DistanceSqr2D(predecessor_anchor_point,
                                                point) <=
            kRadiusToKeepLightCostMapSqr) {
          is_predecessor_in_cost_map_range = true;
          break;
        }
      }
      // When the current lane and all its predecessors are not in map range,
      // erase it. In this way, we also add all successor lanes of exit section
      // in trimmed cost map.
      if (!is_predecessor_in_cost_map_range) {
        cost_it = mutable_light_successor_lane_map->erase(cost_it);
        continue;
      }
    }
    ++cost_it;
  }
}

voy::RouteDivergenceWarning GetRouteDivergenceWarning(
    planner::pb::SectionSequenceState section_sequence_state) {
  switch (section_sequence_state) {
    case planner::pb::kUnknownState:
    case planner::pb::kNoRouteState:
    case planner::pb::kFirstSequenceState:
    case planner::pb::kNormalState:
      return voy::RouteDivergenceWarning::kNormal;
    case planner::pb::kInfeasibleSequenceState:
      return voy::RouteDivergenceWarning::kInfeasibleRoute;
    case planner::pb::kDivergedState:
      return voy::RouteDivergenceWarning::kDiverged;
    default:
      DCHECK(false) << "Invalid section sequence state type: "
                    << planner::pb::SectionSequenceState_Name(
                           section_sequence_state);
  }
  return voy::RouteDivergenceWarning::kNormal;
}

// Adds |section_id| to
// arrived_section_ids_from_current_route_point in route status.
void AddArrivedSectionAndRoadId(int64_t section_id, int64_t road_id,
                                pb::RouteStatus* route_status) {
  ::google::protobuf::RepeatedField<int64_t>* arrived_section_ids =
      DCHECK_NOTNULL(route_status)
          ->mutable_arrived_section_ids_from_current_route_point();
  const bool need_to_add = arrived_section_ids->empty() ||
                           (*arrived_section_ids->rbegin()) != section_id;
  if (need_to_add) {
    arrived_section_ids->Add(section_id);
  }

  ::google::protobuf::RepeatedField<int64_t>* arrived_road_ids =
      route_status->mutable_arrived_road_ids_from_ride_start();
  const bool need_to_add_road =
      arrived_road_ids->empty() || (*arrived_road_ids->rbegin()) != road_id;
  if (need_to_add_road) {
    arrived_road_ids->Add(road_id);
  }
}

// Returns true if pose just passes by the next waypoint.
// |route_status| arrived section ids must be updated already.
// route_status.waypoint_count() has the value before this pose.
bool PassedWaypoint(const pb::RouteStatus& route_status,
                    const hdmap::LaneInfo& pose_lane_info,
                    double pose_percentage_along_lane) {
  const int32_t current_waypoint_count = route_status.waypoint_count();
  const pb::RouteSolution& route_solution =
      route_status.current_route_solution();
  const auto& waypoint_infos = route_solution.waypoint_infos();
  if (waypoint_infos.empty() ||
      current_waypoint_count >= waypoint_infos.size()) {
    return false;
  }
  const auto& waypoint_info = waypoint_infos.Get(current_waypoint_count);
  if (waypoint_info.waypoint().lane_candidates().empty()) {
    DCHECK(!waypoint_info.waypoint().lane_candidates().empty());
    return false;
  }
  const pb::RoutePoint& prev_route_point =
      (current_waypoint_count > 0)
          ? waypoint_infos.Get(current_waypoint_count - 1).waypoint()
          : route_solution.origin();
  if (prev_route_point.lane_candidates().empty()) {
    DCHECK(!prev_route_point.lane_candidates().empty());
    return false;
  }

  const pb::LaneCandidate& origin_lane_candidate =
      prev_route_point.lane_candidates(0);
  bool left_origin_section = false;
  for (int64_t arrived_section_id :
       route_status.arrived_section_ids_from_current_route_point()) {
    if (arrived_section_id != origin_lane_candidate.section_id()) {
      left_origin_section = true;
      break;
    }
  }

  const pb::LaneCandidate& waypoint_lane_candidate =
      waypoint_info.waypoint().lane_candidates(0);
  if (origin_lane_candidate.percentage_along_lane() >
          waypoint_lane_candidate.percentage_along_lane() &&
      origin_lane_candidate.section_id() ==
          waypoint_lane_candidate.section_id() &&
      !left_origin_section) {
    // prev route point has larger percent along lane and both points are on the
    // same section.
    // Must left origin section to pass the next waypoint, when origin has
    // larger percent along lane.
    return false;
  }

  for (const auto& lane_candidate :
       waypoint_info.waypoint().lane_candidates()) {
    if (pose_lane_info.section_id != lane_candidate.section_id()) {
      continue;
    }
    // Pose passed by the waypoint.
    if (pose_percentage_along_lane > lane_candidate.percentage_along_lane()) {
      return true;
    }
  }
  return false;
}

// Returns route lane ids associated with construction zones.
std::set<int64_t> GetRouteLaneIdsAssociatedWithCZ(
    const ::google::protobuf::RepeatedPtrField<pb::RouteLane>& route_lanes,
    const ::google::protobuf::RepeatedField<::google::protobuf::int64>&
        lane_ids_associated_with_cz) {
  std::set<int64_t> route_lane_ids_associated_with_cz;
  for (const pb::RouteLane& route_lane : route_lanes) {
    const int64_t route_lane_id = route_lane.lane_id();
    if (std::any_of(lane_ids_associated_with_cz.begin(),
                    lane_ids_associated_with_cz.end(),
                    [route_lane_id](const int64_t lane_id) {
                      return route_lane_id == lane_id;
                    })) {
      route_lane_ids_associated_with_cz.insert(route_lane_id);
    }
  }
  return route_lane_ids_associated_with_cz;
}

// Posts rt event for route through construction zones about proposed route
// solution.
void PostRTEventForThroughConstructionZonesRoute(
    const pb::RouteStatus& route_status) {
  const std::set<int64_t> route_lane_ids_associated_with_cz =
      GetRouteLaneIdsAssociatedWithCZ(
          route_status.proposed_route_solution().route_lanes(),
          route_status.construction_zones_info()
              .construction_zone_associated_lane_ids());
  if (route_lane_ids_associated_with_cz.empty()) {
    return;
  }

  std::string rt_event_info =
      "Global proposed route goes through Construction Zones. Associated "
      "lane ids are [";
  size_t i = 0;
  for (const int64 lane_id : route_lane_ids_associated_with_cz) {
    rt_event_info += strings::StringPrintf("%ld", lane_id);
    if (i + 1 != route_lane_ids_associated_with_cz.size()) {
      rt_event_info += ",";
    }
    i++;
  }
  rt_event_info += "]";
  rt_event::PostRtEvent<rt_event::routing::GlobalThroughConstructionZonesRoute>(
      strings::StringPrintf(
          "%s, route query time is %ld.", rt_event_info.c_str(),
          route_status.proposed_route_query().query_timestamp()));
}

// Returns true if planned route have any roads in |arrived_road_ids|.
bool IsLoopRoute(
    const ::google::protobuf::RepeatedPtrField<pb::RouteLane>& route_lanes,
    const ::google::protobuf::RepeatedField<::google::protobuf::int64>&
        arrived_road_ids) {
  if (route_lanes.size() <= 1) {
    return false;
  }
  int64_t checked_road_id = route_lanes.at(0).road_id();
  for (const pb::RouteLane& route_lane : route_lanes) {
    const int64_t route_road_id = route_lane.road_id();
    // Skip checked road id.
    if (route_road_id == checked_road_id) {
      continue;
    }
    checked_road_id = route_road_id;
    if (std::any_of(arrived_road_ids.begin(), arrived_road_ids.end(),
                    [route_road_id](const int64_t arrived_road_id) {
                      return route_road_id == arrived_road_id;
                    })) {
      return true;
    }
  }
  return false;
}

// Returns true if the route has changed.
bool IsRouteChanged(const pb::RouteSolution& old_route_solution,
                    const RouteInfo& new_route_info) {
  if (old_route_solution.mission_id() !=
      new_route_info.route_solution.mission_id()) {
    // The mission changed, we regard it is a new route.
    return true;
  }
  // The mission ID has not changed, we check whether the road sequence is the
  // same.
  const auto& old_route_road_ids = old_route_solution.route_road_ids();
  const auto& new_route_road_ids =
      new_route_info.route_solution.route_road_ids();
  if (old_route_road_ids.empty() && new_route_road_ids.empty()) {
    // If both the new and old route are empty, we should not regard the route
    // has changed.
    return false;
  }
  if (old_route_road_ids.empty() || new_route_road_ids.empty()) {
    // The route change from empty to non-empty, or non-empty to empty.
    return true;
  }

  const auto new_start_road_id = *new_route_road_ids.begin();
  const auto new_start_at_old_it = std::find(
      old_route_road_ids.begin(), old_route_road_ids.end(), new_start_road_id);

  if (new_start_at_old_it == old_route_road_ids.end()) {
    // The start of new route is not in the old route, the two routes are not
    // the same.
    return true;
  }

  const auto [mismatch_it_old, mismatch_it_new] =
      std::mismatch(new_start_at_old_it, old_route_road_ids.end(),
                    new_route_road_ids.begin(), new_route_road_ids.end());

  // The road sequence after the projection on the old route point should be
  // contained in the new route.
  return !(mismatch_it_old == old_route_road_ids.end() &&
           mismatch_it_new == new_route_road_ids.end());
}

// Updates the route meta information.
// Note: The |route_meta| is the meta data in the last frame, it is
// corresponding with the |last_route_solution|. Please be careful to update it.
void UpdateRouteMeta(const hdmap::HdMap& hdmap,
                     const pb::RouteSolution& last_route_solution,
                     const RouteInfo& new_route_info,
                     pb::RouteMeta* route_meta) {
  if (route_meta == nullptr) {
    return;
  }
  const bool is_route_changed =
      IsRouteChanged(last_route_solution, new_route_info);
  if (!is_route_changed) {
    // The route is not changed, we do not need to update the route meta
    // information.
    return;
  }
  route_meta->set_region(hdmap::util::GetMapRegion());
  route_meta->set_geometry_version(
      hdmap.GetHdMapVersion(hdmap::MapLayer::GEOMETRY_LAYER));
  auto& current_route_meta = *route_meta->mutable_current_route_meta();
  // The route has changed.
  // 0. Update the last route meta.
  route_meta->mutable_last_route_meta()->CopyFrom(
      route_meta->current_route_meta());
  // 1. Update current route meta data.
  current_route_meta.Clear();
  const std::optional<std::string> car_id = node_utils::GetCarId();
  const std::string route_id = node_utils::GetRouteIdByRouteTimestamp(
      car_id, new_route_info.route_solution.mission_id(),
      new_route_info.route_query_info.timestamp);
  current_route_meta.set_route_id(route_id);

  // 2. Update route_change_info.
  pb::RouteChangeInfo& route_change_info =
      *route_meta->mutable_route_change_info();
  route_change_info.set_is_route_change(true);
  // Clear last change reasons before update it.
  route_change_info.clear_change_reasons();
  route_change_info.clear_active_reroute_factors();
  const auto& current_route_query =
      new_route_info.route_query_info.current_route_query;
  if (current_route_query == std::nullopt) {
    LOG(ERROR) << "Current rout query is nullopt!";
    route_change_info.add_change_reasons(
        pb::RouteChangeInfo::kUnknownRouteChangeReason);
    return;
  }
  if (!new_route_info.route_query_info.is_replanning_query) {
    route_change_info.add_change_reasons(
        pb::RouteChangeInfo::kRouteChangeForNewRideRideQuery);
  }
  if (current_route_query->replan_purpose() ==
      pb::ReplanPurpose::DESIRED_REPLAN_FOR_NEW_ROUTE) {
    route_change_info.add_change_reasons(
        pb::RouteChangeInfo::kRouteChangeForRerouteForGlobalRouteUnPassable);
    route_change_info.mutable_active_reroute_factors()->CopyFrom(
        current_route_query->desired_route_request().active_reroute_factors());
  }
  if (route_change_info.change_reasons().empty()) {
    route_change_info.add_change_reasons(
        pb::RouteChangeInfo::kUnknownRouteChangeReason);
  }
  // Add global route change rt_event.
  std::ostringstream rt_event_info;
  rt_event_info << "route_id: " << route_id << "_"
                << "route_change_reasons: ";
  for (const auto reason : route_change_info.change_reasons()) {
    rt_event_info << pb::RouteChangeInfo::RouteChangeReason_Name(reason) << "_";
  }
  rt_event_info << "planner_active_reroute_factors: ";
  for (const auto factor : route_change_info.active_reroute_factors()) {
    rt_event_info << pb::PlannerActiveRerouteFactors_Name(factor) << "_";
  }
  rt_event::PostRtEvent<rt_event::routing::GlobalRouteChanged>(
      rt_event_info.str());
}

}  // namespace

void RouteStatusManager::DumpState(pb::RouteStatusManagerState* to_pb) const {
  if (!to_pb) {
    return;
  }
  to_pb->mutable_current_route_status()->CopyFrom(current_route_status_);
  to_pb->mutable_lane_sequence()->CopyFrom(lane_sequence_);
}

void RouteStatusManager::LoadState(const pb::RouteStatusManagerState& from_pb) {
  current_route_status_.CopyFrom(from_pb.current_route_status());
  lane_sequence_.CopyFrom(from_pb.lane_sequence());
}

void RouteStatusManager::UpdateRouteStatusDistanceAndTime(
    double distance_to_route_end, double time_to_route_end) {
  const double last_dist_to_dest_m = current_route_status_.dist_to_dest_m();
  const double last_time_to_dest_s = current_route_status_.time_to_dest_s();
  current_route_status_.set_dist_to_dest_m(distance_to_route_end);
  current_route_status_.set_time_to_dest_s(time_to_route_end);
  if (current_route_status_.proposed_route_solution().mission_id() !=
      current_route_status_.current_route_solution().mission_id()) {
    return;
  }

  utility::AddRtEventForEDAAndETA(current_route_status_);

  if (last_dist_to_dest_m - distance_to_route_end >=
      kMaxDistToDestinationDiffBetweenTwoCycles) {
    rt_event::PostRtEvent<rt_event::routing::GlobalDistToDestinationDeviation>(
        strings::StringPrintf("Distance to destination has deviation. Last one "
                              "is %f, the new is %f.",
                              last_dist_to_dest_m, distance_to_route_end));
  }
  if (last_time_to_dest_s - time_to_route_end >=
      kMaxTimeToDestinationDiffBetweenTwoCycles) {
    rt_event::PostRtEvent<rt_event::routing::GlobalTimeToDestinationDeviation>(
        strings::StringPrintf(
            "Time to destination has deviation. Last one is %f, the new is %f.",
            last_time_to_dest_s, time_to_route_end));
  }
}

bool RouteStatusManager::UpdateRouteStatusDistanceAndTime(
    const pb::RouteSolution& route_solution, int waypoint_count) {
  const double last_dist_to_dest_m = current_route_status_.dist_to_dest_m();
  const double last_time_to_dest_s = current_route_status_.time_to_dest_s();
  current_route_status_.set_dist_to_dest_m(0.0);
  current_route_status_.set_time_to_dest_s(0.0);
  if (route_solution.success() && ego_car_status_) {
    std::optional<pb::RoutePoint> pose_route_point =
        ego_car_status_->GetEgoOrigin();
    if (pose_route_point) {
      if (pose_route_point->lane_candidates().empty()) {
        // We only want to fill lane ids, speed does not matter.
        utility::ValidateAndFillLaneCandidatesForOrigin(
            /*speed_for_path_point_meter_per_sec=*/1.0,
            routing_engine_->routing_map(), &(*pose_route_point));
        if (pose_route_point->lane_candidates().empty()) {
          return false;
        }
      }
      const std::optional<std::pair<double, double>> dist_time =
          routing::engine_utils::GetDistAndTimeToDest(
              *pose_route_point, waypoint_count, route_solution,
              routing_engine_->routing_map());
      if (dist_time) {
        UpdateRouteStatusDistanceAndTime(dist_time->first, dist_time->second);
        return true;
      }
      // If GetDistAndTimeToDest failed, we will use last cycle's dist_and_time.
      UpdateRouteStatusDistanceAndTime(last_dist_to_dest_m,
                                       last_time_to_dest_s);
    }
  }
  return false;
}

void RouteStatusManager::UpdateConstructionZonesInfo(
    const DynamicMapElementInfo& dynamic_map_element_info) {
  const int64_t incoming_construction_zone_timestamp =
      dynamic_map_element_info.current_timestamp_for_construction_zone;
  const int64_t incoming_map_change_area_timestamp =
      dynamic_map_element_info.current_timestamp_for_map_change_area;
  if (current_route_status_.construction_zones_info()
              .construction_zone_updated_timestamp() ==
          incoming_construction_zone_timestamp &&
      current_route_status_.construction_zones_info()
              .map_change_area_update_timestamp() ==
          incoming_map_change_area_timestamp) {
    return;
  }
  const std::set<int64_t>& incoming_construction_zones_associated_lane_ids =
      dynamic_map_element_info.dynamic_zone_associated_lanes;
  auto* new_construction_zones_info =
      current_route_status_.mutable_construction_zones_info();
  new_construction_zones_info->set_construction_zone_updated_timestamp(
      incoming_construction_zone_timestamp);
  new_construction_zones_info->set_map_change_area_update_timestamp(
      incoming_map_change_area_timestamp);
  auto* construction_zones_associated_lane_ids =
      new_construction_zones_info
          ->mutable_construction_zone_associated_lane_ids();
  construction_zones_associated_lane_ids->Reserve(
      incoming_construction_zones_associated_lane_ids.size());
  for (const int64_t associated_lane_id :
       incoming_construction_zones_associated_lane_ids) {
    construction_zones_associated_lane_ids->Add(associated_lane_id);
  }
}

void RouteStatusManager::UpdateBrokenTrafficLightsInfo(
    const BrokenTrafficLightsInfo& broken_traffic_lights_info) {
  const int64_t incoming_timestamp =
      broken_traffic_lights_info.current_timestamp;
  if (current_route_status_.broken_traffic_lights_info().updated_timestamp() ==
      incoming_timestamp) {
    return;
  }

  auto* new_broken_traffic_lights_info =
      current_route_status_.mutable_broken_traffic_lights_info();
  new_broken_traffic_lights_info->set_has_broken_traffic_lights(
      broken_traffic_lights_info.has_broken_traffic_lights);
  new_broken_traffic_lights_info->set_updated_timestamp(incoming_timestamp);

  const std::set<int64_t>& incoming_associated_lane_ids =
      broken_traffic_lights_info.broken_traffic_lights_associated_lanes;
  new_broken_traffic_lights_info->clear_broken_light_associated_lane_ids();
  auto* broken_light_associated_lane_ids =
      new_broken_traffic_lights_info
          ->mutable_broken_light_associated_lane_ids();
  broken_light_associated_lane_ids->Reserve(
      incoming_associated_lane_ids.size());
  for (const int64_t associated_lane_id : incoming_associated_lane_ids) {
    broken_light_associated_lane_ids->Add(associated_lane_id);
  }
}

void RouteStatusManager::UpdateConstraintInfo(const RouteInfo& route_info) {
  if (current_route_status_.constraint_info().constraint_name().compare(
          route_info.constraint_info.constraint_name()) == 0) {
    return;
  }

  current_route_status_.mutable_constraint_info()->CopyFrom(
      route_info.constraint_info);
}

void RouteStatusManager::UpdateCurrentRouteStatus(const RouteInfo& route_info,
                                                  bool is_path_rejected) {
  UpdateRouteMeta(CHECK_NOTNULL(routing_engine_)->hdmap(),
                  current_route_status_.current_route_solution(), route_info,
                  current_route_status_.mutable_route_meta_data());
  current_route_status_.set_is_valid(false);
  current_route_status_.set_route_display_state(
      routing::pb::RouteDisplayState::kDisplay);
  UpdateCurrentRouteSolution(route_info.route_solution, is_path_rejected);
  current_route_status_.set_waypoint_count(
      route_info.route_query_info.waypoint_count);  // Init the waypoint count.
  current_route_status_.set_source_of_query(
      route_info.route_query_info.source_of_query);
  *current_route_status_.mutable_ride_route_query() =
      route_info.route_query_info.original_ride_route_query;
  if (route_info.route_query_info.current_route_query) {
    *current_route_status_.mutable_current_route_query() =
        *route_info.route_query_info.current_route_query;
  } else {
    current_route_status_.clear_current_route_query();
  }
  current_route_status_.set_is_replanning_query(
      route_info.route_query_info.is_replanning_query);

  current_route_status_.clear_upcoming_waypoints();
  // Get waypoints from current route solution.
  for (const pb::WaypointInfo& waypoint_info :
       route_info.route_solution.waypoint_infos()) {
    if (!waypoint_info.is_on_route()) {
      continue;
    }
    pb::UpcomingWaypoint upcoming_waypoint;
    pb::WaypointInfo* new_waypoint_info =
        upcoming_waypoint.mutable_waypoint_info();
    *new_waypoint_info = waypoint_info;
    new_waypoint_info->set_dist_to_dest_m(waypoint_info.dist_to_dest_m());
    new_waypoint_info->set_time_to_dest_s(waypoint_info.time_to_dest_s());
    *current_route_status_.add_upcoming_waypoints() = upcoming_waypoint;
  }

  UpdateRouteStatusDistanceAndTime(
      current_route_status_.current_route_solution(),
      current_route_status_.waypoint_count());

  UpdateConstructionZonesInfo(
      routing_engine_->routing_map()->dynamic_map_element_info());

  UpdateBrokenTrafficLightsInfo(routing_engine_->cost_generator()
                                    .blockage_cost_estimator()
                                    .broken_traffic_lights_info());

  UpdateConstraintInfo(route_info);

  const std::optional<CurrentPoseAndLane> current_pose_info =
      ego_car_status_->GetCurrentPoseAndLane();
  TrimCostMap(routing_engine_->hdmap(), current_pose_info,
              current_route_status_.waypoint_count(),
              current_route_status_.mutable_current_route_solution());
  TrimLightSuccessorLaneMap(
      routing_engine_->hdmap(), current_pose_info,
      current_route_status_.waypoint_count(),
      current_route_status_.mutable_current_route_solution());
  const bool is_empty_route =
      current_route_status_.current_route_solution().route_lanes().empty();
  if (!is_empty_route) {
    const auto origin_lane_idx = utility::GetOriginLaneIndexOfRouteLanes(
        current_route_status_.current_route_solution());
    // For replan route, we don't display route points if ego is off route,
    // although it is not likely.
    if (!route_info.route_query_info.is_replanning_query ||
        origin_lane_idx != std::nullopt) {
      ::google::protobuf::RepeatedPtrField<voy::Point2d> route_points =
          utility::GenerateRouteDisplayPoints(
              current_route_status_.current_route_solution(), routing_engine_,
              origin_lane_idx);
      current_route_status_.mutable_current_display_route()->Swap(
          &route_points);
    }
  } else {
    current_route_status_.clear_current_display_route();
  }

  // Copy the arrived section ids, only available for the route from seed.
  if (route_info.route_query_info
          .arrived_section_ids_from_current_route_point) {
    *current_route_status_
         .mutable_arrived_section_ids_from_current_route_point() =
        *(route_info.route_query_info
              .arrived_section_ids_from_current_route_point);
  } else {
    current_route_status_.clear_arrived_section_ids_from_current_route_point();
  }

  // Copy ride_start_pose.
  if (route_info.route_query_info.ride_start_pose) {
    *current_route_status_.mutable_ride_start_pose() =
        *(route_info.route_query_info.ride_start_pose);
  } else {
    current_route_status_.clear_ride_start_pose();
  }

  // Copy the arrived road ids.
  if (route_info.route_query_info.arrived_road_ids_from_ride_start) {
    *current_route_status_.mutable_arrived_road_ids_from_ride_start() =
        *(route_info.route_query_info.arrived_road_ids_from_ride_start);
  } else {
    current_route_status_.clear_arrived_road_ids_from_ride_start();
  }

  if (is_empty_route) {
    return;
  }

  const std::optional<pb::RoutePoint> pose_route_point =
      ego_car_status_->GetEgoOrigin();
  if (pose_route_point) {
    UpdateArrivedSectionIdsAndWaypointCount(*pose_route_point);
  }
}

void RouteStatusManager::UpdateCurrentRouteSolution(
    const pb::RouteSolution& route_solution, bool is_path_rejected) {
  ::google::protobuf::RepeatedPtrField<pb::RouteLane> last_route_lanes;
  pb::RouteCostFactors last_route_cost_factors;
  const bool are_route_solution_for_same_query =
      route_solution.mission_id() ==
          current_route_status_.current_route_solution().mission_id() &&
      route_solution.name() ==
          current_route_status_.current_route_solution().name() &&
      proto_util::Equals(
          route_solution.destination(),
          current_route_status_.current_route_solution().destination());
  if (is_path_rejected && !are_route_solution_for_same_query) {
    utility::AddRtEventForRouteStatusManagerHasIllegalRejectingPath();
  }
  if (is_path_rejected && are_route_solution_for_same_query) {
    last_route_lanes =
        current_route_status_.current_route_solution().route_lanes();
    last_route_cost_factors =
        current_route_status_.current_route_solution().route_cost_factors();
  }
  *current_route_status_.mutable_current_route_solution() = route_solution;
  if (is_path_rejected && are_route_solution_for_same_query) {
    current_route_status_.mutable_current_route_solution()
        ->mutable_route_lanes()
        ->CopyFrom(last_route_lanes);
    current_route_status_.mutable_current_route_solution()
        ->mutable_route_cost_factors()
        ->CopyFrom(last_route_cost_factors);
    current_route_status_.mutable_current_route_solution()
        ->set_route_lanes_source(pb::RouteLanesSource::kLastRouteSolution);
  }
}

bool RouteStatusManager::IsPreparedToProposeCostMap(
    const RouteInfo& proposed_route_info,
    pb::ProposingReason proposing_reason) const {
  if (proposing_reason != pb::ProposingReason::kReplanning) {
    return false;
  }
  if (untrimmed_proposed_route_solution_ == std::nullopt) {
    return false;
  }
  const auto& proposed_route_solution_in = proposed_route_info.route_solution;
  const auto& last_proposed_route_solution =
      current_route_status_.proposed_route_solution();
  if (last_proposed_route_solution.mission_id() !=
          proposed_route_solution_in.mission_id() ||
      !proto_util::Equals(last_proposed_route_solution.destination(),
                          proposed_route_solution_in.destination()) ||
      last_proposed_route_solution.is_wide_cost_map() !=
          proposed_route_solution_in.is_wide_cost_map()) {
    return false;
  }
  return true;
}

void RouteStatusManager::UpdateProposedRouteSolution(
    const RouteInfo& proposed_route_info,
    const pb::ProposingReason proposing_reason,
    bool should_planner_trigger_mrc) {
  const auto& proposed_route_solution_in = proposed_route_info.route_solution;
  // If proposed route solution is not successful, we will make use of last
  // proposed route solution in current_route_status and failed input proposed
  // route solution to create a new one for planner.
  if (proposed_route_solution_in.success() ||
      proposing_reason == pb::ProposingReason::kCompletion ||
      proposing_reason == pb::ProposingReason::kCancellation) {
    *current_route_status_.mutable_proposed_route_solution() =
        proposed_route_solution_in;
  } else {
    auto* last_proposed_route_solution =
        current_route_status_.mutable_proposed_route_solution();
    // [NOTE]: Set true when this route_solution is human-made.
    last_proposed_route_solution->set_success(true);
    // [NOTE]: Set is_astar_searched false When replan failed and need propose a
    // human-made route solution.
    last_proposed_route_solution->set_is_astar_searched(false);
    last_proposed_route_solution->set_name(proposed_route_solution_in.name());
    last_proposed_route_solution->set_query_timestamp(
        proposed_route_solution_in.query_timestamp());
    last_proposed_route_solution->set_has_construction_zones(
        proposed_route_solution_in.has_construction_zones());
    last_proposed_route_solution->mutable_origin()->CopyFrom(
        proposed_route_solution_in.origin());
    last_proposed_route_solution->mutable_destination()->CopyFrom(
        proposed_route_solution_in.destination());
    last_proposed_route_solution->mutable_astar_search_debug()->CopyFrom(
        proposed_route_solution_in.astar_search_debug());
  }

  // Update selection meta between cloud route and onboard route.
  current_route_status_.clear_cloud_route_selection_meta();
  if (proposed_route_info.route_solution.success() &&
      proposed_route_info.cloud_route_selection_meta.has_value()) {
    current_route_status_.mutable_cloud_route_selection_meta()->CopyFrom(
        *proposed_route_info.cloud_route_selection_meta);
  }

  *current_route_status_.mutable_proposed_route_query() =
      base::CheckAndGetValue(
          proposed_route_info.route_query_info.current_route_query);
  current_route_status_.set_proposed_waypoint_count(
      proposed_route_info.route_query_info.waypoint_count);

  const std::optional<CurrentPoseAndLane> current_pose_info =
      ego_car_status_->GetCurrentPoseAndLane();
  int64_t pose_road_id = 0;
  // Set the arrived section ids.
  if (proposed_route_info.route_query_info
          .arrived_section_ids_from_current_route_point) {
    *current_route_status_
         .mutable_proposed_arrived_section_ids_from_current_route_point() =
        *(proposed_route_info.route_query_info
              .arrived_section_ids_from_current_route_point);
  } else {
    current_route_status_
        .clear_proposed_arrived_section_ids_from_current_route_point();
    if (current_pose_info) {
      const std::optional<hdmap::LaneInfo> lane_info =
          state::utils::GetPoseLaneInfo(routing_engine_->hdmap(),
                                        *current_pose_info);
      if (lane_info) {
        current_route_status_
            .add_proposed_arrived_section_ids_from_current_route_point(
                lane_info->section_id);
        pose_road_id = lane_info->road_id;
      }
    }
  }

  // Set the arrived road ids.
  if (proposed_route_info.route_query_info.arrived_road_ids_from_ride_start) {
    *current_route_status_.mutable_proposed_arrived_road_ids_from_ride_start() =
        *(proposed_route_info.route_query_info
              .arrived_road_ids_from_ride_start);
  } else {
    current_route_status_.clear_proposed_arrived_road_ids_from_ride_start();
    if (pose_road_id > 0) {
      current_route_status_.add_proposed_arrived_road_ids_from_ride_start(
          pose_road_id);
    }
  }

  // Set the ride_start_pose.
  if (proposed_route_info.route_query_info.ride_start_pose) {
    *current_route_status_.mutable_proposed_ride_start_pose() =
        *(proposed_route_info.route_query_info.ride_start_pose);
  } else {
    *current_route_status_.mutable_proposed_ride_start_pose() =
        current_route_status_.current_pose();
  }

  current_route_status_.set_proposing_reason(proposing_reason);

  // If routing detects consecutive search failures with unknown reasons, pass
  // this info to planner node to trigger mrc.
  if (should_planner_trigger_mrc) {
    current_route_status_.set_special_planner_response_request(
        pb::kTriggerMRCDueToRoutingFailure);
  } else if (current_route_status_.special_planner_response_request() ==
             pb::kTriggerMRCDueToRoutingFailure) {
    current_route_status_.set_special_planner_response_request(
        pb::kNoSpecialResponseRequest);
  }

  if (proposed_route_info.route_solution.success() ||
      proposing_reason == pb::ProposingReason::kCompletion ||
      proposing_reason == pb::ProposingReason::kCancellation) {
    untrimmed_proposed_route_solution_ = proposed_route_info.route_solution;
    TrimCostMap(routing_engine_->hdmap(), current_pose_info,
                current_route_status_.proposed_waypoint_count(),
                current_route_status_.mutable_proposed_route_solution());
    TrimLightSuccessorLaneMap(
        routing_engine_->hdmap(), current_pose_info,
        current_route_status_.proposed_waypoint_count(),
        current_route_status_.mutable_proposed_route_solution());
  } else if (untrimmed_proposed_route_solution_ != std::nullopt) {
    UpdateCostMap(routing_engine_->hdmap(), current_pose_info,
                  untrimmed_proposed_route_solution_->cost_map(),
                  current_route_status_.mutable_proposed_route_solution());
    UpdateLightSuccessorLaneMap(
        routing_engine_->hdmap(), current_pose_info,
        untrimmed_proposed_route_solution_->light_successor_lane_map(),
        current_route_status_.mutable_proposed_route_solution());
  }

  // Post rt event for route through construction zones about proposed route
  // solution.
  PostRTEventForThroughConstructionZonesRoute(current_route_status_);
  if (current_route_status_.proposed_route_solution().mission_id() !=
      current_route_status_.current_route_solution().mission_id()) {
    return;
  }

  const routing::pb::RouteQuery& proposed_route_query =
      current_route_status_.proposed_route_query();
  // Check if current route solution is a not required loop route:
  // 1. Has valid route solution
  // 2. Not a predefined route.
  // 3. Command does not query a loop route.
  // Post rt event for loop route about proposed route solution.
  if (current_route_status_.proposed_route_solution().success() &&
      !(current_route_status_.proposed_waypoint_count() > 0 &&
        proposed_route_query.is_loop_route()) &&
      proposed_route_query.predefined_route_roads().empty() &&
      IsLoopRoute(
          current_route_status_.proposed_route_solution().route_lanes(),
          current_route_status_.proposed_arrived_road_ids_from_ride_start())) {
    current_route_status_.mutable_proposed_route_solution()
        ->set_is_loopback_route(true);
    rt_event::PostRtEvent<rt_event::routing::GlobalLoopRoute>(
        strings::StringPrintf(
            "Global proposed route is loop checked by arrived road ids from "
            "ride start, route query time is %ld.",
            proposed_route_query.query_timestamp()));
  }
}

bool RouteStatusManager::UpdateLaneSequence(
    const planner::pb::PlanningLaneSequence& lane_sequence,
    const pb::RoutePoint& pose_route_point) {
  if (lane_sequence.current_lane_ids().empty()) {
    return false;
  }

  lane_sequence_ = lane_sequence;
  current_route_status_.set_mission_id_for_local_route(
      lane_sequence.route_mission_id());
  // If local route mission id is different from current route solution mission
  // id, it means current route is changed to a new route, but local route ETA
  // is still for the previous route, and should not be displayed.
  if (current_route_status_.mission_id_for_local_route() !=
      current_route_status_.current_route_solution().mission_id()) {
    current_route_status_.set_route_display_state(
        routing::pb::RouteDisplayState::kNotDisplayETA);
  } else if (lane_sequence.dist_to_dest_m() <
                 kMaxDistToDestToNotDisplayRouteM ||
             (lane_sequence.immediate_pullover_destination()
                      .accumulated_distance() <
                  kMaxPulloverDistToNotDisplayRouteM &&
              (lane_sequence.immediate_pullover_destination()
                       .immediate_pullover_source() ==
                   routing::pb::ImmediatePullOverSource::kPlanning ||
               lane_sequence.immediate_pullover_destination()
                       .immediate_pullover_source() ==
                   routing::pb::ImmediatePullOverSource::kRouting))) {
    current_route_status_.set_route_display_state(
        routing::pb::RouteDisplayState::kNotDisplay);
  } else {
    current_route_status_.set_route_display_state(
        routing::pb::RouteDisplayState::kDisplay);
  }
  current_route_status_.set_dist_to_dest_in_local_m(
      lane_sequence.dist_to_dest_m());
  current_route_status_.set_time_to_dest_in_local_s(
      lane_sequence.time_to_dest_s());
  // NOTE(Tingran): Do not update pose using lane sequence because lane sequence
  // is received at a lower frequency than pose and may be behind the latest
  // pose.
  *current_route_status_.mutable_current_lane_ids() =
      lane_sequence.current_lane_ids();
  current_route_status_.set_route_divergence_warning(
      GetRouteDivergenceWarning(lane_sequence_.section_sequence_state()));

  // Sync immediate pullover info from planner to route status.
  SyncImmediatePullOverInfoFromPlanningLaneSequence(lane_sequence);

  if (current_route_status_.mission_state() == pb::MissionState::kIdle) {
    // NOTE(Tingran): Figure out the correct value. If set to be true, infra may
    // get empty route solution. So keep it as false for now.
    current_route_status_.set_is_valid(false);
    return true;
  }

  // Update the dist and time to destination from current pose.
  const pb::RouteSolution& current_route_solution =
      current_route_status_.current_route_solution();
  const std::optional<std::pair<double, double>> time_and_dist_to_current_end =
      routing::engine_utils::GetDistAndTimeToDest(
          pose_route_point, current_route_status_.waypoint_count(),
          current_route_solution, routing_engine_->routing_map());

  if (!time_and_dist_to_current_end.has_value()) {
    LOG(ERROR) << "Failed to get time_and_dist_to_current_end!";
    LOG(ERROR) << "------pose_route_point: "
               << pose_route_point.Utf8DebugString();
    current_route_status_.set_is_valid(false);
    return true;
  }
  UpdateArrivedSectionIdsAndWaypointCount(pose_route_point);
  UpdateRouteStatusDistanceAndTime((*time_and_dist_to_current_end).first,
                                   (*time_and_dist_to_current_end).second);

  // Update dist and time to destination for each waypoints.
  ::google::protobuf::RepeatedPtrField<pb::UpcomingWaypoint>* waypoints =
      current_route_status_.mutable_upcoming_waypoints();
  for (auto it = waypoints->begin(); it != waypoints->end();) {
    const double dist_to_waypoint = current_route_status_.dist_to_dest_m() -
                                    (*it).waypoint_info().dist_to_dest_m();
    const double time_to_waypoint = current_route_status_.time_to_dest_s() -
                                    (*it).waypoint_info().time_to_dest_s();
    // If this waypoint is arrived or passed, erase it.
    if (dist_to_waypoint < kWaypointArrivalThreshold) {
      it = waypoints->erase(it);
      continue;
    }
    (*it).set_dist_from_ego_car_m(dist_to_waypoint);
    (*it).set_time_from_ego_car_s(time_to_waypoint);
    ++it;
  }

  current_route_status_.set_is_valid(true);
  return true;
}

void RouteStatusManager::UpdateArrivedSectionIdsAndWaypointCount(
    const pb::RoutePoint& pose_route_point) {
  if (pose_route_point.lane_candidates().empty()) {
    return;
  }
  const auto& pose_lane_candidate = pose_route_point.lane_candidates(0);
  const hdmap::LaneInfo& pose_lane_info =
      routing_engine_->hdmap().GetLaneInfo(pose_lane_candidate.lane_id());
  AddArrivedSectionAndRoadId(pose_lane_info.section_id, pose_lane_info.road_id,
                             &current_route_status_);

  bool passed_waypoint =
      PassedWaypoint(current_route_status_, pose_lane_info,
                     pose_lane_candidate.percentage_along_lane());
  if (passed_waypoint) {
    current_route_status_.set_waypoint_count(
        current_route_status_.waypoint_count() + 1);
    // Clear the section ids when ego is in a new trip segment.
    current_route_status_.clear_arrived_section_ids_from_current_route_point();
    AddArrivedSectionAndRoadId(pose_lane_info.section_id,
                               pose_lane_info.road_id, &current_route_status_);
  }
}

void RouteStatusManager::SyncImmediatePullOverInfoFromPlanningLaneSequence(
    const planner::pb::PlanningLaneSequence& lane_sequence) {
  // Update immediate pull over state and point from lane sequence.
  auto* immediate_pullover_info =
      current_route_status_.mutable_immediate_pullover_info();
  if (immediate_pullover_info->immediate_pullover_state() ==
      pb::ImmediatePullOverState::kTriggered) {
    immediate_pullover_info->set_planner_immediate_pullover_state(
        lane_sequence.immediate_pullover_destination()
            .planner_immediate_pullover_state());

    if (lane_sequence.immediate_pullover_destination()
            .has_immediate_pullover_point()) {
      immediate_pullover_info->mutable_immediate_pullover_point()->set_x(
          lane_sequence.immediate_pullover_destination()
              .immediate_pullover_point()
              .x());
      immediate_pullover_info->mutable_immediate_pullover_point()->set_y(
          lane_sequence.immediate_pullover_destination()
              .immediate_pullover_point()
              .y());
    } else {
      // If found a point but fail to pull over, lane sequence will clear this
      // point and start to find a new one.
      // TODO(Wenyue): clear immediate pull over point after few cycles or few
      // seconds in case the message could be missed.
      immediate_pullover_info->clear_immediate_pullover_point();
    }

    // Failed after several immediate pull over retries in time range. Set state
    // to failed and clear point.
    if (lane_sequence.immediate_pullover_destination().is_terminated()) {
      std::stringstream debug_info;
      debug_info << "Received immediate pullover failed message, source is: "
                 << immediate_pullover_info->immediate_pullover_source();
      rt_event::PostRtEvent<rt_event::routing::ImmediatePullOverFail>(
          debug_info.str());
      ResetImmediatePullOverInfo();
    }
  }
}

void RouteStatusManager::UpdateImmediatePullOverStateToTriggered(
    const pb::ImmediatePullOverSource immediate_pull_over_source) {
  if (current_route_status_.immediate_pullover_info()
              .immediate_pullover_state() ==
          pb::ImmediatePullOverState::kTriggered &&
      current_route_status_.immediate_pullover_info()
              .immediate_pullover_source() != immediate_pull_over_source) {
    LOG(INFO) << "Immediate pullover is in progress, but change source. "
                 "Original source: "
              << current_route_status_.immediate_pullover_info()
                     .immediate_pullover_source()
              << ", new source: " << immediate_pull_over_source;
  }

  auto* immediate_pullover_info =
      current_route_status_.mutable_immediate_pullover_info();
  immediate_pullover_info->set_immediate_pullover_state(
      pb::ImmediatePullOverState::kTriggered);
  immediate_pullover_info->set_immediate_pullover_source(
      immediate_pull_over_source);
}

// Note: We still keep source as triggered source.
void RouteStatusManager::UpdateImmediatePullOverStateToSuccess() {
  if (current_route_status_.immediate_pullover_info()
          .immediate_pullover_state() ==
      pb::ImmediatePullOverState::kTriggered) {
    std::stringstream debug_info;
    debug_info << "Received immediate pullover success message, source is: "
               << current_route_status_.immediate_pullover_info()
                      .immediate_pullover_source();
    rt_event::PostRtEvent<rt_event::routing::ImmediatePullOverSuccess>(
        debug_info.str());
    auto* immediate_pullover_info =
        current_route_status_.mutable_immediate_pullover_info();
    immediate_pullover_info->set_immediate_pullover_state(
        pb::ImmediatePullOverState::kSuccess);
  }
}

void RouteStatusManager::ResetImmediatePullOverInfo() {
  auto* immediate_pullover_info =
      current_route_status_.mutable_immediate_pullover_info();
  immediate_pullover_info->set_immediate_pullover_state(
      pb::ImmediatePullOverState::kNotTriggered);
  immediate_pullover_info->set_immediate_pullover_source(
      pb::ImmediatePullOverSource::kNoneSource);
  immediate_pullover_info->clear_immediate_pullover_point();
  immediate_pullover_info->set_planner_immediate_pullover_state(
      pb::ImmediatePullOverState::kNotTriggered);
}

// Updates special planner response request to route status.
void RouteStatusManager::UpdateSpecialPlannerResponseRequest(
    pb::SpecialPlannerResponseRequest special_request) {
  current_route_status_.set_special_planner_response_request(special_request);
}

void RouteStatusManager::AppendRoutingFailureReason(
    const std::string& routing_failure_reason) {
  std::string* new_routing_failure_reason =
      current_route_status_.add_routing_failure_reasons();
  *new_routing_failure_reason = routing_failure_reason;
}

}  // namespace routing
