#include "planner/elective_lane_change/elective_lane_change_decider_utils.h"

#include <algorithm>
#include <limits>
#include <map>
#include <memory>
#include <set>
#include <string>
#include <unordered_map>
#include <vector>

#include "geometry/model/point_2d.h"
#include "geometry/model/polygon_with_cache.h"
#include "latency/latency_stat.h"
#include "log_utils/log_macros.h"
#include "math/constants.h"
#include "math/interpolation.h"
#include "math/math_util.h"
#include "math/range.h"
#include "math/unit_conversion.h"
#include "planner/behavior/util/lane_sequence_geometry/lane_sequence_geometry_utility.h"
#include "planner/planning_gflags.h"
#include "planner/speed/constraint/constraint_util.h"
#include "planner/speed/overlap/overlap_lib_util.h"
#include "planner/speed/reasoning_input/traffic_rules/speed_limit_traffic_rule.h"
#include "planner/world_model/planner_object/planner_object.h"
#include "planner/world_model/traffic_participant/traffic_participant_pose.h"
#include "pnc_map_service/map_elements/lane.h"
#include "trace/trace.h"
#include "voy_trace/trace_planner.h"

namespace planner {

constexpr double kSlowMovingAgentLowestSpeedInMps = 0.0;
constexpr double kSlowMovingCyclistOrUnknownDaySpeedInMps = 9.0;
constexpr double kSlowMovingCyclistOrUnknownNightSpeedInMps = 9.0;
constexpr double kSlowMovingVehicleDaySpeedInMps = 10.0;
constexpr double kSlowMovingVehicleNightSpeedInMps = 10.0;
constexpr double kSlowMovingCyclistOrUnknownSpeedLimitRatio = 1.0;
constexpr double kSlowMovingCyclistOrUnknownSpeedLimitNightRatio = 1.0;
constexpr double kSlowMovingVehicleSpeedLimitRatio = 0.85;
constexpr double kSlowMovingVehicleSpeedLimitNightRatio = 0.85;
constexpr double kSlowMovingVehicleJunctionDistInMeter = 50.0;
constexpr double kMaybeSlowMovingVehicleJunctionDistInMeter = 200.0;
constexpr double kMaxRequiredBlockingDurationInMSec = 3000;
constexpr double kMaxRequiredBlockingDurationForHighSpeedInMSec = 7000;
constexpr double kMinSanitationRequiredBlockingDurationInMSec = 1000;
constexpr double kMinRequiredBusStopBlockingDurationInMSec = 1000;
constexpr double kOncomingRequiredBlockingDurationInMSec = 1000;
constexpr double kMaxElectiveLaneChangeScore = 1.0;
constexpr double kMinElectiveLaneChangeScore = 0.0;
constexpr double kSlowMovingVehicleSpeedInNeighborLaneInMps = 3.0;
constexpr int kRequiredNumberOfVehiclesInNeighborLane = 2;
constexpr int kRequiredNumberOfSlowVehiclesInNeighborLane = 1;
constexpr double kVisibilityLengthDistInMeter = 100.0;
constexpr double kVisibilitySamplingDistInMeter = 4.0;
constexpr double kPreviewedRouteInfoAimLaneDistInMeter = 20.0;
constexpr double kSlowVehicleCompareSpeedLimitRatio = 0.5;
constexpr double kSlowVehicleCompareFrontObjectRatio = 0.7;
constexpr double kSlowMovingVehicleDiffSpeedInMps = 1.0;
constexpr double kMinimumDistanceBetweenTwoPointsInPolyline = 1.0;
constexpr double kVisibilityWidthInLaneDistInMeter = 1.5;
// The time windows since midnight by hour for night to consider night scenario.
constexpr std::array<std::pair<int, int>, 2> kHourWindowsSinceMidnightForNight =
    {{{0, 7}, {22, 24}}};
constexpr double kMaxElectiveLaneChangeCutInScore = 1.0;
constexpr double kMinElectiveLaneChangeCutInScore = 0.0;
constexpr std::array<std::pair<double, double>, 1>
    kLeftELCRoadExitHeadingWindowsAngleInRadians = {{{0.1 * M_PI, 0.9 * M_PI}}};
constexpr std::array<std::pair<double, double>, 1>
    kRightELCRoadExitHeadingWindowsAngleInRadians = {
        {{-0.9 * M_PI, -0.1 * M_PI}}};
constexpr std::array<std::pair<double, double>, 1>
    kUTurnHeadingWindowsAngleInRadians = {{{-0.7 * M_PI, -0.3 * M_PI}}};
constexpr std::array<std::pair<double, double>, 1>
    kJunctionFromRightSideHeadingWindowsAngleInRadians = {
        {{0.4 * M_PI, 0.6 * M_PI}}};
constexpr std::array<std::pair<double, double>, 1>
    kNeighborLaneSTMHeadingWindowsAngleInRadians = {
        {{-1.0 * M_PI, 1.0 * M_PI}}};
constexpr std::array<std::pair<double, double>, 1>
    kLeftELCGeneralHeadingWindowsAngleInRadians = {{{0.1 * M_PI, 0.9 * M_PI}}};
constexpr std::array<std::pair<double, double>, 1>
    kRightELCGeneralHeadingWindowsAngleInRadians = {
        {{-0.9 * M_PI, -0.1 * M_PI}}};
constexpr double kElectiveLaneChangeCutInRiskAvoidanceScore = 2.0;
constexpr double kObjectCenterRadiusInUTurnLaneDistInMeter = 0.0001;
constexpr double kBlockingStateBoundaryClearanceDistInMeter = 3.0;
constexpr double kBlockingStateDiffArcLengthDistInMeter = 1.0;
constexpr double kSlowMovingELCThresholdExpansionRatio = 1.0;
constexpr double kSlowMovingSanitationSpeedInMps = 5.5;
constexpr double kMinRemainingDistanceForLaneChangeInMeter = 50.0;
constexpr double kLaneChangeDurationInSec = 6.0;
constexpr double kMaxSpeedForSlowCutInAgent = 2.0;
constexpr double kMaxAgentConsiderableLateralDistToLaneBoundary = 2.0;
constexpr double kMaxArclengthForSharpCutIn = 10.0;
constexpr double kMaxDistBetweenEgoPathAndAgentInMeter = 2.0;
constexpr int64 kHasSTMSignalRecentlyDurationInMSec = 2000;
// The distance ahead of ego that we use to compute region_start_arclength.
constexpr double kRiskAvoidanceNeighborLaneAgentTTCInSec = 5.0;
constexpr double kRiskAvoidanceNeighborLaneAgentSpeedBasedTimeBufferInSec = 3.0;
constexpr double kRiskAvoidanceNeighborLaneAgentMinSafetyDistInMeter = 15.0;
constexpr double kFrontObjectsInTargetRangeDistInMeter = 100.0;
constexpr double kBehindObjectsInTargetRangeDistInMeter = -5.0;
constexpr double kRiskAvoidanceNeighborLaneAgentMaxDist = 10.0;
constexpr double kRiskAvoidanceNeighborLaneAgentMinDist = -100.0;
constexpr double kMinRequiredBlockingDurationInMSec = 2500;

// TODO(Wenyue): Fix this API, make it unrelated to regional path.
RegionalPathNextInfo GetRegionalPathNextInfoForELC(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const math::geometry::Point2d& pos) {
  RegionalPathNextInfo regional_path_next_info_result;
  double distance_to_next_crosswalk = 0.0;
  double distance_crosswalk_to_start_pos = 0.0;
  double distance_crosswalk_to_end_pos = 0.0;
  const double ego_arc_length = lane_sequence[0]->GetArclength(pos);
  for (const pnc_map::Lane* lane : lane_sequence) {
    if (lane->IsInJunction() && lane->turn() != hdmap::Lane_Turn_STRAIGHT) {
      regional_path_next_info_result.next_turn_type = lane->turn();
      break;
    }
  }
  for (const pnc_map::Lane* lane : lane_sequence) {
    if (!lane->crosswalks_ranges().empty()) {
      for (const auto& crosswalk : lane->crosswalks_ranges()) {
        distance_crosswalk_to_start_pos = crosswalk.start_pos * lane->length();
        distance_crosswalk_to_end_pos = crosswalk.end_pos * lane->length();
        if (lane != lane_sequence.front() ||
            distance_crosswalk_to_end_pos > ego_arc_length) {
          distance_to_next_crosswalk = distance_to_next_crosswalk +
                                       distance_crosswalk_to_start_pos -
                                       ego_arc_length;
          regional_path_next_info_result.distance_to_next_crosswalk =
              distance_to_next_crosswalk > 0 ? distance_to_next_crosswalk : 0;
          return regional_path_next_info_result;
        } else {
          continue;
        }
      }
    }
    distance_to_next_crosswalk += lane->length();
  }
  return regional_path_next_info_result;
}

bool IsNightScene(const std::tm* time_info) {
  if (time_info == nullptr) {
    return false;
  }
  const int hours_since_midnight = time_info->tm_hour;
  for (const auto& hour_window : kHourWindowsSinceMidnightForNight) {
    if (hours_since_midnight >= hour_window.first &&
        hours_since_midnight < hour_window.second) {
      return true;
    }
  }
  return false;
}

std::optional<TypedObjectId> GetObjectIdOfYieldDominantConstraint(
    const speed::SpeedResult& speed_result) {
  const int dominant_ix = speed_result.dominant_constraint_ix();
  if (dominant_ix < 0) {
    return std::nullopt;
  }
  DCHECK_LT(dominant_ix, speed_result.constraints().size());
  speed::CheckDominantConstraintDecision(
      speed_result.constraints(),
      speed_result.planning_time_horizon_in_second(), speed_result.decisions(),
      speed_result.tree_decisions(), dominant_ix);

  return std::make_optional<TypedObjectId>(
      TypedObjectId(speed_result.constraints()[dominant_ix].obj_id,
                    speed_result.constraints()[dominant_ix].IsConstructionZone()
                        ? pb::ObjectSourceType::kConstructionZone
                        : pb::ObjectSourceType::kTrackedObject));
}

std::vector<const PlannerObject*>
GetBlockingObjectsOnLaneSequenceByOccupancyParam(
    const SpeedWorldModel& world_model,
    const std::unordered_map<ObjectId, PlannerObject>& object_map,
    const selection::TrajectoryMetaData& candidate,
    const std::vector<const pnc_map::Lane*>& lane_sequence) {
  std::vector<std::pair<double, const PlannerObject*>>
      arclength_and_objects_on_lane_sequence;
  const math::geometry::PolylineCurve2d& current_center_line =
      lane_selection::GetLaneSequenceCurve(
          lane_sequence, lane_selection::LaneCurveType::kCenterLine);
  const math::geometry::Point2d ego_rear(
      world_model.robot_state().plan_init_state_snapshot().x(),
      world_model.robot_state().plan_init_state_snapshot().y());
  const double ego_arc_length =
      current_center_line
          .GetProximity(ego_rear, math::pb::UseExtensionFlag::kForbid)
          .arc_length;

  for (const auto& [typed_object_id, object_reasoning_info] :
       candidate.object_reasoning_info_map()) {
    const int64_t agent_id =
        object_reasoning_info.blocking_state_data().object_id;
    if (object_map.find(agent_id) == object_map.end()) {
      continue;
    }
    const pb::LateralBlockingState::BlockingState blocking_state =
        object_reasoning_info.blocking_state_data()
            .tracked_blocking_info.blocking_state();
    const PlannerObject& object = object_map.at(agent_id);
    const auto& object_occupancy_state_iter =
        candidate.object_occupancy_state_map().find(agent_id);
    if (object_occupancy_state_iter ==
        candidate.object_occupancy_state_map().end()) {
      continue;
    }
    const ObjectOccupancyParam& object_occupancy_param =
        object_occupancy_state_iter->second->current_snapshot_info()
            .object_occupancy_param();
    const double left_boundary_clearance_m =
        object_occupancy_param.left_boundary_clearance_m;
    const double right_boundary_clearance_m =
        object_occupancy_param.right_boundary_clearance_m;

    const math::ProximityQueryInfo& proximity =
        current_center_line.GetProximity(object.center_2d(),
                                         math::pb::UseExtensionFlag::kForbid);
    const double agent_arc_length = proximity.arc_length;
    // Gets the arc length between ego and agent.
    const double diff_arc_length = agent_arc_length - ego_arc_length;
    if (blocking_state == pb::LateralBlockingState::NON_BLOCKING ||
        blocking_state == pb::LateralBlockingState::SOFT_BLOCKING ||
        left_boundary_clearance_m >
            kBlockingStateBoundaryClearanceDistInMeter ||
        right_boundary_clearance_m >
            kBlockingStateBoundaryClearanceDistInMeter ||
        diff_arc_length < kBlockingStateDiffArcLengthDistInMeter) {
      continue;
    }
    arclength_and_objects_on_lane_sequence.push_back(
        std::make_pair(agent_arc_length, &object));
  }

  std::sort(arclength_and_objects_on_lane_sequence.begin(),
            arclength_and_objects_on_lane_sequence.end(),
            [](std::pair<double, const PlannerObject*> lhs,
               std::pair<double, const PlannerObject*> rhs) {
              return lhs.first < rhs.first;
            });

  std::vector<const PlannerObject*> objects_on_lane_sequence;
  objects_on_lane_sequence.reserve(
      arclength_and_objects_on_lane_sequence.size());
  std::transform(arclength_and_objects_on_lane_sequence.begin(),
                 arclength_and_objects_on_lane_sequence.end(),
                 std::back_inserter(objects_on_lane_sequence),
                 [](const auto& pair) { return pair.second; });
  return objects_on_lane_sequence;
}

std::vector<const PlannerObject*> GetBlockingObjectsOnLaneSequence(
    const std::unordered_map<ObjectId, PlannerObject>& object_map,
    const selection::TrajectoryMetaData& candidate) {
  std::vector<std::pair<double, const PlannerObject*>>
      arclength_and_objects_on_lane_sequence;
  for (const auto& agent_in_lane_state : candidate.agent_in_lane_states_map()) {
    const int64_t agent_id = agent_in_lane_state.first;
    if (object_map.find(agent_id) == object_map.end()) {
      continue;
    }
    const std::unique_ptr<AgentInLaneStates>& in_lane_state =
        agent_in_lane_state.second;
    const pb::AgentSnapshotBlockingState blockage_state =
        in_lane_state->tracked_state.blocking_info.blocking_state;
    if (blockage_state == pb::AgentSnapshotBlockingState::NOT_BLOCKING ||
        blockage_state == pb::AgentSnapshotBlockingState::SOFT_BLOCKING) {
      continue;
    }
    const PlannerObject& object = object_map.at(agent_id);
    const double arc_length =
        in_lane_state->tracked_state.inlane_param.start_arclength_m;
    arclength_and_objects_on_lane_sequence.push_back(
        std::make_pair(arc_length, &object));
  }

  std::sort(arclength_and_objects_on_lane_sequence.begin(),
            arclength_and_objects_on_lane_sequence.end(),
            [](std::pair<double, const PlannerObject*> lhs,
               std::pair<double, const PlannerObject*> rhs) {
              return lhs.first < rhs.first;
            });

  std::vector<const PlannerObject*> objects_on_lane_sequence;
  objects_on_lane_sequence.reserve(
      arclength_and_objects_on_lane_sequence.size());
  std::transform(arclength_and_objects_on_lane_sequence.begin(),
                 arclength_and_objects_on_lane_sequence.end(),
                 std::back_inserter(objects_on_lane_sequence),
                 [](const auto& pair) { return pair.second; });
  return objects_on_lane_sequence;
}

std::vector<const PlannerObject*> GetNonLeavingObjects(
    const SpeedWorldModel& world_model,
    const selection::TrajectoryMetaData& candidate,
    const std::vector<const PlannerObject*>& objects,
    std::ostringstream& debug_oss) {
  std::vector<const PlannerObject*> non_leaving_objects;
  std::copy_if(
      objects.begin(), objects.end(), std::back_inserter(non_leaving_objects),
      [&world_model, &candidate, &debug_oss](const PlannerObject* object) {
        return WillPrimaryBpStayOnLaneSequence(
            world_model, candidate, *CHECK_NOTNULL(object), debug_oss);
      });
  return non_leaving_objects;
}

double GetDistanceToJunction(
    const std::optional<math::geometry::PolygonWithCache2d>& contour,
    const math::geometry::Point2d& pos, const pnc_map::Lane* current_lane,
    const std::vector<const pnc_map::Lane*>& lane_sequence) {
  DCHECK(contour.has_value() || current_lane != nullptr);

  const std::vector<const pnc_map::Lane*>& current_lane_in_vector = {
      current_lane};
  const std::vector<const pnc_map::Lane*> associated_lanes =
      contour.has_value()
          ? pnc_map::GetAssociatedLanes(lane_sequence, contour.value())
          : current_lane_in_vector;

  std::set<int64_t> road_ids;
  double dist_to_junction = std::numeric_limits<double>::infinity();
  for (const auto& lane_ptr : associated_lanes) {
    const pnc_map::Road& road = *DCHECK_NOTNULL(lane_ptr->section()->road());
    if (road.IsInJunction()) {
      return 0.0;
    }
    const auto [_, inserted] = road_ids.insert(road.id());
    if (!inserted) {
      continue;
    }

    const double pos_arclength_on_road =
        road.reference_line()
            .GetProximity(pos, math::pb::UseExtensionFlag::kForbid)
            .arc_length;
    math::UpdateMin(
        road.road_start_to_nearest_junction_dist_m() - pos_arclength_on_road,
        dist_to_junction);
  }
  return dist_to_junction;
}

double GetDistanceToCrosswalk(
    const math::geometry::Point2d& pos,
    const std::vector<const pnc_map::Lane*>& lane_sequence) {
  const RegionalPathNextInfo& regional_path_next_info =
      GetRegionalPathNextInfoForELC(lane_sequence, pos);
  const double distance_to_crosswalk =
      regional_path_next_info.distance_to_next_crosswalk;
  return std::max(distance_to_crosswalk, 0.0);
}

double GetDistanceBetweenObjects(const selection::TrajectoryMetaData& candidate,
                                 const PlannerObject& front_object,
                                 const PlannerObject& rear_object) {
  const AgentInLaneStatesMap& tracked_object_param_map =
      candidate.agent_in_lane_states_map();
  const AgentInLaneStates* front_agent_inlane_state =
      gtl::FindOrDie(tracked_object_param_map, front_object.id()).get();
  const AgentInLaneStates* rear_agent_inlane_state =
      gtl::FindOrDie(tracked_object_param_map, rear_object.id()).get();
  return front_agent_inlane_state->tracked_state.inlane_param
             .start_arclength_m -
         rear_agent_inlane_state->tracked_state.inlane_param.end_arclength_m;
}

bool HasCongestionAhead(const std::vector<const PlannerObject*>&
                            non_leaving_blocking_objects_on_lane_sequence,
                        const PlannerObject& leading_object,
                        const double leading_object_projected_speed,
                        const double min_ego_remaining_lane_change_distance,
                        const std::vector<math::Range1d>& visibility_ranges,
                        std::ostringstream& debug_oss) {
  debug_oss << "\n[HasCongestionAhead]:\n";

  // Find objects with similar or smaller speed than the leading object.
  std::vector<const PlannerObject*> similar_or_slower_speed_objects;
  std::copy_if(non_leaving_blocking_objects_on_lane_sequence.begin(),
               non_leaving_blocking_objects_on_lane_sequence.end(),
               std::back_inserter(similar_or_slower_speed_objects),
               [&leading_object,
                leading_object_projected_speed](const PlannerObject* object) {
                 constexpr double kHigherSpeedThresholdInMps = 3.0;
                 return object->id() != leading_object.id() &&
                        object->speed() < leading_object_projected_speed +
                                              kHigherSpeedThresholdInMps;
               });

  // Find vehicles with similar or smaller speed than the leading object.
  auto slow_moving_vehicle_iter = std::find_if(
      similar_or_slower_speed_objects.begin(),
      similar_or_slower_speed_objects.end(),
      [](const PlannerObject* object) { return object->is_vehicle(); });

  // 1. Congestion if there is a similar speed or slower vehicle ahead of the
  // leading agent.
  if (slow_moving_vehicle_iter != similar_or_slower_speed_objects.end()) {
    debug_oss
        << "Congestion due to similar speed vehicle ahead of leading object. "
        << (*slow_moving_vehicle_iter)->id() << "\n";
    return true;
  }

  // 2. Congestion if the overall density of objects is large.
  constexpr double kMaxDistanceBeyondEgoToCheckDensityInMeter = 100.0;
  constexpr double kMinDistanceBeyondLeadAgentToCheckDensityInMeter = 30.0;
  const double density_range_min =
      leading_object.l2_distance_to_ego_m() + leading_object.length() * 0.5;
  const double density_range_max = std::max(
      std::min(kMaxDistanceBeyondEgoToCheckDensityInMeter,
               min_ego_remaining_lane_change_distance),
      density_range_min + kMinDistanceBeyondLeadAgentToCheckDensityInMeter);

  // To compute density, we first compute the total occupancy of objects in the
  // range. The occupancy is:
  // - (For an object) The length of the object plus a speed-based range.
  // - (For occlusion) The length of the occlusion.

  // Compute the total occupancy of objects in the range.
  double object_occupancy = 0.0;
  for (const auto* object : similar_or_slower_speed_objects) {
    if (object->l2_distance_to_ego_m() < density_range_min ||
        object->l2_distance_to_ego_m() > density_range_max) {
      continue;
    }
    constexpr double kVehicleSpeedToOccupancyRatio = 3.0;
    constexpr double kNonVehicleSpeedToOccupancyRatio = 1.0;
    object_occupancy +=
        (object->length() +
         (object->is_vehicle()
              ? object->speed() * kVehicleSpeedToOccupancyRatio
              : object->speed() * kNonVehicleSpeedToOccupancyRatio));
  }

  // Compute the total length of occlusions in the range.
  const double occluded_range_length = ComputeOccludedRangeLength(
      visibility_ranges, density_range_min, density_range_max);

  // The occupancy of occlusions is the length of the occlusion multiplied by a
  // weight that is decided with the remaining LC distance. The less the
  // remaining distance, the more weight the occlusion.
  constexpr double kOcclusionToOccupancyMinRatio = 0.2;
  constexpr double kOcclusionToOccupancyMaxRatio = 0.4;
  constexpr double kOcclusionMinRatioDistanceInMeter = 180.0;
  constexpr double kOcclusionMaxRatioDistanceInMeter = 50.0;
  // Reduce the distance needed for special vehicles (sanitation, construction).
  constexpr double kOcclusionMinRatioDistanceForSpecialVehicleInMeter = 60.0;
  constexpr double kOcclusionMaxRatioDistanceForSpecialVehicleInMeter = 30.0;
  const bool is_special_vehicle =
      leading_object.is_sanitation() || leading_object.is_construction();
  const double occlusion_min_ratio_distance =
      is_special_vehicle ? kOcclusionMinRatioDistanceForSpecialVehicleInMeter
                         : kOcclusionMinRatioDistanceInMeter;
  const double occlusion_max_ratio_distance =
      is_special_vehicle ? kOcclusionMaxRatioDistanceForSpecialVehicleInMeter
                         : kOcclusionMaxRatioDistanceInMeter;
  const double occlusion_to_occupancy_ratio = math::GetLinearInterpolatedY(
      occlusion_min_ratio_distance, occlusion_max_ratio_distance,
      kOcclusionToOccupancyMinRatio, kOcclusionToOccupancyMaxRatio,
      min_ego_remaining_lane_change_distance);
  const double occlusion_occupancy =
      occluded_range_length * occlusion_to_occupancy_ratio;

  // Then, compute the density by taking the total occupancy and dividing it by
  // the range.
  const double total_occupancy = object_occupancy + occlusion_occupancy;
  const double density = math::Clamp(
      total_occupancy / std::max(density_range_max - density_range_min,
                                 math::constants::kEpsilon),
      0.0, 1.0);

  constexpr double kCongestionDensityThreshold = 0.3;
  const bool has_congestion = density > kCongestionDensityThreshold;

  debug_oss << DUMP_TO_STREAM(has_congestion) << "\n"
            << DUMP_TO_STREAM(density)
            << " (threshold: " << kCongestionDensityThreshold << ")\n"
            << DUMP_TO_STREAM(total_occupancy, density_range_min,
                              density_range_max)
            << "\n"
            << DUMP_TO_STREAM(object_occupancy) << "\n"
            << DUMP_TO_STREAM(occlusion_occupancy, occluded_range_length,
                              occlusion_min_ratio_distance,
                              occlusion_max_ratio_distance,
                              occlusion_to_occupancy_ratio, is_special_vehicle,
                              min_ego_remaining_lane_change_distance)
            << "\n";
  return has_congestion;
}

double ComputeOccludedRangeLength(
    const std::vector<math::Range1d>& visibility_ranges, const double range_min,
    const double range_max) {
  double occluded_range_length = 0.0;
  double occlusion_start = range_min;
  for (const auto& visibility_range : visibility_ranges) {
    // Skip if the occlusion start is beyond the visibility range.
    if (occlusion_start > visibility_range.end_pos) {
      continue;
    }

    // Stop is the visibility range is beyond the range max.
    if (visibility_range.start_pos > range_max) {
      occluded_range_length += range_max - occlusion_start;
      occlusion_start = range_max;
      break;
    }

    // When we find a visibility range beyond the occlusion start, then the
    // range between the occlusion start and the visibility range start is the
    // occlusion.
    if (visibility_range.start_pos > occlusion_start) {
      occluded_range_length += visibility_range.start_pos - occlusion_start;
      occlusion_start = visibility_range.end_pos;
      continue;
    }

    // Otherwise, we can extend the occlusion start to the end of the visibility
    // range.
    occlusion_start = visibility_range.end_pos;
  }
  // If the occlusion start is still within the range, we can add the remaining
  // length.
  if (occlusion_start < range_max) {
    occluded_range_length += range_max - occlusion_start;
  }
  return occluded_range_length;
}

double CalculateRequiredSpeed(const PlannerObject& object,
                              const double remaining_lane_change_distance,
                              const bool is_night_scene,
                              const double local_lane_change_speed_threshold,
                              const bool is_use_for_normal_elc) {
  if (is_use_for_normal_elc) {
    // For the local lane change, the speed limit is already considered in
    // ComputeSpeedThresholdForLocalLaneChange; For the global lane change, the
    // speed limit is considered when computing the time benefit in
    // GetDynamicDistanceThreshold.
    // TODO(anwu): update for gap align decision.
    return local_lane_change_speed_threshold;
  }

  const double gap_align_slow_moving_vehicle_speed =
      is_night_scene ? kSlowMovingVehicleNightSpeedInMps
                     : kSlowMovingVehicleDaySpeedInMps;
  const double slow_moving_cyclist_or_unknown_speed =
      is_night_scene ? kSlowMovingCyclistOrUnknownNightSpeedInMps
                     : kSlowMovingCyclistOrUnknownDaySpeedInMps;
  const double max_speed =
      object.object_type() == voy::perception::ObjectType::VEHICLE
          ? gap_align_slow_moving_vehicle_speed
          : slow_moving_cyclist_or_unknown_speed;
  return kSlowMovingELCThresholdExpansionRatio *
         math::GetLinearInterpolatedY(
             kSlowMovingVehicleJunctionDistInMeter,
             kMaybeSlowMovingVehicleJunctionDistInMeter,
             kSlowMovingAgentLowestSpeedInMps, max_speed,
             remaining_lane_change_distance,
             /*allow_extension=*/false);
}

double CalculateRequiredRatio(const PlannerObject& object,
                              const double remaining_lane_change_distance,
                              const bool is_night_scene,
                              const bool is_use_for_normal_elc) {
  if (is_use_for_normal_elc) {
    // For the local lane change, the speed limit ratio is already considered in
    // ComputeSpeedThresholdForLocalLaneChange; For the global lane change, the
    // speed limit is considered when computing the time benefit in
    // GetDynamicDistanceThreshold.
    // TODO(anwu): update for gap align decision.
    return 1.0;
  }

  const double max_slow_moving_vehicle_speed_limit_ratio =
      is_night_scene ? kSlowMovingVehicleSpeedLimitNightRatio
                     : kSlowMovingVehicleSpeedLimitRatio;
  const double max_slow_moving_cyclist_or_unknown_speed_limit_ratio =
      is_night_scene ? kSlowMovingCyclistOrUnknownSpeedLimitNightRatio
                     : kSlowMovingCyclistOrUnknownSpeedLimitRatio;
  const double max_ratio =
      object.object_type() == voy::perception::ObjectType::VEHICLE
          ? max_slow_moving_vehicle_speed_limit_ratio
          : max_slow_moving_cyclist_or_unknown_speed_limit_ratio;
  return kSlowMovingELCThresholdExpansionRatio *
         math::GetLinearInterpolatedY(
             kSlowMovingVehicleJunctionDistInMeter,
             kMaybeSlowMovingVehicleJunctionDistInMeter, 0.0, max_ratio,
             remaining_lane_change_distance,
             /*allow_extension=*/false);
}

double GetSpeedLimit(const pnc_map::Lane* current_lane) {
  const auto& speed_limits = current_lane->speed_limits();
  const double speed_limit =
      std::min_element(speed_limits.begin(), speed_limits.end(),
                       [](const hdmap::Limit& lhs, const hdmap::Limit& rhs) {
                         return lhs.limit_max() < rhs.limit_max();
                       })
          ->limit_max();
  const double ego_max_speed =
      speed::traffic_rules::GetEgoMaxSpeedDependOnCar();
  return std::min(ego_max_speed, speed_limit);
}

bool IsVehicleAtRequiredSpeed(const PlannerObject& object,
                              const double object_projected_speed,
                              const pnc_map::Lane* current_lane,
                              const double remaining_lane_change_distance,
                              const bool is_night_scene,
                              const double local_lane_change_speed_threshold,
                              const bool is_use_for_normal_elc,
                              std::ostringstream& debug_oss) {
  DCHECK(object.object_type() == voy::perception::ObjectType::VEHICLE);

  if (object.is_primary_stationary()) {
    debug_oss << __func__ << ": false (Not handling stationary vehicles.)\n";
    return false;
  }

  const double required_speed = CalculateRequiredSpeed(
      object, remaining_lane_change_distance, is_night_scene,
      local_lane_change_speed_threshold, is_use_for_normal_elc);
  const double required_ratio =
      CalculateRequiredRatio(object, remaining_lane_change_distance,
                             is_night_scene, is_use_for_normal_elc);
  const double speed_limit = GetSpeedLimit(current_lane);

  const bool is_vehicle_at_required_speed =
      object_projected_speed < required_speed &&
      object_projected_speed < speed_limit * required_ratio;

  debug_oss << __func__ << ": " << is_vehicle_at_required_speed << " ("
            << DUMP_TO_STREAM(required_speed) << ", "
            << DUMP_TO_STREAM(required_ratio) << ", "
            << DUMP_TO_STREAM(speed_limit) << ", "
            << DUMP_TO_STREAM(object_projected_speed) << ")\n";
  return is_vehicle_at_required_speed;
}

double ComputeSpeedThresholdForLocalLaneChange(
    const double ego_speed, const double speed_limit,
    const double left_lane_speed, const double right_lane_speed,
    const planner::PlannerObject& object,
    const planner::PlannerObject* next_object,
    const double ego_local_remaining_lane_change_distance,
    const bool is_on_highway, std::ostringstream& debug_oss) {
  // Agents may slow down before the end of the local lane change (e.g. vehicles
  // slowing down before crosswalk), and we shouldn't trigger ELC in this case.
  // We should only trigger progress ELC when:
  // - The agent is much slower than the agent in the neighbor lanes.
  // - The agent is much slower than the agent in the front.
  // - The agent is slower than the expected driving speed before the upcoming
  // crosswalk/junction.
  // TODO(Zhaorui): Compute separately for the left ELC and right ELC.
  const double object_local_remaining_lane_change_distance =
      ego_local_remaining_lane_change_distance - object.l2_distance_to_ego_m();
  const double neighbor_lane_speed =
      std::min(left_lane_speed, right_lane_speed);
  constexpr double kNeighborLaneSpeedDiffThresholdInMps = 4.0;
  constexpr double kNeighborLaneEffectiveRangeInMeter = 120.0;
  const double speed_threshold_by_neighbor_lane_speed =
      object_local_remaining_lane_change_distance >
              kNeighborLaneEffectiveRangeInMeter
          ? std::numeric_limits<double>::infinity()
          : (neighbor_lane_speed - kNeighborLaneSpeedDiffThresholdInMps);

  constexpr double kNextObjectSpeedDiffThresholdInMps = 4.0;
  constexpr double kNextAgentEffectiveRangeInMeter = 200.0;
  double speed_threshold_by_next_agent =
      std::numeric_limits<double>::infinity();
  if (next_object != nullptr && object_local_remaining_lane_change_distance <
                                    kNextAgentEffectiveRangeInMeter) {
    const double obj_dist_to_next_obj = next_object->l2_distance_to_ego_m() -
                                        object.l2_distance_to_ego_m() -
                                        object.length();
    constexpr double kMaxDistanceToConsiderNextObjInMeter = 50.0;
    if (obj_dist_to_next_obj < kMaxDistanceToConsiderNextObjInMeter) {
      speed_threshold_by_next_agent =
          next_object->speed() - kNextObjectSpeedDiffThresholdInMps;
    }
  }

  const double kMinSpeedBeforeLocalLaneChangeInMps = 0.0;
  const double kMaxSpeedBeforeLocalLaneChangeInMps = 20.0;
  const double kMinDistBeforeLocalLaneChangeInMeter = 20.0;
  const double kMaxDistBeforeLocalLaneChangeInMeter = 200.0;
  const double kMaxDistBeforeLocalLaneChangeOnHighwayInMeter = 500.0;
  // TODO(anwu): Use speed_limit only.
  const double max_speed_before_local_lane_change =
      is_on_highway
          ? speed_limit
          : std::min(speed_limit, kMaxSpeedBeforeLocalLaneChangeInMps);
  const double max_dist_before_local_lane_change =
      is_on_highway ? kMaxDistBeforeLocalLaneChangeOnHighwayInMeter
                    : kMaxDistBeforeLocalLaneChangeInMeter;
  const double speed_threshold_by_dist = math::GetLinearInterpolatedY(
      kMinDistBeforeLocalLaneChangeInMeter, max_dist_before_local_lane_change,
      kMinSpeedBeforeLocalLaneChangeInMps, max_speed_before_local_lane_change,
      object_local_remaining_lane_change_distance);

  // No need to trigger progress ELC when agent is moderately faster than ego.
  const double kMaxAgentSpeedDiffThresholdInMps = 3.0;
  const double speed_threshold_by_ego_speed =
      ego_speed + kMaxAgentSpeedDiffThresholdInMps;

  debug_oss << "\n[LocalLaneChange]\n"
            << DUMP_TO_STREAM(speed_threshold_by_dist) << "\n"
            << DUMP_TO_STREAM(speed_threshold_by_next_agent)
            << (next_object != nullptr
                    ? " (" + std::to_string(next_object->id()) + ")"
                    : "")
            << "\n"
            << DUMP_TO_STREAM(speed_threshold_by_neighbor_lane_speed) << "\n"
            << DUMP_TO_STREAM(speed_threshold_by_ego_speed) << "\n";

  return std::max(
      0.0, std::min({speed_threshold_by_neighbor_lane_speed,
                     speed_threshold_by_next_agent, speed_threshold_by_dist,
                     speed_threshold_by_ego_speed}));
}

bool IsSlowMovingSanitation(const PlannerObject& object,
                            std::ostringstream& debug_oss) {
  DCHECK(object.object_type() == voy::perception::ObjectType::VEHICLE);

  const bool is_object_sanitation =
      IsSanitationVehicle(object.tracked_object());
  const bool is_object_slow = object.speed() < kSlowMovingSanitationSpeedInMps;
  const bool is_slow_moving_sanitation = is_object_sanitation && is_object_slow;

  debug_oss << __func__ << ": " << is_slow_moving_sanitation << " ("
            << DUMP_TO_STREAM(is_object_sanitation) << ", "
            << DUMP_TO_STREAM(is_object_slow) << ")\n";
  return is_slow_moving_sanitation;
}

bool IsVehicleSlowInNeighborLaneSequence(
    const double slow_vehicle_ratio_in_left_lane_sequence,
    const double slow_vehicle_ratio_in_right_lane_sequence,
    std::ostringstream& debug_oss) {
  const bool is_vehicle_slow_in_neighbor_lane_sequence =
      slow_vehicle_ratio_in_left_lane_sequence >
          kSlowVehicleCompareFrontObjectRatio &&
      slow_vehicle_ratio_in_right_lane_sequence >
          kSlowVehicleCompareFrontObjectRatio;
  debug_oss << "[IsVehicleSlowInNeighborLaneSequence] "
            << is_vehicle_slow_in_neighbor_lane_sequence << "\n"
            << "left slow vehicle ratio:\t"
            << slow_vehicle_ratio_in_left_lane_sequence << "\n"
            << "right slow vehicle ratio:\t"
            << slow_vehicle_ratio_in_right_lane_sequence << "\n";
  return is_vehicle_slow_in_neighbor_lane_sequence;
}

double CalculateCyclistOrUnknownSpeedScore(
    const PlannerObject& object, const double object_projected_speed,
    const double speed_limit, double remaining_lane_change_distance,
    const bool is_night_scene, const double local_lane_change_speed_threshold,
    const bool is_use_for_normal_elc, std::ostringstream& debug_oss) {
  if (object.object_type() != voy::perception::ObjectType::CYCLIST &&
      object.object_type() != voy::perception::ObjectType::UNKNOWN) {
    return kMinElectiveLaneChangeScore;
  }

  debug_oss << "\n[CalculateCyclistOrUnknownSpeedScore]\n";
  if (object.is_object_drivable() || object.is_better_not_drive()) {
    DCHECK(object.object_type() == voy::perception::ObjectType::UNKNOWN);
    debug_oss << "Not handling drivable or better not drive objects.\n";
    return kMinElectiveLaneChangeScore;
  }

  const double required_speed = CalculateRequiredSpeed(
      object, remaining_lane_change_distance, is_night_scene,
      local_lane_change_speed_threshold, is_use_for_normal_elc);
  const double required_ratio =
      CalculateRequiredRatio(object, remaining_lane_change_distance,
                             is_night_scene, is_use_for_normal_elc);

  const double cyclist_or_unknown_speed_score =
      (object_projected_speed < required_speed &&
       object_projected_speed < speed_limit * required_ratio)
          ? kMaxElectiveLaneChangeScore
          : kMinElectiveLaneChangeScore;
  debug_oss << DUMP_TO_STREAM(required_speed) << "\n"
            << DUMP_TO_STREAM(required_ratio) << "\n"
            << DUMP_TO_STREAM(speed_limit) << "\n"
            << DUMP_TO_STREAM(object_projected_speed) << "\n"
            << DUMP_TO_STREAM(cyclist_or_unknown_speed_score) << "\n";
  return cyclist_or_unknown_speed_score;
}

double CalculateVehicleSpeedScoreForGapAlign(
    const PlannerObject& object, const double projected_agent_speed,
    const pnc_map::Lane* current_lane,
    const double remaining_lane_change_distance, const bool is_night_scene,
    const double local_lane_change_speed_threshold,
    const bool is_use_for_normal_elc, std::ostringstream& debug_oss) {
  if (object.object_type() != voy::perception::ObjectType::VEHICLE) {
    debug_oss << __func__ << ": Not a vehicle and use minimum value.\n";
    return kMinElectiveLaneChangeScore;
  }

  const bool is_vehicle_at_required_speed = IsVehicleAtRequiredSpeed(
      object, projected_agent_speed, current_lane,
      remaining_lane_change_distance, is_night_scene,
      local_lane_change_speed_threshold, is_use_for_normal_elc, debug_oss);
  const bool is_slow_moving_sanitation =
      IsSlowMovingSanitation(object, debug_oss);

  const double vehicle_speed_score =
      (is_vehicle_at_required_speed || is_slow_moving_sanitation)
          ? kMaxElectiveLaneChangeScore
          : kMinElectiveLaneChangeScore;
  debug_oss << __func__ << ": " << DUMP_TO_STREAM(vehicle_speed_score) << "\n";
  return vehicle_speed_score;
}

bool IsLeadingObjectFarFromEgo(const double ego_speed,
                               const PlannerObject& leading_object,
                               const double leading_object_projected_speed,
                               std::ostringstream& debug_oss) {
  // We use TTC-based distance and raw-speed based distance to decide if the
  // leading object is far from ego. TTC-based distance.
  constexpr double kMaxTTCToTriggerELCInSec = 8.0;
  const double ego_agent_speed_diff =
      ego_speed - leading_object_projected_speed;
  const double ttc_based_distance =
      ego_agent_speed_diff > math::constants::kEpsilon
          ? ego_agent_speed_diff * kMaxTTCToTriggerELCInSec
          : 0.0;

  // Raw-speed based distance.
  constexpr double kMaxRawSpeedBasedTimeToTriggerELCInSec = 2.5;
  const double raw_speed_based_distance =
      ego_speed * kMaxRawSpeedBasedTimeToTriggerELCInSec;

  // Raw-distance.
  constexpr double kMaxRawDistanceToTriggerELCInMeter = 15.0;

  const double ego_object_distance =
      leading_object.l2_distance_to_ego_m() - leading_object.length() * 0.5;
  const bool is_leading_object_far_from_ego =
      ego_object_distance > ttc_based_distance &&
      ego_object_distance > raw_speed_based_distance &&
      ego_object_distance > kMaxRawDistanceToTriggerELCInMeter;

  debug_oss << "[IsLeadingObjectFarFromEgo] " << is_leading_object_far_from_ego
            << "\n"
            << DUMP_TO_STREAM(ego_object_distance) << "\n"
            << DUMP_TO_STREAM(ttc_based_distance) << "\n"
            << DUMP_TO_STREAM(raw_speed_based_distance) << "\n";
  return is_leading_object_far_from_ego;
}

// TODO(Zhaorui): Add a UT for CalculateVehicleSpeedScore.
double CalculateVehicleSpeedScore(
    const double ego_speed, const PlannerObject& leading_object,
    const double leading_object_projected_speed,
    const selection::TrajectoryMetaData& candidate,
    const std::vector<const PlannerObject*>& /*vehicles_on_lane_sequence*/,
    const double min_remaining_lane_change_distance,
    const double slow_vehicle_ratio_in_left_lane_sequence,
    const double slow_vehicle_ratio_in_right_lane_sequence,
    const bool should_avoid_bus_bulb, const bool is_night_scene,
    const double local_lane_change_speed_threshold,
    const bool is_use_for_normal_elc, std::ostringstream& debug_oss) {
  if (leading_object.object_type() != voy::perception::ObjectType::VEHICLE) {
    return kMinElectiveLaneChangeScore;
  }

  debug_oss << "\n[VehicleSpeedScore]\n";
  if (should_avoid_bus_bulb) {
    debug_oss << "Skip checking speed to avoid the bus bulb.\n";
    return kMaxElectiveLaneChangeScore;
  }

  const bool is_vehicle_at_required_speed = IsVehicleAtRequiredSpeed(
      leading_object, leading_object_projected_speed, candidate.current_lane(),
      min_remaining_lane_change_distance, is_night_scene,
      local_lane_change_speed_threshold, is_use_for_normal_elc, debug_oss);
  const bool is_vehicle_slow_in_neighbor_lane_sequence =
      IsVehicleSlowInNeighborLaneSequence(
          slow_vehicle_ratio_in_left_lane_sequence,
          slow_vehicle_ratio_in_right_lane_sequence, debug_oss);
  const bool is_slow_moving_sanitation =
      IsSlowMovingSanitation(leading_object, debug_oss);
  const bool is_leading_object_far_from_ego = IsLeadingObjectFarFromEgo(
      ego_speed, leading_object, leading_object_projected_speed, debug_oss);
  const double vehicle_speed_score =
      ((is_vehicle_at_required_speed || is_slow_moving_sanitation) &&
       !is_vehicle_slow_in_neighbor_lane_sequence &&
       !is_leading_object_far_from_ego)
          ? kMaxElectiveLaneChangeScore
          : kMinElectiveLaneChangeScore;

  debug_oss << DUMP_TO_STREAM(vehicle_speed_score) << "\n";
  return vehicle_speed_score;
}

double CalculatePedestrianSpeedScore(const PlannerObject& object) {
  if (object.object_type() == voy::perception::ObjectType::PED) {
    return kMaxElectiveLaneChangeScore;
  }
  return kMinElectiveLaneChangeScore;
}

bool IsBlockDurationEnough(const SpeedWorldModel& world_model,
                           const PlannerObject& object,
                           const double object_projected_speed,
                           const int64_t current_timestamp,
                           const int64_t block_start_timestamp,
                           const bool should_avoid_bus_bulb,
                           std::ostringstream& debug_oss) {
  const double duration_threshold =
      ComputeBlockDurationThreshold(world_model, object, object_projected_speed,
                                    should_avoid_bus_bulb, debug_oss);

  const int64_t block_duration = current_timestamp - block_start_timestamp;
  debug_oss << "\n[DebugTimeScore]\n"
            << DUMP_TO_STREAM(current_timestamp) << "\n"
            << DUMP_TO_STREAM(block_start_timestamp) << "\n"
            << "accumulated duration: " << block_duration << "/"
            << duration_threshold << "\n";

  return block_duration >= duration_threshold;
}

double CalculateProgressSavingScore(double cyclist_or_unknown_speed_score,
                                    double vehicle_speed_score,
                                    double pedestrian_speed_score) {
  return std::max({cyclist_or_unknown_speed_score, vehicle_speed_score,
                   pedestrian_speed_score});
}

double CalculateCongestionRisk(
    const double min_ego_remaining_lane_change_distance,
    const PlannerObject& leading_object,
    const double leading_object_projected_speed,
    const std::vector<const PlannerObject*>&
        non_leaving_blocking_objects_on_lane_sequence,
    const bool is_congestion_in_neighbor_lane, const bool should_avoid_bus_bulb,
    const std::vector<math::Range1d>& visibility_ranges,
    std::ostringstream& debug_oss) {
  debug_oss << "\n[CongestionRisk]\n";
  if (should_avoid_bus_bulb) {
    debug_oss << "Skip checking congestion to avoid the bus bulb.\n";
    return kMinElectiveLaneChangeScore;
  }

  const bool has_congestion_ahead = HasCongestionAhead(
      non_leaving_blocking_objects_on_lane_sequence, leading_object,
      leading_object_projected_speed, min_ego_remaining_lane_change_distance,
      visibility_ranges, debug_oss);
  const bool has_congestion_risk =
      has_congestion_ahead || is_congestion_in_neighbor_lane;
  debug_oss << DUMP_TO_STREAM(has_congestion_ahead) << "\n"
            << DUMP_TO_STREAM(is_congestion_in_neighbor_lane) << "\n"
            << DUMP_TO_STREAM(has_congestion_risk);

  return has_congestion_risk ? kMaxElectiveLaneChangeScore
                             : kMinElectiveLaneChangeScore;
}

// TODO(Zhaorui) : Add a UT for the function.
std::pair<double, pb::ELCDirection>
CalculateDistanceRiskAndELCDirectionByRoutingMessage(
    const SpeedWorldModel& world_model, const PlannerObject& object,
    const double object_projected_speed,
    const std::optional<double>& left_elc_remaining_distance,
    const std::optional<double>& right_elc_remaining_distance,
    const pb::ELCDirection& elc_direction_by_cut_in_avoidance_result,
    const double left_dynamic_distance_threshold,
    const double right_dynamic_distance_threshold,
    const bool right_elc_has_merge,
    const bool should_avoid_bus_bulb_with_left_elc,
    const bool should_avoid_bus_bulb_with_right_elc,
    std::ostringstream& debug_oss) {
  debug_oss << "\n[DistanceRiskAndELCDirection]\n";

  // Skip checking to avoid bus bulb ahead, but only allow left LC.
  if (should_avoid_bus_bulb_with_left_elc ||
      should_avoid_bus_bulb_with_right_elc) {
    debug_oss << "Should avoid bus bulb. No risk.\n";

    if (should_avoid_bus_bulb_with_left_elc &&
        should_avoid_bus_bulb_with_right_elc) {
      return std::make_pair(kMinElectiveLaneChangeScore,
                            pb::ELCDirection::EITHER);
    }
    if (should_avoid_bus_bulb_with_left_elc) {
      return std::make_pair(kMinElectiveLaneChangeScore,
                            pb::ELCDirection::LEFT);
    }
    return std::make_pair(kMinElectiveLaneChangeScore, pb::ELCDirection::RIGHT);
  }

  // Avoid triggering ELC when agent has little impact on object's speed in the
  // near future.
  constexpr double kMaxTTCToAgentInSec = 7.0;
  constexpr double kMaxTimeToAgentInSec = 4.0;
  constexpr double kMaxRawDistToAgentInMeter = 30.0;
  const double ego_speed =
      world_model.robot_state().plan_init_state_snapshot().speed();
  const double ego_object_speed_diff = ego_speed - object_projected_speed;
  const double object_distance_to_ego = object.l2_distance_to_ego_m();
  const bool is_agent_within_max_ttc =
      object_distance_to_ego < (ego_object_speed_diff * kMaxTTCToAgentInSec);
  const bool is_agent_within_max_time =
      object_distance_to_ego < ego_speed * kMaxTimeToAgentInSec;
  const bool is_agent_within_max_raw_dist =
      object_distance_to_ego < kMaxRawDistToAgentInMeter;
  debug_oss << DUMP_TO_STREAM(is_agent_within_max_ttc) << "\n"
            << DUMP_TO_STREAM(is_agent_within_max_time) << "\n"
            << DUMP_TO_STREAM(is_agent_within_max_raw_dist) << "\n";
  if (!is_agent_within_max_ttc && !is_agent_within_max_time &&
      !is_agent_within_max_raw_dist) {
    debug_oss << "Agent is far from ego. Has risk.\n";
    return std::make_pair(kMaxElectiveLaneChangeScore,
                          pb::ELCDirection::EITHER);
  }

  // Check distance risk by ELC direction.
  // TODO(anwu): Consider merge risk for left LC.
  const bool no_distance_risk_for_left_lc =
      left_elc_remaining_distance.has_value() &&
      left_elc_remaining_distance.value() - object_distance_to_ego >
          left_dynamic_distance_threshold;
  const bool no_distance_risk_for_right_lc =
      right_elc_remaining_distance.has_value() &&
      right_elc_remaining_distance.value() - object_distance_to_ego >
          right_dynamic_distance_threshold;
  const bool no_merge_risk_for_right_lc = !right_elc_has_merge;
  const bool no_risk_avoidance_risk_for_left_lc =
      elc_direction_by_cut_in_avoidance_result == pb::ELCDirection::LEFT ||
      elc_direction_by_cut_in_avoidance_result == pb::ELCDirection::EITHER;
  const bool no_risk_avoidance_risk_for_right_lc =
      elc_direction_by_cut_in_avoidance_result == pb::ELCDirection::RIGHT ||
      elc_direction_by_cut_in_avoidance_result == pb::ELCDirection::EITHER;
  const bool no_risk_for_left_lc =
      no_distance_risk_for_left_lc && no_risk_avoidance_risk_for_left_lc;
  const bool no_risk_for_right_lc = no_distance_risk_for_right_lc &&
                                    no_merge_risk_for_right_lc &&
                                    no_risk_avoidance_risk_for_right_lc;

  debug_oss << DUMP_TO_STREAM(no_risk_for_left_lc) << "\n"
            << DUMP_TO_STREAM(no_risk_for_right_lc) << "\n"
            << DUMP_TO_STREAM(no_distance_risk_for_left_lc) << "\n"
            << DUMP_TO_STREAM(no_distance_risk_for_right_lc) << "\n"
            << DUMP_TO_STREAM(no_merge_risk_for_right_lc) << "\n"
            << DUMP_TO_STREAM(no_risk_avoidance_risk_for_left_lc) << "\n"
            << DUMP_TO_STREAM(no_risk_avoidance_risk_for_right_lc) << "\n";

  if (no_risk_for_left_lc && no_risk_for_right_lc) {
    return std::make_pair(kMinElectiveLaneChangeScore,
                          pb::ELCDirection::EITHER);
  }
  if (no_risk_for_left_lc) {
    return std::make_pair(kMinElectiveLaneChangeScore, pb::ELCDirection::LEFT);
  }
  if (no_risk_for_right_lc) {
    return std::make_pair(kMinElectiveLaneChangeScore, pb::ELCDirection::RIGHT);
  }
  return std::make_pair(kMaxElectiveLaneChangeScore, pb::ELCDirection::EITHER);
}

double CalculateNormalizedRisk(double congestion_risk, double distance_risk) {
  return std::max(congestion_risk, distance_risk);
}

double CalculateNormalizedELCScore(double progress_saving_score,
                                   double normalized_risk) {
  return progress_saving_score - normalized_risk;
}

std::pair<bool, bool> ShouldAvoidBusBulb(
    const selection::TrajectoryMetaData& candidate,
    const math::geometry::Point2d& ego_ra,
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const std::vector<const PlannerObject*>& objects_on_lane_sequence,
    const std::optional<double>& left_elc_remaining_distance,
    const std::optional<double>& right_elc_remaining_distance,
    std::ostringstream& debug_oss) {
  const std::optional<math::Range1d>& ra_arclength_range_to_bus_bulb =
      GetNearestBusBulbRangeFromRA(
          CHECK_NOTNULL(candidate.trajectory_info)->traffic_rules().zones);
  if (!ra_arclength_range_to_bus_bulb.has_value()) {
    return std::make_pair(false, false);
  }

  debug_oss << "\n[ShouldAvoidBusBulb]\n";
  debug_oss << "Bus bulb at (" << ra_arclength_range_to_bus_bulb->start_pos
            << ", " << ra_arclength_range_to_bus_bulb->end_pos << ").\n";

  // Find the first lane in the bus bulb range.
  const LaneFollowSequenceCurve& lane_sequence_curve =
      LaneFollowSequenceCurve(lane_sequence);
  const double ego_ra_arc_length_on_lane_sequence_curve =
      lane_sequence_curve.nominal_path()
          .GetProximity(ego_ra, math::pb::UseExtensionFlag::kAllow)
          .arc_length;
  const pnc_map::Lane* bus_bulb_lane = nullptr;
  double distance_to_bus_bulb_start = ego_ra_arc_length_on_lane_sequence_curve +
                                      ra_arclength_range_to_bus_bulb->start_pos;
  for (const auto* lane : lane_sequence) {
    // We use a small negative buffer to avoid getting the lane before the bus
    // lane. This buffer should not be longer than a bus bulb.
    constexpr double kBusLaneSearchBufferInMeter = -5.0;
    if (distance_to_bus_bulb_start - lane->length() <
        kBusLaneSearchBufferInMeter) {
      bus_bulb_lane = lane;
      break;
    }
    distance_to_bus_bulb_start -= lane->length();
  }
  if (bus_bulb_lane == nullptr) {
    debug_oss << "Bus bulb lane not found.\n";
    return std::make_pair(false, false);
  }

  // Check if the bus bulb lane is the rightmost lane. If not, don't trigger if
  // there are no bus zones on the lane.
  if (!bus_bulb_lane->IsRightmostDrivableLane()) {
    const auto& bus_bulb_lane_zone_infos = bus_bulb_lane->zones();
    if (!std::any_of(bus_bulb_lane_zone_infos.begin(),
                     bus_bulb_lane_zone_infos.end(), [](const auto& zone_info) {
                       const pnc_map::Zone* zone = zone_info.zone;
                       return zone != nullptr &&
                              (zone->type() == hdmap::Zone::DIRECT_BUS_BULB ||
                               zone->type() == hdmap::Zone::BUS_BULB);
                     })) {
      debug_oss << "No bus zones on the bus bulb lane.\n";
      return std::make_pair(false, false);
    }
  }

  // Ignore far-away bus bulbs.
  constexpr double kBusBulbSearchDistInMeter = 150.0;
  if (ra_arclength_range_to_bus_bulb->start_pos > kBusBulbSearchDistInMeter) {
    debug_oss << "Bus bulb too far away.\n";
    return std::make_pair(false, false);
  }

  // Ego already in bus bulb.
  if (ra_arclength_range_to_bus_bulb->start_pos < -math::constants::kEpsilon) {
    debug_oss << "Ego already in the bulb.\n";
    return std::make_pair(false, false);
  }

  // If a junction is between the bus bulb and ego, don't trigger ELC.
  const double ego_distance_to_junction =
      GetDistanceToJunction(/*contour=*/std::nullopt, ego_ra,
                            candidate.current_lane(), lane_sequence);
  if (ra_arclength_range_to_bus_bulb->start_pos > ego_distance_to_junction) {
    debug_oss << "Junction between bus bulb and ego.\n";
    return std::make_pair(false, false);
  }

  // See if there are any bus on or before the nearest bus bulb.
  std::vector<const PlannerObject*> buses_on_or_before_bus_bulb;
  for (const auto& object : objects_on_lane_sequence) {
    if (!IsObjectBus(object->tracked_object())) {
      continue;
    }
    if (object->l2_distance_to_ego_m() >
        ra_arclength_range_to_bus_bulb->end_pos) {
      continue;
    }
    buses_on_or_before_bus_bulb.emplace_back(object);
  }
  if (buses_on_or_before_bus_bulb.empty()) {
    debug_oss << "No buses on or before the bus bulb.\n";
    return std::make_pair(false, false);
  }
  debug_oss << DUMP_ITERABLE_POINTS_TO_STREAM_WITH_FIELD(
                   buses_on_or_before_bus_bulb, id())
            << "\n";

  // See if any bus is stopped or decelerating from a moderate speed.
  const bool any_stopped_or_decelerating_bus = std::any_of(
      buses_on_or_before_bus_bulb.begin(), buses_on_or_before_bus_bulb.end(),
      [&debug_oss](const PlannerObject* bus) {
        constexpr double kNearStoppedBusMaxSpeedInMps = 2.0;
        constexpr double kDeceleratingBusMaxSpeedInMps = 8.33;
        const bool result = bus->speed() < kNearStoppedBusMaxSpeedInMps ||
                            (bus->acceleration() < -math::constants::kEpsilon &&
                             bus->speed() < kDeceleratingBusMaxSpeedInMps);
        if (result) {
          debug_oss << "Bus " << bus->id() << " is stopped or decelerating. "
                    << DUMP_TO_STREAM(bus->speed()) << ", "
                    << DUMP_TO_STREAM(bus->acceleration()) << "\n";
        }
        return result;
      });
  if (!any_stopped_or_decelerating_bus) {
    return std::make_pair(false, false);
  }

  // Only avoid the bus bulb if there are enough global remaining distance after
  // the bus bulb.
  constexpr double kMinGlobalRemainingDistanceToTriggerELCInMeter = 80.0;
  const bool should_avoid_bus_bulb_with_left_elc =
      left_elc_remaining_distance.has_value() &&
      left_elc_remaining_distance.value() -
              ra_arclength_range_to_bus_bulb->end_pos >
          kMinGlobalRemainingDistanceToTriggerELCInMeter;
  const bool should_avoid_bus_bulb_with_right_elc =
      right_elc_remaining_distance.has_value() &&
      right_elc_remaining_distance.value() -
              ra_arclength_range_to_bus_bulb->end_pos >
          kMinGlobalRemainingDistanceToTriggerELCInMeter;
  return std::make_pair(should_avoid_bus_bulb_with_left_elc,
                        should_avoid_bus_bulb_with_right_elc);
}

std::vector<const PlannerObject*> GetObjectsInLaneSequence(
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const std::vector<const pnc_map::Lane*>& lane_sequence) {
  std::vector<const PlannerObject*> objects_in_lane_sequence;
  objects_in_lane_sequence.reserve(planner_object_map.size());
  constexpr double kMinInterAreaToAgentAreaRatio = 0.2;
  for (const auto& [_, planner_object] : planner_object_map) {
    const std::vector<const pnc_map::Lane*>& associated_lanes =
        planner_object.associated_lanes();
    if (associated_lanes.empty()) {
      continue;
    }

    // Consider the agent as on the lane unless most of its area is outside of
    // the lane.
    const auto& agent_box = planner_object.pose().oriented_box();
    const double agent_area = agent_box.Area();
    for (const auto* associated_lane : associated_lanes) {
      // Check if any of its associated lane is in the lane sequence.
      if (!std::any_of(lane_sequence.begin(), lane_sequence.end(),
                       [associated_lane](const pnc_map::Lane* lane) {
                         return associated_lane->id() == lane->id();
                       })) {
        continue;
      }

      const double intersection_area = math::geometry::IntersectionArea(
          agent_box, associated_lane->border());
      if (intersection_area / std::max(math::constants::kEpsilon, agent_area) <
          kMinInterAreaToAgentAreaRatio) {
        continue;
      }

      objects_in_lane_sequence.push_back(&planner_object);
      break;
    }
  }
  objects_in_lane_sequence.shrink_to_fit();
  return objects_in_lane_sequence;
}

int GetSlowObjectCount(
    const std::vector<const PlannerObject*>& planner_objects) {
  int slow_object_num = 0;
  for (const PlannerObject* planner_object : planner_objects) {
    if (planner_object->speed() < kSlowMovingVehicleSpeedInNeighborLaneInMps) {
      slow_object_num++;
    }
  }
  return slow_object_num;
}

bool IsCongestionInNeighborLane(
    const int left_neighbor_vehicles_num, const int right_neighbor_vehicles_num,
    const int left_neighbor_slow_vehicles_num,
    const int right_neighbor_slow_vehicles_num,
    const bool is_left_neighbor_lane_sequence_empty,
    const bool is_right_neighbor_lane_sequence_empty,
    const pb::ELCDirection& elc_direction_by_distance_risk,
    std::ostringstream& debug_oss) {
  const bool left_lane_congestion =
      !is_left_neighbor_lane_sequence_empty &&
      (left_neighbor_slow_vehicles_num >=
           kRequiredNumberOfSlowVehiclesInNeighborLane ||
       left_neighbor_vehicles_num >= kRequiredNumberOfVehiclesInNeighborLane);
  const bool right_lane_congestion =
      !is_right_neighbor_lane_sequence_empty &&
      (right_neighbor_slow_vehicles_num >=
           kRequiredNumberOfSlowVehiclesInNeighborLane ||
       right_neighbor_vehicles_num >= kRequiredNumberOfVehiclesInNeighborLane);
  debug_oss << "\n[Congestion]\n"
            << DUMP_TO_STREAM(left_lane_congestion) << "\n"
            << DUMP_TO_STREAM(right_lane_congestion) << "\n";
  return (elc_direction_by_distance_risk == pb::ELCDirection::LEFT &&
          left_lane_congestion) ||
         (elc_direction_by_distance_risk == pb::ELCDirection::RIGHT &&
          right_lane_congestion) ||
         (elc_direction_by_distance_risk == pb::ELCDirection::EITHER &&
          left_lane_congestion && right_lane_congestion);
}

std::optional<math::Range1d> GetNearestBusBulbRangeFromRA(
    const std::vector<speed::traffic_rules::ZoneInLaneSequence>&
        intersected_zone_in_lane_vector) {
  DCHECK(std::is_sorted(intersected_zone_in_lane_vector.begin(),
                        intersected_zone_in_lane_vector.end(),
                        [](const auto& lfs, const auto& rhs) {
                          return lfs.start_ra_arclength <
                                 rhs.start_ra_arclength;
                        }))
      << "intersected_zone_in_lane_vector is not sorted.";

  const auto nearest_bus_bulb_iter = std::find_if(
      intersected_zone_in_lane_vector.begin(),
      intersected_zone_in_lane_vector.end(), [](const auto& zone) {
        return (zone.zone_ptr->type() == hdmap::Zone::BUS_BULB ||
                zone.zone_ptr->type() == hdmap::Zone::DIRECT_BUS_BULB) &&
               zone.end_ra_arclength > math::constants::kEpsilon;
      });

  if (nearest_bus_bulb_iter == intersected_zone_in_lane_vector.end()) {
    return std::nullopt;
  }

  return math::Range1d{nearest_bus_bulb_iter->start_ra_arclength,
                       nearest_bus_bulb_iter->end_ra_arclength};
}

// TODO(Zhaorui) : Move this to planner_object.h so everyone else can use this
// as well.
bool IsObjectBus(const voy::TrackedObject& tracked_object) {
  return std::any_of(tracked_object.attributes().begin(),
                     tracked_object.attributes().end(), [](const auto& attr) {
                       return attr == voy::perception::Attribute::VEHICLE_BUS;
                     });
}

math::geometry::MultiPolyline2d GetIntersectionFromVisibilityPolygon(
    const SpeedWorldModel& world_model,
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const bool is_left_side_boundary, std::ostringstream& debug_oss) {
  math::geometry::MultiPolyline2d intersection_curves;
  // Gets the visibility polygon
  const math::geometry::PolygonWithCache2d& visibility_polygon =
      world_model.occlusion_checker()
          .occlusion_grid_visibility_polygon_for_lane_change();

  if (visibility_polygon.size() == 0) {
    debug_oss << "[VisibilityPolygon] is empty."
              << "\n";
    return intersection_curves;
  }

  // Gets the side boundary
  const math::geometry::PolylineCurve2d side_boundary =
      lane_selection::GetLaneSequenceCurve(
          lane_sequence, is_left_side_boundary
                             ? lane_selection::LaneCurveType::kLeftBoundary
                             : lane_selection::LaneCurveType::kRightBoundary);

  // Gets the intersection line strings
  math::geometry::Intersection(visibility_polygon, side_boundary,
                               intersection_curves);
  return intersection_curves;
}

bool IsNearPointPolyline(const math::geometry::Polyline2d& intersection_curve) {
  if (intersection_curve.size() != 2) {
    return false;
  }
  const double dist_between_points =
      std::pow((intersection_curve[0].x() - intersection_curve[1].x()), 2) +
      std::pow((intersection_curve[0].y() - intersection_curve[1].y()), 2);
  return dist_between_points < kMinimumDistanceBetweenTwoPointsInPolyline;
}

double ComputeVisibilityWidth(
    const SpeedWorldModel& /* world_model */,
    const math::geometry::MultiPolyline2d& left_intersection_curves,
    const math::geometry::MultiPolyline2d& right_intersection_curves,
    const math::geometry::PolylineCurve2d& center_line,
    const double visibility_length, const double ego_arc_length,
    std::ostringstream& debug_oss) {
  const math::geometry::Point2d aim_point =
      center_line.GetInterp(visibility_length + ego_arc_length);

  std::vector<double> dist_to_left_side_boundaries;
  for (const auto& left_intersection_curve : left_intersection_curves) {
    if (IsNearPointPolyline(left_intersection_curve)) {
      continue;
    }
    const math::geometry::PolylineCurve2d& left_intersection_polyline_curve =
        math::geometry::PolylineCurve2d(left_intersection_curve,
                                        /*filter_points=*/true);
    if (left_intersection_polyline_curve.size() < 2) {
      continue;
    }
    const auto& proximity = left_intersection_polyline_curve.GetProximity(
        aim_point, math::pb::UseExtensionFlag::kForbid);
    if (proximity.relative_position == math::RelativePosition::kWithIn) {
      const double dist_to_left_side_boundary = proximity.dist;
      debug_oss << "left: " << dist_to_left_side_boundary << " ";
      dist_to_left_side_boundaries.push_back(dist_to_left_side_boundary);
    }
  }

  const double dist_to_left_side =
      dist_to_left_side_boundaries.size() != 0
          ? *std::min_element(dist_to_left_side_boundaries.begin(),
                              dist_to_left_side_boundaries.end())
          : 0.0;

  std::vector<double> dist_to_right_side_boundarys;
  for (const auto& right_intersection_curve : right_intersection_curves) {
    if (IsNearPointPolyline(right_intersection_curve)) {
      continue;
    }
    const math::geometry::PolylineCurve2d& right_intersection_polyline_curve =
        math::geometry::PolylineCurve2d(right_intersection_curve,
                                        /*filter_points=*/true);
    if (right_intersection_polyline_curve.size() < 2) {
      continue;
    }
    const auto& proximity = right_intersection_polyline_curve.GetProximity(
        aim_point, math::pb::UseExtensionFlag::kForbid);
    if (proximity.relative_position == math::RelativePosition::kWithIn) {
      const double dist_to_right_side_boundary = proximity.dist;
      debug_oss << "right: " << dist_to_right_side_boundary << " ";
      dist_to_right_side_boundarys.push_back(dist_to_right_side_boundary);
    }
  }

  const double dist_to_right_side =
      dist_to_right_side_boundarys.size() != 0
          ? *std::min_element(dist_to_right_side_boundarys.begin(),
                              dist_to_right_side_boundarys.end())
          : 0.0;

  const double visibility_width = dist_to_left_side + dist_to_right_side;
  debug_oss << DUMP_TO_STREAM(visibility_width) << "\n";
  return visibility_width;
}

std::vector<VisibilityInfo> ComputeVisibilityInfos(
    const SpeedWorldModel& world_model,
    const math::geometry::MultiPolyline2d& left_intersection_curves,
    const math::geometry::MultiPolyline2d& right_intersection_curves,
    const math::geometry::PolylineCurve2d& center_line,
    const double ego_arc_length, std::ostringstream& debug_oss) {
  VisibilityInfo visibility_length_width;
  std::vector<VisibilityInfo> visibility_length_width_info;
  for (int i = 0;
       i <= kVisibilityLengthDistInMeter / kVisibilitySamplingDistInMeter;
       i++) {
    visibility_length_width.arc_length = i * kVisibilitySamplingDistInMeter;
    visibility_length_width.visibility_width_at_arc_length =
        ComputeVisibilityWidth(world_model, left_intersection_curves,
                               right_intersection_curves, center_line,
                               visibility_length_width.arc_length,
                               ego_arc_length, debug_oss);
    visibility_length_width_info.push_back(visibility_length_width);
  }
  return visibility_length_width_info;
}

std::vector<VisibilityInfo> ComputeVisibilityInfosForELC(
    const SpeedWorldModel& world_model,
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    std::ostringstream& debug_oss) {
  TRACE_EVENT_SCOPE(planner,
                    ElectiveLaneChangeDecider_ComputeVisibilityInfosForELC);
  // Gets the intersection linestrings by visibility polygon and side boundary
  const math::geometry::MultiPolyline2d& left_intersection_curves =
      GetIntersectionFromVisibilityPolygon(world_model, lane_sequence,
                                           /*is_left_side_boundary=*/true,
                                           debug_oss);
  const math::geometry::MultiPolyline2d& right_intersection_curves =
      GetIntersectionFromVisibilityPolygon(world_model, lane_sequence,
                                           /*is_right_side_boundary=*/false,
                                           debug_oss);

  const math::geometry::PolylineCurve2d center_line =
      lane_selection::GetLaneSequenceCurve(
          lane_sequence, lane_selection::LaneCurveType::kCenterLine);

  const math::geometry::Point2d ego_rear_axle(
      world_model.robot_state().plan_init_state_snapshot().x(),
      world_model.robot_state().plan_init_state_snapshot().y());
  const double ego_arc_length =
      center_line
          .GetProximity(ego_rear_axle, math::pb::UseExtensionFlag::kForbid)
          .arc_length;

  return ComputeVisibilityInfos(world_model, left_intersection_curves,
                                right_intersection_curves, center_line,
                                ego_arc_length, debug_oss);
}

lane_selection::PreviewLanePoint GetAimLaneAndOffsetForPreviewOnrouteResult(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const double ego_arc_length_m, const double preview_buffer_m) {
  lane_selection::PreviewLanePoint aim_lane_and_offset;
  const double preview_arc_length_m = ego_arc_length_m + preview_buffer_m;
  double sum_length = 0.0;
  for (const auto& lane : lane_sequence) {
    sum_length += lane->length();
    if (sum_length > preview_arc_length_m) {
      aim_lane_and_offset.lane = lane;
      aim_lane_and_offset.arc_length =
          preview_arc_length_m - (sum_length - lane->length());
      return aim_lane_and_offset;
    }
  }

  return aim_lane_and_offset;
}

std::optional<lane_selection::PreviewRouteResult> GetELCRoutePreview(
    const SpeedWorldModel& world_model,
    const lane_selection::LaneSequenceCandidates& lane_sequence_candidates,
    const std::vector<const pnc_map::Lane*>& neighbor_lane_sequence,
    const bool get_left_preview_onroute_result,
    const math::geometry::Point2d& ego_ra, std::ostringstream& debug_oss) {
  TRACE_EVENT_SCOPE(planner, ElectiveLaneChangeDecider_GetPreviewOnrouteResult);
  // Use neighbor lane sequence's end as lane point in query.
  if (neighbor_lane_sequence.empty()) {
    debug_oss << __func__ << ": neighbor lane sequence is empty.\n";
    return std::nullopt;
  }

  const math::geometry::PolylineCurve2d& neighbor_center_line =
      lane_selection::GetLaneSequenceCurve(
          neighbor_lane_sequence, lane_selection::LaneCurveType::kCenterLine);

  const double ego_arc_length =
      neighbor_center_line
          .GetProximity(ego_ra, math::pb::UseExtensionFlag::kForbid)
          .arc_length;

  const lane_selection::PreviewLanePoint lane_point_to_pass =
      GetAimLaneAndOffsetForPreviewOnrouteResult(
          neighbor_lane_sequence, ego_arc_length,
          /*preview_buffer_m=*/kPreviewedRouteInfoAimLaneDistInMeter);

  if (lane_point_to_pass.lane == nullptr) {
    debug_oss << "\n[Onroute] aim lane is empty\n";
    return std::nullopt;
  }

  const std::string direction =
      get_left_preview_onroute_result ? "left - " : "right - ";

  pb::PreviewRouteDebug debug;
  const std::optional<lane_selection::PreviewRouteResult>&
      preview_route_result = lane_sequence_candidates.GetPreviewRouteResult(
          *world_model.pnc_map_service(), world_model.regional_map(),
          *world_model.global_route_solution(),
          lane_point_to_pass, /*compared_sequence_type=*/
          lane_selection::PreviewRequestOptimalSequenceType::kOptimalLaneFollow,
          lane_selection::PreviewRouteSearchStrategy::kDefaultByMinCost,
          &debug);

  if (!preview_route_result.has_value()) {
    debug_oss << "\n[Onroute] No result from interface of " << direction
              << " GetELCRoutePreview.\n";
    return std::nullopt;
  }

  if (!preview_route_result.value().on_route_preview_route_result.has_value()) {
    debug_oss << "\n[Onroute] No " << direction
              << " on route preview route result.\n";
    return std::nullopt;
  }

  return preview_route_result;
}

std::optional<pb::LaneChangeInstanceDetail> GetBackwardLCInstanceDetail(
    const SpeedWorldModel& world_model,
    const lane_selection::LaneSequenceCandidates& lane_sequence_candidates,
    const pb::PlanningSegmentSequence& planning_segment_sequence,
    const std::vector<const pnc_map::Lane*>& neighbor_lane_sequence,
    const bool is_left_lane_change, std::ostringstream& debug_oss) {
  // Find the source lane and target lane ID of the first lane change in the
  // intended direction.
  std::optional<int64_t> source_lane_id;
  std::optional<int64_t> target_lane_id;
  std::optional<double> target_lane_start_arc_length;
  for (const auto& planning_segment :
       planning_segment_sequence.planning_segments()) {
    if (planning_segment.has_lane_change_segment() &&
        planning_segment.lane_change_segment().lane_change_direction() ==
            (is_left_lane_change ? pb::LaneChangeMode::LEFT_LANE_CHANGE
                                 : pb::LaneChangeMode::RIGHT_LANE_CHANGE)) {
      DCHECK_GT(
          planning_segment.lane_change_segment().lc_source_segments_size(), 0);
      DCHECK_GT(
          planning_segment.lane_change_segment().lc_target_segments_size(), 0);
      source_lane_id = planning_segment.lane_change_segment()
                           .lc_source_segments(0)
                           .lane_id();
      target_lane_start_arc_length = planning_segment.lane_change_segment()
                                         .lc_target_segments(0)
                                         .start_arclength();
      target_lane_id = planning_segment.lane_change_segment()
                           .lc_target_segments(0)
                           .lane_id();
      break;
    }
  }

  if (!source_lane_id.has_value() || !target_lane_id.has_value() ||
      !target_lane_start_arc_length.has_value()) {
    return std::nullopt;
  }

  // The source and target lanes are flipped for the backward lane change.
  debug_oss << "GetLaneChangeInstanceDetail: "
            << (is_left_lane_change ? "Right" : "Left")
            << " LC. source_lane: " << target_lane_id.value()
            << ", target_lane: " << source_lane_id.value() << ", ";

  // Skip if the source lane isn't on the neighbor lane sequence.
  if (!std::any_of(neighbor_lane_sequence.begin(), neighbor_lane_sequence.end(),
                   [&target_lane_id](const pnc_map::Lane* lane) {
                     return lane->id() == target_lane_id.value();
                   })) {
    debug_oss << "source lane not in the neighbor lane sequence.\n";
    return std::nullopt;
  }

  // Computes the lane change start position's arc length percentage on the
  // target lane.
  const pnc_map::Lane* target_lane =
      world_model.pnc_map_service()->GetLaneById(target_lane_id.value());
  DCHECK(target_lane != nullptr);
  const double target_lane_length = target_lane->length();
  const double target_lane_start_arc_length_percentage =
      math::Clamp(target_lane_start_arc_length.value() /
                      std::max(target_lane_length, math::constants::kEpsilon),
                  0.0, 1.0);

  debug_oss << "source_lane_percentage: "
            << target_lane_start_arc_length_percentage << "\n";

  // Use GetLaneChangeInstanceDetail to find the valid lane change distance for
  // the lane change from target lane to source lane (the backward LC).
  const pnc_map::Lane* source_lane =
      world_model.pnc_map_service()->GetLaneById(source_lane_id.value());

  return lane_sequence_candidates.GetLaneChangeInstanceDetail(
      world_model.regional_map(),
      *DCHECK_NOTNULL(world_model.global_route_solution()),
      world_model.drivable_lane_reasoner(), target_lane, source_lane,
      target_lane_start_arc_length_percentage, !is_left_lane_change);
}

// TODO(Zhaorui) : Add a UT for the function.
double GetDistanceToBackwardLcEnd(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const pb::PlanningSegmentSequence& planning_segment_sequence,
    const math::geometry::Point2d& ego_ra, const bool is_left_lane_change,
    std::ostringstream& debug_oss) {
  double distance_from_first_to_last_same_direction_lc_target_segment_length =
      0.0;
  double
      distance_from_first_to_last_opposite_direction_lc_target_segment_length =
          0.0;
  double distance_from_first_to_last_lf_segment_length = 0.0;
  double leading_lf_offset = 0.0;
  bool is_first_segment = true;
  pb::LaneChangeMode same_lane_change_direction =
      is_left_lane_change ? pb::LaneChangeMode::LEFT_LANE_CHANGE
                          : pb::LaneChangeMode::RIGHT_LANE_CHANGE;
  pb::LaneChangeMode opposite_lane_change_direction =
      is_left_lane_change ? pb::LaneChangeMode::RIGHT_LANE_CHANGE
                          : pb::LaneChangeMode::LEFT_LANE_CHANGE;

  debug_oss << "\n[GetDistanceToBackwardLcEnd]\n"
            << DUMP_TO_STREAM(is_left_lane_change) << "\n";

  std::vector<int64_t> leading_lf_sequence;
  for (const auto& planning_segment :
       planning_segment_sequence.planning_segments()) {
    if (planning_segment.has_lane_change_segment()) {
      break;
    }
    leading_lf_sequence.emplace_back(
        planning_segment.lane_follow_segment().lf_segment().lane_id());
  }

  for (const auto& planning_segment :
       planning_segment_sequence.planning_segments()) {
    // Calculate distance of opposite direction lc segment.
    if (planning_segment.has_lane_change_segment() &&
        planning_segment.lane_change_segment().lane_change_direction() ==
            opposite_lane_change_direction) {
      for (const auto& target_segment :
           planning_segment.lane_change_segment().lc_target_segments()) {
        distance_from_first_to_last_opposite_direction_lc_target_segment_length +=
            target_segment.end_arclength() - target_segment.start_arclength();
      }
      break;
    }

    // Calculate distance of lf segment.
    if (planning_segment.has_lane_follow_segment()) {
      const pb::Segment& lf_segment =
          planning_segment.lane_follow_segment().lf_segment();
      distance_from_first_to_last_lf_segment_length +=
          lf_segment.end_arclength() - lf_segment.start_arclength();
      // TODO(Zhaorui) : Find the consecutive LF segments before the first LC
      // segment.
      if (is_first_segment) {
        leading_lf_offset = lf_segment.start_arclength();
        is_first_segment = false;
      }
    }

    // Calculate distance of same direction lc segment.
    if (planning_segment.has_lane_change_segment() &&
        planning_segment.lane_change_segment().lane_change_direction() ==
            same_lane_change_direction) {
      for (const auto& target_segment :
           planning_segment.lane_change_segment().lc_target_segments()) {
        distance_from_first_to_last_same_direction_lc_target_segment_length +=
            target_segment.end_arclength() - target_segment.start_arclength();
      }
    }
  }

  const std::vector<const pnc_map::Lane*>& leading_lf_lane_sequence =
      joint_pnc_map_service.GetLaneSequence(leading_lf_sequence);
  bool is_leading_lf_sequence_valid = true;
  for (size_t i = 0; i + 1 < leading_lf_lane_sequence.size(); i++) {
    if (leading_lf_lane_sequence[i]->IsSuccessor(
            *leading_lf_lane_sequence[i + 1])) {
      continue;
    }
    is_leading_lf_sequence_valid = false;
    DCHECK(false) << "Invalid leading lf sequence.";
    break;
  }

  double ego_arc_length_on_planning_segment_sequence = 0.0;
  if (!leading_lf_sequence.empty() && is_leading_lf_sequence_valid) {
    const LaneFollowSequenceCurve& lf_and_first_lc_source_sequence_curve =
        LaneFollowSequenceCurve(leading_lf_lane_sequence);
    const double ego_arc_length_on_leading_lf_sequence =
        lf_and_first_lc_source_sequence_curve.nominal_path()
            .GetProximity(ego_ra, math::pb::kAllow)
            .arc_length;
    ego_arc_length_on_planning_segment_sequence =
        ego_arc_length_on_leading_lf_sequence - leading_lf_offset;
  }

  const double distance_to_last_lc_segment =
      distance_from_first_to_last_lf_segment_length +
      distance_from_first_to_last_same_direction_lc_target_segment_length +
      distance_from_first_to_last_opposite_direction_lc_target_segment_length -
      ego_arc_length_on_planning_segment_sequence;

  debug_oss << DUMP_TO_STREAM(distance_to_last_lc_segment) << "\n"
            << DUMP_TO_STREAM(planning_segment_sequence.DebugString()) << "\n";

  return distance_to_last_lc_segment;
}

std::optional<double> ComputeELCRemainingDistanceInRoutePreview(
    const SpeedWorldModel& world_model,
    const lane_selection::LaneSequenceCandidates& lane_sequence_candidates,
    const std::optional<lane_selection::PreviewRouteResult>&
        preview_reroute_result,
    const pnc_map::Lane& current_lane,
    const std::vector<const pnc_map::Lane*>& current_lane_sequence,
    const std::vector<const pnc_map::Lane*>& neighbor_lane_sequence,
    const math::geometry::Point2d& ego_ra, const bool is_left_lane_change,
    const double dist_to_destination, const bool is_on_highway,
    std::ostringstream& debug_oss) {
  if (!preview_reroute_result.has_value()) {
    return std::nullopt;
  }

  DCHECK(preview_reroute_result->on_route_preview_route_result.has_value());
  const lane_selection::PreviewRouteInfo& elc_preview_route_info =
      preview_reroute_result.value()
          .on_route_preview_route_result.value()
          .previewed_route_info;

  const pb::PlanningSegmentSequence& previewed_planning_segment_sequence =
      elc_preview_route_info.planning_segment_sequence;

  // Get remaining distance from route preview.
  const double remaining_distance_from_route_preview =
      GetDistanceToBackwardLcEnd(
          *CHECK_NOTNULL(world_model.joint_pnc_map_service()),
          previewed_planning_segment_sequence, ego_ra, is_left_lane_change,
          debug_oss);

  // Get remaining distance from global route.
  const std::optional<pb::LaneChangeInstanceDetail>&
      backward_lc_instance_detail = GetBackwardLCInstanceDetail(
          world_model, lane_sequence_candidates,
          previewed_planning_segment_sequence, neighbor_lane_sequence,
          is_left_lane_change, debug_oss);

  // The default value of valid_lane_change_distance is
  // std::numeric_limits<double>::max() as defined in
  // lane_sequence_candidates.cpp. We use 1e7 (10000 km) as a threshold check
  // its validity.
  constexpr double kMaxValidLaneChangeDistanceInMeter = 1e7;
  const double remaining_distance_for_backward_lc =
      (backward_lc_instance_detail.has_value() &&
       backward_lc_instance_detail->valid_lane_change_distance() >
           math::constants::kEpsilon &&
       backward_lc_instance_detail->valid_lane_change_distance() <
           kMaxValidLaneChangeDistanceInMeter)
          ? backward_lc_instance_detail->valid_lane_change_distance()
          : 0.0;

  // The default value of total_lane_change_times_to_next_turn_junction is
  // std::numeric_limits<int32_t>::max() as defined in
  // lane_sequence_candidates.cpp. We use 100 as a threshold to check its
  // validity.
  constexpr double kMaxLaneChangeTimesToNextTurnJunction = 100;
  const int num_lane_changes_from_source_lane =
      (backward_lc_instance_detail.has_value() &&
       backward_lc_instance_detail
               ->total_lane_change_times_to_next_turn_junction() > 0 &&
       backward_lc_instance_detail
               ->total_lane_change_times_to_next_turn_junction() <
           kMaxLaneChangeTimesToNextTurnJunction)
          ? backward_lc_instance_detail
                ->total_lane_change_times_to_next_turn_junction()
          : 0;

  // Reduce the remaining distance for each LC.
  const double current_lane_speed_limit = GetSpeedLimit(&current_lane);
  constexpr double kTimeBasedReductionPerLaneChangeInSec = 5.0;
  const double num_lane_changes_dist_reduction_for_global_route =
      num_lane_changes_from_source_lane *
      kTimeBasedReductionPerLaneChangeInSec * current_lane_speed_limit;

  // The valid lane change distance is based on lane and may be longer than the
  // actual lane change distance. In the short term, we subtract a buffer to
  // avoid triggering ELC when there isn't enough remaining distance.
  constexpr double kValidLaneChangeDistanceBufferInMeter = 50.0;

  const double remaining_distance_from_global_route =
      std::max(0.0, remaining_distance_for_backward_lc -
                        num_lane_changes_dist_reduction_for_global_route -
                        kValidLaneChangeDistanceBufferInMeter);

  // When close to destination, we subtract a buffer to take into account that
  // ego needs to slow down before reaching the destination.
  constexpr double kDestinationDistanceBufferInMeter = 50.0;
  const double num_lane_changes_dist_reduction_for_destination =
      std::max(0.0, (num_lane_changes_from_source_lane - 1) *
                        kTimeBasedReductionPerLaneChangeInSec *
                        current_lane_speed_limit);
  const double remaining_distance_to_destination =
      std::max(0.0, dist_to_destination - kDestinationDistanceBufferInMeter -
                        num_lane_changes_dist_reduction_for_destination);

  // Use the incentive to avoid ELC toward a blockage or bus zone.
  // TODO(anwu): Currently there seems to be no incentives in the backward LC
  // instance info. We need to work with routing to populate it.
  const std::vector<LaneChangeInstance>& lc_instances =
      elc_preview_route_info.lane_change_instances;
  pb::InstanceInfo backward_lc_instance_info;
  pb::InstanceInfo::UrgencyLevel backward_lc_urgency_level =
      pb::InstanceInfo::kUnknownUrgencyLevel;
  bool should_use_global_route_by_incentives = false;
  if (lc_instances.size() > 1) {
    backward_lc_instance_info = lc_instances.at(1).instance_info();
    backward_lc_urgency_level = backward_lc_instance_info.urgency_level();
    should_use_global_route_by_incentives = !std::any_of(
        backward_lc_instance_info.incentives().begin(),
        backward_lc_instance_info.incentives().end(),
        [](const pb::LaneChangeIncentive& incentive) {
          return incentive.type() == pb::LaneChangeIncentive::kHardBlock ||
                 incentive.type() == pb::LaneChangeIncentive::kSoftBlock ||
                 incentive.type() == pb::LaneChangeIncentive::kBusLane ||
                 incentive.type() == pb::LaneChangeIncentive::kBusBulb ||
                 incentive.type() == pb::LaneChangeIncentive::kDirectBusBulb;
        });
  }

  // When there is a merge ahead, don't use global route remaining distance.
  const bool elc_has_merge =
      IsMergeStructure(current_lane_sequence, &current_lane,
                       neighbor_lane_sequence, is_left_lane_change, debug_oss);

  debug_oss << "\n[ComputeELCRemainingDistanceInRoutePreview]\n"
            << DUMP_TO_STREAM(remaining_distance_from_route_preview) << "\n"
            << DUMP_TO_STREAM(remaining_distance_from_global_route) << "\n"
            << DUMP_TO_STREAM(remaining_distance_to_destination) << "\n"
            << DUMP_TO_STREAM(dist_to_destination) << "\n"
            << DUMP_TO_STREAM(num_lane_changes_dist_reduction_for_destination)
            << "\n"
            << DUMP_TO_STREAM(remaining_distance_for_backward_lc) << "\n"
            << DUMP_TO_STREAM(num_lane_changes_from_source_lane) << "\n"
            << DUMP_TO_STREAM(num_lane_changes_dist_reduction_for_global_route)
            << "\n"
            << "preview backward LC urgency: "
            << pb::InstanceInfo::UrgencyLevel_Name(backward_lc_urgency_level)
            << "\n"
            << DUMP_TO_STREAM(should_use_global_route_by_incentives) << "\n"
            << DUMP_TO_STREAM(elc_has_merge) << "\n";

  // Log incentives.
  debug_oss << "Backward LC incentives: ";
  for (const auto& incentive : backward_lc_instance_info.incentives()) {
    debug_oss << pb::LaneChangeIncentive::IncentiveType_Name(incentive.type())
              << " ";
  }
  debug_oss << "\n";

  const bool should_use_global_route =
      should_use_global_route_by_incentives && !elc_has_merge;
  debug_oss << DUMP_TO_STREAM(should_use_global_route) << "\n";

  return std::make_optional<double>(std::min(
      should_use_global_route &&
              FLAGS_planning_enable_elc_remaining_distance_with_global_route_on_local
          ? remaining_distance_to_destination
          : dist_to_destination,
      std::max(
          remaining_distance_from_route_preview,
          (should_use_global_route &&
           (is_on_highway ||
            FLAGS_planning_enable_elc_remaining_distance_with_global_route_on_local))
              ? remaining_distance_from_global_route
              : 0.0)));
}

std::vector<double> GetObjectsSpeedInLaneSequence(
    const std::vector<const PlannerObject*>& objects) {
  std::vector<double> objects_speed;
  objects_speed.reserve(objects.size());
  std::transform(objects.begin(), objects.end(),
                 std::back_inserter(objects_speed),
                 [](const auto* object) { return object->speed(); });
  return objects_speed;
}

double GetSlowMovingVehicleRatioInLaneSequence(
    const selection::TrajectoryMetaData& candidate,
    const std::vector<double>& objects_speed, double front_object_speed) {
  if (objects_speed.empty()) {
    return 0.0;
  }
  const double speed_limit = GetSpeedLimit(candidate.current_lane());
  int slow_vehicle_number = 0;
  for (const auto object_speed : objects_speed) {
    if (object_speed < front_object_speed + kSlowMovingVehicleDiffSpeedInMps ||
        object_speed < speed_limit * kSlowVehicleCompareSpeedLimitRatio) {
      slow_vehicle_number++;
    }
  }
  const double slow_vehicle_ratio =
      slow_vehicle_number * 1.0 / objects_speed.size();
  return slow_vehicle_ratio;
}

std::vector<math::Range1d> ComputeVisibilityRanges(
    const std::vector<VisibilityInfo>& visibility_infos,
    std::ostringstream& debug_oss) {
  debug_oss << "\n[ComputeVisibilityRanges]\n";

  if (visibility_infos.empty()) {
    debug_oss << "visibility_infos is empty.\n";
    return {};
  }

  std::vector<math::Range1d> visibility_ranges;
  int consecutive_visible_count = 0;
  double start_arc_length = 0.0;
  for (auto info_iter = visibility_infos.begin();
       info_iter != visibility_infos.end(); ++info_iter) {
    const auto& visibility_info = *info_iter;

    // Accumulate visible range.
    if (visibility_info.visibility_width_at_arc_length >
        kVisibilityWidthInLaneDistInMeter) {
      if (consecutive_visible_count == 0) {
        start_arc_length = visibility_info.arc_length;
      }
      consecutive_visible_count++;
      continue;
    }

    // Skip short visible ranges.
    if (consecutive_visible_count < 2) {
      consecutive_visible_count = 0;
      continue;
    }

    // Get the arc-length of the last visible range, and add it to the ranges.
    const auto& last_iter = std::prev(info_iter);
    const double end_arc_length = last_iter->arc_length;
    visibility_ranges.emplace_back(
        math::Range1d(start_arc_length, end_arc_length));
    consecutive_visible_count = 0;
  }
  // Add last visible range to ranges.
  if (consecutive_visible_count >= 2) {
    const double end_arc_length = visibility_infos.back().arc_length;
    visibility_ranges.emplace_back(
        math::Range1d(start_arc_length, end_arc_length));
  }

  debug_oss << SHORT_DUMP_ITERABLE_TO_STRING_WITH_FIELDS(visibility_ranges,
                                                         start_pos, end_pos)
            << "\n";

  return visibility_ranges;
}

bool ObjectOverlapsWithZone(
    const math::geometry::PolygonWithCache2d& object_contour,
    const math::geometry::PolygonWithCache2d& zone_border) {
  if (object_contour.points().empty() || zone_border.points().empty()) {
    return false;
  }
  return math::geometry::Intersects(object_contour, zone_border);
}

std::optional<RiskAvoidanceObjectInfo> CheckObjectInRoadExitZone(
    const PlannerObject& planner_object,
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const selection::TrajectoryMetaData& candidate) {
  RiskAvoidanceObjectInfo risk_avoidance_object_info;
  const speed::TrajectoryInfo& trajectory_info = *candidate.trajectory_info;
  const std::vector<speed::traffic_rules::ZoneInLaneSequence>&
      intersected_zone_in_lane_vector = trajectory_info.traffic_rules().zones;
  DCHECK(std::is_sorted(intersected_zone_in_lane_vector.begin(),
                        intersected_zone_in_lane_vector.end(),
                        [](const auto& lhs, const auto& rhs) {
                          return lhs.start_ra_arclength <
                                 rhs.start_ra_arclength;
                        }))
      << "intersected_zone_in_lane_vector is not sorted.";
  const auto& contour = planner_object.contour();
  for (const auto& zone : intersected_zone_in_lane_vector) {
    if (zone.zone_ptr->type() != hdmap::Zone::ROAD_EXIT) {
      continue;
    }
    if (!ObjectOverlapsWithZone(contour, zone.zone_ptr->border())) {
      continue;
    }
    const pb::ELCDirection& elc_direction_for_road_exit =
        GetELCDirectionForRoadExit(zone.zone_ptr, lane_sequence);
    DCHECK(elc_direction_for_road_exit != pb::ELCDirection::EITHER);

    risk_avoidance_object_info.elc_direction = elc_direction_for_road_exit;
    risk_avoidance_object_info.risk_avoidance_type =
        pb::RiskAvoidanceType::RoadExit;
    return std::make_optional<RiskAvoidanceObjectInfo>(
        RiskAvoidanceObjectInfo(risk_avoidance_object_info));
  }
  return std::nullopt;
}

// TODO(Zhaorui): Add a UT for IsObjectAtCrosswalk.
bool IsObjectAtCrosswalk(const PlannerObject& planner_object,
                         const selection::TrajectoryMetaData& candidate,
                         std::ostringstream& debug_oss) {
  const speed::TrajectoryInfo& trajectory_info = *candidate.trajectory_info;
  const std::vector<speed::traffic_rules::ZoneInLaneSequence>&
      intersected_zone_in_lane_vector = trajectory_info.traffic_rules().zones;
  DCHECK(std::is_sorted(intersected_zone_in_lane_vector.begin(),
                        intersected_zone_in_lane_vector.end(),
                        [](const auto& lhs, const auto& rhs) {
                          return lhs.start_ra_arclength <
                                 rhs.start_ra_arclength;
                        }))
      << "intersected_zone_in_lane_vector is not sorted.";
  const auto& contour = planner_object.contour();
  for (const auto& zone : intersected_zone_in_lane_vector) {
    if ((zone.zone_ptr->type() == hdmap::Zone::CROSSWALK &&
         ObjectOverlapsWithZone(contour, zone.zone_ptr->border()))) {
      debug_oss << "CrosswalkObjectID: " << planner_object.id();
      return true;
    }
  }
  return false;
}

std::optional<RiskAvoidanceObjectInfo> CheckIsObjectUTurn(
    const pnc_map::PncMapService& pnc_map_service,
    const PlannerObject& planner_object) {
  if (!FLAGS_planning_enable_elective_lane_change_u_turn_risk_avoidance) {
    return std::nullopt;
  }
  const auto* hdmap = pnc_map_service.hdmap();
  const auto& center_2d = planner_object.center_2d();
  hdmap::Point point;
  point.set_x(center_2d.x());
  point.set_y(center_2d.y());
  const auto lanes =
      hdmap->GetLanes(point, kObjectCenterRadiusInUTurnLaneDistInMeter);

  // TODO(Zhaorui) : Observe this feature after landing and decide if we should
  // add this condition based on static map infos.
  const bool is_object_in_u_turn_lane =
      std::any_of(lanes.begin(), lanes.end(), [](const hdmap::Lane* lane) {
        return lane->turn() == hdmap::Lane::U_TURN &&
               lane->type() == hdmap::Lane_LaneType_VIRTUAL;
      });

  if (!is_object_in_u_turn_lane) {
    return std::nullopt;
  }
  RiskAvoidanceObjectInfo risk_avoidance_object_info;
  risk_avoidance_object_info.object = &planner_object;
  risk_avoidance_object_info.elc_direction = pb::ELCDirection::RIGHT;
  risk_avoidance_object_info.risk_avoidance_type = pb::RiskAvoidanceType::UTurn;
  return std::make_optional<RiskAvoidanceObjectInfo>(
      RiskAvoidanceObjectInfo(risk_avoidance_object_info));
}

// TODO(Zhaorui): Add a UT for CheckObjectInJunctionFromRightSide.
std::optional<RiskAvoidanceObjectInfo> CheckObjectInJunctionFromRightSide(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const PlannerObject& planner_object,
    const selection::TrajectoryMetaData& candidate,
    std::ostringstream& debug_oss) {
  if (!FLAGS_planning_enable_elective_lane_change_junction_right_side_risk_avoidance) {
    return std::nullopt;
  }
  // 1. Because before entering the junction, one must first enter the zebra
  // crossing. If only the junction is processed without considering the zebra
  // crossing, the car will only start timing after entering the junction, which
  // is actually quite late. If timing starts from the zebra crossing, these two
  // parts of time will accumulate.

  // 2. Some agents are on the crosswalk but not in
  // the junction. For example, if a single crosswalk has obstacles crossing it,
  // and the shape and slice constraints can still be met in this scenario,
  // there is definitely a risk of cutting in. If all the conditions for risk
  // avoidance can be met in this situation, then obstacle avoidance should
  // still be considered
  if (!planner_object.is_in_junction() &&
      !IsObjectAtCrosswalk(planner_object, candidate, debug_oss)) {
    return std::nullopt;
  }
  const auto object_direction_for_junction_risk_avoidance =
      GetELCDirectionForObject(lane_sequence, planner_object);
  if (object_direction_for_junction_risk_avoidance == pb::ELCDirection::RIGHT) {
    return std::nullopt;
  }
  RiskAvoidanceObjectInfo risk_avoidance_object_info;
  risk_avoidance_object_info.object = &planner_object;
  risk_avoidance_object_info.elc_direction = pb::ELCDirection::LEFT;
  risk_avoidance_object_info.risk_avoidance_type =
      pb::RiskAvoidanceType::JunctionFromRightSide;
  return std::make_optional<RiskAvoidanceObjectInfo>(
      RiskAvoidanceObjectInfo(risk_avoidance_object_info));
}

pb::ELCDirection GetELCDirectionForRoadExit(
    const pnc_map::Zone* zone_ptr,
    const std::vector<const pnc_map::Lane*>& lane_sequence) {
  const math::geometry::Point2d& road_exit_point =
      zone_ptr->border().points()[0];
  const math::geometry::PolylineCurve2d& center_line =
      lane_selection::GetLaneSequenceCurve(
          lane_sequence, lane_selection::LaneCurveType::kCenterLine);
  const math::pb::Side& side = center_line.GetSide(road_exit_point);
  return side == math::pb::kRight ? pb::ELCDirection::LEFT
                                  : pb::ELCDirection::RIGHT;
}

std::vector<RiskAvoidanceObjectInfo> GetRiskyObjectsForRiskAvoidance(
    const SpeedWorldModel& world_model,
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const selection::TrajectoryMetaData& candidate,
    const std::map<ObjectId, std::vector<pb::ELCRiskDirective>>&
        elc_risk_directives,
    const bool is_left_neighbor_lane_safe_for_risk_avoidance_elc,
    const bool is_right_neighbor_lane_safe_for_risk_avoidance_elc,
    pb::RiskAvoidanceELCSeed& seed, std::ostringstream& debug_oss) {
  debug_oss << "\n[Risky Objects]\n";

  if (FLAGS_planning_enable_elective_lane_change_neighbor_lane_stm) {
    UpdateObjectRecentlySTMTimeMap(world_model, seed);
  }

  std::vector<RiskAvoidanceObjectInfo> risk_avoidance_object_infos;
  for (const auto& pair : planner_object_map) {
    const ObjectId& object_id = pair.first;
    const PlannerObject& planner_object = pair.second;

    // Don't process non-vehicle risky objects.
    if (planner_object.object_type() != voy::perception::ObjectType::VEHICLE) {
      continue;
    }

    // Risky object from road exit.
    const std::optional<RiskAvoidanceObjectInfo>& object_in_road_exit_zone =
        CheckObjectInRoadExitZone(planner_object, lane_sequence, candidate);
    if (object_in_road_exit_zone != std::nullopt) {
      const bool is_neighbor_lane_safe_for_risk_avoidance_elc =
          object_in_road_exit_zone->elc_direction == pb::ELCDirection::LEFT
              ? is_left_neighbor_lane_safe_for_risk_avoidance_elc
              : is_right_neighbor_lane_safe_for_risk_avoidance_elc;
      if (is_neighbor_lane_safe_for_risk_avoidance_elc) {
        RiskAvoidanceObjectInfo risk_avoidance_object_info;
        risk_avoidance_object_info.object = &planner_object;
        debug_oss << "Road exit: " << object_id << ", ELC direction: "
                  << (object_in_road_exit_zone->elc_direction ==
                              pb::ELCDirection::LEFT
                          ? "left"
                          : "right")
                  << "\n";
        risk_avoidance_object_info.elc_direction =
            object_in_road_exit_zone->elc_direction;
        risk_avoidance_object_info.risk_avoidance_type =
            pb::RiskAvoidanceType::RoadExit;
        risk_avoidance_object_info.risk_confidence =
            pb::ELC_RISK_CONFIDENCE_LOW;
        risk_avoidance_object_infos.push_back(risk_avoidance_object_info);
      }
    }

    // Risky object from U-turn.
    const std::optional<RiskAvoidanceObjectInfo>& object_in_u_turn_lane =
        CheckIsObjectUTurn(*CHECK_NOTNULL(world_model.pnc_map_service()),
                           planner_object);
    if (object_in_u_turn_lane != std::nullopt) {
      RiskAvoidanceObjectInfo risk_avoidance_object_info;
      risk_avoidance_object_info.object = &planner_object;
      debug_oss << "U-turn: " << object_id << ", ELC direction: "
                << (object_in_road_exit_zone->elc_direction ==
                            pb::ELCDirection::LEFT
                        ? "left"
                        : "right")
                << "\n";
      risk_avoidance_object_info.elc_direction = pb::ELCDirection::RIGHT;
      risk_avoidance_object_info.risk_avoidance_type =
          pb::RiskAvoidanceType::UTurn;
      risk_avoidance_object_info.risk_confidence = pb::ELC_RISK_CONFIDENCE_LOW;
      risk_avoidance_object_infos.push_back(risk_avoidance_object_info);
    }

    // Risky object from right in junctions.
    const std::optional<RiskAvoidanceObjectInfo>&
        object_in_junction_from_right_side = CheckObjectInJunctionFromRightSide(
            lane_sequence, planner_object, candidate, debug_oss);
    if (object_in_junction_from_right_side != std::nullopt) {
      RiskAvoidanceObjectInfo risk_avoidance_object_info;
      risk_avoidance_object_info.object = &planner_object;
      debug_oss << "Junction: " << object_id << ", ELC direction: "
                << (object_in_road_exit_zone->elc_direction ==
                            pb::ELCDirection::LEFT
                        ? "left"
                        : "right")
                << "\n";
      risk_avoidance_object_info.elc_direction = pb::ELCDirection::LEFT;
      risk_avoidance_object_info.risk_avoidance_type =
          pb::RiskAvoidanceType::JunctionFromRightSide;
      risk_avoidance_object_info.risk_confidence = pb::ELC_RISK_CONFIDENCE_LOW;
      risk_avoidance_object_infos.push_back(risk_avoidance_object_info);
    }

    // Risky STM object.
    if (FLAGS_planning_enable_elective_lane_change_neighbor_lane_stm) {
      const std::optional<RiskAvoidanceObjectInfo>&
          object_in_neighbor_lane_stm = CheckObjectInNeighborLaneSTM(
              candidate, candidate.trajectory_info, planner_object, seed);
      if (object_in_neighbor_lane_stm != std::nullopt) {
        const bool is_neighbor_lane_safe_for_risk_avoidance_elc =
            object_in_neighbor_lane_stm->elc_direction == pb::ELCDirection::LEFT
                ? is_left_neighbor_lane_safe_for_risk_avoidance_elc
                : is_right_neighbor_lane_safe_for_risk_avoidance_elc;
        if (is_neighbor_lane_safe_for_risk_avoidance_elc) {
          RiskAvoidanceObjectInfo risk_avoidance_object_info;
          risk_avoidance_object_info.object = &planner_object;
          debug_oss << "STM: " << object_id << ", ELC direction: "
                    << (object_in_road_exit_zone->elc_direction ==
                                pb::ELCDirection::LEFT
                            ? "left"
                            : "right")
                    << "\n";
          risk_avoidance_object_info.elc_direction =
              object_in_neighbor_lane_stm->elc_direction;
          risk_avoidance_object_info.risk_avoidance_type =
              pb::RiskAvoidanceType::NeighborLaneSTM;
          risk_avoidance_object_info.risk_confidence =
              pb::ELC_RISK_CONFIDENCE_LOW;
          risk_avoidance_object_infos.push_back(risk_avoidance_object_info);
        }
      }
    }
  }

  // Risky objects from ELCRiskDirective.
  for (const auto& pair : elc_risk_directives) {
    const ObjectId& object_id = pair.first;
    const std::vector<pb::ELCRiskDirective>& object_elc_risk_directives =
        pair.second;
    for (const auto& elc_risk_directive : object_elc_risk_directives) {
      const auto planner_object_iter = planner_object_map.find(object_id);
      if (planner_object_iter == planner_object_map.end()) {
        continue;
      }
      const PlannerObject& planner_object = planner_object_iter->second;

      if (elc_risk_directive.risk_direction() == math::pb::kOn) {
        continue;
      }

      RiskAvoidanceObjectInfo risk_avoidance_object_info;
      risk_avoidance_object_info.object = &planner_object;
      risk_avoidance_object_info.elc_direction =
          elc_risk_directive.risk_direction() == math::pb::kLeft
              ? pb::ELCDirection::RIGHT
              : pb::ELCDirection::LEFT;
      risk_avoidance_object_info.risk_avoidance_type =
          ELCRiskDirectiveTypeToRiskAvoidanceType(
              elc_risk_directive.risk_type());
      risk_avoidance_object_info.risk_confidence =
          elc_risk_directive.risk_confidence();
      risk_avoidance_object_infos.push_back(risk_avoidance_object_info);

      debug_oss << "Risk Directive ("
                << pb::ELCRiskDirective_RiskType_Name(
                       elc_risk_directive.risk_type())
                << ") : " << object_id << ", ELC direction: "
                << (risk_avoidance_object_info.elc_direction ==
                            pb::ELCDirection::LEFT
                        ? "left"
                        : "right")
                << "\n";
      // Currently, we only support the first risk directive of each object.
      // TODO(anwu): Support processing multiple risk directives per object.
      break;
    }
  }

  return risk_avoidance_object_infos;
}

bool HasCutInDistanceRisk(
    const speed::pb::OverlapRegion& overlap_region, const double ego_speed,
    const selection::TrajectoryMetaData& candidate,
    const RiskAvoidanceObjectInfo& risk_avoidance_object_info,
    std::ostringstream& debug_oss) {
  // This function shouldn't be called if the risk confidence is high.
  DCHECK(risk_avoidance_object_info.risk_confidence !=
         pb::ELC_RISK_CONFIDENCE_HIGH);

  double ego_fb_to_collision_point =
      speed::GetPaddedOverlapStart(overlap_region.overlap_slices(0));
  ego_fb_to_collision_point +=
      candidate.ego_in_lane_params().rear_axle_to_front_bumper_m;

  constexpr double kMinDistWithRiskForMediumConfidenceInMeter = 20.0;
  // TODO(anwu): Try decreasing this value.
  constexpr double kMinDistWithRiskForLowConfidenceInMeter = 25.0;
  const double min_dist_with_risk =
      risk_avoidance_object_info.risk_confidence ==
              pb::ELC_RISK_CONFIDENCE_MEDIUM
          ? kMinDistWithRiskForMediumConfidenceInMeter
          : kMinDistWithRiskForLowConfidenceInMeter;

  debug_oss << "Ego FB to collision point: " << ego_fb_to_collision_point
            << " / " << min_dist_with_risk << " m.\n";
  if (ego_fb_to_collision_point < min_dist_with_risk) {
    // Return early if the ego is too close to the cut in agent.
    debug_oss << "The ego is too close to collision point.\n";
    return false;
  }

  if (!HasCutInRiskByTTC(overlap_region, ego_speed, candidate,
                         risk_avoidance_object_info, debug_oss)) {
    // Return early if ego near collision point than agent a lot or agent near
    // collision point than ego a lot.
    debug_oss << "No cut-in risk by TTC.\n";
    return false;
  }

  // Skip the subsequent overlap check if the risk confidence is medium.
  if (risk_avoidance_object_info.risk_confidence ==
      pb::ELC_RISK_CONFIDENCE_MEDIUM) {
    return true;
  }

  const ::google::protobuf::RepeatedPtrField<speed::pb::OverlapSlice>&
      overlap_slices = overlap_region.overlap_slices();
  DCHECK(!overlap_slices.empty());
  const auto first_strict_slice =
      std::find_if(overlap_slices.begin(), overlap_slices.end(),
                   [](const speed::pb::OverlapSlice& slice) {
                     return speed::HasStrictOverlap(slice);
                   });
  if (first_strict_slice == overlap_slices.end() ||
      first_strict_slice == overlap_slices.begin()) {
    // The cut-in overlap region should be first approaching ego path, and then
    // encroaching ego path, excluding multi-lane cut-in scenario.
    debug_oss << "The agent doesn't have strict overlap, or it is already on "
                 "ego path.\n";
    return false;
  }

  const bool continuous_cross_ego_path =
      std::any_of(first_strict_slice, overlap_slices.end(),
                  [](const speed::pb::OverlapSlice& slice) {
                    return speed::HasStrictOverlap(slice);
                  });
  if (!continuous_cross_ego_path) {
    // One feature of cut-in overlap is it will occupy ego lane once entering
    // ego lane.
    debug_oss << "The agent will leave ego path in the future.\n";
    return false;
  }
  return true;
}

double GetCutInDistanceScore(
    const std::vector<speed::pb::OverlapRegion>& overlap_regions,
    const double ego_speed, const selection::TrajectoryMetaData& candidate,
    const RiskAvoidanceObjectInfo& risk_avoidance_object_info,
    std::ostringstream& debug_oss) {
  // Skip distance score computation for high confidence signals.
  if (risk_avoidance_object_info.risk_confidence ==
      pb::ELC_RISK_CONFIDENCE_HIGH) {
    return kMaxElectiveLaneChangeCutInScore;
  }

  if (overlap_regions.empty()) {
    return kMinElectiveLaneChangeCutInScore;
  }
  VOY_LATENCY_STAT_RECORD_PLANNER_S2(LAT_GetCutInDistanceScore);
  if (std::any_of(overlap_regions.begin(), overlap_regions.end(),
                  [ego_speed, &candidate, &risk_avoidance_object_info,
                   &debug_oss](const auto& overlap_region) {
                    return HasCutInDistanceRisk(
                        overlap_region, ego_speed, candidate,
                        risk_avoidance_object_info, debug_oss);
                  })) {
    return kMaxElectiveLaneChangeCutInScore;
  }
  return kMinElectiveLaneChangeCutInScore;
}

double GetCutInHeadingScore(
    const PlannerObject& planner_object, const double ego_heading,
    const RiskAvoidanceObjectInfo& risk_avoidance_object_info) {
  // Skip heading score computation for high & medium confidence signals.
  if (risk_avoidance_object_info.risk_confidence ==
          pb::ELC_RISK_CONFIDENCE_HIGH ||
      risk_avoidance_object_info.risk_confidence ==
          pb::ELC_RISK_CONFIDENCE_MEDIUM) {
    return kMaxElectiveLaneChangeCutInScore;
  }

  const pb::ELCDirection& elc_direction =
      risk_avoidance_object_info.elc_direction;
  if (elc_direction != pb::ELCDirection::RIGHT &&
      elc_direction != pb::ELCDirection::LEFT) {
    return kMinElectiveLaneChangeCutInScore;
  }
  const std::array<std::pair<double, double>, 1> kHeadingWindowsAngleInRadians =
      GetRiskAvoidanceHeadingWindowsAngleInRadians(
          risk_avoidance_object_info.risk_avoidance_type, elc_direction);
  for (const auto& heading_window : kHeadingWindowsAngleInRadians) {
    // Based on the current logic here, only objects that are cutting in from
    // the right side will be handled.
    if (math::WrapFromMinusPiToPi(planner_object.heading() - ego_heading) >=
            heading_window.first &&
        math::WrapFromMinusPiToPi(planner_object.heading() - ego_heading) <
            heading_window.second) {
      return kMaxElectiveLaneChangeCutInScore;
    }
  }
  return kMinElectiveLaneChangeCutInScore;
}

double GetELCCutInScore(const double cut_in_distance_score,
                        const double cut_in_heading_score) {
  return cut_in_distance_score + cut_in_heading_score;
}

bool IsRiskDurationEnough(const int64_t current_timestamp,
                          const int64_t risk_start_timestamp,
                          std::ostringstream& debug_oss) {
  constexpr double kMinRequiredAccumulationDurationInMSec = 2000;
  const int64_t risk_duration = current_timestamp - risk_start_timestamp;
  debug_oss << "\n[Risk Duration]\n"
            << DUMP_TO_STREAM(current_timestamp) << "\n"
            << DUMP_TO_STREAM(risk_start_timestamp) << "\n"
            << "accumulated duration: " << risk_duration << "/"
            << kMinRequiredAccumulationDurationInMSec << ")\n";

  return risk_duration >= kMinRequiredAccumulationDurationInMSec;
}

// TODO(Zhaorui): Add a UT for IsNeighborLaneSafeForRiskAvoidanceELC.
bool IsNeighborLaneSafeForRiskAvoidanceELC(
    const SpeedWorldModel& world_model,
    const std::vector<const pnc_map::Lane*>& neighbor_lane_sequence,
    const std::vector<const PlannerObject*>& objects_in_neighbor_lane_sequence,
    const bool is_left_neighbor, std::ostringstream& debug_oss) {
  debug_oss << "\n[" << (is_left_neighbor ? "Left" : "Right")
            << " Neighbor Safe for ELC]\n";
  if (neighbor_lane_sequence.empty()) {
    debug_oss << "Neighbor lane empty.\n";
    return true;
  }
  const math::geometry::PolylineCurve2d& neighbor_center_line =
      lane_selection::GetLaneSequenceCurve(
          neighbor_lane_sequence, lane_selection::LaneCurveType::kCenterLine);
  const math::geometry::Point2d& ego_rear =
      world_model.robot_state().plan_init_state_snapshot().rear_axle_position();
  const double ego_arc_length =
      neighbor_center_line
          .GetProximity(ego_rear, math::pb::UseExtensionFlag::kForbid)
          .arc_length;
  const double ego_speed =
      world_model.robot_state().plan_init_state_snapshot().speed();

  for (const PlannerObject* object : objects_in_neighbor_lane_sequence) {
    const double object_arc_length =
        neighbor_center_line
            .GetProximity(object->pose().center_2d(),
                          math::pb::UseExtensionFlag::kForbid)
            .arc_length;
    // Skip object far away from ego.
    if (object_arc_length >
            ego_arc_length + kRiskAvoidanceNeighborLaneAgentMaxDist ||
        object_arc_length <
            ego_arc_length + kRiskAvoidanceNeighborLaneAgentMinDist) {
      continue;
    }

    // Check if object can reach ego shortly.
    const double ttc_based_dist =
        (object->speed() - ego_speed) * kRiskAvoidanceNeighborLaneAgentTTCInSec;
    const double speed_based_dist =
        std::max(kRiskAvoidanceNeighborLaneAgentMinSafetyDistInMeter,
                 std::max(ego_speed, object->speed()) *
                     kRiskAvoidanceNeighborLaneAgentSpeedBasedTimeBufferInSec);
    if (std::max(ttc_based_dist, speed_based_dist) >
        ego_arc_length - object_arc_length) {
      debug_oss << "Risky neighbor lane object:" << object->id() << "\n";
      return false;
    }
  }
  debug_oss << "No risky neighbor lane objects.\n";
  return true;
}

ELCRiskAvoidanceResult ComputeELCRiskAvoidanceResult(
    const selection::TrajectoryMetaData& candidate,
    const std::map<ObjectId, std::vector<pb::ELCRiskDirective>>&
        elc_risk_directives,
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const SpeedWorldModel& world_model,
    const bool is_left_neighbor_lane_safe_for_risk_avoidance_elc,
    const bool is_right_neighbor_lane_safe_for_risk_avoidance_elc,
    pb::RiskAvoidanceELCSeed& seed, std::ostringstream& debug_oss) {
  ELCRiskAvoidanceResult elc_cut_in_avoidance_result;
  const auto& planner_object_map = world_model.planner_object_map();
  const std::vector<RiskAvoidanceObjectInfo>& risk_avoidance_object_infos =
      GetRiskyObjectsForRiskAvoidance(
          world_model, planner_object_map, lane_sequence, candidate,
          elc_risk_directives,
          is_left_neighbor_lane_safe_for_risk_avoidance_elc,
          is_right_neighbor_lane_safe_for_risk_avoidance_elc, seed, debug_oss);

  std::vector<CutInObjectInfo> cut_in_object_infos;
  cut_in_object_infos.reserve(risk_avoidance_object_infos.size());
  debug_oss << "\n[Per Risky Object]\n";
  for (const RiskAvoidanceObjectInfo& risk_avoidance_object_info :
       risk_avoidance_object_infos) {
    const PlannerObject& object =
        *CHECK_NOTNULL(risk_avoidance_object_info.object);
    const ObjectId& object_id = object.id();
    debug_oss << "\n"
              << object_id << " ("
              << pb::RiskAvoidanceType_Name(
                     risk_avoidance_object_info.risk_avoidance_type)
              << ")"
                 ":\n";

    // Skip primary stationary and non-vehicles for now.
    if (object.is_primary_stationary()) {
      debug_oss << "Primary bp is stationary, skip.\n";
      continue;
    }
    if (!object.is_vehicle()) {
      debug_oss << "Object is not a vehicle, skip.\n";
      continue;
    }

    const auto& overlap_regions =
        candidate.object_overlap_region_map->at(object_id);

    CutInObjectInfo cut_in_object;
    cut_in_object.cut_in_distance_score = GetCutInDistanceScore(
        overlap_regions,
        world_model.robot_state().plan_init_state_snapshot().speed(), candidate,
        risk_avoidance_object_info, debug_oss);
    cut_in_object.cut_in_heading_score = GetCutInHeadingScore(
        object, world_model.robot_state().plan_init_state_snapshot().heading(),
        risk_avoidance_object_info);
    cut_in_object.object_id = object_id;
    cut_in_object.elc_cut_in_score =
        GetELCCutInScore(cut_in_object.cut_in_distance_score,
                         cut_in_object.cut_in_heading_score);
    cut_in_object.elc_direction = risk_avoidance_object_info.elc_direction;
    cut_in_object.risk_avoidance_type =
        risk_avoidance_object_info.risk_avoidance_type;
    cut_in_object_infos.push_back(cut_in_object);

    debug_oss << "Cut-in score: " << cut_in_object.elc_cut_in_score
              << ", distance score: " << cut_in_object.cut_in_distance_score
              << ", heading score: " << cut_in_object.cut_in_heading_score
              << "\n";
  }

  for (const auto& cut_in_object_info : cut_in_object_infos) {
    if (cut_in_object_info.elc_cut_in_score >
        kElectiveLaneChangeCutInRiskAvoidanceScore -
            math::constants::kEpsilon) {
      debug_oss << "\n[Risk Avoidance] ID: " << cut_in_object_info.object_id
                << "\n";
      elc_cut_in_avoidance_result.has_risk = true;
      elc_cut_in_avoidance_result.object_id = cut_in_object_info.object_id;
      elc_cut_in_avoidance_result.elc_direction =
          cut_in_object_info.elc_direction;
      elc_cut_in_avoidance_result.risk_avoidance_type =
          cut_in_object_info.risk_avoidance_type;
      return elc_cut_in_avoidance_result;
    }
  }
  elc_cut_in_avoidance_result.has_risk = false;
  elc_cut_in_avoidance_result.object_id = std::nullopt;
  return elc_cut_in_avoidance_result;
}

void ComputeEgoAgentTTCDiffThreshold(
    const selection::TrajectoryMetaData& candidate,
    const RiskAvoidanceObjectInfo& risk_avoidance_object_info,
    double& ego_pass_agent_ttc_diff_threshold,
    double& ego_yield_agent_ttc_diff_threshold) {
  // Use a relaxed check for medium confidence risk avoidance.
  if (risk_avoidance_object_info.risk_confidence ==
      pb::ELC_RISK_CONFIDENCE_MEDIUM) {
    constexpr double kEgoPassAgentTTCDiffThresholdForMediumConfidenceInSec =
        1.4;
    constexpr double kEgoYieldAgentTTCDiffThresholdForMediumConfidenceInSec =
        1.0;
    ego_pass_agent_ttc_diff_threshold =
        kEgoPassAgentTTCDiffThresholdForMediumConfidenceInSec;
    ego_yield_agent_ttc_diff_threshold =
        kEgoYieldAgentTTCDiffThresholdForMediumConfidenceInSec;
    return;
  }

  // Use a moderate check for neighbor lane STM.
  if (risk_avoidance_object_info.risk_avoidance_type ==
      pb::RiskAvoidanceType::NeighborLaneSTM) {
    constexpr double kEgoPassAgentTTCDiffThresholdForNeighborLaneSTMInSec = 1.1;
    constexpr double kEgoYieldAgentTTCDiffThresholdForNeighborLaneSTMInSec =
        0.7;
    ego_pass_agent_ttc_diff_threshold =
        kEgoPassAgentTTCDiffThresholdForNeighborLaneSTMInSec;
    ego_yield_agent_ttc_diff_threshold =
        kEgoYieldAgentTTCDiffThresholdForNeighborLaneSTMInSec;
    return;
  }

  // Otherwise, set the thresholds based on the lane index.
  constexpr double kEgoPassAgentTTCDiffThresholdForFirstLaneInSec = 1.4;
  constexpr double kEgoYieldAgentTTCDiffThresholdForFirstLaneInSec = 1.0;
  constexpr double kEgoPassAgentTTCDiffThresholdForSecondLaneInSec = 1.1;
  constexpr double kEgoYieldAgentTTCDiffThresholdForSecondLaneInSec = 0.7;
  constexpr double kEgoPassAgentTTCDiffThresholdForThirdLaneInSec = 0.9;
  constexpr double kEgoYieldAgentTTCDiffThresholdForThirdLaneInSec = 0.5;

  const int current_lane_index_with_direction = GetLaneIndexWithDirection(
      candidate, risk_avoidance_object_info.elc_direction);
  switch (current_lane_index_with_direction) {
    case 0:  // First lane
      ego_pass_agent_ttc_diff_threshold =
          kEgoPassAgentTTCDiffThresholdForFirstLaneInSec;
      ego_yield_agent_ttc_diff_threshold =
          kEgoYieldAgentTTCDiffThresholdForFirstLaneInSec;
      break;
    case 1:  // Second lane
      ego_pass_agent_ttc_diff_threshold =
          kEgoPassAgentTTCDiffThresholdForSecondLaneInSec;
      ego_yield_agent_ttc_diff_threshold =
          kEgoYieldAgentTTCDiffThresholdForSecondLaneInSec;
      break;
    default:  // Other lanes
      ego_pass_agent_ttc_diff_threshold =
          kEgoPassAgentTTCDiffThresholdForThirdLaneInSec;
      ego_yield_agent_ttc_diff_threshold =
          kEgoYieldAgentTTCDiffThresholdForThirdLaneInSec;
      break;
  }
}

// TODO(Zhaorui): Add a UT for HasCutInRiskByTTC.
bool HasCutInRiskByTTC(
    const speed::pb::OverlapRegion& overlap_region, const double ego_speed,
    const selection::TrajectoryMetaData& candidate,
    const RiskAvoidanceObjectInfo& risk_avoidance_object_info,
    std::ostringstream& debug_oss) {
  // This function shouldn't be called if the risk confidence is high.
  DCHECK(risk_avoidance_object_info.risk_confidence !=
         pb::ELC_RISK_CONFIDENCE_HIGH);

  double ego_fb_to_collision_point =
      speed::GetPaddedOverlapStart(overlap_region.overlap_slices(0));
  ego_fb_to_collision_point +=
      candidate.ego_in_lane_params().rear_axle_to_front_bumper_m;

  // When the ego TTC is large, don't trigger to avoid FP.
  const double ego_ttc_time = ego_fb_to_collision_point /
                              std::max(ego_speed, math::constants::kEpsilon);
  constexpr double kEgoTTCMaxToTriggerInSec = 8.0;
  // Use a smaller threshold for junction to be more conservative.
  constexpr double kEgoTTCMaxToTriggerInJunctionInSec = 5.0;
  const double ego_ttc_max_to_trigger_in_sec =
      risk_avoidance_object_info.risk_avoidance_type ==
              pb::RiskAvoidanceType::JunctionFromRightSide
          ? kEgoTTCMaxToTriggerInJunctionInSec
          : kEgoTTCMaxToTriggerInSec;
  const bool should_trigger_by_ego_ttc =
      ego_ttc_time < ego_ttc_max_to_trigger_in_sec;

  // When the agent TTC is large, don't trigger to avoid FP.
  const double agent_ttc_time =
      overlap_region.overlap_slices(0).relative_time_in_sec();
  constexpr double kAgentTTCMaxToTriggerInSec = 6.0;
  const bool should_trigger_by_agent_ttc =
      agent_ttc_time < kAgentTTCMaxToTriggerInSec;

  debug_oss << "[TTC debug]\n"
            << DUMP_TO_STREAM(ego_ttc_time) << "/" << kEgoTTCMaxToTriggerInSec
            << "\n"
            << DUMP_TO_STREAM(agent_ttc_time) << "/"
            << kAgentTTCMaxToTriggerInSec << "\n";
  if (!should_trigger_by_ego_ttc || !should_trigger_by_agent_ttc) {
    debug_oss << "Should not trigger by ego TTC or agent TTC.\n";
    return false;
  }

  // Compute ego pass/yield agent TTC diff thresholds.
  double ego_pass_agent_ttc_diff_threshold = 0.0;
  double ego_yield_agent_ttc_diff_threshold = 0.0;
  ComputeEgoAgentTTCDiffThreshold(candidate, risk_avoidance_object_info,
                                  ego_pass_agent_ttc_diff_threshold,
                                  ego_yield_agent_ttc_diff_threshold);

  const bool can_ego_pass_agent_safely =
      agent_ttc_time - ego_ttc_time > ego_pass_agent_ttc_diff_threshold;
  const bool can_ego_yield_agent_safely =
      ego_ttc_time - agent_ttc_time > ego_yield_agent_ttc_diff_threshold;

  const bool is_agent_already_on_path =
      speed::HasStrictOverlap(overlap_region.overlap_slices(0));

  // TODO(anwu): When there is padded overlap and the ego is nudging the agent
  // at t=0, we can also trigger risk avoidance ELC.

  debug_oss << DUMP_TO_STREAM(agent_ttc_time - ego_ttc_time) << "/"
            << ego_pass_agent_ttc_diff_threshold << "\n"
            << DUMP_TO_STREAM(can_ego_pass_agent_safely) << "\n"
            << DUMP_TO_STREAM(ego_ttc_time - agent_ttc_time) << "/"
            << ego_yield_agent_ttc_diff_threshold << "\n"
            << DUMP_TO_STREAM(can_ego_yield_agent_safely) << "\n"
            << DUMP_TO_STREAM(is_agent_already_on_path) << "\n";

  return !(can_ego_pass_agent_safely || can_ego_yield_agent_safely ||
           is_agent_already_on_path);
}

bool IsEgoInLeftmostOrRightmostVehicleLane(
    const selection::TrajectoryMetaData& candidate,
    const pb::ELCDirection& elc_direction) {
  const pnc_map::Lane* current_lane = candidate.current_lane();
  return (elc_direction == pb::ELCDirection::LEFT &&
          current_lane->IsRightmostDrivableLane()) ||
         (elc_direction == pb::ELCDirection::RIGHT &&
          current_lane->IsLeftmostDrivableLane());
}

// TODO(Zhaorui): Add a UT for GetLaneIndexWithDirection.
int GetLaneIndexWithDirection(const selection::TrajectoryMetaData& candidate,
                              const pb::ELCDirection& elc_direction) {
  const pnc_map::Lane* current_lane = candidate.current_lane();
  const pnc_map::Section* current_section = current_lane->section();
  const int current_lane_index = current_section->GetLaneIndex(*current_lane);
  const pnc_map::Lane* rightmost_lane =
      current_section->GetRightMostDrivableLane();
  const int rightmost_lane_index =
      current_section->GetLaneIndex(*rightmost_lane);
  const pnc_map::Lane* leftmost_lane =
      current_section->GetLeftMostDrivableLane();
  const int leftmost_lane_index = current_section->GetLaneIndex(*leftmost_lane);

  const int current_lane_index_with_direction =
      elc_direction == pb::ELCDirection::RIGHT
          ? current_lane_index - leftmost_lane_index
          : rightmost_lane_index - current_lane_index;

  return current_lane_index_with_direction;
}

bool IsSanitationVehicle(const voy::TrackedObject& tracked_object) {
  return std::any_of(tracked_object.attributes().begin(),
                     tracked_object.attributes().end(), [](const auto& attr) {
                       return attr ==
                              voy::perception::Attribute::VEHICLE_SANITATION;
                     });
}

// TODO(Zhaorui): Add a UT for GetELCDirectionForObject.
pb::ELCDirection GetELCDirectionForObject(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const PlannerObject& planner_object) {
  const auto center_line = lane_selection::GetLaneSequenceCurve(
      lane_sequence, lane_selection::LaneCurveType::kCenterLine);
  const auto side = center_line.GetSide(planner_object.center_2d());
  return side == math::pb::kRight ? pb::ELCDirection::LEFT
                                  : pb::ELCDirection::RIGHT;
}

// TODO(Zhaorui): Add a UT for CheckObjectInNeighborLaneSTM.
std::optional<RiskAvoidanceObjectInfo> CheckObjectInNeighborLaneSTM(
    const selection::TrajectoryMetaData& candidate,
    const std::shared_ptr<const speed::TrajectoryInfo>& trajectory_info,
    const PlannerObject& planner_object, pb::RiskAvoidanceELCSeed& seed) {
  if (planner_object.object_type() != voy::perception::ObjectType::VEHICLE) {
    return std::nullopt;
  }

  if (!trajectory_info->IsLaneKeep()) {
    return std::nullopt;
  }

  if (std::any_of(seed.object_recently_stm_time_map().begin(),
                  seed.object_recently_stm_time_map().end(),
                  [&planner_object](const auto& object_recently_stm_time_iter) {
                    return object_recently_stm_time_iter.first ==
                           planner_object.id();
                  })) {
    return std::nullopt;
  }

  if (planner_object.speed() > kMaxSpeedForSlowCutInAgent ||
      planner_object.is_stationary()) {
    return std::nullopt;
  }

  const auto& agent_in_lane_states_map = candidate.agent_in_lane_states_map();
  const auto* agent_inlane_state =
      agent_in_lane_states_map.find(planner_object.id())->second.get();
  const double start_arc_length =
      agent_inlane_state->tracked_state.inlane_param.start_arclength_m;

  const double cross_track_encroachment_m =
      agent_inlane_state->tracked_state.inlane_param.cross_track_encroachment_m;
  if (cross_track_encroachment_m <
      -kMaxAgentConsiderableLateralDistToLaneBoundary) {
    return std::nullopt;
  }

  const auto& overlap_regions =
      candidate.object_overlap_region_map->at(planner_object.id());
  const double leading_bumper_offset =
      candidate.ego_in_lane_params().rear_axle_to_front_bumper_m;

  const bool is_ego_going_straight_when_cut_in = std::any_of(
      overlap_regions.begin(), overlap_regions.end(),
      [trajectory_info, &start_arc_length,
       leading_bumper_offset](const auto& overlap_region) {
        const double padded_overlap_start =
            speed::GetPaddedOverlapStart(overlap_region.overlap_slices(0)) +
            leading_bumper_offset;
        return padded_overlap_start - start_arc_length <
                   kMaxArclengthForSharpCutIn &&
               std::abs(overlap_region.overlap_slices(0).signed_lateral_gap()) <
                   kMaxDistBetweenEgoPathAndAgentInMeter &&
               !trajectory_info->IsInJunction(padded_overlap_start);
      });

  if (!is_ego_going_straight_when_cut_in) {
    return std::nullopt;
  }

  RiskAvoidanceObjectInfo risk_avoidance_object_info;
  risk_avoidance_object_info.object = &planner_object;
  risk_avoidance_object_info.elc_direction =
      GetELCDirectionForObject(candidate.lane_sequence(), planner_object);
  risk_avoidance_object_info.risk_avoidance_type =
      pb::RiskAvoidanceType::NeighborLaneSTM;
  return std::make_optional<RiskAvoidanceObjectInfo>(
      RiskAvoidanceObjectInfo(risk_avoidance_object_info));
}

std::array<std::pair<double, double>, 1>
GetRiskAvoidanceHeadingWindowsAngleInRadians(
    const pb::RiskAvoidanceType& risk_avoidance_type,
    const pb::ELCDirection& elc_direction) {
  switch (risk_avoidance_type) {
    case pb::RiskAvoidanceType::JunctionFromRightSide:
      return kJunctionFromRightSideHeadingWindowsAngleInRadians;
    case pb::RiskAvoidanceType::NeighborLaneSTM:
      return kNeighborLaneSTMHeadingWindowsAngleInRadians;
    case pb::RiskAvoidanceType::RoadExit:
      return elc_direction == pb::ELCDirection::LEFT
                 ? kLeftELCRoadExitHeadingWindowsAngleInRadians
                 : kRightELCRoadExitHeadingWindowsAngleInRadians;
    case pb::RiskAvoidanceType::UTurn:
      return kUTurnHeadingWindowsAngleInRadians;
    default:
      return elc_direction == pb::ELCDirection::LEFT
                 ? kLeftELCGeneralHeadingWindowsAngleInRadians
                 : kRightELCGeneralHeadingWindowsAngleInRadians;
  }
}

double ComputeBlockDurationThreshold(const SpeedWorldModel& world_model,
                                     const PlannerObject& object,
                                     const double object_projected_speed,
                                     const bool should_avoid_bus_bulb,
                                     std::ostringstream& debug_oss) {
  if (should_avoid_bus_bulb) {
    return kMinRequiredBusStopBlockingDurationInMSec;
  }
  constexpr double kMaxSanitationVehicleSpeedToUseSmallBlockingDurationInMps =
      8.33;  // 30 km/h
  if (IsSanitationVehicle(object.tracked_object()) &&
      object_projected_speed <
          kMaxSanitationVehicleSpeedToUseSmallBlockingDurationInMps) {
    return kMinSanitationRequiredBlockingDurationInMSec;
  }
  if (object_projected_speed < -math::constants::kEpsilon) {
    return kOncomingRequiredBlockingDurationInMSec;
  }
  const double dynamic_time_threshold = GetDynamicTimeThreshold(
      world_model, object, object_projected_speed, debug_oss);
  return dynamic_time_threshold;
}

// TODO(Zhaorui): Add a UT for UpdateObjectRecentlySTMTimeMap.
void UpdateObjectRecentlySTMTimeMap(const SpeedWorldModel& world_model,
                                    pb::RiskAvoidanceELCSeed& seed) {
  for (const auto& planner_object_iter : world_model.planner_object_map()) {
    const auto object_prediction_iter =
        world_model.object_prediction_map().find(planner_object_iter.first);
    const std::vector<PredictedTrajectoryWrapper>& object_prediction =
        object_prediction_iter->second;
    if (std::any_of(object_prediction.begin(), object_prediction.end(),
                    [](const PredictedTrajectoryWrapper& trajectory) {
                      return trajectory.MightStartToMoveSoon();
                    })) {
      seed.mutable_object_recently_stm_time_map()->insert(
          {planner_object_iter.first, world_model.snapshot_timestamp()});
    }
  }

  for (auto first = seed.mutable_object_recently_stm_time_map()->begin();
       first != seed.mutable_object_recently_stm_time_map()->end();) {
    if (world_model.snapshot_timestamp() - first->second >
        kHasSTMSignalRecentlyDurationInMSec)
      first = seed.mutable_object_recently_stm_time_map()->erase(first);
    else
      ++first;
  }
}

void PopulateELCDecision(
    const std::vector<const pnc_map::Lane*>& current_lane_sequence,
    const math::geometry::Point2d& ego_pos, const int64_t current_timestamp,
    const pb::ElectiveLaneChangeType& type,
    const std::optional<double>& target_offset_from_ego,
    const std::optional<math::geometry::Point2d>& target_pos,
    const pb::ELCDirection& direction,
    const std::optional<ObjectId>& trigger_object_id,
    pb::ElectiveLaneChangeDecision& elc_decision) {
  DCHECK(target_offset_from_ego.has_value() || target_pos.has_value());

  // Populate basic trigger information.
  elc_decision.set_is_triggered(true);
  elc_decision.set_trigger_type(type);
  elc_decision.set_trigger_direction(direction);

  if (trigger_object_id.has_value()) {
    elc_decision.set_trigger_object_id(trigger_object_id.value());
  }
  elc_decision.set_trigger_timestamp(current_timestamp);

  // Populate lane path and arc length.
  const LaneFollowSequenceCurve& current_lane_sequence_curve =
      LaneFollowSequenceCurve(current_lane_sequence);
  const double ego_arc_length_on_current_lane_sequence_curve =
      current_lane_sequence_curve.nominal_path()
          .GetProximity(ego_pos, math::pb::UseExtensionFlag::kAllow)
          .arc_length;
  const double target_arc_length_on_current_lane_sequence_curve =
      target_offset_from_ego.has_value()
          ? ego_arc_length_on_current_lane_sequence_curve +
                target_offset_from_ego.value()
          : current_lane_sequence_curve.nominal_path()
                .GetProximity(target_pos.value(),
                              math::pb::UseExtensionFlag::kAllow)
                .arc_length;

  // Add lanes in lane sequence from current lane into lane path until the lane
  // path covers target arc length.
  double lane_path_length = 0.0;
  double target_arc_length_on_last_lane =
      target_arc_length_on_current_lane_sequence_curve;
  for (const pnc_map::Lane* lane : current_lane_sequence) {
    const double lane_length = lane->length();
    lane_path_length += lane_length;

    // Don't add lane before the current lane.
    if (lane_path_length < ego_arc_length_on_current_lane_sequence_curve) {
      continue;
    }
    elc_decision.add_lane_path(lane->id());

    // Subtract lane length from target arc length if the target point is not on
    // the lane.
    if (lane_path_length > target_arc_length_on_current_lane_sequence_curve) {
      break;
    }
    target_arc_length_on_last_lane -= lane->length();
  }
  elc_decision.set_arc_length_on_lane_path_last_lane(
      target_arc_length_on_last_lane);
}

// If the overlap slice is disappear, it will return false.

// The method doc is
// https://cooper.didichuxing.com/docs2/document/2203309199268.

// At present, only the primary predicted trajectory is considered. If there is
// no overlap or if there is a tendency to leave the main trajectory, false will
// be returned. In other cases, true will be returned.
bool WillPrimaryBpStayOnLaneSequence(
    const SpeedWorldModel& world_model,
    const selection::TrajectoryMetaData& candidate, const PlannerObject& object,
    std::ostringstream& debug_oss) {
  debug_oss << "Object " << object.id() << ": ";
  const tbb::concurrent_unordered_map<ObjectId,
                                      std::vector<PredictedTrajectoryWrapper>>&
      object_prediction_map = world_model.object_prediction_map();
  // If the object is not in the map, it means that the input is abnormal and
  // not a situation we need to consider.
  if (object_prediction_map.find(object.id()) == object_prediction_map.end()) {
    debug_oss << "no prediction map. Will stay.\n";
    return true;
  }

  const std::vector<PredictedTrajectoryWrapper>& predicted_trajectories =
      object_prediction_map.at(object.id());
  const auto& primary_bp_iter =
      std::find_if(predicted_trajectories.begin(), predicted_trajectories.end(),
                   [](const auto& predicted_trajectory) {
                     return predicted_trajectory.is_multi_output_trajectory() &&
                            predicted_trajectory.is_primary_trajectory();
                   });
  // If the primary_predicted_trajectory doesn't exist, it means that the input
  // is abnormal and
  // not a situation we need to consider.
  if (primary_bp_iter == predicted_trajectories.end()) {
    debug_oss << "no prediction prediction. Will stay.\n";
    return true;
  }

  // If the primary BP is short, we judge that the object will stay.
  constexpr double kObjectLeavingMaxFutureTInSec = 5.0;
  if (primary_bp_iter->size() <
      static_cast<int>(kObjectLeavingMaxFutureTInSec * 10)) {
    debug_oss << "primary prediction too short (" << primary_bp_iter->size()
              << " poses). Will stay.\n";
    return true;
  }

  const std::shared_ptr<
      std::map<ObjectId, std::vector<speed::pb::OverlapRegion>>>&
      object_overlap_region_map = candidate.object_overlap_region_map;
  // If there is no overlap for this object, don't process it.
  if (object_overlap_region_map->find(object.id()) ==
      object_overlap_region_map->end()) {
    debug_oss << "no overlap. Will stay.\n";
    return true;
  }
  const std::vector<speed::pb::OverlapRegion>& overlap_regions =
      object_overlap_region_map->at(object.id());
  // A leaving object is one whose primary trajectory, after a certain time:
  // 1. Doesn't have any overlap.
  // 2. Has overlap but the lateral gap is beyond a certain threshold.
  constexpr double kObjectLeavingMinLateralGapInM = 0.6;
  for (const auto& overlap_region : overlap_regions) {
    if (overlap_region.trajectory_id() != primary_bp_iter->id()) {
      continue;
    }

    // No need to process overlaps with small end t.
    if (overlap_region.end_padded_relative_time_in_sec() <
        kObjectLeavingMaxFutureTInSec) {
      continue;
    }

    // If there's any overlap slice within the lateral gap threshold beyond
    // future t, we judge that the agent will stay.
    for (const auto& overlap_slice : overlap_region.overlap_slices()) {
      if (overlap_slice.relative_time_in_sec() >
              kObjectLeavingMaxFutureTInSec &&
          std::abs(overlap_slice.signed_lateral_gap()) <
              kObjectLeavingMinLateralGapInM) {
        debug_oss << "lateral gap " << overlap_slice.signed_lateral_gap()
                  << "m at " << overlap_slice.relative_time_in_sec()
                  << "s. Will stay.\n";
        return true;
      }
    }
  }
  debug_oss << "No overlap within " << kObjectLeavingMinLateralGapInM
            << "m beyond " << kObjectLeavingMaxFutureTInSec
            << "s. Will leave.\n";
  return false;
}

double GetProjectedSpeedOnLaneSequence(
    const math::geometry::Point2d& pos, double heading, double speed,
    const LaneFollowSequenceCurve& lane_sequence) {
  const math::geometry::PolylineCurve2d& center_line =
      lane_sequence.nominal_path();
  const math::ProximityQueryInfo& proximity =
      center_line.GetProximity(pos, math::pb::kForbid);
  const double center_line_heading =
      center_line.GetInterpTheta(proximity.arc_length);
  const double center_line_heading_diff =
      math::AngleDiff(heading, center_line_heading);
  return speed * std::cos(center_line_heading_diff);
}

double GetObjectFutureProjectedSpeedOnLaneSequence(
    const SpeedWorldModel& world_model, const PlannerObject& object,
    const LaneFollowSequenceCurve& lane_sequence, const double future_t) {
  // If future_t is not positive, use the current pose.
  if (future_t < math::constants::kEpsilon) {
    return GetProjectedSpeedOnLaneSequence(object.center_2d(), object.heading(),
                                           object.speed(), lane_sequence);
  }

  const tbb::concurrent_unordered_map<ObjectId,
                                      std::vector<PredictedTrajectoryWrapper>>&
      object_prediction_map = world_model.object_prediction_map();

  // If the object is not in the map, use the current pose.
  if (object_prediction_map.find(object.id()) == object_prediction_map.end()) {
    return GetProjectedSpeedOnLaneSequence(object.center_2d(), object.heading(),
                                           object.speed(), lane_sequence);
  }

  const std::vector<PredictedTrajectoryWrapper>& predicted_trajectories =
      object_prediction_map.at(object.id());
  const auto& primary_bp_iter =
      std::find_if(predicted_trajectories.begin(), predicted_trajectories.end(),
                   [](const auto& predicted_trajectory) {
                     return predicted_trajectory.is_multi_output_trajectory() &&
                            predicted_trajectory.is_primary_trajectory();
                   });
  // If the primary_predicted_trajectory doesn't exist, use the current pose.
  if (primary_bp_iter == predicted_trajectories.end()) {
    return GetProjectedSpeedOnLaneSequence(object.center_2d(), object.heading(),
                                           object.speed(), lane_sequence);
  }

  // Get the closest pose on primary BP before future_t.
  const PredictedTrajectoryWrapper& primary_bp = *primary_bp_iter;
  const std::vector<PredictedPose>& primary_bp_poses =
      primary_bp.predicted_poses();
  if (primary_bp_poses.empty()) {
    return GetProjectedSpeedOnLaneSequence(object.center_2d(), object.heading(),
                                           object.speed(), lane_sequence);
  }

  const int query_idx = std::min(static_cast<int>(primary_bp_poses.size()) - 1,
                                 static_cast<int>(future_t * 10));
  const PredictedPose& bp_pose_at_query_idx = primary_bp_poses.at(query_idx);
  return GetProjectedSpeedOnLaneSequence(
      bp_pose_at_query_idx.center_2d(), bp_pose_at_query_idx.heading(),
      bp_pose_at_query_idx.speed(), lane_sequence);
}

double GetObjectSelectedProjectedSpeed(
    const SpeedWorldModel& world_model, const PlannerObject& object,
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const bool is_on_highway, std::ostringstream& debug_oss) {
  constexpr double kObjectFutureTInSec = 5.0;
  const LaneFollowSequenceCurve& lane_sequence_curve =
      LaneFollowSequenceCurve(lane_sequence);
  const double object_future_projected_speed =
      GetObjectFutureProjectedSpeedOnLaneSequence(
          world_model, object, lane_sequence_curve, kObjectFutureTInSec);
  const double object_current_projected_speed =
      GetObjectFutureProjectedSpeedOnLaneSequence(world_model, object,
                                                  lane_sequence_curve,
                                                  /*future_t=*/0.0);
  const double object_projected_speed =
      SelectProjectedSpeed(object_current_projected_speed,
                           object_future_projected_speed, is_on_highway);

  debug_oss << DUMP_TO_STREAM(object_projected_speed) << "\n"
            << DUMP_TO_STREAM(object_current_projected_speed) << "\n"
            << DUMP_TO_STREAM(object_future_projected_speed)
            << " at t=" << kObjectFutureTInSec << "s\n";
  return object_projected_speed;
}

double SelectProjectedSpeed(const double current_projected_speed,
                            const double future_projected_speed,
                            const bool is_on_highway) {
  // When the future speed has much difference from the current
  // speed, use the future speed; otherwise, use the current speed.
  constexpr double kSpeedDiffToUseFutureProjectedSpeedInMps = 2.5;
  const bool should_select_future_speed_on_local =
      std::abs(future_projected_speed - current_projected_speed) >
          kSpeedDiffToUseFutureProjectedSpeedInMps &&
      !is_on_highway;
  const bool should_select_future_speed_on_highway =
      future_projected_speed - current_projected_speed >
          kSpeedDiffToUseFutureProjectedSpeedInMps &&
      is_on_highway;

  return (should_select_future_speed_on_local ||
          should_select_future_speed_on_highway)
             ? future_projected_speed
             : current_projected_speed;
}

// Returns the risk score that indicates the risk for ELC.
// It is now either 0 or 1.
// NOTE: The risk score will be subtracted. The larger the
// remaining_lane_change_distance is, the smaller the distance risk is.
double CalculateDistanceRiskScoreForTargetLane(
    const double remaining_lane_change_distance,
    const double plan_init_state_snapshot_speed,
    std::ostringstream& debug_oss) {
  // If remaining distance for lane change is larger than this value, we do not
  // consider risk score.
  constexpr double kMinElectiveLaneChangeDistanceRiskScore = 0.0;
  constexpr double kMaxElectiveLaneChangeDistanceRiskScore = 1.0;
  const double min_lane_change_distance =
      std::max(kMinRemainingDistanceForLaneChangeInMeter,
               plan_init_state_snapshot_speed * kLaneChangeDurationInSec);
  const bool has_distance_risk =
      remaining_lane_change_distance <
      min_lane_change_distance + math::constants::kEpsilon;
  const double elc_distance_risk_score =
      has_distance_risk ? kMaxElectiveLaneChangeDistanceRiskScore
                        : kMinElectiveLaneChangeDistanceRiskScore;
  debug_oss << __func__ << ": " << elc_distance_risk_score << " ("
            << DUMP_TO_STREAM(remaining_lane_change_distance) << ", "
            << DUMP_TO_STREAM(min_lane_change_distance) << ", "
            << DUMP_TO_STREAM(has_distance_risk) << ")\n";
  return elc_distance_risk_score;
}

ELCScoreInfo ComputeELCScore(
    const std::function<double(const PlannerObject&, double, double, bool,
                               double, std::ostringstream&)>&
        calculate_vehicle_speed_score_func,
    const std::function<double(std::ostringstream&)>&
        calculate_congestion_risk_func,
    const std::function<double(std::ostringstream&)>&
        calculate_distance_risk_func,
    const PlannerObject& object, const double object_projected_speed,
    const double remaining_lane_change_distance, const double speed_limit,
    const bool is_night_scene, const double local_lane_change_speed_threshold,
    const bool is_use_for_normal_elc, std::ostringstream& debug_oss) {
  const double cyclist_or_unknown_speed_score =
      CalculateCyclistOrUnknownSpeedScore(
          object, object_projected_speed, speed_limit,
          remaining_lane_change_distance, is_night_scene,
          local_lane_change_speed_threshold, is_use_for_normal_elc, debug_oss);
  const double vehicle_speed_score = calculate_vehicle_speed_score_func(
      object, object_projected_speed, remaining_lane_change_distance,
      is_night_scene, local_lane_change_speed_threshold, debug_oss);
  const double pedestrian_speed_score = CalculatePedestrianSpeedScore(object);
  const double progress_saving_score =
      CalculateProgressSavingScore(cyclist_or_unknown_speed_score,
                                   vehicle_speed_score, pedestrian_speed_score);

  const double congestion_risk = calculate_congestion_risk_func(debug_oss);
  const double distance_risk = calculate_distance_risk_func(debug_oss);
  const double normalized_risk =
      CalculateNormalizedRisk(congestion_risk, distance_risk);

  // Normalized ELC score
  const double normalized_ELC_score =
      CalculateNormalizedELCScore(progress_saving_score, normalized_risk);

  debug_oss << "ELCScore: " << normalized_ELC_score << "\n"
            << "ProgressSavingScore: " << progress_saving_score << " ("
            << DUMP_TO_STREAM(vehicle_speed_score) << ", "
            << DUMP_TO_STREAM(cyclist_or_unknown_speed_score) << ", "
            << DUMP_TO_STREAM(pedestrian_speed_score) << ")\n"
            << "RiskScore: " << normalized_risk << " ("
            << DUMP_TO_STREAM(congestion_risk) << ", "
            << DUMP_TO_STREAM(distance_risk) << ")\n";

  ELCScoreInfo elc_score_info;
  elc_score_info.progress_saving_score = progress_saving_score;
  elc_score_info.congestion_risk = congestion_risk;
  elc_score_info.distance_risk = distance_risk;
  elc_score_info.normalized_ELC_score = normalized_ELC_score;

  return elc_score_info;
}

double ComputeELCScoreForGapAlign(
    const PlannerObject& object,
    const LaneChangeObjectInfo& lane_change_object_info,
    const pnc_map::Lane* current_lane, const std::tm* order_start_time_info,
    const double plan_init_state_snapshot_speed,
    const double remaining_lane_change_distance, const bool is_ego_in_junction,
    std::ostringstream& debug_oss) {
  auto calculate_vehicle_speed_score_func =
      [current_lane](const PlannerObject& object, double object_projected_speed,
                     double remaining_lane_change_distance, bool is_night_scene,
                     double local_lane_change_speed_threshold,
                     std::ostringstream& debug_oss) {
        return CalculateVehicleSpeedScoreForGapAlign(
            object, object_projected_speed, current_lane,
            remaining_lane_change_distance, is_night_scene,
            local_lane_change_speed_threshold,
            /*is_use_for_normal_elc*/ false, debug_oss);
      };

  auto calculate_congestion_risk_func = [](std::ostringstream& debug_oss) {
    const double congestion_risk = kMinElectiveLaneChangeCutInScore;
    debug_oss << "CalculateCongestionRisk: " << DUMP_TO_STREAM(congestion_risk)
              << "\n";

    return congestion_risk;
  };

  auto calculate_distance_risk_func =
      [remaining_lane_change_distance,
       plan_init_state_snapshot_speed](std::ostringstream& debug_oss) {
        return CalculateDistanceRiskScoreForTargetLane(
            remaining_lane_change_distance, plan_init_state_snapshot_speed,
            debug_oss);
      };
  const double local_lane_change_speed_threshold =
      kSlowMovingVehicleDaySpeedInMps;
  // If both agent and ego are in the junction, we may need to recall more slow
  // agent to avoid over yielding in the junction.
  const double object_projected_speed =
      is_ego_in_junction && object.is_in_junction()
          ? object.speed()
          : lane_change_object_info.agent_speed_at_sixth_second;
  const ELCScoreInfo& elc_score_info = ComputeELCScore(
      calculate_vehicle_speed_score_func, calculate_congestion_risk_func,
      calculate_distance_risk_func, object, object_projected_speed,
      remaining_lane_change_distance, GetSpeedLimit(current_lane),
      IsNightScene(order_start_time_info), local_lane_change_speed_threshold,
      /*is_use_for_normal_elc*/ false, debug_oss);
  return elc_score_info.normalized_ELC_score;
}

double ComputeELCScoreForNormalELC(
    const SpeedWorldModel& world_model,
    const selection::TrajectoryMetaData& candidate, const PlannerObject& object,
    const double object_projected_speed,
    const std::vector<const PlannerObject*>&
        non_leaving_blocking_objects_on_lane_sequence,
    const double min_ego_remaining_lane_change_distance,
    const bool is_congestion_in_neighbor_lane, const bool should_avoid_bus_bulb,
    const std::vector<math::Range1d>& visibility_ranges,
    const double slow_vehicle_ratio_in_left_lane_sequence,
    const double slow_vehicle_ratio_in_right_lane_sequence,
    const double distance_risk, const double local_lane_change_speed_threshold,
    std::ostringstream& debug_oss) {
  auto calculate_vehicle_speed_score_func =
      [&world_model, &candidate, &non_leaving_blocking_objects_on_lane_sequence,
       slow_vehicle_ratio_in_left_lane_sequence,
       slow_vehicle_ratio_in_right_lane_sequence, should_avoid_bus_bulb](
          const PlannerObject& object, const double object_projected_speed,
          double min_ego_remaining_lane_change_distance, bool is_night_scene,
          double local_lane_change_speed_threshold,
          std::ostringstream& debug_oss) {
        return CalculateVehicleSpeedScore(
            world_model.robot_state().plan_init_state_snapshot().speed(),
            object, object_projected_speed, candidate,
            non_leaving_blocking_objects_on_lane_sequence,
            min_ego_remaining_lane_change_distance,
            slow_vehicle_ratio_in_left_lane_sequence,
            slow_vehicle_ratio_in_right_lane_sequence, should_avoid_bus_bulb,
            is_night_scene, local_lane_change_speed_threshold,
            /*is_use_for_normal_elc*/ true, debug_oss);
      };

  auto calculate_congestion_risk_func =
      [&object, object_projected_speed,
       &non_leaving_blocking_objects_on_lane_sequence,
       is_congestion_in_neighbor_lane, should_avoid_bus_bulb,
       &visibility_ranges,
       min_ego_remaining_lane_change_distance](std::ostringstream& debug_oss) {
        return CalculateCongestionRisk(
            min_ego_remaining_lane_change_distance, object,
            object_projected_speed,
            non_leaving_blocking_objects_on_lane_sequence,
            is_congestion_in_neighbor_lane, should_avoid_bus_bulb,
            visibility_ranges, debug_oss);
      };

  auto calculate_distance_risk_func =
      [distance_risk](std::ostringstream& /*debug_oss*/) {
        return distance_risk;
      };

  const ELCScoreInfo& elc_score_info = ComputeELCScore(
      calculate_vehicle_speed_score_func, calculate_congestion_risk_func,
      calculate_distance_risk_func, object, object_projected_speed,
      min_ego_remaining_lane_change_distance,
      GetSpeedLimit(candidate.current_lane()),
      IsNightScene(world_model.order_start_time_info()),
      local_lane_change_speed_threshold, /*is_use_for_normal_elc*/ true,
      debug_oss);

  return elc_score_info.normalized_ELC_score;
}

double GetLaneSpeed(const std::vector<double>& objects_speed_in_lane_sequence,
                    const pnc_map::Lane* lane, std::ostringstream& debug_oss) {
  // Get the speed limit but limit it to 40km/h if it's a rightmost lane.
  constexpr double kMinimumSpeedLimitInMps = 4.17;
  const double lane_speed_limit =
      lane == nullptr ? kMinimumSpeedLimitInMps : GetSpeedLimit(lane);
  constexpr double kRightmostLaneMaxSpeedInMps = 11.11;
  const bool is_rightmost_lane =
      lane != nullptr && lane->IsRightmostDrivableLane();
  const double speed_limit_by_rightmost_lane =
      is_rightmost_lane ? kRightmostLaneMaxSpeedInMps
                        : std::numeric_limits<double>::infinity();
  const double final_speed_limit =
      std::min(lane_speed_limit, speed_limit_by_rightmost_lane);

  // When there are no objects on the lane sequence, use speed limit.
  if (objects_speed_in_lane_sequence.empty()) {
    debug_oss << "[GetLaneSpeed] using speed limit: " << final_speed_limit
              << (is_rightmost_lane ? " (rightmost lane)\n" : "\n");
    return final_speed_limit;
  }

  // When there are objects on the lane sequence, use the objects' average
  // speed, but still limit it to the speed limit, and also 40km/h if it's the
  // rightmost lane.
  double sum_speed = 0.0;
  for (const double speed : objects_speed_in_lane_sequence) {
    sum_speed += speed;
  }
  const double lane_speed = sum_speed / objects_speed_in_lane_sequence.size();
  const double final_lane_speed =
      std::min({lane_speed, speed_limit_by_rightmost_lane, lane_speed_limit});
  debug_oss << "[GetLaneSpeed] using average speed " << final_lane_speed
            << (is_rightmost_lane ? " (rightmost lane)\n" : "\n");
  return final_lane_speed;
}

double GetDynamicDistanceThreshold(const double neighbor_lane_speed,
                                   const double object_projected_speed,
                                   const bool is_left_lane_change,
                                   const bool is_on_highway) {
  if (neighbor_lane_speed <
      object_projected_speed + math::constants::kEpsilon) {
    return std::numeric_limits<double>::infinity();
  }

  constexpr double kMinRemainingLaneChangeDistanceInMeter = 25.0;

  // When object has a non-positive speed, use the min distance.
  if (object_projected_speed < math::constants::kEpsilon) {
    return kMinRemainingLaneChangeDistanceInMeter;
  }

  // TODO(anwu): Increase benefit threshold for LC to rightmost lane.
  constexpr double kMinTimeBenefitForLeftLcInSec = 10.0;
  constexpr double kMinTimeBenefitForRightLcInSec = 12.0;
  constexpr double kMinTimeBenefitForLeftLcOnHighwayInSec = 20.0;
  constexpr double kMinTimeBenefitForRightLcOnHighwayInSec = 25.0;
  const double min_time_benefit =
      is_left_lane_change
          ? (is_on_highway ? kMinTimeBenefitForLeftLcOnHighwayInSec
                           : kMinTimeBenefitForLeftLcInSec)
          : (is_on_highway ? kMinTimeBenefitForRightLcOnHighwayInSec
                           : kMinTimeBenefitForRightLcInSec);

  const double min_global_remaining_lane_change_distance =
      min_time_benefit * neighbor_lane_speed * object_projected_speed /
      (neighbor_lane_speed - object_projected_speed);
  return std::max(kMinRemainingLaneChangeDistanceInMeter,
                  min_global_remaining_lane_change_distance);
}

std::vector<const PlannerObject*> GetObjectsInTargetRange(
    const SpeedWorldModel& world_model,
    const std::vector<const pnc_map::Lane*>& neighbor_lane_sequence,
    const std::vector<const PlannerObject*>&
        objects_in_neighbor_lane_sequence) {
  if (neighbor_lane_sequence.empty()) {
    return {};
  }
  const math::geometry::PolylineCurve2d& neighbor_center_line =
      lane_selection::GetLaneSequenceCurve(
          neighbor_lane_sequence, lane_selection::LaneCurveType::kCenterLine);
  const math::geometry::Point2d& ego_rear =
      world_model.robot_state().plan_init_state_snapshot().rear_axle_position();
  const double ego_arc_length =
      neighbor_center_line
          .GetProximity(ego_rear, math::pb::UseExtensionFlag::kForbid)
          .arc_length;

  std::vector<const PlannerObject*> objects_in_target_range;
  for (const PlannerObject* object : objects_in_neighbor_lane_sequence) {
    const double object_arc_length =
        neighbor_center_line
            .GetProximity(object->pose().center_2d(),
                          math::pb::UseExtensionFlag::kForbid)
            .arc_length;
    if (object_arc_length <
            ego_arc_length + kFrontObjectsInTargetRangeDistInMeter &&
        object_arc_length >
            ego_arc_length + kBehindObjectsInTargetRangeDistInMeter) {
      objects_in_target_range.push_back(object);
    }
  }
  return objects_in_target_range;
}

// TODO(Zhaorui): Add a UT for GetDynamicTimeThreshold.
double GetDynamicTimeThreshold(const SpeedWorldModel& world_model,
                               const PlannerObject& object,
                               const double object_projected_speed,
                               std::ostringstream& debug_oss) {
  // When object speed is high, increase the time threshold to avoid excessive
  // LC.
  constexpr double kMinSpeedToIncreaseTimeThresholdInMps =
      math::KmphToMps(28.0);
  constexpr double kMaxSpeedToIncreaseTimeThresholdInMps =
      math::KmphToMps(36.0);
  if (object_projected_speed > kMinSpeedToIncreaseTimeThresholdInMps) {
    const double dynamic_time_threshold_for_high_speed =
        math::GetLinearInterpolatedY(
            kMinSpeedToIncreaseTimeThresholdInMps,
            kMaxSpeedToIncreaseTimeThresholdInMps,
            kMaxRequiredBlockingDurationInMSec,
            kMaxRequiredBlockingDurationForHighSpeedInMSec,
            object_projected_speed);
    debug_oss << "Duration increased to "
              << dynamic_time_threshold_for_high_speed
              << " msec due to high speed " << object_projected_speed << "\n";
    return dynamic_time_threshold_for_high_speed;
  }

  // Otherwise, scale the time threshold by the ratio of ego speed to agent
  // speed.
  const double ego_speed =
      world_model.robot_state().plan_init_state_snapshot().speed();
  const double speed_diff_ratio = math::Clamp(
      object.speed() / std::max(ego_speed, math::constants::kEpsilon), 0.0,
      1.0);
  // If the speed_diff_ratio is near to 0, it means that the before agent is
  // slow, and we what to trigger ELC faster.
  const double dynamic_time_threshold = math::LinearInterpolate(
      kMinRequiredBlockingDurationInMSec, kMaxRequiredBlockingDurationInMSec,
      speed_diff_ratio);

  debug_oss << "speed_diff_ratio:\t" << speed_diff_ratio << "\n"
            << "dynamic_time_threshold:\t" << dynamic_time_threshold << "\n";

  return dynamic_time_threshold;
}

bool IsMergeStructure(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const pnc_map::Lane* current_lane,
    const std::vector<const pnc_map::Lane*>& neighbor_lane_sequence,
    const bool is_left_elc, std::ostringstream& debug_oss) {
  if (!FLAGS_planning_enable_elc_by_merge_structure) {
    return false;
  }

  if (neighbor_lane_sequence.empty() || lane_sequence.empty()) {
    return false;
  }

  if (std::any_of(lane_sequence.begin(), lane_sequence.end(),
                  [](const pnc_map::Lane* lane) {
                    return DCHECK_NOTNULL(lane)->turn() == hdmap::Lane::U_TURN;
                  })) {
    return false;
  }

  if (current_lane == nullptr) {
    return false;
  }

  // Avoid crashes caused by inability to access the left neighbor lane on the
  // leftmost lane.
  if (is_left_elc && (current_lane->IsLeftmostLane() ||
                      current_lane->IsLeftmostDrivableLane() ||
                      current_lane->left_lane() == nullptr)) {
    return false;
  }

  // Avoid crashes caused by inability to access the right neighbor lane on the
  // rightmost lane.
  if (!is_left_elc && (current_lane->IsRightmostLane() ||
                       current_lane->IsRightmostDrivableLane() ||
                       current_lane->right_lane() == nullptr)) {
    return false;
  }

  const auto& current_lane_iter =
      std::find_if(lane_sequence.begin(), lane_sequence.end(),
                   [current_lane](const pnc_map::Lane* lane) {
                     return DCHECK_NOTNULL(lane)->id() == current_lane->id();
                   });
  if (current_lane_iter == lane_sequence.end()) {
    return false;
  }

  int64_t brother_lane_id = 0;
  for (auto iter = current_lane_iter; iter < lane_sequence.end(); ++iter) {
    if (DCHECK_NOTNULL(*iter)->IsMergeLane()) {
      brother_lane_id = (*iter)->id();
      debug_oss << "brother lane id: " << brother_lane_id << "\n";
      break;
    }
  }

  // TODO(Zhaorui): we can early return false if brother lane id is unchanged.
  for (const pnc_map::Lane* lane : neighbor_lane_sequence) {
    if (lane == nullptr) {
      return false;
    }
    if (lane->IsMergeLane() &&
        lane->IsBrotherId(brother_lane_id,
                          /*relation_type=*/
                          pnc_map::BrotherLane::RelationType::kMerge,
                          /*is_drivable_only=*/true)) {
      debug_oss << "\n[MergeLane] lane id: " << DCHECK_NOTNULL(lane)->id()
                << "\n";
      return true;
    }
  }

  debug_oss << "\n[MergeLane]: No Merge Lane"
            << "\n";
  return false;
}

std::vector<int64> GetLanePathForRiskAvoidance(
    const LaneSequenceResult& lane_seq_res,
    const std::vector<speed::pb::OverlapRegion>& overlap_regions) {
  std::vector<int64> lane_path;
  if (overlap_regions.empty() || overlap_regions[0].overlap_slices().empty()) {
    return lane_path;
  }
  const auto agent_polygon = math::geometry::Convert<math::geometry::Polygon2d>(
      overlap_regions[0].overlap_slices(0).agent_bbox_points().points());
  if (agent_polygon.empty()) {
    return lane_path;
  }
  const math::geometry::Point2d& agent_postion = agent_polygon[0];
  const math::geometry::PolylineCurve2d& current_center_line =
      lane_selection::GetLaneSequenceCurve(
          lane_seq_res.lane_sequence,
          lane_selection::LaneCurveType::kCenterLine);
  const double agent_arc_length =
      current_center_line
          .GetProximity(agent_postion, math::pb::UseExtensionFlag::kForbid)
          .arc_length;

  double sum_length = 0.0;
  const pnc_map::Lane* associated_lane = nullptr;
  for (const auto& lane : lane_seq_res.lane_sequence) {
    sum_length += lane->length();
    if (sum_length > agent_arc_length) {
      associated_lane = lane;
      break;
    }
  }
  for (const pnc_map::Lane* lane : lane_seq_res.lane_sequence) {
    if (lane->id() != lane_seq_res.current_lane->id()) {
      continue;
    }
    lane_path.push_back(lane->id());
    if (associated_lane != nullptr && lane->id() == associated_lane->id()) {
      break;
    }
  }

  return lane_path;
}

const planner::PlannerObject* GetNextVehicleOnLaneSequence(
    const std::vector<const PlannerObject*>& vehicles_on_lane_sequence,
    const int64_t object_id) {
  bool found_object = false;
  for (auto it = vehicles_on_lane_sequence.begin();
       it != vehicles_on_lane_sequence.end(); it++) {
    if ((*it)->id() == object_id) {
      found_object = true;
      continue;
    }
    if (!found_object) {
      continue;
    }

    if ((*it)->is_vehicle()) {
      return *it;
    }
  }

  return nullptr;
}

void FinalizeELCDecision(
    const pb::ElectiveLaneChangeDeciderSeed& seed,
    const pb::ElectiveLaneChangeDecision& last_elc_decision,
    pb::ElectiveLaneChangeDecision& elc_decision) {
  if (seed.risk_avoidance_elc_seed().has_risk()) {
    elc_decision.set_is_accumulating(true);
    elc_decision.set_accumulating_type(
        pb::ElectiveLaneChangeType::RISK_AVOIDANCE_ELC);
    elc_decision.set_accumulating_object_id(
        seed.risk_avoidance_elc_seed().object_id());
    elc_decision.set_accumulating_start_timestamp(
        seed.risk_avoidance_elc_seed().risk_start_timestamp());
    elc_decision.set_accumulating_direction(
        seed.risk_avoidance_elc_seed().elc_direction());
  } else if (seed.progress_elc_seed().is_blocking()) {
    elc_decision.set_is_accumulating(true);
    elc_decision.set_accumulating_type(
        pb::ElectiveLaneChangeType::PROGRESS_ELC);
    elc_decision.set_accumulating_object_id(
        seed.progress_elc_seed().object_id());
    elc_decision.set_accumulating_start_timestamp(
        seed.progress_elc_seed().block_start_timestamp());
    elc_decision.set_accumulating_direction(
        seed.progress_elc_seed().elc_direction());
  }

  // When ELC is triggered in the current iteration, trigger_timestamp and
  // trigger_object_id will be set automatically.
  if (elc_decision.is_triggered()) {
    return;
  }

  // Otherwise, copy trigger timestamp & object ID from the last decision.
  elc_decision.set_trigger_timestamp(last_elc_decision.trigger_timestamp());
  elc_decision.set_trigger_object_id(last_elc_decision.trigger_object_id());
}

pb::RiskAvoidanceType ELCRiskDirectiveTypeToRiskAvoidanceType(
    const pb::ELCRiskDirective::RiskType& elc_risk_directive_type) {
  switch (elc_risk_directive_type) {
    case pb::ELCRiskDirective::SLOW_CUT_IN:
      return pb::RiskAvoidanceType::RiskDirectiveSlowCutIn;
    default:
      DCHECK(false) << "Unknown elc risk directive type: "
                    << static_cast<int>(elc_risk_directive_type);
      return pb::RiskAvoidanceType::Unknown;
  }
}

}  // namespace planner
