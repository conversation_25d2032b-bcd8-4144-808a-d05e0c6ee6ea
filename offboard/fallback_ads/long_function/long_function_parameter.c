
#include "long_function/long_function_parameter.h"

#include <stdbool.h>

#include "common_math_library/interpolation.h"

const volatile bool kVsiLongSpeedFilterEnable = false;
const volatile bool kVsiLatSpeedFilterEnable = false;
const volatile bool kVsiYawRateFilterEnable = false;
const volatile bool kVsiSteeringWheelAngleFilterEnable = false;
const volatile bool kVsiSteeringWheelAngleRateFilterEnable = false;

// maximum vehicle or object stop time value
const volatile float kCdsMaxStopTime = 40.0F;

// maximum time to collision value
const volatile float kCdsMaxTimeToCollision = 40.0F;

// maximum long distance for object validation
const volatile float kCdsMaxObjectLongDistance = 100.0F;

// maximum lateral distance for object validation
const volatile float kCdsMaxObjectLateralDistance = 5.0F;

// maximum positive jerk for free stop control
const volatile float kFscMaxJerk = 2.0F;

// minimum positive jerk for free stop control
const volatile float kFscMinJerk = -3.0F;

// ego velocity threshold for free stop control hold
const volatile float kFscEgoVelocityThreshold = 1.5F;

// deceleration request when the vehicle is about to stop
const volatile float kFscAccelRequestForStop = -1.0F;

// ego vehicle brake response time
const volatile float kFscEgoBrakeResponseTime = 0.2F;

// collision probability threshold
const volatile float kSedCollisionProbabilityThreshold = -0.01F;

// time to collision threshold
const volatile float kSedTimeToCollisionThreshold = 1.5F;

// turn off delay time for emergency brake trigger
const volatile float kSedEmergencyBrakeEnableDelayTime = 10.0F;

// time to reach stop point threshold
const volatile float kSedTimeToReachStopPointThreshold = 5.0F;

// turn off delay time for object following brake trigger
const volatile float kSedObjectFollowingBrakeDelayTime = 2.0F;

// acceleration threshold for lane following brake
const volatile float kSedAccelThreshold = -8.0F;

// turn off delay time for lane too short
const volatile float kSedLaneIsTooShortDelayTime = 0.1F;

// UN Regulation No.157 Automated Lane Keeping Systems, chapter5.2.3.3
// the safety distance has increased by 2 meters
const volatile CML_Table1D tCdsSafeDistance[CDS_SAFE_DISTANCE_TABLE_ROWS] = {
    {2.0F, 4.0F},    {2.78F, 5.1F},   {5.56F, 8.7F},  {8.33F, 12.8F},
    {11.11F, 17.6F}, {13.89F, 22.8F}, {16.67F, 28.7F}};

// deceleration required for free stop control
const volatile CML_Table1D
    tFscFreeStopAccRequest[FSC_FREE_STOP_ACC_REQUEST_TABLE_ROWS] = {
        {0.0F, -1.3F},  {2.0F, -1.3F},   {2.78F, -1.3F},  {5.56F, -1.5F},
        {8.33F, -2.0F}, {11.11F, -3.0F}, {13.89F, -3.5F}, {16.67F, -5.0F}};

// emergency braking distance by road test
const volatile CML_Table1D
    tSedEmergencyBrakeDistance[SED_EMERGENCY_BRAKE_DISTANCE_TABLE_ROWS] = {
        {0.0F, 0.0F},   {5.0F, 0.5F},   {10.0F, 1.1F},
        {20.0F, 3.2F},  {30.0F, 6.2F},  {40.0F, 9.8F},
        {50.0F, 14.2F}, {60.0F, 19.5F}, {70.0F, 24.9F}};

// free stop brake distance by road test
const volatile CML_Table1D
    tSedFreeStopBrakeDistance[SED_FREE_STOP_BRAKE_DISTANCE_TABLE_ROWS] = {
        {0.0F, 0.0F},   {6.6F, 2.7F},    {11.0F, 5.8F},   {16.0F, 10.0F},
        {21.0F, 16.0F}, {24.5F, 22.78F}, {30.0F, 28.87F}, {35.6F, 36.9F},
        {40.0F, 41.0F}, {50.0F, 53.5F},  {58.4F, 65.6F},  {59.0F, 67.5F}};

bool GetVsiLongSpeedFilterEnable() { return kVsiLongSpeedFilterEnable; }

bool GetVsiLatSpeedFilterEnable() { return kVsiLatSpeedFilterEnable; }

bool GetVsiYawRateFilterEnable() { return kVsiYawRateFilterEnable; }

bool GetVsiSteeringWheelAngleFilterEnable() {
  return kVsiSteeringWheelAngleFilterEnable;
}

bool GetVsiSteeringWheelAngleRateFilterEnable() {
  return kVsiSteeringWheelAngleRateFilterEnable;
}

float GetCdsMaxStopTime() { return kCdsMaxStopTime; }

float GetCdsMaxTimeToCollision() { return kCdsMaxTimeToCollision; }

float GetCdsMaxObjectLongDistance() { return kCdsMaxObjectLongDistance; }

float GetCdsMaxObjectLateralDistance() { return kCdsMaxObjectLateralDistance; }

float GetFscMaxJerk() { return kFscMaxJerk; }

float GetFscMinJerk() { return kFscMinJerk; }

float GetFscEgoVelocityThreshold() { return kFscEgoVelocityThreshold; }

float GetFscAccelRequestForStop() { return kFscAccelRequestForStop; }

float GetFscEgoBrakeResponseTime() { return kFscEgoBrakeResponseTime; }

float GetSedCollisionProbabilityThreshold() {
  return kSedCollisionProbabilityThreshold;
}

float GetSedTimeToCollisionThreshold() { return kSedTimeToCollisionThreshold; }

float GetSedEmergencyBrakeEnableDelayTime() {
  return kSedEmergencyBrakeEnableDelayTime;
}

float GetSedTimeToReachStopPointThreshold() {
  return kSedTimeToReachStopPointThreshold;
}

float GetSedObjectFollowingBrakeDelayTime() {
  return kSedObjectFollowingBrakeDelayTime;
}

float GetSedAccelThreshold() { return kSedAccelThreshold; }

float GetSedLaneIsTooShortDelayTime() { return kSedLaneIsTooShortDelayTime; }

const CML_Table1D* GetCdsSafeDistance() {
  return (const CML_Table1D*)tCdsSafeDistance;
}

const CML_Table1D* GetFscFreeStopAccRequest() {
  return (const CML_Table1D*)tFscFreeStopAccRequest;
}

const CML_Table1D* GetSedEmergencyBrakeDistance() {
  return (const CML_Table1D*)tSedEmergencyBrakeDistance;
}

const CML_Table1D* GetSedFreeStopBrakeDistance() {
  return (const CML_Table1D*)tSedFreeStopBrakeDistance;
}
