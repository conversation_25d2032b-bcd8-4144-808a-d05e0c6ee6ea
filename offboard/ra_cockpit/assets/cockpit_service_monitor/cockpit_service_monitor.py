#!/usr/bin/env python3
# encoding: utf-8
"""Check cockpit service and process status.

<AUTHOR>
"""
import subprocess
import time
import logging
from logging.handlers import TimedRotatingFileHandler
import os
from datetime import datetime
import requests
import re
import shutil

# check log dir
LOG_DIR = "/opt/RA/log"
SCRIPT_DIR = "/opt/RA/videos/script"
CONTAINER_NAME = "ra_cockpit"
CORE_BACKUP_DIR = "/opt/RA/core_dumps"

os.makedirs(LOG_DIR, exist_ok=True)
os.makedirs(CORE_BACKUP_DIR, exist_ok=True)

DEBUG_MODE = False

WEBHOOK_URL = "https://im-dichat.xiaojukeji.com/api/hooks/incoming/f48df8c3-4637-4385-b3cd-5184d95eab81"

if DEBUG_MODE:
    # for test
    WEBHOOK_URL = "https://im-dichat.xiaojukeji.com/api/hooks/incoming/2c12f2b9-204f-4754-ae45-83956812dd7a"


def get_current_logged_user():
    """get logged in user"""
    try:
        output = subprocess.check_output(['loginctl', 'list-sessions', '--no-legend'], text=True)
        for line in output.strip().splitlines():
            parts = line.split()
            if len(parts) >= 3:
                session_id, user = parts[0], parts[2]
                if user == 'gdm':
                    continue
                session_info = subprocess.check_output(['loginctl', 'show-session', session_id, '-p', 'Active'], text=True)
                if 'Active=yes' in session_info:
                    return user
    except Exception as e:
        logger.error(f"获取用户名失败: {str(e)}")
    return "unknown"


username = get_current_logged_user()


def send_alert(message):
    """send alert to DC group"""
    try:
        # only operators computer will send alert
        if not re.match(r'^(BJRA|SHRA|GZRA|SZRA)\d+$', username):
            logger.warning(f"用户名 {username} 不符合告警发送规则，跳过发送。")
            return
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        data = {"text": f"{current_time}: [{username}] {message}"}
        response = requests.post(WEBHOOK_URL, json=data, timeout=5)
        if response.status_code != 200:
            logger.error(f"发送告警失败: {response.status_code}")
    except requests.exceptions.RequestException as e:
        logger.error(f"发送告警异常: {str(e)}")


# log config
log_file = os.path.join(LOG_DIR, f"cockpit_service_monitor-{username}.log")
file_handler = TimedRotatingFileHandler(
    log_file,
    when="midnight",  # change log file at midnight
    interval=1,  # 1 new log file per day
    backupCount=30,  # keep 30 days of logs
    encoding="utf-8",
)
file_handler.suffix = "%Y-%m-%d"  # log file suffix
# set log format
formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
file_handler.setFormatter(formatter)
# add log handler
logger = logging.getLogger("ServiceMonitor")
logger.setLevel(logging.INFO)
logger.addHandler(file_handler)


def check_service_status(service_name):
    """check service status"""
    try:
        result = subprocess.run(
            ["systemctl", "is-active", service_name],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            timeout=10,
        )
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        logger.error(f"检查服务 {service_name} 状态超时")
        return False
    except Exception as e:
        logger.error(f"检查服务 {service_name} 状态异常: {str(e)}")
        return False


def check_process_status(process_name):
    """check process status"""
    try:
        result = subprocess.run(
            ["pgrep", process_name],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            timeout=10,
        )
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        logger.error(f"检查进程 {process_name} 状态超时")
        return False
    except Exception as e:
        logger.error(f"检查进程 {process_name} 状态异常: {str(e)}")
        return False


def restart_service(service_name):
    """restart service and confirm status"""
    try:
        # restart service
        logger.info(f"尝试重启服务 {service_name}")
        result = subprocess.run(
            ["systemctl", "restart", service_name],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            timeout=30,
        )

        if result.returncode != 0:
            error_msg = f"重启服务 {service_name} 失败: {result.stderr}"
            logger.error(error_msg)
            send_alert(error_msg)
            return False

        time.sleep(5)

        # check if service restart was successful
        if check_service_status(service_name):
            logger.info(f"服务 {service_name} 已成功重启")
            send_alert(f"服务 {service_name} 挂了，但已成功重启")
            return True

        error_msg = f"服务 {service_name} 重启失败，请及时检查"
        logger.error(error_msg)
        send_alert(error_msg)
        return False

    except subprocess.TimeoutExpired:
        error_msg = f"重启服务 {service_name} 超时"
        logger.error(error_msg)
        send_alert(error_msg)
        return False
    except Exception as e:
        error_msg = f"重启服务 {service_name} 异常: {str(e)}"
        logger.error(error_msg)
        send_alert(error_msg)
        return False


def restart_process(process_name):
    """restart process and confirm status"""
    try:
        process_script_map = {
            "drtc_agent": SCRIPT_DIR + "/start_drtc_agent.sh",
            "recorder_agent": SCRIPT_DIR + "/start_recorder_agent.sh",
            "screenkey": SCRIPT_DIR + "/start_screenkey.sh",
        }
        # restart process
        if process_name not in process_script_map:
            error_msg = f"进程 {process_name} 没有对应的启动脚本"
            logger.error(error_msg)
            send_alert(error_msg)
            return False

        script_path = process_script_map[process_name]
        if not os.path.exists(script_path):
            error_msg = f"启动脚本 {script_path} 不存在"
            logger.error(error_msg)
            send_alert(error_msg)
            return False

        logger.info(f"尝试在容器内重启进程 {process_name}")
        result = subprocess.run(
            ["docker", "exec", CONTAINER_NAME, "bash", script_path],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            timeout=30,
        )

        if result.returncode != 0:
            error_msg = f"在容器内执行启动脚本 {script_path} 失败: {result.stderr}"
            logger.error(error_msg)
            send_alert(error_msg)
            return False

        time.sleep(5)

        # check if restart was successful
        if check_process_status(process_name):
            logger.info(f"进程 {process_name} 已在容器内成功重启")
            send_alert(f"进程 {process_name} 挂了，但已在容器内成功重启")
            return True

        error_msg = f"进程 {process_name} 在容器内重启失败，请及时检查"
        logger.error(error_msg)
        send_alert(error_msg)
        return False

    except subprocess.TimeoutExpired:
        error_msg = f"在容器内重启进程 {process_name} 超时"
        logger.error(error_msg)
        send_alert(error_msg)
        return False

    except Exception as e:
        error_msg = f"在容器内重启进程 {process_name} 异常: {str(e)}"
        logger.error(error_msg)
        send_alert(error_msg)
        return False


def should_monitor():
    """check if ra_cockpit_main is running"""
    return check_process_status("ra_cockpit_main")


def check_core_dump_for_ra_cockpit():
    """check ra_cockpit_main core, report and move"""
    try:
        for filename in os.listdir("/tmp"):
            if filename.startswith("core-ra_cockpit_main"):
                full_path = os.path.join("/tmp", filename)
                backup_path = os.path.join(CORE_BACKUP_DIR, filename)

                if os.path.exists(backup_path):
                    continue

                shutil.move(full_path, backup_path)

                send_alert(f"检测到 ra_cockpit_main core dump 文件，已移动至: {backup_path}")
                logger.warning(f"检测到 core dump 文件并已转移: {full_path} -> {backup_path}")

    except Exception as e:
        logger.error(f"检测 core 文件异常: {str(e)}")


def main():
    # processes to be monitored
    processes = ["drtc_agent"]
    # services to be monitored
    services = ["video_file_uploader.service"]
    logger.info("启动monitor")

    while True:
        try:
            # check processes (only when ra_cockpit_main is running)
            if should_monitor():
                for process in processes:
                    if not check_process_status(process):
                        logger.warning(f"进程 {process} 未运行")
                        restart_process(process)

            # check services
            for service in services:
                if not check_service_status(service):
                    logger.warning(f"服务 {service} 未运行")
                    restart_service(service)

            # check for main process core dump
            check_core_dump_for_ra_cockpit()

            time.sleep(1)

        except Exception as e:
            logger.error(f"监控异常: {str(e)}")
            send_alert(f"监控异常: {str(e)}，请及时检查")
            time.sleep(10)


if __name__ == "__main__":
    main()
