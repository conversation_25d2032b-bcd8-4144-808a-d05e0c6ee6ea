/*
 * File: FallbackSigOutput_data.c
 *
 * Code generated for Simulink model 'FallbackSigOutput'.
 *
 * Model version                  : 1.318
 * Simulink Coder version         : 9.5 (R2021a) 14-Nov-2020
 * C/C++ source code generated on : Sun Jun  8 12:40:32 2025
 *
 * Target selection: autosar.tlc
 * Embedded hardware selection: Infineon->TriCore
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "FallbackSigOutput.h"
#include "FallbackSigOutput_private.h"

/* Invariant block signals (default storage) */
const ConstB_FallbackSigOutput_T FallbackSigOutput_ConstB = {
  11U,                                 /* '<S65>/Width' */
  8U                                   /* '<S59>/Width' */
};

/* Block parameters (default storage) */
P_FallbackSigOutput_T FallbackSigOutput_P = {
  /* Variable: EAD_AccFilterTime_P
   * Referenced by: '<S17>/Parameter6'
   */
  0.2F,

  /* Mask Parameter: BitwiseOperator1_BitMask
   * Referenced by: '<S8>/Bitwise Operator1'
   */
  1U,

  /* Mask Parameter: BitwiseOperator5_BitMask
   * Referenced by: '<S8>/Bitwise Operator5'
   */
  128U,

  /* Mask Parameter: CompareToConstant_const
   * Referenced by: '<S101>/Constant'
   */
  3U,

  /* Mask Parameter: CompareToConstant_const_m
   * Referenced by: '<S108>/Constant'
   */
  3U,

  /* Mask Parameter: CompareToConstant1_const
   * Referenced by: '<S109>/Constant'
   */
  3U,

  /* Mask Parameter: CompareToConstant_const_n
   * Referenced by: '<S115>/Constant'
   */
  3U,

  /* Mask Parameter: CompareToConstant9_const
   * Referenced by: '<S32>/Constant'
   */
  2U,

  /* Mask Parameter: CompareToConstant10_const
   * Referenced by: '<S30>/Constant'
   */
  1U,

  /* Mask Parameter: CompareToConstant8_const
   * Referenced by: '<S31>/Constant'
   */
  2U,

  /* Mask Parameter: CounterLimited1_uplimit
   * Referenced by: '<S81>/FixPt Switch'
   */
  15U,

  /* Mask Parameter: CounterLimited1_uplimit_c
   * Referenced by: '<S87>/FixPt Switch'
   */
  15U,

  /* Mask Parameter: CounterLimited1_uplimit_f
   * Referenced by: '<S93>/FixPt Switch'
   */
  15U,

  /* Mask Parameter: CounterLimited2_uplimit
   * Referenced by: '<S98>/FixPt Switch'
   */
  15U,

  /* Mask Parameter: CounterLimited6_uplimit
   * Referenced by: '<S104>/FixPt Switch'
   */
  15U,

  /* Mask Parameter: CounterLimited10_uplimit
   * Referenced by: '<S112>/FixPt Switch'
   */
  15U,

  /* Mask Parameter: CounterLimited9_uplimit
   * Referenced by: '<S118>/FixPt Switch'
   */
  15U,

  /* Mask Parameter: CounterLimited2_uplimit_j
   * Referenced by: '<S52>/FixPt Switch'
   */
  255U,

  /* Mask Parameter: CounterLimited1_uplimit_d
   * Referenced by: '<S55>/FixPt Switch'
   */
  255U,

  /* Mask Parameter: CounterLimited1_uplimit_fk
   * Referenced by: '<S63>/FixPt Switch'
   */
  255U,

  /* Computed Parameter: FBS_DebugInfo_Y0
   * Referenced by: '<S2>/FBS_DebugInfo'
   */
  {
    0U,                                /* EAD_McuVersion */
    0U,                                /* EAD_SystemState */
    0.0F,                              /* EAD_AccRequest */
    0.0F,                              /* EAD_JerkRequest */
    0.0F,                              /* EAD_SteeringAngle */
    0.0F,                              /* EAD_LimitSteeringAngle */
    0.0F                               /* EAD_MaxSteeringAngleRate */
  },

  /* Computed Parameter: VIMMid3CanFr14_SG_AdNomALgtReqGroupSafe_Y0
   * Referenced by: '<S2>/VIMMid3CanFr14_SG_AdNomALgtReqGroupSafe'
   */
  {
    1920U,                             /* AdNomALgtReqGroupSafeALgtNomReqMax */
    1920U,                             /* AdNomALgtReqGroupSafeALgtNomReqMin */
    0U,                                /* AdNomALgtReqGroupSafeChks */
    0U,                                /* AdNomALgtReqGroupSafeCntr */
    2000U,                             /* AdNomALgtReqGroupSafeNegLimForJerk */
    2000U                              /* AdNomALgtReqGroupSafePosLimForJerk */
  },

  /* Computed Parameter: VIMBMid6CanFdFr28_SG_SecAdNomALgtReqGroupSafe_Y0
   * Referenced by: '<S2>/VIMBMid6CanFdFr28_SG_SecAdNomALgtReqGroupSafe'
   */
  {
    1920U,                           /* SecAdNomALgtReqGroupSafeALgtNomReqMax */
    1920U,                           /* SecAdNomALgtReqGroupSafeALgtNomReqMin */
    0U,                                /* SecAdNomALgtReqGroupSafeChks */
    0U,                                /* SecAdNomALgtReqGroupSafeCntr */
    2000U,                           /* SecAdNomALgtReqGroupSafeNegLimForJerk */
    2000U                            /* SecAdNomALgtReqGroupSafePosLimForJerk */
  },

  /* Computed Parameter: VIMMid3CanFr09_SG_AdDirReq_Y0
   * Referenced by: '<S2>/VIMMid3CanFr09_SG_AdDirReq'
   */
  {
    0U,                                /* AdDirReqChks */
    0U,                                /* AdDirReqCntr */
    0U                                 /* AdDirReqDirReq */
  },

  /* Computed Parameter: VIMMid3CanFr15_SG_AdPrimALgtLimReqGroupSafe_Y0
   * Referenced by: '<S2>/VIMMid3CanFr15_SG_AdPrimALgtLimReqGroupSafe'
   */
  {
    1920U,                             /* AdPrimALgtLimReqGroupSafeALgtMaxReq */
    1920U,                             /* AdPrimALgtLimReqGroupSafeALgtMinReq */
    0U,                                /* AdPrimALgtLimReqGroupSafeChks */
    0U                                 /* AdPrimALgtLimReqGroupSafeCntr */
  },

  /* Computed Parameter: VIMMid5CanFdFr12_SG_AdPrimWhlAgReqGroupSafe_Y0
   * Referenced by: '<S2>/VIMMid5CanFdFr12_SG_AdPrimWhlAgReqGroupSafe'
   */
  {
    0U,                                /* AdPrimWhlAgReqGroupSafeChks */
    0U,                                /* AdPrimWhlAgReqGroupSafeCntr */
    16194U                             /* AdPrimWhlAgReqGroupSafeWhlAgReq */
  },

  /* Computed Parameter: VIMBMid6CanFdFr28_SG_AdSecALgtLimReqGroupSafe_Y0
   * Referenced by: '<S2>/VIMBMid6CanFdFr28_SG_AdSecALgtLimReqGroupSafe'
   */
  {
    1920U,                             /* AdSecALgtLimReqGroupSafeALgtMaxReq */
    1920U,                             /* AdSecALgtLimReqGroupSafeALgtMinReq */
    0U,                                /* AdSecALgtLimReqGroupSafeChks */
    0U                                 /* AdSecALgtLimReqGroupSafeCntr */
  },

  /* Computed Parameter: VIMBMid6CanFdFr14_SG_AdSecWhlAgReqGroupSafe_Y0
   * Referenced by: '<S2>/VIMBMid6CanFdFr14_SG_AdSecWhlAgReqGroupSafe'
   */
  {
    0U,                                /* AdSecWhlAgReqGroupSafeChks */
    0U,                                /* AdSecWhlAgReqGroupSafeCntr */
    16194U                             /* AdSecWhlAgReqGroupSafeWhlAgReq */
  },

  /* Computed Parameter: VIMMid3CanFr09_SG_AdStandStillReq_Y0
   * Referenced by: '<S2>/VIMMid3CanFr09_SG_AdStandStillReq'
   */
  {
    0U,                                /* AdStandStillReqChks */
    0U,                                /* AdStandStillReqCntr */
    0U                                 /* AdStandStillReqReq */
  },

  /* Computed Parameter: VIMMid3CanFr13_SG_AdWhlLockReq_Y0
   * Referenced by: '<S2>/VIMMid3CanFr13_SG_AdWhlLockReq'
   */
  {
    0U,                                /* AdWhlLockReqChks */
    0U,                                /* AdWhlLockReqCntr */
    0U                                 /* AdWhlLockReqNoReqApplyRel */
  },

  /* Computed Parameter: VIMMid3CanFr11_SG_AdpLiReqFromAPI_Y0
   * Referenced by: '<S2>/VIMMid3CanFr11_SG_AdpLiReqFromAPI'
   */
  {
    false,                             /* AdpLiReqFromAPIAhbcActvn */
    0U,                                /* AdpLiReqFromAPIChks */
    0U,                                /* AdpLiReqFromAPICntr */
    false,                             /* AdpLiReqFromAPIHzrdLiActvnReq */
    false,                             /* AdpLiReqFromAPIHzrdLiDeactnReq */
    false,                             /* AdpLiReqFromAPIIncrLiRiReq */
    false                              /* AdpLiReqFromAPIIndcrLeReq */
  },

  /* Computed Parameter: VIMMid3CanFr07_SG_AutnmsDrvStReq_Y0
   * Referenced by: '<S2>/VIMMid3CanFr07_SG_AutnmsDrvStReq'
   */
  {
    0U,                                /* AutnmsDrvStReqAutnmsDrvStReq */
    0U,                                /* AutnmsDrvStReqChecksum */
    0U                                 /* AutnmsDrvStReqCounter */
  },

  /* Computed Parameter: VIMMid3CanFr04_SG_HmiAutnmsSts_Y0
   * Referenced by: '<S2>/VIMMid3CanFr04_SG_HmiAutnmsSts'
   */
  {
    0U,                                /* HmiAutnmsStsChecksum */
    0U,                                /* HmiAutnmsStsCounter */
    0U                                 /* HmiAutnmsStsHmiAutnmsSts */
  },

  /* Computed Parameter: VIMBMid6CanFdFr29_SG_SecAdWhlLockReq_Y0
   * Referenced by: '<S2>/VIMBMid6CanFdFr29_SG_SecAdWhlLockReq'
   */
  {
    0U,                                /* SecAdWhlLockReqChks */
    0U,                                /* SecAdWhlLockReqCntr */
    0U                                 /* SecAdWhlLockReqNoReqApplyRel */
  },

  /* Computed Parameter: VIMMid3CanFr08_SG_VehOperStReq_Y0
   * Referenced by: '<S2>/VIMMid3CanFr08_SG_VehOperStReq'
   */
  {
    0U,                                /* VehOperStReqChecksum */
    0U,                                /* VehOperStReqCounter */
    0U                                 /* VehOperStReqVehOperStReq */
  },

  /* Computed Parameter: VCU1Mid3CanFr06_CarTiGlb_A_Y0
   * Referenced by: '<S2>/VCU1Mid3CanFr06_CarTiGlb_A'
   */
  0U,

  /* Computed Parameter: FallbackDebugInfo_TrajectoryCurvatureChange_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_TrajectoryCurvatureChange'
   */
  480000U,

  /* Computed Parameter: Constant6_Value
   * Referenced by: '<S8>/Constant6'
   */
  3.1416F,

  /* Computed Parameter: Constant4_Value
   * Referenced by: '<S8>/Constant4'
   */
  180.0F,

  /* Computed Parameter: Constant49_Value
   * Referenced by: '<S8>/Constant49'
   */
  -0.85F,

  /* Computed Parameter: Constant51_Value
   * Referenced by: '<S8>/Constant51'
   */
  -0.85F,

  /* Computed Parameter: Constant13_Value
   * Referenced by: '<S8>/Constant13'
   */
  1.25F,

  /* Computed Parameter: Constant14_Value
   * Referenced by: '<S8>/Constant14'
   */
  0.25F,

  /* Computed Parameter: Constant16_Value
   * Referenced by: '<S8>/Constant16'
   */
  1.0F,

  /* Computed Parameter: Constant_Value
   * Referenced by: '<S17>/Constant'
   */
  22.0F,

  /* Computed Parameter: IAM_Ts_P1_Value
   * Referenced by: '<S20>/IAM_Ts_P1'
   */
  0.0F,

  /* Computed Parameter: Constant8_Value
   * Referenced by: '<S17>/Constant8'
   */
  -3.0F,

  /* Computed Parameter: Constant6_Value_c
   * Referenced by: '<S17>/Constant6'
   */
  -10.0F,

  /* Computed Parameter: Constant7_Value
   * Referenced by: '<S17>/Constant7'
   */
  -20.0F,

  /* Computed Parameter: Constant14_Value_l
   * Referenced by: '<S17>/Constant14'
   */
  -6.0F,

  /* Computed Parameter: Constant11_Value
   * Referenced by: '<S17>/Constant11'
   */
  -5.0F,

  /* Computed Parameter: Constant4_Value_b
   * Referenced by: '<S17>/Constant4'
   */
  -3.0F,

  /* Computed Parameter: Constant3_Value
   * Referenced by: '<S17>/Constant3'
   */
  -3.0F,

  /* Computed Parameter: Constant10_Value
   * Referenced by: '<S17>/Constant10'
   */
  -1.0F,

  /* Computed Parameter: Constant19_Value
   * Referenced by: '<S17>/Constant19'
   */
  1.0F,

  /* Computed Parameter: Constant20_Value
   * Referenced by: '<S17>/Constant20'
   */
  0.01F,

  /* Computed Parameter: IAM_Ts_P2_Value
   * Referenced by: '<S21>/IAM_Ts_P2'
   */
  0.0F,

  /* Computed Parameter: Constant17_Value
   * Referenced by: '<S18>/Constant17'
   */
  -5.0F,

  /* Computed Parameter: Constant1_Value
   * Referenced by: '<S18>/Constant1'
   */
  -3.0F,

  /* Computed Parameter: Constant3_Value_c
   * Referenced by: '<S18>/Constant3'
   */
  -3.0F,

  /* Computed Parameter: Constant2_Value
   * Referenced by: '<S18>/Constant2'
   */
  -1.0F,

  /* Computed Parameter: Constant5_Value
   * Referenced by: '<S18>/Constant5'
   */
  -1.0F,

  /* Computed Parameter: Constant4_Value_j
   * Referenced by: '<S18>/Constant4'
   */
  -3.0F,

  /* Computed Parameter: Constant9_Value
   * Referenced by: '<S18>/Constant9'
   */
  -100.0F,

  /* Computed Parameter: Constant6_Value_h
   * Referenced by: '<S18>/Constant6'
   */
  -2.0F,

  /* Computed Parameter: Constant7_Value_m
   * Referenced by: '<S18>/Constant7'
   */
  -2.0F,

  /* Computed Parameter: Constant45_Value
   * Referenced by: '<S16>/Constant45'
   */
  -10.0F,

  /* Computed Parameter: Constant47_Value
   * Referenced by: '<S16>/Constant47'
   */
  -10.0F,

  /* Computed Parameter: Constant53_Value
   * Referenced by: '<S16>/Constant53'
   */
  -10.0F,

  /* Computed Parameter: Constant57_Value
   * Referenced by: '<S16>/Constant57'
   */
  -100.0F,

  /* Computed Parameter: Constant59_Value
   * Referenced by: '<S16>/Constant59'
   */
  -100.0F,

  /* Computed Parameter: IAM_Ts_P2_Value_m
   * Referenced by: '<S38>/IAM_Ts_P2'
   */
  0.0F,

  /* Computed Parameter: IAM_Ts_P2_Value_e
   * Referenced by: '<S39>/IAM_Ts_P2'
   */
  0.0F,

  /* Computed Parameter: IAM_Ts_P2_Value_c
   * Referenced by: '<S40>/IAM_Ts_P2'
   */
  0.0F,

  /* Computed Parameter: IAM_Ts_P2_Value_b
   * Referenced by: '<S41>/IAM_Ts_P2'
   */
  0.0F,

  /* Computed Parameter: Constant10_Value_h
   * Referenced by: '<S57>/Constant10'
   */
  0.1F,

  /* Computed Parameter: Constant19_Value_m
   * Referenced by: '<S57>/Constant19'
   */
  1.0F,

  /* Computed Parameter: Constant20_Value_h
   * Referenced by: '<S57>/Constant20'
   */
  0.01F,

  /* Computed Parameter: IAM_Ts_P2_Value_mh
   * Referenced by: '<S61>/IAM_Ts_P2'
   */
  0.0F,

  /* Computed Parameter: Constant1_Value_d
   * Referenced by: '<S82>/Constant1'
   */
  0.0F,

  /* Computed Parameter: Constant1_Value_j
   * Referenced by: '<S83>/Constant1'
   */
  0.0F,

  /* Computed Parameter: VTS_WheelSpeedFrntRi_Y0
   * Referenced by: '<S71>/VTS_WheelSpeedFrntRi'
   */
  0.0F,

  /* Computed Parameter: VTS_WheelSpeedFrntLe_Y0
   * Referenced by: '<S71>/VTS_WheelSpeedFrntLe'
   */
  0.0F,

  /* Computed Parameter: Constant_Value_g
   * Referenced by: '<S82>/Constant'
   */
  3.6F,

  /* Computed Parameter: Constant3_Value_f
   * Referenced by: '<S82>/Constant3'
   */
  0.01F,

  /* Computed Parameter: Constant_Value_k
   * Referenced by: '<S83>/Constant'
   */
  3.6F,

  /* Computed Parameter: Constant3_Value_d
   * Referenced by: '<S83>/Constant3'
   */
  0.01F,

  /* Computed Parameter: Constant1_Value_p
   * Referenced by: '<S89>/Constant1'
   */
  0.0F,

  /* Computed Parameter: Constant1_Value_g
   * Referenced by: '<S90>/Constant1'
   */
  0.0F,

  /* Computed Parameter: VTS_WheelSpeedReRi_Y0
   * Referenced by: '<S72>/VTS_WheelSpeedReRi'
   */
  0.0F,

  /* Computed Parameter: VTS_WheelSpeedReLe_Y0
   * Referenced by: '<S72>/VTS_WheelSpeedReLe'
   */
  0.0F,

  /* Computed Parameter: Constant3_Value_p
   * Referenced by: '<S89>/Constant3'
   */
  0.01F,

  /* Computed Parameter: Constant_Value_f
   * Referenced by: '<S89>/Constant'
   */
  3.6F,

  /* Computed Parameter: Constant3_Value_n
   * Referenced by: '<S90>/Constant3'
   */
  0.01F,

  /* Computed Parameter: Constant_Value_d
   * Referenced by: '<S90>/Constant'
   */
  3.6F,

  /* Computed Parameter: VTS_VehicleSpeed_Y0
   * Referenced by: '<S73>/VTS_VehicleSpeed'
   */
  0.0F,

  /* Computed Parameter: Constant_Value_a
   * Referenced by: '<S95>/Constant'
   */
  3.6F,

  /* Computed Parameter: VTS_SteerWheelRotSpd_Y0
   * Referenced by: '<S74>/VTS_SteerWheelRotSpd'
   */
  0.0F,

  /* Computed Parameter: VTS_SteerWheelAngle_Y0
   * Referenced by: '<S74>/VTS_SteerWheelAngle'
   */
  0.0F,

  /* Computed Parameter: Constant1_Value_jk
   * Referenced by: '<S99>/Constant1'
   */
  57.2957802F,

  /* Computed Parameter: Constant2_Value_f
   * Referenced by: '<S99>/Constant2'
   */
  57.2957802F,

  /* Computed Parameter: VTS_YawRate_Y0
   * Referenced by: '<S76>/VTS_YawRate'
   */
  0.0F,

  /* Computed Parameter: VTS_LateralAcce_Y0
   * Referenced by: '<S76>/VTS_LateralAcce'
   */
  0.0F,

  /* Computed Parameter: Constant1_Value_l
   * Referenced by: '<S106>/Constant1'
   */
  9.8F,

  /* Computed Parameter: Constant3_Value_c3
   * Referenced by: '<S106>/Constant3'
   */
  57.2957802F,

  /* Computed Parameter: VTS_LongitAcce_Y0
   * Referenced by: '<S77>/VTS_LongitAcce'
   */
  0.0F,

  /* Computed Parameter: Constant1_Value_ji
   * Referenced by: '<S113>/Constant1'
   */
  9.8F,

  /* Computed Parameter: Constant10_Value_hs
   * Referenced by: '<S8>/Constant10'
   */
  3.6F,

  /* Expression: single([0 2 3 8 10 20 30 40 50 60])
   * Referenced by: '<S8>/1-D Lookup Table'
   */
  { 0.0F, 2.0F, 3.0F, 8.0F, 10.0F, 20.0F, 30.0F, 40.0F, 50.0F, 60.0F },

  /* Computed Parameter: Constant1_Value_m
   * Referenced by: '<S8>/Constant1'
   */
  3.1416F,

  /* Computed Parameter: Constant2_Value_d
   * Referenced by: '<S8>/Constant2'
   */
  180.0F,

  /* Computed Parameter: Constant3_Value_dk
   * Referenced by: '<S8>/Constant3'
   */
  3.1416F,

  /* Computed Parameter: Constant48_Value
   * Referenced by: '<S8>/Constant48'
   */
  0.85F,

  /* Computed Parameter: Constant5_Value_c
   * Referenced by: '<S8>/Constant5'
   */
  180.0F,

  /* Computed Parameter: Constant50_Value
   * Referenced by: '<S8>/Constant50'
   */
  0.85F,

  /* Computed Parameter: UnitDelay_InitialCondition
   * Referenced by: '<S11>/Unit Delay'
   */
  0.0F,

  /* Computed Parameter: Constant18_Value
   * Referenced by: '<S17>/Constant18'
   */
  0.1F,

  /* Computed Parameter: UnitDelay_InitialCondition_f
   * Referenced by: '<S21>/Unit Delay'
   */
  0.0F,

  /* Computed Parameter: IAM_Ts_P4_Value
   * Referenced by: '<S20>/IAM_Ts_P4'
   */
  1.0F,

  /* Computed Parameter: UnitDelay_InitialCondition_e
   * Referenced by: '<S20>/Unit Delay'
   */
  0.0F,

  /* Computed Parameter: UnitDelay_InitialCondition_g
   * Referenced by: '<S19>/Unit Delay'
   */
  0.0F,

  /* Computed Parameter: Constant52_Value
   * Referenced by: '<S16>/Constant52'
   */
  3.0F,

  /* Computed Parameter: Constant2_Value_g
   * Referenced by: '<S16>/Constant2'
   */
  -100.0F,

  /* Computed Parameter: Constant58_Value
   * Referenced by: '<S16>/Constant58'
   */
  22.0F,

  /* Computed Parameter: Constant3_Value_b
   * Referenced by: '<S16>/Constant3'
   */
  22.0F,

  /* Computed Parameter: Constant56_Value
   * Referenced by: '<S16>/Constant56'
   */
  22.0F,

  /* Computed Parameter: Constant46_Value
   * Referenced by: '<S16>/Constant46'
   */
  3.0F,

  /* Computed Parameter: Constant7_Value_l
   * Referenced by: '<S16>/Constant7'
   */
  3.0F,

  /* Computed Parameter: Constant44_Value
   * Referenced by: '<S16>/Constant44'
   */
  3.0F,

  /* Computed Parameter: Constant8_Value_g
   * Referenced by: '<S16>/Constant8'
   */
  -10.0F,

  /* Computed Parameter: Constant5_Value_h
   * Referenced by: '<S10>/Constant5'
   */
  29.0F,

  /* Computed Parameter: UnitDelay_InitialCondition_e5
   * Referenced by: '<S40>/Unit Delay'
   */
  0.0F,

  /* Computed Parameter: UnitDelay_InitialCondition_p
   * Referenced by: '<S41>/Unit Delay'
   */
  0.0F,

  /* Computed Parameter: UnitDelay_InitialCondition_eo
   * Referenced by: '<S38>/Unit Delay'
   */
  0.0F,

  /* Computed Parameter: UnitDelay_InitialCondition_i
   * Referenced by: '<S39>/Unit Delay'
   */
  0.0F,

  /* Computed Parameter: Constant24_Value
   * Referenced by: '<S47>/Constant24'
   */
  0.01F,

  /* Computed Parameter: UnitDelay1_InitialCondition
   * Referenced by: '<S49>/Unit Delay1'
   */
  0.0F,

  /* Computed Parameter: Constant19_Value_h
   * Referenced by: '<S49>/Constant19'
   */
  1000.0F,

  /* Computed Parameter: IAM_Ts_P2_Value_j
   * Referenced by: '<S49>/IAM_Ts_P2'
   */
  0.0F,

  /* Computed Parameter: Constant23_Value
   * Referenced by: '<S49>/Constant23'
   */
  1000.0F,

  /* Computed Parameter: Constant18_Value_c
   * Referenced by: '<S57>/Constant18'
   */
  0.1F,

  /* Computed Parameter: UnitDelay_InitialCondition_fd
   * Referenced by: '<S61>/Unit Delay'
   */
  0.0F,

  /* Computed Parameter: Constant20_Value_j
   * Referenced by: '<S49>/Constant20'
   */
  255U,

  /* Computed Parameter: FallbackDebugInfo_AccRequestBySpeed_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_AccRequestBySpeed'
   */
  200U,

  /* Computed Parameter: FallbackDebugInfo_AccRequestByOutOfOdd_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_AccRequestByOutOfOdd'
   */
  200U,

  /* Computed Parameter: FallbackDebugInfo_AccRequestForSystemError_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_AccRequestForSystemError'
   */
  200U,

  /* Computed Parameter: FallbackDebugInfo_AccRequestAfterRateLimit_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_AccRequestAfterRateLimit'
   */
  200U,

  /* Computed Parameter: FallbackSystemStatus_AcuMid3SsmCounter0MessageID_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_AcuMid3SsmCounter0MessageID'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_AcuFbCanMessageID_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_AcuFbCanMessageID'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_AcuMid3SsmCounter0Timer_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_AcuMid3SsmCounter0Timer'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_AcuMid3SsmCounter1MessageID_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_AcuMid3SsmCounter1MessageID'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_AcuMid3SsmCounter1Timer_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_AcuMid3SsmCounter1Timer'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_AcuMid5SsmCounter0MessageID_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_AcuMid5SsmCounter0MessageID'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_AcuMid5SsmCounter0Timer_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_AcuMid5SsmCounter0Timer'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_AcuMid5SsmCounter1MessageID_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_AcuMid5SsmCounter1MessageID'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_AcuMid5SsmCounter1Timer_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_AcuMid5SsmCounter1Timer'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_AcuMid6SsmCounter1Timer_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_AcuMid6SsmCounter1Timer'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_AcuFbCanTimer_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_AcuFbCanTimer'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_AcuMid6SsmCounter0MessageID_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_AcuMid6SsmCounter0MessageID'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_AcuMid6SsmCounter0Timer_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_AcuMid6SsmCounter0Timer'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_AcuMid6SsmCounter1MessageID_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_AcuMid6SsmCounter1MessageID'
   */
  0U,

  /* Computed Parameter: VIMMid3CanFr13_AdSetSpd_Y0
   * Referenced by: '<S2>/VIMMid3CanFr13_AdSetSpd'
   */
  0U,

  /* Computed Parameter: ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedKPH_Y0
   * Referenced by: '<S2>/ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedKPH'
   */
  0U,

  /* Computed Parameter: ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedKPH_write_Y0
   * Referenced by: '<S2>/ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedKPH_write'
   */
  0U,

  /* Computed Parameter: ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedKPH_Y0
   * Referenced by: '<S2>/ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedKPH'
   */
  0U,

  /* Computed Parameter: ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedKPH_Y0
   * Referenced by: '<S2>/ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedKPH'
   */
  0U,

  /* Computed Parameter: ESC_Status_ESC_VehicleSpeed_Y0
   * Referenced by: '<S2>/ESC_Status_ESC_VehicleSpeed'
   */
  0U,

  /* Computed Parameter: FallbackDebugInfo_EgoLaneWidth_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_EgoLaneWidth'
   */
  0U,

  /* Computed Parameter: FallbackDebugInfo_EgoStopTime_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_EgoStopTime'
   */
  0U,

  /* Computed Parameter: FallbackDebugInfo_EmergencyBrakeAcc_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_EmergencyBrakeAcc'
   */
  200U,

  /* Computed Parameter: FbAcuAvailable_FallbackSelfCheckStatus_Y0
   * Referenced by: '<S2>/FbAcuAvailable_FallbackSelfCheckStatus'
   */
  0U,

  /* Computed Parameter: FallbackDebugInfo_FallbackDebugInfoReserve2_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_FallbackDebugInfoReserve2'
   */
  0U,

  /* Computed Parameter: FallbackDebugInfo_FallbackDebugInfoReserve3_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_FallbackDebugInfoReserve3'
   */
  0U,

  /* Computed Parameter: FallbackDebugInfo_FeedforwardsSteerAngle_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_FeedforwardsSteerAngle'
   */
  7800U,

  /* Computed Parameter: FallbackSystemStatus_FrontCameraCanMessageID_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_FrontCameraCanMessageID'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_FrontCameraCanTimer_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_FrontCameraCanTimer'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_FrontRadarCanMessageID_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_FrontRadarCanMessageID'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_FrontRadarCanTimer_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_FrontRadarCanTimer'
   */
  0U,

  /* Computed Parameter: FallbackDebugInfo_GradientLimitAccRequest_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_GradientLimitAccRequest'
   */
  200U,

  /* Computed Parameter: FallbackDebugInfo_HeadingAngleError_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_HeadingAngleError'
   */
  200U,

  /* Computed Parameter: FallbackDebugInfo_LateralDistanceError_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_LateralDistanceError'
   */
  3000U,

  /* Computed Parameter: FallbackDebugInfo_LimitAccRequest_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_LimitAccRequest'
   */
  200U,

  /* Computed Parameter: FallbackDebugInfo_LimitSteerAngle_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_LimitSteerAngle'
   */
  7800U,

  /* Computed Parameter: FallbackDebugInfo_LongNecAcc_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_LongNecAcc'
   */
  200U,

  /* Computed Parameter: FallbackDebugInfo_LongAccRequest_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_LongAccRequest'
   */
  200U,

  /* Computed Parameter: FallbackDebugInfo_LqrIterationError_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_LqrIterationError'
   */
  0U,

  /* Computed Parameter: FallbackDebugInfo_LqrIterationNums_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_LqrIterationNums'
   */
  0U,

  /* Computed Parameter: FallbackDebugInfo_MaxSteerAngleThreshold_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_MaxSteerAngleThreshold'
   */
  7800U,

  /* Computed Parameter: FallbackDebugInfo_ObjectStopTime_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_ObjectStopTime'
   */
  0U,

  /* Computed Parameter: SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNom_C_Y0
   * Referenced by: '<S2>/SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNom_C'
   */
  16384U,

  /* Computed Parameter: SSMMid5CanFdFr03_1V1R_PrimALatDataRawSafeNom_C_Y0
   * Referenced by: '<S2>/SSMMid5CanFdFr03_1V1R_PrimALatDataRawSafeNom_C'
   */
  16384U,

  /* Computed Parameter: SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNom_C_Y0
   * Referenced by: '<S2>/SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNom_C'
   */
  0U,

  /* Computed Parameter: FallbackDebugInfo_RawAccRequest_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_RawAccRequest'
   */
  200U,

  /* Computed Parameter: FallbackDebugInfo_RawSteerAngle_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_RawSteerAngle'
   */
  7800U,

  /* Computed Parameter: SAS_Status_SAS_SteerWheelAngle_Y0
   * Referenced by: '<S2>/SAS_Status_SAS_SteerWheelAngle'
   */
  0,

  /* Computed Parameter: FallbackDebugInfo_SteerAngleByLQR_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_SteerAngleByLQR'
   */
  0U,

  /* Computed Parameter: FallbackDebugInfo_SteerAngleForSystemError_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_SteerAngleForSystemError'
   */
  0U,

  /* Computed Parameter: FallbackDebugInfo_SteerAngle_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_SteerAngle'
   */
  7800U,

  /* Computed Parameter: FallbackDebugInfo_TimeToCollison_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_TimeToCollison'
   */
  0U,

  /* Computed Parameter: FallbackDebugInfo_TrajectoryCurvature_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_TrajectoryCurvature'
   */
  15625U,

  /* Computed Parameter: FallbackDebugInfo_TrajectoryHeadingAngle_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_TrajectoryHeadingAngle'
   */
  200U,

  /* Computed Parameter: FallbackDebugInfo_TrajectoryLength_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_TrajectoryLength'
   */
  0U,

  /* Computed Parameter: FallbackDebugInfo_TrajectoryPosY0_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_TrajectoryPosY0'
   */
  3000U,

  /* Computed Parameter: FallbackSystemStatus_VehMid3SsmCounter0MessageID_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_VehMid3SsmCounter0MessageID'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_VehMid3SsmCounter0Timer_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_VehMid3SsmCounter0Timer'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_VehMid3SsmCounter1MessageID_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_VehMid3SsmCounter1MessageID'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_VehMid3SsmCounter1Timer_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_VehMid3SsmCounter1Timer'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_VehMid3VcuCounter0MessageID_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_VehMid3VcuCounter0MessageID'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_VehMid3VcuCounter0Timer_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_VehMid3VcuCounter0Timer'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_VehMid3VcuCounter1MessageID_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_VehMid3VcuCounter1MessageID'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_VehMid3VcuCounter1Timer_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_VehMid3VcuCounter1Timer'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_VehMid5SsmCounter0MessageID_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_VehMid5SsmCounter0MessageID'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_VehMid5SsmCounter0Timer_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_VehMid5SsmCounter0Timer'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_VehMid5SsmCounter1MessageID_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_VehMid5SsmCounter1MessageID'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_VehMid5SsmCounter1Timer_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_VehMid5SsmCounter1Timer'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_VehMid6SsmCounter0MessageID_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_VehMid6SsmCounter0MessageID'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_VehMid6SsmCounter0Timer_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_VehMid6SsmCounter0Timer'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_VehMid6SsmCounter1MessageID_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_VehMid6SsmCounter1MessageID'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_VehMid6SsmCounter1Timer_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_VehMid6SsmCounter1Timer'
   */
  0U,

  /* Computed Parameter: YRS1_YRS_LateralAcce_Y0
   * Referenced by: '<S2>/YRS1_YRS_LateralAcce'
   */
  2000U,

  /* Computed Parameter: YRS2_YRS_LongitAcce_Y0
   * Referenced by: '<S2>/YRS2_YRS_LongitAcce'
   */
  2000U,

  /* Computed Parameter: YRS1_YRS_YawRate_Y0
   * Referenced by: '<S2>/YRS1_YRS_YawRate'
   */
  18000U,

  /* Computed Parameter: VCU1Mid3CanFr08_1V1R_YawRate1_C_Y0
   * Referenced by: '<S2>/VCU1Mid3CanFr08_1V1R_YawRate1_C'
   */
  0,

  /* Computed Parameter: Constant14_Value_l4
   * Referenced by: '<S6>/Constant14'
   */
  0U,

  /* Computed Parameter: Out1_Y0
   * Referenced by: '<S65>/Out1'
   */
  0U,

  /* Computed Parameter: Constant_Value_l
   * Referenced by: '<S65>/Constant'
   */
  1U,

  /* Computed Parameter: UnitDelay_InitialCondition_pb
   * Referenced by: '<S65>/Unit Delay'
   */
  0U,

  /* Computed Parameter: Constant_Value_go
   * Referenced by: '<S47>/Constant'
   */
  99U,

  /* Computed Parameter: Constant1_Value_k
   * Referenced by: '<S47>/Constant1'
   */
  52U,

  /* Computed Parameter: Constant2_Value_fd
   * Referenced by: '<S47>/Constant2'
   */
  57U,

  /* Computed Parameter: Constant3_Value_a
   * Referenced by: '<S47>/Constant3'
   */
  55U,

  /* Computed Parameter: Constant4_Value_k
   * Referenced by: '<S47>/Constant4'
   */
  32U,

  /* Computed Parameter: Constant5_Value_m
   * Referenced by: '<S47>/Constant5'
   */
  21U,

  /* Computed Parameter: Constant6_Value_e
   * Referenced by: '<S47>/Constant6'
   */
  21U,

  /* Computed Parameter: Constant7_Value_o
   * Referenced by: '<S47>/Constant7'
   */
  23U,

  /* Computed Parameter: Constant8_Value_j
   * Referenced by: '<S47>/Constant8'
   */
  86U,

  /* Computed Parameter: Constant9_Value_g
   * Referenced by: '<S47>/Constant9'
   */
  87U,

  /* Computed Parameter: Constant10_Value_j
   * Referenced by: '<S47>/Constant10'
   */
  34U,

  /* Computed Parameter: Constant11_Value_d
   * Referenced by: '<S47>/Constant11'
   */
  33U,

  /* Computed Parameter: Constant12_Value
   * Referenced by: '<S47>/Constant12'
   */
  80U,

  /* Computed Parameter: Constant13_Value_m
   * Referenced by: '<S47>/Constant13'
   */
  38U,

  /* Expression: 0x141
   * Referenced by: '<S47>/Constant14'
   */
  321U,

  /* Expression: 0x191
   * Referenced by: '<S47>/Constant15'
   */
  401U,

  /* Computed Parameter: Constant16_Value_o
   * Referenced by: '<S47>/Constant16'
   */
  128U,

  /* Computed Parameter: FallbackSystemStatus_AcuMid3SsmCounter0Timeout_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_AcuMid3SsmCounter0Timeout'
   */
  0,

  /* Computed Parameter: FallbackSystemStatus_AcuMid3SsmCounter1Timeout_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_AcuMid3SsmCounter1Timeout'
   */
  0,

  /* Computed Parameter: FallbackSystemStatus_AcuMid5SsmCounter0Timeout_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_AcuMid5SsmCounter0Timeout'
   */
  0,

  /* Computed Parameter: FallbackSystemStatus_AcuMid5SsmCounter1Timeout_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_AcuMid5SsmCounter1Timeout'
   */
  0,

  /* Computed Parameter: FallbackSystemStatus_AcuMid6SsmCounter0Timeout_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_AcuMid6SsmCounter0Timeout'
   */
  0,

  /* Computed Parameter: FallbackSystemStatus_AcuMid6SsmCounter1Timeout_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_AcuMid6SsmCounter1Timeout'
   */
  0,

  /* Computed Parameter: FallbackSystemStatus_AcuFbCanTimeout_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_AcuFbCanTimeout'
   */
  0,

  /* Computed Parameter: FallbackSystemStatus_AswSoftwarewareVersion_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_AswSoftwarewareVersion'
   */
  0U,

  /* Computed Parameter: FallbackDebugInfo_AvoidCollisionEnable_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_AvoidCollisionEnable'
   */
  0,

  /* Computed Parameter: ESC_DA_MESSAGE_ESC_ABA_active_Y0
   * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_ABA_active'
   */
  Cx0_INACTIVE,

  /* Computed Parameter: ESC_DA_MESSAGE_ESC_ABA_available_Y0
   * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_ABA_available'
   */
  Cx0_NOT_AVAILABLE,

  /* Computed Parameter: ESC_DA_MESSAGE_ESC_ABP_active_Y0
   * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_ABP_active'
   */
  Cx0_INACTIVE,

  /* Computed Parameter: ESC_DA_MESSAGE_ESC_ABP_available_Y0
   * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_ABP_available'
   */
  Cx0_NOT_AVAILABLE,

  /* Computed Parameter: ESC_Status_ESC_ABSActive_Y0
   * Referenced by: '<S2>/ESC_Status_ESC_ABSActive'
   */
  Cx0_INACTIVE,

  /* Computed Parameter: ESC_DA_MESSAGE_ESC_AEB_active_Y0
   * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_AEB_active'
   */
  Cx0_INACTIVE,

  /* Computed Parameter: ESC_DA_MESSAGE_ESC_AEB_available_Y0
   * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_AEB_available'
   */
  Cx0_NOT_AVAILABLE,

  /* Computed Parameter: ESC_Status_ESC_AVHStatus_Y0
   * Referenced by: '<S2>/ESC_Status_ESC_AVHStatus'
   */
  Cx0_OFF,

  /* Computed Parameter: ESC_DA_MESSAGE_ESC_AWB_active_Y0
   * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_AWB_active'
   */
  Cx0_INACTIVE,

  /* Computed Parameter: ESC_DA_MESSAGE_ESC_AWB_available_Y0
   * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_AWB_available'
   */
  Cx0_NOT_AVAILABLE,

  /* Computed Parameter: ESC_Status_ESC_BrakePedalSwitchInvalid_Y0
   * Referenced by: '<S2>/ESC_Status_ESC_BrakePedalSwitchInvalid'
   */
  Cx0_VALID,

  /* Computed Parameter: ESC_Status_ESC_BrakePedalSwitchStatus_Y0
   * Referenced by: '<S2>/ESC_Status_ESC_BrakePedalSwitchStatus'
   */
  Cx0_BRAKE_PEDAL_ISNOT_ACTIVE,

  /* Computed Parameter: ESC_DA_MESSAGE_ESC_BrakeTempTooHigh_Y0
   * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_BrakeTempTooHigh'
   */
  Cx0_NORMAL,

  /* Computed Parameter: ESC_DA_MESSAGE_ESC_DA_MESSAGE_AliveCounter_Y0
   * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_DA_MESSAGE_AliveCounter'
   */
  0U,

  /* Computed Parameter: ESC_DA_MESSAGE_ESC_DTC_Active_Y0
   * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_DTC_Active'
   */
  Cx0_INACTIVE,

  /* Computed Parameter: ESC_DA_MESSAGE_ESC_DiagExtModSts_Y0
   * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_DiagExtModSts'
   */
  Cx0_NOT_INTHE_EXTENSION_MODE,

  /* Computed Parameter: ESC_Status_ESC_EPBStatus_Y0
   * Referenced by: '<S2>/ESC_Status_ESC_EPBStatus'
   */
  Cx0_RELEASED,

  /* Computed Parameter: ESC_Status_ESC_ESPActive_Y0
   * Referenced by: '<S2>/ESC_Status_ESC_ESPActive'
   */
  Cx0_ESP_INACTIVE,

  /* Computed Parameter: ESC_Status_ESC_ESPFailed_Y0
   * Referenced by: '<S2>/ESC_Status_ESC_ESPFailed'
   */
  Cx0_ESP_NORMAL,

  /* Computed Parameter: ESC_FrontWheelSpeedKPH_ESC_FLWheelDirection_Y0
   * Referenced by: '<S2>/ESC_FrontWheelSpeedKPH_ESC_FLWheelDirection'
   */
  Cx0_STANDSTILL,

  /* Computed Parameter: ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedInvalid_Y0
   * Referenced by: '<S2>/ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedInvalid'
   */
  Cx0_VALID,

  /* Computed Parameter: ESC_FrontWheelSpeedKPH_ESC_FRWheelDirection_Y0
   * Referenced by: '<S2>/ESC_FrontWheelSpeedKPH_ESC_FRWheelDirection'
   */
  Cx0_STANDSTILL,

  /* Computed Parameter: ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedInvalid_Y0
   * Referenced by: '<S2>/ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedInvalid'
   */
  Cx0_VALID,

  /* Computed Parameter: ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_AliveCounter_Y0
   * Referenced by: '<S2>/ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_AliveCounter'
   */
  0U,

  /* Computed Parameter: ESC_Status_ESC_HHCActive_Y0
   * Referenced by: '<S2>/ESC_Status_ESC_HHCActive'
   */
  Cx0_INACTIVE,

  /* Computed Parameter: ESC_FrontWheelSpeedKPH_ESC_Mcylinder_PressureInvalid_Y0
   * Referenced by: '<S2>/ESC_FrontWheelSpeedKPH_ESC_Mcylinder_PressureInvalid'
   */
  Cx0_VALID,

  /* Computed Parameter: ESC_DA_MESSAGE_ESC_NoBrakeForce_Y0
   * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_NoBrakeForce'
   */
  Cx0_EXIST_BRAKE_FORCE,

  /* Computed Parameter: ESC_Status_ESC_PATAResponse_Y0
   * Referenced by: '<S2>/ESC_Status_ESC_PATAResponse'
   */
  Cx0_PATA_REQUESTED_EXECUTED_ESP_OFF,

  /* Computed Parameter: ESC_DA_MESSAGE_ESC_QDCFRS_Y0
   * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_QDCFRS'
   */
  Cx0_NO_ERROR,

  /* Computed Parameter: ESC_RearWheelSpeedKPH_ESC_RLWheelDirection_Y0
   * Referenced by: '<S2>/ESC_RearWheelSpeedKPH_ESC_RLWheelDirection'
   */
  Cx0_STANDSTILL,

  /* Computed Parameter: ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedInvalid_Y0
   * Referenced by: '<S2>/ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedInvalid'
   */
  Cx0_VALID,

  /* Computed Parameter: ESC_RearWheelSpeedKPH_ESC_RRWheelDirection_Y0
   * Referenced by: '<S2>/ESC_RearWheelSpeedKPH_ESC_RRWheelDirection'
   */
  Cx0_STANDSTILL,

  /* Computed Parameter: ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedInvalid_Y0
   * Referenced by: '<S2>/ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedInvalid'
   */
  Cx0_VALID,

  /* Computed Parameter: ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_AliveCounter_Y0
   * Referenced by: '<S2>/ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_AliveCounter'
   */
  0U,

  /* Computed Parameter: ESC_Status_ESC_Status_AliveCounter_Y0
   * Referenced by: '<S2>/ESC_Status_ESC_Status_AliveCounter'
   */
  0U,

  /* Computed Parameter: ESC_Status_ESC_TCSActive_Y0
   * Referenced by: '<S2>/ESC_Status_ESC_TCSActive'
   */
  Cx0_TCS_INACTIVE,

  /* Computed Parameter: ESC_Status_ESC_VehicleSpeedInvalid_Y0
   * Referenced by: '<S2>/ESC_Status_ESC_VehicleSpeedInvalid'
   */
  Cx0_VALID,

  /* Computed Parameter: ESC_DA_MESSAGE_ESC_Vehiclestandstill_Y0
   * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_Vehiclestandstill'
   */
  Cx0_NOT_STANDSTILL,

  /* Computed Parameter: FallbackSystemStatus_FrontCameraCalibrationStatus_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_FrontCameraCalibrationStatus'
   */
  Cx0_Unknown,

  /* Computed Parameter: FallbackSystemStatus_FrontRadarCalibrationStatus_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_FrontRadarCalibrationStatus'
   */
  0U,

  /* Computed Parameter: FallbackDebugInfo_FallbackTriggerStatus_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_FallbackTriggerStatus'
   */
  0U,

  /* Computed Parameter: FallbackDebugInfo_LateralDistanceErrorWeight_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_LateralDistanceErrorWeight'
   */
  0U,

  /* Computed Parameter: FallbackDebugInfo_HeadingAngleErrorWeight_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_HeadingAngleErrorWeight'
   */
  0U,

  /* Computed Parameter: FallbackDebugInfo_LaneValidState_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_LaneValidState'
   */
  0U,

  /* Computed Parameter: FallbackDebugInfo_LateralContribution_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_LateralContribution'
   */
  0U,

  /* Computed Parameter: FallbackDebugInfo_HeadingAngleContribution_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_HeadingAngleContribution'
   */
  0U,

  /* Computed Parameter: FallbackDebugInfo_MaxSteerAngleRateThreshold_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_MaxSteerAngleRateThreshold'
   */
  0U,

  /* Computed Parameter: FallbackDebugInfo_MinAccRate_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_MinAccRate'
   */
  0U,

  /* Computed Parameter: SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNomQf_C_Y0
   * Referenced by: '<S2>/SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNomQf_C'
   */
  Cx0_GenQf1_UndefindDataAccur,

  /* Computed Parameter: SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNomQf_C_Y0
   * Referenced by: '<S2>/SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNomQf_C'
   */
  Cx0_GenQf1_UndefindDataAccur,

  /* Computed Parameter: VCU1Mid3CanFr03_1V1R_PrpsnTqDirAct_C_Y0
   * Referenced by: '<S2>/VCU1Mid3CanFr03_1V1R_PrpsnTqDirAct_C'
   */
  Cx0_PrpsnDirAct1_Ukwn,

  /* Computed Parameter: SAS_Status_SAS_CalibrationSts_Y0
   * Referenced by: '<S2>/SAS_Status_SAS_CalibrationSts'
   */
  Cx0_SENSOR_NOT_CALIBRATED,

  /* Computed Parameter: SAS_Status_SAS_FailureSts_Y0
   * Referenced by: '<S2>/SAS_Status_SAS_FailureSts'
   */
  Cx0_INVALID,

  /* Computed Parameter: SAS_Status_SAS_Status_AliveCounter_Y0
   * Referenced by: '<S2>/SAS_Status_SAS_Status_AliveCounter'
   */
  0U,

  /* Computed Parameter: SAS_Status_SAS_SteerWheelRotSpd_Y0
   * Referenced by: '<S2>/SAS_Status_SAS_SteerWheelRotSpd'
   */
  0U,

  /* Computed Parameter: FallbackDebugInfo_SafeDistance_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_SafeDistance'
   */
  0U,

  /* Computed Parameter: VIMMid3CanFr04_UDcDcAvlLoSideExt_Y0
   * Referenced by: '<S2>/VIMMid3CanFr04_UDcDcAvlLoSideExt'
   */
  0U,

  /* Computed Parameter: VIMMid3CanFr09_VehUsgStReq_Y0
   * Referenced by: '<S2>/VIMMid3CanFr09_VehUsgStReq'
   */
  Cx0_ExtModSts_NoMod,

  /* Computed Parameter: VCU1Mid3CanFr36_1V1R_WhlLockStsLockSts_C_Y0
   * Referenced by: '<S2>/VCU1Mid3CanFr36_1V1R_WhlLockStsLockSts_C'
   */
  Cx0_WhlLockSts1_Ukwn,

  /* Computed Parameter: YRS1_YRS1_AliveCounter_Y0
   * Referenced by: '<S2>/YRS1_YRS1_AliveCounter'
   */
  0U,

  /* Computed Parameter: YRS2_YRS_AliveCounter_Y0
   * Referenced by: '<S2>/YRS2_YRS_AliveCounter'
   */
  0U,

  /* Computed Parameter: YRS1_YRS_LateralSensorState_Y0
   * Referenced by: '<S2>/YRS1_YRS_LateralSensorState'
   */
  Cx0_VALID,

  /* Computed Parameter: YRS2_YRS_LongitSensorState_Y0
   * Referenced by: '<S2>/YRS2_YRS_LongitSensorState'
   */
  Cx0_VALID,

  /* Computed Parameter: YRS1_YRS_YawRateSensorState_Y0
   * Referenced by: '<S2>/YRS1_YRS_YawRateSensorState'
   */
  Cx0_VALID,

  /* Computed Parameter: VCU1Mid3CanFr08_1V1R_YawRate1Qf1_C_Y0
   * Referenced by: '<S2>/VCU1Mid3CanFr08_1V1R_YawRate1Qf1_C'
   */
  Cx0_Qf1_DevOfDataUndefd,

  /* Computed Parameter: Constant7_Value_d
   * Referenced by: '<S24>/Constant7'
   */
  1,

  /* Computed Parameter: Constant6_Value_l
   * Referenced by: '<S24>/Constant6'
   */
  0,

  /* Computed Parameter: Constant7_Value_h
   * Referenced by: '<S42>/Constant7'
   */
  1,

  /* Computed Parameter: Constant6_Value_d
   * Referenced by: '<S42>/Constant6'
   */
  0,

  /* Computed Parameter: Constant7_Value_j
   * Referenced by: '<S43>/Constant7'
   */
  1,

  /* Computed Parameter: Constant6_Value_b
   * Referenced by: '<S43>/Constant6'
   */
  0,

  /* Computed Parameter: Constant7_Value_g
   * Referenced by: '<S44>/Constant7'
   */
  1,

  /* Computed Parameter: Constant6_Value_ew
   * Referenced by: '<S44>/Constant6'
   */
  0,

  /* Computed Parameter: Constant7_Value_c
   * Referenced by: '<S45>/Constant7'
   */
  1,

  /* Computed Parameter: Constant6_Value_j
   * Referenced by: '<S45>/Constant6'
   */
  0,

  /* Computed Parameter: Constant7_Value_gs
   * Referenced by: '<S64>/Constant7'
   */
  1,

  /* Computed Parameter: Constant6_Value_n
   * Referenced by: '<S64>/Constant6'
   */
  0,

  /* Computed Parameter: FallbackSystemStatus_VehMid3SsmCounter0Timeout_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_VehMid3SsmCounter0Timeout'
   */
  0,

  /* Computed Parameter: FallbackSystemStatus_VehMid3SsmCounter1Timeout_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_VehMid3SsmCounter1Timeout'
   */
  0,

  /* Computed Parameter: FallbackSystemStatus_VehMid3VcuCounter0Timeout_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_VehMid3VcuCounter0Timeout'
   */
  0,

  /* Computed Parameter: FallbackSystemStatus_VehMid3VcuCounter1Timeout_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_VehMid3VcuCounter1Timeout'
   */
  0,

  /* Computed Parameter: FallbackSystemStatus_VehMid5SsmCounter0Timeout_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_VehMid5SsmCounter0Timeout'
   */
  0,

  /* Computed Parameter: FallbackSystemStatus_VehMid5SsmCounter1Timeout_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_VehMid5SsmCounter1Timeout'
   */
  0,

  /* Computed Parameter: FallbackSystemStatus_VehMid6SsmCounter0Timeout_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_VehMid6SsmCounter0Timeout'
   */
  0,

  /* Computed Parameter: FallbackSystemStatus_VehMid6SsmCounter1Timeout_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_VehMid6SsmCounter1Timeout'
   */
  0,

  /* Computed Parameter: FallbackSystemStatus_FrontCameraCanTimeout_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_FrontCameraCanTimeout'
   */
  0,

  /* Computed Parameter: FallbackSystemStatus_FrontRadarCanTimeout_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_FrontRadarCanTimeout'
   */
  0,

  /* Computed Parameter: UnitDelay_InitialCondition_ev
   * Referenced by: '<S6>/Unit Delay'
   */
  0,

  /* Computed Parameter: UnitDelay_InitialCondition_b
   * Referenced by: '<S24>/Unit Delay'
   */
  0,

  /* Computed Parameter: UnitDelay1_InitialCondition_f
   * Referenced by: '<S36>/Unit Delay1'
   */
  0,

  /* Computed Parameter: UnitDelay1_InitialCondition_p
   * Referenced by: '<S35>/Unit Delay1'
   */
  0,

  /* Computed Parameter: UnitDelay1_InitialCondition_fc
   * Referenced by: '<S37>/Unit Delay1'
   */
  0,

  /* Computed Parameter: UnitDelay_InitialCondition_j
   * Referenced by: '<S44>/Unit Delay'
   */
  0,

  /* Computed Parameter: UnitDelay_InitialCondition_a
   * Referenced by: '<S45>/Unit Delay'
   */
  0,

  /* Computed Parameter: UnitDelay_InitialCondition_c
   * Referenced by: '<S10>/Unit Delay'
   */
  0,

  /* Computed Parameter: UnitDelay1_InitialCondition_b
   * Referenced by: '<S33>/Unit Delay1'
   */
  0,

  /* Computed Parameter: UnitDelay1_InitialCondition_k
   * Referenced by: '<S34>/Unit Delay1'
   */
  0,

  /* Computed Parameter: UnitDelay_InitialCondition_cb
   * Referenced by: '<S42>/Unit Delay'
   */
  0,

  /* Computed Parameter: UnitDelay_InitialCondition_pm
   * Referenced by: '<S43>/Unit Delay'
   */
  0,

  /* Computed Parameter: Constant5_Value_k
   * Referenced by: '<S7>/Constant5'
   */
  0,

  /* Computed Parameter: Constant23_Value_l
   * Referenced by: '<S56>/Constant23'
   */
  0,

  /* Computed Parameter: Constant24_Value_b
   * Referenced by: '<S56>/Constant24'
   */
  0,

  /* Computed Parameter: Constant25_Value
   * Referenced by: '<S56>/Constant25'
   */
  0,

  /* Computed Parameter: Constant26_Value
   * Referenced by: '<S56>/Constant26'
   */
  0,

  /* Computed Parameter: Constant27_Value
   * Referenced by: '<S56>/Constant27'
   */
  0,

  /* Computed Parameter: Constant28_Value
   * Referenced by: '<S56>/Constant28'
   */
  0,

  /* Computed Parameter: UnitDelay_InitialCondition_h
   * Referenced by: '<S64>/Unit Delay'
   */
  0,

  /* Computed Parameter: Constant15_Value_a
   * Referenced by: '<S8>/Constant15'
   */
  0U,

  /* Computed Parameter: Constant1_Value_c
   * Referenced by: '<S17>/Constant1'
   */
  6U,

  /* Computed Parameter: Constant2_Value_j
   * Referenced by: '<S17>/Constant2'
   */
  7U,

  /* Computed Parameter: Constant14_Value_m
   * Referenced by: '<S18>/Constant14'
   */
  1U,

  /* Computed Parameter: Constant_Value_b
   * Referenced by: '<S10>/Constant'
   */
  2U,

  /* Computed Parameter: Constant6_Value_ng
   * Referenced by: '<S10>/Constant6'
   */
  1U,

  /* Computed Parameter: Constant17_Value_o
   * Referenced by: '<S10>/Constant17'
   */
  2U,

  /* Computed Parameter: Constant19_Value_k
   * Referenced by: '<S10>/Constant19'
   */
  0U,

  /* Computed Parameter: Constant7_Value_k
   * Referenced by: '<S10>/Constant7'
   */
  1U,

  /* Computed Parameter: Constant8_Value_e
   * Referenced by: '<S10>/Constant8'
   */
  2U,

  /* Computed Parameter: Constant1_Value_a
   * Referenced by: '<S10>/Constant1'
   */
  1U,

  /* Computed Parameter: Constant9_Value_gl
   * Referenced by: '<S10>/Constant9'
   */
  1U,

  /* Computed Parameter: Constant10_Value_m
   * Referenced by: '<S10>/Constant10'
   */
  0U,

  /* Computed Parameter: Constant11_Value_g
   * Referenced by: '<S10>/Constant11'
   */
  1U,

  /* Computed Parameter: Constant12_Value_b
   * Referenced by: '<S10>/Constant12'
   */
  0U,

  /* Computed Parameter: Constant15_Value_n
   * Referenced by: '<S10>/Constant15'
   */
  4U,

  /* Computed Parameter: Constant4_Value_i
   * Referenced by: '<S10>/Constant4'
   */
  3U,

  /* Computed Parameter: Constant14_Value_c
   * Referenced by: '<S10>/Constant14'
   */
  2U,

  /* Computed Parameter: Constant_Value_a3
   * Referenced by: '<S52>/Constant'
   */
  0U,

  /* Computed Parameter: Constant_Value_p
   * Referenced by: '<S55>/Constant'
   */
  0U,

  /* Computed Parameter: Constant19_Value_n
   * Referenced by: '<S47>/Constant19'
   */
  1U,

  /* Computed Parameter: Constant20_Value_i
   * Referenced by: '<S47>/Constant20'
   */
  4U,

  /* Computed Parameter: Constant17_Value_l
   * Referenced by: '<S47>/Constant17'
   */
  1U,

  /* Computed Parameter: Constant18_Value_cm
   * Referenced by: '<S47>/Constant18'
   */
  4U,

  /* Computed Parameter: Out1_Y0_j
   * Referenced by: '<S59>/Out1'
   */
  0U,

  /* Computed Parameter: Constant_Value_m
   * Referenced by: '<S59>/Constant'
   */
  1U,

  /* Computed Parameter: UnitDelay_InitialCondition_m
   * Referenced by: '<S59>/Unit Delay'
   */
  0U,

  /* Computed Parameter: Constant_Value_lw
   * Referenced by: '<S63>/Constant'
   */
  0U,

  /* Computed Parameter: Constant15_Value_i
   * Referenced by: '<S57>/Constant15'
   */
  1U,

  /* Computed Parameter: Constant1_Value_i
   * Referenced by: '<S57>/Constant1'
   */
  7U,

  /* Computed Parameter: Constant2_Value_c
   * Referenced by: '<S57>/Constant2'
   */
  6U,

  /* Computed Parameter: Constant3_Value_h
   * Referenced by: '<S57>/Constant3'
   */
  5U,

  /* Computed Parameter: Constant5_Value_o
   * Referenced by: '<S57>/Constant5'
   */
  4U,

  /* Computed Parameter: Constant22_Value
   * Referenced by: '<S57>/Constant22'
   */
  3U,

  /* Computed Parameter: Constant4_Value_n
   * Referenced by: '<S57>/Constant4'
   */
  2U,

  /* Computed Parameter: Constant11_Value_e
   * Referenced by: '<S57>/Constant11'
   */
  8U,

  /* Computed Parameter: Constant8_Value_jv
   * Referenced by: '<S57>/Constant8'
   */
  7U,

  /* Computed Parameter: Constant7_Value_ov
   * Referenced by: '<S57>/Constant7'
   */
  6U,

  /* Computed Parameter: Constant6_Value_i
   * Referenced by: '<S57>/Constant6'
   */
  5U,

  /* Computed Parameter: Constant9_Value_b
   * Referenced by: '<S57>/Constant9'
   */
  0U,

  /* Computed Parameter: Constant14_Value_g
   * Referenced by: '<S57>/Constant14'
   */
  0U,

  /* Computed Parameter: Constant_Value_c
   * Referenced by: '<S81>/Constant'
   */
  0U,

  /* Computed Parameter: VTS_ChecksumEscDa_Y0
   * Referenced by: '<S78>/VTS_ChecksumEscDa'
   */
  0U,

  /* Computed Parameter: VTS_AliveCounterEscDa_Y0
   * Referenced by: '<S78>/VTS_AliveCounterEscDa'
   */
  0U,

  /* Computed Parameter: Constant4_Value_bh
   * Referenced by: '<S78>/Constant4'
   */
  1U,

  /* Computed Parameter: Constant5_Value_a
   * Referenced by: '<S78>/Constant5'
   */
  1U,

  /* Computed Parameter: Constant6_Value_dc
   * Referenced by: '<S78>/Constant6'
   */
  1U,

  /* Computed Parameter: Constant7_Value_hb
   * Referenced by: '<S78>/Constant7'
   */
  1U,

  /* Computed Parameter: Constant8_Value_f
   * Referenced by: '<S78>/Constant8'
   */
  1U,

  /* Computed Parameter: Constant9_Value_o
   * Referenced by: '<S78>/Constant9'
   */
  1U,

  /* Computed Parameter: Constant3_Value_dg
   * Referenced by: '<S78>/Constant3'
   */
  1U,

  /* Computed Parameter: Constant2_Value_b
   * Referenced by: '<S78>/Constant2'
   */
  1U,

  /* Computed Parameter: Constant1_Value_ib
   * Referenced by: '<S78>/Constant1'
   */
  1U,

  /* Computed Parameter: Output_InitialCondition
   * Referenced by: '<S79>/Output'
   */
  0U,

  /* Computed Parameter: Constant11_Value_i
   * Referenced by: '<S78>/Constant11'
   */
  0U,

  /* Computed Parameter: Constant10_Value_o
   * Referenced by: '<S78>/Constant10'
   */
  1U,

  /* Computed Parameter: Constant12_Value_e
   * Referenced by: '<S78>/Constant12'
   */
  1U,

  /* Computed Parameter: Constant13_Value_l
   * Referenced by: '<S78>/Constant13'
   */
  1U,

  /* Computed Parameter: Constant14_Value_eo
   * Referenced by: '<S78>/Constant14'
   */
  1U,

  /* Computed Parameter: Constant15_Value_i1
   * Referenced by: '<S78>/Constant15'
   */
  0U,

  /* Computed Parameter: FixPtConstant_Value
   * Referenced by: '<S80>/FixPt Constant'
   */
  1U,

  /* Computed Parameter: Constant7_Value_jf
   * Referenced by: '<S82>/Constant7'
   */
  0U,

  /* Computed Parameter: Constant8_Value_c
   * Referenced by: '<S82>/Constant8'
   */
  1U,

  /* Computed Parameter: Constant2_Value_e
   * Referenced by: '<S82>/Constant2'
   */
  1U,

  /* Computed Parameter: Constant5_Value_mv
   * Referenced by: '<S82>/Constant5'
   */
  2U,

  /* Computed Parameter: Constant4_Value_h
   * Referenced by: '<S82>/Constant4'
   */
  0U,

  /* Computed Parameter: Constant7_Value_e
   * Referenced by: '<S83>/Constant7'
   */
  0U,

  /* Computed Parameter: Constant8_Value_o
   * Referenced by: '<S83>/Constant8'
   */
  1U,

  /* Computed Parameter: Constant2_Value_h
   * Referenced by: '<S83>/Constant2'
   */
  1U,

  /* Computed Parameter: Constant5_Value_g
   * Referenced by: '<S83>/Constant5'
   */
  2U,

  /* Computed Parameter: Constant4_Value_l
   * Referenced by: '<S83>/Constant4'
   */
  0U,

  /* Computed Parameter: Constant_Value_gc
   * Referenced by: '<S87>/Constant'
   */
  0U,

  /* Computed Parameter: Output_InitialCondition_h
   * Referenced by: '<S85>/Output'
   */
  0U,

  /* Computed Parameter: Constant2_Value_fr
   * Referenced by: '<S84>/Constant2'
   */
  0U,

  /* Computed Parameter: Constant1_Value_jp
   * Referenced by: '<S84>/Constant1'
   */
  0U,

  /* Computed Parameter: Constant3_Value_l
   * Referenced by: '<S84>/Constant3'
   */
  1U,

  /* Computed Parameter: FixPtConstant_Value_b
   * Referenced by: '<S86>/FixPt Constant'
   */
  1U,

  /* Computed Parameter: VTS_ChecksumFrntWhl_Y0
   * Referenced by: '<S71>/VTS_ChecksumFrntWhl'
   */
  0U,

  /* Computed Parameter: VTS_AliveCounterFrntWhl_Y0
   * Referenced by: '<S71>/VTS_AliveCounterFrntWhl'
   */
  0U,

  /* Computed Parameter: VTS_WheelDirectionFrntRi_Y0
   * Referenced by: '<S71>/VTS_WheelDirectionFrntRi'
   */
  0U,

  /* Computed Parameter: VTS_SpeedInvalidFrntRi_Y0
   * Referenced by: '<S71>/VTS_SpeedInvalidFrntRi'
   */
  0U,

  /* Computed Parameter: VTS_WheelDirectionFrntLe_Y0
   * Referenced by: '<S71>/VTS_WheelDirectionFrntLe'
   */
  0U,

  /* Computed Parameter: VTS_SpeedInvalidFrntLe_Y0
   * Referenced by: '<S71>/VTS_SpeedInvalidFrntLe'
   */
  0U,

  /* Computed Parameter: Constant6_Value_b2
   * Referenced by: '<S82>/Constant6'
   */
  3U,

  /* Computed Parameter: Constant6_Value_ek
   * Referenced by: '<S83>/Constant6'
   */
  3U,

  /* Computed Parameter: Constant_Value_fn
   * Referenced by: '<S93>/Constant'
   */
  0U,

  /* Computed Parameter: Output_InitialCondition_f
   * Referenced by: '<S91>/Output'
   */
  0U,

  /* Computed Parameter: Constant7_Value_hs
   * Referenced by: '<S88>/Constant7'
   */
  0U,

  /* Computed Parameter: FixPtConstant_Value_h
   * Referenced by: '<S92>/FixPt Constant'
   */
  1U,

  /* Computed Parameter: Constant7_Value_dl
   * Referenced by: '<S89>/Constant7'
   */
  0U,

  /* Computed Parameter: Constant8_Value_i
   * Referenced by: '<S89>/Constant8'
   */
  1U,

  /* Computed Parameter: Constant2_Value_p
   * Referenced by: '<S89>/Constant2'
   */
  1U,

  /* Computed Parameter: Constant5_Value_oq
   * Referenced by: '<S89>/Constant5'
   */
  2U,

  /* Computed Parameter: Constant4_Value_bc
   * Referenced by: '<S89>/Constant4'
   */
  0U,

  /* Computed Parameter: Constant7_Value_gq
   * Referenced by: '<S90>/Constant7'
   */
  0U,

  /* Computed Parameter: Constant8_Value_ez
   * Referenced by: '<S90>/Constant8'
   */
  1U,

  /* Computed Parameter: Constant2_Value_m
   * Referenced by: '<S90>/Constant2'
   */
  1U,

  /* Computed Parameter: Constant5_Value_p
   * Referenced by: '<S90>/Constant5'
   */
  2U,

  /* Computed Parameter: Constant4_Value_li
   * Referenced by: '<S90>/Constant4'
   */
  0U,

  /* Computed Parameter: VTS_ChecksumReWhl_Y0
   * Referenced by: '<S72>/VTS_ChecksumReWhl'
   */
  0U,

  /* Computed Parameter: VTS_AliveCounterReWhl_Y0
   * Referenced by: '<S72>/VTS_AliveCounterReWhl'
   */
  0U,

  /* Computed Parameter: VTS_WheelDirectionReRi_Y0
   * Referenced by: '<S72>/VTS_WheelDirectionReRi'
   */
  0U,

  /* Computed Parameter: VTS_SpeedInvalidReRi_Y0
   * Referenced by: '<S72>/VTS_SpeedInvalidReRi'
   */
  0U,

  /* Computed Parameter: VTS_WheelDirectionReLe_Y0
   * Referenced by: '<S72>/VTS_WheelDirectionReLe'
   */
  0U,

  /* Computed Parameter: VTS_SpeedInvalidReLe_Y0
   * Referenced by: '<S72>/VTS_SpeedInvalidReLe'
   */
  0U,

  /* Computed Parameter: Constant6_Value_p
   * Referenced by: '<S89>/Constant6'
   */
  3U,

  /* Computed Parameter: Constant6_Value_h5
   * Referenced by: '<S90>/Constant6'
   */
  3U,

  /* Computed Parameter: Constant_Value_o
   * Referenced by: '<S98>/Constant'
   */
  0U,

  /* Computed Parameter: Constant2_Value_k
   * Referenced by: '<S94>/Constant2'
   */
  0U,

  /* Computed Parameter: Constant4_Value_a
   * Referenced by: '<S94>/Constant4'
   */
  0U,

  /* Computed Parameter: Constant5_Value_c2
   * Referenced by: '<S94>/Constant5'
   */
  1U,

  /* Computed Parameter: Constant1_Value_ax
   * Referenced by: '<S94>/Constant1'
   */
  0U,

  /* Computed Parameter: Constant3_Value_ct
   * Referenced by: '<S94>/Constant3'
   */
  0U,

  /* Computed Parameter: Constant6_Value_bo
   * Referenced by: '<S94>/Constant6'
   */
  0U,

  /* Computed Parameter: Constant7_Value_jy
   * Referenced by: '<S94>/Constant7'
   */
  0U,

  /* Computed Parameter: Constant8_Value_ek
   * Referenced by: '<S94>/Constant8'
   */
  0U,

  /* Computed Parameter: Constant10_Value_n
   * Referenced by: '<S94>/Constant10'
   */
  0U,

  /* Computed Parameter: Output_InitialCondition_d
   * Referenced by: '<S96>/Output'
   */
  0U,

  /* Computed Parameter: Constant12_Value_k
   * Referenced by: '<S94>/Constant12'
   */
  0U,

  /* Computed Parameter: Constant11_Value_o
   * Referenced by: '<S94>/Constant11'
   */
  0U,

  /* Computed Parameter: Constant9_Value_e
   * Referenced by: '<S94>/Constant9'
   */
  0U,

  /* Computed Parameter: FixPtConstant_Value_o
   * Referenced by: '<S97>/FixPt Constant'
   */
  1U,

  /* Computed Parameter: VTS_EscStatusChecksum_Y0
   * Referenced by: '<S73>/VTS_EscStatusChecksum'
   */
  0U,

  /* Computed Parameter: VTS_EscStatusAliveCounter_Y0
   * Referenced by: '<S73>/VTS_EscStatusAliveCounter'
   */
  0U,

  /* Computed Parameter: Constant_Value_b2
   * Referenced by: '<S99>/Constant'
   */
  1U,

  /* Computed Parameter: Constant3_Value_o
   * Referenced by: '<S99>/Constant3'
   */
  0U,

  /* Computed Parameter: Constant4_Value_ax
   * Referenced by: '<S99>/Constant4'
   */
  1U,

  /* Computed Parameter: Constant_Value_h
   * Referenced by: '<S104>/Constant'
   */
  0U,

  /* Computed Parameter: Output_InitialCondition_m
   * Referenced by: '<S102>/Output'
   */
  0U,

  /* Computed Parameter: Constant5_Value_af
   * Referenced by: '<S100>/Constant5'
   */
  0U,

  /* Computed Parameter: FixPtConstant_Value_l
   * Referenced by: '<S103>/FixPt Constant'
   */
  1U,

  /* Computed Parameter: VTS_SASChecksum_Y0
   * Referenced by: '<S74>/VTS_SASChecksum'
   */
  0U,

  /* Computed Parameter: VTS_SASAliveCounter_Y0
   * Referenced by: '<S74>/VTS_SASAliveCounter'
   */
  0U,

  /* Computed Parameter: VTS_SASFailureSts_Y0
   * Referenced by: '<S74>/VTS_SASFailureSts'
   */
  0U,

  /* Computed Parameter: VTS_SASCalibrationSts_Y0
   * Referenced by: '<S74>/VTS_SASCalibrationSts'
   */
  0U,

  /* Computed Parameter: Constant4_Value_f
   * Referenced by: '<S106>/Constant4'
   */
  0U,

  /* Computed Parameter: Constant5_Value_on
   * Referenced by: '<S106>/Constant5'
   */
  1U,

  /* Computed Parameter: Constant_Value_oj
   * Referenced by: '<S106>/Constant'
   */
  0U,

  /* Computed Parameter: Constant2_Value_fr0
   * Referenced by: '<S106>/Constant2'
   */
  1U,

  /* Computed Parameter: Constant_Value_i
   * Referenced by: '<S112>/Constant'
   */
  0U,

  /* Computed Parameter: Output_InitialCondition_o
   * Referenced by: '<S110>/Output'
   */
  0U,

  /* Computed Parameter: Constant4_Value_o
   * Referenced by: '<S107>/Constant4'
   */
  0U,

  /* Computed Parameter: FixPtConstant_Value_ht
   * Referenced by: '<S111>/FixPt Constant'
   */
  1U,

  /* Computed Parameter: VTS_YRS1AliveCounter_Y0
   * Referenced by: '<S76>/VTS_YRS1AliveCounter'
   */
  0U,

  /* Computed Parameter: VTS_LateralSensorState_Y0
   * Referenced by: '<S76>/VTS_LateralSensorState'
   */
  0U,

  /* Computed Parameter: VTS_YawRateSensorState_Y0
   * Referenced by: '<S76>/VTS_YawRateSensorState'
   */
  0U,

  /* Computed Parameter: VTS_YRS1Checksum_Y0
   * Referenced by: '<S76>/VTS_YRS1Checksum'
   */
  0U,

  /* Computed Parameter: Constant_Value_o5
   * Referenced by: '<S113>/Constant'
   */
  0U,

  /* Computed Parameter: Constant2_Value_l
   * Referenced by: '<S113>/Constant2'
   */
  1U,

  /* Computed Parameter: Constant_Value_ay
   * Referenced by: '<S118>/Constant'
   */
  0U,

  /* Computed Parameter: Constant6_Value_hd
   * Referenced by: '<S114>/Constant6'
   */
  0U,

  /* Computed Parameter: Output_InitialCondition_m4
   * Referenced by: '<S116>/Output'
   */
  0U,

  /* Computed Parameter: FixPtConstant_Value_i
   * Referenced by: '<S117>/FixPt Constant'
   */
  1U,

  /* Computed Parameter: VTS_YRS2AliveCounter_Y0
   * Referenced by: '<S77>/VTS_YRS2AliveCounter'
   */
  0U,

  /* Computed Parameter: VTS_LongitSensorState_Y0
   * Referenced by: '<S77>/VTS_LongitSensorState'
   */
  0U,

  /* Computed Parameter: VTS_YRS2Checksum_Y0
   * Referenced by: '<S77>/VTS_YRS2Checksum'
   */
  0U,

  /* Computed Parameter: ESC_DA_MESSAGE_ESC_DA_MESSAGE_Checksum_Y0
   * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_DA_MESSAGE_Checksum'
   */
  0U,

  /* Computed Parameter: ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_Checksum_Y0
   * Referenced by: '<S2>/ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_Checksum'
   */
  0U,

  /* Computed Parameter: ESC_FrontWheelSpeedKPH_ESC_Mcylinder_Pressure_Y0
   * Referenced by: '<S2>/ESC_FrontWheelSpeedKPH_ESC_Mcylinder_Pressure'
   */
  0U,

  /* Computed Parameter: ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_Checksum_Y0
   * Referenced by: '<S2>/ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_Checksum'
   */
  0U,

  /* Computed Parameter: ESC_Status_ESC_Status_Checksum_Y0
   * Referenced by: '<S2>/ESC_Status_ESC_Status_Checksum'
   */
  0U,

  /* Computed Parameter: SAS_Status_SAS_Status_Checksum_Y0
   * Referenced by: '<S2>/SAS_Status_SAS_Status_Checksum'
   */
  0U,

  /* Computed Parameter: YRS1_YRS1_Checksum_Y0
   * Referenced by: '<S2>/YRS1_YRS1_Checksum'
   */
  0U,

  /* Computed Parameter: YRS2_YRS2_Checksum_Y0
   * Referenced by: '<S2>/YRS2_YRS2_Checksum'
   */
  0U,

  /* Computed Parameter: VIMMid3CanFr04_IDcDcAvlMaxLoSideExt_Y0
   * Referenced by: '<S2>/VIMMid3CanFr04_IDcDcAvlMaxLoSideExt'
   */
  0U,

  /* Computed Parameter: VIMMid3CanFr04_IDcDcAvlLoSideExt_Y0
   * Referenced by: '<S2>/VIMMid3CanFr04_IDcDcAvlLoSideExt'
   */
  0U,

  /* Computed Parameter: FbAcuAvailable_VehControlStatus_Y0
   * Referenced by: '<S2>/FbAcuAvailable_VehControlStatus'
   */
  0U,

  /* Computed Parameter: FbAcuAvailable_Sensor1v1rStatus_Y0
   * Referenced by: '<S2>/FbAcuAvailable_Sensor1v1rStatus'
   */
  0U,

  /* Computed Parameter: FbAcuAvailable_McuStatus_Y0
   * Referenced by: '<S2>/FbAcuAvailable_McuStatus'
   */
  0U,

  /* Computed Parameter: FbAcuAvailable_FbAcuReserved_Y0
   * Referenced by: '<S2>/FbAcuAvailable_FbAcuReserved'
   */
  0U,

  /* Computed Parameter: FbAcuAvailable_FbAcuRollingCounter_Y0
   * Referenced by: '<S2>/FbAcuAvailable_FbAcuRollingCounter'
   */
  0U,

  /* Computed Parameter: FallbackSystemStatus_SystemStatusRollingCounter_Y0
   * Referenced by: '<S2>/FallbackSystemStatus_SystemStatusRollingCounter'
   */
  0U,

  /* Computed Parameter: FallbackDebugInfo_LateralSystemState_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_LateralSystemState'
   */
  0U,

  /* Computed Parameter: FallbackDebugInfo_FallbackDebugInfoRollingCounter_Y0
   * Referenced by: '<S2>/FallbackDebugInfo_FallbackDebugInfoRollingCounter'
   */
  0U,

  /* Computed Parameter: Constant11_Value_j
   * Referenced by: '<S8>/Constant11'
   */
  0U,

  /* Computed Parameter: Constant13_Value_a
   * Referenced by: '<S17>/Constant13'
   */
  5U,

  /* Computed Parameter: Constant5_Value_cx
   * Referenced by: '<S17>/Constant5'
   */
  3U,

  /* Computed Parameter: Constant9_Value_bf
   * Referenced by: '<S17>/Constant9'
   */
  8U,

  /* Computed Parameter: Constant15_Value_h
   * Referenced by: '<S18>/Constant15'
   */
  0U,

  /* Computed Parameter: Constant16_Value_m
   * Referenced by: '<S18>/Constant16'
   */
  1U,

  /* Computed Parameter: Constant8_Value_n
   * Referenced by: '<S18>/Constant8'
   */
  1U,

  /* Computed Parameter: Constant2_Value_be
   * Referenced by: '<S10>/Constant2'
   */
  2U,

  /* Computed Parameter: Constant3_Value_j
   * Referenced by: '<S10>/Constant3'
   */
  2U,

  /* Computed Parameter: UnitDelay1_InitialCondition_p4
   * Referenced by: '<S10>/Unit Delay1'
   */
  3U,

  /* Computed Parameter: Constant20_Value_d
   * Referenced by: '<S10>/Constant20'
   */
  3U,

  /* Computed Parameter: Constant21_Value
   * Referenced by: '<S10>/Constant21'
   */
  0U,

  /* Computed Parameter: Constant13_Value_h
   * Referenced by: '<S10>/Constant13'
   */
  0U,

  /* Computed Parameter: Constant2_Value_kc
   * Referenced by: '<S7>/Constant2'
   */
  0U,

  /* Computed Parameter: Constant1_Value_c0
   * Referenced by: '<S7>/Constant1'
   */
  0U,

  /* Computed Parameter: Constant_Value_ip
   * Referenced by: '<S7>/Constant'
   */
  0U,

  /* Computed Parameter: Constant3_Value_pc
   * Referenced by: '<S7>/Constant3'
   */
  0U,

  /* Computed Parameter: Constant6_Value_py
   * Referenced by: '<S7>/Constant6'
   */
  0U,

  /* Computed Parameter: Constant7_Value_di
   * Referenced by: '<S7>/Constant7'
   */
  0U,

  /* Computed Parameter: FixPtConstant_Value_a
   * Referenced by: '<S51>/FixPt Constant'
   */
  1U,

  /* Computed Parameter: Output_InitialCondition_l
   * Referenced by: '<S50>/Output'
   */
  0U,

  /* Computed Parameter: UnitDelay_InitialCondition_je
   * Referenced by: '<S49>/Unit Delay'
   */
  0U,

  /* Computed Parameter: Constant8_Value_j0
   * Referenced by: '<S49>/Constant8'
   */
  0U,

  /* Computed Parameter: Constant21_Value_o
   * Referenced by: '<S49>/Constant21'
   */
  1U,

  /* Computed Parameter: FixPtConstant_Value_e
   * Referenced by: '<S54>/FixPt Constant'
   */
  1U,

  /* Computed Parameter: Output_InitialCondition_a
   * Referenced by: '<S53>/Output'
   */
  0U,

  /* Computed Parameter: Output_InitialCondition_he
   * Referenced by: '<S60>/Output'
   */
  0U,

  /* Computed Parameter: FixPtConstant_Value_lu
   * Referenced by: '<S62>/FixPt Constant'
   */
  1U,

  /* Computed Parameter: Constant_Value_p4
   * Referenced by: '<S69>/Constant'
   */
  1U,

  /* Computed Parameter: Constant1_Value_h
   * Referenced by: '<S69>/Constant1'
   */
  1U,

  /* Computed Parameter: Constant2_Value_eg
   * Referenced by: '<S69>/Constant2'
   */
  1U,

  /* Computed Parameter: Constant3_Value_m
   * Referenced by: '<S69>/Constant3'
   */
  0U,

  /* Computed Parameter: Constant4_Value_bs
   * Referenced by: '<S69>/Constant4'
   */
  1U,

  /* Computed Parameter: Constant5_Value_e
   * Referenced by: '<S69>/Constant5'
   */
  1U,

  /* Computed Parameter: Constant6_Value_k
   * Referenced by: '<S69>/Constant6'
   */
  1U,

  /* Computed Parameter: Constant7_Value_cs
   * Referenced by: '<S69>/Constant7'
   */
  1U,

  /* Computed Parameter: Constant12_Value_a
   * Referenced by: '<S69>/Constant12'
   */
  1U,

  /* Computed Parameter: Constant13_Value_c
   * Referenced by: '<S69>/Constant13'
   */
  1U,

  /* Computed Parameter: Constant8_Value_fn
   * Referenced by: '<S69>/Constant8'
   */
  1U,

  /* Computed Parameter: Constant9_Value_i
   * Referenced by: '<S69>/Constant9'
   */
  1U,

  /* Computed Parameter: Constant10_Value_a
   * Referenced by: '<S69>/Constant10'
   */
  1U,

  /* Computed Parameter: Constant11_Value_m
   * Referenced by: '<S69>/Constant11'
   */
  1U,

  /* Computed Parameter: Constant14_Value_lf
   * Referenced by: '<S69>/Constant14'
   */
  1U,

  /* Computed Parameter: Constant15_Value_ii
   * Referenced by: '<S69>/Constant15'
   */
  0U,

  /* Computed Parameter: Constant16_Value_c
   * Referenced by: '<S69>/Constant16'
   */
  0U,

  /* Computed Parameter: Constant17_Value_n
   * Referenced by: '<S69>/Constant17'
   */
  1U,

  /* Computed Parameter: Constant18_Value_n
   * Referenced by: '<S69>/Constant18'
   */
  0U,

  /* Computed Parameter: Constant19_Value_nz
   * Referenced by: '<S69>/Constant19'
   */
  0U,

  /* Computed Parameter: Constant20_Value_o
   * Referenced by: '<S69>/Constant20'
   */
  0U,

  /* Computed Parameter: Constant21_Value_a
   * Referenced by: '<S69>/Constant21'
   */
  0U,

  /* Computed Parameter: Constant22_Value_l
   * Referenced by: '<S69>/Constant22'
   */
  0U,

  /* Computed Parameter: Constant23_Value_c
   * Referenced by: '<S69>/Constant23'
   */
  0U,

  /* Computed Parameter: Constant24_Value_g
   * Referenced by: '<S69>/Constant24'
   */
  0U,

  /* Computed Parameter: Constant25_Value_j
   * Referenced by: '<S69>/Constant25'
   */
  0U
};

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
