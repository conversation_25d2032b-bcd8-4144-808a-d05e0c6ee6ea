/*
 * File: CameraSigInput_types.h
 *
 * Code generated for Simulink model 'CameraSigInput'.
 *
 * Model version                  : 1.22
 * Simulink Coder version         : 9.5 (R2021a) 14-Nov-2020
 * C/C++ source code generated on : Thu Dec 12 16:01:01 2024
 *
 * Target selection: autosar.tlc
 * Embedded hardware selection: Infineon->TriCore
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_CameraSigInput_types_h_
#define RTW_HEADER_CameraSigInput_types_h_
#include "common_math_library/rtwtypes.h"
#include "_out/Appl/GenData/Rte_Type.h"

/* Model Code Variants */

/* Parameters (default storage) */
typedef struct P_CameraSigInput_T_ P_CameraSigInput_T;

#endif                                 /* RTW_HEADER_CameraSigInput_types_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
