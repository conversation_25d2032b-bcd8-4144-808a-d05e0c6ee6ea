#ifndef OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_TRACKED_OBJECT_LIST_OBSERVER_H_
#define OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_TRACKED_OBJECT_LIST_OBSERVER_H_

#include <functional>
#include <memory>

#include "../data_observer.h"
#include "teleassist_protos/teleassist.pb.h"
#include "voy_protos/pose.pb.h"
#include "voy_protos/tracked_objects.pb.h"

namespace remote_assist {

class RemoteAssistProcessor;

class TrackedObjectListObserver : public IDataObserver {
 public:
  using TrackedObjectListCallback =
      std::function<void(std::shared_ptr<voy::TrackedObjectList>, DataChannel)>;
  using PoseCallback = std::function<void(const voy::Pose&, DataChannel)>;
  using LatencyTraceCallback =
      std::function<void(std::shared_ptr<remote_assist::LatencyTrace>,
                         std::shared_ptr<voy::TrackedObjectList>, DataChannel)>;

  TrackedObjectListObserver(TrackedObjectListCallback to_list_callback,
                            PoseCallback pose_callback,
                            LatencyTraceCallback latency_callback = nullptr);

  bool IsInterestedIn(
      const teleassist::pb::TeleassistRequest& message) override;

  std::shared_ptr<google::protobuf::Message> OnMessage(
      const teleassist::pb::TeleassistRequest& message,
      DataChannel data_channel) override;

 private:
  TrackedObjectListCallback to_list_callback_;
  PoseCallback pose_callback_;
  LatencyTraceCallback latency_callback_;
};

}  // namespace remote_assist

#endif  // OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_TRACKED_OBJECT_LIST_OBSERVER_H_
