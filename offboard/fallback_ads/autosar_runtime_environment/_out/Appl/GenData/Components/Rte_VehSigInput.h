/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *             File:  Rte_VehSigInput.h
 *           Config:  DiDi_FBU.dpa
 *      ECU-Project:  DiDi_FBU
 *
 *        Generator:  MICROSAR RTE Generator Version 4.27.0
 *                    RTE Core Version 1.27.0
 *          License:  CBD2100894
 *
 *      Description:  Application header file for SW-C <VehSigInput>
 *********************************************************************************************************************/

/* double include prevention */
#ifndef RTE_VEHSIGINPUT_H
# define RTE_VEHSIGINPUT_H

# ifndef RTE_CORE
#  ifdef RTE_APPLICATION_HEADER_FILE
//#   error Multiple application header files included.
#  endif
#  define RTE_APPLICATION_HEADER_FILE
#  ifndef RTE_PTR2ARRAYBASETYPE_PASSING
#   define RTE_PTR2ARRAYBASETYPE_PASSING
#  endif
# endif

# ifdef __cplusplus
extern "C"
{
# endif /* __cplusplus */

/* include files */

# include "_out/Appl/GenData/Components/Rte_VehSigInput_Type.h"
# include "_out/Appl/GenData/Rte_DataHandleType.h"


# ifndef RTE_CORE
/**********************************************************************************************************************
 * Init Values for unqueued S/R communication (primitive types only)
 *********************************************************************************************************************/

#  define Rte_InitValue_AcuFbAdsStatus_AcuFbReserved (0U)
#  define Rte_InitValue_AcuFbAdsStatus_AcuFbReserved1 (0U)
#  define Rte_InitValue_AcuFbAdsStatus_AcuFbReserved2 (0U)
#  define Rte_InitValue_AcuFbAdsStatus_AcuFbReserved3 (0U)
#  define Rte_InitValue_AcuFbAdsStatus_AcuFbReserved4 (0)
#  define Rte_InitValue_AcuFbAdsStatus_AcuFbRollingCounter (0U)
#  define Rte_InitValue_AcuFbAdsStatus_AdsFaultStatus (0U)
#  define Rte_InitValue_AcuFbAdsStatus_AdsFaultStatusBackup (0U)
#  define Rte_InitValue_AcuFbAdsStatus_ReqResleaseFbControl (0U)
#  define Rte_InitValue_VCU1Mid3CanFr02_HornActvIf (0U)
#  define Rte_InitValue_VCU1Mid3CanFr02_HornSwtStsIf (0U)
#  define Rte_InitValue_VCU1Mid3CanFr02_WshrFldLvlContnsIf (0U)
#  define Rte_InitValue_VCU1Mid3CanFr05_PassSeatSts (0U)
#  define Rte_InitValue_VCU1Mid3CanFr05_WinWipgAutCmdIf (0U)
#  define Rte_InitValue_VCU1Mid3CanFr05_WipgFrntAutModIf (0U)
#  define Rte_InitValue_VCU1Mid3CanFr05_WiprActvIf (FALSE)
#  define Rte_InitValue_VCU1Mid3CanFr05_WshngCycActvIf (FALSE)
#  define Rte_InitValue_VCU1Mid3CanFr09_WhlMotSysCluSts (FALSE)
#  define Rte_InitValue_VCU1Mid3CanFr11_AhbcIndcnToAPI (2U)
#  define Rte_InitValue_VCU1Mid3CanFr11_DoorDrvrMovmtFailNotif (0U)
#  define Rte_InitValue_VCU1Mid3CanFr11_DoorDrvrReMovmtFailNotif (0U)
#  define Rte_InitValue_VCU1Mid3CanFr11_DoorPassMovmtFailNotif (0U)
#  define Rte_InitValue_VCU1Mid3CanFr11_DoorPassReMovmtFailNotif (0U)
#  define Rte_InitValue_VCU1Mid3CanFr12_ExtDcDcActvnAllwd (FALSE)
#  define Rte_InitValue_VCU1Mid3CanFr12_PtTqAtAxleAvlFrntMax (0)
#  define Rte_InitValue_VCU1Mid3CanFr12_PtTqAtAxleAvlReMax (0)
#  define Rte_InitValue_VCU1Mid3CanFr13_AdActvnOkFromVehDyn (FALSE)
#  define Rte_InitValue_VCU1Mid3CanFr13_PtTqAtAxleAvlReMaxLong (0)
#  define Rte_InitValue_VCU1Mid3CanFr14_CllsnFwdWarnReq (FALSE)
#  define Rte_InitValue_VCU1Mid3CanFr14_CllsnThreat (0U)
#  define Rte_InitValue_VCU1Mid3CanFr14_CnclReqForCrsCtrl (0U)
#  define Rte_InitValue_VCU1Mid3CanFr14_CooltFlowInDtElecForExt (0U)
#  define Rte_InitValue_VCU1Mid3CanFr14_CooltTInDtElecForExt (0U)
#  define Rte_InitValue_VCU1Mid3CanFr14_IndcrDisp1WdSts (0U)
#  define Rte_InitValue_VCU1Mid3CanFr14_IndcrTurnSts1WdSts (0U)
#  define Rte_InitValue_VCU1Mid3CanFr14_SrvRqrdForCllsnAid (FALSE)
#  define Rte_InitValue_VCU1Mid3CanFr16_AsySftyDecelEnadByVehDyn (FALSE)
#  define Rte_InitValue_VCU1Mid3CanFr16_EngOilPWarn (FALSE)
#  define Rte_InitValue_VCU1Mid3CanFr16_InhbOfAsySftyDecelByVehDyn (FALSE)
#  define Rte_InitValue_VCU1Mid3CanFr16_PrpsnErrIndcnReq (1U)
#  define Rte_InitValue_VCU1Mid3CanFr16_TankFlapSts (0U)
#  define Rte_InitValue_VCU1Mid3CanFr16_TrsmFltIndcn (0U)
#  define Rte_InitValue_VCU1Mid3CanFr17_DoorDrvrReSts (0U)
#  define Rte_InitValue_VCU1Mid3CanFr17_DoorDrvrSts (0U)
#  define Rte_InitValue_VCU1Mid3CanFr17_DoorPassReSts (0U)
#  define Rte_InitValue_VCU1Mid3CanFr17_DoorPassSts (0U)
#  define Rte_InitValue_VCU1Mid3CanFr17_EngOilLvlSts (0U)
#  define Rte_InitValue_VCU1Mid3CanFr17_HoodSts (0U)
#  define Rte_InitValue_VCU1Mid3CanFr17_TrSts (0U)
#  define Rte_InitValue_VCU1Mid3CanFr18_AsySftyBrkDlyEstimd (0U)
#  define Rte_InitValue_VCU1Mid3CanFr18_HvSysActvStsExt1 (FALSE)
#  define Rte_InitValue_VCU1Mid3CanFr19_AccrPedlRat (0U)
#  define Rte_InitValue_VCU1Mid3CanFr19_ChrgnUReqExt (0U)
#  define Rte_InitValue_VCU1Mid3CanFr19_DrvrDecelReq (0)
#  define Rte_InitValue_VCU1Mid3CanFr19_PtGearAct (0U)
#  define Rte_InitValue_VCU1Mid3CanFr22_PtTqAtAxleAvlFrntMaxLong (0)
#  define Rte_InitValue_VCU1Mid3CanFr25_EgyAvlChrgTot (10U)
#  define Rte_InitValue_VCU1Mid3CanFr25_EgyAvlDchaTot (10U)
#  define Rte_InitValue_VCU1Mid3CanFr29_BattChrgnTiEstimdExt (0U)
#  define Rte_InitValue_VCU1Mid3CanFr29_BattIExt (4100U)
#  define Rte_InitValue_VCU1Mid3CanFr29_BattUExt (1520U)
#  define Rte_InitValue_VCU1Mid3CanFr29_ChrgnTypExt (0U)
#  define Rte_InitValue_VCU1Mid3CanFr29_ChrgrHndlStsExt (0U)
#  define Rte_InitValue_VCU1Mid3CanFr30_VehManDeactvnReqInProgs1 (0U)
#  define Rte_InitValue_VCU1Mid3CanFr30_VehUsgSt (0U)
#  define Rte_InitValue_VCU1Mid3CanFr34_SwtBeamHiToAPI (0U)
#  define Rte_InitValue_VCU1Mid3CanFr34_SwtLiHzrdWarnToAPI (FALSE)
# endif


# ifndef RTE_CORE

/**********************************************************************************************************************
 * Buffers for implicit communication
 *********************************************************************************************************************/
#  define RTE_START_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(Rte_tsMainTask_Core0_10ms, RTE_VAR_NOINIT) Rte_MainTask_Core0_10ms; /* PRQA S 0759 */ /* MD_MSR_Union */

#  define RTE_STOP_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

#  define RTE_START_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(Rte_tsOsTask_Init_Core0_APP, RTE_VAR_NOINIT) Rte_OsTask_Init_Core0_APP; /* PRQA S 0759 */ /* MD_MSR_Union */

#  define RTE_STOP_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


# endif


# ifndef RTE_CORE

/**********************************************************************************************************************
 * Rte_IRead_<r>_<p>_<d>
 * Rte_IStatus_<r>_<p>_<d>
 * Rte_IFeedback_<r>_<p>_<d>
 * Rte_IWrite_<r>_<p>_<d>
 * Rte_IWriteRef_<r>_<p>_<d>
 * Rte_IInvalidate_<r>_<p>_<d>
 *********************************************************************************************************************/


#  define Rte_IRead_VehSigInput_10ms_Runnable_AcuFbAdsStatus_AcuFbReserved() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_AcuFbAdsStatus_AcuFbReserved.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_AcuFbAdsStatus_AcuFbReserved1() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_AcuFbAdsStatus_AcuFbReserved1.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_AcuFbAdsStatus_AcuFbReserved2() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_AcuFbAdsStatus_AcuFbReserved2.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_AcuFbAdsStatus_AcuFbReserved3() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_AcuFbAdsStatus_AcuFbReserved3.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_AcuFbAdsStatus_AcuFbReserved4() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_AcuFbAdsStatus_AcuFbReserved4.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_AcuFbAdsStatus_AcuFbRollingCounter() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_AcuFbAdsStatus_AcuFbRollingCounter.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_AcuFbAdsStatus_AdsFaultStatus() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_AcuFbAdsStatus_AdsFaultStatus.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_AcuFbAdsStatus_AdsFaultStatusBackup() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_AcuFbAdsStatus_AdsFaultStatusBackup.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_AcuFbAdsStatus_ReqResleaseFbControl() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_AcuFbAdsStatus_ReqResleaseFbControl.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_SSMBMid6CanFdFr01_SG_AdSecSteerStsSafeGroup() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_SSMBMid6CanFdFr01_SG_AdSecSteerStsSafeGroup.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_SSMBMid6CanFdFr03_SG_SSMBDegraded() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_SSMBMid6CanFdFr03_SG_SSMBDegraded.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_SSMBMid6CanFdFr03_SG_SecPoseMonSafe() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_SSMBMid6CanFdFr03_SG_SecPoseMonSafe.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_SSMBMid6CanFdFr03_SG_SecSteerMotTq() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_SSMBMid6CanFdFr03_SG_SecSteerMotTq.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_SSMBMid6CanFdFr03_SG_SecWhlLockSts() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_SSMBMid6CanFdFr03_SG_SecWhlLockSts.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_SSMBMid6CanFdFr04_SG_BrkDegradedRdnt() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_SSMBMid6CanFdFr04_SG_BrkDegradedRdnt.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_SSMBMid6CanFdFr11_SG_AdSecPahStsGroupSafe() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_SSMBMid6CanFdFr11_SG_AdSecPahStsGroupSafe.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_SSMBMid6CanFdFr11_SG_AdSecSafeStopActvGroupSafe() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_SSMBMid6CanFdFr11_SG_AdSecSafeStopActvGroupSafe.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_SSMBMid6CanFdFr11_SG_AdSecWhlAgRateLimEstimdSafe() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_SSMBMid6CanFdFr11_SG_AdSecWhlAgRateLimEstimdSafe.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_SSMBMid6CanFdFr11_SG_SecMaxALatEstimdGroup() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_SSMBMid6CanFdFr11_SG_SecMaxALatEstimdGroup.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_SSMBMid6CanFdFr11_SG_WhlAgReqFbRdnt() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_SSMBMid6CanFdFr11_SG_WhlAgReqFbRdnt.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_SSMMid3CanFr01_SG_AdSecSteerModStsSafeGroupByGat() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_SSMMid3CanFr01_SG_AdSecSteerModStsSafeGroupByGat.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_SSMMid3CanFr01_SG_AdSecSteerStsSafeGroupByGatewy() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_SSMMid3CanFr01_SG_AdSecSteerStsSafeGroupByGatewy.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_SSMMid3CanFr07_SG_PrimVehSpdGroupSafe() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_SSMMid3CanFr07_SG_PrimVehSpdGroupSafe.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_SSMMid3CanFr11_SG_PrimALgtDataRawSafe() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_SSMMid3CanFr11_SG_PrimALgtDataRawSafe.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr03_SG_AdPrimWhlAgEstimdGroupSafe() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_SSMMid5CanFdFr03_SG_AdPrimWhlAgEstimdGroupSafe.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr03_SG_PrimALatDataRawSafe() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_SSMMid5CanFdFr03_SG_PrimALatDataRawSafe.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr03_SG_PrimAxleSlipStsAndRelAg() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_SSMMid5CanFdFr03_SG_PrimAxleSlipStsAndRelAg.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr03_SG_PrimVLatSafe() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_SSMMid5CanFdFr03_SG_PrimVLatSafe.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr03_SG_PrimWhlRotDirReSafe1() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_SSMMid5CanFdFr03_SG_PrimWhlRotDirReSafe1.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr03_SG_PrimWhlRotToothCntr() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_SSMMid5CanFdFr03_SG_PrimWhlRotToothCntr.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr03_SG_PrimYawRateSafe() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_SSMMid5CanFdFr03_SG_PrimYawRateSafe.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr04_SG_PrimVehMSafe() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_SSMMid5CanFdFr04_SG_PrimVehMSafe.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr04_SG_PrimWhlAgSpdFrntSafe() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_SSMMid5CanFdFr04_SG_PrimWhlAgSpdFrntSafe.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr04_SG_PrimWhlAgSpdReSafe() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_SSMMid5CanFdFr04_SG_PrimWhlAgSpdReSafe.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr06_SG_AdPrimSafeStopActvGroupSafe() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_SSMMid5CanFdFr06_SG_AdPrimSafeStopActvGroupSafe.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr06_SG_SSMDegraded() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_SSMMid5CanFdFr06_SG_SSMDegraded.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr06_SG_WhlAgReqFb() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_SSMMid5CanFdFr06_SG_WhlAgReqFb.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr01_SG_AdPrimSteerStsSafeGroup() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr01_SG_AdPrimSteerStsSafeGroup.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr01_SG_CarModInCrashStsSafe() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr01_SG_CarModInCrashStsSafe.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr02_HornActvIf() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr02_HornActvIf.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr02_HornSwtStsIf() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr02_HornSwtStsIf.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr02_SG_BrkTqMinReq() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr02_SG_BrkTqMinReq.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr02_WshrFldLvlContnsIf() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr02_WshrFldLvlContnsIf.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr03_SG_AdSecSteerActvnGroupSafe() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr03_SG_AdSecSteerActvnGroupSafe.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr03_SG_PrpsnTqDir() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr03_SG_PrpsnTqDir.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr03_SG_SteerWhlTqGroup() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr03_SG_SteerWhlTqGroup.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr04_SG_FricEstimnFromVehDynGroup() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr04_SG_FricEstimnFromVehDynGroup.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr04_SG_PrpsnTqDirCpby() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr04_SG_PrpsnTqDirCpby.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr04_SG_SwtExtrLiToAPI() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr04_SG_SwtExtrLiToAPI.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr05_PassSeatSts() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr05_PassSeatSts.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr05_SG_AdSecBlindStopMonActvnGroupSaf() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr05_SG_AdSecBlindStopMonActvnGroupSaf.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr05_SG_DrvrGearShiftManReq() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr05_SG_DrvrGearShiftManReq.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr05_SG_SnsrClngErrIf() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr05_SG_SnsrClngErrIf.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr05_SG_VehMGroup() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr05_SG_VehMGroup.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr05_WinWipgAutCmdIf() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr05_WinWipgAutCmdIf.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr05_WipgFrntAutModIf() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr05_WipgFrntAutModIf.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr05_WiprActvIf() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr05_WiprActvIf.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr05_WshngCycActvIf() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr05_WshngCycActvIf.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr08_SG_AgDataRawSafe() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr08_SG_AgDataRawSafe.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr08_SG_ClstrSts1ForAutnmsDrv() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr08_SG_ClstrSts1ForAutnmsDrv.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr09_SG_AdSteerPaddlPsdGroupSafe() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr09_SG_AdSteerPaddlPsdGroupSafe.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr09_SG_SteerWhlSnsr() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr09_SG_SteerWhlSnsr.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr09_WhlMotSysCluSts() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr09_WhlMotSysCluSts.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr11_AhbcIndcnToAPI() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr11_AhbcIndcnToAPI.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr11_DoorDrvrMovmtFailNotif() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr11_DoorDrvrMovmtFailNotif.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr11_DoorDrvrReMovmtFailNotif() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr11_DoorDrvrReMovmtFailNotif.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr11_DoorPassMovmtFailNotif() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr11_DoorPassMovmtFailNotif.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr11_DoorPassReMovmtFailNotif() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr11_DoorPassReMovmtFailNotif.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr11_SG_BltLockStAtDrvr() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr11_SG_BltLockStAtDrvr.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr11_SG_BltLockStAtPass() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr11_SG_BltLockStAtPass.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr11_SG_BltLockStAtRowSecLe() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr11_SG_BltLockStAtRowSecLe.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr11_SG_BltLockStAtRowSecRi() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr11_SG_BltLockStAtRowSecRi.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr11_SG_DoorPassRePosnStsToAPI() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr11_SG_DoorPassRePosnStsToAPI.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr12_ExtDcDcActvnAllwd() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr12_ExtDcDcActvnAllwd.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr12_PtTqAtAxleAvlFrntMax() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr12_PtTqAtAxleAvlFrntMax.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr12_PtTqAtAxleAvlReMax() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr12_PtTqAtAxleAvlReMax.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr12_SG_BrkPedlVal() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr12_SG_BrkPedlVal.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr12_SG_ClstrSts2ForAutnmsDrv() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr12_SG_ClstrSts2ForAutnmsDrv.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr13_AdActvnOkFromVehDyn() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr13_AdActvnOkFromVehDyn.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr13_PtTqAtAxleAvlReMaxLong() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr13_PtTqAtAxleAvlReMaxLong.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr13_SG_AutnmsDrvModMngtGlbSafe1() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr13_SG_AutnmsDrvModMngtGlbSafe1.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr13_SG_EngFltIndcn() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr13_SG_EngFltIndcn.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr14_CllsnFwdWarnReq() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr14_CllsnFwdWarnReq.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr14_CllsnThreat() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr14_CllsnThreat.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr14_CnclReqForCrsCtrl() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr14_CnclReqForCrsCtrl.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr14_CooltFlowInDtElecForExt() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr14_CooltFlowInDtElecForExt.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr14_CooltTInDtElecForExt() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr14_CooltTInDtElecForExt.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr14_IndcrDisp1WdSts() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr14_IndcrDisp1WdSts.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr14_IndcrTurnSts1WdSts() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr14_IndcrTurnSts1WdSts.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr14_SG_CllsnAidSnvtySeld() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr14_SG_CllsnAidSnvtySeld.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr14_SrvRqrdForCllsnAid() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr14_SrvRqrdForCllsnAid.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr16_AsySftyDecelEnadByVehDyn() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr16_AsySftyDecelEnadByVehDyn.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr16_EngOilPWarn() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr16_EngOilPWarn.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr16_InhbOfAsySftyDecelByVehDyn() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr16_InhbOfAsySftyDecelByVehDyn.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr16_PrpsnErrIndcnReq() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr16_PrpsnErrIndcnReq.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr16_SG_RoadLoadNom() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr16_SG_RoadLoadNom.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr16_TankFlapSts() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr16_TankFlapSts.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr16_TrsmFltIndcn() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr16_TrsmFltIndcn.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr17_DoorDrvrReSts() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr17_DoorDrvrReSts.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr17_DoorDrvrSts() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr17_DoorDrvrSts.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr17_DoorPassReSts() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr17_DoorPassReSts.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr17_DoorPassSts() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr17_DoorPassSts.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr17_EngOilLvlSts() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr17_EngOilLvlSts.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr17_HoodSts() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr17_HoodSts.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr17_SG_ALnchTiDly3() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr17_SG_ALnchTiDly3.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr17_SG_AlrmSts1() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr17_SG_AlrmSts1.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr17_TrSts() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr17_TrSts.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr18_AsySftyBrkDlyEstimd() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr18_AsySftyBrkDlyEstimd.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr18_HvSysActvStsExt1() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr18_HvSysActvStsExt1.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr18_SG_AccrPedlPsd() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr18_SG_AccrPedlPsd.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr18_SG_BrkPedlPsdSafeGroup() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr18_SG_BrkPedlPsdSafeGroup.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr18_SG_OvrdDecelByDrvr() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr18_SG_OvrdDecelByDrvr.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr19_AccrPedlRat() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr19_AccrPedlRat.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr19_ChrgnUReqExt() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr19_ChrgnUReqExt.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr19_DrvrDecelReq() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr19_DrvrDecelReq.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr19_PtGearAct() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr19_PtGearAct.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr19_SG_TirePWarnFrntRi() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr19_SG_TirePWarnFrntRi.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr19_SG_TirePWarnReLe() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr19_SG_TirePWarnReLe.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr19_SG_TirePWarnReRi() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr19_SG_TirePWarnReRi.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr22_PtTqAtAxleAvlFrntMaxLong() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr22_PtTqAtAxleAvlFrntMaxLong.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr22_SG_AdSecBrkActvnGroupSafe() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr22_SG_AdSecBrkActvnGroupSafe.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr22_SG_BrkFricTqTotAtWhlsAct() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr22_SG_BrkFricTqTotAtWhlsAct.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr25_EgyAvlChrgTot() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr25_EgyAvlChrgTot.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr25_EgyAvlDchaTot() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr25_EgyAvlDchaTot.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr25_SG_DrvrIntvSts() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr25_SG_DrvrIntvSts.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr25_SG_DrvrPrsntGroup() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr25_SG_DrvrPrsntGroup.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr29_BattChrgnTiEstimdExt() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr29_BattChrgnTiEstimdExt.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr29_BattIExt() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr29_BattIExt.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr29_BattUExt() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr29_BattUExt.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr29_ChrgnTypExt() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr29_ChrgnTypExt.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr29_ChrgrHndlStsExt() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr29_ChrgrHndlStsExt.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr30_SG_AutnmsDrvModMngtExtSafe() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr30_SG_AutnmsDrvModMngtExtSafe.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr30_SG_BrkDegraded() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr30_SG_BrkDegraded.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr30_VehManDeactvnReqInProgs1() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr30_VehManDeactvnReqInProgs1.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr30_VehUsgSt() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr30_VehUsgSt.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr34_SG_StandStillMgrStsForHldSafe() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr34_SG_StandStillMgrStsForHldSafe.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr34_SG_SwtIndcrToAPI() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr34_SG_SwtIndcrToAPI.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr34_SwtBeamHiToAPI() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr34_SwtBeamHiToAPI.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr34_SwtLiHzrdWarnToAPI() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr34_SwtLiHzrdWarnToAPI.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr36_SG_ALgtMaxAvl() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr36_SG_ALgtMaxAvl.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr36_SG_WhlLockSts() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VCU1Mid3CanFr36_SG_WhlLockSts.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VIMBMid6CanFdFr14_ACU_SG_AdSecWhlAgReqGroupSafe_A() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VIMBMid6CanFdFr14_ACU_SG_AdSecWhlAgReqGroupSafe_A.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VIMBMid6CanFdFr28_ACU_SG_AdSecALgtLimReqGroupSafe_A() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VIMBMid6CanFdFr28_ACU_SG_AdSecALgtLimReqGroupSafe_A.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VIMBMid6CanFdFr28_ACU_SG_SecAdNomALgtReqGroupSafe_A() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VIMBMid6CanFdFr28_ACU_SG_SecAdNomALgtReqGroupSafe_A.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VIMMid3CanFr07_ACU_SG_AutnmsDrvStReq_A() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VIMMid3CanFr07_ACU_SG_AutnmsDrvStReq_A.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VIMMid3CanFr14_ACU_SG_AdNomALgtReqGroupSafe_A() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VIMMid3CanFr14_ACU_SG_AdNomALgtReqGroupSafe_A.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VIMMid3CanFr15_ACU_SG_AdPrimALgtLimReqGroupSafe_A() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VIMMid3CanFr15_ACU_SG_AdPrimALgtLimReqGroupSafe_A.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VIMMid5CanFdFr02_ACU_SG_AdPrimPose_A() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VIMMid5CanFdFr02_ACU_SG_AdPrimPose_A.value)


#  define Rte_IRead_VehSigInput_10ms_Runnable_VIMMid5CanFdFr12_ACU_SG_AdPrimWhlAgReqGroupSafe_A() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_VehSigInput_VehSigInput_10ms_Runnable.Rte_VIMMid5CanFdFr12_ACU_SG_AdPrimWhlAgReqGroupSafe_A.value)


#  define Rte_IWrite_VehSigInput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_VehSigInput_VSI_McuCanTimeout_VSI_McuCanTimeout.value = *(data) \
  )


#  define Rte_IWriteRef_VehSigInput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_VehSigInput_VSI_McuCanTimeout_VSI_McuCanTimeout.value \
  )


#  define Rte_IWrite_VehSigInput_10ms_Runnable_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_VehSigInput_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R.value = *(data) \
  )


#  define Rte_IWriteRef_VehSigInput_10ms_Runnable_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_VehSigInput_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R.value \
  )


#  define Rte_IWrite_VehSigInput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_VehSigInput_VSI_VehicleInfo_VSI_VehicleInfo.value = *(data) \
  )


#  define Rte_IWriteRef_VehSigInput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_VehSigInput_VSI_VehicleInfo_VSI_VehicleInfo.value \
  )


#  define Rte_IWrite_VehSigInput_Init_VSI_McuCanTimeout_VSI_McuCanTimeout(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_VehSigInput_VehSigInput_Init.Rte_VSI_McuCanTimeout_VSI_McuCanTimeout.value = *(data) \
  )


#  define Rte_IWriteRef_VehSigInput_Init_VSI_McuCanTimeout_VSI_McuCanTimeout() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_VehSigInput_VehSigInput_Init.Rte_VSI_McuCanTimeout_VSI_McuCanTimeout.value \
  )


#  define Rte_IWrite_VehSigInput_Init_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_VehSigInput_VehSigInput_Init.Rte_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R.value = *(data) \
  )


#  define Rte_IWriteRef_VehSigInput_Init_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_VehSigInput_VehSigInput_Init.Rte_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R.value \
  )


#  define Rte_IWrite_VehSigInput_Init_VSI_VehicleInfo_VSI_VehicleInfo(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_VehSigInput_VehSigInput_Init.Rte_VSI_VehicleInfo_VSI_VehicleInfo.value = *(data) \
  )


#  define Rte_IWriteRef_VehSigInput_Init_VSI_VehicleInfo_VSI_VehicleInfo() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_VehSigInput_VehSigInput_Init.Rte_VSI_VehicleInfo_VSI_VehicleInfo.value \
  )


# endif /* !defined(RTE_CORE) */


# define VehSigInput_START_SEC_CODE
//# include "VehSigInput_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/**********************************************************************************************************************
 * Runnable entities
 *********************************************************************************************************************/

# ifndef RTE_CORE
#  define RTE_RUNNABLE_VehSigInput_10ms_Runnable VehSigInput_10ms_Runnable
#  define RTE_RUNNABLE_VehSigInput_Init VehSigInput_Init
# endif

FUNC(void, VehSigInput_CODE) VehSigInput_10ms_Runnable(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */
FUNC(void, VehSigInput_CODE) VehSigInput_Init(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */

# define VehSigInput_STOP_SEC_CODE
//# include "VehSigInput_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

# ifdef __cplusplus
} /* extern "C" */
# endif /* __cplusplus */

#endif /* RTE_VEHSIGINPUT_H */

/**********************************************************************************************************************
 MISRA 2012 violations and justifications
 *********************************************************************************************************************/

/* module specific MISRA deviations:
   MD_Rte_0786:  MISRA rule: Rule5.5
     Reason:     Same macro and idintifier names in first 63 characters are required to meet AUTOSAR spec.
     Risk:       No functional risk.
     Prevention: Not required.

   MD_Rte_3449:  MISRA rule: Rule8.5
     Reason:     Schedulable entities are declared by the RTE and also by the BSW modules.
     Risk:       No functional risk.
     Prevention: Not required.

   MD_Rte_3451:  MISRA rule: Rule8.5
     Reason:     Schedulable entities are declared by the RTE and also by the BSW modules.
     Risk:       No functional risk.
     Prevention: Not required.

*/
