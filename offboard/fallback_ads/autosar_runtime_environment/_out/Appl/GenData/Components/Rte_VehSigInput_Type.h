/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *             File:  Rte_VehSigInput_Type.h
 *           Config:  DiDi_FBU.dpa
 *      ECU-Project:  DiDi_FBU
 *
 *        Generator:  MICROSAR RTE Generator Version 4.27.0
 *                    RTE Core Version 1.27.0
 *          License:  CBD2100894
 *
 *      Description:  Application types header file for SW-C <VehSigInput>
 *********************************************************************************************************************/

/* double include prevention */
#ifndef RTE_VEHSIGINPUT_TYPE_H
# define RTE_VEHSIGINPUT_TYPE_H

# ifdef __cplusplus
extern "C"
{
# endif /* __cplusplus */

# include "_out/Appl/GenData/Rte_Type.h"

# ifndef RTE_CORE

/**********************************************************************************************************************
 * Range, Invalidation, Enumeration and Bit Field Definitions
 *********************************************************************************************************************/

#  define AccRequestAfterRateLimit_adt_LowerLimit (0U)
#  define AccRequestAfterRateLimit_adt_UpperLimit (511U)

#  define AccRequestByOutOfOdd_adt_LowerLimit (0U)
#  define AccRequestByOutOfOdd_adt_UpperLimit (511U)

#  define AccRequestBySpeed_adt_LowerLimit (0U)
#  define AccRequestBySpeed_adt_UpperLimit (511U)

#  define AccRequestForSystemError_adt_LowerLimit (0U)
#  define AccRequestForSystemError_adt_UpperLimit (511U)

#  define AccrPedlRat_adt_LowerLimit (0U)
#  define AccrPedlRat_adt_UpperLimit (25600U)

#  define AcuFbCanTimer_adt_LowerLimit (0U)
#  define AcuFbCanTimer_adt_UpperLimit (4095U)

#  define AcuMid3SsmCounter0Timer_adt_LowerLimit (0U)
#  define AcuMid3SsmCounter0Timer_adt_UpperLimit (4095U)

#  define AcuMid3SsmCounter1Timer_adt_LowerLimit (0U)
#  define AcuMid3SsmCounter1Timer_adt_UpperLimit (4095U)

#  define AcuMid5SsmCounter0Timer_adt_LowerLimit (0U)
#  define AcuMid5SsmCounter0Timer_adt_UpperLimit (4095U)

#  define AcuMid5SsmCounter1Timer_adt_LowerLimit (0U)
#  define AcuMid5SsmCounter1Timer_adt_UpperLimit (4095U)

#  define AcuMid6SsmCounter0Timer_adt_LowerLimit (0U)
#  define AcuMid6SsmCounter0Timer_adt_UpperLimit (4095U)

#  define AcuMid6SsmCounter1Timer_adt_LowerLimit (0U)
#  define AcuMid6SsmCounter1Timer_adt_UpperLimit (4095U)

#  define AdSetSpd_adt_LowerLimit (0U)
#  define AdSetSpd_adt_UpperLimit (600U)

#  define AswSoftwarewareVersion_adt_LowerLimit (0U)
#  define AswSoftwarewareVersion_adt_UpperLimit (255U)

#  define AsySftyBrkDlyEstimd_adt_LowerLimit (0U)
#  define AsySftyBrkDlyEstimd_adt_UpperLimit (127U)

#  define BattIExt_adt_LowerLimit (0U)
#  define BattIExt_adt_UpperLimit (8191U)

#  define BattUExt_adt_LowerLimit (0U)
#  define BattUExt_adt_UpperLimit (2044U)

#  define BootLoaderVersion_adt_LowerLimit (0U)
#  define BootLoaderVersion_adt_UpperLimit (255U)

#  define BswSoftwarewareVersion_adt_LowerLimit (0U)
#  define BswSoftwarewareVersion_adt_UpperLimit (255U)

#  define CarTiGlb_A_adt_LowerLimit (0U)
#  define CarTiGlb_A_adt_UpperLimit (4294967295U)

#  define ChrgnUReqExt_adt_LowerLimit (0U)
#  define ChrgnUReqExt_adt_UpperLimit (216U)

#  define ControlErrorHeading_adt_LowerLimit (-2048)
#  define ControlErrorHeading_adt_UpperLimit (2047)

#  define ControlErrorLateral_adt_LowerLimit (-128)
#  define ControlErrorLateral_adt_UpperLimit (127)

#  define ControlErrorLongitudinal_adt_LowerLimit (-2048)
#  define ControlErrorLongitudinal_adt_UpperLimit (2047)

#  define ControlErrorVelocity_adt_LowerLimit (-2048)
#  define ControlErrorVelocity_adt_UpperLimit (2047)

#  define ControlWheelAngleOffset_adt_LowerLimit (-8388608)
#  define ControlWheelAngleOffset_adt_UpperLimit (8388607)

#  define CooltFlowInDtElecForExt_adt_LowerLimit (0U)
#  define CooltFlowInDtElecForExt_adt_UpperLimit (1023U)

#  define CooltTInDtElecForExt_adt_LowerLimit (0U)
#  define CooltTInDtElecForExt_adt_UpperLimit (1800U)

#  define DrvrDecelReq_adt_LowerLimit (-128)
#  define DrvrDecelReq_adt_UpperLimit (127)

#  define EgoLaneWidth_adt_LowerLimit (0U)
#  define EgoLaneWidth_adt_UpperLimit (500U)

#  define EgoStopTime_adt_LowerLimit (0U)
#  define EgoStopTime_adt_UpperLimit (800U)

#  define EgyAvlChrgTot_adt_LowerLimit (0U)
#  define EgyAvlChrgTot_adt_UpperLimit (2410U)

#  define EgyAvlDchaTot_adt_LowerLimit (0U)
#  define EgyAvlDchaTot_adt_UpperLimit (2410U)

#  define EmergencyBrakeAcc_adt_LowerLimit (0U)
#  define EmergencyBrakeAcc_adt_UpperLimit (511U)

#  define FC_LineTiStamp_adt_LowerLimit (0U)
#  define FC_LineTiStamp_adt_UpperLimit (4294967295U)

#  define FC_Line_01_HeadingAngle_adt_LowerLimit (0U)
#  define FC_Line_01_HeadingAngle_adt_UpperLimit (6554U)

#  define FC_Line_01_Width_adt_LowerLimit (0U)
#  define FC_Line_01_Width_adt_UpperLimit (255U)

#  define FC_Line_01_dx_End_adt_LowerLimit (0U)
#  define FC_Line_01_dx_End_adt_UpperLimit (1023U)

#  define FC_Line_01_dx_End_std_adt_LowerLimit (0U)
#  define FC_Line_01_dx_End_std_adt_UpperLimit (511U)

#  define FC_Line_01_dx_Start_adt_LowerLimit (0U)
#  define FC_Line_01_dx_Start_adt_UpperLimit (8191U)

#  define FC_Line_01_dx_Start_std_adt_LowerLimit (0U)
#  define FC_Line_01_dx_Start_std_adt_UpperLimit (511U)

#  define FC_Line_01_dy_adt_LowerLimit (0U)
#  define FC_Line_01_dy_adt_UpperLimit (4095U)

#  define FC_Line_01_exist_prob_adt_LowerLimit (0U)
#  define FC_Line_01_exist_prob_adt_UpperLimit (50U)

#  define FC_Line_02_HeadingAngle_adt_LowerLimit (0U)
#  define FC_Line_02_HeadingAngle_adt_UpperLimit (6554U)

#  define FC_Line_02_Width_adt_LowerLimit (0U)
#  define FC_Line_02_Width_adt_UpperLimit (255U)

#  define FC_Line_02_dx_End_adt_LowerLimit (0U)
#  define FC_Line_02_dx_End_adt_UpperLimit (1023U)

#  define FC_Line_02_dx_End_std_adt_LowerLimit (0U)
#  define FC_Line_02_dx_End_std_adt_UpperLimit (511U)

#  define FC_Line_02_dx_Start_adt_LowerLimit (0U)
#  define FC_Line_02_dx_Start_adt_UpperLimit (8191U)

#  define FC_Line_02_dx_Start_std_adt_LowerLimit (0U)
#  define FC_Line_02_dx_Start_std_adt_UpperLimit (511U)

#  define FC_Line_02_dy_adt_LowerLimit (0U)
#  define FC_Line_02_dy_adt_UpperLimit (4095U)

#  define FC_Line_02_exist_prob_adt_LowerLimit (0U)
#  define FC_Line_02_exist_prob_adt_UpperLimit (50U)

#  define FC_Line_03_HeadingAngle_adt_LowerLimit (0U)
#  define FC_Line_03_HeadingAngle_adt_UpperLimit (6554U)

#  define FC_Line_03_Width_adt_LowerLimit (0U)
#  define FC_Line_03_Width_adt_UpperLimit (255U)

#  define FC_Line_03_dx_End_adt_LowerLimit (0U)
#  define FC_Line_03_dx_End_adt_UpperLimit (1023U)

#  define FC_Line_03_dx_End_std_adt_LowerLimit (0U)
#  define FC_Line_03_dx_End_std_adt_UpperLimit (511U)

#  define FC_Line_03_dx_Start_adt_LowerLimit (0U)
#  define FC_Line_03_dx_Start_adt_UpperLimit (8191U)

#  define FC_Line_03_dx_Start_std_adt_LowerLimit (0U)
#  define FC_Line_03_dx_Start_std_adt_UpperLimit (511U)

#  define FC_Line_03_dy_adt_LowerLimit (0U)
#  define FC_Line_03_dy_adt_UpperLimit (4095U)

#  define FC_Line_03_exist_prob_adt_LowerLimit (0U)
#  define FC_Line_03_exist_prob_adt_UpperLimit (50U)

#  define FC_Line_04_HeadingAngle_adt_LowerLimit (0U)
#  define FC_Line_04_HeadingAngle_adt_UpperLimit (6554U)

#  define FC_Line_04_Width_adt_LowerLimit (0U)
#  define FC_Line_04_Width_adt_UpperLimit (255U)

#  define FC_Line_04_dx_End_adt_LowerLimit (0U)
#  define FC_Line_04_dx_End_adt_UpperLimit (1023U)

#  define FC_Line_04_dx_End_std_adt_LowerLimit (0U)
#  define FC_Line_04_dx_End_std_adt_UpperLimit (511U)

#  define FC_Line_04_dx_Start_adt_LowerLimit (0U)
#  define FC_Line_04_dx_Start_adt_UpperLimit (8191U)

#  define FC_Line_04_dx_Start_std_adt_LowerLimit (0U)
#  define FC_Line_04_dx_Start_std_adt_UpperLimit (511U)

#  define FC_Line_04_dy_adt_LowerLimit (0U)
#  define FC_Line_04_dy_adt_UpperLimit (4095U)

#  define FC_Line_04_exist_prob_adt_LowerLimit (0U)
#  define FC_Line_04_exist_prob_adt_UpperLimit (50U)

#  define FC_Line_05_HeadingAngle_adt_LowerLimit (0U)
#  define FC_Line_05_HeadingAngle_adt_UpperLimit (6554U)

#  define FC_Line_05_Width_adt_LowerLimit (0U)
#  define FC_Line_05_Width_adt_UpperLimit (255U)

#  define FC_Line_05_dx_End_adt_LowerLimit (0U)
#  define FC_Line_05_dx_End_adt_UpperLimit (1023U)

#  define FC_Line_05_dx_End_std_adt_LowerLimit (0U)
#  define FC_Line_05_dx_End_std_adt_UpperLimit (511U)

#  define FC_Line_05_dx_Start_adt_LowerLimit (0U)
#  define FC_Line_05_dx_Start_adt_UpperLimit (8191U)

#  define FC_Line_05_dx_Start_std_adt_LowerLimit (0U)
#  define FC_Line_05_dx_Start_std_adt_UpperLimit (511U)

#  define FC_Line_05_dy_adt_LowerLimit (0U)
#  define FC_Line_05_dy_adt_UpperLimit (4095U)

#  define FC_Line_05_exist_prob_adt_LowerLimit (0U)
#  define FC_Line_05_exist_prob_adt_UpperLimit (50U)

#  define FC_Line_06_HeadingAngle_adt_LowerLimit (0U)
#  define FC_Line_06_HeadingAngle_adt_UpperLimit (6554U)

#  define FC_Line_06_Width_adt_LowerLimit (0U)
#  define FC_Line_06_Width_adt_UpperLimit (255U)

#  define FC_Line_06_dx_End_adt_LowerLimit (0U)
#  define FC_Line_06_dx_End_adt_UpperLimit (1023U)

#  define FC_Line_06_dx_End_std_adt_LowerLimit (0U)
#  define FC_Line_06_dx_End_std_adt_UpperLimit (511U)

#  define FC_Line_06_dx_Start_adt_LowerLimit (0U)
#  define FC_Line_06_dx_Start_adt_UpperLimit (8191U)

#  define FC_Line_06_dx_Start_std_adt_LowerLimit (0U)
#  define FC_Line_06_dx_Start_std_adt_UpperLimit (511U)

#  define FC_Line_06_dy_adt_LowerLimit (0U)
#  define FC_Line_06_dy_adt_UpperLimit (4095U)

#  define FC_Line_06_exist_prob_adt_LowerLimit (0U)
#  define FC_Line_06_exist_prob_adt_UpperLimit (50U)

#  define FC_Obj20_Ax_adt_LowerLimit (0U)
#  define FC_Obj20_Ax_adt_UpperLimit (320U)

#  define FC_Obj20_Ay_adt_LowerLimit (0U)
#  define FC_Obj20_Ay_adt_UpperLimit (240U)

#  define FC_Obj20_Dx_adt_LowerLimit (0U)
#  define FC_Obj20_Dx_adt_UpperLimit (4094U)

#  define FC_Obj20_Dx_Vnce_adt_LowerLimit (0U)
#  define FC_Obj20_Dx_Vnce_adt_UpperLimit (1000U)

#  define FC_Obj20_Dy_adt_LowerLimit (0U)
#  define FC_Obj20_Dy_adt_UpperLimit (16000U)

#  define FC_Obj20_Dy_Vnce_adt_LowerLimit (0U)
#  define FC_Obj20_Dy_Vnce_adt_UpperLimit (1000U)

#  define FC_Obj20_ExistProb_adt_LowerLimit (0U)
#  define FC_Obj20_ExistProb_adt_UpperLimit (50U)

#  define FC_Obj20_HeadingAngle_adt_LowerLimit (0U)
#  define FC_Obj20_HeadingAngle_adt_UpperLimit (314U)

#  define FC_Obj20_Height_adt_LowerLimit (0U)
#  define FC_Obj20_Height_adt_UpperLimit (511U)

#  define FC_Obj20_Length_adt_LowerLimit (0U)
#  define FC_Obj20_Length_adt_UpperLimit (510U)

#  define FC_Obj20_Vx_adt_LowerLimit (0U)
#  define FC_Obj20_Vx_adt_UpperLimit (2040U)

#  define FC_Obj20_Vy_adt_LowerLimit (0U)
#  define FC_Obj20_Vy_adt_UpperLimit (1000U)

#  define FC_Obj20_Width_adt_LowerLimit (0U)
#  define FC_Obj20_Width_adt_UpperLimit (1200U)

#  define FRS_HostSpeed_adt_LowerLimit (0U)
#  define FRS_HostSpeed_adt_UpperLimit (4095U)

#  define FRS_Host_Yaw_adt_LowerLimit (0U)
#  define FRS_Host_Yaw_adt_UpperLimit (2047U)

#  define FRS_Latency_adt_LowerLimit (0U)
#  define FRS_Latency_adt_UpperLimit (63U)

#  define FTFC_Line_01_curvature_alte_adt_LowerLimit (0U)
#  define FTFC_Line_01_curvature_alte_adt_UpperLimit (4095U)

#  define FTFC_Line_01_curve_adt_LowerLimit (0U)
#  define FTFC_Line_01_curve_adt_UpperLimit (16382U)

#  define FTFC_Line_02_curvature_alte_adt_LowerLimit (0U)
#  define FTFC_Line_02_curvature_alte_adt_UpperLimit (4095U)

#  define FTFC_Line_02_curve_adt_LowerLimit (0U)
#  define FTFC_Line_02_curve_adt_UpperLimit (16382U)

#  define FTFC_Line_03_curvature_alte_adt_LowerLimit (0U)
#  define FTFC_Line_03_curvature_alte_adt_UpperLimit (4095U)

#  define FTFC_Line_03_curve_adt_LowerLimit (0U)
#  define FTFC_Line_03_curve_adt_UpperLimit (16382U)

#  define FTFC_Line_04_curvature_alte_adt_LowerLimit (0U)
#  define FTFC_Line_04_curvature_alte_adt_UpperLimit (4095U)

#  define FTFC_Line_04_curve_adt_LowerLimit (0U)
#  define FTFC_Line_04_curve_adt_UpperLimit (16382U)

#  define FTFC_Line_05_curvature_alte_adt_LowerLimit (0U)
#  define FTFC_Line_05_curvature_alte_adt_UpperLimit (4095U)

#  define FTFC_Line_05_curve_adt_LowerLimit (0U)
#  define FTFC_Line_05_curve_adt_UpperLimit (16382U)

#  define FTFC_Line_06_curvature_alte_adt_LowerLimit (0U)
#  define FTFC_Line_06_curvature_alte_adt_UpperLimit (4095U)

#  define FTFC_Line_06_curve_adt_LowerLimit (0U)
#  define FTFC_Line_06_curve_adt_UpperLimit (16382U)

#  define FTFC_Obj20_CenterAngle_adt_LowerLimit (0U)
#  define FTFC_Obj20_CenterAngle_adt_UpperLimit (314U)

#  define FTFC_Obj20_CornerPoint_x_adt_LowerLimit (0U)
#  define FTFC_Obj20_CornerPoint_x_adt_UpperLimit (20000U)

#  define FTFC_Obj20_CornerPoint_y_adt_LowerLimit (0U)
#  define FTFC_Obj20_CornerPoint_y_adt_UpperLimit (16000U)

#  define FTFC_Obj20_DistInLane_adt_LowerLimit (0U)
#  define FTFC_Obj20_DistInLane_adt_UpperLimit (2000U)

#  define FeedforwardsSteerAngle_adt_LowerLimit (0U)
#  define FeedforwardsSteerAngle_adt_UpperLimit (15600U)

#  define FrFr_AccOBJ_Ax_adt_LowerLimit (0U)
#  define FrFr_AccOBJ_Ax_adt_UpperLimit (1023U)

#  define FrFr_AccOBJ_Ay_adt_LowerLimit (0U)
#  define FrFr_AccOBJ_Ay_adt_UpperLimit (511U)

#  define FrFr_AccOBJ_Dx_adt_LowerLimit (0U)
#  define FrFr_AccOBJ_Dx_adt_UpperLimit (8191U)

#  define FrFr_AccOBJ_Dx_Vnce_adt_LowerLimit (0U)
#  define FrFr_AccOBJ_Dx_Vnce_adt_UpperLimit (1000U)

#  define FrFr_AccOBJ_Dy_adt_LowerLimit (0U)
#  define FrFr_AccOBJ_Dy_adt_UpperLimit (2047U)

#  define FrFr_AccOBJ_Dy_Vnce_adt_LowerLimit (0U)
#  define FrFr_AccOBJ_Dy_Vnce_adt_UpperLimit (1000U)

#  define FrFr_AccOBJ_ExistProb_adt_LowerLimit (0U)
#  define FrFr_AccOBJ_ExistProb_adt_UpperLimit (50U)

#  define FrFr_AccOBJ_HeadingAngle_adt_LowerLimit (0U)
#  define FrFr_AccOBJ_HeadingAngle_adt_UpperLimit (314U)

#  define FrFr_AccOBJ_Height_adt_LowerLimit (0U)
#  define FrFr_AccOBJ_Height_adt_UpperLimit (511U)

#  define FrFr_AccOBJ_Length_adt_LowerLimit (0U)
#  define FrFr_AccOBJ_Length_adt_UpperLimit (510U)

#  define FrFr_AccOBJ_ObstacleProb_adt_LowerLimit (0U)
#  define FrFr_AccOBJ_ObstacleProb_adt_UpperLimit (50U)

#  define FrFr_AccOBJ_Vx_adt_LowerLimit (0U)
#  define FrFr_AccOBJ_Vx_adt_UpperLimit (4095U)

#  define FrFr_AccOBJ_Vx_std_adt_LowerLimit (0U)
#  define FrFr_AccOBJ_Vx_std_adt_UpperLimit (4095U)

#  define FrFr_AccOBJ_Vy_adt_LowerLimit (0U)
#  define FrFr_AccOBJ_Vy_adt_UpperLimit (1023U)

#  define FrFr_AccOBJ_Vy_std_adt_LowerLimit (0U)
#  define FrFr_AccOBJ_Vy_std_adt_UpperLimit (1023U)

#  define FrFr_AccOBJ_Width_adt_LowerLimit (0U)
#  define FrFr_AccOBJ_Width_adt_UpperLimit (1200U)

#  define Fr_AccOBJ_Ax_adt_LowerLimit (0U)
#  define Fr_AccOBJ_Ax_adt_UpperLimit (1023U)

#  define Fr_AccOBJ_Ay_adt_LowerLimit (0U)
#  define Fr_AccOBJ_Ay_adt_UpperLimit (511U)

#  define Fr_AccOBJ_Dx_adt_LowerLimit (0U)
#  define Fr_AccOBJ_Dx_adt_UpperLimit (8191U)

#  define Fr_AccOBJ_Dx_Vnce_adt_LowerLimit (0U)
#  define Fr_AccOBJ_Dx_Vnce_adt_UpperLimit (1000U)

#  define Fr_AccOBJ_Dy_adt_LowerLimit (0U)
#  define Fr_AccOBJ_Dy_adt_UpperLimit (2047U)

#  define Fr_AccOBJ_Dy_Vnce_adt_LowerLimit (0U)
#  define Fr_AccOBJ_Dy_Vnce_adt_UpperLimit (1000U)

#  define Fr_AccOBJ_ExistProb_adt_LowerLimit (0U)
#  define Fr_AccOBJ_ExistProb_adt_UpperLimit (50U)

#  define Fr_AccOBJ_HeadingAngle_adt_LowerLimit (0U)
#  define Fr_AccOBJ_HeadingAngle_adt_UpperLimit (314U)

#  define Fr_AccOBJ_Height_adt_LowerLimit (0U)
#  define Fr_AccOBJ_Height_adt_UpperLimit (511U)

#  define Fr_AccOBJ_Length_adt_LowerLimit (0U)
#  define Fr_AccOBJ_Length_adt_UpperLimit (510U)

#  define Fr_AccOBJ_ObstacleProb_adt_LowerLimit (0U)
#  define Fr_AccOBJ_ObstacleProb_adt_UpperLimit (50U)

#  define Fr_AccOBJ_Vx_adt_LowerLimit (0U)
#  define Fr_AccOBJ_Vx_adt_UpperLimit (4095U)

#  define Fr_AccOBJ_Vx_std_adt_LowerLimit (0U)
#  define Fr_AccOBJ_Vx_std_adt_UpperLimit (4095U)

#  define Fr_AccOBJ_Vy_adt_LowerLimit (0U)
#  define Fr_AccOBJ_Vy_adt_UpperLimit (1023U)

#  define Fr_AccOBJ_Vy_std_adt_LowerLimit (0U)
#  define Fr_AccOBJ_Vy_std_adt_UpperLimit (1023U)

#  define Fr_AccOBJ_Width_adt_LowerLimit (0U)
#  define Fr_AccOBJ_Width_adt_UpperLimit (1200U)

#  define FrontCameraCanTimer_adt_LowerLimit (0U)
#  define FrontCameraCanTimer_adt_UpperLimit (4095U)

#  define FrontObjectTTC_adt_LowerLimit (0U)
#  define FrontObjectTTC_adt_UpperLimit (4095U)

#  define FrontRadarCanTimer_adt_LowerLimit (0U)
#  define FrontRadarCanTimer_adt_UpperLimit (4095U)

#  define GnssPoseHeading_adt_LowerLimit (-8388608)
#  define GnssPoseHeading_adt_UpperLimit (8388607)

#  define GnssPosePositionX_adt_LowerLimit (-2147483647 - 1)
#  define GnssPosePositionX_adt_UpperLimit (2147483647)

#  define GnssPosePositionY_adt_LowerLimit (-2147483647 - 1)
#  define GnssPosePositionY_adt_UpperLimit (2147483647)

#  define GnssPoseVelocityX_adt_LowerLimit (-131072)
#  define GnssPoseVelocityX_adt_UpperLimit (131071)

#  define GnssPoseVelocityY_adt_LowerLimit (-131072)
#  define GnssPoseVelocityY_adt_UpperLimit (131071)

#  define GnssPoseYaw_adt_LowerLimit (-8388608)
#  define GnssPoseYaw_adt_UpperLimit (8388607)

#  define GradientLimitAccRequest_adt_LowerLimit (0U)
#  define GradientLimitAccRequest_adt_UpperLimit (511U)

#  define HeadingAngleError_adt_LowerLimit (0U)
#  define HeadingAngleError_adt_UpperLimit (400U)

#  define LatPidDistanceDComponent_adt_LowerLimit (-1024)
#  define LatPidDistanceDComponent_adt_UpperLimit (1023)

#  define LatPidDistanceIComponent_adt_LowerLimit (-2048)
#  define LatPidDistanceIComponent_adt_UpperLimit (2047)

#  define LatPidDistancePComponent_adt_LowerLimit (-2048)
#  define LatPidDistancePComponent_adt_UpperLimit (2047)

#  define LatPidDistanceTotal_adt_LowerLimit (-2048)
#  define LatPidDistanceTotal_adt_UpperLimit (2047)

#  define LatPidHeadingDComponent_adt_LowerLimit (-2048)
#  define LatPidHeadingDComponent_adt_UpperLimit (2047)

#  define LatPidHeadingIComponent_adt_LowerLimit (-2048)
#  define LatPidHeadingIComponent_adt_UpperLimit (2047)

#  define LatPidHeadingPComponent_adt_LowerLimit (-2048)
#  define LatPidHeadingPComponent_adt_UpperLimit (2047)

#  define LatPidHeadingTotal_adt_LowerLimit (-2048)
#  define LatPidHeadingTotal_adt_UpperLimit (2047)

#  define LatSteeringFeeback_adt_LowerLimit (-2048)
#  define LatSteeringFeeback_adt_UpperLimit (2047)

#  define LatSteeringFeedforward_adt_LowerLimit (-2048)
#  define LatSteeringFeedforward_adt_UpperLimit (2047)

#  define LateralDistanceError_adt_LowerLimit (0U)
#  define LateralDistanceError_adt_UpperLimit (6000U)

#  define LeFr_AccOBJ_Ax_adt_LowerLimit (0U)
#  define LeFr_AccOBJ_Ax_adt_UpperLimit (1023U)

#  define LeFr_AccOBJ_Ay_adt_LowerLimit (0U)
#  define LeFr_AccOBJ_Ay_adt_UpperLimit (511U)

#  define LeFr_AccOBJ_Dx_adt_LowerLimit (0U)
#  define LeFr_AccOBJ_Dx_adt_UpperLimit (8191U)

#  define LeFr_AccOBJ_Dx_Vnce_adt_LowerLimit (0U)
#  define LeFr_AccOBJ_Dx_Vnce_adt_UpperLimit (1000U)

#  define LeFr_AccOBJ_Dy_adt_LowerLimit (0U)
#  define LeFr_AccOBJ_Dy_adt_UpperLimit (2047U)

#  define LeFr_AccOBJ_Dy_Vnce_adt_LowerLimit (0U)
#  define LeFr_AccOBJ_Dy_Vnce_adt_UpperLimit (1000U)

#  define LeFr_AccOBJ_ExistProb_adt_LowerLimit (0U)
#  define LeFr_AccOBJ_ExistProb_adt_UpperLimit (50U)

#  define LeFr_AccOBJ_HeadingAngle_adt_LowerLimit (0U)
#  define LeFr_AccOBJ_HeadingAngle_adt_UpperLimit (314U)

#  define LeFr_AccOBJ_Height_adt_LowerLimit (0U)
#  define LeFr_AccOBJ_Height_adt_UpperLimit (511U)

#  define LeFr_AccOBJ_Length_adt_LowerLimit (0U)
#  define LeFr_AccOBJ_Length_adt_UpperLimit (510U)

#  define LeFr_AccOBJ_ObstacleProb_adt_LowerLimit (0U)
#  define LeFr_AccOBJ_ObstacleProb_adt_UpperLimit (50U)

#  define LeFr_AccOBJ_Vx_adt_LowerLimit (0U)
#  define LeFr_AccOBJ_Vx_adt_UpperLimit (4095U)

#  define LeFr_AccOBJ_Vx_std_adt_LowerLimit (0U)
#  define LeFr_AccOBJ_Vx_std_adt_UpperLimit (4095U)

#  define LeFr_AccOBJ_Vy_adt_LowerLimit (0U)
#  define LeFr_AccOBJ_Vy_adt_UpperLimit (1023U)

#  define LeFr_AccOBJ_Vy_std_adt_LowerLimit (0U)
#  define LeFr_AccOBJ_Vy_std_adt_UpperLimit (1023U)

#  define LeFr_AccOBJ_Width_adt_LowerLimit (0U)
#  define LeFr_AccOBJ_Width_adt_UpperLimit (1200U)

#  define Le_AccOBJ_Ax_adt_LowerLimit (0U)
#  define Le_AccOBJ_Ax_adt_UpperLimit (1023U)

#  define Le_AccOBJ_Ay_adt_LowerLimit (0U)
#  define Le_AccOBJ_Ay_adt_UpperLimit (511U)

#  define Le_AccOBJ_Dx_adt_LowerLimit (0U)
#  define Le_AccOBJ_Dx_adt_UpperLimit (8191U)

#  define Le_AccOBJ_Dx_Vnce_adt_LowerLimit (0U)
#  define Le_AccOBJ_Dx_Vnce_adt_UpperLimit (1000U)

#  define Le_AccOBJ_Dy_adt_LowerLimit (0U)
#  define Le_AccOBJ_Dy_adt_UpperLimit (2047U)

#  define Le_AccOBJ_Dy_Vnce_adt_LowerLimit (0U)
#  define Le_AccOBJ_Dy_Vnce_adt_UpperLimit (1000U)

#  define Le_AccOBJ_ExistProb_adt_LowerLimit (0U)
#  define Le_AccOBJ_ExistProb_adt_UpperLimit (50U)

#  define Le_AccOBJ_HeadingAngle_adt_LowerLimit (0U)
#  define Le_AccOBJ_HeadingAngle_adt_UpperLimit (314U)

#  define Le_AccOBJ_Height_adt_LowerLimit (0U)
#  define Le_AccOBJ_Height_adt_UpperLimit (511U)

#  define Le_AccOBJ_Length_adt_LowerLimit (0U)
#  define Le_AccOBJ_Length_adt_UpperLimit (510U)

#  define Le_AccOBJ_ObstacleProb_adt_LowerLimit (0U)
#  define Le_AccOBJ_ObstacleProb_adt_UpperLimit (50U)

#  define Le_AccOBJ_Vx_adt_LowerLimit (0U)
#  define Le_AccOBJ_Vx_adt_UpperLimit (4095U)

#  define Le_AccOBJ_Vx_std_adt_LowerLimit (0U)
#  define Le_AccOBJ_Vx_std_adt_UpperLimit (4095U)

#  define Le_AccOBJ_Vy_adt_LowerLimit (0U)
#  define Le_AccOBJ_Vy_adt_UpperLimit (1023U)

#  define Le_AccOBJ_Vy_std_adt_LowerLimit (0U)
#  define Le_AccOBJ_Vy_std_adt_UpperLimit (1023U)

#  define Le_AccOBJ_Width_adt_LowerLimit (0U)
#  define Le_AccOBJ_Width_adt_UpperLimit (1200U)

#  define LeftRoadEdgeLatDist_adt_LowerLimit (-16384)
#  define LeftRoadEdgeLatDist_adt_UpperLimit (16383)

#  define LeftRoadEdgeLength_adt_LowerLimit (0U)
#  define LeftRoadEdgeLength_adt_UpperLimit (65535U)

#  define LimitAccRequest_adt_LowerLimit (0U)
#  define LimitAccRequest_adt_UpperLimit (511U)

#  define LimitSteerAngle_adt_LowerLimit (0U)
#  define LimitSteerAngle_adt_UpperLimit (15600U)

#  define LimitSteerAngleRequest_adt_LowerLimit (0U)
#  define LimitSteerAngleRequest_adt_UpperLimit (7800U)

#  define LongAccRequest_adt_LowerLimit (0U)
#  define LongAccRequest_adt_UpperLimit (511U)

#  define LongAccelerationFeeback_adt_LowerLimit (-2048)
#  define LongAccelerationFeeback_adt_UpperLimit (2047)

#  define LongAccelerationFeedforward_adt_LowerLimit (-2048)
#  define LongAccelerationFeedforward_adt_UpperLimit (2047)

#  define LongNecAcc_adt_LowerLimit (0U)
#  define LongNecAcc_adt_UpperLimit (511U)

#  define LongPidDistanceDComponent_adt_LowerLimit (-2048)
#  define LongPidDistanceDComponent_adt_UpperLimit (2047)

#  define LongPidDistanceIComponent_adt_LowerLimit (-2048)
#  define LongPidDistanceIComponent_adt_UpperLimit (2047)

#  define LongPidDistancePComponent_adt_LowerLimit (-2048)
#  define LongPidDistancePComponent_adt_UpperLimit (2047)

#  define LongPidDistanceTotal_adt_LowerLimit (-2048)
#  define LongPidDistanceTotal_adt_UpperLimit (2047)

#  define LongPidSpeedDComponent_adt_LowerLimit (-2048)
#  define LongPidSpeedDComponent_adt_UpperLimit (2047)

#  define LongPidSpeedIComponent_adt_LowerLimit (-2048)
#  define LongPidSpeedIComponent_adt_UpperLimit (2047)

#  define LongPidSpeedPComponent_adt_LowerLimit (-2048)
#  define LongPidSpeedPComponent_adt_UpperLimit (2047)

#  define LongPidSpeedTotal_adt_LowerLimit (-2048)
#  define LongPidSpeedTotal_adt_UpperLimit (2047)

#  define LqrIterationError_adt_LowerLimit (0U)
#  define LqrIterationError_adt_UpperLimit (500U)

#  define MaxSteerAngleRateThreshold_adt_LowerLimit (0U)
#  define MaxSteerAngleRateThreshold_adt_UpperLimit (255U)

#  define MaxSteerAngleThreshold_adt_LowerLimit (0U)
#  define MaxSteerAngleThreshold_adt_UpperLimit (15600U)

#  define MinAccRate_adt_LowerLimit (0U)
#  define MinAccRate_adt_UpperLimit (255U)

#  define ObjectStopTime_adt_LowerLimit (0U)
#  define ObjectStopTime_adt_UpperLimit (800U)

#  define PoseAccelerationX_adt_LowerLimit (-32768)
#  define PoseAccelerationX_adt_UpperLimit (32767)

#  define PoseAccelerationY_adt_LowerLimit (-32768)
#  define PoseAccelerationY_adt_UpperLimit (32767)

#  define PoseDebugPositionX_adt_LowerLimit (-2147483647 - 1)
#  define PoseDebugPositionX_adt_UpperLimit (2147483647)

#  define PoseDebugPositionXStd_adt_LowerLimit (0U)
#  define PoseDebugPositionXStd_adt_UpperLimit (1048575U)

#  define PoseDebugPositionY_adt_LowerLimit (-2147483647 - 1)
#  define PoseDebugPositionY_adt_UpperLimit (2147483647)

#  define PoseDebugPositionYStd_adt_LowerLimit (0U)
#  define PoseDebugPositionYStd_adt_UpperLimit (1048575U)

#  define PoseDebugSideSlipAngle_adt_LowerLimit (-8388608)
#  define PoseDebugSideSlipAngle_adt_UpperLimit (8388607)

#  define PoseDebugSpeed_adt_LowerLimit (-131072)
#  define PoseDebugSpeed_adt_UpperLimit (131071)

#  define PoseDebugSpeedStd_adt_LowerLimit (0U)
#  define PoseDebugSpeedStd_adt_UpperLimit (1048575U)

#  define PoseDebugYaw_adt_LowerLimit (-8388608)
#  define PoseDebugYaw_adt_UpperLimit (8388607)

#  define PoseDebugYawStd_adt_LowerLimit (0U)
#  define PoseDebugYawStd_adt_UpperLimit (1048575U)

#  define PosePitch_adt_LowerLimit (-8388608)
#  define PosePitch_adt_UpperLimit (8388607)

#  define PosePositionX_adt_LowerLimit (-2147483647 - 1)
#  define PosePositionX_adt_UpperLimit (2147483647)

#  define PosePositionY_adt_LowerLimit (-2147483647 - 1)
#  define PosePositionY_adt_UpperLimit (2147483647)

#  define PoseVelocityX_adt_LowerLimit (-131072)
#  define PoseVelocityX_adt_UpperLimit (131071)

#  define PoseVelocityY_adt_LowerLimit (-131072)
#  define PoseVelocityY_adt_UpperLimit (131071)

#  define PoseYaw_adt_LowerLimit (-8388608)
#  define PoseYaw_adt_UpperLimit (8388607)

#  define PoseYawRate_adt_LowerLimit (-8388608)
#  define PoseYawRate_adt_UpperLimit (8388607)

#  define PrimALatDataRawSafeNom_C_adt_LowerLimit (0U)
#  define PrimALatDataRawSafeNom_C_adt_UpperLimit (32767U)

#  define PrimALgtDataRawSafeNom_C_adt_LowerLimit (0U)
#  define PrimALgtDataRawSafeNom_C_adt_UpperLimit (32767U)

#  define PrimVehSpdGroupSafeNom_C_adt_LowerLimit (0U)
#  define PrimVehSpdGroupSafeNom_C_adt_UpperLimit (8191U)

#  define PtTqAtAxleAvlFrntMax_adt_LowerLimit (-4096)
#  define PtTqAtAxleAvlFrntMax_adt_UpperLimit (4095)

#  define PtTqAtAxleAvlFrntMaxLong_adt_LowerLimit (-4096)
#  define PtTqAtAxleAvlFrntMaxLong_adt_UpperLimit (4095)

#  define PtTqAtAxleAvlReMax_adt_LowerLimit (-4096)
#  define PtTqAtAxleAvlReMax_adt_UpperLimit (4095)

#  define PtTqAtAxleAvlReMaxLong_adt_LowerLimit (-4096)
#  define PtTqAtAxleAvlReMaxLong_adt_UpperLimit (4095)

#  define RawAccRequest_adt_LowerLimit (0U)
#  define RawAccRequest_adt_UpperLimit (511U)

#  define RawSteerAngle_adt_LowerLimit (0U)
#  define RawSteerAngle_adt_UpperLimit (15600U)

#  define RearObjectTTC_adt_LowerLimit (0U)
#  define RearObjectTTC_adt_UpperLimit (4095U)

#  define RiFr_AccOBJ_Ax_adt_LowerLimit (0U)
#  define RiFr_AccOBJ_Ax_adt_UpperLimit (1023U)

#  define RiFr_AccOBJ_Ay_adt_LowerLimit (0U)
#  define RiFr_AccOBJ_Ay_adt_UpperLimit (511U)

#  define RiFr_AccOBJ_Dx_adt_LowerLimit (0U)
#  define RiFr_AccOBJ_Dx_adt_UpperLimit (8191U)

#  define RiFr_AccOBJ_Dx_Vnce_adt_LowerLimit (0U)
#  define RiFr_AccOBJ_Dx_Vnce_adt_UpperLimit (1000U)

#  define RiFr_AccOBJ_Dy_adt_LowerLimit (0U)
#  define RiFr_AccOBJ_Dy_adt_UpperLimit (2047U)

#  define RiFr_AccOBJ_Dy_Vnce_adt_LowerLimit (0U)
#  define RiFr_AccOBJ_Dy_Vnce_adt_UpperLimit (1000U)

#  define RiFr_AccOBJ_ExistProb_adt_LowerLimit (0U)
#  define RiFr_AccOBJ_ExistProb_adt_UpperLimit (50U)

#  define RiFr_AccOBJ_HeadingAngle_adt_LowerLimit (0U)
#  define RiFr_AccOBJ_HeadingAngle_adt_UpperLimit (314U)

#  define RiFr_AccOBJ_Height_adt_LowerLimit (0U)
#  define RiFr_AccOBJ_Height_adt_UpperLimit (511U)

#  define RiFr_AccOBJ_Length_adt_LowerLimit (0U)
#  define RiFr_AccOBJ_Length_adt_UpperLimit (510U)

#  define RiFr_AccOBJ_ObstacleProb_adt_LowerLimit (0U)
#  define RiFr_AccOBJ_ObstacleProb_adt_UpperLimit (50U)

#  define RiFr_AccOBJ_Vx_adt_LowerLimit (0U)
#  define RiFr_AccOBJ_Vx_adt_UpperLimit (4095U)

#  define RiFr_AccOBJ_Vx_std_adt_LowerLimit (0U)
#  define RiFr_AccOBJ_Vx_std_adt_UpperLimit (4095U)

#  define RiFr_AccOBJ_Vy_adt_LowerLimit (0U)
#  define RiFr_AccOBJ_Vy_adt_UpperLimit (1023U)

#  define RiFr_AccOBJ_Vy_std_adt_LowerLimit (0U)
#  define RiFr_AccOBJ_Vy_std_adt_UpperLimit (1023U)

#  define RiFr_AccOBJ_Width_adt_LowerLimit (0U)
#  define RiFr_AccOBJ_Width_adt_UpperLimit (1200U)

#  define Ri_AccOBJ_Ax_adt_LowerLimit (0U)
#  define Ri_AccOBJ_Ax_adt_UpperLimit (1023U)

#  define Ri_AccOBJ_Ay_adt_LowerLimit (0U)
#  define Ri_AccOBJ_Ay_adt_UpperLimit (511U)

#  define Ri_AccOBJ_Dx_adt_LowerLimit (0U)
#  define Ri_AccOBJ_Dx_adt_UpperLimit (8191U)

#  define Ri_AccOBJ_Dx_Vnce_adt_LowerLimit (0U)
#  define Ri_AccOBJ_Dx_Vnce_adt_UpperLimit (1000U)

#  define Ri_AccOBJ_Dy_adt_LowerLimit (0U)
#  define Ri_AccOBJ_Dy_adt_UpperLimit (2047U)

#  define Ri_AccOBJ_Dy_Vnce_adt_LowerLimit (0U)
#  define Ri_AccOBJ_Dy_Vnce_adt_UpperLimit (1000U)

#  define Ri_AccOBJ_ExistProb_adt_LowerLimit (0U)
#  define Ri_AccOBJ_ExistProb_adt_UpperLimit (50U)

#  define Ri_AccOBJ_HeadingAngle_adt_LowerLimit (0U)
#  define Ri_AccOBJ_HeadingAngle_adt_UpperLimit (314U)

#  define Ri_AccOBJ_Height_adt_LowerLimit (0U)
#  define Ri_AccOBJ_Height_adt_UpperLimit (511U)

#  define Ri_AccOBJ_Length_adt_LowerLimit (0U)
#  define Ri_AccOBJ_Length_adt_UpperLimit (510U)

#  define Ri_AccOBJ_ObstacleProb_adt_LowerLimit (0U)
#  define Ri_AccOBJ_ObstacleProb_adt_UpperLimit (50U)

#  define Ri_AccOBJ_Vx_adt_LowerLimit (0U)
#  define Ri_AccOBJ_Vx_adt_UpperLimit (4095U)

#  define Ri_AccOBJ_Vx_std_adt_LowerLimit (0U)
#  define Ri_AccOBJ_Vx_std_adt_UpperLimit (4095U)

#  define Ri_AccOBJ_Vy_adt_LowerLimit (0U)
#  define Ri_AccOBJ_Vy_adt_UpperLimit (1023U)

#  define Ri_AccOBJ_Vy_std_adt_LowerLimit (0U)
#  define Ri_AccOBJ_Vy_std_adt_UpperLimit (1023U)

#  define Ri_AccOBJ_Width_adt_LowerLimit (0U)
#  define Ri_AccOBJ_Width_adt_UpperLimit (1200U)

#  define RightRoadEdgeLatDist_adt_LowerLimit (-16384)
#  define RightRoadEdgeLatDist_adt_UpperLimit (16383)

#  define RightRoadEdgeLength_adt_LowerLimit (0U)
#  define RightRoadEdgeLength_adt_UpperLimit (65535U)

#  define RoadLoadNomCoeffSts_adt_LowerLimit (0U)
#  define RoadLoadNomCoeffSts_adt_UpperLimit (1U)

#  define SafeDistance_adt_LowerLimit (0U)
#  define SafeDistance_adt_UpperLimit (255U)

#  define SteerAngle_adt_LowerLimit (0U)
#  define SteerAngle_adt_UpperLimit (15600U)

#  define SteerAngleByLQR_adt_LowerLimit (0U)
#  define SteerAngleByLQR_adt_UpperLimit (7800U)

#  define SteerAngleForSystemError_adt_LowerLimit (0U)
#  define SteerAngleForSystemError_adt_UpperLimit (7800U)

#  define StopLineLeftEdgeX_adt_LowerLimit (0U)
#  define StopLineLeftEdgeX_adt_UpperLimit (65535U)

#  define StopLineLeftEdgeY_adt_LowerLimit (-2048)
#  define StopLineLeftEdgeY_adt_UpperLimit (2047)

#  define StopLineRightEdgeX_adt_LowerLimit (0U)
#  define StopLineRightEdgeX_adt_UpperLimit (65535U)

#  define StopLineRightEdgeY_adt_LowerLimit (-2048)
#  define StopLineRightEdgeY_adt_UpperLimit (2047)

#  define TimeToCollison_adt_LowerLimit (0U)
#  define TimeToCollison_adt_UpperLimit (800U)

#  define TrajectoryCurvature_adt_LowerLimit (0U)
#  define TrajectoryCurvature_adt_UpperLimit (31250U)

#  define TrajectoryCurvatureChange_adt_LowerLimit (0U)
#  define TrajectoryCurvatureChange_adt_UpperLimit (960000U)

#  define TrajectoryHeadingAngle_adt_LowerLimit (0U)
#  define TrajectoryHeadingAngle_adt_UpperLimit (400U)

#  define TrajectoryLength_adt_LowerLimit (0U)
#  define TrajectoryLength_adt_UpperLimit (20000U)

#  define TrajectoryPosY0_adt_LowerLimit (0U)
#  define TrajectoryPosY0_adt_UpperLimit (6000U)

#  define TrajectoryPose00Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose00Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose00Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose00Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose00Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose00Heading_adt_UpperLimit (32767)

#  define TrajectoryPose00PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose00PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose00PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose00PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose00Speed_adt_LowerLimit (0U)
#  define TrajectoryPose00Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose00Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose00Steering_adt_UpperLimit (2047)

#  define TrajectoryPose01Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose01Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose01Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose01Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose01Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose01Heading_adt_UpperLimit (32767)

#  define TrajectoryPose01PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose01PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose01PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose01PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose01Speed_adt_LowerLimit (0U)
#  define TrajectoryPose01Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose01Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose01Steering_adt_UpperLimit (2047)

#  define TrajectoryPose02Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose02Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose02Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose02Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose02Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose02Heading_adt_UpperLimit (32767)

#  define TrajectoryPose02PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose02PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose02PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose02PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose02Speed_adt_LowerLimit (0U)
#  define TrajectoryPose02Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose02Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose02Steering_adt_UpperLimit (2047)

#  define TrajectoryPose03Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose03Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose03Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose03Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose03Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose03Heading_adt_UpperLimit (32767)

#  define TrajectoryPose03PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose03PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose03PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose03PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose03Speed_adt_LowerLimit (0U)
#  define TrajectoryPose03Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose03Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose03Steering_adt_UpperLimit (2047)

#  define TrajectoryPose04Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose04Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose04Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose04Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose04Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose04Heading_adt_UpperLimit (32767)

#  define TrajectoryPose04PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose04PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose04PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose04PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose04Speed_adt_LowerLimit (0U)
#  define TrajectoryPose04Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose04Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose04Steering_adt_UpperLimit (2047)

#  define TrajectoryPose05Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose05Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose05Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose05Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose05Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose05Heading_adt_UpperLimit (32767)

#  define TrajectoryPose05PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose05PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose05PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose05PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose05Speed_adt_LowerLimit (0U)
#  define TrajectoryPose05Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose05Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose05Steering_adt_UpperLimit (2047)

#  define TrajectoryPose06Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose06Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose06Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose06Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose06Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose06Heading_adt_UpperLimit (32767)

#  define TrajectoryPose06PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose06PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose06PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose06PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose06Speed_adt_LowerLimit (0U)
#  define TrajectoryPose06Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose06Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose06Steering_adt_UpperLimit (2047)

#  define TrajectoryPose07Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose07Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose07Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose07Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose07Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose07Heading_adt_UpperLimit (32767)

#  define TrajectoryPose07PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose07PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose07PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose07PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose07Speed_adt_LowerLimit (0U)
#  define TrajectoryPose07Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose07Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose07Steering_adt_UpperLimit (2047)

#  define TrajectoryPose08Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose08Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose08Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose08Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose08Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose08Heading_adt_UpperLimit (32767)

#  define TrajectoryPose08PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose08PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose08PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose08PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose08Speed_adt_LowerLimit (0U)
#  define TrajectoryPose08Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose08Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose08Steering_adt_UpperLimit (2047)

#  define TrajectoryPose09Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose09Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose09Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose09Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose09Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose09Heading_adt_UpperLimit (32767)

#  define TrajectoryPose09PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose09PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose09PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose09PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose09Speed_adt_LowerLimit (0U)
#  define TrajectoryPose09Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose09Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose09Steering_adt_UpperLimit (2047)

#  define TrajectoryPose10Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose10Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose10Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose10Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose10Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose10Heading_adt_UpperLimit (32767)

#  define TrajectoryPose10PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose10PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose10PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose10PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose10Speed_adt_LowerLimit (0U)
#  define TrajectoryPose10Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose10Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose10Steering_adt_UpperLimit (2047)

#  define TrajectoryPose11Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose11Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose11Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose11Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose11Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose11Heading_adt_UpperLimit (32767)

#  define TrajectoryPose11PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose11PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose11PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose11PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose11Speed_adt_LowerLimit (0U)
#  define TrajectoryPose11Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose11Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose11Steering_adt_UpperLimit (2047)

#  define TrajectoryPose12Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose12Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose12Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose12Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose12Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose12Heading_adt_UpperLimit (32767)

#  define TrajectoryPose12PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose12PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose12PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose12PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose12Speed_adt_LowerLimit (0U)
#  define TrajectoryPose12Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose12Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose12Steering_adt_UpperLimit (2047)

#  define TrajectoryPose13Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose13Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose13Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose13Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose13Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose13Heading_adt_UpperLimit (32767)

#  define TrajectoryPose13PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose13PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose13PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose13PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose13Speed_adt_LowerLimit (0U)
#  define TrajectoryPose13Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose13Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose13Steering_adt_UpperLimit (2047)

#  define TrajectoryPose14Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose14Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose14Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose14Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose14Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose14Heading_adt_UpperLimit (32767)

#  define TrajectoryPose14PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose14PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose14PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose14PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose14Speed_adt_LowerLimit (0U)
#  define TrajectoryPose14Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose14Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose14Steering_adt_UpperLimit (2047)

#  define TrajectoryPose15Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose15Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose15Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose15Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose15Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose15Heading_adt_UpperLimit (32767)

#  define TrajectoryPose15PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose15PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose15PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose15PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose15Speed_adt_LowerLimit (0U)
#  define TrajectoryPose15Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose15Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose15Steering_adt_UpperLimit (2047)

#  define TrajectoryPose16Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose16Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose16Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose16Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose16Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose16Heading_adt_UpperLimit (32767)

#  define TrajectoryPose16PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose16PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose16PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose16PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose16Speed_adt_LowerLimit (0U)
#  define TrajectoryPose16Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose16Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose16Steering_adt_UpperLimit (2047)

#  define TrajectoryPose17Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose17Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose17Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose17Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose17Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose17Heading_adt_UpperLimit (32767)

#  define TrajectoryPose17PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose17PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose17PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose17PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose17Speed_adt_LowerLimit (0U)
#  define TrajectoryPose17Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose17Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose17Steering_adt_UpperLimit (2047)

#  define TrajectoryPose18Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose18Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose18Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose18Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose18Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose18Heading_adt_UpperLimit (32767)

#  define TrajectoryPose18PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose18PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose18PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose18PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose18Speed_adt_LowerLimit (0U)
#  define TrajectoryPose18Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose18Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose18Steering_adt_UpperLimit (2047)

#  define TrajectoryPose19Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose19Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose19Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose19Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose19Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose19Heading_adt_UpperLimit (32767)

#  define TrajectoryPose19PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose19PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose19PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose19PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose19Speed_adt_LowerLimit (0U)
#  define TrajectoryPose19Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose19Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose19Steering_adt_UpperLimit (2047)

#  define TrajectoryPose20Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose20Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose20Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose20Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose20Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose20Heading_adt_UpperLimit (32767)

#  define TrajectoryPose20PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose20PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose20PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose20PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose20Speed_adt_LowerLimit (0U)
#  define TrajectoryPose20Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose20Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose20Steering_adt_UpperLimit (2047)

#  define TrajectoryPose21Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose21Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose21Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose21Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose21Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose21Heading_adt_UpperLimit (32767)

#  define TrajectoryPose21PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose21PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose21PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose21PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose21Speed_adt_LowerLimit (0U)
#  define TrajectoryPose21Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose21Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose21Steering_adt_UpperLimit (2047)

#  define TrajectoryPose22Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose22Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose22Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose22Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose22Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose22Heading_adt_UpperLimit (32767)

#  define TrajectoryPose22PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose22PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose22PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose22PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose22Speed_adt_LowerLimit (0U)
#  define TrajectoryPose22Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose22Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose22Steering_adt_UpperLimit (2047)

#  define TrajectoryPose23Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose23Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose23Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose23Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose23Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose23Heading_adt_UpperLimit (32767)

#  define TrajectoryPose23PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose23PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose23PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose23PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose23Speed_adt_LowerLimit (0U)
#  define TrajectoryPose23Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose23Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose23Steering_adt_UpperLimit (2047)

#  define TrajectoryPose24Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose24Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose24Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose24Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose24Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose24Heading_adt_UpperLimit (32767)

#  define TrajectoryPose24PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose24PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose24PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose24PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose24Speed_adt_LowerLimit (0U)
#  define TrajectoryPose24Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose24Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose24Steering_adt_UpperLimit (2047)

#  define TrajectoryPose25Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose25Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose25Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose25Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose25Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose25Heading_adt_UpperLimit (32767)

#  define TrajectoryPose25PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose25PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose25PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose25PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose25Speed_adt_LowerLimit (0U)
#  define TrajectoryPose25Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose25Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose25Steering_adt_UpperLimit (2047)

#  define TrajectoryPose26Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose26Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose26Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose26Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose26Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose26Heading_adt_UpperLimit (32767)

#  define TrajectoryPose26PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose26PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose26PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose26PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose26Speed_adt_LowerLimit (0U)
#  define TrajectoryPose26Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose26Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose26Steering_adt_UpperLimit (2047)

#  define TrajectoryPose27Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose27Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose27Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose27Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose27Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose27Heading_adt_UpperLimit (32767)

#  define TrajectoryPose27PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose27PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose27PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose27PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose27Speed_adt_LowerLimit (0U)
#  define TrajectoryPose27Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose27Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose27Steering_adt_UpperLimit (2047)

#  define TrajectoryPose28Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose28Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose28Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose28Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose28Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose28Heading_adt_UpperLimit (32767)

#  define TrajectoryPose28PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose28PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose28PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose28PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose28Speed_adt_LowerLimit (0U)
#  define TrajectoryPose28Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose28Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose28Steering_adt_UpperLimit (2047)

#  define TrajectoryPose29Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose29Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose29Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose29Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose29Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose29Heading_adt_UpperLimit (32767)

#  define TrajectoryPose29PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose29PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose29PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose29PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose29Speed_adt_LowerLimit (0U)
#  define TrajectoryPose29Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose29Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose29Steering_adt_UpperLimit (2047)

#  define TrajectoryPose30Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose30Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose30Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose30Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose30Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose30Heading_adt_UpperLimit (32767)

#  define TrajectoryPose30PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose30PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose30PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose30PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose30Speed_adt_LowerLimit (0U)
#  define TrajectoryPose30Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose30Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose30Steering_adt_UpperLimit (2047)

#  define TrajectoryPose31Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose31Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose31Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose31Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose31Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose31Heading_adt_UpperLimit (32767)

#  define TrajectoryPose31PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose31PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose31PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose31PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose31Speed_adt_LowerLimit (0U)
#  define TrajectoryPose31Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose31Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose31Steering_adt_UpperLimit (2047)

#  define TrajectoryPose32Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose32Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose32Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose32Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose32Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose32Heading_adt_UpperLimit (32767)

#  define TrajectoryPose32PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose32PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose32PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose32PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose32Speed_adt_LowerLimit (0U)
#  define TrajectoryPose32Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose32Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose32Steering_adt_UpperLimit (2047)

#  define TrajectoryPose33Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose33Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose33Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose33Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose33Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose33Heading_adt_UpperLimit (32767)

#  define TrajectoryPose33PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose33PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose33PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose33PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose33Speed_adt_LowerLimit (0U)
#  define TrajectoryPose33Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose33Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose33Steering_adt_UpperLimit (2047)

#  define TrajectoryPose34Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose34Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose34Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose34Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose34Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose34Heading_adt_UpperLimit (32767)

#  define TrajectoryPose34PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose34PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose34PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose34PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose34Speed_adt_LowerLimit (0U)
#  define TrajectoryPose34Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose34Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose34Steering_adt_UpperLimit (2047)

#  define TrajectoryPose35Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose35Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose35Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose35Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose35Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose35Heading_adt_UpperLimit (32767)

#  define TrajectoryPose35PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose35PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose35PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose35PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose35Speed_adt_LowerLimit (0U)
#  define TrajectoryPose35Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose35Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose35Steering_adt_UpperLimit (2047)

#  define TrajectoryPose36Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose36Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose36Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose36Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose36Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose36Heading_adt_UpperLimit (32767)

#  define TrajectoryPose36PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose36PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose36PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose36PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose36Speed_adt_LowerLimit (0U)
#  define TrajectoryPose36Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose36Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose36Steering_adt_UpperLimit (2047)

#  define TrajectoryPose37Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose37Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose37Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose37Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose37Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose37Heading_adt_UpperLimit (32767)

#  define TrajectoryPose37PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose37PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose37PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose37PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose37Speed_adt_LowerLimit (0U)
#  define TrajectoryPose37Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose37Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose37Steering_adt_UpperLimit (2047)

#  define TrajectoryPose38Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose38Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose38Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose38Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose38Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose38Heading_adt_UpperLimit (32767)

#  define TrajectoryPose38PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose38PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose38PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose38PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose38Speed_adt_LowerLimit (0U)
#  define TrajectoryPose38Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose38Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose38Steering_adt_UpperLimit (2047)

#  define TrajectoryPose39Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose39Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose39Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose39Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose39Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose39Heading_adt_UpperLimit (32767)

#  define TrajectoryPose39PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose39PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose39PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose39PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose39Speed_adt_LowerLimit (0U)
#  define TrajectoryPose39Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose39Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose39Steering_adt_UpperLimit (2047)

#  define TrajectoryPose40Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose40Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose40Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose40Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose40Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose40Heading_adt_UpperLimit (32767)

#  define TrajectoryPose40PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose40PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose40PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose40PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose40Speed_adt_LowerLimit (0U)
#  define TrajectoryPose40Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose40Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose40Steering_adt_UpperLimit (2047)

#  define TrajectoryPose41Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose41Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose41Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose41Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose41Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose41Heading_adt_UpperLimit (32767)

#  define TrajectoryPose41PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose41PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose41PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose41PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose41Speed_adt_LowerLimit (0U)
#  define TrajectoryPose41Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose41Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose41Steering_adt_UpperLimit (2047)

#  define TrajectoryPose42Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose42Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose42Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose42Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose42Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose42Heading_adt_UpperLimit (32767)

#  define TrajectoryPose42PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose42PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose42PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose42PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose42Speed_adt_LowerLimit (0U)
#  define TrajectoryPose42Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose42Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose42Steering_adt_UpperLimit (2047)

#  define TrajectoryPose43Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose43Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose43Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose43Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose43Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose43Heading_adt_UpperLimit (32767)

#  define TrajectoryPose43PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose43PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose43PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose43PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose43Speed_adt_LowerLimit (0U)
#  define TrajectoryPose43Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose43Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose43Steering_adt_UpperLimit (2047)

#  define TrajectoryPose44Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose44Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose44Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose44Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose44Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose44Heading_adt_UpperLimit (32767)

#  define TrajectoryPose44PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose44PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose44PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose44PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose44Speed_adt_LowerLimit (0U)
#  define TrajectoryPose44Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose44Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose44Steering_adt_UpperLimit (2047)

#  define TrajectoryPose45Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose45Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose45Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose45Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose45Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose45Heading_adt_UpperLimit (32767)

#  define TrajectoryPose45PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose45PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose45PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose45PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose45Speed_adt_LowerLimit (0U)
#  define TrajectoryPose45Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose45Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose45Steering_adt_UpperLimit (2047)

#  define TrajectoryPose46Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose46Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose46Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose46Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose46Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose46Heading_adt_UpperLimit (32767)

#  define TrajectoryPose46PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose46PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose46PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose46PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose46Speed_adt_LowerLimit (0U)
#  define TrajectoryPose46Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose46Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose46Steering_adt_UpperLimit (2047)

#  define TrajectoryPose47Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose47Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose47Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose47Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose47Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose47Heading_adt_UpperLimit (32767)

#  define TrajectoryPose47PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose47PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose47PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose47PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose47Speed_adt_LowerLimit (0U)
#  define TrajectoryPose47Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose47Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose47Steering_adt_UpperLimit (2047)

#  define TrajectoryPose48Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose48Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose48Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose48Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose48Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose48Heading_adt_UpperLimit (32767)

#  define TrajectoryPose48PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose48PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose48PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose48PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose48Speed_adt_LowerLimit (0U)
#  define TrajectoryPose48Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose48Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose48Steering_adt_UpperLimit (2047)

#  define TrajectoryPose49Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose49Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose49Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose49Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose49Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose49Heading_adt_UpperLimit (32767)

#  define TrajectoryPose49PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose49PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose49PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose49PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose49Speed_adt_LowerLimit (0U)
#  define TrajectoryPose49Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose49Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose49Steering_adt_UpperLimit (2047)

#  define TrajectoryPose50Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose50Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose50Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose50Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose50Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose50Heading_adt_UpperLimit (32767)

#  define TrajectoryPose50PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose50PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose50PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose50PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose50Speed_adt_LowerLimit (0U)
#  define TrajectoryPose50Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose50Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose50Steering_adt_UpperLimit (2047)

#  define TrajectoryPose51Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose51Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose51Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose51Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose51Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose51Heading_adt_UpperLimit (32767)

#  define TrajectoryPose51PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose51PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose51PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose51PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose51Speed_adt_LowerLimit (0U)
#  define TrajectoryPose51Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose51Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose51Steering_adt_UpperLimit (2047)

#  define TrajectoryPose52Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose52Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose52Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose52Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose52Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose52Heading_adt_UpperLimit (32767)

#  define TrajectoryPose52PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose52PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose52PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose52PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose52Speed_adt_LowerLimit (0U)
#  define TrajectoryPose52Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose52Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose52Steering_adt_UpperLimit (2047)

#  define TrajectoryPose53Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose53Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose53Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose53Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose53Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose53Heading_adt_UpperLimit (32767)

#  define TrajectoryPose53PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose53PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose53PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose53PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose53Speed_adt_LowerLimit (0U)
#  define TrajectoryPose53Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose53Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose53Steering_adt_UpperLimit (2047)

#  define TrajectoryPose54Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose54Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose54Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose54Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose54Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose54Heading_adt_UpperLimit (32767)

#  define TrajectoryPose54PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose54PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose54PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose54PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose54Speed_adt_LowerLimit (0U)
#  define TrajectoryPose54Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose54Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose54Steering_adt_UpperLimit (2047)

#  define TrajectoryPose55Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose55Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose55Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose55Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose55Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose55Heading_adt_UpperLimit (32767)

#  define TrajectoryPose55PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose55PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose55PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose55PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose55Speed_adt_LowerLimit (0U)
#  define TrajectoryPose55Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose55Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose55Steering_adt_UpperLimit (2047)

#  define TrajectoryPose56Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose56Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose56Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose56Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose56Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose56Heading_adt_UpperLimit (32767)

#  define TrajectoryPose56PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose56PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose56PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose56PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose56Speed_adt_LowerLimit (0U)
#  define TrajectoryPose56Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose56Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose56Steering_adt_UpperLimit (2047)

#  define TrajectoryPose57Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose57Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose57Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose57Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose57Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose57Heading_adt_UpperLimit (32767)

#  define TrajectoryPose57PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose57PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose57PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose57PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose57Speed_adt_LowerLimit (0U)
#  define TrajectoryPose57Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose57Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose57Steering_adt_UpperLimit (2047)

#  define TrajectoryPose58Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose58Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose58Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose58Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose58Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose58Heading_adt_UpperLimit (32767)

#  define TrajectoryPose58PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose58PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose58PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose58PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose58Speed_adt_LowerLimit (0U)
#  define TrajectoryPose58Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose58Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose58Steering_adt_UpperLimit (2047)

#  define TrajectoryPose59Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose59Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose59Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose59Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose59Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose59Heading_adt_UpperLimit (32767)

#  define TrajectoryPose59PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose59PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose59PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose59PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose59Speed_adt_LowerLimit (0U)
#  define TrajectoryPose59Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose59Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose59Steering_adt_UpperLimit (2047)

#  define TrajectoryPose60Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose60Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose60Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose60Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose60Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose60Heading_adt_UpperLimit (32767)

#  define TrajectoryPose60PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose60PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose60PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose60PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose60Speed_adt_LowerLimit (0U)
#  define TrajectoryPose60Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose60Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose60Steering_adt_UpperLimit (2047)

#  define TrajectoryPose61Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose61Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose61Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose61Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose61Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose61Heading_adt_UpperLimit (32767)

#  define TrajectoryPose61PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose61PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose61PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose61PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose61Speed_adt_LowerLimit (0U)
#  define TrajectoryPose61Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose61Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose61Steering_adt_UpperLimit (2047)

#  define TrajectoryPose62Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose62Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose62Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose62Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose62Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose62Heading_adt_UpperLimit (32767)

#  define TrajectoryPose62PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose62PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose62PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose62PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose62Speed_adt_LowerLimit (0U)
#  define TrajectoryPose62Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose62Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose62Steering_adt_UpperLimit (2047)

#  define TrajectoryPose63Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose63Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose63Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose63Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose63Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose63Heading_adt_UpperLimit (32767)

#  define TrajectoryPose63PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose63PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose63PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose63PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose63Speed_adt_LowerLimit (0U)
#  define TrajectoryPose63Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose63Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose63Steering_adt_UpperLimit (2047)

#  define TrajectoryPose64Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose64Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose64Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose64Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose64Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose64Heading_adt_UpperLimit (32767)

#  define TrajectoryPose64PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose64PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose64PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose64PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose64Speed_adt_LowerLimit (0U)
#  define TrajectoryPose64Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose64Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose64Steering_adt_UpperLimit (2047)

#  define TrajectoryPose65Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose65Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose65Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose65Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose65Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose65Heading_adt_UpperLimit (32767)

#  define TrajectoryPose65PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose65PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose65PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose65PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose65Speed_adt_LowerLimit (0U)
#  define TrajectoryPose65Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose65Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose65Steering_adt_UpperLimit (2047)

#  define TrajectoryPose66Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose66Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose66Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose66Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose66Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose66Heading_adt_UpperLimit (32767)

#  define TrajectoryPose66PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose66PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose66PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose66PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose66Speed_adt_LowerLimit (0U)
#  define TrajectoryPose66Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose66Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose66Steering_adt_UpperLimit (2047)

#  define TrajectoryPose67Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose67Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose67Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose67Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose67Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose67Heading_adt_UpperLimit (32767)

#  define TrajectoryPose67PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose67PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose67PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose67PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose67Speed_adt_LowerLimit (0U)
#  define TrajectoryPose67Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose67Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose67Steering_adt_UpperLimit (2047)

#  define TrajectoryPose68Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose68Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose68Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose68Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose68Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose68Heading_adt_UpperLimit (32767)

#  define TrajectoryPose68PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose68PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose68PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose68PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose68Speed_adt_LowerLimit (0U)
#  define TrajectoryPose68Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose68Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose68Steering_adt_UpperLimit (2047)

#  define TrajectoryPose69Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose69Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose69Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose69Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose69Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose69Heading_adt_UpperLimit (32767)

#  define TrajectoryPose69PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose69PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose69PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose69PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose69Speed_adt_LowerLimit (0U)
#  define TrajectoryPose69Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose69Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose69Steering_adt_UpperLimit (2047)

#  define TrajectoryPose70Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose70Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose70Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose70Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose70Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose70Heading_adt_UpperLimit (32767)

#  define TrajectoryPose70PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose70PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose70PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose70PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose70Speed_adt_LowerLimit (0U)
#  define TrajectoryPose70Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose70Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose70Steering_adt_UpperLimit (2047)

#  define TrajectoryPose71Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose71Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose71Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose71Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose71Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose71Heading_adt_UpperLimit (32767)

#  define TrajectoryPose71PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose71PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose71PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose71PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose71Speed_adt_LowerLimit (0U)
#  define TrajectoryPose71Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose71Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose71Steering_adt_UpperLimit (2047)

#  define TrajectoryPose72Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose72Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose72Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose72Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose72Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose72Heading_adt_UpperLimit (32767)

#  define TrajectoryPose72PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose72PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose72PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose72PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose72Speed_adt_LowerLimit (0U)
#  define TrajectoryPose72Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose72Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose72Steering_adt_UpperLimit (2047)

#  define TrajectoryPose73Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose73Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose73Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose73Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose73Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose73Heading_adt_UpperLimit (32767)

#  define TrajectoryPose73PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose73PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose73PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose73PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose73Speed_adt_LowerLimit (0U)
#  define TrajectoryPose73Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose73Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose73Steering_adt_UpperLimit (2047)

#  define TrajectoryPose74Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose74Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose74Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose74Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose74Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose74Heading_adt_UpperLimit (32767)

#  define TrajectoryPose74PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose74PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose74PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose74PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose74Speed_adt_LowerLimit (0U)
#  define TrajectoryPose74Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose74Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose74Steering_adt_UpperLimit (2047)

#  define TrajectoryPose75Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose75Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose75Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose75Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose75Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose75Heading_adt_UpperLimit (32767)

#  define TrajectoryPose75PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose75PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose75PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose75PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose75Speed_adt_LowerLimit (0U)
#  define TrajectoryPose75Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose75Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose75Steering_adt_UpperLimit (2047)

#  define TrajectoryPose76Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose76Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose76Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose76Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose76Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose76Heading_adt_UpperLimit (32767)

#  define TrajectoryPose76PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose76PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose76PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose76PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose76Speed_adt_LowerLimit (0U)
#  define TrajectoryPose76Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose76Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose76Steering_adt_UpperLimit (2047)

#  define TrajectoryPose77Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose77Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose77Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose77Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose77Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose77Heading_adt_UpperLimit (32767)

#  define TrajectoryPose77PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose77PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose77PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose77PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose77Speed_adt_LowerLimit (0U)
#  define TrajectoryPose77Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose77Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose77Steering_adt_UpperLimit (2047)

#  define TrajectoryPose78Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose78Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose78Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose78Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose78Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose78Heading_adt_UpperLimit (32767)

#  define TrajectoryPose78PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose78PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose78PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose78PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose78Speed_adt_LowerLimit (0U)
#  define TrajectoryPose78Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose78Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose78Steering_adt_UpperLimit (2047)

#  define TrajectoryPose79Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose79Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose79Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose79Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose79Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose79Heading_adt_UpperLimit (32767)

#  define TrajectoryPose79PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose79PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose79PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose79PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose79Speed_adt_LowerLimit (0U)
#  define TrajectoryPose79Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose79Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose79Steering_adt_UpperLimit (2047)

#  define TrajectoryPose80Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose80Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose80Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose80Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose80Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose80Heading_adt_UpperLimit (32767)

#  define TrajectoryPose80PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose80PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose80PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose80PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose80Speed_adt_LowerLimit (0U)
#  define TrajectoryPose80Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose80Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose80Steering_adt_UpperLimit (2047)

#  define TrajectoryPose81Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose81Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose81Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose81Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose81Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose81Heading_adt_UpperLimit (32767)

#  define TrajectoryPose81PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose81PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose81PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose81PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose81Speed_adt_LowerLimit (0U)
#  define TrajectoryPose81Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose81Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose81Steering_adt_UpperLimit (2047)

#  define TrajectoryPose82Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose82Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose82Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose82Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose82Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose82Heading_adt_UpperLimit (32767)

#  define TrajectoryPose82PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose82PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose82PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose82PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose82Speed_adt_LowerLimit (0U)
#  define TrajectoryPose82Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose82Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose82Steering_adt_UpperLimit (2047)

#  define TrajectoryPose83Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose83Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose83Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose83Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose83Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose83Heading_adt_UpperLimit (32767)

#  define TrajectoryPose83PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose83PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose83PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose83PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose83Speed_adt_LowerLimit (0U)
#  define TrajectoryPose83Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose83Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose83Steering_adt_UpperLimit (2047)

#  define TrajectoryPose84Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose84Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose84Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose84Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose84Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose84Heading_adt_UpperLimit (32767)

#  define TrajectoryPose84PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose84PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose84PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose84PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose84Speed_adt_LowerLimit (0U)
#  define TrajectoryPose84Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose84Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose84Steering_adt_UpperLimit (2047)

#  define TrajectoryPose85Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose85Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose85Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose85Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose85Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose85Heading_adt_UpperLimit (32767)

#  define TrajectoryPose85PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose85PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose85PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose85PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose85Speed_adt_LowerLimit (0U)
#  define TrajectoryPose85Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose85Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose85Steering_adt_UpperLimit (2047)

#  define TrajectoryPose86Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose86Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose86Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose86Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose86Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose86Heading_adt_UpperLimit (32767)

#  define TrajectoryPose86PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose86PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose86PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose86PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose86Speed_adt_LowerLimit (0U)
#  define TrajectoryPose86Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose86Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose86Steering_adt_UpperLimit (2047)

#  define TrajectoryPose87Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose87Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose87Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose87Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose87Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose87Heading_adt_UpperLimit (32767)

#  define TrajectoryPose87PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose87PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose87PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose87PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose87Speed_adt_LowerLimit (0U)
#  define TrajectoryPose87Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose87Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose87Steering_adt_UpperLimit (2047)

#  define TrajectoryPose88Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose88Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose88Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose88Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose88Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose88Heading_adt_UpperLimit (32767)

#  define TrajectoryPose88PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose88PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose88PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose88PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose88Speed_adt_LowerLimit (0U)
#  define TrajectoryPose88Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose88Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose88Steering_adt_UpperLimit (2047)

#  define TrajectoryPose89Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose89Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose89Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose89Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose89Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose89Heading_adt_UpperLimit (32767)

#  define TrajectoryPose89PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose89PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose89PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose89PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose89Speed_adt_LowerLimit (0U)
#  define TrajectoryPose89Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose89Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose89Steering_adt_UpperLimit (2047)

#  define TrajectoryPose90Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose90Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose90Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose90Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose90Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose90Heading_adt_UpperLimit (32767)

#  define TrajectoryPose90PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose90PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose90PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose90PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose90Speed_adt_LowerLimit (0U)
#  define TrajectoryPose90Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose90Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose90Steering_adt_UpperLimit (2047)

#  define TrajectoryPose91Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose91Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose91Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose91Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose91Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose91Heading_adt_UpperLimit (32767)

#  define TrajectoryPose91PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose91PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose91PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose91PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose91Speed_adt_LowerLimit (0U)
#  define TrajectoryPose91Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose91Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose91Steering_adt_UpperLimit (2047)

#  define TrajectoryPose92Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose92Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose92Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose92Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose92Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose92Heading_adt_UpperLimit (32767)

#  define TrajectoryPose92PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose92PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose92PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose92PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose92Speed_adt_LowerLimit (0U)
#  define TrajectoryPose92Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose92Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose92Steering_adt_UpperLimit (2047)

#  define TrajectoryPose93Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose93Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose93Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose93Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose93Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose93Heading_adt_UpperLimit (32767)

#  define TrajectoryPose93PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose93PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose93PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose93PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose93Speed_adt_LowerLimit (0U)
#  define TrajectoryPose93Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose93Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose93Steering_adt_UpperLimit (2047)

#  define TrajectoryPose94Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose94Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose94Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose94Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose94Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose94Heading_adt_UpperLimit (32767)

#  define TrajectoryPose94PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose94PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose94PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose94PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose94Speed_adt_LowerLimit (0U)
#  define TrajectoryPose94Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose94Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose94Steering_adt_UpperLimit (2047)

#  define TrajectoryPose95Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose95Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose95Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose95Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose95Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose95Heading_adt_UpperLimit (32767)

#  define TrajectoryPose95PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose95PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose95PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose95PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose95Speed_adt_LowerLimit (0U)
#  define TrajectoryPose95Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose95Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose95Steering_adt_UpperLimit (2047)

#  define TrajectoryPose96Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose96Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose96Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose96Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose96Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose96Heading_adt_UpperLimit (32767)

#  define TrajectoryPose96PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose96PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose96PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose96PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose96Speed_adt_LowerLimit (0U)
#  define TrajectoryPose96Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose96Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose96Steering_adt_UpperLimit (2047)

#  define TrajectoryPose97Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose97Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose97Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose97Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose97Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose97Heading_adt_UpperLimit (32767)

#  define TrajectoryPose97PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose97PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose97PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose97PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose97Speed_adt_LowerLimit (0U)
#  define TrajectoryPose97Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose97Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose97Steering_adt_UpperLimit (2047)

#  define TrajectoryPose98Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose98Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose98Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose98Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose98Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose98Heading_adt_UpperLimit (32767)

#  define TrajectoryPose98PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose98PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose98PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose98PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose98Speed_adt_LowerLimit (0U)
#  define TrajectoryPose98Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose98Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose98Steering_adt_UpperLimit (2047)

#  define TrajectoryPose99Acceleration_adt_LowerLimit (-2048)
#  define TrajectoryPose99Acceleration_adt_UpperLimit (2047)

#  define TrajectoryPose99Curvature_adt_LowerLimit (0U)
#  define TrajectoryPose99Curvature_adt_UpperLimit (4095U)

#  define TrajectoryPose99Heading_adt_LowerLimit (-32768)
#  define TrajectoryPose99Heading_adt_UpperLimit (32767)

#  define TrajectoryPose99PositionX_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose99PositionX_adt_UpperLimit (2147483647)

#  define TrajectoryPose99PositionY_adt_LowerLimit (-2147483647 - 1)
#  define TrajectoryPose99PositionY_adt_UpperLimit (2147483647)

#  define TrajectoryPose99Speed_adt_LowerLimit (0U)
#  define TrajectoryPose99Speed_adt_UpperLimit (4095U)

#  define TrajectoryPose99Steering_adt_LowerLimit (-2048)
#  define TrajectoryPose99Steering_adt_UpperLimit (2047)

#  define UDcDcAvlLoSideExt_adt_LowerLimit (0U)
#  define UDcDcAvlLoSideExt_adt_UpperLimit (250U)

#  define VehMid3SsmCounter0Timer_adt_LowerLimit (0U)
#  define VehMid3SsmCounter0Timer_adt_UpperLimit (4095U)

#  define VehMid3SsmCounter1Timer_adt_LowerLimit (0U)
#  define VehMid3SsmCounter1Timer_adt_UpperLimit (4095U)

#  define VehMid3VcuCounter0Timer_adt_LowerLimit (0U)
#  define VehMid3VcuCounter0Timer_adt_UpperLimit (4095U)

#  define VehMid3VcuCounter1Timer_adt_LowerLimit (0U)
#  define VehMid3VcuCounter1Timer_adt_UpperLimit (4095U)

#  define VehMid5SsmCounter0Timer_adt_LowerLimit (0U)
#  define VehMid5SsmCounter0Timer_adt_UpperLimit (4095U)

#  define VehMid5SsmCounter1Timer_adt_LowerLimit (0U)
#  define VehMid5SsmCounter1Timer_adt_UpperLimit (4095U)

#  define VehMid6SsmCounter0Timer_adt_LowerLimit (0U)
#  define VehMid6SsmCounter0Timer_adt_UpperLimit (4095U)

#  define VehMid6SsmCounter1Timer_adt_LowerLimit (0U)
#  define VehMid6SsmCounter1Timer_adt_UpperLimit (4095U)

#  define YRS_LateralAcce_adt_LowerLimit (0U)
#  define YRS_LateralAcce_adt_UpperLimit (4000U)

#  define YRS_LongitAcce_adt_LowerLimit (0U)
#  define YRS_LongitAcce_adt_UpperLimit (4000U)

#  define YRS_YawRate_adt_LowerLimit (0U)
#  define YRS_YawRate_adt_UpperLimit (36000U)

#  define YawRate1_C_adt_LowerLimit (-24576)
#  define YawRate1_C_adt_UpperLimit (24576)

#  define PrimAxleSlipStsAndRelAgRelPitch_adt_LowerLimit (-32768)
#  define PrimAxleSlipStsAndRelAgRelPitch_adt_UpperLimit (32766)

#  define PrimAxleSlipStsAndRelAgRelRoll_adt_LowerLimit (-32768)
#  define PrimAxleSlipStsAndRelAgRelRoll_adt_UpperLimit (32766)

#  define PrimVLatSafeMax_adt_LowerLimit (-16384)
#  define PrimVLatSafeMax_adt_UpperLimit (16383)

#  define PrimVLatSafeMin_adt_LowerLimit (-16384)
#  define PrimVLatSafeMin_adt_UpperLimit (16383)

#  define PrimVLatSafeNom_adt_LowerLimit (-16384)
#  define PrimVLatSafeNom_adt_UpperLimit (16383)

#  define PrimWhlAgSpdFrntSafeLe_adt_LowerLimit (-32640)
#  define PrimWhlAgSpdFrntSafeLe_adt_UpperLimit (32640)

#  define PrimWhlAgSpdFrntSafeRi_adt_LowerLimit (-32640)
#  define PrimWhlAgSpdFrntSafeRi_adt_UpperLimit (32640)

#  define PrimWhlAgSpdReSafeLe_adt_LowerLimit (-32640)
#  define PrimWhlAgSpdReSafeLe_adt_UpperLimit (32640)

#  define PrimWhlAgSpdReSafeRi_adt_LowerLimit (-32640)
#  define PrimWhlAgSpdReSafeRi_adt_UpperLimit (32640)

#  define PrimYawRateSafeMax_adt_LowerLimit (-16384)
#  define PrimYawRateSafeMax_adt_UpperLimit (16383)

#  define PrimYawRateSafeMin_adt_LowerLimit (-16384)
#  define PrimYawRateSafeMin_adt_UpperLimit (16383)

#  define PrimYawRateSafeNom_adt_LowerLimit (-16384)
#  define PrimYawRateSafeNom_adt_UpperLimit (16383)

#  define RoadLoadNomCoeff1_adt_LowerLimit (-510)
#  define RoadLoadNomCoeff1_adt_UpperLimit (511)

#  define RollRate1_adt_LowerLimit (-24576)
#  define RollRate1_adt_UpperLimit (24576)

#  define SecMaxALatEstimdGroupAcceleration_adt_LowerLimit (-16352)
#  define SecMaxALatEstimdGroupAcceleration_adt_UpperLimit (16353)

#  define SecMaxALatEstimdGroupJerk_adt_LowerLimit (-16384)
#  define SecMaxALatEstimdGroupJerk_adt_UpperLimit (16383)

#  define SecPoseMonSafeLatErr_adt_LowerLimit (-512)
#  define SecPoseMonSafeLatErr_adt_UpperLimit (511)

#  define SecPoseMonSafeLgtErr_adt_LowerLimit (-1024)
#  define SecPoseMonSafeLgtErr_adt_UpperLimit (1023)

#  define SecSteerMotTqSteerMotTq_adt_LowerLimit (-7680)
#  define SecSteerMotTqSteerMotTq_adt_UpperLimit (7680)

#  define SteerWhlAgSafe_adt_LowerLimit (-14848)
#  define SteerWhlAgSafe_adt_UpperLimit (14848)

#  define SteerWhlAgSpdSafe_adt_LowerLimit (-6400)
#  define SteerWhlAgSpdSafe_adt_UpperLimit (6400)

#  define SteerWhlTq_adt_LowerLimit (-7680)
#  define SteerWhlTq_adt_UpperLimit (7680)

#  define YawRate1_adt_LowerLimit (-24576)
#  define YawRate1_adt_UpperLimit (24576)

#  define AdPrimPosePosX_A_adt_LowerLimit (-2147483647 - 1)
#  define AdPrimPosePosX_A_adt_UpperLimit (2147483647)

#  define AdPrimPosePosY_A_adt_LowerLimit (-2147483647 - 1)
#  define AdPrimPosePosY_A_adt_UpperLimit (2147483647)

#  define SecPoseMonSafeYawErr_adt_LowerLimit (-128)
#  define SecPoseMonSafeYawErr_adt_UpperLimit (127)

#  define AdNomALgtReqGroupSafeALgtNomReqMax_A_adt_LowerLimit (0U)
#  define AdNomALgtReqGroupSafeALgtNomReqMax_A_adt_UpperLimit (3840U)

#  define AdNomALgtReqGroupSafeALgtNomReqMax_adt_LowerLimit (0U)
#  define AdNomALgtReqGroupSafeALgtNomReqMax_adt_UpperLimit (3840U)

#  define AdNomALgtReqGroupSafeALgtNomReqMin_A_adt_LowerLimit (0U)
#  define AdNomALgtReqGroupSafeALgtNomReqMin_A_adt_UpperLimit (3840U)

#  define AdNomALgtReqGroupSafeALgtNomReqMin_adt_LowerLimit (0U)
#  define AdNomALgtReqGroupSafeALgtNomReqMin_adt_UpperLimit (3840U)

#  define AdNomALgtReqGroupSafeNegLimForJerk_A_adt_LowerLimit (0U)
#  define AdNomALgtReqGroupSafeNegLimForJerk_A_adt_UpperLimit (4000U)

#  define AdNomALgtReqGroupSafeNegLimForJerk_adt_LowerLimit (0U)
#  define AdNomALgtReqGroupSafeNegLimForJerk_adt_UpperLimit (4000U)

#  define AdNomALgtReqGroupSafePosLimForJerk_A_adt_LowerLimit (0U)
#  define AdNomALgtReqGroupSafePosLimForJerk_A_adt_UpperLimit (4000U)

#  define AdNomALgtReqGroupSafePosLimForJerk_adt_LowerLimit (0U)
#  define AdNomALgtReqGroupSafePosLimForJerk_adt_UpperLimit (4000U)

#  define AdPrimALgtLimReqGroupSafeALgtMaxReq_A_adt_LowerLimit (0U)
#  define AdPrimALgtLimReqGroupSafeALgtMaxReq_A_adt_UpperLimit (3840U)

#  define AdPrimALgtLimReqGroupSafeALgtMaxReq_adt_LowerLimit (0U)
#  define AdPrimALgtLimReqGroupSafeALgtMaxReq_adt_UpperLimit (3840U)

#  define AdPrimALgtLimReqGroupSafeALgtMinReq_A_adt_LowerLimit (0U)
#  define AdPrimALgtLimReqGroupSafeALgtMinReq_A_adt_UpperLimit (3840U)

#  define AdPrimALgtLimReqGroupSafeALgtMinReq_adt_LowerLimit (0U)
#  define AdPrimALgtLimReqGroupSafeALgtMinReq_adt_UpperLimit (3840U)

#  define AdPrimPoseChks_A_adt_LowerLimit (0U)
#  define AdPrimPoseChks_A_adt_UpperLimit (65535U)

#  define AdPrimPoseDataID_A_adt_LowerLimit (0U)
#  define AdPrimPoseDataID_A_adt_UpperLimit (65535U)

#  define AdPrimPosePitch_A_adt_LowerLimit (0U)
#  define AdPrimPosePitch_A_adt_UpperLimit (65520U)

#  define AdPrimPoseRoll_A_adt_LowerLimit (0U)
#  define AdPrimPoseRoll_A_adt_UpperLimit (65520U)

#  define AdPrimPoseVX_A_adt_LowerLimit (0U)
#  define AdPrimPoseVX_A_adt_UpperLimit (65535U)

#  define AdPrimPoseVY_A_adt_LowerLimit (0U)
#  define AdPrimPoseVY_A_adt_UpperLimit (16383U)

#  define AdPrimWhlAgEstimdGroupSafeWhlAgRate_adt_LowerLimit (0U)
#  define AdPrimWhlAgEstimdGroupSafeWhlAgRate_adt_UpperLimit (28000U)

#  define AdPrimWhlAgEstimdGroupSafeWhlAg_adt_LowerLimit (0U)
#  define AdPrimWhlAgEstimdGroupSafeWhlAg_adt_UpperLimit (32388U)

#  define AdPrimWhlAgReqGroupSafeWhlAgReq_A_adt_LowerLimit (0U)
#  define AdPrimWhlAgReqGroupSafeWhlAgReq_A_adt_UpperLimit (32388U)

#  define AdPrimWhlAgReqGroupSafeWhlAgReq_adt_LowerLimit (0U)
#  define AdPrimWhlAgReqGroupSafeWhlAgReq_adt_UpperLimit (32388U)

#  define AdSecALgtLimReqGroupSafeALgtMaxReq_A_adt_LowerLimit (0U)
#  define AdSecALgtLimReqGroupSafeALgtMaxReq_A_adt_UpperLimit (3840U)

#  define AdSecALgtLimReqGroupSafeALgtMaxReq_adt_LowerLimit (0U)
#  define AdSecALgtLimReqGroupSafeALgtMaxReq_adt_UpperLimit (3840U)

#  define AdSecALgtLimReqGroupSafeALgtMinReq_A_adt_LowerLimit (0U)
#  define AdSecALgtLimReqGroupSafeALgtMinReq_A_adt_UpperLimit (3840U)

#  define AdSecALgtLimReqGroupSafeALgtMinReq_adt_LowerLimit (0U)
#  define AdSecALgtLimReqGroupSafeALgtMinReq_adt_UpperLimit (3840U)

#  define AdSecWhlAgRateLimEstimdSafeAdWhlAgRate1_adt_LowerLimit (0U)
#  define AdSecWhlAgRateLimEstimdSafeAdWhlAgRate1_adt_UpperLimit (28000U)

#  define AdSecWhlAgReqGroupSafeWhlAgReq_A_adt_LowerLimit (0U)
#  define AdSecWhlAgReqGroupSafeWhlAgReq_A_adt_UpperLimit (32388U)

#  define AdSecWhlAgReqGroupSafeWhlAgReq_adt_LowerLimit (0U)
#  define AdSecWhlAgReqGroupSafeWhlAgReq_adt_UpperLimit (32388U)

#  define BrkPedlValBrkPedlVal_adt_LowerLimit (0U)
#  define BrkPedlValBrkPedlVal_adt_UpperLimit (25600U)

#  define BrkTqMinReqBrkTqGrdtNegMinReq_adt_LowerLimit (0U)
#  define BrkTqMinReqBrkTqGrdtNegMinReq_adt_UpperLimit (1023U)

#  define BrkTqMinReqBrkTqGrdtPosMinReq_adt_LowerLimit (0U)
#  define BrkTqMinReqBrkTqGrdtPosMinReq_adt_UpperLimit (1023U)

#  define BrkTqMinReqBrkTqMinReq_adt_LowerLimit (0U)
#  define BrkTqMinReqBrkTqMinReq_adt_UpperLimit (4095U)

#  define PrimALatDataRawSafeMax_adt_LowerLimit (0U)
#  define PrimALatDataRawSafeMax_adt_UpperLimit (32767U)

#  define PrimALatDataRawSafeMin_adt_LowerLimit (0U)
#  define PrimALatDataRawSafeMin_adt_UpperLimit (32767U)

#  define PrimALatDataRawSafeNom_adt_LowerLimit (0U)
#  define PrimALatDataRawSafeNom_adt_UpperLimit (32767U)

#  define PrimALgtDataRawSafeMax_adt_LowerLimit (0U)
#  define PrimALgtDataRawSafeMax_adt_UpperLimit (32767U)

#  define PrimALgtDataRawSafeMin_adt_LowerLimit (0U)
#  define PrimALgtDataRawSafeMin_adt_UpperLimit (32767U)

#  define PrimALgtDataRawSafeNom_adt_LowerLimit (0U)
#  define PrimALgtDataRawSafeNom_adt_UpperLimit (32767U)

#  define PrimVehSpdGroupSafeMax_adt_LowerLimit (0U)
#  define PrimVehSpdGroupSafeMax_adt_UpperLimit (8191U)

#  define PrimVehSpdGroupSafeMin_adt_LowerLimit (0U)
#  define PrimVehSpdGroupSafeMin_adt_UpperLimit (8191U)

#  define PrimVehSpdGroupSafeNom_adt_LowerLimit (0U)
#  define PrimVehSpdGroupSafeNom_adt_UpperLimit (8191U)

#  define RoadLoadNomCoeff0_adt_LowerLimit (0U)
#  define RoadLoadNomCoeff0_adt_UpperLimit (2047U)

#  define RoadLoadNomCoeff2_adt_LowerLimit (0U)
#  define RoadLoadNomCoeff2_adt_UpperLimit (991U)

#  define SecAdNomALgtReqGroupSafeALgtNomReqMax_A_adt_LowerLimit (0U)
#  define SecAdNomALgtReqGroupSafeALgtNomReqMax_A_adt_UpperLimit (3840U)

#  define SecAdNomALgtReqGroupSafeALgtNomReqMax_adt_LowerLimit (0U)
#  define SecAdNomALgtReqGroupSafeALgtNomReqMax_adt_UpperLimit (3840U)

#  define SecAdNomALgtReqGroupSafeALgtNomReqMin_A_adt_LowerLimit (0U)
#  define SecAdNomALgtReqGroupSafeALgtNomReqMin_A_adt_UpperLimit (3840U)

#  define SecAdNomALgtReqGroupSafeALgtNomReqMin_adt_LowerLimit (0U)
#  define SecAdNomALgtReqGroupSafeALgtNomReqMin_adt_UpperLimit (3840U)

#  define SecAdNomALgtReqGroupSafeNegLimForJerk_A_adt_LowerLimit (0U)
#  define SecAdNomALgtReqGroupSafeNegLimForJerk_A_adt_UpperLimit (4000U)

#  define SecAdNomALgtReqGroupSafeNegLimForJerk_adt_LowerLimit (0U)
#  define SecAdNomALgtReqGroupSafeNegLimForJerk_adt_UpperLimit (4000U)

#  define SecAdNomALgtReqGroupSafePosLimForJerk_A_adt_LowerLimit (0U)
#  define SecAdNomALgtReqGroupSafePosLimForJerk_A_adt_UpperLimit (4000U)

#  define SecAdNomALgtReqGroupSafePosLimForJerk_adt_LowerLimit (0U)
#  define SecAdNomALgtReqGroupSafePosLimForJerk_adt_UpperLimit (4000U)

#  define WhlAgReqFbRdntWhlAgRateReqFeedFwd_adt_LowerLimit (0U)
#  define WhlAgReqFbRdntWhlAgRateReqFeedFwd_adt_UpperLimit (28000U)

#  define WhlAgReqFbRdntWhlAgReq_adt_LowerLimit (0U)
#  define WhlAgReqFbRdntWhlAgReq_adt_UpperLimit (32388U)

#  define WhlAgReqFbWhlAgRateReqFeedFwd_adt_LowerLimit (0U)
#  define WhlAgReqFbWhlAgRateReqFeedFwd_adt_UpperLimit (28000U)

#  define WhlAgReqFbWhlAgReq_adt_LowerLimit (0U)
#  define WhlAgReqFbWhlAgReq_adt_UpperLimit (32388U)

#  define AdPrimPoseAX_A_adt_LowerLimit (0U)
#  define AdPrimPoseAX_A_adt_UpperLimit (1048575U)

#  define AdPrimPoseAY_A_adt_LowerLimit (0U)
#  define AdPrimPoseAY_A_adt_UpperLimit (1048575U)

#  define AdPrimPoseAZ_A_adt_LowerLimit (0U)
#  define AdPrimPoseAZ_A_adt_UpperLimit (1048575U)

#  define AdPrimPosePitchRate_A_adt_LowerLimit (0U)
#  define AdPrimPosePitchRate_A_adt_UpperLimit (1048575U)

#  define AdPrimPoseRollRate_A_adt_LowerLimit (0U)
#  define AdPrimPoseRollRate_A_adt_UpperLimit (1048575U)

#  define AdPrimPoseTiStampNanoSec_A_adt_LowerLimit (0U)
#  define AdPrimPoseTiStampNanoSec_A_adt_UpperLimit (4294967295U)

#  define AdPrimPoseTiStampSec_A_adt_LowerLimit (0U)
#  define AdPrimPoseTiStampSec_A_adt_UpperLimit (4294967295U)

#  define AdPrimPoseYawRate_A_adt_LowerLimit (0U)
#  define AdPrimPoseYawRate_A_adt_UpperLimit (4194000U)

#  define AdPrimPoseYaw_A_adt_LowerLimit (0U)
#  define AdPrimPoseYaw_A_adt_UpperLimit (16776000U)

#  define ALgtMaxAvlALgtMaxAvlLong_adt_LowerLimit (0U)
#  define ALgtMaxAvlALgtMaxAvlLong_adt_UpperLimit (180U)

#  define ALgtMaxAvlALgtMaxAvl_adt_LowerLimit (0U)
#  define ALgtMaxAvlALgtMaxAvl_adt_UpperLimit (180U)

#  define ALgtMaxAvlChks_adt_LowerLimit (0U)
#  define ALgtMaxAvlChks_adt_UpperLimit (255U)

#  define ALgtMaxAvlCntr_adt_LowerLimit (0U)
#  define ALgtMaxAvlCntr_adt_UpperLimit (15U)

#  define ALgtMaxAvlDecelLgtMaxAvl_adt_LowerLimit (0U)
#  define ALgtMaxAvlDecelLgtMaxAvl_adt_UpperLimit (150U)

#  define ALnchTiDly3Sec2_adt_LowerLimit (0U)
#  define ALnchTiDly3Sec2_adt_UpperLimit (60U)

#  define AdFreeDstChks_adt_LowerLimit (0U)
#  define AdFreeDstChks_adt_UpperLimit (255U)

#  define AdFreeDstCntr_adt_LowerLimit (0U)
#  define AdFreeDstCntr_adt_UpperLimit (15U)

#  define AdFreeDstFreeDstFwd_adt_LowerLimit (0U)
#  define AdFreeDstFreeDstFwd_adt_UpperLimit (250U)

#  define AdFreeDstFreeDstRvs_adt_LowerLimit (0U)
#  define AdFreeDstFreeDstRvs_adt_UpperLimit (250U)

#  define AdFusedFricEstimnFricEstimn_adt_LowerLimit (0U)
#  define AdFusedFricEstimnFricEstimn_adt_UpperLimit (200U)

#  define AdNomALgtReqGroupSafeChks_A_adt_LowerLimit (0U)
#  define AdNomALgtReqGroupSafeChks_A_adt_UpperLimit (255U)

#  define AdNomALgtReqGroupSafeChks_adt_LowerLimit (0U)
#  define AdNomALgtReqGroupSafeChks_adt_UpperLimit (255U)

#  define AdNomALgtReqGroupSafeCntr_A_adt_LowerLimit (0U)
#  define AdNomALgtReqGroupSafeCntr_A_adt_UpperLimit (15U)

#  define AdNomALgtReqGroupSafeCntr_adt_LowerLimit (0U)
#  define AdNomALgtReqGroupSafeCntr_adt_UpperLimit (15U)

#  define AdPrimALgtLimReqGroupSafeChks_A_adt_LowerLimit (0U)
#  define AdPrimALgtLimReqGroupSafeChks_A_adt_UpperLimit (255U)

#  define AdPrimALgtLimReqGroupSafeChks_adt_LowerLimit (0U)
#  define AdPrimALgtLimReqGroupSafeChks_adt_UpperLimit (255U)

#  define AdPrimALgtLimReqGroupSafeCntr_A_adt_LowerLimit (0U)
#  define AdPrimALgtLimReqGroupSafeCntr_A_adt_UpperLimit (15U)

#  define AdPrimALgtLimReqGroupSafeCntr_adt_LowerLimit (0U)
#  define AdPrimALgtLimReqGroupSafeCntr_adt_UpperLimit (15U)

#  define AdPrimPoseAXConf_A_adt_LowerLimit (0U)
#  define AdPrimPoseAXConf_A_adt_UpperLimit (7U)

#  define AdPrimPoseAYConf_A_adt_LowerLimit (0U)
#  define AdPrimPoseAYConf_A_adt_UpperLimit (7U)

#  define AdPrimPoseAZConf_A_adt_LowerLimit (0U)
#  define AdPrimPoseAZConf_A_adt_UpperLimit (7U)

#  define AdPrimPoseCntr_A_adt_LowerLimit (0U)
#  define AdPrimPoseCntr_A_adt_UpperLimit (255U)

#  define AdPrimPosePitchConf_A_adt_LowerLimit (0U)
#  define AdPrimPosePitchConf_A_adt_UpperLimit (7U)

#  define AdPrimPosePitchRateConf_A_adt_LowerLimit (0U)
#  define AdPrimPosePitchRateConf_A_adt_UpperLimit (7U)

#  define AdPrimPosePosXConf_A_adt_LowerLimit (0U)
#  define AdPrimPosePosXConf_A_adt_UpperLimit (7U)

#  define AdPrimPosePosYConf_A_adt_LowerLimit (0U)
#  define AdPrimPosePosYConf_A_adt_UpperLimit (7U)

#  define AdPrimPoseRollConf_A_adt_LowerLimit (0U)
#  define AdPrimPoseRollConf_A_adt_UpperLimit (7U)

#  define AdPrimPoseRollRateConf_A_adt_LowerLimit (0U)
#  define AdPrimPoseRollRateConf_A_adt_UpperLimit (7U)

#  define AdPrimPoseVXConf_A_adt_LowerLimit (0U)
#  define AdPrimPoseVXConf_A_adt_UpperLimit (7U)

#  define AdPrimPoseVYConf_A_adt_LowerLimit (0U)
#  define AdPrimPoseVYConf_A_adt_UpperLimit (7U)

#  define AdPrimPoseYawConf_A_adt_LowerLimit (0U)
#  define AdPrimPoseYawConf_A_adt_UpperLimit (7U)

#  define AdPrimPoseYawRateConf_A_adt_LowerLimit (0U)
#  define AdPrimPoseYawRateConf_A_adt_UpperLimit (7U)

#  define AdPrimWhlAgEstimdGroupSafeChks_adt_LowerLimit (0U)
#  define AdPrimWhlAgEstimdGroupSafeChks_adt_UpperLimit (255U)

#  define AdPrimWhlAgEstimdGroupSafeCntr_adt_LowerLimit (0U)
#  define AdPrimWhlAgEstimdGroupSafeCntr_adt_UpperLimit (15U)

#  define AdPrimWhlAgReqGroupSafeChks_A_adt_LowerLimit (0U)
#  define AdPrimWhlAgReqGroupSafeChks_A_adt_UpperLimit (255U)

#  define AdPrimWhlAgReqGroupSafeChks_adt_LowerLimit (0U)
#  define AdPrimWhlAgReqGroupSafeChks_adt_UpperLimit (255U)

#  define AdPrimWhlAgReqGroupSafeCntr_A_adt_LowerLimit (0U)
#  define AdPrimWhlAgReqGroupSafeCntr_A_adt_UpperLimit (15U)

#  define AdPrimWhlAgReqGroupSafeCntr_adt_LowerLimit (0U)
#  define AdPrimWhlAgReqGroupSafeCntr_adt_UpperLimit (15U)

#  define AdSecALgtLimReqGroupSafeChks_A_adt_LowerLimit (0U)
#  define AdSecALgtLimReqGroupSafeChks_A_adt_UpperLimit (255U)

#  define AdSecALgtLimReqGroupSafeChks_adt_LowerLimit (0U)
#  define AdSecALgtLimReqGroupSafeChks_adt_UpperLimit (255U)

#  define AdSecALgtLimReqGroupSafeCntr_A_adt_LowerLimit (0U)
#  define AdSecALgtLimReqGroupSafeCntr_A_adt_UpperLimit (15U)

#  define AdSecALgtLimReqGroupSafeCntr_adt_LowerLimit (0U)
#  define AdSecALgtLimReqGroupSafeCntr_adt_UpperLimit (15U)

#  define AdSecWhlAgRateLimEstimdSafeChks_adt_LowerLimit (0U)
#  define AdSecWhlAgRateLimEstimdSafeChks_adt_UpperLimit (255U)

#  define AdSecWhlAgRateLimEstimdSafeCntr_adt_LowerLimit (0U)
#  define AdSecWhlAgRateLimEstimdSafeCntr_adt_UpperLimit (15U)

#  define AdSecWhlAgReqGroupSafeChks_A_adt_LowerLimit (0U)
#  define AdSecWhlAgReqGroupSafeChks_A_adt_UpperLimit (255U)

#  define AdSecWhlAgReqGroupSafeChks_adt_LowerLimit (0U)
#  define AdSecWhlAgReqGroupSafeChks_adt_UpperLimit (255U)

#  define AdSecWhlAgReqGroupSafeCntr_A_adt_LowerLimit (0U)
#  define AdSecWhlAgReqGroupSafeCntr_A_adt_UpperLimit (15U)

#  define AdSecWhlAgReqGroupSafeCntr_adt_LowerLimit (0U)
#  define AdSecWhlAgReqGroupSafeCntr_adt_UpperLimit (15U)

#  define AgDataRawSafeChks_adt_LowerLimit (0U)
#  define AgDataRawSafeChks_adt_UpperLimit (255U)

#  define AgDataRawSafeCntr_adt_LowerLimit (0U)
#  define AgDataRawSafeCntr_adt_UpperLimit (15U)

#  define BrkTqMinReqBrkTqMinReqChks_adt_LowerLimit (0U)
#  define BrkTqMinReqBrkTqMinReqChks_adt_UpperLimit (255U)

#  define BrkTqMinReqBrkTqMinReqCntr_adt_LowerLimit (0U)
#  define BrkTqMinReqBrkTqMinReqCntr_adt_UpperLimit (15U)

#  define FricEstimnFromVehDyn_adt_LowerLimit (0U)
#  define FricEstimnFromVehDyn_adt_UpperLimit (200U)

#  define PrimALatDataRawSafeChks_adt_LowerLimit (0U)
#  define PrimALatDataRawSafeChks_adt_UpperLimit (255U)

#  define PrimALatDataRawSafeCntr_adt_LowerLimit (0U)
#  define PrimALatDataRawSafeCntr_adt_UpperLimit (15U)

#  define PrimALgtDataRawSafeChks_adt_LowerLimit (0U)
#  define PrimALgtDataRawSafeChks_adt_UpperLimit (255U)

#  define PrimALgtDataRawSafeCntr_adt_LowerLimit (0U)
#  define PrimALgtDataRawSafeCntr_adt_UpperLimit (15U)

#  define PrimVLatSafeChks_adt_LowerLimit (0U)
#  define PrimVLatSafeChks_adt_UpperLimit (255U)

#  define PrimVLatSafeCntr_adt_LowerLimit (0U)
#  define PrimVLatSafeCntr_adt_UpperLimit (15U)

#  define PrimVehMSafeChks_adt_LowerLimit (0U)
#  define PrimVehMSafeChks_adt_UpperLimit (255U)

#  define PrimVehMSafeCntr_adt_LowerLimit (0U)
#  define PrimVehMSafeCntr_adt_UpperLimit (15U)

#  define PrimVehMSafeVehMMax_adt_LowerLimit (0U)
#  define PrimVehMSafeVehMMax_adt_UpperLimit (255U)

#  define PrimVehMSafeVehMMin_adt_LowerLimit (0U)
#  define PrimVehMSafeVehMMin_adt_UpperLimit (255U)

#  define PrimVehMSafeVehMNom_adt_LowerLimit (0U)
#  define PrimVehMSafeVehMNom_adt_UpperLimit (255U)

#  define PrimVehSpdGroupSafeChks_adt_LowerLimit (0U)
#  define PrimVehSpdGroupSafeChks_adt_UpperLimit (255U)

#  define PrimVehSpdGroupSafeCntr_adt_LowerLimit (0U)
#  define PrimVehSpdGroupSafeCntr_adt_UpperLimit (15U)

#  define PrimWhlAgSpdFrntSafeChks_adt_LowerLimit (0U)
#  define PrimWhlAgSpdFrntSafeChks_adt_UpperLimit (255U)

#  define PrimWhlAgSpdFrntSafeCntr_adt_LowerLimit (0U)
#  define PrimWhlAgSpdFrntSafeCntr_adt_UpperLimit (15U)

#  define PrimWhlAgSpdReSafeChks_adt_LowerLimit (0U)
#  define PrimWhlAgSpdReSafeChks_adt_UpperLimit (255U)

#  define PrimWhlAgSpdReSafeCntr_adt_LowerLimit (0U)
#  define PrimWhlAgSpdReSafeCntr_adt_UpperLimit (15U)

#  define PrimYawRateSafeChks_adt_LowerLimit (0U)
#  define PrimYawRateSafeChks_adt_UpperLimit (255U)

#  define PrimYawRateSafeCntr_adt_LowerLimit (0U)
#  define PrimYawRateSafeCntr_adt_UpperLimit (15U)

#  define SecAdNomALgtReqGroupSafeChks_A_adt_LowerLimit (0U)
#  define SecAdNomALgtReqGroupSafeChks_A_adt_UpperLimit (255U)

#  define SecAdNomALgtReqGroupSafeChks_adt_LowerLimit (0U)
#  define SecAdNomALgtReqGroupSafeChks_adt_UpperLimit (255U)

#  define SecAdNomALgtReqGroupSafeCntr_A_adt_LowerLimit (0U)
#  define SecAdNomALgtReqGroupSafeCntr_A_adt_UpperLimit (15U)

#  define SecAdNomALgtReqGroupSafeCntr_adt_LowerLimit (0U)
#  define SecAdNomALgtReqGroupSafeCntr_adt_UpperLimit (15U)

#  define SecPoseMonSafeChks_adt_LowerLimit (0U)
#  define SecPoseMonSafeChks_adt_UpperLimit (255U)

#  define SecPoseMonSafeCntr_adt_LowerLimit (0U)
#  define SecPoseMonSafeCntr_adt_UpperLimit (15U)

#  define SteerWhlSnsrSafeChks_adt_LowerLimit (0U)
#  define SteerWhlSnsrSafeChks_adt_UpperLimit (255U)

#  define SteerWhlSnsrSafeCntr_adt_LowerLimit (0U)
#  define SteerWhlSnsrSafeCntr_adt_UpperLimit (15U)

#  define WhlAgReqFbChks_adt_LowerLimit (0U)
#  define WhlAgReqFbChks_adt_UpperLimit (255U)

#  define WhlAgReqFbCntr_adt_LowerLimit (0U)
#  define WhlAgReqFbCntr_adt_UpperLimit (15U)

#  define WhlAgReqFbRdntChks_adt_LowerLimit (0U)
#  define WhlAgReqFbRdntChks_adt_UpperLimit (255U)

#  define WhlAgReqFbRdntCntr_adt_LowerLimit (0U)
#  define WhlAgReqFbRdntCntr_adt_UpperLimit (15U)

#  ifndef Cx0_OnOff1_Off
#   define Cx0_OnOff1_Off (0U)
#  endif

#  ifndef Cx1_OnOff1_On
#   define Cx1_OnOff1_On (1U)
#  endif

#  ifndef Cx0_AhbcSts1_Off
#   define Cx0_AhbcSts1_Off (0U)
#  endif

#  ifndef Cx1_AhbcSts1_On
#   define Cx1_AhbcSts1_On (1U)
#  endif

#  ifndef Cx2_AhbcSts1_Unavailable
#   define Cx2_AhbcSts1_Unavailable (2U)
#  endif

#  ifndef Cx0_AsySftyDecelEnadByVehDyn1_AsyBrkgNotAllwd
#   define Cx0_AsySftyDecelEnadByVehDyn1_AsyBrkgNotAllwd (0U)
#  endif

#  ifndef Cx1_AsySftyDecelEnadByVehDyn1_AsyBrkgAllwd
#   define Cx1_AsySftyDecelEnadByVehDyn1_AsyBrkgAllwd (1U)
#  endif

#  ifndef Cx00_ChrgnTyp_Disconnected
#   define Cx00_ChrgnTyp_Disconnected (0U)
#  endif

#  ifndef Cx01_ChrgnTyp_AcChrgn
#   define Cx01_ChrgnTyp_AcChrgn (1U)
#  endif

#  ifndef Cx02_ChrgnTyp_ChaChrgn
#   define Cx02_ChrgnTyp_ChaChrgn (2U)
#  endif

#  ifndef Cx03_ChrgnTyp_CcsChrgn
#   define Cx03_ChrgnTyp_CcsChrgn (3U)
#  endif

#  ifndef Cx04_ChrgnTyp_GbtChrgn
#   define Cx04_ChrgnTyp_GbtChrgn (4U)
#  endif

#  ifndef Cx05_ChrgnTyp_ThreePhaChrgn
#   define Cx05_ChrgnTyp_ThreePhaChrgn (5U)
#  endif

#  ifndef Cx06_ChrgnTyp_Indu
#   define Cx06_ChrgnTyp_Indu (6U)
#  endif

#  ifndef CxFF_ChrgnTyp_Invld
#   define CxFF_ChrgnTyp_Invld (255U)
#  endif

#  ifndef Cx0_OnBdChrgrHndlSts_Disconnected
#   define Cx0_OnBdChrgrHndlSts_Disconnected (0U)
#  endif

#  ifndef Cx1_OnBdChrgrHndlSts_ConnectedWithoutPower
#   define Cx1_OnBdChrgrHndlSts_ConnectedWithoutPower (1U)
#  endif

#  ifndef Cx2_OnBdChrgrHndlSts_PowerAvailableButNotActivated
#   define Cx2_OnBdChrgrHndlSts_PowerAvailableButNotActivated (2U)
#  endif

#  ifndef Cx3_OnBdChrgrHndlSts_ConnectedWithPower
#   define Cx3_OnBdChrgrHndlSts_ConnectedWithPower (3U)
#  endif

#  ifndef Cx4_OnBdChrgrHndlSts_Init
#   define Cx4_OnBdChrgrHndlSts_Init (4U)
#  endif

#  ifndef Cx5_OnBdChrgrHndlSts_Fault
#   define Cx5_OnBdChrgrHndlSts_Fault (5U)
#  endif

#  ifndef Cx0_CllsnThreat1_Ukwn
#   define Cx0_CllsnThreat1_Ukwn (0U)
#  endif

#  ifndef Cx1_CllsnThreat1_ThreatLo
#   define Cx1_CllsnThreat1_ThreatLo (1U)
#  endif

#  ifndef Cx2_CllsnThreat1_ThreatMed
#   define Cx2_CllsnThreat1_ThreatMed (2U)
#  endif

#  ifndef Cx3_CllsnThreat1_ThreatHi
#   define Cx3_CllsnThreat1_ThreatHi (3U)
#  endif

#  ifndef Cx0_CnclReqCrit1_NotAllwd1
#   define Cx0_CnclReqCrit1_NotAllwd1 (0U)
#  endif

#  ifndef Cx1_CnclReqCrit1_NoCnclReq
#   define Cx1_CnclReqCrit1_NoCnclReq (1U)
#  endif

#  ifndef Cx2_CnclReqCrit1_CnclReq
#   define Cx2_CnclReqCrit1_CnclReq (2U)
#  endif

#  ifndef Cx3_CnclReqCrit1_NotAllwd2
#   define Cx3_CnclReqCrit1_NotAllwd2 (3U)
#  endif

#  ifndef Cx00_PwrDoorMovmtFailNotif_NoFailr
#   define Cx00_PwrDoorMovmtFailNotif_NoFailr (0U)
#  endif

#  ifndef Cx01_PwrDoorMovmtFailNotif_StopCmd
#   define Cx01_PwrDoorMovmtFailNotif_StopCmd (1U)
#  endif

#  ifndef Cx02_PwrDoorMovmtFailNotif_FctRelsNOK
#   define Cx02_PwrDoorMovmtFailNotif_FctRelsNOK (2U)
#  endif

#  ifndef Cx03_PwrDoorMovmtFailNotif_VehPitchOrRollAgOutOfRng
#   define Cx03_PwrDoorMovmtFailNotif_VehPitchOrRollAgOutOfRng (3U)
#  endif

#  ifndef Cx04_PwrDoorMovmtFailNotif_VehSpdOutOfLim
#   define Cx04_PwrDoorMovmtFailNotif_VehSpdOutOfLim (4U)
#  endif

#  ifndef Cx05_PwrDoorMovmtFailNotif_AmbTOutOfRng
#   define Cx05_PwrDoorMovmtFailNotif_AmbTOutOfRng (5U)
#  endif

#  ifndef Cx06_PwrDoorMovmtFailNotif_SplyUBelowLim
#   define Cx06_PwrDoorMovmtFailNotif_SplyUBelowLim (6U)
#  endif

#  ifndef Cx07_PwrDoorMovmtFailNotif_SplyUAboveLim
#   define Cx07_PwrDoorMovmtFailNotif_SplyUAboveLim (7U)
#  endif

#  ifndef Cx08_PwrDoorMovmtFailNotif_PosnRngMissmatch
#   define Cx08_PwrDoorMovmtFailNotif_PosnRngMissmatch (8U)
#  endif

#  ifndef Cx09_PwrDoorMovmtFailNotif_EndPosnLim
#   define Cx09_PwrDoorMovmtFailNotif_EndPosnLim (9U)
#  endif

#  ifndef Cx0A_PwrDoorMovmtFailNotif_RvsgMovmtFinshd
#   define Cx0A_PwrDoorMovmtFailNotif_RvsgMovmtFinshd (10U)
#  endif

#  ifndef Cx0B_PwrDoorMovmtFailNotif_AntiCllsnSigDetd
#   define Cx0B_PwrDoorMovmtFailNotif_AntiCllsnSigDetd (11U)
#  endif

#  ifndef Cx0C_PwrDoorMovmtFailNotif_AntiTrapSigDetd
#   define Cx0C_PwrDoorMovmtFailNotif_AntiTrapSigDetd (12U)
#  endif

#  ifndef Cx0D_PwrDoorMovmtFailNotif_ThermoProtnActvDetd
#   define Cx0D_PwrDoorMovmtFailNotif_ThermoProtnActvDetd (13U)
#  endif

#  ifndef Cx0E_PwrDoorMovmtFailNotif_DoorMotStallCdn
#   define Cx0E_PwrDoorMovmtFailNotif_DoorMotStallCdn (14U)
#  endif

#  ifndef Cx0F_PwrDoorMovmtFailNotif_DoorMotTiOut
#   define Cx0F_PwrDoorMovmtFailNotif_DoorMotTiOut (15U)
#  endif

#  ifndef Cx10_PwrDoorMovmtFailNotif_AgSnsrFlt
#   define Cx10_PwrDoorMovmtFailNotif_AgSnsrFlt (16U)
#  endif

#  ifndef Cx11_PwrDoorMovmtFailNotif_DoorHallMotFlt
#   define Cx11_PwrDoorMovmtFailNotif_DoorHallMotFlt (17U)
#  endif

#  ifndef Cx12_PwrDoorMovmtFailNotif_DoorMotDrvrGenFailr
#   define Cx12_PwrDoorMovmtFailNotif_DoorMotDrvrGenFailr (18U)
#  endif

#  ifndef Cx13_PwrDoorMovmtFailNotif_LtchNotReldDetd
#   define Cx13_PwrDoorMovmtFailNotif_LtchNotReldDetd (19U)
#  endif

#  ifndef Cx14_PwrDoorMovmtFailNotif_CluHndlgFailr
#   define Cx14_PwrDoorMovmtFailNotif_CluHndlgFailr (20U)
#  endif

#  ifndef Cx15_PwrDoorMovmtFailNotif_SysIninErr
#   define Cx15_PwrDoorMovmtFailNotif_SysIninErr (21U)
#  endif

#  ifndef Cx16_PwrDoorMovmtFailNotif_NetErr
#   define Cx16_PwrDoorMovmtFailNotif_NetErr (22U)
#  endif

#  ifndef Cx17_PwrDoorMovmtFailNotif_FctNotAvl
#   define Cx17_PwrDoorMovmtFailNotif_FctNotAvl (23U)
#  endif

#  ifndef Cx18_PwrDoorMovmtFailNotif_PrmSetInvld
#   define Cx18_PwrDoorMovmtFailNotif_PrmSetInvld (24U)
#  endif

#  ifndef Cx19_PwrDoorMovmtFailNotif_GenMemFailr
#   define Cx19_PwrDoorMovmtFailNotif_GenMemFailr (25U)
#  endif

#  ifndef Cx1A_PwrDoorMovmtFailNotif_TestModActv
#   define Cx1A_PwrDoorMovmtFailNotif_TestModActv (26U)
#  endif

#  ifndef Cx1B_PwrDoorMovmtFailNotif_NOKToClsDoor
#   define Cx1B_PwrDoorMovmtFailNotif_NOKToClsDoor (27U)
#  endif

#  ifndef Cx1C_PwrDoorMovmtFailNotif_NOKToOpenDoor
#   define Cx1C_PwrDoorMovmtFailNotif_NOKToOpenDoor (28U)
#  endif

#  ifndef Cx1D_PwrDoorMovmtFailNotif_UsgModInvld
#   define Cx1D_PwrDoorMovmtFailNotif_UsgModInvld (29U)
#  endif

#  ifndef Cx0_DoorSts2_Ukwn
#   define Cx0_DoorSts2_Ukwn (0U)
#  endif

#  ifndef Cx1_DoorSts2_Opend
#   define Cx1_DoorSts2_Opend (1U)
#  endif

#  ifndef Cx2_DoorSts2_Clsd
#   define Cx2_DoorSts2_Clsd (2U)
#  endif

#  define ESC_FLWheelSpeedKPH_adt_LowerLimit (0U)
#  define ESC_FLWheelSpeedKPH_adt_UpperLimit (8191U)

#  ifndef Cx1FFF_INVALID_VALUE
#   define Cx1FFF_INVALID_VALUE (8191U)
#  endif

#  define ESC_FRWheelSpeedKPH_adt_LowerLimit (0U)
#  define ESC_FRWheelSpeedKPH_adt_UpperLimit (8191U)

#  define ESC_RLWheelSpeedKPH_adt_LowerLimit (0U)
#  define ESC_RLWheelSpeedKPH_adt_UpperLimit (8191U)

#  define ESC_RRWheelSpeedKPH_adt_LowerLimit (0U)
#  define ESC_RRWheelSpeedKPH_adt_UpperLimit (8191U)

#  define ESC_VehicleSpeed_adt_LowerLimit (0U)
#  define ESC_VehicleSpeed_adt_UpperLimit (8191U)

#  ifndef Cx0_EngOilLvlSts1_OilLvlOk
#   define Cx0_EngOilLvlSts1_OilLvlOk (0U)
#  endif

#  ifndef Cx1_EngOilLvlSts1_OilLvlLo1
#   define Cx1_EngOilLvlSts1_OilLvlLo1 (1U)
#  endif

#  ifndef Cx2_EngOilLvlSts1_OilLvlLo2
#   define Cx2_EngOilLvlSts1_OilLvlLo2 (2U)
#  endif

#  ifndef Cx3_EngOilLvlSts1_OilLvlHi
#   define Cx3_EngOilLvlSts1_OilLvlHi (3U)
#  endif

#  ifndef Cx4_EngOilLvlSts1_OilLvlSrvRqrd
#   define Cx4_EngOilLvlSts1_OilLvlSrvRqrd (4U)
#  endif

#  ifndef Cx5_EngOilLvlSts1_Resd
#   define Cx5_EngOilLvlSts1_Resd (5U)
#  endif

#  ifndef Cx0_EngOilPWarn1_EngOilPOk
#   define Cx0_EngOilPWarn1_EngOilPOk (0U)
#  endif

#  ifndef Cx1_EngOilPWarn1_EngOilPNotOk
#   define Cx1_EngOilPWarn1_EngOilPNotOk (1U)
#  endif

#  ifndef Cx0_Boolean_FALSE
#   define Cx0_Boolean_FALSE (0U)
#  endif

#  ifndef Cx1_Boolean_TRUE
#   define Cx1_Boolean_TRUE (1U)
#  endif

#  ifndef Cx0_OnOffCrit1_NotVld1
#   define Cx0_OnOffCrit1_NotVld1 (0U)
#  endif

#  ifndef Cx1_OnOffCrit1_Off
#   define Cx1_OnOffCrit1_Off (1U)
#  endif

#  ifndef Cx2_OnOffCrit1_On
#   define Cx2_OnOffCrit1_On (2U)
#  endif

#  ifndef Cx3_OnOffCrit1_NotVld2
#   define Cx3_OnOffCrit1_NotVld2 (3U)
#  endif

#  ifndef Cx0_IndcrSts1_Off
#   define Cx0_IndcrSts1_Off (0U)
#  endif

#  ifndef Cx1_IndcrSts1_LeOn
#   define Cx1_IndcrSts1_LeOn (1U)
#  endif

#  ifndef Cx2_IndcrSts1_RiOn
#   define Cx2_IndcrSts1_RiOn (2U)
#  endif

#  ifndef Cx3_IndcrSts1_LeAndRiOn
#   define Cx3_IndcrSts1_LeAndRiOn (3U)
#  endif

#  ifndef Cx0_InhbOfAsySftyDecelByVehDyn1_AsySftyBrkgNotDendByBrk
#   define Cx0_InhbOfAsySftyDecelByVehDyn1_AsySftyBrkgNotDendByBrk (0U)
#  endif

#  ifndef Cx1_InhbOfAsySftyDecelByVehDyn1_AsySftyBrkgDendByBrk
#   define Cx1_InhbOfAsySftyDecelByVehDyn1_AsySftyBrkgDendByBrk (1U)
#  endif

#  ifndef Cx0_PassSeatSts1_Empty
#   define Cx0_PassSeatSts1_Empty (0U)
#  endif

#  ifndef Cx1_PassSeatSts1_Fmale
#   define Cx1_PassSeatSts1_Fmale (1U)
#  endif

#  ifndef Cx2_PassSeatSts1_OccptLrg
#   define Cx2_PassSeatSts1_OccptLrg (2U)
#  endif

#  ifndef Cx3_PassSeatSts1_Ukwn
#   define Cx3_PassSeatSts1_Ukwn (3U)
#  endif

#  ifndef Cx1_MilSt_Off
#   define Cx1_MilSt_Off (1U)
#  endif

#  ifndef Cx2_MilSt_On
#   define Cx2_MilSt_On (2U)
#  endif

#  ifndef Cx3_MilSt_Blink
#   define Cx3_MilSt_Blink (3U)
#  endif

#  ifndef Cx0_PtGearAct1_Neut
#   define Cx0_PtGearAct1_Neut (0U)
#  endif

#  ifndef Cx1_PtGearAct1_Gear1
#   define Cx1_PtGearAct1_Gear1 (1U)
#  endif

#  ifndef Cx2_PtGearAct1_Gear2
#   define Cx2_PtGearAct1_Gear2 (2U)
#  endif

#  ifndef Cx3_PtGearAct1_Gear3
#   define Cx3_PtGearAct1_Gear3 (3U)
#  endif

#  ifndef Cx4_PtGearAct1_Gear4
#   define Cx4_PtGearAct1_Gear4 (4U)
#  endif

#  ifndef Cx5_PtGearAct1_Gear5
#   define Cx5_PtGearAct1_Gear5 (5U)
#  endif

#  ifndef Cx6_PtGearAct1_Gear6
#   define Cx6_PtGearAct1_Gear6 (6U)
#  endif

#  ifndef Cx7_PtGearAct1_Gear7
#   define Cx7_PtGearAct1_Gear7 (7U)
#  endif

#  ifndef Cx8_PtGearAct1_Gear8
#   define Cx8_PtGearAct1_Gear8 (8U)
#  endif

#  ifndef Cx9_PtGearAct1_Gear9
#   define Cx9_PtGearAct1_Gear9 (9U)
#  endif

#  ifndef CxA_PtGearAct1_Gear10
#   define CxA_PtGearAct1_Gear10 (10U)
#  endif

#  ifndef CxB_PtGearAct1_Gear11
#   define CxB_PtGearAct1_Gear11 (11U)
#  endif

#  ifndef CxC_PtGearAct1_Gear12
#   define CxC_PtGearAct1_Gear12 (12U)
#  endif

#  ifndef CxD_PtGearAct1_Gear13
#   define CxD_PtGearAct1_Gear13 (13U)
#  endif

#  ifndef CxE_PtGearAct1_Ukwn
#   define CxE_PtGearAct1_Ukwn (14U)
#  endif

#  ifndef CxF_PtGearAct1_Rvs
#   define CxF_PtGearAct1_Rvs (15U)
#  endif

#  define ALnchTiDly3Qf_adt_LowerLimit (0U)
#  define ALnchTiDly3Qf_adt_UpperLimit (3U)

#  ifndef Cx0_Qf1_DevOfDataUndefd
#   define Cx0_Qf1_DevOfDataUndefd (0U)
#  endif

#  ifndef Cx1_Qf1_DataTmpUndefdAndEvlnInProgs
#   define Cx1_Qf1_DataTmpUndefdAndEvlnInProgs (1U)
#  endif

#  ifndef Cx2_Qf1_DevOfDataNotWithinRngAllwd
#   define Cx2_Qf1_DevOfDataNotWithinRngAllwd (2U)
#  endif

#  ifndef Cx3_Qf1_DataCalcdWithDevDefd
#   define Cx3_Qf1_DataCalcdWithDevDefd (3U)
#  endif

#  ifndef Cx0_NoYes1_No
#   define Cx0_NoYes1_No (0U)
#  endif

#  ifndef Cx1_NoYes1_Yes
#   define Cx1_NoYes1_Yes (1U)
#  endif

#  define AdFusedFricEstimnFricEstimnConf_adt_LowerLimit (0U)
#  define AdFusedFricEstimnFricEstimnConf_adt_UpperLimit (7U)

#  ifndef Cx0_Qly3_De0
#   define Cx0_Qly3_De0 (0U)
#  endif

#  ifndef Cx1_Qly3_De1
#   define Cx1_Qly3_De1 (1U)
#  endif

#  ifndef Cx2_Qly3_De2
#   define Cx2_Qly3_De2 (2U)
#  endif

#  ifndef Cx3_Qly3_De3
#   define Cx3_Qly3_De3 (3U)
#  endif

#  ifndef Cx4_Qly3_De4
#   define Cx4_Qly3_De4 (4U)
#  endif

#  ifndef Cx5_Qly3_De5
#   define Cx5_Qly3_De5 (5U)
#  endif

#  ifndef Cx6_Qly3_De6
#   define Cx6_Qly3_De6 (6U)
#  endif

#  ifndef Cx7_Qly3_De7
#   define Cx7_Qly3_De7 (7U)
#  endif

#  ifndef Cx00_AdSteerPerf_PerfLvl0
#   define Cx00_AdSteerPerf_PerfLvl0 (0U)
#  endif

#  ifndef Cx01_AdSteerPerf_PerfLvl1
#   define Cx01_AdSteerPerf_PerfLvl1 (1U)
#  endif

#  ifndef Cx02_AdSteerPerf_PerfLvl2
#   define Cx02_AdSteerPerf_PerfLvl2 (2U)
#  endif

#  ifndef Cx03_AdSteerPerf_PerfLvl3
#   define Cx03_AdSteerPerf_PerfLvl3 (3U)
#  endif

#  ifndef Cx04_AdSteerPerf_PerfLvl4
#   define Cx04_AdSteerPerf_PerfLvl4 (4U)
#  endif

#  ifndef Cx05_AdSteerPerf_PerfLvl5
#   define Cx05_AdSteerPerf_PerfLvl5 (5U)
#  endif

#  ifndef Cx06_AdSteerPerf_PerfLvl6
#   define Cx06_AdSteerPerf_PerfLvl6 (6U)
#  endif

#  ifndef Cx07_AdSteerPerf_PerfLvl7
#   define Cx07_AdSteerPerf_PerfLvl7 (7U)
#  endif

#  ifndef Cx08_AdSteerPerf_PerfLvl8
#   define Cx08_AdSteerPerf_PerfLvl8 (8U)
#  endif

#  ifndef Cx09_AdSteerPerf_PerfLvl9
#   define Cx09_AdSteerPerf_PerfLvl9 (9U)
#  endif

#  ifndef Cx0A_AdSteerPerf_PerfLvl10
#   define Cx0A_AdSteerPerf_PerfLvl10 (10U)
#  endif

#  ifndef Cx0B_AdSteerPerf_PerfLvl11
#   define Cx0B_AdSteerPerf_PerfLvl11 (11U)
#  endif

#  ifndef Cx0C_AdSteerPerf_PerfLvl12
#   define Cx0C_AdSteerPerf_PerfLvl12 (12U)
#  endif

#  ifndef Cx0D_AdSteerPerf_PerfLvl13
#   define Cx0D_AdSteerPerf_PerfLvl13 (13U)
#  endif

#  ifndef Cx0E_AdSteerPerf_PerfLvl14
#   define Cx0E_AdSteerPerf_PerfLvl14 (14U)
#  endif

#  ifndef Cx0F_AdSteerPerf_PerfLvl15
#   define Cx0F_AdSteerPerf_PerfLvl15 (15U)
#  endif

#  ifndef Cx10_AdSteerPerf_PerfLvl16
#   define Cx10_AdSteerPerf_PerfLvl16 (16U)
#  endif

#  ifndef Cx11_AdSteerPerf_PerfLvl17
#   define Cx11_AdSteerPerf_PerfLvl17 (17U)
#  endif

#  ifndef Cx12_AdSteerPerf_PerfLvl18
#   define Cx12_AdSteerPerf_PerfLvl18 (18U)
#  endif

#  ifndef Cx13_AdSteerPerf_PerfLvl19
#   define Cx13_AdSteerPerf_PerfLvl19 (19U)
#  endif

#  ifndef Cx14_AdSteerPerf_PerfLvl20
#   define Cx14_AdSteerPerf_PerfLvl20 (20U)
#  endif

#  ifndef Cx0_AdSteerSts_DegradedLvl4
#   define Cx0_AdSteerSts_DegradedLvl4 (0U)
#  endif

#  ifndef Cx1_AdSteerSts_DegradedLvl3
#   define Cx1_AdSteerSts_DegradedLvl3 (1U)
#  endif

#  ifndef Cx2_AdSteerSts_DegradedLvl2
#   define Cx2_AdSteerSts_DegradedLvl2 (2U)
#  endif

#  ifndef Cx3_AdSteerSts_DegradedLvl1
#   define Cx3_AdSteerSts_DegradedLvl1 (3U)
#  endif

#  ifndef Cx4_AdSteerSts_SysOK
#   define Cx4_AdSteerSts_SysOK (4U)
#  endif

#  define AdPrimWhlAgEstimdGroupSafeQf1_adt_LowerLimit (0U)
#  define AdPrimWhlAgEstimdGroupSafeQf1_adt_UpperLimit (3U)

#  ifndef Cx0_AdSteerMod_Off
#   define Cx0_AdSteerMod_Off (0U)
#  endif

#  ifndef Cx1_AdSteerMod_On
#   define Cx1_AdSteerMod_On (1U)
#  endif

#  ifndef Cx2_AdSteerMod_Unknown
#   define Cx2_AdSteerMod_Unknown (2U)
#  endif

#  ifndef Cx0_VehOperIntv_Off
#   define Cx0_VehOperIntv_Off (0U)
#  endif

#  ifndef Cx1_VehOperIntv_On
#   define Cx1_VehOperIntv_On (1U)
#  endif

#  ifndef Cx0_VehOperModSts1_Ukwn
#   define Cx0_VehOperModSts1_Ukwn (0U)
#  endif

#  ifndef Cx1_VehOperModSts1_NoVehOper
#   define Cx1_VehOperModSts1_NoVehOper (1U)
#  endif

#  ifndef Cx2_VehOperModSts1_VehOper
#   define Cx2_VehOperModSts1_VehOper (2U)
#  endif

#  ifndef Cx0_OkNotOk_NotOk
#   define Cx0_OkNotOk_NotOk (0U)
#  endif

#  ifndef Cx1_OkNotOk_Ok
#   define Cx1_OkNotOk_Ok (1U)
#  endif

#  define RollRate1Qf1_adt_LowerLimit (0U)
#  define RollRate1Qf1_adt_UpperLimit (3U)

#  define YawRate1Qf1_adt_LowerLimit (0U)
#  define YawRate1Qf1_adt_UpperLimit (3U)

#  ifndef Cx0_AlrmSt_Disarmd
#   define Cx0_AlrmSt_Disarmd (0U)
#  endif

#  ifndef Cx1_AlrmSt_Armd
#   define Cx1_AlrmSt_Armd (1U)
#  endif

#  ifndef Cx2_AlrmSt_Actv
#   define Cx2_AlrmSt_Actv (2U)
#  endif

#  ifndef Cx0_AlrmTrgSrc_NoTrigSrc
#   define Cx0_AlrmTrgSrc_NoTrigSrc (0U)
#  endif

#  ifndef Cx1_AlrmTrgSrc_SnsrSoundrBattBacked
#   define Cx1_AlrmTrgSrc_SnsrSoundrBattBacked (1U)
#  endif

#  ifndef Cx2_AlrmTrgSrc_SnsrIncln
#   define Cx2_AlrmTrgSrc_SnsrIncln (2U)
#  endif

#  ifndef Cx3_AlrmTrgSrc_SnsrIntrScanr
#   define Cx3_AlrmTrgSrc_SnsrIntrScanr (3U)
#  endif

#  ifndef Cx4_AlrmTrgSrc_Hood
#   define Cx4_AlrmTrgSrc_Hood (4U)
#  endif

#  ifndef Cx5_AlrmTrgSrc_Tr
#   define Cx5_AlrmTrgSrc_Tr (5U)
#  endif

#  ifndef Cx6_AlrmTrgSrc_DoorDrvr
#   define Cx6_AlrmTrgSrc_DoorDrvr (6U)
#  endif

#  ifndef Cx7_AlrmTrgSrc_DoorPass
#   define Cx7_AlrmTrgSrc_DoorPass (7U)
#  endif

#  ifndef Cx8_AlrmTrgSrc_DoorReLe
#   define Cx8_AlrmTrgSrc_DoorReLe (8U)
#  endif

#  ifndef Cx9_AlrmTrgSrc_DoorReRi
#   define Cx9_AlrmTrgSrc_DoorReRi (9U)
#  endif

#  ifndef CxA_AlrmTrgSrc_VehImobnInvld
#   define CxA_AlrmTrgSrc_VehImobnInvld (10U)
#  endif

#  ifndef Cx0_SnsrInclnFailr_NoFailr
#   define Cx0_SnsrInclnFailr_NoFailr (0U)
#  endif

#  ifndef Cx1_SnsrInclnFailr_Failr
#   define Cx1_SnsrInclnFailr_Failr (1U)
#  endif

#  ifndef Cx0_SnsrIntrScanrFailr_NoFailr
#   define Cx0_SnsrIntrScanrFailr_NoFailr (0U)
#  endif

#  ifndef Cx1_SnsrIntrScanrFailr_Failr
#   define Cx1_SnsrIntrScanrFailr_Failr (1U)
#  endif

#  ifndef Cx0_SnsrSoundrBattBackedFailr_NoFailr
#   define Cx0_SnsrSoundrBattBackedFailr_NoFailr (0U)
#  endif

#  ifndef Cx1_SnsrSoundrBattBackedFailr_Failr
#   define Cx1_SnsrSoundrBattBackedFailr_Failr (1U)
#  endif

#  ifndef Cx0_ActvSt_Idle
#   define Cx0_ActvSt_Idle (0U)
#  endif

#  ifndef Cx1_ActvSt_Actv
#   define Cx1_ActvSt_Actv (1U)
#  endif

#  ifndef Cx2_ActvSt_NotActv
#   define Cx2_ActvSt_NotActv (2U)
#  endif

#  ifndef Cx0_BltLockSt1_Unlock
#   define Cx0_BltLockSt1_Unlock (0U)
#  endif

#  ifndef Cx1_BltLockSt1_Lock
#   define Cx1_BltLockSt1_Lock (1U)
#  endif

#  ifndef Cx0_DevErrSts2_NoFlt
#   define Cx0_DevErrSts2_NoFlt (0U)
#  endif

#  ifndef Cx1_DevErrSts2_Flt
#   define Cx1_DevErrSts2_Flt (1U)
#  endif

#  ifndef Cx0_CptEquid_Equid
#   define Cx0_CptEquid_Equid (0U)
#  endif

#  ifndef Cx1_CptEquid_NotEquid
#   define Cx1_CptEquid_NotEquid (1U)
#  endif

#  ifndef Cx0_GenQf1_UndefindDataAccur
#   define Cx0_GenQf1_UndefindDataAccur (0U)
#  endif

#  ifndef Cx1_GenQf1_TmpUndefdData
#   define Cx1_GenQf1_TmpUndefdData (1U)
#  endif

#  ifndef Cx2_GenQf1_DataAccurNotWithinSpcn
#   define Cx2_GenQf1_DataAccurNotWithinSpcn (2U)
#  endif

#  ifndef Cx3_GenQf1_AccurData
#   define Cx3_GenQf1_AccurData (3U)
#  endif

#  define BrkPedlValBrkPedlValQf_adt_LowerLimit (0U)
#  define BrkPedlValBrkPedlValQf_adt_UpperLimit (3U)

#  define BrkTqMinReqBrkActrCtrlModForMinTqReq_adt_LowerLimit (0U)
#  define BrkTqMinReqBrkActrCtrlModForMinTqReq_adt_UpperLimit (3U)

#  ifndef Cx0_BrkActrCtrlMod1_Norm
#   define Cx0_BrkActrCtrlMod1_Norm (0U)
#  endif

#  ifndef Cx1_BrkActrCtrlMod1_Cmft
#   define Cx1_BrkActrCtrlMod1_Cmft (1U)
#  endif

#  ifndef Cx2_BrkActrCtrlMod1_Sfty
#   define Cx2_BrkActrCtrlMod1_Sfty (2U)
#  endif

#  ifndef Cx3_BrkActrCtrlMod1_VldForAd
#   define Cx3_BrkActrCtrlMod1_VldForAd (3U)
#  endif

#  ifndef Cx0_IdPen_ProfUkwn
#   define Cx0_IdPen_ProfUkwn (0U)
#  endif

#  ifndef Cx1_IdPen_Prof1
#   define Cx1_IdPen_Prof1 (1U)
#  endif

#  ifndef Cx2_IdPen_Prof2
#   define Cx2_IdPen_Prof2 (2U)
#  endif

#  ifndef Cx3_IdPen_Prof3
#   define Cx3_IdPen_Prof3 (3U)
#  endif

#  ifndef Cx4_IdPen_Prof4
#   define Cx4_IdPen_Prof4 (4U)
#  endif

#  ifndef Cx5_IdPen_Prof5
#   define Cx5_IdPen_Prof5 (5U)
#  endif

#  ifndef Cx6_IdPen_Prof6
#   define Cx6_IdPen_Prof6 (6U)
#  endif

#  ifndef Cx7_IdPen_Prof7
#   define Cx7_IdPen_Prof7 (7U)
#  endif

#  ifndef Cx8_IdPen_Prof8
#   define Cx8_IdPen_Prof8 (8U)
#  endif

#  ifndef Cx9_IdPen_Prof9
#   define Cx9_IdPen_Prof9 (9U)
#  endif

#  ifndef CxA_IdPen_Prof10
#   define CxA_IdPen_Prof10 (10U)
#  endif

#  ifndef CxB_IdPen_Prof11
#   define CxB_IdPen_Prof11 (11U)
#  endif

#  ifndef CxC_IdPen_Prof12
#   define CxC_IdPen_Prof12 (12U)
#  endif

#  ifndef CxD_IdPen_Prof13
#   define CxD_IdPen_Prof13 (13U)
#  endif

#  ifndef CxE_IdPen_Resd14
#   define CxE_IdPen_Resd14 (14U)
#  endif

#  ifndef CxF_IdPen_ProfAll
#   define CxF_IdPen_ProfAll (15U)
#  endif

#  ifndef Cx0_Snvty1_NotInUse
#   define Cx0_Snvty1_NotInUse (0U)
#  endif

#  ifndef Cx1_Snvty1_LoSnvty
#   define Cx1_Snvty1_LoSnvty (1U)
#  endif

#  ifndef Cx2_Snvty1_NormSnvty
#   define Cx2_Snvty1_NormSnvty (2U)
#  endif

#  ifndef Cx3_Snvty1_HiSnvty
#   define Cx3_Snvty1_HiSnvty (3U)
#  endif

#  ifndef Cx0_StsAutnmsDrv_ADNotOk
#   define Cx0_StsAutnmsDrv_ADNotOk (0U)
#  endif

#  ifndef Cx1_StsAutnmsDrv_Degraded1
#   define Cx1_StsAutnmsDrv_Degraded1 (1U)
#  endif

#  ifndef Cx2_StsAutnmsDrv_Degraded2
#   define Cx2_StsAutnmsDrv_Degraded2 (2U)
#  endif

#  ifndef Cx3_StsAutnmsDrv_ADOk
#   define Cx3_StsAutnmsDrv_ADOk (3U)
#  endif

#  ifndef Cx0_DoorPosnSts_Idle
#   define Cx0_DoorPosnSts_Idle (0U)
#  endif

#  ifndef Cx1_DoorPosnSts_Unkwn
#   define Cx1_DoorPosnSts_Unkwn (1U)
#  endif

#  ifndef Cx2_DoorPosnSts_FullClsd
#   define Cx2_DoorPosnSts_FullClsd (2U)
#  endif

#  ifndef Cx3_DoorPosnSts_HalfClsd
#   define Cx3_DoorPosnSts_HalfClsd (3U)
#  endif

#  ifndef Cx4_DoorPosnSts_Opend
#   define Cx4_DoorPosnSts_Opend (4U)
#  endif

#  ifndef Cx5_DoorPosnSts_MovgClsDir
#   define Cx5_DoorPosnSts_MovgClsDir (5U)
#  endif

#  ifndef Cx6_DoorPosnSts_MovgOpenDir
#   define Cx6_DoorPosnSts_MovgOpenDir (6U)
#  endif

#  ifndef Cx7_DoorPosnSts_ManMovmtClsDir
#   define Cx7_DoorPosnSts_ManMovmtClsDir (7U)
#  endif

#  ifndef Cx8_DoorPosnSts_ManMovmtOpenDir
#   define Cx8_DoorPosnSts_ManMovmtOpenDir (8U)
#  endif

#  ifndef Cx9_DoorPosnSts_PrepnForMovmt
#   define Cx9_DoorPosnSts_PrepnForMovmt (9U)
#  endif

#  ifndef CxA_DoorPosnSts_StopMinPntForCls
#   define CxA_DoorPosnSts_StopMinPntForCls (10U)
#  endif

#  ifndef Cx0_DwnTipReq_DwnTipReqNotActv
#   define Cx0_DwnTipReq_DwnTipReqNotActv (0U)
#  endif

#  ifndef Cx1_DwnTipReq_DwnTipReqActv
#   define Cx1_DwnTipReq_DwnTipReqActv (1U)
#  endif

#  ifndef Cx0_SteerWhlDwnReq1_SteerWhlDwnReqNotActv
#   define Cx0_SteerWhlDwnReq1_SteerWhlDwnReqNotActv (0U)
#  endif

#  ifndef Cx1_SteerWhlDwnReq1_SteerWhlDwnReqActv
#   define Cx1_SteerWhlDwnReq1_SteerWhlDwnReqActv (1U)
#  endif

#  ifndef Cx0_SteerWhlUpReq_SteerWhlUpReqNotActv
#   define Cx0_SteerWhlUpReq_SteerWhlUpReqNotActv (0U)
#  endif

#  ifndef Cx1_SteerWhlUpReq_SteerWhlUpReqActv
#   define Cx1_SteerWhlUpReq_SteerWhlUpReqActv (1U)
#  endif

#  ifndef Cx0_TipReq1_TipReqNotActv
#   define Cx0_TipReq1_TipReqNotActv (0U)
#  endif

#  ifndef Cx1_TipReq1_TipReqActv
#   define Cx1_TipReq1_TipReqActv (1U)
#  endif

#  ifndef Cx0_UpTipReq1_UpTipReqNotActv
#   define Cx0_UpTipReq1_UpTipReqNotActv (0U)
#  endif

#  ifndef Cx1_UpTipReq1_UpTipReqActv
#   define Cx1_UpTipReq1_UpTipReqActv (1U)
#  endif

#  ifndef Cx0_NoYesCrit1_NotVld1
#   define Cx0_NoYesCrit1_NotVld1 (0U)
#  endif

#  ifndef Cx1_NoYesCrit1_No
#   define Cx1_NoYesCrit1_No (1U)
#  endif

#  ifndef Cx2_NoYesCrit1_Yes
#   define Cx2_NoYesCrit1_Yes (2U)
#  endif

#  ifndef Cx3_NoYesCrit1_NotVld2
#   define Cx3_NoYesCrit1_NotVld2 (3U)
#  endif

#  define FricEstimnFromVehDynQly_adt_LowerLimit (0U)
#  define FricEstimnFromVehDynQly_adt_UpperLimit (7U)

#  define PrimALatDataRawSafeMinMaxQf_adt_LowerLimit (0U)
#  define PrimALatDataRawSafeMinMaxQf_adt_UpperLimit (3U)

#  ifndef Cx0_SafeQf_Ukwn
#   define Cx0_SafeQf_Ukwn (0U)
#  endif

#  ifndef Cx1_SafeQf_NoIntgrty
#   define Cx1_SafeQf_NoIntgrty (1U)
#  endif

#  ifndef Cx2_SafeQf_IntgrtyCmprmd
#   define Cx2_SafeQf_IntgrtyCmprmd (2U)
#  endif

#  ifndef Cx3_SafeQf_DataIntgrtyOK
#   define Cx3_SafeQf_DataIntgrtyOK (3U)
#  endif

#  define PrimALatDataRawSafeNomQf_adt_LowerLimit (0U)
#  define PrimALatDataRawSafeNomQf_adt_UpperLimit (3U)

#  define PrimALgtDataRawSafeMinMaxQf_adt_LowerLimit (0U)
#  define PrimALgtDataRawSafeMinMaxQf_adt_UpperLimit (3U)

#  define PrimALgtDataRawSafeNomQf_adt_LowerLimit (0U)
#  define PrimALgtDataRawSafeNomQf_adt_UpperLimit (3U)

#  define PrimAxleSlipStsAndRelAgFrntAxle_adt_LowerLimit (0U)
#  define PrimAxleSlipStsAndRelAgFrntAxle_adt_UpperLimit (3U)

#  ifndef Cx0_SlipSts_Err
#   define Cx0_SlipSts_Err (0U)
#  endif

#  ifndef Cx1_SlipSts_NoInfo
#   define Cx1_SlipSts_NoInfo (1U)
#  endif

#  ifndef Cx2_SlipSts_NotSlip
#   define Cx2_SlipSts_NotSlip (2U)
#  endif

#  ifndef Cx3_SlipSts_Slip
#   define Cx3_SlipSts_Slip (3U)
#  endif

#  define PrimAxleSlipStsAndRelAgIntvSts_adt_LowerLimit (0U)
#  define PrimAxleSlipStsAndRelAgIntvSts_adt_UpperLimit (3U)

#  ifndef Cx0_IntvSts_Err
#   define Cx0_IntvSts_Err (0U)
#  endif

#  ifndef Cx1_IntvSts_NoInfo
#   define Cx1_IntvSts_NoInfo (1U)
#  endif

#  ifndef Cx2_IntvSts_NoIntv
#   define Cx2_IntvSts_NoIntv (2U)
#  endif

#  ifndef Cx3_IntvSts_Intv
#   define Cx3_IntvSts_Intv (3U)
#  endif

#  define PrimAxleSlipStsAndRelAgReAxle_adt_LowerLimit (0U)
#  define PrimAxleSlipStsAndRelAgReAxle_adt_UpperLimit (3U)

#  define PrimAxleSlipStsAndRelAgRelPitchQf_adt_LowerLimit (0U)
#  define PrimAxleSlipStsAndRelAgRelPitchQf_adt_UpperLimit (3U)

#  ifndef Cx0_Qly2_Flt
#   define Cx0_Qly2_Flt (0U)
#  endif

#  ifndef Cx1_Qly2_NoInfo
#   define Cx1_Qly2_NoInfo (1U)
#  endif

#  ifndef Cx2_Qly2_Vld
#   define Cx2_Qly2_Vld (2U)
#  endif

#  define PrimAxleSlipStsAndRelAgRelRolQf_adt_LowerLimit (0U)
#  define PrimAxleSlipStsAndRelAgRelRolQf_adt_UpperLimit (3U)

#  define PrimVLatSafeMinMaxQf_adt_LowerLimit (0U)
#  define PrimVLatSafeMinMaxQf_adt_UpperLimit (3U)

#  define PrimVLatSafeNomQf_adt_LowerLimit (0U)
#  define PrimVLatSafeNomQf_adt_UpperLimit (3U)

#  define PrimVehMSafeNomQf_adt_LowerLimit (0U)
#  define PrimVehMSafeNomQf_adt_UpperLimit (3U)

#  define PrimVehMSafeSafeQf_adt_LowerLimit (0U)
#  define PrimVehMSafeSafeQf_adt_UpperLimit (3U)

#  define PrimVehSpdGroupSafeMinMaxQf_adt_LowerLimit (0U)
#  define PrimVehSpdGroupSafeMinMaxQf_adt_UpperLimit (3U)

#  define PrimVehSpdGroupSafeMovDir_adt_LowerLimit (0U)
#  define PrimVehSpdGroupSafeMovDir_adt_UpperLimit (7U)

#  ifndef Cx0_MovDir1_Ukwn
#   define Cx0_MovDir1_Ukwn (0U)
#  endif

#  ifndef Cx1_MovDir1_Standstill
#   define Cx1_MovDir1_Standstill (1U)
#  endif

#  ifndef Cx2_MovDir1_NotStandstill
#   define Cx2_MovDir1_NotStandstill (2U)
#  endif

#  ifndef Cx3_MovDir1_Fwd
#   define Cx3_MovDir1_Fwd (3U)
#  endif

#  ifndef Cx4_MovDir1_Rvs
#   define Cx4_MovDir1_Rvs (4U)
#  endif

#  ifndef Cx5_MovDir1_Err
#   define Cx5_MovDir1_Err (5U)
#  endif

#  define PrimVehSpdGroupSafeMovDirQf_adt_LowerLimit (0U)
#  define PrimVehSpdGroupSafeMovDirQf_adt_UpperLimit (3U)

#  define PrimVehSpdGroupSafeNomQf_adt_LowerLimit (0U)
#  define PrimVehSpdGroupSafeNomQf_adt_UpperLimit (3U)

#  define PrimWhlAgSpdFrntSafeLeQf_adt_LowerLimit (0U)
#  define PrimWhlAgSpdFrntSafeLeQf_adt_UpperLimit (3U)

#  define PrimWhlAgSpdFrntSafeRiQf_adt_LowerLimit (0U)
#  define PrimWhlAgSpdFrntSafeRiQf_adt_UpperLimit (3U)

#  define PrimWhlAgSpdReSafeLeQf_adt_LowerLimit (0U)
#  define PrimWhlAgSpdReSafeLeQf_adt_UpperLimit (3U)

#  define PrimWhlAgSpdReSafeRiQf_adt_LowerLimit (0U)
#  define PrimWhlAgSpdReSafeRiQf_adt_UpperLimit (3U)

#  ifndef Cx0_WhlRotlDirStd1_Undefd
#   define Cx0_WhlRotlDirStd1_Undefd (0U)
#  endif

#  ifndef Cx1_WhlRotlDirStd1_StandStill
#   define Cx1_WhlRotlDirStd1_StandStill (1U)
#  endif

#  ifndef Cx2_WhlRotlDirStd1_Fwd
#   define Cx2_WhlRotlDirStd1_Fwd (2U)
#  endif

#  ifndef Cx3_WhlRotlDirStd1_Backw
#   define Cx3_WhlRotlDirStd1_Backw (3U)
#  endif

#  define PrimYawRateSafeMinMaxQf_adt_LowerLimit (0U)
#  define PrimYawRateSafeMinMaxQf_adt_UpperLimit (3U)

#  define PrimYawRateSafeNomQf_adt_LowerLimit (0U)
#  define PrimYawRateSafeNomQf_adt_UpperLimit (3U)

#  ifndef Cx0_PrpsnTqDirCpby1_Ukwn
#   define Cx0_PrpsnTqDirCpby1_Ukwn (0U)
#  endif

#  ifndef Cx1_PrpsnTqDirCpby1_FullFcn
#   define Cx1_PrpsnTqDirCpby1_FullFcn (1U)
#  endif

#  ifndef Cx2_PrpsnTqDirCpby1_FwdOnly
#   define Cx2_PrpsnTqDirCpby1_FwdOnly (2U)
#  endif

#  ifndef Cx3_PrpsnTqDirCpby1_RvsOnly
#   define Cx3_PrpsnTqDirCpby1_RvsOnly (3U)
#  endif

#  ifndef Cx4_PrpsnTqDirCpby1_NoFcn
#   define Cx4_PrpsnTqDirCpby1_NoFcn (4U)
#  endif

#  ifndef Cx0_PrpsnDirAct1_Ukwn
#   define Cx0_PrpsnDirAct1_Ukwn (0U)
#  endif

#  ifndef Cx1_PrpsnDirAct1_Fwd
#   define Cx1_PrpsnDirAct1_Fwd (1U)
#  endif

#  ifndef Cx2_PrpsnDirAct1_Rvs
#   define Cx2_PrpsnDirAct1_Rvs (2U)
#  endif

#  ifndef Cx3_PrpsnDirAct1_ZeroTorque
#   define Cx3_PrpsnDirAct1_ZeroTorque (3U)
#  endif

#  ifndef Cx0_SSMBDegraded_NoChAvl
#   define Cx0_SSMBDegraded_NoChAvl (0U)
#  endif

#  ifndef Cx1_SSMBDegraded_DegradedModRemnNCh
#   define Cx1_SSMBDegraded_DegradedModRemnNCh (1U)
#  endif

#  ifndef Cx2_SSMBDegraded_SngChAvl
#   define Cx2_SSMBDegraded_SngChAvl (2U)
#  endif

#  ifndef Cx3_SSMBDegraded_FullAvl
#   define Cx3_SSMBDegraded_FullAvl (3U)
#  endif

#  ifndef Cx4_SSMBDegraded_RdnChAvl
#   define Cx4_SSMBDegraded_RdnChAvl (4U)
#  endif

#  ifndef Cx0_SSMDegraded_NoChAvl
#   define Cx0_SSMDegraded_NoChAvl (0U)
#  endif

#  ifndef Cx1_SSMDegraded_DegradedModRemnCh
#   define Cx1_SSMDegraded_DegradedModRemnCh (1U)
#  endif

#  ifndef Cx2_SSMDegraded_SngChAvl
#   define Cx2_SSMDegraded_SngChAvl (2U)
#  endif

#  ifndef Cx3_SSMDegraded_FullAvl
#   define Cx3_SSMDegraded_FullAvl (3U)
#  endif

#  ifndef Cx4_SSMDegraded_RdnChAvl
#   define Cx4_SSMDegraded_RdnChAvl (4U)
#  endif

#  define SecPoseMonSafeSafeQf_adt_LowerLimit (0U)
#  define SecPoseMonSafeSafeQf_adt_UpperLimit (3U)

#  define SecSteerMotTqSteerMotTqQf_adt_LowerLimit (0U)
#  define SecSteerMotTqSteerMotTqQf_adt_UpperLimit (3U)

#  ifndef Cx0_WhlLockDegraded1_Ukwn
#   define Cx0_WhlLockDegraded1_Ukwn (0U)
#  endif

#  ifndef Cx1_WhlLockDegraded1_FullFcn
#   define Cx1_WhlLockDegraded1_FullFcn (1U)
#  endif

#  ifndef Cx2_WhlLockDegraded1_DegradedFcn
#   define Cx2_WhlLockDegraded1_DegradedFcn (2U)
#  endif

#  ifndef Cx3_WhlLockDegraded1_NoFcn
#   define Cx3_WhlLockDegraded1_NoFcn (3U)
#  endif

#  ifndef Cx0_WhlLockSts1_Ukwn
#   define Cx0_WhlLockSts1_Ukwn (0U)
#  endif

#  ifndef Cx1_WhlLockSts1_AllWhlsReld
#   define Cx1_WhlLockSts1_AllWhlsReld (1U)
#  endif

#  ifndef Cx2_WhlLockSts1_FrntWhlsLockd
#   define Cx2_WhlLockSts1_FrntWhlsLockd (2U)
#  endif

#  ifndef Cx3_WhlLockSts1_ReWhlsLockd
#   define Cx3_WhlLockSts1_ReWhlsLockd (3U)
#  endif

#  ifndef Cx4_WhlLockSts1_AllWhlsLockd
#   define Cx4_WhlLockSts1_AllWhlsLockd (4U)
#  endif

#  ifndef Cx0_VehHldSts1_NotActv
#   define Cx0_VehHldSts1_NotActv (0U)
#  endif

#  ifndef Cx1_VehHldSts1_SoftStop
#   define Cx1_VehHldSts1_SoftStop (1U)
#  endif

#  ifndef Cx2_VehHldSts1_DrvrOffRels
#   define Cx2_VehHldSts1_DrvrOffRels (2U)
#  endif

#  ifndef Cx3_VehHldSts1_HldActv
#   define Cx3_VehHldSts1_HldActv (3U)
#  endif

#  ifndef Cx4_VehHldSts1_Resd
#   define Cx4_VehHldSts1_Resd (4U)
#  endif

#  define SteerWhlSnsrQf_adt_LowerLimit (0U)
#  define SteerWhlSnsrQf_adt_UpperLimit (3U)

#  define SteerWhlTqQf_adt_LowerLimit (0U)
#  define SteerWhlTqQf_adt_UpperLimit (3U)

#  ifndef Cx0_LiExtFctReq1_Off
#   define Cx0_LiExtFctReq1_Off (0U)
#  endif

#  ifndef Cx1_LiExtFctReq1_Pos
#   define Cx1_LiExtFctReq1_Pos (1U)
#  endif

#  ifndef Cx2_LiExtFctReq1_Lo
#   define Cx2_LiExtFctReq1_Lo (2U)
#  endif

#  ifndef Cx3_LiExtFctReq1_AutLi
#   define Cx3_LiExtFctReq1_AutLi (3U)
#  endif

#  ifndef Cx0_IndcrTypExt1_Off
#   define Cx0_IndcrTypExt1_Off (0U)
#  endif

#  ifndef Cx1_IndcrTypExt1_Le
#   define Cx1_IndcrTypExt1_Le (1U)
#  endif

#  ifndef Cx2_IndcrTypExt1_Ri
#   define Cx2_IndcrTypExt1_Ri (2U)
#  endif

#  ifndef Cx0_TirePWarn_NoWarn
#   define Cx0_TirePWarn_NoWarn (0U)
#  endif

#  ifndef Cx1_TirePWarn_SoftWarn
#   define Cx1_TirePWarn_SoftWarn (1U)
#  endif

#  ifndef Cx2_TirePWarn_HardWarn
#   define Cx2_TirePWarn_HardWarn (2U)
#  endif

#  ifndef Cx3_TirePWarn_ResdWarn
#   define Cx3_TirePWarn_ResdWarn (3U)
#  endif

#  ifndef Cx0_TirePWarnType_NoWarn
#   define Cx0_TirePWarnType_NoWarn (0U)
#  endif

#  ifndef Cx1_TirePWarnType_iTPMSWarn
#   define Cx1_TirePWarnType_iTPMSWarn (1U)
#  endif

#  ifndef Cx2_TirePWarnType_dTPMSWarn
#   define Cx2_TirePWarnType_dTPMSWarn (2U)
#  endif

#  ifndef Cx3_TirePWarnType_FullWarn
#   define Cx3_TirePWarnType_FullWarn (3U)
#  endif

#  ifndef Cx0_TrlrM_Lvl0
#   define Cx0_TrlrM_Lvl0 (0U)
#  endif

#  ifndef Cx1_TrlrM_Lvl1
#   define Cx1_TrlrM_Lvl1 (1U)
#  endif

#  ifndef Cx2_TrlrM_Lvl2
#   define Cx2_TrlrM_Lvl2 (2U)
#  endif

#  ifndef Cx3_TrlrM_Lvl3
#   define Cx3_TrlrM_Lvl3 (3U)
#  endif

#  define SAS_SteerWheelAngle_adt_LowerLimit (-7800)
#  define SAS_SteerWheelAngle_adt_UpperLimit (32767)

#  ifndef Cx7FFF_INVALID
#   define Cx7FFF_INVALID (32767)
#  endif

#  define SAS_SteerWheelRotSpd_adt_LowerLimit (0U)
#  define SAS_SteerWheelRotSpd_adt_UpperLimit (255U)

#  ifndef CxFF_INVALID
#   define CxFF_INVALID (255U)
#  endif

#  ifndef Cx0_BeamHiFctReq1_Neut
#   define Cx0_BeamHiFctReq1_Neut (0U)
#  endif

#  ifndef Cx1_BeamHiFctReq1_Flash
#   define Cx1_BeamHiFctReq1_Flash (1U)
#  endif

#  ifndef Cx2_BeamHiFctReq1_HiBeam
#   define Cx2_BeamHiFctReq1_HiBeam (2U)
#  endif

#  ifndef Cx3_BeamHiFctReq1_LoBeam
#   define Cx3_BeamHiFctReq1_LoBeam (3U)
#  endif

#  ifndef Cx0_PsdNotPsd_NotPsd
#   define Cx0_PsdNotPsd_NotPsd (0U)
#  endif

#  ifndef Cx1_PsdNotPsd_Psd
#   define Cx1_PsdNotPsd_Psd (1U)
#  endif

#  ifndef Cx00_TrsmFltIndcn1_TrsmFltIndcnNoReq
#   define Cx00_TrsmFltIndcn1_TrsmFltIndcnNoReq (0U)
#  endif

#  ifndef Cx01_TrsmFltIndcn1_TrsmFltIndcn1
#   define Cx01_TrsmFltIndcn1_TrsmFltIndcn1 (1U)
#  endif

#  ifndef Cx02_TrsmFltIndcn1_TrsmFltIndcn2
#   define Cx02_TrsmFltIndcn1_TrsmFltIndcn2 (2U)
#  endif

#  ifndef Cx03_TrsmFltIndcn1_TrsmFltIndcn3
#   define Cx03_TrsmFltIndcn1_TrsmFltIndcn3 (3U)
#  endif

#  ifndef Cx04_TrsmFltIndcn1_TrsmFltIndcn4
#   define Cx04_TrsmFltIndcn1_TrsmFltIndcn4 (4U)
#  endif

#  ifndef Cx05_TrsmFltIndcn1_TrsmFltIndcn5
#   define Cx05_TrsmFltIndcn1_TrsmFltIndcn5 (5U)
#  endif

#  ifndef Cx06_TrsmFltIndcn1_TrsmFltIndcn6
#   define Cx06_TrsmFltIndcn1_TrsmFltIndcn6 (6U)
#  endif

#  ifndef Cx07_TrsmFltIndcn1_TrsmFltIndcn7
#   define Cx07_TrsmFltIndcn1_TrsmFltIndcn7 (7U)
#  endif

#  ifndef Cx08_TrsmFltIndcn1_TrsmFltIndcn8
#   define Cx08_TrsmFltIndcn1_TrsmFltIndcn8 (8U)
#  endif

#  ifndef Cx09_TrsmFltIndcn1_TrsmFltIndcn9
#   define Cx09_TrsmFltIndcn1_TrsmFltIndcn9 (9U)
#  endif

#  ifndef Cx0A_TrsmFltIndcn1_TrsmFltIndcn10
#   define Cx0A_TrsmFltIndcn1_TrsmFltIndcn10 (10U)
#  endif

#  ifndef Cx0B_TrsmFltIndcn1_TrsmFltIndcn11
#   define Cx0B_TrsmFltIndcn1_TrsmFltIndcn11 (11U)
#  endif

#  ifndef Cx0C_TrsmFltIndcn1_TrsmFltIndcn12
#   define Cx0C_TrsmFltIndcn1_TrsmFltIndcn12 (12U)
#  endif

#  ifndef Cx0D_TrsmFltIndcn1_TrsmFltIndcn13
#   define Cx0D_TrsmFltIndcn1_TrsmFltIndcn13 (13U)
#  endif

#  ifndef Cx0E_TrsmFltIndcn1_TrsmFltIndcn14
#   define Cx0E_TrsmFltIndcn1_TrsmFltIndcn14 (14U)
#  endif

#  ifndef Cx0F_TrsmFltIndcn1_TrsmFltIndcn15
#   define Cx0F_TrsmFltIndcn1_TrsmFltIndcn15 (15U)
#  endif

#  ifndef Cx10_TrsmFltIndcn1_TrsmFltIndcn16
#   define Cx10_TrsmFltIndcn1_TrsmFltIndcn16 (16U)
#  endif

#  ifndef Cx11_TrsmFltIndcn1_TrsmFltIndcn17
#   define Cx11_TrsmFltIndcn1_TrsmFltIndcn17 (17U)
#  endif

#  ifndef Cx12_TrsmFltIndcn1_TrsmFltIndcn18
#   define Cx12_TrsmFltIndcn1_TrsmFltIndcn18 (18U)
#  endif

#  ifndef Cx13_TrsmFltIndcn1_TrsmFltIndcn19
#   define Cx13_TrsmFltIndcn1_TrsmFltIndcn19 (19U)
#  endif

#  ifndef Cx14_TrsmFltIndcn1_TrsmFltIndcn20
#   define Cx14_TrsmFltIndcn1_TrsmFltIndcn20 (20U)
#  endif

#  ifndef Cx15_TrsmFltIndcn1_TrsmFltIndcn21
#   define Cx15_TrsmFltIndcn1_TrsmFltIndcn21 (21U)
#  endif

#  ifndef Cx16_TrsmFltIndcn1_TrsmFltIndcn22
#   define Cx16_TrsmFltIndcn1_TrsmFltIndcn22 (22U)
#  endif

#  ifndef Cx17_TrsmFltIndcn1_TrsmFltIndcn23
#   define Cx17_TrsmFltIndcn1_TrsmFltIndcn23 (23U)
#  endif

#  ifndef Cx18_TrsmFltIndcn1_TrsmFltIndcn24
#   define Cx18_TrsmFltIndcn1_TrsmFltIndcn24 (24U)
#  endif

#  ifndef Cx19_TrsmFltIndcn1_TrsmFltIndcn25
#   define Cx19_TrsmFltIndcn1_TrsmFltIndcn25 (25U)
#  endif

#  ifndef Cx1A_TrsmFltIndcn1_TrsmFltIndcn26
#   define Cx1A_TrsmFltIndcn1_TrsmFltIndcn26 (26U)
#  endif

#  ifndef Cx1B_TrsmFltIndcn1_TrsmFltIndcn27
#   define Cx1B_TrsmFltIndcn1_TrsmFltIndcn27 (27U)
#  endif

#  ifndef Cx1C_TrsmFltIndcn1_TrsmFltIndcn28
#   define Cx1C_TrsmFltIndcn1_TrsmFltIndcn28 (28U)
#  endif

#  ifndef Cx1D_TrsmFltIndcn1_TrsmFltIndcn29
#   define Cx1D_TrsmFltIndcn1_TrsmFltIndcn29 (29U)
#  endif

#  ifndef Cx1E_TrsmFltIndcn1_TrsmFltIndcn30
#   define Cx1E_TrsmFltIndcn1_TrsmFltIndcn30 (30U)
#  endif

#  ifndef Cx1F_TrsmFltIndcn1_TrsmFltIndcn31
#   define Cx1F_TrsmFltIndcn1_TrsmFltIndcn31 (31U)
#  endif

#  ifndef Cx0_VehManDeactvnReqInProgs_NoReq
#   define Cx0_VehManDeactvnReqInProgs_NoReq (0U)
#  endif

#  ifndef Cx1_VehManDeactvnReqInProgs_BgnStopReq
#   define Cx1_VehManDeactvnReqInProgs_BgnStopReq (1U)
#  endif

#  ifndef Cx2_VehManDeactvnReqInProgs_StopReqd
#   define Cx2_VehManDeactvnReqInProgs_StopReqd (2U)
#  endif

#  ifndef Cx3_VehManDeactvnReqInProgs_Resd
#   define Cx3_VehManDeactvnReqInProgs_Resd (3U)
#  endif

#  ifndef Cx0_ExtModSts_NoMod
#   define Cx0_ExtModSts_NoMod (0U)
#  endif

#  ifndef Cx1_ExtModSts_IniMod
#   define Cx1_ExtModSts_IniMod (1U)
#  endif

#  ifndef Cx2_ExtModSts_ClimaMod
#   define Cx2_ExtModSts_ClimaMod (2U)
#  endif

#  ifndef Cx3_ExtModSts_RadioMod
#   define Cx3_ExtModSts_RadioMod (3U)
#  endif

#  ifndef Cx4_ExtModSts_SrvMod
#   define Cx4_ExtModSts_SrvMod (4U)
#  endif

#  ifndef Cx5_ExtModSts_PrpsnActvMod
#   define Cx5_ExtModSts_PrpsnActvMod (5U)
#  endif

#  ifndef Cx0_EradCluSts1_CluOpen
#   define Cx0_EradCluSts1_CluOpen (0U)
#  endif

#  ifndef Cx1_EradCluSts1_CluClsd
#   define Cx1_EradCluSts1_CluClsd (1U)
#  endif

#  ifndef Cx0_WipgSpd2_WipgSpd0Rpm
#   define Cx0_WipgSpd2_WipgSpd0Rpm (0U)
#  endif

#  ifndef Cx1_WipgSpd2_WipgSpd40Rpm
#   define Cx1_WipgSpd2_WipgSpd40Rpm (1U)
#  endif

#  ifndef Cx2_WipgSpd2_WipgSpd43Rpm
#   define Cx2_WipgSpd2_WipgSpd43Rpm (2U)
#  endif

#  ifndef Cx3_WipgSpd2_WipgSpd46Rpm
#   define Cx3_WipgSpd2_WipgSpd46Rpm (3U)
#  endif

#  ifndef Cx4_WipgSpd2_WipgSpd50Rpm
#   define Cx4_WipgSpd2_WipgSpd50Rpm (4U)
#  endif

#  ifndef Cx5_WipgSpd2_WipgSpd54Rpm
#   define Cx5_WipgSpd2_WipgSpd54Rpm (5U)
#  endif

#  ifndef Cx6_WipgSpd2_WipgSpd57Rpm
#   define Cx6_WipgSpd2_WipgSpd57Rpm (6U)
#  endif

#  ifndef Cx7_WipgSpd2_WipgSpd60Rpm
#   define Cx7_WipgSpd2_WipgSpd60Rpm (7U)
#  endif

#  ifndef Cx0_WipgAutFrntMod_Off
#   define Cx0_WipgAutFrntMod_Off (0U)
#  endif

#  ifndef Cx1_WipgAutFrntMod_ImdtMod
#   define Cx1_WipgAutFrntMod_ImdtMod (1U)
#  endif

#  ifndef Cx2_WipgAutFrntMod_IntlMod
#   define Cx2_WipgAutFrntMod_IntlMod (2U)
#  endif

#  ifndef Cx3_WipgAutFrntMod_ContnsMod
#   define Cx3_WipgAutFrntMod_ContnsMod (3U)
#  endif

# endif /* RTE_CORE */

# ifdef __cplusplus
} /* extern "C" */
# endif /* __cplusplus */

#endif /* RTE_VEHSIGINPUT_TYPE_H */
