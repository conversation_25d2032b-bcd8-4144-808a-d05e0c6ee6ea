"""checkpoint."""
from airflow.exceptions import AirflowFailException
from airflow.utils.state import State
from airflow.operators.python import PythonOperator


class Checkpoint(object):
    """Checkpoint class to record output"""

    def __init__(self, task_group_name, ignore_fail_task=None):
        self.task_group_name = task_group_name
        self.ignore_fail_task = ignore_fail_task

    def check(self, **context):
        task_outputs = {}
        error_info = {}

        upstream_tasks = {
            task.task_id: task
            for task in context["dag_run"].get_task_instances()
            if task.task_id.startswith(self.task_group_name)
            and not task.task_id.endswith("checkpoint")
        }

        ckpt_state = State.SUCCESS
        current_task = context["ti"]
        upstream_states = []
        for task_id, task in upstream_tasks.items():
            task_output = current_task.xcom_pull(key="output", task_ids=task_id)
            if task_output:
                task_outputs.update(task_output)

            if task.state == State.FAILED:
                failures = current_task.xcom_pull(key="failures", task_ids=task_id)
                if failures:
                    error_info[task_id] = failures
            if task.state != State.FAILED or (
                self.ignore_fail_task and task_id not in self.ignore_fail_task
            ):
                upstream_states.append(task.state)

        context["ti"].xcom_push(key="output", value=task_outputs)
        context["ti"].xcom_push("error_info", value=error_info)

        if all([state == State.SUCCESS for state in upstream_states]):
            ckpt_state = State.SUCCESS
        elif all([state == State.SKIPPED for state in upstream_states]):
            ckpt_state = State.SKIPPED
        elif any([state == State.UPSTREAM_FAILED for state in upstream_states]):
            ckpt_state = State.UPSTREAM_FAILED
        elif any([state == State.FAILED for state in upstream_states]):
            ckpt_state = State.FAILED
        else:
            ckpt_state = State.SUCCESS

        if ckpt_state in [State.FAILED, State.UPSTREAM_FAILED]:
            raise AirflowFailException

    def op(self, dag):
        """Wraps the checkpoint function as Airflow op."""
        return PythonOperator(
            task_id="checkpoint",
            dag=dag,
            python_callable=self.check,
            trigger_rule="all_done",
            provide_context=True,
        )
