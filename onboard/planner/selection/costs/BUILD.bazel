load("//bazel:defs.bzl", "voy_cc_test")

package(default_visibility = ["//visibility:public"])

cc_library(
    name = "cost_registry",
    srcs = ["cost_registry.cpp"],
    hdrs = [
        "cost_registry.h",
    ],
    include_prefix = "planner/selection/costs",
    deps = [
        "//onboard/planner/selection:trajectory_meta",
        "//protobuf_cpp:protos_cpp",
    ],
)

cc_library(
    name = "short_path_cost",
    srcs = [
        "short_path_cost.cpp",
    ],
    hdrs = [
        "short_path_cost.h",
    ],
    include_prefix = "planner/selection/costs",
    deps = [
        "//onboard/planner:constants",
        "//onboard/planner/selection:trajectory_meta",
    ],
)

cc_library(
    name = "speed_diversity_cost",
    srcs = [
        "speed_diversity_cost.cpp",
    ],
    hdrs = [
        "speed_diversity_cost.h",
    ],
    include_prefix = "planner/selection/costs",
    deps = [
        "//onboard/planner:voy_rt_event_planner",
        "//onboard/planner/selection:trajectory_meta",
        "//onboard/planner/speed/profile",
    ],
)

voy_cc_test(
    name = "cost_registry_test",
    srcs = [
        "cost_registry_test.cpp",
    ],
    deps = [
        ":cost_registry",
        "//onboard/planner/selection/test:selection_test_fixture",
        "@voy-sdk//:gtest",
    ],
)

cc_library(
    name = "longitudinal_diff_cost",
    srcs = [
        "longitudinal_diff_cost.cpp",
    ],
    hdrs = [
        "longitudinal_diff_cost.h",
    ],
    include_prefix = "planner/selection/costs",
    deps = [
        "//onboard/planner/selection:trajectory_meta",
    ],
)

cc_library(
    name = "mrc_latch_cost",
    srcs = [
        "mrc_latch_cost.cpp",
    ],
    hdrs = [
        "mrc_latch_cost.h",
    ],
    include_prefix = "planner/selection/costs",
    deps = [
        "//onboard/planner/selection:trajectory_meta",
    ],
)

voy_cc_test(
    name = "longitudinal_diff_cost_test",
    srcs = [
        "longitudinal_diff_cost_test.cpp",
    ],
    deps = [
        ":longitudinal_diff_cost",
        "//onboard/planner/selection/test:selection_test_fixture",
        "@voy-sdk//:gtest",
    ],
)

cc_library(
    name = "safety_precheck_cost",
    srcs = [
        "safety_precheck_cost.cpp",
    ],
    hdrs = [
        "safety_precheck_cost.h",
    ],
    include_prefix = "planner/selection/costs",
    deps = [
        ":cost_registry",
        "//onboard/planner/selection:trajectory_meta",
    ],
)

voy_cc_test(
    name = "safety_precheck_cost_test",
    srcs = [
        "safety_precheck_cost_test.cpp",
    ],
    deps = [
        ":safety_precheck_cost",
        "//onboard/planner/selection/test:selection_test_fixture",
        "@voy-sdk//:gtest",
    ],
)

cc_library(
    name = "progress_cost",
    srcs = [
        "progress_cost.cpp",
    ],
    hdrs = [
        "progress_cost.h",
    ],
    include_prefix = "planner/selection/costs",
    deps = [
        "//onboard/planner/selection:trajectory_meta",
    ],
)

voy_cc_test(
    name = "progress_cost_test",
    srcs = [
        "progress_cost_test.cpp",
    ],
    deps = [
        ":progress_cost",
        "//onboard/planner/selection/test:selection_test_fixture",
        "@voy-sdk//:gtest",
    ],
)

cc_library(
    name = "traffic_rule_cost",
    srcs = [
        "traffic_rule_cost.cpp",
    ],
    hdrs = [
        "traffic_rule_cost.h",
    ],
    include_prefix = "planner/selection/costs",
    deps = [
        "//onboard/planner/selection:trajectory_meta",
        "//onboard/planner/utility/stuck_signal:stuck_detector_utils",
        "@voy-sdk//:absl-strings",
    ],
)

voy_cc_test(
    name = "traffic_rule_cost_test",
    srcs = [
        "traffic_rule_cost_test.cpp",
    ],
    deps = [
        ":traffic_rule_cost",
        "//onboard/planner/selection/test:selection_test_fixture",
        "@voy-sdk//:gtest",
    ],
)

cc_library(
    name = "lateral_discomfort_cost",
    srcs = [
        "lateral_discomfort_cost.cpp",
    ],
    hdrs = [
        "lateral_discomfort_cost.h",
    ],
    include_prefix = "planner/selection/costs",
    deps = [
        "//onboard/common/math",
        "//onboard/planner:voy_planner_gflags",
        "//onboard/planner/selection:trajectory_meta",
        "//protobuf_cpp:protos_cpp",
    ],
)

voy_cc_test(
    name = "lateral_discomfort_cost_test",
    srcs = [
        "lateral_discomfort_cost_test.cpp",
    ],
    deps = [
        ":lateral_discomfort_cost",
        "//onboard/planner/selection/test:selection_test_fixture",
        "@voy-sdk//:gtest",
    ],
)

cc_library(
    name = "longitudinal_discomfort_cost",
    srcs = [
        "longitudinal_discomfort_cost.cpp",
    ],
    hdrs = [
        "longitudinal_discomfort_cost.h",
    ],
    include_prefix = "planner/selection/costs",
    deps = [
        "//onboard/planner/selection:trajectory_meta",
        "//protobuf_cpp:protos_cpp",
    ],
)

voy_cc_test(
    name = "longitudinal_discomfort_cost_test",
    srcs = [
        "longitudinal_discomfort_cost_test.cpp",
    ],
    deps = [
        ":longitudinal_discomfort_cost",
        "//onboard/planner/selection:trajectory_meta",
        "//onboard/planner/selection/test:selection_test_fixture",
        "//protobuf_cpp:protos_cpp",
        "@voy-sdk//:gtest",
    ],
)

cc_library(
    name = "lateral_diff_cost",
    srcs = [
        "lateral_diff_cost.cpp",
    ],
    hdrs = [
        "lateral_diff_cost.h",
    ],
    include_prefix = "planner/selection/costs",
    deps = [
        "//onboard/common/math",
        "//onboard/planner:voy_planner_gflags",
        "//onboard/planner/selection:trajectory_meta",
        "//protobuf_cpp:protos_cpp",
    ],
)

voy_cc_test(
    name = "lateral_diff_cost_test",
    srcs = [
        "lateral_diff_cost_test.cpp",
    ],
    deps = [
        ":lateral_diff_cost",
        "//onboard/planner/selection/test:selection_test_fixture",
        "@voy-sdk//:gtest",
    ],
)

cc_library(
    name = "path_diff_cost",
    srcs = [
        "path_diff_cost.cpp",
    ],
    hdrs = [
        "path_diff_cost.h",
    ],
    include_prefix = "planner/selection/costs",
    deps = [
        "//onboard/common/math",
        "//onboard/planner/selection:trajectory_meta",
        "//protobuf_cpp:protos_cpp",
    ],
)

cc_library(
    name = "hard_boundary_risk_cost",
    srcs = [
        "hard_boundary_risk_cost.cpp",
    ],
    hdrs = [
        "hard_boundary_risk_cost.h",
    ],
    include_prefix = "planner/selection/costs",
    deps = [
        "//onboard/common/math",
        "//onboard/planner/selection:trajectory_meta",
        "//protobuf_cpp:protos_cpp",
    ],
)

cc_library(
    name = "lane_marking_cost",
    srcs = [
        "lane_marking_cost.cpp",
    ],
    hdrs = [
        "lane_marking_cost.h",
    ],
    include_prefix = "planner/selection/costs",
    deps = [
        "//onboard/common/math",
        "//onboard/planner/selection:trajectory_meta",
        "//protobuf_cpp:protos_cpp",
    ],
)

cc_library(
    name = "last_path_cost",
    srcs = [
        "last_path_cost.cpp",
    ],
    hdrs = [
        "last_path_cost.h",
    ],
    include_prefix = "planner/selection/costs",
    deps = [
        "//onboard/common/math",
        "//onboard/planner/selection:trajectory_meta",
        "//protobuf_cpp:protos_cpp",
    ],
)

cc_library(
    name = "routing_v2_cost",
    srcs = [
        "routing_v2_cost.cpp",
    ],
    hdrs = [
        "routing_v2_cost.h",
    ],
    include_prefix = "planner/selection/costs",
    deps = [
        "//onboard/common/math",
        "//onboard/planner/selection:trajectory_meta",
        "//onboard/planner/selection/scenario_selectors:selector_utils",
        "//protobuf_cpp:protos_cpp",
    ],
)
