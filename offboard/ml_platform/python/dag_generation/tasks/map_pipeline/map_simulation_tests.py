"""map simulation tests"""
from airflow.utils.task_group import TaskGroup
from airflow.operators.python import PythonOperator
from airflow.exceptions import AirflowSkipException

from dag_generation.constants import TaskType
from dag_generation.tasks.abstract_task import AbstractTask
from dag_generation.tasks.task_factory import register

from dag_generation.tasks.map_pipeline.checkpoint import Checkpoint
from dataloop.utils.cr_utils import <PERSON><PERSON><PERSON>, trigger_buildbots, trigger_simplan


_GROUP_NAME = TaskType.MAP_SIMULATION_TESTS


target_path_platform = {
    "./map_models/online_map_detector_fusion.onnx": "onnx",
    "./map_models/online_map_detector_fusion_pg189.ifxmodel": "fp16_gen3",
    "./map_models/online_map_detector_fusion_rtx30xx.ifxmodel": "fp16_3060",
    "./map_models/online_map_detector_fusion_l20.ifxmodel": "fp16_l20",
    "./map_models/online_map_detector_fusion_gen4.ifxmodel": "fp16_gen4",
    "./map_models/online_map_detector_fusion_x86.ifxmodel": "fp32_x86",
}


@register(TaskType.MAP_SIMULATION_TESTS)
class MapSimulationTestsTask(AbstractTask):
    """map simulation tests task"""

    def _is_skip(self, **context):
        ifx_mapping = context["ti"].xcom_pull(
            task_ids="map_model_deploy.ifx_mapping", key="ifx_mapping"
        )
        if not ifx_mapping:
            raise AirflowSkipException

    def _create_cr(self, **context):
        ifx_mapping = context["ti"].xcom_pull(
            task_ids="map_model_deploy.ifx_mapping", key="ifx_mapping"
        )
        update_info = {
            "module": "map.model-files",
            "target_path_platform": target_path_platform,
            "ifx_mapping": ifx_mapping,
        }
        cr = TriggerCR(update_info)
        kunpeng_cr_link = cr.do()
        context["ti"].xcom_push(key="kunpeng_cr_link", value=kunpeng_cr_link)
        output = {"kunpeng_cr_link": kunpeng_cr_link}
        context["ti"].xcom_push(key="output", value=output)

    def _trigger_buildbot(self, **context):
        kunpeng_cr_link = context["ti"].xcom_pull(
            task_ids=f"{_GROUP_NAME}.create_cr",
            key="kunpeng_cr_link",
        )
        kunpeng_id = kunpeng_cr_link.rsplit("/", 1)[-1]
        username = context["dag_run"].conf.get("user_name")
        buildbots = [
            {
                "type": "ci:trigger_airflow_dag",
                "name": "online_map_regression_test",
                "params": {
                    "custom args": "--enabled-subtasks gen3_normal gen3_golden",
                    "is_loop1": "true",
                    "upload_output_bag": "always",
                    "compared_with_master": "true",
                },
            },
            {
                "type": "ci:pop_exec",
                "name": "voyager_performance_test_gen3",
                "params": {
                    "select_scenario_labels": "perception_gen3_perf_test",
                    "Enable ACU-in-loop PRT?": "No. (Only Perf Test.)",
                    "Enable ML-PLANNER trajectory?": "No. (Use Planning trajectory.)",
                    "Upload PerfTest bags to Web-Monitor?": "No.",
                    "select_base_version": "Latest_Master",
                },
            },
            {
                "type": "ci:trigger_airflow_dag",
                "name": "localization_regression_test",
                "params": {
                    "custom args": """custom args=--sim-args localization.*.localization_eval_job.enabled_modules='["LOCALIZATION","SENSING","SEMANTIC_MAPPING"]' localization.*.localization_eval_job.disable_sim_cache_reader=true -m""",
                    "is_loop1": "true",
                    "upload_output_bag": "always",
                    "compared_with_master": "true",
                },
            },
            {
                "type": "ci:simulation_performance_compare_opt",
                "name": "ci_simulation_performance_compare_opt_select",
                "params": {
                    "default": "--without-perception",
                },
            },
        ]
        trigger_buildbots(kunpeng_id, username, buildbots)

    def _trigger_simplan(self, **context):
        kunpeng_cr_link = context["ti"].xcom_pull(
            task_ids=f"{_GROUP_NAME}.create_cr",
            key="kunpeng_cr_link",
        )
        kunpeng_id = kunpeng_cr_link.rsplit("/", 1)[-1]
        username = context["dag_run"].conf.get("user_name")

        sim_plans = {
            2589: "map_hb_polygon_border_change",
            3089: "map_lm_polygon_tp",
            2725: "map_lm_polygon_fp",
            2587: "map_hb_polygon_new",
            2591: "map_hb_polygon_new_fp",
            853: "localization_standard_test",
            2593: "map_hb_polygon_border_change_fp",
        }

        sim_plan_ids = list(sim_plans.keys())
        sim_params = {
            "is_loop1": False,
            "selected_priority": "1",
            "base_version": "Checkout_Point",
        }
        trigger_simplan(kunpeng_id, username, sim_plan_ids, sim_params)

    def gen_airflow_ops(self, dag, *args, **kwargs):
        with TaskGroup(_GROUP_NAME, dag=dag) as tg:
            is_skip_op = PythonOperator(
                task_id="is_skip",
                dag=dag,
                python_callable=self._is_skip,
                provide_context=True,
            )

            create_cr_op = PythonOperator(
                task_id="create_cr",
                dag=dag,
                python_callable=self._create_cr,
                provide_context=True,
            )

            trigger_buildbot_op = PythonOperator(
                task_id="trigger_buildbot",
                dag=dag,
                python_callable=self._trigger_buildbot,
                provide_context=True,
            )

            trigger_simplan_op = PythonOperator(
                task_id="trigger_simplan",
                dag=dag,
                python_callable=self._trigger_simplan,
                provide_context=True,
            )

            checkpoint_op = Checkpoint(_GROUP_NAME).op(dag)

            is_skip_op >> create_cr_op >> trigger_buildbot_op >> checkpoint_op
            create_cr_op >> trigger_simplan_op >> checkpoint_op

            return tg
