#include "common_math_library/sort.h"

#include <stdbool.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>

#include "common_math_library/assert_cfg.h"

static int compare_float_ascend(const void *a, const void *b) {
  float fa = *(const float *)a;
  float fb = *(const float *)b;
  return (fa > fb) - (fa < fb);
}

static int compare_float_descend(const void *a, const void *b) {
  float fa = *(const float *)a;
  float fb = *(const float *)b;
  return (fb > fa) - (fb < fa);
}

// sorts an array of floats in ascending or descending order
void CML_Sort(float *array, uint8_t num_of_elements, const char *direction) {
  if (strcmp(direction, "ascend") == 0) {
    // sort in ascending order
    qsort(array, num_of_elements, sizeof(float), compare_float_ascend);
  } else if (strcmp(direction, "descend") == 0) {
    // sort in descending order
    qsort(array, num_of_elements, sizeof(float), compare_float_descend);
  } else {
    // handle invalid direction
    CML_ASSERT(false);
  }
}
