#include "prediction/vectornet/vectornet_feature_extractor.h"

#include <algorithm>
#include <atomic>
#include <cmath>
#include <cstddef>
#include <cstdint>
#include <cstdlib>
#include <iterator>
#include <limits>
#include <memory>
#include <set>
#include <string>
#include <tuple>
#include <unordered_set>
#include <utility>
#include <vector>

#include <boost/functional/hash.hpp>
#include <glog/logging.h>
#include <math.h>
#include <tbb/parallel_for.h>

#include "base/base_dir.h"
#include "geometry/model/enums.h"
#include "geometry/model/oriented_box.h"
#include "geometry/model/point_2d.h"
#include "geometry/model/polygon_with_cache.h"
#include "geometry/model/polyline.h"
#include "gtl/map_util.h"
#include "math/math_util.h"
#include "pnc_map_service/map_elements/lane.h"
#include "prediction/classification/feature_extraction/feature_util.h"
#include "prediction/common/definition/prediction_constants.h"
#include "prediction/common/definition/prediction_gflag.h"
#include "prediction/common/utility/config_manager.h"
#include "prediction/scene_snapshot/route_type.h"
#include "prediction/scene_snapshot/scene_context.h"
#include "prediction/scene_snapshot/speed_limits.h"
#include "prediction/scene_snapshot/traffic_light_infer.h"
#include "prediction/vectornet/vectornet_feature_processor.h"
#include "prediction/vectornet/vectornet_sample.h"
#include "prediction/vectornet/vectornet_util.h"
#include "prediction_protos/map_object.pb.h"
#include "prediction_protos/prediction_feature_spec.pb.h"
#include "trace/trace.h"
#include "voy_trace/trace_prediction.h"
#include "voystat/stats_prediction.h"

namespace prediction {
namespace {

using ConstMapFeatureIterator =
    std::map<MapObjectKey, MapObjectFeature>::const_iterator;
using RepeatedAgentTags = google::protobuf::RepeatedField<int>;
using CheckMissOfMapKeyInCacheFunction =
    std::function<bool(pb::MapObject::MapObjectType, int64_t)>;

constexpr int kInvalidInputTensorIndex = -999;

// The number of input tensors for vectornet. These tensors are agents features,
// agents mask, map object features and map objects mask.
// Introduce mask rather than calculate them within model has the following
// advantages:
// 1) Brings bias to the polyline feature extractor.
// 2) Support dynamic batch size which can help handle different number of
// target agents.
// 3) Standard input shape makes it easier for pytorch-onnx model
// conversion.
constexpr int kNumInputTensorsForVectornet = 4;

// The additional number of input tensors for feature and mask;
constexpr int kNumInputTensorsForFeatureWithMask = 2;

// The additional number of input tensors for route feature and mask;
constexpr int kNumInputTensorsForRoute = 2;

// The index of agent mask in input tensor.
constexpr int kAgentMaskIndex = 1;
// The index of route mask in input tensor.
constexpr int kRouteMaskIndex = 5;
// The index of route mask in input tensor, if the skeleton feature is extracted
// as well.
constexpr int kRouteMaskIndexWithSkeletonEnabled = 7;

// Sample interval when getting sample points from map object line.
constexpr float kMapLineSampleInterval = 5.0;  // meter.

// The max length for ref line of small map objects.
constexpr float kMaxLineLengthForSmallMapObjects = 10.0;  // meter.

// Smaller sample interval when getting sample points from short map object
// lines.
constexpr float kLowerBoundOfMapObjectSampleInterval = 1.0;  // meter.

// The minimum sample interval for traffic cone or barrier.
constexpr float kMinTrafficConeOrBarrierSampleInterval = 0.2;  // meter.

// The minimum distance of a vector to insert extra points.
constexpr float kMinDistanceToPostProcessMapSegment = 5.0;  // meter.

// Distance to merge 2 continuous points from one map object.
constexpr float kMinDistanceToMergeContinuousMapPoints = 0.1;  // meter.

// Zone-like map objects should have enough points.
constexpr int kMinNumOfPointsForZone = 3;

// The index of ego agent in vectornet agent feature.
constexpr int kEgoAgentIndexInMarginalModel = 1;
constexpr int kEgoAgentIndexInPredictEgoModel = 0;

// The max distance range for ego route feature in the marginal/CBP model.
constexpr int kMaxDistanceRangeForEgoRoute = 500;  // meter.

// Corners point of agent to construct polyline representation.
const std::vector<math::geometry::Box2dCornerType> kAgentBoxCornerPointsType = {
    math::geometry::Box2dCornerType::kFrontLeft,
    math::geometry::Box2dCornerType::kRearLeft,
    math::geometry::Box2dCornerType::kRearRight};

// Line segment of the map object.
struct MapLineSegment {
  // x, y points of the map line segment.
  std::vector<math::geometry::Point2d> segment_points;

  // Map object type of the map line segment
  pb::MapObject::MapObjectType line_type = pb::MapObject::UNKNOWN_TYPE;

  // Turn type of lane.
  hdmap::Lane::Turn lane_turn_type = hdmap::Lane::UNKNOWN_TURN;

  // Speed limits of the map line segment. The length should be same as
  // segment_points or empty.
  std::vector<float> speed_limits;

  // AttributeType of left marking.
  std::vector<pb::MapObject::MapObjectType> left_marking_types;

  // AttributeType of right marking.
  std::vector<pb::MapObject::MapObjectType> right_marking_types;

  // Lane segment width.
  double lane_segment_width = -1;

  // x, y points of the left/right marking.
  std::vector<math::geometry::Point2d> left_marking_points;
  std::vector<math::geometry::Point2d> right_marking_points;
};

// Appends the first point to the end of the border points.
template <typename PolygonType>
math::geometry::PolylineCurve2d GetClosedBorderLine(const PolygonType& border) {
  DCHECK_GE(border.points().size(), kMinNumOfPointsForZone);

  std::vector<math::geometry::Point2d> closed_border_line;
  closed_border_line.reserve(border.points().size() + 1);
  for (const auto& point : border.points()) {
    if (closed_border_line.empty() ||
        math::geometry::ComparableDistance(closed_border_line.back(), point) >
            math::constants::kEpsilonForSq) {
      closed_border_line.emplace_back(
          math::geometry::Point2d(point.x(), point.y()));
    }
  }
  DCHECK(!closed_border_line.empty());
  if (math::geometry::ComparableDistance(closed_border_line.back(),
                                         closed_border_line[0]) >
      math::constants::kEpsilonForSq) {
    closed_border_line.emplace_back(closed_border_line[0]);
  }
  return math::geometry::PolylineCurve2d(std::move(closed_border_line));
}

// Sample line dynamically with different intervals determined by its total
// length.
std::vector<math::geometry::Point2d> GetDynamicSampledPoints(
    const math::geometry::PolylineCurve2d& line, float sample_interval) {
  return line.GetSampledPoints(0.0, line.GetTotalArcLength(), sample_interval,
                               math::pb::UseExtensionFlag::kForbid);
}

// Will update the dynamic sample interval by curvature in future.
// Get dynamic sample interval for map object line.
float GetDynamicSampleIntervalForMapObjectLine(
    const math::geometry::PolylineCurve2d& line) {
  // If the total arc length is less or equal to
  // kMaxLineLengthForSmallMapObjects(10m), we will use a much little sample
  // interval 1m instead of 5m.
  return line.GetTotalArcLength() <= kMaxLineLengthForSmallMapObjects
             ? kLowerBoundOfMapObjectSampleInterval
             : kMapLineSampleInterval;
}

// Get dense map object raw points with post-process.
std::vector<math::geometry::Point2d> GetDenseRawPointsWithPostProcess(
    const math::geometry::PolylineCurve2d& line, int num_max_points) {
  // If points from original object are little enough,
  // and the mean interval is small enough, we use these raw points directly.
  const std::vector<math::geometry::Point2d>& map_raw_points = line.points();
  const double total_length = line.GetTotalArcLength();
  const int num_raw_points = static_cast<int>(map_raw_points.size());
  // Return objects large enough or small enough.
  if (num_raw_points >= num_max_points ||
      total_length <= kLowerBoundOfMapObjectSampleInterval) {
    STATS_COUNTER2(prediction, vectornet,
                   num_map_objects_with_enough_raw_points, 1);
    return line.points();
  }

  // If an object has points less than num_max_points, we use uniform sample to
  // segments long enough to add more necessary points to keep resolution.
  std::vector<math::geometry::Point2d> res;
  res.push_back(map_raw_points[0]);
  const int num_extra_points = num_max_points - num_raw_points;
  DCHECK_GT(num_extra_points, 0);

  for (int i = 1; i < num_raw_points; i++) {
    const double dx = map_raw_points[i].x() - map_raw_points[i - 1].x();
    const double dy = map_raw_points[i].y() - map_raw_points[i - 1].y();
    const double segment_length = std::hypot(dx, dy);

    // Assign extra points to separate current segment by its portion of
    // length. The min resolution should be 1m anyway.
    const int num_extra_points_in_one_interval = std::min(
        static_cast<int>(segment_length / kLowerBoundOfMapObjectSampleInterval),
        static_cast<int>(segment_length / total_length * num_extra_points));

    // Add extra points only for segments that take a big enough part in total
    // so have positive valid num_extra_points_in_one_interval.
    if (segment_length > kMinDistanceToPostProcessMapSegment &&
        num_extra_points_in_one_interval > 0) {
      // K extra points cuts the segment to K+1 sub-segments.
      // x, y offset for each sub segment.
      const double delta_x = dx / (num_extra_points_in_one_interval + 1);
      const double delta_y = dy / (num_extra_points_in_one_interval + 1);
      // Add points between i-1 and i.
      for (int j = 1; j <= num_extra_points_in_one_interval; ++j) {
        res.emplace_back(map_raw_points[i - 1].x() + delta_x * j,
                         map_raw_points[i - 1].y() + delta_y * j);
      }
    }
    res.push_back(map_raw_points[i]);
  }
  DCHECK_LE(res.size(), num_max_points);
  STATS_COUNTER2(prediction, vectornet,
                 num_simple_simplified_map_objects_with_postprocess, 1);
  return res;
}

// Get points from lane from lane->center_line(), left_lane_marking_info and
// right_lane_marking_info. So we can get the points where lane marking type
// changes.
math::geometry::PolylineCurve2d GetMergedLanePolyline(
    const pnc_map::Lane* lane) {
  const math::geometry::PolylineCurve2d& lane_center_line = lane->center_line();
  const math::geometry::Polyline2d& center_line_points =
      lane_center_line.points();
  // Get points from lane_marking_info.
  auto GetPointsFromLaneMarkingInfoPoints =
      [lane_center_line](const std::vector<pnc_map::LaneMarkingSegmentInfo>&
                             lane_marking_infos) {
        std::vector<math::geometry::Point2d> lane_marking_info_points;
        lane_marking_info_points.reserve(lane_marking_infos.size());
        for (const auto& lane_marking_info : lane_marking_infos) {
          lane_marking_info_points.emplace_back(lane_center_line.GetInterp(
              lane_marking_info.start_pos,
              /*math::pb::UseExtensionFlag allow_extension_flag=*/
              math::pb::UseExtensionFlag::kForbid));
        }
        return lane_marking_info_points;
      };

  const std::vector<math::geometry::Point2d> left_lane_marking_info_points =
      GetPointsFromLaneMarkingInfoPoints(lane->left_lane_marking_info());
  const std::vector<math::geometry::Point2d> right_lane_marking_info_points =
      GetPointsFromLaneMarkingInfoPoints(lane->right_lane_marking_info());

  // Merge 3 vectors.
  std::vector<math::geometry::Point2d> merged_points =
      std::move(center_line_points);
  merged_points.insert(
      merged_points.end(),
      std::make_move_iterator(left_lane_marking_info_points.begin()),
      std::make_move_iterator(left_lane_marking_info_points.end()));
  merged_points.insert(
      merged_points.end(),
      std::make_move_iterator(right_lane_marking_info_points.begin()),
      std::make_move_iterator(right_lane_marking_info_points.end()));

  // Sort points by arc length on lane centerline.
  std::sort(merged_points.begin(), merged_points.end(),
            [lane](const math::geometry::Point2d& a,
                   const math::geometry::Point2d& b) {
              return lane->GetArclength(a) < lane->GetArclength(b);
            });

  // Skip very close points.
  auto last = std::unique(
      merged_points.begin(), merged_points.end(),
      [lane](const math::geometry::Point2d& a,
             const math::geometry::Point2d& b) {
        return std::abs(lane->GetArclength(b) - lane->GetArclength(a)) <=
               kMinDistanceToMergeContinuousMapPoints;
      });
  merged_points.erase(last, merged_points.end());
  merged_points.shrink_to_fit();

  return math::geometry::PolylineCurve2d(std::move(merged_points));
}

// Splits line into fixed length segments besides the remaining part. Each
// segment is presented by (num_vectors_in_map_object_feature + 1)
// points (both start point and end point included) sampled every K meters,
// which means the length of each segment is (num_vectors_in_map_object_feature
// * K);
// TODO(York): Will refactor this function to unify all the segment generation
// by raw points.
std::vector<std::vector<math::geometry::Point2d>> ToSegments(
    const math::geometry::PolylineCurve2d& line,
    int num_vectors_in_map_object_feature, float sample_interval,
    bool is_cone_or_barrier = false, bool use_dense_map_sampling = false) {
  std::vector<math::geometry::Point2d> sampled_points;
  // Special logic for cones and barriers.
  if (is_cone_or_barrier) {
    // If the traffic cone or barrier raw points are less than
    // num_vectors_in_map_object_feature + 1, we will use the raw points
    // directly.
    // Otherwise, we will sample the points with minimum sample interval
    // according to the total arc length.
    if (static_cast<int>(line.points().size()) <=
        num_vectors_in_map_object_feature + 1) {
      sampled_points = line.points();
    } else {
      const float sample_interval_for_cone_or_barrier =
          line.GetTotalArcLength() / num_vectors_in_map_object_feature;
      sampled_points =
          GetDynamicSampledPoints(line, sample_interval_for_cone_or_barrier);
    }

  } else if (use_dense_map_sampling) {
    // Enable dense map segment for other map objects.
    sampled_points = GetDenseRawPointsWithPostProcess(
        line, num_vectors_in_map_object_feature + 1);
  } else {
    sampled_points = GetDynamicSampledPoints(line, sample_interval);
  }

  const double last_sampled_points_arc_length =
      line.GetProximity(sampled_points.back(),
                        math::pb::UseExtensionFlag::kForbid)
          .arc_length;
  if (line.GetTotalArcLength() - last_sampled_points_arc_length >
          math::constants::kEpsilon &&
      last_sampled_points_arc_length > math::constants::kEpsilon) {
    sampled_points.push_back(line.GetEndPoint());
  }
  if (static_cast<int>(sampled_points.size()) >
      num_vectors_in_map_object_feature + 1) {
    STATS_COUNTER2(prediction, vectornet, num_map_objects_with_extra_vectors,
                   1);
  }
  std::vector<std::vector<math::geometry::Point2d>> segments;
  for (size_t i = 0; i + 1 < sampled_points.size(); ++i) {
    if (segments.empty() ||
        segments.back().size() ==
            static_cast<size_t>(num_vectors_in_map_object_feature) + 1) {
      // Create a new segment. It starts with sampled_points[i].
      segments.push_back({sampled_points[i]});
    }
    if (math::geometry::Distance(sampled_points[i], sampled_points[i + 1]) <=
        math::constants::kEpsilon) {
      continue;
    }
    segments.back().push_back(sampled_points[i + 1]);
  }
  return segments;
}

// Returns view range or |Inf| for this value that is not set(0.0 means not
// set).
double GetViewRangeOrInf(double view_range) {
  return math::NearZero(view_range) ? std::numeric_limits<double>::max()
                                    : view_range;
}

// To sort and get top k background agents which are important or near to ego.
struct AgentPriorityKey {
  bool is_important_to_ego = false;
  double distance_to_target_object = -1.0;
  int agent_index = -1;
  bool is_oncoming_agent_behind_target_vehicle = false;
  int64_t agent_id = -1;
};

// To sort and get top k traffic light which are not reasoning result or near to
// ego.
struct TrafficLightPriorityKey {
  bool is_reasoning_result = false;
  double distance_to_target_object = -1.0;
  TrafficLightKey traffic_light_key;
};

// Selects at most |max_num_agents| indexes from |agents_features|. It must
// include target agent and ego agent. For other agents, select them depending
// on its distance to target agent.
// When target agent is not ego agent: the index of target agent is always 0
// and the index of ego agent is always 1.
// When target agent is ego agent, the index of target/ego agent is 0.
std::vector<int> SelectBackgroundAgentIndexes(
    const voy::TrackedObject& target_object,
    const std::vector<AgentFeature>& agents_features, int max_num_agents,
    double view_range, const RepeatedAgentTags& excluded_background_agent_tags,
    int max_num_importance_rank_agent) {
  const bool is_predict_ego = (target_object.id() == constants::kRobotObjId);
  // For marginal prediction: Ego agent and target agent should be included.
  // For joint prediction : Target agent is ego agent.
  const int num_reserved_agents_for_pq =
      (is_predict_ego ? kEgoAgentIndexInPredictEgoModel
                      : kEgoAgentIndexInMarginalModel) +
      1;
  DCHECK_GE(max_num_agents, num_reserved_agents_for_pq);
  const math::geometry::Point2d target_agent_position(target_object.center_x(),
                                                      target_object.center_y());
  // Use priority_queue to select at most |max_num_agents| indexes from
  // |agents_features|. Ego object and target object itself should be
  // selected. For mlplanner, important to ego agents will be selected first.
  // Other objects are selected depending on distance to target object. The
  // prior queue will use `AgentPriorityKey` to save the sorting information
  // and the index of `agent_features`.
  auto compare = [](const AgentPriorityKey& a, const AgentPriorityKey& b) {
    if (a.is_important_to_ego != b.is_important_to_ego) {
      return a.is_important_to_ego;
    }
    return a.distance_to_target_object < b.distance_to_target_object;
  };
  std::priority_queue<AgentPriorityKey, std::vector<AgentPriorityKey>,
                      decltype(compare)>
      pq(compare);
  int target_agent_index = -1;
  int ego_index = -1;
  const std::set<int> excluded_agent_type_set(
      excluded_background_agent_tags.begin(),
      excluded_background_agent_tags.end());
  for (size_t i = 0; i < agents_features.size(); ++i) {
    // If there are too many agents at one moment, joint prediction will not
    // cover all agents in DL prediction pipeline. This will leave out some
    // important agents. The preliminary solution is to ignore unknown-type
    // agents in DL pipeline because most of the unknown-type agents
    // are far away from the ego and stationary and unknown-type agents will
    // not be handled by marginal prediction.
    if (excluded_agent_type_set.count(
            static_cast<int>(agents_features[i].agent_tag)) != 0u) {
      continue;
    }
    if (agents_features[i].object_id == target_object.id()) {
      target_agent_index = i;
    } else if (agents_features[i].object_id == constants::kRobotObjId) {
      ego_index = i;
    } else {
      const double distance_to_target_object = math::geometry::Distance(
          target_agent_position, agents_features[i].current_center);
      const int64_t importance_rank_to_ego =
          agents_features[i].importance_rank_to_ego;
      // Note: Only set `max_num_importance_rank_agent` for mlplanner onboard
      // inference, DO NOT add it into offboard config for now.
      const bool is_important_to_ego =
          importance_rank_to_ego != constants::kInvalidImportanceRankToEgo &&
          importance_rank_to_ego <= max_num_importance_rank_agent;
      if (distance_to_target_object < view_range) {
        pq.emplace(AgentPriorityKey{
            /*is_important_to_ego=*/is_important_to_ego,
            /*distance_to_target_object=*/distance_to_target_object,
            /*agent_index=*/static_cast<int>(i)});
      }
    }
    if (pq.size() >
        static_cast<size_t>(max_num_agents - num_reserved_agents_for_pq)) {
      pq.pop();
    }
  }
  DCHECK_NE(target_agent_index, -1);
  std::vector<int> indexes;
  // Plus |num_reserved_agents_for_pq| for target/ego agent.
  indexes.reserve(pq.size() + num_reserved_agents_for_pq);
  while (!pq.empty()) {
    indexes.push_back(pq.top().agent_index);
    pq.pop();
  }
  if (!is_predict_ego) {
    DCHECK_NE(ego_index, -1);
    indexes.push_back(ego_index);
  }
  indexes.push_back(target_agent_index);
  // According to the distance to the target agent, the agent features located
  // after the reserved agent are saved in order from near to far.
  std::reverse(indexes.begin(), indexes.end());
  return indexes;
}

// Selects at most |max_num_lidar_patch_agents| indexes from
// |candidate_background_agents_key|. The selection results must include target
// agent and exclude the ego.
std::vector<ObjectId> SelectLidarPatchBackgroundAgentForTarget(
    const voy::TrackedObject& target_object,
    const std::vector<AgentKey>& candidate_background_agents_key,
    const std::unordered_map<ObjectId, const AgentFeature*>& agents_feature_map,
    int max_num_agents) {
  DCHECK(!candidate_background_agents_key.empty());
  DCHECK(!agents_feature_map.empty());
  const int num_max_selected_agents = std::min(
      max_num_agents, static_cast<int>(candidate_background_agents_key.size()));

  const ObjectId target_id = target_object.id();
  DCHECK_GE(num_max_selected_agents, 1);
  DCHECK_EQ(candidate_background_agents_key[0].agent_id, target_id);

  // Early return if there's only target agent in candidate background agents.
  if (num_max_selected_agents == 1) {
    return std::vector<ObjectId>{target_id};
  }

  // Builds priority key for candidate background agents.
  std::vector<AgentPriorityKey> agents_priority_key;
  agents_priority_key.reserve(candidate_background_agents_key.size());
  // Returns true if the agent is oncoming to the target agent.
  auto is_oncoming = [](double target_agent_heading,
                        double agent_heading) -> bool {
    return std::abs(math::NormalizeMinusPiToPi(target_agent_heading -
                                               agent_heading)) > M_PI / 2.0;
  };
  // Returns true if the agent is behind the target agent.
  auto is_behind = [](const math::geometry::Point2d& target_agent_center,
                      const math::geometry::Point2d& agent_center,
                      double target_agent_heading) -> bool {
    return ((target_agent_center.x() - agent_center.x()) *
                std::cos(target_agent_heading) +
            (target_agent_center.y() - agent_center.y()) *
                std::sin(target_agent_heading)) < 0.0;
  };
  const math::geometry::Point2d target_agent_center(target_object.center_x(),
                                                    target_object.center_y());
  const double target_agent_heading = target_object.box_heading();
  const double target_agent_speed = target_object.velocity();
  for (const auto& agent_key : candidate_background_agents_key) {
    const auto& iter = agents_feature_map.find(agent_key.agent_id);
    DCHECK(iter != agents_feature_map.end());
    const AgentFeature& agent_feature = *(DCHECK_NOTNULL(iter->second));
    // Filter out the target agent, the ego and the stationary agents, Note
    // that, the target agent will definitely be selected, the ego will never be
    // selected because it lacks the lidar-related features, and the stationary
    // object will not be selected because it will be generated obstacles as
    // stationary context.
    if (agent_key.agent_id == target_id ||
        agent_key.agent_id == constants::kRobotObjId ||
        agent_feature.is_stationary_object) {
      continue;
    }
    // Note that, the distance to target obejct are calculated from the current
    // agent's center to the bounding boxes(which is fair for large vehicles) of
    // other agents based on a dynamic elliptical distance.
    agents_priority_key.push_back(AgentPriorityKey{
        .distance_to_target_object =
            vectornet_util::CalculateDynamicEllipseDistanceBasedOnSpeed(
                target_agent_center, target_agent_heading, target_agent_speed,
                math::geometry::PolylineCurve2d(agent_feature.sampled_points)),
        .is_oncoming_agent_behind_target_vehicle =
            target_object.object_type() ==
                voy::perception::ObjectType::VEHICLE &&
            is_oncoming(target_agent_heading,
                        agent_feature.current_box_heading) &&
            is_behind(target_agent_center, agent_feature.current_center,
                      target_agent_heading),
        .agent_id = agent_key.agent_id});
  }

  // Select |num_max_selected_agents - 1| agents for target agent by defined
  // priority.
  auto compare = [](const AgentPriorityKey& a, const AgentPriorityKey& b) {
    if (a.is_oncoming_agent_behind_target_vehicle !=
        b.is_oncoming_agent_behind_target_vehicle) {
      return !a.is_oncoming_agent_behind_target_vehicle;
    }
    return a.distance_to_target_object < b.distance_to_target_object;
  };
  const int num_selected_other_agents =
      std::min(num_max_selected_agents - 1,
               static_cast<int>(agents_priority_key.size()));
  std::nth_element(agents_priority_key.begin(),
                   agents_priority_key.begin() + num_selected_other_agents,
                   agents_priority_key.end(), compare);

  std::vector<ObjectId> selected_agent_ids;
  selected_agent_ids.reserve(num_selected_other_agents + 1);
  selected_agent_ids.push_back(target_id);
  std::transform(
      agents_priority_key.begin(),
      agents_priority_key.begin() + num_selected_other_agents,
      std::back_inserter(selected_agent_ids),
      [](const AgentPriorityKey& agent_key) { return agent_key.agent_id; });

  return selected_agent_ids;
}

// Selects at most |max_num_obstacles| indexes from |obstacles_features|. The
// obstacles are sorted and selected based on the distance between each
// obstacle's centerpoint and the target agent.
std::vector<int> SelectBackgroundObstacleIndexes(
    const voy::TrackedObject& target_object,
    const std::vector<ObstacleFeature>& obstacles_features,
    int max_num_obstacles, double view_range) {
  const math::geometry::Point2d target_agent_position(target_object.center_x(),
                                                      target_object.center_y());

  auto compare = [](const AgentPriorityKey& a, const AgentPriorityKey& b) {
    return a.distance_to_target_object < b.distance_to_target_object;
  };
  std::priority_queue<AgentPriorityKey, std::vector<AgentPriorityKey>,
                      decltype(compare)>
      pq(compare);
  for (size_t i = 0; i < obstacles_features.size(); ++i) {
    const double distance_to_target_object = math::geometry::Distance(
        target_agent_position, obstacles_features[i].current_center);
    if (distance_to_target_object < view_range) {
      pq.emplace(AgentPriorityKey{
          /*is_important_to_ego=*/true,
          /*distance_to_target_object=*/distance_to_target_object,
          /*agent_index=*/static_cast<int>(i)});
    }
    if (pq.size() > static_cast<size_t>(max_num_obstacles)) {
      pq.pop();
    }
  }
  std::vector<int> indexes;
  indexes.reserve(pq.size());
  while (!pq.empty()) {
    indexes.push_back(pq.top().agent_index);
    pq.pop();
  }
  std::reverse(indexes.begin(), indexes.end());
  return indexes;
}

// Selects at most |max_num_traffic_light| indexes from
// |traffic_light_features|. Traffic lights are sorted and filtered selected
// on: 1. Whether they are reasoning results. 2. The distance between their
// associated stop line and the the target agent.
std::vector<TrafficLightKey> SelectBackgroundTrafficLights(
    const voy::TrackedObject& target_object,
    const std::map<TrafficLightKey, TrafficLightFeature>&
        traffic_light_features,
    int max_num_traffic_light) {
  const math::geometry::Point2d target_agent_position(target_object.center_x(),
                                                      target_object.center_y());
  auto compare = [](const TrafficLightPriorityKey& a,
                    const TrafficLightPriorityKey& b) {
    if (a.is_reasoning_result != b.is_reasoning_result) {
      return !a.is_reasoning_result;
    }
    return a.distance_to_target_object < b.distance_to_target_object;
  };
  std::priority_queue<TrafficLightPriorityKey,
                      std::vector<TrafficLightPriorityKey>, decltype(compare)>
      pq(compare);
  for (const auto& [tl_key, tl_feature] : traffic_light_features) {
    const double distance_to_target_object = math::geometry::Distance(
        target_agent_position, tl_feature.locate_point);
    pq.emplace(TrafficLightPriorityKey{
        /*is_reasoning_result=*/tl_feature.is_reasoning_result,
        /*distance_to_target_object=*/distance_to_target_object,
        /*traffic_light_key=*/tl_key});
    if (pq.size() > static_cast<size_t>(max_num_traffic_light)) {
      pq.pop();
    }
  }

  std::vector<TrafficLightKey> tl_keys;
  tl_keys.reserve(pq.size());
  while (!pq.empty()) {
    tl_keys.push_back(pq.top().traffic_light_key);
    pq.pop();
  }
  return tl_keys;
}

std::unordered_map<ObjectId, std::vector<ObjectId>>
GetLidarPatchBackgroundAgentIDs(
    const std::vector<const Agent*>& target_agents,
    const std::vector<AgentFeature>& agents_features,
    const std::map<ObjectId, std::vector<AgentKey>>& background_agent_keys,
    int max_num_agents_with_lidar_patch) {
  // Early return if there's only target agent in candidate background agents.
  if (max_num_agents_with_lidar_patch == 1) {
    std::unordered_map<ObjectId, std::vector<ObjectId>> selected_agent_ids;
    for (const Agent* agent : target_agents) {
      selected_agent_ids.emplace((*agent).object_id(),
                                 std::vector<ObjectId>{(*agent).object_id()});
    }
    return selected_agent_ids;
  }

  // Generate |agents_feature_map| from |agent_id| mapping to |agent_fature|.
  std::unordered_map<ObjectId, const AgentFeature*> agents_feature_map;
  agents_feature_map.reserve(agents_features.size());
  std::transform(agents_features.begin(), agents_features.end(),
                 std::inserter(agents_feature_map, agents_feature_map.end()),
                 [](const AgentFeature& agent_feature)
                     -> std::pair<ObjectId, const AgentFeature*> {
                   return {agent_feature.object_id, &agent_feature};
                 });

  std::unordered_map<ObjectId, std::vector<ObjectId>>
      selected_lidar_background_agents_id;
  selected_lidar_background_agents_id.reserve(target_agents.size());

  for (const Agent* target_agent : target_agents) {
    DCHECK(target_agent);
    const int64_t target_agent_id = target_agent->object_id();
    const auto candidate_agent_keys_iter =
        background_agent_keys.find(target_agent_id);
    DCHECK(candidate_agent_keys_iter != background_agent_keys.end());
    std::vector<ObjectId> selected_background_agents_ids =
        SelectLidarPatchBackgroundAgentForTarget(
            (*target_agent).tracked_object(), candidate_agent_keys_iter->second,
            agents_feature_map, max_num_agents_with_lidar_patch);
    const auto result = selected_lidar_background_agents_id.try_emplace(
        target_agent_id, std::move(selected_background_agents_ids));
    DCHECK(result.second);
  }

  return selected_lidar_background_agents_id;
}

// Gets highlight map object keys based on config
std::set<MapObjectKey> GetHighlightMapObjectKeys(
    const math::geometry::Point2d& target_agent_position,
    const std::map<MapObjectKey, MapObjectFeature>& map_objects_features,
    const pb::ExtractorConfig& config) {
  std::set<MapObjectKey> highlight_map_object_set;
  for (const auto highlight_object_type : config.highlight_map_objects()) {
    switch (highlight_object_type) {
      case pb::ExtractorConfig::CROSSWALK_IN_RANGE: {
        const double view_range = GetViewRangeOrInf(config.view_range());
        for (const auto& map_objects_feature : map_objects_features) {
          if (map_objects_feature.first.map_object_type !=
              pb::MapObject::CROSSWALK) {
            continue;
          }
          const double distance_to_target_object = math::geometry::Distance(
              target_agent_position, map_objects_feature.second.segment);
          if (distance_to_target_object < view_range) {
            highlight_map_object_set.emplace(map_objects_feature.first);
          }
        }
      } break;

      case pb::ExtractorConfig::EGO_LANE: {
        for (const auto& map_objects_feature : map_objects_features) {
          if (map_objects_feature.second.is_in_ego_lane_seq &&
              (map_objects_feature.first.map_object_type ==
                   pb::MapObject::LANE ||
               map_objects_feature.first.map_object_type ==
                   pb::MapObject::LANE_MARKING)) {
            // NOTE(lipei): In the highway, road/lane may have larger length(
            // several kilometers), So here we would limit the ego route
            // length.
            const double distance_to_target_object = math::geometry::Distance(
                target_agent_position, map_objects_feature.second.segment);
            if (distance_to_target_object <= kMaxDistanceRangeForEgoRoute) {
              highlight_map_object_set.emplace(map_objects_feature.first);
            }
          }
        }
      } break;

      default:
        DCHECK(false) << "Does not support this highlight object type:"
                      << pb::ExtractorConfig::HighlightMapObject_Name(
                             highlight_object_type);
    }
  }
  return highlight_map_object_set;
}

// Map object feature priority key, used to sort map object feature and
// get target object's top k nearest map object's feature within defined
// view range.
struct MapObjFeaturePriorityKey {
  // Whether it is highlight map object.
  bool is_highlight_object = false;

  // Distance from map line segment to target object.
  double dist_to_target_object = -1.0;
};

// Selects target agent's background map object features based on configured
// strategy.
// |view_range|: The range of the view(center on the target agents), which is
// used to filter background map objects that exceed the distance range to
// center of the target agent. If we configured highlight map object
// selection, these highlight map object selection is regardless of the view
// range.
std::vector<ConstMapFeatureIterator> SelectBackgroundMapObjectIterators(
    const voy::TrackedObject& target_object,
    const std::map<MapObjectKey, MapObjectFeature>& map_objects_features,
    const pb::ExtractorConfig& extractor_config) {
  // Gets highlight map object set.
  const math::geometry::Point2d target_agent_position(target_object.center_x(),
                                                      target_object.center_y());
  const std::set<MapObjectKey> highlight_map_object_set =
      GetHighlightMapObjectKeys(target_agent_position, map_objects_features,
                                extractor_config);

  const double view_range = GetViewRangeOrInf(extractor_config.view_range());
  const int max_num_map_objects_segment =
      extractor_config.max_num_map_objects_segment();
  const bool use_ellipse_distance_to_select_map_objects =
      extractor_config.use_ellipse_distance_to_select_map_objects();
  // Use priority_queue to select at most |max_num_map_objects_segment|
  // iterators from |map_objects_features|. They are selected depending on
  // whether it is highlight and the distance to target object. The
  // |ConstMapFeatureIterator| in priority_queue is the iterator of
  // |map_objects_features|.
  using T = std::pair<MapObjFeaturePriorityKey, ConstMapFeatureIterator>;
  static auto compare = [](const T& a, const T& b) {
    if (a.first.is_highlight_object != b.first.is_highlight_object) {
      return a.first.is_highlight_object;
    }
    return a.first.dist_to_target_object < b.first.dist_to_target_object;
  };
  std::priority_queue<T, std::vector<T>, decltype(compare)> pq(compare);
  for (auto iter = map_objects_features.cbegin();
       iter != map_objects_features.cend(); ++iter) {
    const double distance_to_target_object =
        use_ellipse_distance_to_select_map_objects
            ? vectornet_util::CalculateDynamicEllipseDistanceBasedOnSpeed(
                  target_agent_position, target_object.heading(),
                  target_object.velocity(), iter->second.segment)
            : math::geometry::Distance(target_agent_position,
                                       iter->second.segment);
    const bool is_highlight_object =
        gtl::ContainsKey(highlight_map_object_set, iter->first);
    if (distance_to_target_object < view_range || is_highlight_object) {
      pq.emplace(MapObjFeaturePriorityKey{
                     /*is_highlight_object=*/is_highlight_object,
                     /*dist_to_target_object=*/distance_to_target_object},
                 iter);
    }
    if (pq.size() > static_cast<size_t>(max_num_map_objects_segment)) {
      pq.pop();
    }
  }
  std::vector<ConstMapFeatureIterator> iterators;
  iterators.reserve(pq.size());
  while (!pq.empty()) {
    iterators.push_back(pq.top().second);
    pq.pop();
  }
  // According to the distance to the target agent, the map features are saved
  // in order from near to far.
  std::reverse(iterators.begin(), iterators.end());
  return iterators;
}

// Converts given lane type to vectornet map object type feature.
pb::MapObject::MapObjectType ToMapObjectTypeFeature(
    const hdmap::Lane::LaneType& lane_type) {
  switch (lane_type) {
    case hdmap::Lane_LaneType_VIRTUAL:
      return pb::MapObject::LANE_CENTER_LINE_VIRTUAL;
    case hdmap::Lane_LaneType_REGULAR:
      return pb::MapObject::LANE_CENTER_LINE_REGULAR;
    case hdmap::Lane_LaneType_BUS:
      return pb::MapObject::LANE_CENTER_LINE_BUS;
    case hdmap::Lane_LaneType_BIKE:
      return pb::MapObject::LANE_CENTER_LINE_BIKE;
    case hdmap::Lane_LaneType_WAITING:
      return pb::MapObject::LANE_CENTER_LINE_WAITING;
    case hdmap::Lane_LaneType_CENTER_LEFT_TURN:
      return pb::MapObject::LANE_CENTER_LINE_CENTER_LEFT_TURN;
    case hdmap::Lane_LaneType_EMERGENCY:
      return pb::MapObject::LANE_CENTER_LINE_EMERGENCY;
    default:
      break;
  }
  return pb::MapObject::UNKNOWN_TYPE;
}

// Converts given lane marking type to vectornet map object type feature.
pb::MapObject::MapObjectType ToMapObjectTypeFeature(
    const hdmap::LaneMarking::Attribute::Type& lane_marking_type) {
  switch (lane_marking_type) {
    case hdmap::LaneMarking_Attribute_Type_VIRTUAL:
      return pb::MapObject::LANE_MARKING_VIRTUAL;
    case hdmap::LaneMarking_Attribute_Type_BROKEN:
      return pb::MapObject::LANE_MARKING_BROKEN;
    case hdmap::LaneMarking_Attribute_Type_SOLID:
      return pb::MapObject::LANE_MARKING_SOLID;
    case hdmap::LaneMarking_Attribute_Type_DOUBLE_BROKEN:
      return pb::MapObject::LANE_MARKING_DOUBLE_BROKEN;
    case hdmap::LaneMarking_Attribute_Type_DOUBLE_SOLID:
      return pb::MapObject::LANE_MARKING_DOUBLE_SOLID;
    default:
      break;
  }
  return pb::MapObject::UNKNOWN_TYPE;
}

// Gets the lane marking type of each point in the segments. Return empty
// results if the |lane_marking| is nullptr.
std::vector<std::vector<pb::MapObject::MapObjectType>> GetLaneMarkingTypes(
    const std::vector<std::vector<math::geometry::Point2d>>& segments,
    const pnc_map::LaneMarking* lane_marking) {
  if (lane_marking == nullptr) return {};
  std::vector<std::vector<pb::MapObject::MapObjectType>>
      segments_lane_marking_types;
  segments_lane_marking_types.reserve(segments.size());
  for (const auto& segment : segments) {
    std::vector<pb::MapObject::MapObjectType> segment_lane_marking_types;
    segment_lane_marking_types.reserve(segment.size());
    for (const auto& point : segment) {
      const pb::MapObject::MapObjectType type =
          ToMapObjectTypeFeature(lane_marking->GetAttributeType(point));
      segment_lane_marking_types.push_back(type);
    }
    segments_lane_marking_types.push_back(
        std::move(segment_lane_marking_types));
  }
  return segments_lane_marking_types;
}

// Gets accurate lane marking type for the given |segments| by projecting the
// middle point of the segment to the given |lane_marking|. It is more accurate
// than using the start point of the segment that might be wrong for segments
// with lane marking type change. Returns empty results if the |lane_marking| is
// nullptr.
std::vector<std::vector<pb::MapObject::MapObjectType>>
GetAccurateLaneMarkingTypes(
    const std::vector<std::vector<math::geometry::Point2d>>& segments,
    const pnc_map::LaneMarking* lane_marking) {
  if (lane_marking == nullptr) {
    return {};
  }

  std::vector<std::vector<pb::MapObject::MapObjectType>>
      segments_lane_marking_types;
  segments_lane_marking_types.reserve(segments.size());

  for (const auto& segment : segments) {
    std::vector<pb::MapObject::MapObjectType> lane_marking_types;
    const auto num_points = segment.size();
    lane_marking_types.reserve(num_points);

    for (size_t i = 0; i + 1 < num_points; ++i) {
      const auto& start_point = segment[i];
      const auto& end_point = segment[i + 1];
      const auto middle_point =
          math::geometry::Point2d((start_point.x() + end_point.x()) / 2,
                                  (start_point.y() + end_point.y()) / 2);
      const pb::MapObject::MapObjectType type =
          ToMapObjectTypeFeature(lane_marking->GetAttributeType(middle_point));
      lane_marking_types.push_back(type);
    }
    // TODO(xiongwei): No need to add the last point's lane marking type. We may
    // add lane marking type for `vector`, not for `point`.
    lane_marking_types.push_back(lane_marking_types.back());
    DCHECK_EQ(lane_marking_types.size(), num_points);

    segments_lane_marking_types.push_back(std::move(lane_marking_types));
  }

  return segments_lane_marking_types;
}

// Hash the pair to query the unique lane with watch line.
// The pair is (turn, id).
struct PairHash {
  size_t operator()(const std::pair<hdmap::Lane::Turn, int64_t>& p) const {
    return std::hash<hdmap::Lane::Turn>{}(p.first) ^
           (std::hash<int64_t>{}(p.second) << 1);
  }
};

// We assume watch lines from virtual lanes with the sample turn types and
// predecessors are actually the same one even though they are different objects
// in map object.
std::vector<const pnc_map::Lane*> GetUniqueLanesWithWatchLine(
    const std::vector<const pnc_map::Lane*>& lanes) {
  std::vector<const pnc_map::Lane*> unique_lanes;
  unique_lanes.reserve(lanes.size());

  std::unordered_set<std::pair<hdmap::Lane::Turn, int64_t>, PairHash> seen_keys;
  seen_keys.reserve(lanes.size());

  for (const auto lane : lanes) {
    if (!lane->watch_line().has_value()) {
      continue;
    }

    // Gets first non-junction predecessor
    const pnc_map::Lane* predecessor_lane = lane;
    if (!lane->predecessors().empty()) {
      predecessor_lane = lane->predecessors().front();
      while (predecessor_lane->IsInJunction() &&
             !predecessor_lane->predecessors().empty()) {
        predecessor_lane = predecessor_lane->predecessors().front();
      }
    }

    auto key = std::make_pair(lane->turn(), predecessor_lane->id());
    if (seen_keys.emplace(key).second) {
      unique_lanes.push_back(lane);
    }
  }

  return unique_lanes;
}

// Gets the speed limits of each point in the segments. Return empty results
// if the |speed_limits| is nullptr.
std::vector<std::vector<float>> GetSpeedLimits(
    const std::vector<std::vector<math::geometry::Point2d>>& segments,
    const SpeedLimits* speed_limits) {
  if (speed_limits == nullptr) return {};

  std::vector<std::vector<float>> segments_speed_limits;
  segments_speed_limits.reserve(segments.size());
  for (const auto& segment : segments) {
    std::vector<float> segment_speed_limits;
    segment_speed_limits.reserve(segment.size());
    for (const auto& point : segment) {
      const float speed_limit = speed_limits->GetSpeedLimit(point);
      segment_speed_limits.push_back(speed_limit);
    }
    segments_speed_limits.push_back(std::move(segment_speed_limits));
  }
  return segments_speed_limits;
}

// Gets the lane marking points of each point in the segments.
std::vector<math::geometry::Point2d> GetLaneMarkingPoints(
    const pnc_map::Lane& lane,
    const std::vector<math::geometry::Point2d>& segment_points, bool is_left) {
  std::vector<math::geometry::Point2d> lane_marking_points;
  lane_marking_points.reserve(segment_points.size());
  int csi_hint = math::kInvalidClosestSegmentIndex;
  for (const auto& point : segment_points) {
    if (is_left) {
      // Gets the left marking points.
      lane_marking_points.emplace_back(
          lane.GetLeftMarkingPoint(point, &csi_hint));
    } else {
      // Gets the right marking points.
      lane_marking_points.emplace_back(
          lane.GetRightMarkingPoint(point, &csi_hint));
    }
  }
  return lane_marking_points;
}

// Extracts line segments from given lines and updates to
// |map_line_segments| if they are missed in cache. Nothing happens if the
// segment is already in the provided cache.
void ExtractSegmentsNotInCache(
    const math::geometry::PolylineCurve2d& line,
    const pb::ExtractorConfig& config,
    pb::MapObject::MapObjectType map_object_type, int64_t map_object_id,
    pb::MapObject::MapObjectType map_line_type,
    hdmap::Lane::Turn lane_turn_type,
    std::map<MapObjectKey, MapLineSegment>* map_line_segments,
    int64_t timestamp = constants::kInvalidTimestamp,
    const CheckMissOfMapKeyInCacheFunction& check_miss_of_map_key_in_cache =
        [](pb::MapObject::MapObjectType, int64_t) { return true; }) {
  DCHECK(map_line_segments != nullptr);
  // Early return if the lane is already in map cache.
  // NOTE: Please skip the cache check if timestamp is given since we assume all
  // map object in cache are static.
  if ((timestamp == constants::kInvalidTimestamp) &&
      (!check_miss_of_map_key_in_cache(map_object_type, map_object_id))) {
    return;
  }

  const bool is_cone_or_barrier =
      map_object_type == pb::MapObject::TRAFFIC_CONE ||
      map_object_type == pb::MapObject::BARRIER;
  const float sample_interval = GetDynamicSampleIntervalForMapObjectLine(line);
  std::vector<std::vector<math::geometry::Point2d>> segments = ToSegments(
      line, config.num_vectors_in_map_object_feature(), sample_interval,
      is_cone_or_barrier,
      config.use_dense_map_sampling() ||
          config.use_dense_map_sampling_for_map_objects_other_than_lane());
  for (size_t i = 0; i < segments.size(); ++i) {
    const MapObjectKey key{.map_object_type = map_object_type,
                           .map_object_id = map_object_id,
                           .segment_index = static_cast<int>(i),
                           .timestamp = timestamp};
    map_line_segments->emplace(
        key, MapLineSegment{std::move(segments[i]), map_line_type,
                            lane_turn_type, /*speed_limits=*/{}});
  }
}

// Extracts segments from given lane if the lane is missed in cache. Nothing
// happens if the lane segment is already in the provided cache.
void ExtractLaneSegmentsNotInCache(
    const pnc_map::Lane* lane, const pb::ExtractorConfig& config,
    std::map<MapObjectKey, MapLineSegment>* map_line_segments,
    CheckMissOfMapKeyInCacheFunction check_miss_of_map_key_in_cache =
        [](pb::MapObject::MapObjectType, int64_t) { return true; }) {
  // Early return if the lane is already in map cache.
  if (!check_miss_of_map_key_in_cache(pb::MapObject::LANE, lane->id())) {
    return;
  }

  const pb::MapObject::MapObjectType map_line_type =
      ToMapObjectTypeFeature(lane->type());
  const hdmap::Lane::Turn lane_turn_type = lane->turn();
  const auto speed_limits = std::make_unique<SpeedLimits>(*lane);

  const float sample_interval =
      GetDynamicSampleIntervalForMapObjectLine(lane->center_line());
  // Only enable when we want all map segmentation for all Map Objects.
  std::vector<std::vector<math::geometry::Point2d>> segments = ToSegments(
      GetMergedLanePolyline(lane), config.num_vectors_in_map_object_feature(),
      sample_interval, false, config.use_dense_map_sampling());
  std::vector<std::vector<float>> segments_speed_limits =
      GetSpeedLimits(segments, speed_limits.get());
  if (!segments_speed_limits.empty()) {
    DCHECK_EQ(segments_speed_limits.size(), segments.size());
  }

  std::vector<std::vector<pb::MapObject::MapObjectType>> left_marking_types =
      config.extract_accurate_lane_marking_type()
          ? GetAccurateLaneMarkingTypes(segments, lane->left_marking())
          : GetLaneMarkingTypes(segments, lane->left_marking());
  if (!left_marking_types.empty()) {
    DCHECK_EQ(left_marking_types.size(), segments.size());
  }

  std::vector<std::vector<pb::MapObject::MapObjectType>> right_marking_types =
      config.extract_accurate_lane_marking_type()
          ? GetAccurateLaneMarkingTypes(segments, lane->right_marking())
          : GetLaneMarkingTypes(segments, lane->right_marking());
  if (!right_marking_types.empty()) {
    DCHECK_EQ(right_marking_types.size(), segments.size());
  }

  for (size_t i = 0; i < segments.size(); ++i) {
    const MapObjectKey key{.map_object_type = pb::MapObject::LANE,
                           .map_object_id = lane->id(),
                           .segment_index = static_cast<int>(i)};
    std::vector<float> segment_speed_limits;
    if (!segments_speed_limits.empty()) {
      segment_speed_limits = std::move(segments_speed_limits[i]);
    }
    std::vector<pb::MapObject::MapObjectType> segment_left_marking_types;
    if (!left_marking_types.empty()) {
      segment_left_marking_types = std::move(left_marking_types[i]);
    }
    std::vector<pb::MapObject::MapObjectType> segment_right_marking_types;
    if (!right_marking_types.empty()) {
      segment_right_marking_types = std::move(right_marking_types[i]);
    }
    // Use the average width at the start and end point to denote the segment
    // average width.
    const math::geometry::Point2d& start_point = segments[i].front();
    const math::geometry::Point2d& end_point = segments[i].back();
    const double lane_segment_width = (lane->GetWidthAtPoint(start_point) +
                                       lane->GetWidthAtPoint(end_point)) /
                                      2;
    std::vector<math::geometry::Point2d> left_marking_points =
        GetLaneMarkingPoints(*lane, segments[i], /*is_left*/ true);
    std::vector<math::geometry::Point2d> right_marking_points =
        GetLaneMarkingPoints(*lane, segments[i], /*is_left*/ false);

    map_line_segments->emplace(
        key, MapLineSegment{
                 .segment_points = std::move(segments[i]),
                 .line_type = map_line_type,
                 .lane_turn_type = lane_turn_type,
                 .speed_limits = std::move(segment_speed_limits),
                 .left_marking_types = std::move(segment_left_marking_types),
                 .right_marking_types = std::move(segment_right_marking_types),
                 .lane_segment_width = lane_segment_width,
                 .left_marking_points = std::move(left_marking_points),
                 .right_marking_points = std::move(right_marking_points),
             });
  }
}

// Extracts stop/watch line segments from given lines and updates to
// |map_line_segments| if they are missed in cache. Nothing happens if the
// segment is already in the provided cache.
// NOTE: Use the lane id as map_object_id_for_traffic_light to query the traffic
// light color.
void ExtractStopLineSegmentsNotInCache(
    const std::vector<math::geometry::Polyline2d> polylines,
    pb::MapObject::MapObjectType map_object_type, int64_t map_object_id,
    int64_t map_object_id_for_traffic_light,
    const std::vector<hdmap::Lane::Turn> lane_turn_types,
    std::map<MapObjectKey, MapLineSegment>* map_line_segments,
    const pb::ExtractorConfig& config,
    int64_t timestamp = constants::kInvalidTimestamp,
    const CheckMissOfMapKeyInCacheFunction& check_miss_of_map_key_in_cache =
        [](pb::MapObject::MapObjectType, int64_t) { return true; }) {
  DCHECK(map_line_segments != nullptr);
  DCHECK_EQ(polylines.size(), lane_turn_types.size());
  // Early return if the lane is already in map cache.
  // NOTE: Please skip the cache check if timestamp is given since we assume all
  // map object in cache are static.
  if ((timestamp == constants::kInvalidTimestamp) &&
      // We have no valid map object id for traffic_signal associated stop
      // lines.
      (!check_miss_of_map_key_in_cache(map_object_type, map_object_id))) {
    return;
  }

  // We should return N* map_line_segments in which the segment points should be
  // 20 at most. But the `polylines is` a list of polylines of different sizes.
  // So we should merge and repartition those polylines to.
  std::vector<math::geometry::Point2d> merged_polyline;
  size_t total_points = 0;
  for (const auto& polyline : polylines) {
    total_points += polyline.size();
  }
  merged_polyline.reserve(total_points);

  for (const auto& polyline : polylines) {
    merged_polyline.insert(merged_polyline.end(), polyline.begin(),
                           polyline.end());
  }

  // The merged_polyline should have even point nums and
  // max_num_map_objects_segment is 20 by default. So it is OK to partition it
  // without breaking any existing stoplines.
  const size_t max_segment_size = config.max_num_map_objects_segment();
  const size_t num_partitions =
      (merged_polyline.size() + max_segment_size - 1) / max_segment_size;

  for (size_t i = 0; i < num_partitions; ++i) {
    const auto segment_start =
        std::next(merged_polyline.begin(), i * max_segment_size);
    const auto segment_end = std::min(
        std::next(segment_start, max_segment_size), merged_polyline.end());
    const MapObjectKey key{
        .map_object_type = map_object_type,
        .map_object_id = map_object_id,
        .segment_index = static_cast<int>(i),
        .timestamp = timestamp,
        .map_object_id_for_traffic_light = map_object_id_for_traffic_light};
    map_line_segments->emplace(key,
                               MapLineSegment{{segment_start, segment_end},
                                              map_object_type,
                                              std::move(lane_turn_types[i]),
                                              /*speed_limits=*/{}});
  }
}

int64_t GenerateRoadHardBoundaryId(
    const math::geometry::PolylineCurve2d& road_hard_boundary) {
  // Generates an unique identifier of road hard boundary based on start point
  // and end point.
  // TODO(haoxianggao): uses hard boundary id provided by mapping team after
  // road-boundary decoupling is done.
  const auto& start_point = road_hard_boundary.GetStartPoint();
  const auto& end_point = road_hard_boundary.GetEndPoint();

  size_t hashcode = 0;
  boost::hash_combine(hashcode, boost::hash_value(start_point.x()));
  boost::hash_combine(hashcode, boost::hash_value(start_point.y()));
  boost::hash_combine(hashcode, boost::hash_value(end_point.x()));
  boost::hash_combine(hashcode, boost::hash_value(end_point.y()));
  return static_cast<int64_t>(hashcode);
}

// Extracts line segments from given road hard boundary and updates to
// |map_line_segments| if the hard boundary is missed in cache. Nothing happens
// if the hard boundary segment is already in the provided cache.
void ExtractRoadHardBoundarySegmentsNotInCache(
    const math::geometry::PolylineCurve2d& road_hard_boundary,
    const pb::ExtractorConfig& config,
    pb::MapObject::MapObjectType map_line_type,
    std::map<MapObjectKey, MapLineSegment>* map_line_segments,
    CheckMissOfMapKeyInCacheFunction check_miss_of_map_key_in_cache =
        [](pb::MapObject::MapObjectType, int64_t) { return true; }) {
  DCHECK(map_line_segments != nullptr);

  if (road_hard_boundary.empty()) {
    DLOG(WARNING) << "Empty road hard boundary.";
    return;
  }

  const int64_t hard_boundary_id =
      GenerateRoadHardBoundaryId(road_hard_boundary);

  // Early return if the hard boundary is already in map cache.
  if (!check_miss_of_map_key_in_cache(pb::MapObject::ROAD_HARD_BOUNDARY,
                                      hard_boundary_id)) {
    return;
  }

  const float sample_interval =
      GetDynamicSampleIntervalForMapObjectLine(road_hard_boundary);
  std::vector<std::vector<math::geometry::Point2d>> segments = ToSegments(
      road_hard_boundary, config.num_vectors_in_map_object_feature(),
      sample_interval, false,
      config.use_dense_map_sampling() ||
          config.use_dense_map_sampling_for_map_objects_other_than_lane());
  for (size_t i = 0; i < segments.size(); ++i) {
    const MapObjectKey key{
        .map_object_type = pb::MapObject::ROAD_HARD_BOUNDARY,
        .map_object_id = hard_boundary_id,
        .segment_index = static_cast<int>(i),
    };
    map_line_segments->emplace(
        key, MapLineSegment{std::move(segments[i]), map_line_type,
                            /*lane_turn_type=*/hdmap::Lane::UNKNOWN_TURN,
                            /*speed_limits=*/{}});
  }
}

// Extracts line segments from given lane marking and updates to
// |map_line_segments| if the lane marking is missed in cache. Nothing happens
// if the lane marking segment is already in the provided cache.
void ExtractLaneMarkingSegmentsNotInCache(
    const pnc_map::LaneMarking& lane_marking, const pb::ExtractorConfig& config,
    std::map<MapObjectKey, MapLineSegment>* map_line_segments,
    CheckMissOfMapKeyInCacheFunction check_miss_of_map_key_in_cache =
        [](pb::MapObject::MapObjectType, int64_t) { return true; }) {
  DCHECK(map_line_segments != nullptr);
  // Early return if the lane is already in map cache.
  if (!check_miss_of_map_key_in_cache(pb::MapObject::LANE_MARKING,
                                      lane_marking.id())) {
    return;
  }

  const float sample_interval =
      GetDynamicSampleIntervalForMapObjectLine(lane_marking.line());
  std::vector<std::vector<math::geometry::Point2d>> segments = ToSegments(
      lane_marking.line(), config.num_vectors_in_map_object_feature(),
      sample_interval, false,
      config.use_dense_map_sampling() ||
          config.use_dense_map_sampling_for_map_objects_other_than_lane());
  for (size_t i = 0; i < segments.size(); ++i) {
    const MapObjectKey key{.map_object_type = pb::MapObject::LANE_MARKING,
                           .map_object_id = lane_marking.id(),
                           .segment_index = static_cast<int>(i)};
    const pb::MapObject::MapObjectType line_type = ToMapObjectTypeFeature(
        lane_marking.GetAttributeType(segments[i].front()));
    map_line_segments->emplace(key,
                               MapLineSegment{std::move(segments[i]), line_type,
                                              /*speed_limits=*/{}});
  }
}

// Extracts line segments from given border lines and updates to
// |map_line_segments| if the line is missed in cache. Nothing happens if the
// line segment is already in the provided cache.
template <typename PolygonType>
void ExtractBorderLineSegmentsNotInCache(
    const PolygonType& border, const pb::ExtractorConfig& config,
    pb::MapObject::MapObjectType map_object_type, int64_t map_object_id,
    std::map<MapObjectKey, MapLineSegment>* map_line_segments,
    float sample_interval = kMapLineSampleInterval,
    int64_t timestamp = constants::kInvalidTimestamp,
    const CheckMissOfMapKeyInCacheFunction& check_miss_of_map_key_in_cache =
        [](pb::MapObject::MapObjectType, int64_t) { return true; }) {
  DCHECK(map_line_segments != nullptr);

  const math::geometry::PolylineCurve2d closed_border_line =
      GetClosedBorderLine(border);
  // To avoid curve fitting problem, we should make sure the length of
  // border-line at least |kMapLineSampleInterval|, then sampled line
  // won't only involves two duplicated points(start point and end point of
  // the border).
  if (closed_border_line.GetTotalArcLength() <= sample_interval) {
    return;
  }
  ExtractSegmentsNotInCache(closed_border_line, config, map_object_type,
                            map_object_id, map_object_type,
                            /*lane_turn_type=*/hdmap::Lane::UNKNOWN_TURN,
                            map_line_segments, timestamp,
                            check_miss_of_map_key_in_cache);
}

// Gets map of map object line segments which are missed in current cache
// based on config and nearby map objects. Nothing happens if the segment is
// already in the provided cache.
std::map<MapObjectKey, MapLineSegment> GetMapLineSegmentsNotInCache(
    const SceneContext& scene_context, const pb::ExtractorConfig& config,
    const CheckMissOfMapKeyInCacheFunction& check_miss_of_map_key_in_cache =
        [](pb::MapObject::MapObjectType, int64_t) { return true; }) {
  std::map<MapObjectKey, MapLineSegment> map_line_segments;
  for (const auto& map_object_type : config.map_object_types()) {
    switch (map_object_type) {
      case pb::MapObject::LANE:
      case pb::MapObject::LANE_WITHOUT_VIRTUAL:
      case pb::MapObject::LANE_WITHOUT_VIRTUAL_IN_JUNCTION: {
        for (const pnc_map::Lane* lane : scene_context.nearby_lanes()) {
          if (lane->type() == hdmap::Lane_LaneType_VIRTUAL) {
            // Skips the extraction of all virtual lanes.
            if (map_object_type == pb::MapObject::LANE_WITHOUT_VIRTUAL) {
              continue;
            }
            // Only skips the extraction of virtual lanes in junction.
            if ((map_object_type ==
                 pb::MapObject::LANE_WITHOUT_VIRTUAL_IN_JUNCTION) &&
                (lane->IsInJunction())) {
              continue;
            }
          }

          ExtractLaneSegmentsNotInCache(lane, config, &map_line_segments,
                                        check_miss_of_map_key_in_cache);
        }
      } break;

      case pb::MapObject::LANE_MARKING: {
        for (const pnc_map::Lane* lane : scene_context.nearby_lanes()) {
          ExtractLaneMarkingSegmentsNotInCache(
              *DCHECK_NOTNULL(lane->left_marking()), config, &map_line_segments,
              check_miss_of_map_key_in_cache);
          ExtractLaneMarkingSegmentsNotInCache(
              *DCHECK_NOTNULL(lane->right_marking()), config,
              &map_line_segments, check_miss_of_map_key_in_cache);
        }
      } break;

      case pb::MapObject::LANE_SECTION: {
        std::set<int64_t> visited_section_set;
        for (const pnc_map::Lane* lane : scene_context.nearby_lanes()) {
          const pnc_map::Section& section =
              *DCHECK_NOTNULL(DCHECK_NOTNULL(lane)->section());
          // Early return if this section's feature has been extracted.
          if (!visited_section_set.insert(section.id()).second) {
            continue;
          }
          const pnc_map::Lane* left_most_vehicle_lane =
              section.GetLeftOrRightMostRunnableLaneForObject(
                  voy::perception::ObjectType::VEHICLE, math::pb::kLeft);
          const pnc_map::Lane* right_most_vehicle_lane =
              section.GetLeftOrRightMostRunnableLaneForObject(
                  voy::perception::ObjectType::VEHICLE, math::pb::kRight);
          if (left_most_vehicle_lane && right_most_vehicle_lane) {
            ExtractLaneMarkingSegmentsNotInCache(
                *DCHECK_NOTNULL(left_most_vehicle_lane->left_marking()), config,
                &map_line_segments, check_miss_of_map_key_in_cache);
            ExtractLaneMarkingSegmentsNotInCache(
                *DCHECK_NOTNULL(right_most_vehicle_lane->right_marking()),
                config, &map_line_segments, check_miss_of_map_key_in_cache);
          }
        }
      } break;

      case pb::MapObject::CROSSWALK: {
        for (const pnc_map::Crosswalk* crosswalk :
             scene_context.nearby_crosswalks()) {
          DCHECK_GE(DCHECK_NOTNULL(crosswalk)->border().points().size(),
                    kMinNumOfPointsForZone)
              << crosswalk->proto().DebugString();

          ExtractBorderLineSegmentsNotInCache(
              DCHECK_NOTNULL(crosswalk)->border(), config,
              pb::MapObject::CROSSWALK, DCHECK_NOTNULL(crosswalk)->id(),
              &map_line_segments, kMapLineSampleInterval,
              constants::kInvalidTimestamp, check_miss_of_map_key_in_cache);
        }
      } break;

      case pb::MapObject::CROSSWALK_CENTERLINE: {
        for (const pnc_map::Crosswalk* crosswalk :
             scene_context.nearby_crosswalks()) {
          if (DCHECK_NOTNULL(crosswalk)->center_line() == nullptr) {
            continue;
          }
          ExtractSegmentsNotInCache(
              *(DCHECK_NOTNULL(crosswalk)->center_line()), config,
              pb::MapObject::CROSSWALK_CENTERLINE,
              DCHECK_NOTNULL(crosswalk)->id(),
              pb::MapObject::CROSSWALK_CENTERLINE,
              /*lane_turn_type=*/hdmap::Lane::UNKNOWN_TURN, &map_line_segments,
              constants::kInvalidTimestamp, check_miss_of_map_key_in_cache);
        }
      } break;

      case pb::MapObject::EXIT_ZONE: {
        for (const pnc_map::Zone* exit_zone :
             scene_context.nearby_exit_zones()) {
          DCHECK_GE(DCHECK_NOTNULL(exit_zone)->border().points().size(),
                    kMinNumOfPointsForZone)
              << exit_zone->proto().DebugString();

          ExtractBorderLineSegmentsNotInCache(
              DCHECK_NOTNULL(exit_zone)->border(), config,
              pb::MapObject::EXIT_ZONE, DCHECK_NOTNULL(exit_zone)->id(),
              &map_line_segments, kMapLineSampleInterval,
              constants::kInvalidTimestamp, check_miss_of_map_key_in_cache);
        }
      } break;

      case pb::MapObject::MEDIAN_STRIP: {
        for (const pnc_map::Zone* median_strip_zone :
             scene_context.nearby_median_strip_zones()) {
          DCHECK_GE(DCHECK_NOTNULL(median_strip_zone)->border().points().size(),
                    kMinNumOfPointsForZone)
              << median_strip_zone->proto().DebugString();

          ExtractBorderLineSegmentsNotInCache(
              DCHECK_NOTNULL(median_strip_zone)->border(), config,
              pb::MapObject::MEDIAN_STRIP,
              DCHECK_NOTNULL(median_strip_zone)->id(), &map_line_segments,
              kMapLineSampleInterval, constants::kInvalidTimestamp,
              check_miss_of_map_key_in_cache);
        }
      } break;

      case pb::MapObject::FENCE: {
        for (const pnc_map::Zone* fence : scene_context.nearby_fences()) {
          DCHECK_GE(DCHECK_NOTNULL(fence)->border().points().size(),
                    kMinNumOfPointsForZone)
              << fence->proto().DebugString();

          ExtractBorderLineSegmentsNotInCache(
              DCHECK_NOTNULL(fence)->border(), config, pb::MapObject::FENCE,
              DCHECK_NOTNULL(fence)->id(), &map_line_segments,
              kMapLineSampleInterval, constants::kInvalidTimestamp,
              check_miss_of_map_key_in_cache);
        }
      } break;

      case pb::MapObject::JUNCTION: {
        for (const pnc_map::Junction* junction :
             scene_context.nearby_junctions()) {
          DCHECK_GE(DCHECK_NOTNULL(junction)->border().points().size(),
                    kMinNumOfPointsForZone)
              << junction->proto().DebugString();

          ExtractBorderLineSegmentsNotInCache(
              DCHECK_NOTNULL(junction)->border(), config,
              pb::MapObject::JUNCTION, DCHECK_NOTNULL(junction)->id(),
              &map_line_segments, kMapLineSampleInterval,
              constants::kInvalidTimestamp, check_miss_of_map_key_in_cache);
        }
      } break;

      case pb::MapObject::ROAD_HARD_BOUNDARY: {
        for (const pnc_map::Road* road : scene_context.nearby_roads()) {
          for (const auto& hard_boundary_line :
               road->left_boundary().hard_boundary_lines) {
            ExtractRoadHardBoundarySegmentsNotInCache(
                hard_boundary_line.hard_boundary, config,
                pb::MapObject::ROAD_HARD_BOUNDARY, &map_line_segments,
                check_miss_of_map_key_in_cache);
          }

          for (const auto& hard_boundary_line :
               road->right_boundary().hard_boundary_lines) {
            ExtractRoadHardBoundarySegmentsNotInCache(
                hard_boundary_line.hard_boundary, config,
                pb::MapObject::ROAD_HARD_BOUNDARY, &map_line_segments,
                check_miss_of_map_key_in_cache);
          }

          if (config.extract_middle_hard_boundary()) {
            for (const auto& hard_boundary_line :
                 road->middle_boundary().hard_boundary_lines) {
              ExtractRoadHardBoundarySegmentsNotInCache(
                  hard_boundary_line.hard_boundary, config,
                  pb::MapObject::ROAD_HARD_BOUNDARY, &map_line_segments,
                  check_miss_of_map_key_in_cache);
            }
          }
        }
      } break;

      case pb::MapObject::CONSTRUCTION_ZONE: {
        for (const auto& construction_zone :
             scene_context.construction_zones()) {
          DCHECK_GE(construction_zone->zone().points().size(),
                    kMinNumOfPointsForZone)
              << construction_zone->DebugString();

          ExtractBorderLineSegmentsNotInCache(
              DCHECK_NOTNULL(construction_zone)->zone(), config,
              pb::MapObject::CONSTRUCTION_ZONE,
              DCHECK_NOTNULL(construction_zone)->id(), &map_line_segments,
              kMapLineSampleInterval, scene_context.timestamp(),
              check_miss_of_map_key_in_cache);
        }
      } break;

      case pb::MapObject::SIDEWALK: {
        for (const pnc_map::Zone* side_walk :
             scene_context.nearby_sidewalks()) {
          DCHECK_GE(DCHECK_NOTNULL(side_walk)->border().points().size(),
                    kMinNumOfPointsForZone)
              << side_walk->proto().DebugString();

          ExtractBorderLineSegmentsNotInCache(
              DCHECK_NOTNULL(side_walk)->border(), config,
              pb::MapObject::SIDEWALK, DCHECK_NOTNULL(side_walk)->id(),
              &map_line_segments, kMapLineSampleInterval,
              constants::kInvalidTimestamp, check_miss_of_map_key_in_cache);
        }
      } break;

      case pb::MapObject::ROUNDABOUT: {
        for (const pnc_map::Zone* roundabout :
             scene_context.nearby_roundabouts()) {
          DCHECK_GE(DCHECK_NOTNULL(roundabout)->border().points().size(),
                    kMinNumOfPointsForZone)
              << roundabout->proto().DebugString();

          ExtractBorderLineSegmentsNotInCache(
              DCHECK_NOTNULL(roundabout)->border(), config,
              pb::MapObject::ROUNDABOUT, DCHECK_NOTNULL(roundabout)->id(),
              &map_line_segments, kMapLineSampleInterval,
              constants::kInvalidTimestamp, check_miss_of_map_key_in_cache);
        }
      } break;

      case pb::MapObject::MAP_CONSTRUCTION_ZONE: {
        for (const pnc_map::Zone* map_cz :
             scene_context.nearby_map_construction_zones()) {
          DCHECK_GE(DCHECK_NOTNULL(map_cz)->border().points().size(),
                    kMinNumOfPointsForZone)
              << map_cz->proto().DebugString();

          ExtractBorderLineSegmentsNotInCache(
              DCHECK_NOTNULL(map_cz)->border(), config,
              pb::MapObject::MAP_CONSTRUCTION_ZONE,
              DCHECK_NOTNULL(map_cz)->id(), &map_line_segments,
              kMapLineSampleInterval, constants::kInvalidTimestamp,
              check_miss_of_map_key_in_cache);
        }
      } break;

      case pb::MapObject::DIVERSION_ZONE: {
        for (const pnc_map::Zone* diversion_zone :
             scene_context.nearby_diversion_zones()) {
          DCHECK_GE(DCHECK_NOTNULL(diversion_zone)->border().points().size(),
                    kMinNumOfPointsForZone)
              << diversion_zone->proto().DebugString();

          ExtractBorderLineSegmentsNotInCache(
              DCHECK_NOTNULL(diversion_zone)->border(), config,
              pb::MapObject::DIVERSION_ZONE,
              DCHECK_NOTNULL(diversion_zone)->id(), &map_line_segments,
              kMapLineSampleInterval, constants::kInvalidTimestamp,
              check_miss_of_map_key_in_cache);
        }
      } break;

      case pb::MapObject::BUS_STOP_ZONE: {
        for (const pnc_map::Zone* bus_stop_zone :
             scene_context.nearby_bus_stop_zones()) {
          DCHECK_GE(DCHECK_NOTNULL(bus_stop_zone)->border().points().size(),
                    kMinNumOfPointsForZone)
              << bus_stop_zone->proto().DebugString();

          ExtractBorderLineSegmentsNotInCache(
              DCHECK_NOTNULL(bus_stop_zone)->border(), config,
              pb::MapObject::BUS_STOP_ZONE, DCHECK_NOTNULL(bus_stop_zone)->id(),
              &map_line_segments, kMapLineSampleInterval,
              constants::kInvalidTimestamp, check_miss_of_map_key_in_cache);
        }
      } break;

      case pb::MapObject::BARRIER:
      case pb::MapObject::TRAFFIC_CONE: {
        for (const voy::TrackedObject& hb_tracked_object :
             scene_context.nearby_traffic_cones_and_barriers()) {
          std::vector<math::geometry::Point2d> segment_points;
          for (size_t i = 0;
               i < static_cast<size_t>(hb_tracked_object.contour_size()); ++i) {
            segment_points.push_back(
                math::geometry::Point2d(hb_tracked_object.contour(i).x(),
                                        hb_tracked_object.contour(i).y()));
          }
          const pb::MapObject::MapObjectType map_object_type =
              hb_tracked_object.object_type() ==
                      voy::perception::ObjectType::TRAFFIC_CONE
                  ? pb::MapObject::TRAFFIC_CONE
                  : pb::MapObject::BARRIER;
          ExtractBorderLineSegmentsNotInCache(
              math::geometry::PolygonWithCache2d(segment_points), config,
              map_object_type, hb_tracked_object.id(), &map_line_segments,
              kMinTrafficConeOrBarrierSampleInterval,
              hb_tracked_object.sync_state().timestamp(),
              check_miss_of_map_key_in_cache);
        }
      } break;

      case pb::MapObject::CURB: {
        for (const auto& pair : scene_context.nearby_curbs()) {
          ExtractSegmentsNotInCache(
              pair.second, config, pb::MapObject::CURB, pair.first,
              pb::MapObject::CURB,
              /*lane_turn_type=*/hdmap::Lane::UNKNOWN_TURN, &map_line_segments,
              constants::kInvalidTimestamp, check_miss_of_map_key_in_cache);
        }
      } break;

      case pb::MapObject::LANE_TRAFFIC_SIGNAL_STOP_LINE: {
        // The traffic signals are all bounded to virtual lanes.
        // For lanes with same turn types and same road ids, we extract only
        // once as the should share the same traffic signals.
        std::unordered_set<std::pair<hdmap::Lane::Turn, int64_t>, PairHash>
            seen_keys;
        std::unordered_set<int64_t> seen_traffic_signals;
        for (const pnc_map::Lane* lane : scene_context.nearby_lanes()) {
          // Lane level skips.
          if (lane == nullptr || lane->traffic_signals().empty() ||
              !seen_keys
                   .emplace(std::make_pair(lane->turn(),
                                           lane->section()->road()->id()))
                   .second) {
            continue;
          }
          for (const auto associate_traffic_signal : lane->traffic_signals()) {
            const pnc_map::TrafficSignal* traffic_signal =
                associate_traffic_signal.signal;
            // Skips empty stop lines and no-vehicle traffic signals.
            if (!seen_traffic_signals.emplace(traffic_signal->id()).second ||
                traffic_signal->stop_lines().empty() ||
                traffic_signal->object_type() !=
                    hdmap::Signal_ObjectType::Signal_ObjectType_VEHICLE) {
              continue;
            }

            // Get all stop lines associated to the same traffic signal.
            std::vector<math::geometry::Polyline2d>
                traffic_signal_stop_line_vec;
            std::vector<hdmap::Lane::Turn> traffic_signal_stop_line_turn_types;

            traffic_signal_stop_line_vec.reserve(
                traffic_signal->stop_lines().size());
            traffic_signal_stop_line_turn_types.reserve(
                traffic_signal->stop_lines().size());

            for (const auto* stop_line : traffic_signal->stop_lines()) {
              traffic_signal_stop_line_vec.push_back(stop_line->polyline());
              traffic_signal_stop_line_turn_types.push_back(
                  stop_line->lane()->turn());
            }
            // NOTE: The lane id serves as the watch line id.
            // LANE_TRAFFIC_SIGNAL_STOP_LINE + signal_id locates a specific map
            // object.
            ExtractStopLineSegmentsNotInCache(
                std::move(traffic_signal_stop_line_vec),
                pb::MapObject::LANE_TRAFFIC_SIGNAL_STOP_LINE,
                traffic_signal->id(), lane->id(),
                std::move(traffic_signal_stop_line_turn_types),
                &map_line_segments, config, constants::kInvalidTimestamp,
                check_miss_of_map_key_in_cache);
          }
        }
      } break;

      case pb::MapObject::LANE_WATCH_LINE: {
        for (const auto* lane :
             GetUniqueLanesWithWatchLine(scene_context.nearby_lanes())) {
          DCHECK(lane->watch_line().has_value());
          std::vector<math::geometry::Polyline2d> segments = {
              lane->watch_line().value()};
          std::vector<hdmap::Lane::Turn> turn_types = {lane->turn()};
          ExtractStopLineSegmentsNotInCache(
              std::move(segments), pb::MapObject::LANE_WATCH_LINE, lane->id(),
              lane->id(), std::move(turn_types), &map_line_segments, config,
              constants::kInvalidTimestamp, check_miss_of_map_key_in_cache);
        }
      } break;

      default:
        DCHECK(false) << "Does not support this type of map feature "
                      << " extraction:"
                      << pb::MapObject::MapObjectType_Name(map_object_type);
    }
  }

  return map_line_segments;
}

// Constructs map features from given segments of map objects.
std::map<MapObjectKey, MapObjectFeature> ConstructMapFeaturesFromSegments(
    const std::map<MapObjectKey, MapLineSegment>& map_line_segments,
    const std::set<std::pair<pb::MapObject::MapObjectType,
                             int64_t /*map object id*/>>& ego_lane_line_set,
    VectornetFeatureProcessor& vectornet_feature_processor) {
  std::map<MapObjectKey, MapObjectFeature> new_map_objects_features;
  for (const auto& [key, line_segment] : map_line_segments) {
    // LANE_TRAFFIC_SIGNAL_STOP_LINE and LANE_WATCH_LINE segments are special
    // cases. Although they are originally independent polylines, we flatten
    // them into a single list of points to ensure compatibility with the map
    // feature extraction process.
    // For example, given a list of stoplines [[p1, p2], [p3, p4]], flattening
    // them into [p1, p2, p3, p4] may generate unnecessary intermediate segments
    // (e.g., [p2, p3]) when reconstructed as [[p1, p2], [p2, p3], [p3, p4]].
    // So we will skip constructing [p2, p3].
    // TODO(yorkchen): Refactor to make sure the pipeline supports generating
    // jumping segments.
    const bool is_jumping_map_segment =
        (line_segment.line_type ==
             pb::MapObject::LANE_TRAFFIC_SIGNAL_STOP_LINE ||
         line_segment.line_type == pb::MapObject::LANE_WATCH_LINE);

    const bool is_in_ego_lane_seq = gtl::ContainsKey(
        ego_lane_line_set,
        std::make_pair(key.map_object_type, key.map_object_id));

    MapObjectFeature& map_object_feature = new_map_objects_features[key];
    const std::vector<math::geometry::Point2d>& segment_points =
        line_segment.segment_points;
    const std::vector<float>& speed_limits = line_segment.speed_limits;
    if (!speed_limits.empty()) {
      DCHECK_EQ(speed_limits.size(), segment_points.size());
    }
    const std::vector<pb::MapObject::MapObjectType>& left_marking_types =
        line_segment.left_marking_types;
    if (!left_marking_types.empty()) {
      DCHECK_EQ(left_marking_types.size(), segment_points.size());
    }
    const std::vector<pb::MapObject::MapObjectType>& right_marking_types =
        line_segment.right_marking_types;
    if (!right_marking_types.empty()) {
      DCHECK_EQ(right_marking_types.size(), segment_points.size());
    }
    const std::vector<math::geometry::Point2d>& left_marking_points =
        line_segment.left_marking_points;
    if (!left_marking_points.empty()) {
      DCHECK_EQ(left_marking_points.size(), segment_points.size());
    }
    const std::vector<math::geometry::Point2d>& right_marking_points =
        line_segment.right_marking_points;
    if (!right_marking_points.empty()) {
      DCHECK_EQ(right_marking_points.size(), segment_points.size());
    }
    for (size_t j = 0; j + 1 < segment_points.size();
         j = j + 1 + static_cast<int>(is_jumping_map_segment)) {
      const math::geometry::Point2d& start_point = segment_points[j];
      const math::geometry::Point2d& end_point = segment_points[j + 1];
      const float speed_limit = speed_limits.empty() ? 0.0F : speed_limits[j];
      const pb::MapObject::MapObjectType left_marking_type =
          left_marking_types.empty() ? pb::MapObject::UNKNOWN_TYPE
                                     : left_marking_types[j];
      const pb::MapObject::MapObjectType right_marking_type =
          right_marking_types.empty() ? pb::MapObject::UNKNOWN_TYPE
                                      : right_marking_types[j];
      const std::optional<math::geometry::Point2d> left_marking_start_point =
          left_marking_points.empty()
              ? std::nullopt
              : std::make_optional(left_marking_points[j]);
      const std::optional<math::geometry::Point2d> left_marking_end_point =
          left_marking_points.empty()
              ? std::nullopt
              : std::make_optional(left_marking_points[j + 1]);
      const std::optional<math::geometry::Point2d> right_marking_start_point =
          right_marking_points.empty()
              ? std::nullopt
              : std::make_optional(right_marking_points[j]);
      const std::optional<math::geometry::Point2d> right_marking_end_point =
          right_marking_points.empty()
              ? std::nullopt
              : std::make_optional(right_marking_points[j + 1]);

      std::vector<float> features =
          vectornet_feature_processor.RunMapFeatureExtractors(
              /*extractor_input=*/MapFeatureExtractorInput{
                  .start_point = start_point,
                  .end_point = end_point,
                  .speed_limit = speed_limit,
                  .lane_turn_type = line_segment.lane_turn_type,
                  .map_object_type = line_segment.line_type,
                  .left_marking_type = left_marking_type,
                  .right_marking_type = right_marking_type,
                  .segment_width = line_segment.lane_segment_width,
                  .left_marking_start_point = left_marking_start_point,
                  .left_marking_end_point = left_marking_end_point,
                  .right_marking_start_point = right_marking_start_point,
                  .right_marking_end_point = right_marking_end_point});
      map_object_feature.features.push_back(std::move(features));
    }
    // Some times the stoplines could be head-to-tail. So the segment points
    // could be [p1, p2, p2, p3] We skip duplicate points to avoid `same arc
    // length` error when creating a PolylineCurve2d.
    std::vector<math::geometry::Point2d> curve_points;
    curve_points.reserve(segment_points.size());

    if (is_jumping_map_segment) {
      curve_points.emplace_back(segment_points.front());
      for (auto it = segment_points.begin() + 1; it != segment_points.end();
           ++it) {
        if (*it != curve_points.back()) {
          curve_points.emplace_back(*it);
        }
      }
    }

    map_object_feature.segment = math::geometry::PolylineCurve2d(
        is_jumping_map_segment ? std::move(curve_points)
                               : std::move(segment_points));
    map_object_feature.is_in_ego_lane_seq = is_in_ego_lane_seq;
  }
  return new_map_objects_features;
}

// Gets agent tag based on tracked object's states.
pb::ExtractorConfig::AgentTag GetAgentTag(
    const voy::TrackedObject& tracked_object) {
  if (tracked_object.object_type() != voy::perception::ObjectType::UNKNOWN) {
    return pb::ExtractorConfig::UNKNOWN_TAG;
  }

  auto has_attribute = [](const voy::TrackedObject& tracked_object,
                          voy::perception::Attribute attribute) {
    return std::find(tracked_object.attributes().begin(),
                     tracked_object.attributes().end(),
                     attribute) != tracked_object.attributes().end();
  };

  constexpr double kFastMovingSpeedForCyclistWithoutPerson = 2.0;  // m/s.
  if (has_attribute(tracked_object,
                    voy::perception::Attribute::CYC_WITHOUT_PERSON_ATTR) &&
      tracked_object.velocity() > kFastMovingSpeedForCyclistWithoutPerson) {
    return pb::ExtractorConfig::MAYBE_CYCLIST;
  }
  if (vectornet_util::IsMovingUnknownObject(tracked_object)) {
    return pb::ExtractorConfig::MOVING_UNKNOWN;
  }
  if (has_attribute(tracked_object, voy::perception::Attribute::IS_DRIVABLE)) {
    return pb::ExtractorConfig::IS_DRIVABLE_UNKNOWN;
  }
  if (has_attribute(tracked_object,
                    voy::perception::Attribute::BETTER_NOT_DRIVE)) {
    return pb::ExtractorConfig::IS_BETTER_NOT_DRIVE_UNKNOWN;
  }
  return pb::ExtractorConfig::STATIONARY_UNKNOWN;
}

// Gets object nearby obstacles and filter obstacles by given filters.
std::vector<int> GetNearbyObstacleIdsWithFilters(
    const voy::TrackedObject& object,
    const perception::ObstacleMap& obstacle_map, int search_scope_in_grid_size,
    const std::vector<bool>& filtered_obstacle_mask) {
  // Find object nearby obstacles.
  const glm::dvec2 object_center{object.center_x(), object.center_y()};
  const std::vector<int>& object_nearby_obstacle_ids =
      obstacle_map.GetNearbyObstacleIds(object_center,
                                        search_scope_in_grid_size);

  std::vector<int> ret_obstacle_ids;
  for (int obstacle_id : object_nearby_obstacle_ids) {
    // Filter obstacles.
    if (filtered_obstacle_mask[obstacle_id]) {
      continue;
    }
    ret_obstacle_ids.push_back(obstacle_id);
  }
  return ret_obstacle_ids;
}

// Gets sorted obstacles' grid indexes for large cluster(>|max_num_obstacles|).
std::unordered_map<int, std::vector<int>> GetLargeClusterSortedGridIndexes(
    const ObstacleSegmentationResult& obstacle_segmentation_result,
    size_t max_num_obstacles) {
  std::unordered_map<int, std::vector<int>> large_cluster_sorted_grid_indexes;
  const perception::ObstacleMap& obstacle_map =
      *DCHECK_NOTNULL(obstacle_segmentation_result.obstacle_map);
  for (auto [cluster_id, obstacles] : obstacle_segmentation_result.clusters) {
    if (obstacles.size() <= max_num_obstacles) {
      continue;
    }
    std::vector<int> obstacle_indexes;
    obstacle_indexes.reserve(obstacles.size());
    for (const perception::Obstacle* obstacle : obstacles) {
      DCHECK(obstacle);
      obstacle_indexes.push_back(perception::ObstacleMap::GridIndexInMap(
          obstacle->x() - obstacle_map.pose().x(),
          obstacle->y() - obstacle_map.pose().y()));
    }
    std::sort(obstacle_indexes.begin(), obstacle_indexes.end());
    large_cluster_sorted_grid_indexes.emplace(cluster_id,
                                              std::move(obstacle_indexes));
  }
  return large_cluster_sorted_grid_indexes;
}

// Gets |ObstacleClusterKey| in stored feature map.
ObstacleClusterKey GetObstacleClusterKey(
    const perception::Obstacle& obstacle, int cluster_id,
    const ObstacleSegmentationResult& obstacle_segmentation_result,
    const std::unordered_map<int, std::vector<int>>&
        large_cluster_sorted_grid_idx,
    int max_num_obstacles) {
  const int cluster_size = static_cast<int>(
      obstacle_segmentation_result.clusters.at(cluster_id).size());
  // Directly return cluster key if cluster no need to be partitioned.
  if (cluster_size <= max_num_obstacles) {
    ObstacleClusterKey obstacle_cluster_key{.cluster_id = cluster_id,
                                            .segment_idx = 0};
    return obstacle_cluster_key;
  }

  // NOTE(lipei): Since we has set a maximum obstacles for each cluster, and
  // some clusters may have more obstacles. To encode these clusters, we
  // would partition these clusters to a few smaller clusters by its spatial
  // position index. Therefore, here need to compute its segment key(cluster's
  // partition index).
  const perception::ObstacleMap& obstacle_map =
      *DCHECK_NOTNULL(obstacle_segmentation_result.obstacle_map);
  const int obstacle_grid_idx = perception::ObstacleMap::GridIndexInMap(
      obstacle.x() - obstacle_map.pose().x(),
      obstacle.y() - obstacle_map.pose().y());
  const std::vector<int>& cluster_sorted_grid_indexes =
      large_cluster_sorted_grid_idx.at(cluster_id);
  const auto larger_iter =
      std::upper_bound(cluster_sorted_grid_indexes.begin(),
                       cluster_sorted_grid_indexes.end(), obstacle_grid_idx);
  DCHECK(larger_iter != cluster_sorted_grid_indexes.begin());
  const int order =
      std::distance(cluster_sorted_grid_indexes.begin(), larger_iter) - 1;
  DCHECK_GE(order, 0);
  return {.cluster_id = cluster_id,
          .segment_idx = static_cast<int>(order / max_num_obstacles)};
}

// Use priority_queue to select at most |max_num_lidar_context_objects|
// agent's nearby cluster in the ascending order.
std::vector<ObstacleClusterKey> SelectNearbyLidarContextObjects(
    const std::map<ObstacleClusterKey, double>& object_candidate_clusters,
    int max_num_lidar_context_objects) {
  using T = std::pair<ObstacleClusterKey, double>;
  static auto compare = [](const T& a, const T& b) {
    return a.second < b.second;
  };
  std::priority_queue<T, std::vector<T>, decltype(compare)> pq(compare);
  for (auto iter = object_candidate_clusters.cbegin();
       iter != object_candidate_clusters.cend(); ++iter) {
    pq.emplace(*iter);
    if (pq.size() > static_cast<size_t>(max_num_lidar_context_objects)) {
      pq.pop();
    }
  }
  std::vector<ObstacleClusterKey> nearby_cluster_keys;
  nearby_cluster_keys.reserve(pq.size());
  while (!pq.empty()) {
    nearby_cluster_keys.push_back(pq.top().first);
    pq.pop();
  }

  // According to the distance to the target agent, the object cluster keys
  // are saved in order from near to far.
  std::reverse(nearby_cluster_keys.begin(), nearby_cluster_keys.end());
  return nearby_cluster_keys;
}

std::unordered_set<int64_t> GetRecommendLaneIds(
    const std::vector<const pnc_map::Section*>& section_route) {
  if (section_route.empty()) {
    return {};
  }
  std::unordered_set<int64_t> ids;
  for (int i = static_cast<int>(section_route.size()) - 1; i >= 0; --i) {
    if (i > 0) {
      const auto& successors = section_route[i - 1]->successors();
      DCHECK(std::find(successors.begin(), successors.end(),
                       section_route[i]) != successors.end());
    }
    const pnc_map::Section* current_section = section_route[i];
    const std::vector<const pnc_map::Lane*>& section_lanes =
        current_section->lanes();
    if (ids.empty()) {
      for (const auto* lane : section_lanes) {
        ids.insert(lane->id());
      }
    } else {
      for (const auto* lane : section_lanes) {
        const std::vector<const pnc_map::Lane*>& successor_lanes =
            lane->successors();
        if (std::any_of(successor_lanes.begin(), successor_lanes.end(),
                        [&ids](const auto* successor_lane) {
                          return ids.find(successor_lane->id()) != ids.end();
                        })) {
          ids.insert(lane->id());
        }
      }
    }
  }
  return ids;
}

}  // namespace

VectornetFeatureExtractor::VectornetFeatureExtractor(
    const pb::ExtractorConfig& config, const pb::FeatureSpec& feature_spec,
    const ::pb::ModelType& model_type)
    : num_input_tensors_(kNumInputTensorsForVectornet),
      enable_skeleton_feature_extraction_(config.num_histories_in_skeleton() >
                                          0),
      enable_route_feature_extraction_(config.num_vectors_in_route_feature() >
                                       0),
      enable_lidar_patch_feature_extraction_(
          config.num_histories_in_lidar_patch_feature() > 0),
      enable_patch_cuboid_feature_extraction_(
          config.num_histories_in_cuboid_feature() > 0),
      enable_lidar_context_feature_extraction_(
          config.max_num_lidar_context_objects() > 0),
      enable_obstacle_feature_extraction_(config.max_num_obstacles_segment() >
                                          0),
      enable_traffic_light_feature_extraction_(config.max_num_traffic_lights() >
                                               0),
      config_(config),
      model_type_(model_type),
      vectornet_feature_processor_(feature_spec) {
  if (enable_skeleton_feature_extraction_) {
    num_input_tensors_ += kNumInputTensorsForFeatureWithMask;
  }
  if (enable_route_feature_extraction_) {
    num_input_tensors_ += kNumInputTensorsForRoute;
  }
  if (enable_lidar_patch_feature_extraction_) {
    num_input_tensors_ += kNumInputTensorsForFeatureWithMask;
  }
  if (enable_patch_cuboid_feature_extraction_) {
    num_input_tensors_ += kNumInputTensorsForFeatureWithMask;
  }
  if (enable_lidar_context_feature_extraction_) {
    num_input_tensors_ += kNumInputTensorsForFeatureWithMask;
  }
  if (enable_obstacle_feature_extraction_) {
    num_input_tensors_ += kNumInputTensorsForFeatureWithMask;
  }
  if (enable_traffic_light_feature_extraction_) {
    num_input_tensors_ += kNumInputTensorsForFeatureWithMask;
  }
}

void VectornetFeatureExtractor::ExtractAgentFeatures(
    const Agent& ego_agent, const std::vector<const Agent*>& non_ego_agents) {
  TRACE_EVENT_SCOPE(prediction, VectornetFeatureExtractor_ExtractAgentFeatures);
  std::vector<int> valid_traffic_agent_sign(non_ego_agents.size(), 0);
  std::vector<AgentFeature> possible_agents_features;
  possible_agents_features.resize(non_ego_agents.size() + 1);

  std::atomic<int> agent_num;
  agent_num.store(0, std::memory_order_relaxed);
  auto extract_task = [&ego_agent, &non_ego_agents, &valid_traffic_agent_sign,
                       &agent_num, &possible_agents_features,
                       this](size_t idx) {
    if (idx == 0) {
      possible_agents_features[idx] = ExtractOneAgentFeature(ego_agent);
      agent_num.fetch_add(1, std::memory_order_relaxed);
      return;
    }

    const auto& agent_ptr = non_ego_agents[idx - 1];
    if (config_.treat_traffic_cone_and_barrier_as_map_objects() &&
        (agent_ptr->object_type() ==
             voy::perception::ObjectType::TRAFFIC_CONE ||
         agent_ptr->object_type() == voy::perception::ObjectType::BARRIER)) {
      return;
    }
    // If the agent matches any type in the obstacle types, it will be
    // processed as an obstacle.
    const auto& obstacle_types = config_.obstacle_types();
    if (std::find(obstacle_types.begin(), obstacle_types.end(),
                  agent_ptr->object_type()) != obstacle_types.end()) {
      if (agent_ptr->object_type() == voy::perception::ObjectType::UNKNOWN) {
        // If treat_dynamic_unknown_as_agent is set to true, dynamic unknown
        // objects will be processed as agent.
        if (config_.treat_dynamic_unknown_as_agent() &&
            !vectornet_util::IsMovingUnknownObject(
                agent_ptr->tracked_object())) {
          return;
        }
      } else {
        return;
      }
    }
    possible_agents_features[idx] = ExtractOneAgentFeature(*agent_ptr);
    valid_traffic_agent_sign[idx - 1] = 1;
    agent_num.fetch_add(1, std::memory_order_relaxed);
  };

  tbb::parallel_for(static_cast<size_t>(0), 1 + non_ego_agents.size(),
                    extract_task);

  agents_features_.clear();
  agents_features_.reserve(agent_num.load(std::memory_order_relaxed));
  agents_features_.emplace_back(std::move(possible_agents_features[0]));
  for (size_t i = 1; i < possible_agents_features.size(); ++i) {
    if (valid_traffic_agent_sign[i - 1]) {
      agents_features_.emplace_back(std::move(possible_agents_features[i]));
    }
  }
}

void VectornetFeatureExtractor::ExtractMapObjectFeatures(
    const SceneContext& scene_context) {
  TRACE_EVENT_SCOPE(prediction,
                    VectornetFeatureExtractor_ExtractMapObjectFeatures);
  // Get map element in the ego route.
  std::set<std::pair<pb::MapObject::MapObjectType, int64_t /*map object id*/>>
      ego_lane_line_set;
  for (const pnc_map::Lane* lane : scene_context.ego_lane_sequence().lanes) {
    ego_lane_line_set.emplace(pb::MapObject::LANE, DCHECK_NOTNULL(lane)->id());
    ego_lane_line_set.emplace(
        pb::MapObject::LANE_MARKING,
        DCHECK_NOTNULL(DCHECK_NOTNULL(lane)->left_marking())->id());
    ego_lane_line_set.emplace(
        pb::MapObject::LANE_MARKING,
        DCHECK_NOTNULL(DCHECK_NOTNULL(lane)->right_marking())->id());
  }

  // Convert map elements specified in config into segments.
  // NOTE: Assume map object features are static and reuse the result in the
  // previous iteration. For time-dependent feature (like traffic-light),
  // it should be filled in |PopulateNormalFeatureAndMaskTensors|.
  std::set<MapObjectKey> map_object_keys_hit_in_cache;
  const std::map<MapObjectKey, MapObjectFeature>& cached_map_segment_features =
      map_objects_features_;
  CheckMissOfMapKeyInCacheFunction check_miss_of_map_key_in_cache =
      [&map_object_keys_hit_in_cache, &cached_map_segment_features](
          pb::MapObject::MapObjectType map_object_type, int64_t map_object_id) {
        const MapObjectKey key{.map_object_type = map_object_type,
                               .map_object_id = map_object_id,
                               .segment_index = 0};
        if (cached_map_segment_features.find(key) !=
            cached_map_segment_features.end()) {
          map_object_keys_hit_in_cache.insert(key);
          return false;
        }
        return true;
      };
  const std::map<MapObjectKey, MapLineSegment> map_line_segments_not_in_cache =
      GetMapLineSegmentsNotInCache(scene_context, config_,
                                   check_miss_of_map_key_in_cache);
  for (const auto& [key, line_segment] : map_line_segments_not_in_cache) {
    // Check our assumption that all missed map object keys can be not found in
    // |map_objects_features_|.
    auto iter = map_objects_features_.find(key);
    DCHECK(iter == map_objects_features_.end())
        << pb::MapObject::MapObjectType_Name(key.map_object_type)
        << key.map_object_id << " exists but does not hit in cache.";
  }
  for (const auto& key : map_object_keys_hit_in_cache) {
    auto iter = map_objects_features_.find(key);
    // Check our assumption that all hit map object keys can be found in
    // |map_objects_features_|.
    DCHECK(iter != map_objects_features_.end());
  }

  // 1. Construct map features from map segments if they are not in cache.
  std::map<MapObjectKey, MapObjectFeature> new_map_objects_features =
      ConstructMapFeaturesFromSegments(map_line_segments_not_in_cache,
                                       ego_lane_line_set,
                                       vectornet_feature_processor_);

  // 2. Modify and copy the map segment features if they are found in
  // |map_segment_feature| of last frame.
  for (auto& [map_segment_key, map_segment_feature] : map_objects_features_) {
    const MapObjectKey map_object_key{
        .map_object_type = map_segment_key.map_object_type,
        .map_object_id = map_segment_key.map_object_id,
        .segment_index = 0};
    if (gtl::ContainsKey(map_object_keys_hit_in_cache, map_object_key)) {
      // Check our assumption that all segments of a map object are either in
      // the cache or not. We only need to check the existence of previous
      // segment for current segment here and leave the other checks for other
      // segment of the map object.
      DCHECK(
          (map_segment_key.segment_index == 0) ||
          (gtl::ContainsKey(
              map_objects_features_,
              MapObjectKey{.map_object_type = map_segment_key.map_object_type,
                           .map_object_id = map_segment_key.map_object_id,
                           .segment_index = map_segment_key.segment_index - 1,
                           .timestamp = map_segment_key.timestamp})));

      const bool is_in_ego_lane_seq = gtl::ContainsKey(
          ego_lane_line_set, std::make_pair(map_segment_key.map_object_type,
                                            map_segment_key.map_object_id));
      map_segment_feature.is_in_ego_lane_seq = is_in_ego_lane_seq;
      new_map_objects_features.emplace(map_segment_key,
                                       std::move(map_segment_feature));
    }
  }

  map_objects_features_ = std::move(new_map_objects_features);
}

std::vector<RouteFeature> VectornetFeatureExtractor::ExtractRouteFeatures(
    const std::vector<std::vector<const pnc_map::Lane*>>& routes,
    const voy::TrackedObject& ego_object, double route_sample_interval,
    bool should_skip_route_truncation) const {
  if (!enable_route_feature_extraction_) {
    return {};
  }
  const int max_num_routes =
      std::min(static_cast<int>(routes.size()), config_.max_num_routes());
  std::vector<RouteFeature> route_features;
  const math::geometry::Point2d ego_point(ego_object.center_x(),
                                          ego_object.center_y());
  for (int i = 0; i < max_num_routes; ++i) {
    math::geometry::PolylineCurve2d ref_line;
    std::vector<int64_t> lane_ids;
    for (const pnc_map::Lane* lane : routes[i]) {
      const auto& center_line = DCHECK_NOTNULL(lane)->center_line();
      ref_line.AppendCurveSegment(center_line);
      lane_ids.push_back(lane->id());
    }
    if (ref_line.empty()) {
      continue;
    }

    DCHECK_GE(ref_line.size(), 2) << "route size: " << routes[i].size();
    const auto ego_proximity =
        ref_line.GetProximity(ego_point, math::pb::UseExtensionFlag::kForbid);
    double start_arc_length =
        should_skip_route_truncation ? 0.0 : ego_proximity.arc_length;
    // At most |num_vectors_in_route_feature| points are extracted.
    const double end_arc_length =
        std::min(start_arc_length + config_.num_vectors_in_route_feature() *
                                        route_sample_interval,
                 ref_line.GetTotalArcLength());
    DCHECK_GE(end_arc_length, start_arc_length);

    // If ego_proximity is too close to end_arc_length, we use the following way
    // to handle sampled points. This may happen when ego will make a uturn or
    // enter a roundabout.
    if (end_arc_length - start_arc_length < constants::kMinValidRouteLength) {
      start_arc_length = std::max(0.0, end_arc_length - 1.0);
    }

    const SpeedLimits speed_limits(routes[i]);
    const RouteType route_type(routes[i]);
    const std::vector<math::geometry::Point2d> sampled_points =
        ref_line.GetSampledPoints(start_arc_length, end_arc_length,
                                  route_sample_interval,
                                  math::pb::UseExtensionFlag::kForbid);
    RouteFeature route_feature;
    DCHECK_GE(sampled_points.size(), 2);
    for (size_t j = 0; j + 1 < sampled_points.size(); ++j) {
      const math::geometry::Point2d& start_point = sampled_points[j];
      const math::geometry::Point2d& end_point = sampled_points[j + 1];
      const pb::MapObject::MapObjectType type =
          route_type.GetRouteType(start_point);
      const float speed_limit = speed_limits.GetSpeedLimit(start_point);

      std::vector<float> feature =
          vectornet_feature_processor_.RunRouteFeatureExtractors(
              /*extractor_input=*/MapFeatureExtractorInput{
                  .start_point = start_point,
                  .end_point = end_point,
                  .speed_limit = speed_limit,
                  .map_object_type = type});
      route_feature.features.push_back(std::move(feature));
    }
    route_feature.lane_sequence_ids = std::move(lane_ids);
    // This field has been deprecated, will remove related code later.
    route_feature.turn = hdmap::Lane::UNKNOWN_TURN;
    route_features.push_back(std::move(route_feature));
  }
  return route_features;
}

// This implement is for extracting section route features.
std::vector<RouteFeature>
VectornetFeatureExtractor::ExtractSectionRouteFeatures(
    const std::vector<const pnc_map::Section*>& route,
    const std::vector<const pnc_map::Lane*>& global_route,
    const voy::TrackedObject& ego_object, double route_sample_interval,
    // TODO(Caojiahe): Test matching valid route instead of extracting
    // imcomplete route.
    bool extract_valid_route) const {
  if (!enable_route_feature_extraction_) {
    return {};
  }
  // TODO(Caojiahe): Change here when we unified section routes/routes in
  // onboard/offboard.
  const int max_num_section_routes = config_.max_num_section_routes() > 0
                                         ? config_.max_num_section_routes()
                                         : config_.max_num_routes();
  std::vector<RouteFeature> route_features;
  const math::geometry::Point2d ego_point(ego_object.center_x(),
                                          ego_object.center_y());
  double accumulated_arc_length = 0;
  std::unordered_set<int64_t> global_route_ids;
  global_route_ids.reserve(global_route.size());
  for (const auto* lane : global_route) {
    global_route_ids.insert(lane->id());
  }
  std::unordered_set<int64_t> recommend_lane_ids = GetRecommendLaneIds(route);

  for (int num_routes = 0, i = 0;
       num_routes < max_num_section_routes &&
       i < static_cast<int>(route.size()) &&
       accumulated_arc_length < constants::kMaxValidRouteLength;
       ++i) {
    double current_section_arc_length = route[i]->GetLength();
    if (i == 0) {
      current_section_arc_length -= route[i]->GetArclength(ego_point);
    }
    std::vector<std::vector<const pnc_map::Lane*>> route_lanes;
    if (extract_valid_route) {
      route_lanes = utility::GetValidRoutes(route[i]->lanes());
    } else {
      route_lanes.reserve(route[i]->lanes().size());
      for (const pnc_map::Lane* lane : route[i]->lanes()) {
        route_lanes.push_back({lane});
      }
    }
    // Some sections in |route| may be behind the current ego pose, which will
    // be skipped if their trimmed arc length is less than 1m. We should check
    // if any lanes have been added to find the first section.
    // TODO(jonathanchen): Delete the corresponding flag logic when we land
    // v3.13.
    const bool is_current_section =
        FLAGS_prediction_enable_mlplanner_trajectory_evaluation
            ? num_routes == 0
            : i == 0;
    for (const auto& lanes : route_lanes) {
      // lanes only contain one lane.
      const pnc_map::Lane* lane = lanes[0];
      const bool is_in_global_route =
          global_route_ids.find(lane->id()) != global_route_ids.end();
      const bool is_recommend =
          recommend_lane_ids.find(lane->id()) != recommend_lane_ids.end();
      const double start_arc_length =
          is_current_section
              ? lane->center_line()
                    .GetProximity(ego_point,
                                  math::pb::UseExtensionFlag::kForbid)
                    .arc_length
              : 0;
      double current_lane_arc_length = lane->length() - start_arc_length;
      if (current_lane_arc_length + accumulated_arc_length >
          constants::kMaxValidRouteLength) {
        current_lane_arc_length =
            constants::kMaxValidRouteLength - accumulated_arc_length;
      }
      if (current_lane_arc_length < constants::kRouteSampleInterval) {
        continue;
      }

      const math::geometry::PolylineCurve2d ref_line(
          lane->center_line().GetSkeletonInRange(
              start_arc_length, start_arc_length + current_lane_arc_length));
      const std::vector<std::vector<math::geometry::Point2d>> segments =
          ToSegments(ref_line, config_.num_vectors_in_route_feature(),
                     route_sample_interval);
      const auto speed_limits = std::make_unique<SpeedLimits>(*lane);
      const RouteType route_type(*lane);
      std::vector<std::vector<float>> segments_speed_limits =
          GetSpeedLimits(segments, speed_limits.get());
      DCHECK_EQ(segments_speed_limits.size(), segments.size());
      for (size_t j = 0; j < segments.size(); ++j) {
        const std::vector<float>& speed_limits = segments_speed_limits[j];
        if (!speed_limits.empty()) {
          DCHECK_EQ(speed_limits.size(), segments[j].size());
        }
        RouteFeature route_feature;
        for (size_t k = 0; k + 1 < segments[j].size(); ++k) {
          const math::geometry::Point2d& start_point = segments[j][k];
          const math::geometry::Point2d& end_point = segments[j][k + 1];
          const float speed_limit =
              speed_limits.empty() ? 0.0F : speed_limits[k];
          const pb::MapObject::MapObjectType type =
              route_type.GetRouteType(start_point);
          const double arc_length =
              accumulated_arc_length +
              ref_line
                  .GetProximity(start_point,
                                math::pb::UseExtensionFlag::kForbid)
                  .arc_length;
          std::vector<float> features =
              vectornet_feature_processor_.RunRouteFeatureExtractors(
                  /*extractor_input=*/MapFeatureExtractorInput{
                      .start_point = start_point,
                      .end_point = end_point,
                      .speed_limit = speed_limit,
                      .map_object_type = type,
                      .arc_length_in_route = arc_length,
                      .is_in_global_route = is_in_global_route,
                      .is_recommend = is_recommend});
          route_feature.features.push_back(std::move(features));
        }
        route_feature.lane_sequence_ids = {lane->id()};
        route_features.push_back(std::move(route_feature));
      }
      num_routes += static_cast<int>(segments.size());
    }
    accumulated_arc_length += current_section_arc_length;
  }
  const int num_section_route_features =
      static_cast<int>(route_features.size());
  STATS_HISTOGRAM(prediction, vectornet, num_section_route_features,
                  num_section_route_features);
  if (num_section_route_features > max_num_section_routes) {
    route_features = {route_features.begin(),
                      route_features.begin() + max_num_section_routes};
  }
  return route_features;
}

void VectornetFeatureExtractor::ExtractPatchCuboidFeatures(
    const std::vector<const Agent*>& target_agents) {
  TRACE_EVENT_SCOPE(prediction,
                    VectornetFeatureExtractor_ExtractPatchCuboidFeatures);
  if (!enable_patch_cuboid_feature_extraction_) {
    return;
  }
  patch_cuboid_features_.clear();
  for (const auto* agent : target_agents) {
    patch_cuboid_features_[DCHECK_NOTNULL(agent)->object_id()] =
        ExtractPatchCuboidFeaturesForOneAgent(*agent);
  }
}

VectorFeatures VectornetFeatureExtractor::ExtractPatchCuboidFeaturesForOneAgent(
    const Agent& agent) const {
  VectorFeatures cuboid_features;
  const av_comm::HistoryBuffer<int64_t, AgentState>& history_states =
      agent.history_states();

  const int history_feature_len = config_.num_histories_in_cuboid_feature() -
                                  agent.num_future_cuboids_with_point_cloud() -
                                  1;
  // Extract cuboid feature for history state.
  if (!history_states.empty() && history_feature_len > 0) {
    auto iter = history_states.size() > history_feature_len
                    ? history_states.end() - history_feature_len
                    : history_states.begin();
    for (; iter != history_states.end(); iter++) {
      const AgentState& history_state = iter->second;
      std::vector<float> feature =
          vectornet_feature_processor_.RunPatchCuboidFeatureExtractors(
              history_state.cuboid_with_point_cloud());
      cuboid_features.push_back(std::move(feature));
    }
  }

  const int future_frame_start = agent.num_future_cuboids_with_point_cloud() -
                                 config_.num_histories_in_cuboid_feature();
  // Extract cuboid feature for current frame.
  if (future_frame_start < 0) {
    std::vector<float> feature =
        vectornet_feature_processor_.RunPatchCuboidFeatureExtractors(
            agent.current_state().cuboid_with_point_cloud());
    cuboid_features.push_back(std::move(feature));
  }

  // Extract cuboid feature for future frame.
  const std::vector<CuboidWithPointCloud>& future_cuboids_with_points =
      agent.future_cuboids_with_points();
  for (int idx = std::max(future_frame_start, 0);
       idx < agent.num_future_cuboids_with_point_cloud(); ++idx) {
    std::vector<float> feature =
        vectornet_feature_processor_.RunPatchCuboidFeatureExtractors(
            future_cuboids_with_points.at(idx));
    cuboid_features.push_back(std::move(feature));
  }
  return cuboid_features;
}

void VectornetFeatureExtractor::ExtractLidarPatchFeatures(
    const std::vector<const Agent*>& target_agents) {
  TRACE_EVENT_SCOPE(prediction,
                    VectornetFeatureExtractor_ExtractLidarPatchFeatures);
  if (!enable_lidar_patch_feature_extraction_) {
    return;
  }
  // Initial lidar patch feature for each target agent.
  std::unordered_map<ObjectId, LidarPatchFeature> new_lidar_patch_features;
  new_lidar_patch_features.reserve(target_agents.size());
  for (const auto* agent : target_agents) {
    new_lidar_patch_features.emplace(
        std::piecewise_construct,
        std::forward_as_tuple(DCHECK_NOTNULL(agent)->object_id()),
        std::forward_as_tuple());
  }

  // Parallel lidar patch feature extraction.
  auto lidar_extraction_task = [&new_lidar_patch_features, &target_agents,
                                this](int i) {
    const Agent& agent = *DCHECK_NOTNULL(target_agents.at(i));
    const auto object_id = agent.object_id();
    const size_t feature_size = config_.num_histories_in_lidar_patch_feature();
    new_lidar_patch_features.at(object_id).features.reserve(feature_size);
    this->ExtractLidarPatchFeaturesForOneAgent(
        agent, &new_lidar_patch_features[object_id]);
  };

  tbb::parallel_for(0, static_cast<int>(target_agents.size()),
                    lidar_extraction_task);

  lidar_patch_features_.swap(new_lidar_patch_features);
}

void VectornetFeatureExtractor::ExtractLidarPatchFeaturesForOneAgent(
    const Agent& agent, LidarPatchFeature* lidar_feature) const {
  DCHECK(lidar_feature);
  const av_comm::HistoryBuffer<int64_t, AgentState>& history_states =
      agent.history_states();

  // Here number of history feature is exclude number of current frame's and
  // future frames' feature.
  const int num_history_feature =
      config_.num_histories_in_lidar_patch_feature() -
      agent.num_future_cuboids_with_point_cloud() - 1;
  // NOTE(lipei): When this agent lidar patch feature has been extracted in the
  // last frame, we will reuse its history frames' of lidar patch feature based
  // on the current frame since this part of feature is the same as before and
  // avoid unnecessary computation. Otherwise, here will extract history
  // frames' lidar patch feature for this agent.
  auto iter = lidar_patch_features_.find(agent.object_id());
  if (iter != lidar_patch_features_.end()) {
    // Last frame's feature:
    // |    |    |    |    |    |    |    |    |    |
    //                                   T-1   T   T+1
    //      |<------- num_history_feature ---->| history_end
    // Current frame's feature:
    //      |    |    |    |    |    |    |    |    |    |
    //                                        T-1   T   T+1
    const LidarPatchFeature& last_frame_feature = iter->second;
    const int history_feature_end =
        static_cast<int>(last_frame_feature.features.size() -
                         last_frame_feature.num_future_frames);
    for (int idx = std::max(history_feature_end - num_history_feature, 0);
         idx < history_feature_end; ++idx) {
      DCHECK_LT(idx, last_frame_feature.features.size());
      lidar_feature->features.push_back(last_frame_feature.features[idx]);
    }
  } else if (!history_states.empty() && num_history_feature > 0) {
    auto iter = history_states.size() > num_history_feature
                    ? history_states.end() - num_history_feature
                    : history_states.begin();
    for (; iter != history_states.end(); iter++) {
      const AgentState& history_state = iter->second;
      VectorFeatures feature =
          vectornet_feature_processor_.RunLidarPatchFeatureExtractors(
              history_state.cuboid_with_point_cloud(),
              config_.max_num_lidar_points());
      lidar_feature->features.push_back(std::move(feature));
    }
  }

  const int future_frame_start = agent.num_future_cuboids_with_point_cloud() -
                                 config_.num_histories_in_lidar_patch_feature();
  // Extract lidar patch feature for current frame.
  if (future_frame_start < 0) {
    VectorFeatures feature =
        vectornet_feature_processor_.RunLidarPatchFeatureExtractors(
            agent.current_state().cuboid_with_point_cloud(),
            config_.max_num_lidar_points());
    lidar_feature->features.push_back(std::move(feature));
  }

  // Extract lidar patch feature for future frame.
  const std::vector<CuboidWithPointCloud>& future_cuboids_with_points =
      agent.future_cuboids_with_points();
  for (int idx = std::max(future_frame_start, 0);
       idx < agent.num_future_cuboids_with_point_cloud(); ++idx) {
    VectorFeatures feature =
        vectornet_feature_processor_.RunLidarPatchFeatureExtractors(
            future_cuboids_with_points.at(idx), config_.max_num_lidar_points());
    lidar_feature->features.push_back(std::move(feature));
    lidar_feature->num_future_frames++;
  }
}

void VectornetFeatureExtractor::ExtractObstacleFeatures(
    const std::vector<const Agent*>& non_ego_agents) {
  std::vector<ObstacleFeature> new_obstacle_features;
  const int agent_num = static_cast<int>(non_ego_agents.size());
  const auto& obstacle_types = config_.obstacle_types();
  for (int idx = 0; idx < agent_num; ++idx) {
    // Filter agents from non_ego_agents that match obstacle type criteria and
    // process them as obstacles.
    const auto& agent_ptr = non_ego_agents[idx];
    if (std::find(obstacle_types.begin(), obstacle_types.end(),
                  agent_ptr->object_type()) == obstacle_types.end()) {
      continue;
    }
    if (config_.treat_dynamic_unknown_as_agent() &&
        agent_ptr->object_type() == voy::perception::ObjectType::UNKNOWN &&
        vectornet_util::IsMovingUnknownObject(agent_ptr->tracked_object())) {
      continue;
    }
    ExtractOneObstacleFeature(*agent_ptr, &new_obstacle_features);
  }
  obstacles_features_.clear();
  obstacles_features_ = std::move(new_obstacle_features);
}

void VectornetFeatureExtractor::ExtractOneObstacleFeature(
    const Agent& agent, std::vector<ObstacleFeature>* obstacle_features) const {
  std::vector<math::geometry::Point2d> segment_points;
  const auto& tracked_object = agent.tracked_object();
  for (size_t i = 0; i < static_cast<size_t>(tracked_object.contour_size());
       ++i) {
    segment_points.push_back(math::geometry::Point2d(
        tracked_object.contour(i).x(), tracked_object.contour(i).y()));
  }
  const math::geometry::PolylineCurve2d closed_border_line =
      GetClosedBorderLine(math::geometry::PolygonWithCache2d(segment_points));
  if (closed_border_line.GetTotalArcLength() <=
      kMinTrafficConeOrBarrierSampleInterval) {
    return;
  }
  const float sample_interval =
      GetDynamicSampleIntervalForMapObjectLine(closed_border_line);
  std::vector<std::vector<math::geometry::Point2d>> segments =
      ToSegments(closed_border_line, config_.num_vectors_in_obstacle_feature(),
                 sample_interval, /*is_cone_or_barrier*/ true);
  const AgentState& current_agent_state = agent.current_state();
  for (const auto& segment : segments) {
    ObstacleFeature obstacle_feature{
        .object_id = agent.object_id(),
        .current_center = math::geometry::Point2d(tracked_object.center_x(),
                                                  tracked_object.center_y()),
        .importance_rank_to_ego =
            agent.last_state().is_initialized()
                ? agent.last_state()->importance_rank_to_ego()
                : constants::kInvalidImportanceRankToEgo};

    for (size_t i = 0; i + 1 < segment.size(); ++i) {
      const math::geometry::Point2d& start_point = segment[i];
      const math::geometry::Point2d& end_point = segment[i + 1];
      obstacle_feature.features.push_back(
          vectornet_feature_processor_.RunObstacleFeatureExtractors(
              current_agent_state, start_point, end_point));
    }
    obstacle_features->emplace_back(obstacle_feature);
  }
}

void VectornetFeatureExtractor::ExtractTrafficLightFeatures(
    const SceneContext& scene_context,
    const pnc_map::PredictionMapService& prediction_map_service) {
  std::map<TrafficLightKey, TrafficLightFeature> new_traffic_light_features;
  for (const auto& [map_object_key, curr_tl_state] :
       scene_context.map_obj_to_tl_state()) {
    // if the traffic light is not found or may be inaccurate, skip processing
    // it.
    if (config_.extract_accurate_traffic_light_feature() &&
        (curr_tl_state.is_detection_suspicious ||
         curr_tl_state.is_reasoning_result)) {
      continue;
    }
    const int64_t map_object_id = map_object_key.second;
    TrafficLightKey tl_key{.map_object_id = map_object_id};

    const pnc_map::StopLine* stop_line = nullptr;
    const pnc_map::Crosswalk* crosswalk = nullptr;
    std::optional<math::geometry::Polyline2d> watch_line = std::nullopt;
    math::geometry::Point2d locate_point;
    pb::MapObject::MapObjectType map_object_type = pb::MapObject::UNKNOWN_TYPE;

    const pnc_map::TrafficSignal* tl_signal =
        prediction_map_service.GetTrafficSignal(
            curr_tl_state.traffic_signal_id);
    const hdmap::Signal* static_signal =
        prediction_map_service.hdmap()->GetSignalPtrById(
            curr_tl_state.traffic_signal_id);
    if (static_signal == nullptr || tl_signal == nullptr) {
      continue;
    }
    if (map_object_key.first == pb::MapObject::LANE) {
      const pnc_map::Lane* junction_lane =
          prediction_map_service.GetLane(map_object_id);
      if (junction_lane == nullptr || junction_lane->predecessors().empty()) {
        continue;
      }
      tl_key.lane_turn_type = junction_lane->turn();
      if (new_traffic_light_features.find(tl_key) !=
          new_traffic_light_features.end()) {
        continue;
      }
      // Get the first lane that is not in junction.
      const pnc_map::Lane* predecessor_lane =
          junction_lane->predecessors().front();
      while (predecessor_lane->IsInJunction()) {
        predecessor_lane = predecessor_lane->predecessors().front();
      }
      // Match the corresponding stop line.
      auto it = std::find_if(
          tl_signal->stop_lines().begin(), tl_signal->stop_lines().end(),
          [&predecessor_lane](const pnc_map::StopLine* line) {
            return line->lane() && line->lane()->id() == predecessor_lane->id();
          });

      if (it == tl_signal->stop_lines().end()) {
        continue;
      }
      stop_line = *it;
      // Get watch_line based on junction lane.
      if (junction_lane->watch_line().has_value()) {
        watch_line = junction_lane->watch_line().value();
      }
      locate_point = stop_line->middle_point();
      map_object_type = ToMapObjectTypeFeature(junction_lane->type());
    } else if (map_object_key.first == pb::MapObject::CROSSWALK) {
      if (new_traffic_light_features.find(tl_key) !=
          new_traffic_light_features.end()) {
        continue;
      }
      crosswalk = prediction_map_service.GetCrosswalk(map_object_id);
      if (crosswalk == nullptr || crosswalk->center_line() == nullptr) {
        continue;
      }
      locate_point = crosswalk->center_line()->GetStartPoint();
      map_object_type = pb::MapObject::CROSSWALK;
    } else {
      continue;
    }

    TrafficLightFeature& tl_feature = new_traffic_light_features[tl_key];
    tl_feature.is_reasoning_result = curr_tl_state.is_reasoning_result;
    tl_feature.locate_point = locate_point;
    tl_feature.has_watch_line = watch_line.has_value();

    DCHECK_EQ(scene_context.map_obj_to_tl_state_history().size(),
              constants::kTrafficLightHistoryItemSize);
    DCHECK_EQ(config_.num_vectors_in_traffic_light_feature() - 1,
              constants::kTrafficLightHistoryItemSize);

    auto extract_single_frame_feature = [&](const auto& state) {
      return vectornet_feature_processor_.RunTrafficLightFeatureExtractors(
          TrafficLightFeatureExtractorInput{
              .color = state.color,
              .associate_lane_turn = tl_key.lane_turn_type,
              .associate_lane_type = state.associate_lane_type,
              .countdown_number = state.countdown_number,
              .is_flashing = state.is_flashing,
              .is_detection_suspicious = state.is_detection_suspicious,
              .is_reasoning_result = state.is_reasoning_result,
              .stop_line = stop_line,
              .crosswalk = crosswalk,
              .watch_line = watch_line,
              .map_object_type = map_object_type,
              .timestamp = state.timestamp,
              .signal_position = static_signal->position(),
          });
    };

    for (size_t i = 0; i < constants::kTrafficLightHistoryItemSize; ++i) {
      const auto& history_states =
          scene_context.map_obj_to_tl_state_history()[i];
      const auto history_iter = history_states.find(map_object_key);
      if (history_iter == history_states.end()) {
        // If the historical states are not continuous, clear the existing
        // features to ensure feature continuity.
        if (!tl_feature.features.empty()) {
          tl_feature.features.clear();
        }
        continue;
      }
      tl_feature.features.push_back(
          extract_single_frame_feature(history_iter->second));
    }

    tl_feature.features.push_back(extract_single_frame_feature(curr_tl_state));
  }
  traffic_light_features_ = std::move(new_traffic_light_features);
}

FlattenLidarFeatureWithIndex
VectornetFeatureExtractor::GetFlattenLidarFeatureWithIndex(
    const std::vector<ObjectId>& background_agent_ids,
    const std::unordered_map<ObjectId, LidarPatchFeature>&
        agents_lidar_patch_features,
    const LocalCoordinates& local_coordinates) const {
  std::vector<std::vector<float>> lidar_data;
  std::vector<std::vector<int>> lidar_index;
  int start_index = 0;
  for (const ObjectId agent_id : background_agent_ids) {
    auto iter = agents_lidar_patch_features.find(agent_id);
    DCHECK(iter != agents_lidar_patch_features.end());
    const std::vector<VectorFeatures>& lidar_patch_feature =
        iter->second.features;

    // Check the number of liar patch history.
    DCHECK_LE(lidar_patch_feature.size(),
              config_.num_histories_in_lidar_patch_feature());
    // Padding zeros for lidar patch with short history.
    const int pad_zero_size_for_lidar_mask =
        config_.num_histories_in_lidar_patch_feature() -
        lidar_patch_feature.size();
    lidar_index.push_back(
        std::vector<int>(pad_zero_size_for_lidar_mask + 1, start_index));
    for (const auto& one_frame_feature : lidar_patch_feature) {
      if (one_frame_feature.empty()) {
        lidar_index.back().push_back(lidar_index.back().back());
        continue;
      }
      int num_points = 0;
      for (const auto& feature : one_frame_feature) {
        lidar_data.push_back(feature);
        vectornet_feature_processor_.RunLidarPatchFeatureNormalizers(
            /*normalizer_input=*/{.feature_size =
                                      static_cast<int>(feature.size()),
                                  .coordinates = local_coordinates},
            /*feature_list_ptr=*/lidar_data.back().data());
        num_points++;
      }
      lidar_index.back().push_back(lidar_index.back().back() + num_points);
    }
    start_index = lidar_index.back().back();
  }
  while (static_cast<int>(lidar_index.size()) <
         config_.max_num_agents_with_lidar_patch()) {
    lidar_index.push_back(std::vector<int>(
        config_.num_histories_in_lidar_patch_feature() + 1, start_index));
  }
  DCHECK_EQ(lidar_data.size(), lidar_index.back().back());
  DCHECK_EQ(lidar_index.size(), config_.max_num_agents_with_lidar_patch());
  DCHECK_EQ(lidar_index[0].size(),
            config_.num_histories_in_lidar_patch_feature() + 1);
  return FlattenLidarFeatureWithIndex{lidar_data, lidar_index};
}

std::vector<neural_net::Tensor> VectornetFeatureExtractor::GetLidarPatchTensors(
    const std::unordered_map<ObjectId, std::vector<CuboidWithPointCloud>>&
        agents_cuboids_with_points,
    ObjectId target_agent_id,
    const std::vector<ObjectId>& selected_background_agents_ids,
    const LocalCoordinates& local_coordinates) const {
  std::vector<neural_net::Tensor> tensors;
  tensors.reserve(kNumInputTensorsForFeatureWithMask);
  const int lidar_patch_feature_size =
      vectornet_feature_processor_.lidar_patch_features_dimension();

  std::unordered_map<ObjectId, LidarPatchFeature> agents_lidar_patch_features;
  agents_lidar_patch_features.reserve(selected_background_agents_ids.size());
  for (const ObjectId agent_id : selected_background_agents_ids) {
    const auto& iter = agents_cuboids_with_points.find(agent_id);
    DCHECK(iter != agents_cuboids_with_points.end());
    const std::vector<CuboidWithPointCloud>& agent_cuboids_with_points =
        iter->second;

    const int num_history_lidar = agent_cuboids_with_points.size();
    const int num_history_feature = std::min(
        num_history_lidar, config_.num_histories_in_lidar_patch_feature());

    std::vector<VectorFeatures> lidar_patch_feature;
    lidar_patch_feature.reserve(num_history_feature);
    // TODO(renhao): There will be redundant calculations of features for the
    // same agent (as background agents for different target agents), and we
    // need to optimize it in future.
    for (int idx = num_history_lidar - num_history_feature;
         idx < num_history_lidar; ++idx) {
      DCHECK_GE(idx, 0);
      VectorFeatures feature =
          vectornet_feature_processor_.RunLidarPatchFeatureExtractors(
              agent_cuboids_with_points.at(idx),
              config_.max_num_lidar_points());
      lidar_patch_feature.push_back(std::move(feature));
    }
    const auto result = agents_lidar_patch_features.emplace(
        agent_id,
        LidarPatchFeature{.features = std::move(lidar_patch_feature)});
    DCHECK(result.second);
  }

  const FlattenLidarFeatureWithIndex lidar_patch_feature_with_index =
      GetFlattenLidarFeatureWithIndex(selected_background_agents_ids,
                                      agents_lidar_patch_features,
                                      local_coordinates);
  // Extract cuboid features and fill to tensor.
  DCHECK_GE(selected_background_agents_ids.size(), 1);
  DCHECK_EQ(selected_background_agents_ids[0], target_agent_id);
  const int num_lidar_points =
      std::max(1, lidar_patch_feature_with_index.index.back().back());

  // Initialize lidar patch feature tensor.
  tensors.emplace_back(
      neural_net::TensorType::kFloat,
      std::vector<int64_t>{num_lidar_points, lidar_patch_feature_size});
  neural_net::Tensor& lidar_patch_tensor = tensors[0];
  std::memset(lidar_patch_tensor.data(), 0, lidar_patch_tensor.num_bytes());

  // Initialize lidar patch mask tensor.
  tensors.emplace_back(
      neural_net::TensorType::kInt32,
      std::vector<int64_t>{config_.max_num_agents_with_lidar_patch(),
                           config_.num_histories_in_lidar_patch_feature() + 1});
  neural_net::Tensor& lidar_patch_mask_tensor = tensors[1];
  std::memset(lidar_patch_mask_tensor.data(), 0,
              lidar_patch_mask_tensor.num_bytes());

  // Fill lidar patch feature to tensor.
  const auto [lidar_data_begin, lidar_data_end] =
      vectornet_util::GetTensorBeginAndEnd<float>(&tensors[0]);
  const auto [lidar_index_begin, lidar_index_end] =
      vectornet_util::GetTensorBeginAndEnd<int32_t>(&tensors[1]);
  float* lidar_data_ptr = lidar_data_begin;
  int32_t* lidar_index_ptr = lidar_index_begin;
  if (!lidar_patch_feature_with_index.features.empty()) {
    for (const std::vector<float>& feature :
         lidar_patch_feature_with_index.features) {
      memcpy(lidar_data_ptr, feature.data(), feature.size() * sizeof(float));
      lidar_data_ptr += feature.size();
    }
    for (const std::vector<int32_t>& index :
         lidar_patch_feature_with_index.index) {
      memcpy(lidar_index_ptr, index.data(), index.size() * sizeof(int32_t));
      lidar_index_ptr += index.size();
    }
    DCHECK_EQ(lidar_data_ptr, lidar_data_end);
    DCHECK_EQ(lidar_index_ptr, lidar_index_end);
  }
  return tensors;
}

std::vector<neural_net::Tensor>
VectornetFeatureExtractor::GetPatchCuboidTensors(
    const std::unordered_map<ObjectId, std::vector<CuboidWithPointCloud>>&
        agents_cuboids_with_points,
    ObjectId target_agent_id,
    const std::vector<ObjectId>& selected_background_agents_ids,
    const LocalCoordinates& local_coordinates) const {
  std::vector<neural_net::Tensor> tensors;
  // Involved two tensor, patch cuboid feature tensor and mask tensor.
  tensors.reserve(kNumInputTensorsForFeatureWithMask);
  const int cuboid_feature_size =
      vectornet_feature_processor_.patch_cuboid_features_dimension();
  const int max_num_histories = config_.num_histories_in_cuboid_feature();
  const int max_num_agents_with_lidar_patch =
      config_.max_num_agents_with_lidar_patch();

  // Initialize patch cuboid feature tensor.
  tensors.emplace_back(
      neural_net::TensorType::kFloat,
      std::vector<int64_t>{max_num_agents_with_lidar_patch, max_num_histories,
                           cuboid_feature_size});
  neural_net::Tensor& patch_cuboid_tensor = tensors[0];
  std::memset(patch_cuboid_tensor.data(), 0, patch_cuboid_tensor.num_bytes());

  // Initialize patch cuboid mask tensor.
  tensors.emplace_back(
      neural_net::TensorType::kBool,
      std::vector<int64_t>{max_num_agents_with_lidar_patch, max_num_histories});
  neural_net::Tensor& patch_cuboid_mask_tensor = tensors[1];
  std::memset(patch_cuboid_mask_tensor.data(), 0,
              patch_cuboid_mask_tensor.num_bytes());

  // Fill patch cuboid feature to tensor.
  const auto [cuboid_data_begin, cuboid_data_end] =
      vectornet_util::GetTensorBeginAndEnd<float>(&tensors[0]);
  const auto [cuboid_mask_begin, cuboid_mask_end] =
      vectornet_util::GetTensorBeginAndEnd<bool>(&tensors[1]);
  float* cuboid_data_ptr = cuboid_data_begin;
  bool* cuboid_mask_ptr = cuboid_mask_begin;

  const int num_selected_agents = selected_background_agents_ids.size();
  DCHECK_GE(num_selected_agents, 1);
  DCHECK_EQ(selected_background_agents_ids[0], target_agent_id);
  DCHECK_LE(num_selected_agents, config_.max_num_agents_with_lidar_patch());

  for (const ObjectId agent_id : selected_background_agents_ids) {
    const auto& iter = agents_cuboids_with_points.find(agent_id);
    DCHECK(iter != agents_cuboids_with_points.end());
    const std::vector<CuboidWithPointCloud>& agent_cuboids_with_points =
        iter->second;

    const int num_history_cuboid = agent_cuboids_with_points.size();
    const int num_history_feature =
        std::min(static_cast<int>(num_history_cuboid), max_num_histories);

    const int pad_zero_size_for_cuboid_mask =
        max_num_histories - num_history_feature;
    cuboid_data_ptr += pad_zero_size_for_cuboid_mask * cuboid_feature_size;
    cuboid_mask_ptr += pad_zero_size_for_cuboid_mask;

    for (int idx = std::max(num_history_cuboid - num_history_feature, 0);
         idx < num_history_cuboid; ++idx) {
      DCHECK_GE(idx, 0);
      // Extractor for the cuboid feature at a certain timestamp.
      std::vector<float> feature =
          vectornet_feature_processor_.RunPatchCuboidFeatureExtractors(
              agent_cuboids_with_points.at(idx));

      // Fill the cuboid feature at the ceratin timestamp to tensor.
      DCHECK(!feature.empty());
      memcpy(cuboid_data_ptr, feature.data(), feature.size() * sizeof(float));
      vectornet_feature_processor_.RunPatchCuboidFeatureNormalizers(
          /*normalizer_input=*/{.feature_size =
                                    static_cast<int>(feature.size()),
                                .coordinates = local_coordinates},
          /*feature_list_ptr=*/cuboid_data_ptr);
      cuboid_data_ptr += feature.size();
      *cuboid_mask_ptr = true;
      cuboid_mask_ptr++;
    }
  }
  cuboid_data_ptr +=
      (config_.max_num_agents_with_lidar_patch() - num_selected_agents) *
      max_num_histories * cuboid_feature_size;
  cuboid_mask_ptr +=
      (config_.max_num_agents_with_lidar_patch() - num_selected_agents) *
      max_num_histories;

  DCHECK_EQ(cuboid_data_ptr, cuboid_data_end);
  DCHECK_EQ(cuboid_mask_ptr, cuboid_mask_end);
  return tensors;
}

void VectornetFeatureExtractor::ExtractLidarContextFeatures(
    const std::vector<const Agent*>& target_agents,
    const SceneContext& scene_context) {
  if (!enable_lidar_context_feature_extraction_) {
    return;
  }
  {
    TRACE_EVENT_SCOPE(prediction,
                      VectornetFeatureExtractor_ExtractLidarContextFeatures);
    // Clear features map.
    lidar_context_features_.clear();
    agent_nearby_obstacle_clusters_.clear();

    std::shared_ptr<ObstacleSegmentationResult> obstacle_segmentation_result =
        scene_context.obstacle_segmentation_result();
    if (obstacle_segmentation_result == nullptr ||
        obstacle_segmentation_result->clusters.empty()) {
      return;
    }

    // Get large clusters' obstacles' sorted grid indexes
    const std::unordered_map<int, std::vector<int>>&
        large_cluster_sorted_grid_idx = GetLargeClusterSortedGridIndexes(
            *obstacle_segmentation_result,
            static_cast<size_t>(config_.max_num_obstacles()));

    // Compute target agent nearby clusters
    for (const auto* agent : target_agents) {
      agent_nearby_obstacle_clusters_.emplace(
          std::piecewise_construct,
          std::forward_as_tuple(DCHECK_NOTNULL(agent)->object_id()),
          std::forward_as_tuple());
    }
    auto agent_nearby_clusters_extraction_task =
        [&target_agents, obstacle_segmentation_result,
         &large_cluster_sorted_grid_idx, this](int i) {
          const Agent& agent = *DCHECK_NOTNULL(target_agents.at(i));
          agent_nearby_obstacle_clusters_[agent.object_id()] =
              this->ExtractNearbyClustersForOneAgent(
                  agent, *obstacle_segmentation_result,
                  large_cluster_sorted_grid_idx);
        };
    tbb::parallel_for(0, static_cast<int>(target_agents.size()),
                      agent_nearby_clusters_extraction_task);

    // Compute cluster feature
    for (const auto* agent : target_agents) {
      const std::vector<ObstacleClusterKey>& cluster_keys =
          agent_nearby_obstacle_clusters_.at(
              DCHECK_NOTNULL(agent)->object_id());
      for (const auto& cluster_key : cluster_keys) {
        lidar_context_features_.emplace(std::piecewise_construct,
                                        std::forward_as_tuple(cluster_key),
                                        std::forward_as_tuple());
      }
    }
    const int num_clusters = lidar_context_features_.size();
    auto cluster_feature_extraction_task = [obstacle_segmentation_result,
                                            &large_cluster_sorted_grid_idx,
                                            this](int i) {
      auto iter = lidar_context_features_.begin();
      std::advance(iter, i);
      iter->second = this->ExtractLidarContextFeaturesForOneCluster(
          iter->first, *obstacle_segmentation_result,
          large_cluster_sorted_grid_idx);
    };
    tbb::parallel_for(0, num_clusters, cluster_feature_extraction_task);
  }
}

std::vector<ObstacleClusterKey>
VectornetFeatureExtractor::ExtractNearbyClustersForOneAgent(
    const Agent& agent,
    const ObstacleSegmentationResult& obstacle_segmentation_result,
    const std::unordered_map<int, std::vector<int>>&
        large_cluster_sorted_grid_idx) {
  const perception::ObstacleMap& obstacle_map =
      *DCHECK_NOTNULL(obstacle_segmentation_result.obstacle_map);

  // Get agent filtered obstacles mask.
  std::vector<bool> filtered_obstacles(
      obstacle_segmentation_result.filtered_object_mask);
  const std::vector<int>& obstacle_ids_inside_box =
      obstacle_map.GetObstacleIdsWithin(ExpandBox(agent.tracked_object()));
  for (int obstacle_id : obstacle_ids_inside_box) {
    filtered_obstacles[obstacle_id] = true;
  }

  // Get agent nearby obstacles.
  DCHECK_GT(config_.obstacle_search_range(), 0.0);
  const int obstacle_seach_range_in_grid_size = static_cast<int>(
      config_.obstacle_search_range() / perception::Obstacle::kObstacleSize);
  const std::vector<int>& object_nearby_obstacle_ids =
      GetNearbyObstacleIdsWithFilters(agent.tracked_object(), obstacle_map,
                                      obstacle_seach_range_in_grid_size,
                                      filtered_obstacles);
  // Early return if has no obstacles.
  if (object_nearby_obstacle_ids.empty()) {
    return {};
  }

  std::map<ObstacleClusterKey, double> object_candidate_clusters;
  const math::geometry::Point2d agent_position{
      agent.tracked_object().center_x(), agent.tracked_object().center_y()};
  for (int obstacle_id : object_nearby_obstacle_ids) {
    const int cluster_id =
        obstacle_segmentation_result.obstacle_id_to_cluster_id[obstacle_id];
    // Skip if it has no cluster.
    if (cluster_id == -1) {
      continue;
    }

    // Get |ObstacleClusterKey|.
    const perception::Obstacle& obstacle =
        *DCHECK_NOTNULL(obstacle_map.GetObstacle(obstacle_id));
    const ObstacleClusterKey& obstacle_cluster_key = GetObstacleClusterKey(
        obstacle, cluster_id, obstacle_segmentation_result,
        large_cluster_sorted_grid_idx, config_.max_num_obstacles());

    // Update comparable distance from agent to cluster.
    const double distance_sq =
        static_cast<double>(boost::geometry::comparable_distance(
            math::geometry::Point2d{obstacle.x(), obstacle.y()},
            agent_position));
    const auto find_it = object_candidate_clusters.find(obstacle_cluster_key);
    if (find_it == object_candidate_clusters.end() ||
        distance_sq < find_it->second) {
      object_candidate_clusters[obstacle_cluster_key] = distance_sq;
    }
  }

  return SelectNearbyLidarContextObjects(
      object_candidate_clusters, config_.max_num_lidar_context_objects());
}

VectorFeatures
VectornetFeatureExtractor::ExtractLidarContextFeaturesForOneCluster(
    const ObstacleClusterKey& obstacle_cluster_key,
    const ObstacleSegmentationResult& obstacle_segmentation_result,
    const std::unordered_map<int, std::vector<int>>&
        large_cluster_sorted_grid_idx) {
  const int cluster_id = obstacle_cluster_key.cluster_id;
  DCHECK_GE(cluster_id, 0);

  DCHECK_GT(obstacle_segmentation_result.clusters.size(), 0);
  const std::vector<const perception::Obstacle*>& cluster =
      obstacle_segmentation_result.clusters.at(cluster_id);
  DCHECK_GT(cluster.size(), 0);

  // Directly calculate cluster feature if cluster's size is no more than
  // maximum configured size and no need to do cluster partition.
  if (static_cast<int>(cluster.size()) <= config_.max_num_obstacles()) {
    return vectornet_feature_processor_.RunLidarContextFeatureExtractors(
        cluster);
  }

  // Large cluster would be partition into a few smaller clusters(no more than
  // maximum configured cluster size), so here would compute obstacle cluster
  // feature based on cluster key(involved cluster partition information)).
  std::vector<const perception::Obstacle*> cluster_partition;
  cluster_partition.reserve(config_.max_num_obstacles());

  const std::vector<int>& cluster_sorted_grid_idx =
      large_cluster_sorted_grid_idx.at(cluster_id);
  const perception::ObstacleMap& obstacle_map =
      *DCHECK_NOTNULL(obstacle_segmentation_result.obstacle_map);

  const int segment_idx = obstacle_cluster_key.segment_idx;
  const int start_idx = segment_idx * config_.max_num_obstacles();
  const int end_idx = std::min((segment_idx + 1) * config_.max_num_obstacles(),
                               static_cast<int>(cluster.size()));
  for (int idx = start_idx; idx < end_idx; idx++) {
    const int obstacle_id =
        obstacle_map.GetObstacleId(cluster_sorted_grid_idx.at(idx));
    cluster_partition.push_back(obstacle_map.GetObstacle(obstacle_id).get());
  }
  return vectornet_feature_processor_.RunLidarContextFeatureExtractors(
      cluster_partition);
}

// Create neural_net Tensors and initialize them.
std::vector<neural_net::Tensor>
VectornetFeatureExtractor::InitializeInputTensors(int batch_size) const {
  TRACE_EVENT_SCOPE(prediction, VectornetPredictor_InitializeInputTensors);

  const int agent_feature_size =
      vectornet_feature_processor_.agent_features_dimension();
  const int map_object_feature_size =
      vectornet_feature_processor_.map_object_features_dimension();

  std::vector<neural_net::Tensor> tensors;
  tensors.reserve(num_input_tensors_);
  int input_tensors_index = 0;

  // Initialize agent tensor.
  tensors.emplace_back(
      neural_net::TensorType::kFloat,
      std::vector<int64_t>{batch_size, config_.max_num_agents(),
                           config_.num_vectors_in_agent_feature(),
                           agent_feature_size});
  neural_net::Tensor& agent_tensor = tensors[input_tensors_index++];
  std::memset(agent_tensor.data(), 0, agent_tensor.num_bytes());

  // Initialize agent mask tensor.
  tensors.emplace_back(
      neural_net::TensorType::kBool,
      std::vector<int64_t>{batch_size, config_.max_num_agents(),
                           config_.num_vectors_in_agent_feature()});
  neural_net::Tensor& agent_mask_tensor = tensors[input_tensors_index++];
  std::memset(agent_mask_tensor.data(), 0, agent_mask_tensor.num_bytes());

  // Initialize map object tensor.
  tensors.emplace_back(
      neural_net::TensorType::kFloat,
      std::vector<int64_t>{batch_size, config_.max_num_map_objects_segment(),
                           config_.num_vectors_in_map_object_feature(),
                           map_object_feature_size});
  neural_net::Tensor& map_object_tensor = tensors[input_tensors_index++];
  std::memset(map_object_tensor.data(), 0, map_object_tensor.num_bytes());

  // Initialize map object mask tensor.
  tensors.emplace_back(
      neural_net::TensorType::kBool,
      std::vector<int64_t>{batch_size, config_.max_num_map_objects_segment(),
                           config_.num_vectors_in_map_object_feature()});
  neural_net::Tensor& map_object_mask_tensor = tensors[input_tensors_index++];
  std::memset(map_object_mask_tensor.data(), 0,
              map_object_mask_tensor.num_bytes());

  // Initialize skeleton feature tensor and mask tensor.
  if (enable_skeleton_feature_extraction_) {
    const int skeleton_feature_size =
        vectornet_feature_processor_.skeleton_features_dimension();
    // Initialize skeleton tensor.
    tensors.emplace_back(
        neural_net::TensorType::kFloat,
        std::vector<int64_t>{batch_size, config_.num_histories_in_skeleton(),
                             constants::kNumPedSkeletonPoints,
                             skeleton_feature_size});
    neural_net::Tensor& skeleton_tensor = tensors[input_tensors_index++];
    std::memset(skeleton_tensor.data(), 0, skeleton_tensor.num_bytes());

    // Initialize skeleton mask tensor.
    tensors.emplace_back(neural_net::TensorType::kBool,
                         std::vector<int64_t>{
                             batch_size,
                             config_.num_histories_in_skeleton(),
                             constants::kNumPedSkeletonPoints,
                         });
    neural_net::Tensor& skeleton_mask_tensor = tensors[input_tensors_index++];
    std::memset(skeleton_mask_tensor.data(), 0,
                skeleton_mask_tensor.num_bytes());
  }

  // Initialize route feature tensor and mask tensor.
  if (enable_route_feature_extraction_) {
    const int route_feature_size =
        vectornet_feature_processor_.route_features_dimension();
    const int num_routes = config_.max_num_routes();
    // Initialize route feature tensor.
    tensors.emplace_back(
        neural_net::TensorType::kFloat,
        std::vector<int64_t>{batch_size, num_routes,
                             config_.num_vectors_in_route_feature(),
                             route_feature_size});
    neural_net::Tensor& route_tensor = tensors[input_tensors_index++];
    std::memset(route_tensor.data(), 0, route_tensor.num_bytes());
    // Initialize route mask tensor.
    tensors.emplace_back(
        neural_net::TensorType::kBool,
        std::vector<int64_t>{batch_size, num_routes,
                             config_.num_vectors_in_route_feature()});
    neural_net::Tensor& route_mask_tensor = tensors[input_tensors_index++];
    std::memset(route_mask_tensor.data(), 0, route_mask_tensor.num_bytes());
  }

  // Initialize lidar patch feature tensor and mask tensor.
  if (enable_lidar_patch_feature_extraction_) {
    const int lidar_patch_feature_size =
        vectornet_feature_processor_.lidar_patch_features_dimension();

    std::vector<int64_t> lidar_data_tensor_shape;
    std::vector<int64_t> lidar_mask_tensor_shape;
    // TODO(renhao): Deprecate the if branch when the onboard model with
    // multi-agent lidar patches is deployed.
    if (config_.max_num_agents_with_lidar_patch() == 1 &&
        config_.squeeze_num_agents_with_lidar_patch()) {
      lidar_data_tensor_shape = {
          batch_size, config_.num_histories_in_lidar_patch_feature(),
          config_.max_num_lidar_points(), lidar_patch_feature_size};
      lidar_mask_tensor_shape = {batch_size,
                                 config_.num_histories_in_lidar_patch_feature(),
                                 config_.max_num_lidar_points()};
    } else {
      lidar_data_tensor_shape = {
          batch_size, config_.max_num_agents_with_lidar_patch(),
          config_.num_histories_in_lidar_patch_feature(),
          config_.max_num_lidar_points(), lidar_patch_feature_size};
      lidar_mask_tensor_shape = {batch_size,
                                 config_.max_num_agents_with_lidar_patch(),
                                 config_.num_histories_in_lidar_patch_feature(),
                                 config_.max_num_lidar_points()};
    }
    // Initialize lidar patch feature tensor.
    tensors.emplace_back(neural_net::TensorType::kFloat,
                         lidar_data_tensor_shape);
    neural_net::Tensor& lidar_patch_tensor = tensors[input_tensors_index++];
    std::memset(lidar_patch_tensor.data(), 0, lidar_patch_tensor.num_bytes());

    // Initialize lidar patch mask tensor.
    tensors.emplace_back(neural_net::TensorType::kBool,
                         lidar_mask_tensor_shape);

    neural_net::Tensor& lidar_patch_mask_tensor =
        tensors[input_tensors_index++];
    std::memset(lidar_patch_mask_tensor.data(), 0,
                lidar_patch_mask_tensor.num_bytes());
  }

  // Initialize patch cuboid feature tensor and mask tensor.
  if (enable_patch_cuboid_feature_extraction_) {
    const int patch_cuboid_feature_size =
        vectornet_feature_processor_.patch_cuboid_features_dimension();

    std::vector<int64_t> cuboid_data_tensor_shape;
    std::vector<int64_t> cuboid_mask_tensor_shape;
    // TODO(renhao): Deprecate the if branch when the onboard model with
    // multi-agent lidar patches is deployed.
    if (config_.max_num_agents_with_lidar_patch() == 1 &&
        config_.squeeze_num_agents_with_lidar_patch()) {
      cuboid_data_tensor_shape = {
          batch_size, config_.num_histories_in_lidar_patch_feature(),
          patch_cuboid_feature_size};
      cuboid_mask_tensor_shape = {
          batch_size, config_.num_histories_in_lidar_patch_feature()};
    } else {
      cuboid_data_tensor_shape = {
          batch_size, config_.max_num_agents_with_lidar_patch(),
          config_.num_histories_in_cuboid_feature(), patch_cuboid_feature_size};
      cuboid_mask_tensor_shape = {batch_size,
                                  config_.max_num_agents_with_lidar_patch(),
                                  config_.num_histories_in_cuboid_feature()};
    }
    // Initialize patch cuboid feature tensor.
    tensors.emplace_back(neural_net::TensorType::kFloat,
                         cuboid_data_tensor_shape);
    neural_net::Tensor& cuboid_tensor = tensors[input_tensors_index++];
    std::memset(cuboid_tensor.data(), 0, cuboid_tensor.num_bytes());

    // Initialize patch cuboid mask tensor.
    tensors.emplace_back(neural_net::TensorType::kBool,
                         cuboid_mask_tensor_shape);

    neural_net::Tensor& cuboid_mask_tensor = tensors[input_tensors_index++];
    std::memset(cuboid_mask_tensor.data(), 0, cuboid_mask_tensor.num_bytes());
  }

  if (enable_lidar_context_feature_extraction_) {
    const int lidar_context_feature_size =
        vectornet_feature_processor_.lidar_context_features_dimension();

    // Initialize lidar context feature tensor.
    tensors.emplace_back(
        neural_net::TensorType::kFloat,
        std::vector<int64_t>{
            batch_size, config_.max_num_lidar_context_objects(),
            config_.max_num_obstacles(), lidar_context_feature_size});
    neural_net::Tensor& lidar_context_tensor = tensors[input_tensors_index++];
    std::memset(lidar_context_tensor.data(), 0,
                lidar_context_tensor.num_bytes());

    // Initialize lidar context mask tensor.
    tensors.emplace_back(
        neural_net::TensorType::kBool,
        std::vector<int64_t>{batch_size,
                             config_.max_num_lidar_context_objects(),
                             config_.max_num_obstacles()});
    neural_net::Tensor& lidar_context_mask_tensor =
        tensors[input_tensors_index++];
    std::memset(lidar_context_mask_tensor.data(), 0,
                lidar_context_mask_tensor.num_bytes());
  }

  // Initialize obstacle feature tensor and mask tensor.
  if (enable_obstacle_feature_extraction_) {
    const int obstacle_feature_size =
        vectornet_feature_processor_.obstacle_features_dimension();
    const int num_obstacles = config_.max_num_obstacles_segment();
    // Initialize obstacle feature tensor.
    tensors.emplace_back(
        neural_net::TensorType::kFloat,
        std::vector<int64_t>{batch_size, num_obstacles,
                             config_.num_vectors_in_obstacle_feature(),
                             obstacle_feature_size});
    neural_net::Tensor& obstacle_tensor = tensors[input_tensors_index++];
    std::memset(obstacle_tensor.data(), 0, obstacle_tensor.num_bytes());
    // Initialize obstacle mask tensor.
    tensors.emplace_back(
        neural_net::TensorType::kBool,
        std::vector<int64_t>{batch_size, num_obstacles,
                             config_.num_vectors_in_obstacle_feature()});
    neural_net::Tensor& obstacle_mask_tensor = tensors[input_tensors_index++];
    std::memset(obstacle_mask_tensor.data(), 0,
                obstacle_mask_tensor.num_bytes());
  }

  // Initialize traffic light feature tensor and mask tensor.
  if (enable_traffic_light_feature_extraction_) {
    const int obstacle_feature_size =
        vectornet_feature_processor_.traffic_light_features_dimension();
    const int num_traffic_lights = config_.max_num_traffic_lights();
    // Initialize traffic light feature tensor.
    tensors.emplace_back(
        neural_net::TensorType::kFloat,
        std::vector<int64_t>{batch_size, num_traffic_lights,
                             config_.num_vectors_in_traffic_light_feature(),
                             obstacle_feature_size});
    neural_net::Tensor& traffic_light_tensor = tensors[input_tensors_index++];
    std::memset(traffic_light_tensor.data(), 0,
                traffic_light_tensor.num_bytes());
    // Initialize traffic light mask tensor.
    tensors.emplace_back(
        neural_net::TensorType::kBool,
        std::vector<int64_t>{batch_size, num_traffic_lights,
                             config_.num_vectors_in_traffic_light_feature()});
    neural_net::Tensor& traffic_light_mask_tensor =
        tensors[input_tensors_index++];
    std::memset(traffic_light_mask_tensor.data(), 0,
                traffic_light_mask_tensor.num_bytes());
  }
  return tensors;
}

AgentFeature VectornetFeatureExtractor::ExtractOneAgentFeature(
    const Agent& agent) const {
  const auto& tracked_object = agent.tracked_object();
  AgentFeature agent_feature{
      .object_id = agent.object_id(),
      .object_type = agent.object_type(),
      .agent_tag = GetAgentTag(tracked_object),
      .current_center = math::geometry::Point2d(tracked_object.center_x(),
                                                tracked_object.center_y()),
      .importance_rank_to_ego =
          agent.last_state().is_initialized()
              ? agent.last_state()->importance_rank_to_ego()
              : constants::kInvalidImportanceRankToEgo,
      .current_box_heading = tracked_object.box_heading(),
      .is_stationary_object = utility::IsStationaryObject(tracked_object)};

  // Extract counter-clockwise corner points of the box.
  const auto agent_box = math::geometry::OrientedBox2d(
      tracked_object.center_x(), tracked_object.center_y(),
      tracked_object.length(), tracked_object.width(),
      tracked_object.box_heading());
  for (const auto corner_type : kAgentBoxCornerPointsType) {
    agent_feature.sampled_points.push_back(agent_box.CornerPoint(corner_type));
  }

  const av_comm::HistoryBuffer<int64_t, AgentState>& agent_history_states =
      agent.history_states();
  if (agent_history_states.empty()) {
    const AgentState& current_agent_state = agent.current_state();
    agent_feature.features.push_back(
        vectornet_feature_processor_.RunAgentFeatureExtractors(
            /*prev_state=*/nullptr, current_agent_state));
    return agent_feature;
  }

  // Ensure there are at most config_.num_vectors_in_agent_feature() features.
  auto prev_iter =
      agent_history_states.size() > config_.num_vectors_in_agent_feature()
          ? agent_history_states.end() - config_.num_vectors_in_agent_feature()
          : agent_history_states.begin();
  for (auto iter = prev_iter + 1; iter != agent_history_states.end();
       ++iter, ++prev_iter) {
    const AgentState& current_agent_state = iter->second;
    const AgentState* previous_agent_state = &(prev_iter->second);
    std::vector<float> features =
        vectornet_feature_processor_.RunAgentFeatureExtractors(
            previous_agent_state, current_agent_state);
    agent_feature.features.push_back(std::move(features));
  }
  const AgentState& current_agent_state = agent.current_state();
  const AgentState* previous_agent_state =
      &(agent_history_states.latest_data());
  std::vector<float> features =
      vectornet_feature_processor_.RunAgentFeatureExtractors(
          previous_agent_state, current_agent_state);
  agent_feature.features.push_back(std::move(features));
  return agent_feature;
}

std::vector<neural_net::Tensor> VectornetFeatureExtractor::GetInputTensors(
    const SceneContext& scene_context,
    const std::vector<const Agent*>& target_agents,
    const std::vector<RouteFeature>& route_features,
    const std::optional<std::map<int64_t, LocalCoordinates>>&
        nullable_local_coordinates,
    std::map<int64_t, std::vector<AgentKey>>* background_agent_keys,
    std::map<int64_t, std::vector<MapObjectKey>>* background_map_object_keys,
    std::unordered_map<ObjectId, std::vector<ObjectId>>*
        lidar_patch_background_agent_ids,
    std::vector<const Agent*>* batch_target_agents,
    std::vector<bool>* batch_is_ignoring_ego_mask) const {
  TRACE_EVENT_SCOPE(prediction, VectornetFeatureExtractor_GetInputTensors);

  int batch_size = target_agents.size();
  std::vector<int> target_agent_idx_to_ignore_ego;

  // NOTE(renhao): Ensures that background agent keys of agent_feature is not
  // empty when lidar patch feature is enabled, because the selection for
  // lidar patch background agents is depended on the selection results of
  // background agents of agent feature.
  std::map<int64_t, std::vector<AgentKey>> background_agent_keys_temp;
  if (background_agent_keys == nullptr &&
      enable_lidar_patch_feature_extraction_) {
    background_agent_keys = &background_agent_keys_temp;
  }

  // If batch_target_agents and batch_is_ignoring_ego_mask are provided, then
  // construct the relationship between batch size index, agent, and
  // whether to ignore ego prediction.
  if (batch_target_agents && batch_is_ignoring_ego_mask) {
    target_agent_idx_to_ignore_ego.reserve(batch_size);
    for (int agent_index = 0; agent_index < batch_size; ++agent_index) {
      if (target_agents[agent_index]->should_do_ignore_ego_predictions()) {
        target_agent_idx_to_ignore_ego.push_back(agent_index);
      }
    }
    const int num_ignore_ego_predictions =
        target_agent_idx_to_ignore_ego.size();
    DCHECK_LE(num_ignore_ego_predictions,
              constants::kMaxNumOfAgentsForMarginalIgnoreEgoPrediction);
    batch_size = target_agents.size() + num_ignore_ego_predictions;

    batch_target_agents->reserve(batch_size);
    batch_target_agents->insert(batch_target_agents->end(),
                                target_agents.begin(), target_agents.end());
    batch_is_ignoring_ego_mask->reserve(batch_size);
    batch_is_ignoring_ego_mask->assign(target_agents.size(), false);
    DCHECK_EQ(batch_target_agents->size(), target_agents.size());
    DCHECK_EQ(batch_is_ignoring_ego_mask->size(), target_agents.size());
  }
  // We will get input tensors by five steps. In the first step, we get the
  // initialized tensors by InitializeInputTensors. In the second~six step, we
  // populate the normal features and masks. In the five step, we copy and
  // mask the original input tensor for ignoring ego prediction. Step 1:
  // Initializes input tensors.
  int feature_inputs_start_index = 0;
  std::vector<neural_net::Tensor> inputs =
      InitializeInputTensors(/*batch_size=*/batch_size);

  DCHECK_EQ(num_input_tensors_, inputs.size())
      << "Inputs tensor must contain and only contain agent, agent_mask, "
         "map_object, map_object_mask tensors and other skeleton/route "
         "features"
         " and mask(if the other features are enabled.).";

  // Step 2: Populates the normal features and masks.
  const int num_populated_agent_map_tensors =
      PopulateNormalFeatureAndMaskTensors(
          scene_context, target_agents, nullable_local_coordinates,
          feature_inputs_start_index, background_agent_keys,
          background_map_object_keys, inputs);
  DCHECK_EQ(num_populated_agent_map_tensors, 4);
  feature_inputs_start_index += num_populated_agent_map_tensors;

  // Step 3: Populates the skeleton features and masks.
  if (enable_skeleton_feature_extraction_) {
    const int num_populated_skeleton_tensors =
        PopulateSkeletonFeatureAndMaskTensors(
            target_agents, feature_inputs_start_index, inputs);
    DCHECK_EQ(num_populated_skeleton_tensors, 2);
    feature_inputs_start_index += num_populated_skeleton_tensors;
  }

  // Step 4: Populates the route features and masks.
  if (enable_route_feature_extraction_) {
    const int num_populated_route_tensors =
        PopulateNormalRouteFeatureAndMaskTensors(
            target_agents, route_features, feature_inputs_start_index, inputs);
    DCHECK_EQ(num_populated_route_tensors, 2);
    feature_inputs_start_index += num_populated_route_tensors;
  }

  // Step 5: Populates the lidar patch/patch cuboid features and masks .
  if (enable_lidar_patch_feature_extraction_ &&
      enable_patch_cuboid_feature_extraction_) {
    TRACE_EVENT_SCOPE(
        prediction, VectornetFeatureExtractor_GetInputTensorsForLidarAndCuboid);
    // Select the lidar patch background agents from the background agents of
    // the agent features.
    std::unordered_map<ObjectId, std::vector<ObjectId>>
        selected_lidar_background_agents_id = GetLidarPatchBackgroundAgentIDs(
            target_agents, agents_features_,
            *DCHECK_NOTNULL(background_agent_keys),
            config_.max_num_agents_with_lidar_patch());
    const int num_populated_lidar_patch_and_patch_cuboid_tensors =
        PopulateLidarPatchAndPatchCuboidFeatureWithMaskTensors(
            target_agents, feature_inputs_start_index,
            selected_lidar_background_agents_id, inputs);
    DCHECK_EQ(num_populated_lidar_patch_and_patch_cuboid_tensors, 4);
    feature_inputs_start_index +=
        num_populated_lidar_patch_and_patch_cuboid_tensors;

    if (lidar_patch_background_agent_ids != nullptr) {
      *lidar_patch_background_agent_ids =
          std::move(selected_lidar_background_agents_id);
    }
  }

  // Step 6: Populates the lidar context features and masks.
  if (enable_lidar_context_feature_extraction_) {
    TRACE_EVENT_SCOPE(
        prediction,
        VectornetFeatureExtractor_GetInputTensorsForObstacleContext);
    const int num_populated_lidar_context_tensors =
        PopulateLidarContextFeatureAndMaskTensors(
            target_agents, feature_inputs_start_index, inputs);
    DCHECK_EQ(num_populated_lidar_context_tensors, 2);
    feature_inputs_start_index += num_populated_lidar_context_tensors;
  }

  // Step 7: Populates the obstacle context features and masks.
  if (enable_obstacle_feature_extraction_) {
    const int num_populated_obstacle_tensors =
        PopulateObstacleFeatureAndMaskTensors(
            target_agents, nullable_local_coordinates,
            feature_inputs_start_index, inputs);
    DCHECK_EQ(num_populated_obstacle_tensors, 2);
    feature_inputs_start_index += num_populated_obstacle_tensors;
  }

  // Step 8: Populates the traffic light context features and masks.
  if (enable_traffic_light_feature_extraction_) {
    const int num_populated_traffic_light_tensors =
        PopulateTrafficLightFeatureAndMaskTensors(
            target_agents, nullable_local_coordinates,
            feature_inputs_start_index, inputs);
    DCHECK_EQ(num_populated_traffic_light_tensors, 2);
    feature_inputs_start_index += num_populated_traffic_light_tensors;
  }
  DCHECK_EQ(feature_inputs_start_index, num_input_tensors_);

  // NOTE(renhao): If a new feature which is related with ego is added, please
  // remember to update corresponding modification for ignore-ego related
  // feature in step 9.
  // Step 9: Copies and masks the original input tensor for ignoring ego
  // prediction.
  if (!target_agent_idx_to_ignore_ego.empty()) {
    CopyAndMaskFeaturesForIgnoreEgoInputs(
        target_agents, batch_size, target_agent_idx_to_ignore_ego,
        *DCHECK_NOTNULL(batch_target_agents),
        *DCHECK_NOTNULL(batch_is_ignoring_ego_mask), inputs);
    DCHECK_EQ(batch_target_agents->size(), batch_size);
    DCHECK_EQ(batch_is_ignoring_ego_mask->size(), batch_size);
  }

  return inputs;
}  // namespace prediction

std::vector<neural_net::Tensor> VectornetFeatureExtractor::GetRouteTensors(
    const std::vector<RouteFeature>& route_features,
    const LocalCoordinates& local_coordinates, int num_routes) const {
  constexpr int batch_size = 1;
  const int route_feature_size =
      vectornet_feature_processor_.route_features_dimension();

  // Initialize route_tensor and route_mask_tensor.
  neural_net::Tensor route_tensor(
      neural_net::TensorType::kFloat,
      std::vector<int64_t>{batch_size, num_routes,
                           config_.num_vectors_in_route_feature(),
                           route_feature_size});
  std::memset(route_tensor.data(), 0, route_tensor.num_bytes());
  neural_net::Tensor route_mask_tensor(
      neural_net::TensorType::kBool,
      std::vector<int64_t>{batch_size, num_routes,
                           config_.num_vectors_in_route_feature()});
  std::memset(route_mask_tensor.data(), 0, route_mask_tensor.num_bytes());

  const auto [route_data_begin, route_data_end] =
      vectornet_util::GetTensorBeginAndEnd<float>(&route_tensor);
  const auto [route_mask_begin, route_mask_end] =
      vectornet_util::GetTensorBeginAndEnd<bool>(&route_mask_tensor);

  float* route_data_ptr = route_data_begin;
  bool* route_mask_ptr = route_mask_begin;
  for (const auto& route_feature : route_features) {
    DCHECK_LE(route_feature.features.size(),
              config_.num_vectors_in_route_feature());
    for (const auto& features : route_feature.features) {
      DCHECK_EQ(features.size(), route_feature_size);
      memcpy(route_data_ptr, features.data(), features.size() * sizeof(float));
      *route_mask_ptr = true;
      vectornet_feature_processor_.RunRouteFeatureNormalizers(
          route_data_ptr, features.size(), local_coordinates);
      route_data_ptr += features.size();
      ++route_mask_ptr;
    }
    const int pad_zero_size_for_route_mask =
        config_.num_vectors_in_route_feature() - route_feature.features.size();
    const int pad_zero_size_for_route_data =
        pad_zero_size_for_route_mask * route_feature_size;
    route_mask_ptr += pad_zero_size_for_route_mask;
    route_data_ptr += pad_zero_size_for_route_data;
  }
  if (route_features.size() < static_cast<size_t>(num_routes)) {
    const int pad_zero_size_for_route_mask =
        (num_routes - route_features.size()) *
        config_.num_vectors_in_route_feature();
    const int pad_zero_size_for_route_data =
        pad_zero_size_for_route_mask * route_feature_size;
    route_mask_ptr += pad_zero_size_for_route_mask;
    route_data_ptr += pad_zero_size_for_route_data;
  }

  DCHECK_EQ(route_mask_ptr, route_mask_end);
  DCHECK_EQ(route_data_ptr, route_data_end);

  return {std::move(route_tensor), std::move(route_mask_tensor)};
}

std::vector<neural_net::Tensor> VectornetFeatureExtractor::GetObstacleTensors(
    const Agent& target_agent, const LocalCoordinates& local_coordinates,
    const int num_obstacles) const {
  constexpr int batch_size = 1;
  const int obstacle_feature_size =
      vectornet_feature_processor_.obstacle_features_dimension();
  // Initialize obstacle_tensor and obstacle_mask_tensor.
  neural_net::Tensor obstacle_tensor(
      neural_net::TensorType::kFloat,
      std::vector<int64_t>{batch_size, num_obstacles,
                           config_.num_vectors_in_obstacle_feature(),
                           obstacle_feature_size});
  std::memset(obstacle_tensor.data(), 0, obstacle_tensor.num_bytes());
  neural_net::Tensor obstacle_mask_tensor(
      neural_net::TensorType::kBool,
      std::vector<int64_t>{batch_size, num_obstacles,
                           config_.num_vectors_in_obstacle_feature()});
  std::memset(obstacle_mask_tensor.data(), 0, obstacle_mask_tensor.num_bytes());

  const auto [obstacle_data_begin, obstacle_data_end] =
      vectornet_util::GetTensorBeginAndEnd<float>(&obstacle_tensor);
  const auto [obstacle_mask_begin, obstacle_mask_end] =
      vectornet_util::GetTensorBeginAndEnd<bool>(&obstacle_mask_tensor);

  float* obstacle_data_ptr = obstacle_data_begin;
  bool* obstacle_mask_ptr = obstacle_mask_begin;

  const std::vector<int> selected_background_obstacle_indexes =
      SelectBackgroundObstacleIndexes(target_agent.tracked_object(),
                                      obstacles_features_, num_obstacles,
                                      GetViewRangeOrInf(config_.view_range()));
  for (int i = 0;
       i < static_cast<int>(selected_background_obstacle_indexes.size()); ++i) {
    const int obstacle_index = selected_background_obstacle_indexes[i];
    const ObstacleFeature& obstacle_feature =
        obstacles_features_[obstacle_index];
    DCHECK_LE(obstacle_feature.features.size(),
              config_.num_vectors_in_obstacle_feature());
    for (const auto& features : obstacle_feature.features) {
      DCHECK_EQ(features.size(), obstacle_feature_size);
      memcpy(obstacle_data_ptr, features.data(),
             features.size() * sizeof(float));
      *obstacle_mask_ptr = true;
      vectornet_feature_processor_.RunObstacleFeatureNormalizers(
          obstacle_data_ptr, features.size(), local_coordinates);
      obstacle_data_ptr += features.size();
      ++obstacle_mask_ptr;
    }
    const int pad_zero_size_for_obstacle_mask =
        config_.num_vectors_in_obstacle_feature() -
        obstacle_feature.features.size();
    const int pad_zero_size_for_obstacle_data =
        pad_zero_size_for_obstacle_mask * obstacle_feature_size;
    obstacle_mask_ptr += pad_zero_size_for_obstacle_mask;
    obstacle_data_ptr += pad_zero_size_for_obstacle_data;
  }
  // Padding zeros to ensure fixed-size tensor output when input obstacles are
  // insufficient.
  if (selected_background_obstacle_indexes.size() <
      static_cast<size_t>(num_obstacles)) {
    const int pad_zero_size_for_obstacle_mask =
        (num_obstacles - selected_background_obstacle_indexes.size()) *
        config_.num_vectors_in_obstacle_feature();
    const int pad_zero_size_for_obstacle_data =
        pad_zero_size_for_obstacle_mask * obstacle_feature_size;
    obstacle_mask_ptr += pad_zero_size_for_obstacle_mask;
    obstacle_data_ptr += pad_zero_size_for_obstacle_data;
  }

  DCHECK_EQ(obstacle_mask_ptr, obstacle_mask_end);
  DCHECK_EQ(obstacle_data_ptr, obstacle_data_end);

  return {std::move(obstacle_tensor), std::move(obstacle_mask_tensor)};
}

std::vector<neural_net::Tensor>
VectornetFeatureExtractor::GetTrafficLightTensors(
    const Agent& target_agent, const LocalCoordinates& local_coordinates,
    const int num_traffic_lights) const {
  constexpr int batch_size = 1;
  const int traffic_light_feature_size =
      vectornet_feature_processor_.traffic_light_features_dimension();
  // Initialize traffic_light_tensor and traffic_light_mask_tensor.
  neural_net::Tensor traffic_light_tensor(
      neural_net::TensorType::kFloat,
      std::vector<int64_t>{batch_size, num_traffic_lights,
                           config_.num_vectors_in_traffic_light_feature(),
                           traffic_light_feature_size});
  std::memset(traffic_light_tensor.data(), 0, traffic_light_tensor.num_bytes());
  neural_net::Tensor traffic_light_mask_tensor(
      neural_net::TensorType::kBool,
      std::vector<int64_t>{batch_size, num_traffic_lights,
                           config_.num_vectors_in_traffic_light_feature()});
  std::memset(traffic_light_mask_tensor.data(), 0,
              traffic_light_mask_tensor.num_bytes());

  const auto [traffic_light_data_begin, traffic_light_data_end] =
      vectornet_util::GetTensorBeginAndEnd<float>(&traffic_light_tensor);
  const auto [traffic_light_mask_begin, traffic_light_mask_end] =
      vectornet_util::GetTensorBeginAndEnd<bool>(&traffic_light_mask_tensor);

  float* traffic_light_data_ptr = traffic_light_data_begin;
  bool* traffic_light_mask_ptr = traffic_light_mask_begin;

  const std::vector<TrafficLightKey> selected_background_traffic_lights =
      SelectBackgroundTrafficLights(target_agent.tracked_object(),
                                    traffic_light_features_,
                                    num_traffic_lights);
  for (const TrafficLightKey& traffic_light_key :
       selected_background_traffic_lights) {
    auto iter = traffic_light_features_.find(traffic_light_key);
    DCHECK(iter != traffic_light_features_.end());
    const VectorFeatures& traffic_light_feature = iter->second.features;
    DCHECK_LE(traffic_light_feature.size(),
              config_.num_vectors_in_traffic_light_feature());

    const int pad_zero_size_for_traffic_light_mask =
        config_.num_vectors_in_traffic_light_feature() -
        traffic_light_feature.size();
    const int pad_zero_size_for_traffic_light_data =
        pad_zero_size_for_traffic_light_mask * traffic_light_feature_size;
    traffic_light_mask_ptr += pad_zero_size_for_traffic_light_mask;
    traffic_light_data_ptr += pad_zero_size_for_traffic_light_data;

    for (const auto& feature : traffic_light_feature) {
      DCHECK_EQ(feature.size(), traffic_light_feature_size);
      memcpy(traffic_light_data_ptr, feature.data(),
             feature.size() * sizeof(float));
      *traffic_light_mask_ptr = true;
      vectornet_feature_processor_.RunTrafficLightFeatureNormalizers(
          traffic_light_data_ptr, feature.size(), local_coordinates,
          iter->second);
      traffic_light_data_ptr += feature.size();
      ++traffic_light_mask_ptr;
    }
  }
  // Padding zeros to ensure fixed-size tensor output when input traffic_lights
  // are insufficient.
  if (selected_background_traffic_lights.size() <
      static_cast<size_t>(num_traffic_lights)) {
    const int pad_zero_size_for_traffic_light_mask =
        (num_traffic_lights - selected_background_traffic_lights.size()) *
        config_.num_vectors_in_traffic_light_feature();
    const int pad_zero_size_for_traffic_light_data =
        pad_zero_size_for_traffic_light_mask * traffic_light_feature_size;
    traffic_light_mask_ptr += pad_zero_size_for_traffic_light_mask;
    traffic_light_data_ptr += pad_zero_size_for_traffic_light_data;
  }

  DCHECK_EQ(traffic_light_mask_ptr, traffic_light_mask_end);
  DCHECK_EQ(traffic_light_data_ptr, traffic_light_data_end);

  return {std::move(traffic_light_tensor),
          std::move(traffic_light_mask_tensor)};
}

int VectornetFeatureExtractor::PopulateSkeletonFeatureAndMaskTensors(
    const std::vector<const Agent*>& target_agents, int inputs_index,
    std::vector<neural_net::Tensor>& inputs) const {
  DCHECK_LE(inputs_index + 2, inputs.size());
  const int start_input_tensor_index = inputs_index;

  const auto [skeleton_data_begin, populate_skeleton_data_end] =
      vectornet_util::GetTensorBeginAndEnd<float>(&inputs[inputs_index++], 0,
                                                  target_agents.size());
  const auto [skeleton_mask_begin, populate_skeleton_mask_end] =
      vectornet_util::GetTensorBeginAndEnd<bool>(&inputs[inputs_index++], 0,
                                                 target_agents.size());

  float* skeleton_data_ptr = skeleton_data_begin;
  bool* skeleton_mask_ptr = skeleton_mask_begin;

  const int num_target_agents = target_agents.size();
  for (int i = 0; i < num_target_agents; ++i) {
    const Agent& target_agent = *target_agents[i];
    const int64_t target_agent_id = target_agent.object_id();
    // Get normalize parameters.
    const LocalCoordinates local_coordinates(target_agent.tracked_object());

    DCHECK(skeleton_features_.find(target_agent_id) !=
           skeleton_features_.end());
    const SkeletonFeature& target_agent_skeleton_feature =
        skeleton_features_.at(target_agent_id);
    // Padding zeros for skeletons with short history.
    const int pad_zero_size_for_skeleton_mask =
        (config_.num_histories_in_skeleton() -
         target_agent_skeleton_feature.mask.size()) *
        constants::kNumPedSkeletonPoints;
    const int skeleton_feature_size =
        vectornet_feature_processor_.skeleton_features_dimension();
    const int pad_zero_size_for_skeleton_data =
        pad_zero_size_for_skeleton_mask * skeleton_feature_size;
    skeleton_data_ptr += pad_zero_size_for_skeleton_data;
    skeleton_mask_ptr += pad_zero_size_for_skeleton_mask;
    for (size_t feature_index = 0;
         feature_index < target_agent_skeleton_feature.mask.size();
         ++feature_index) {
      for (int point_index = 0; point_index < constants::kNumPedSkeletonPoints;
           ++point_index) {
        const bool skeleton_mask =
            target_agent_skeleton_feature.mask[feature_index][point_index];
        *skeleton_mask_ptr = skeleton_mask;
        skeleton_mask_ptr++;
        const std::vector<float>& feature =
            target_agent_skeleton_feature.features[feature_index][point_index];
        if (skeleton_mask) {
          memcpy(skeleton_data_ptr, feature.data(),
                 feature.size() * sizeof(float));
          vectornet_feature_processor_.RunSkeletonFeatureNormalizers(
              /*normalizer_input=*/{.feature_size =
                                        static_cast<int>(feature.size()),
                                    .coordinates = local_coordinates},
              /*feature_list_ptr=*/skeleton_data_ptr);
        }
        skeleton_data_ptr += feature.size();
      }
    }
  }
  DCHECK_EQ(skeleton_data_ptr, populate_skeleton_data_end);
  DCHECK_EQ(skeleton_mask_ptr, populate_skeleton_mask_end);
  return inputs_index - start_input_tensor_index;
}

void VectornetFeatureExtractor::FillLidarPatchTensorForTargetAgent(
    const std::vector<ObjectId>& background_agent_ids,
    const LocalCoordinates& local_coordinates, float*& lidar_data_ptr,
    bool*& lidar_mask_ptr) const {
  DCHECK(lidar_data_ptr);
  DCHECK(lidar_mask_ptr);

  const int num_background_agents = background_agent_ids.size();
  DCHECK_LE(num_background_agents, config_.max_num_agents_with_lidar_patch());

  const int lidar_feature_size =
      vectornet_feature_processor_.lidar_patch_features_dimension();

  // Fill tensor for lidar patch features.
  for (const ObjectId agent_id : background_agent_ids) {
    auto iter = lidar_patch_features_.find(agent_id);
    DCHECK(iter != lidar_patch_features_.end());
    const std::vector<VectorFeatures>& lidar_patch_feature =
        iter->second.features;

    // Check right for horizons of lidar patch.
    DCHECK_LE(lidar_patch_feature.size(),
              config_.num_histories_in_lidar_patch_feature());
    // Padding zeros for lidar patch with short history.
    const int pad_zero_size_for_lidar_mask =
        (config_.num_histories_in_lidar_patch_feature() -
         lidar_patch_feature.size()) *
        config_.max_num_lidar_points();
    lidar_data_ptr += pad_zero_size_for_lidar_mask * lidar_feature_size;
    lidar_mask_ptr += pad_zero_size_for_lidar_mask;

    for (const auto& one_frame_feature : lidar_patch_feature) {
      if (one_frame_feature.empty()) {
        lidar_data_ptr += config_.max_num_lidar_points() * lidar_feature_size;
        lidar_mask_ptr += config_.max_num_lidar_points();
        continue;
      }
      for (const auto& feature : one_frame_feature) {
        memcpy(lidar_data_ptr, feature.data(), feature.size() * sizeof(float));
        vectornet_feature_processor_.RunLidarPatchFeatureNormalizers(
            /*normalizer_input=*/{.feature_size =
                                      static_cast<int>(feature.size()),
                                  .coordinates = local_coordinates},
            /*feature_list_ptr=*/lidar_data_ptr);
        lidar_data_ptr += feature.size();
        *lidar_mask_ptr = true;
        lidar_mask_ptr++;
      }
      const int pad_zero_size_for_one_frame =
          config_.max_num_lidar_points() - one_frame_feature.size();
      lidar_data_ptr += pad_zero_size_for_one_frame * lidar_feature_size;
      lidar_mask_ptr += pad_zero_size_for_one_frame;
    }
  }
  lidar_data_ptr +=
      (config_.max_num_agents_with_lidar_patch() - num_background_agents) *
      config_.num_histories_in_lidar_patch_feature() *
      config_.max_num_lidar_points() * lidar_feature_size;
  lidar_mask_ptr +=
      (config_.max_num_agents_with_lidar_patch() - num_background_agents) *
      config_.num_histories_in_lidar_patch_feature() *
      config_.max_num_lidar_points();
}

void VectornetFeatureExtractor::FillPatchCuboidTensorForTargetAgent(
    const std::vector<ObjectId>& background_agent_ids,
    const LocalCoordinates& local_coordinates, float*& cuboid_data_ptr,
    bool*& cuboid_mask_ptr) const {
  DCHECK(cuboid_data_ptr);
  DCHECK(cuboid_mask_ptr);

  const int num_background_agents = background_agent_ids.size();
  DCHECK_LE(num_background_agents, config_.max_num_agents_with_lidar_patch());

  const int cuboid_feature_size =
      vectornet_feature_processor_.patch_cuboid_features_dimension();

  for (const ObjectId agent_id : background_agent_ids) {
    const auto iter = patch_cuboid_features_.find(agent_id);
    DCHECK(iter != patch_cuboid_features_.end());
    const VectorFeatures& cuboid_features = iter->second;

    const int pad_zero_size_for_cuboid_mask =
        config_.num_histories_in_cuboid_feature() - cuboid_features.size();
    cuboid_data_ptr += pad_zero_size_for_cuboid_mask * cuboid_feature_size;
    cuboid_mask_ptr += pad_zero_size_for_cuboid_mask;
    for (const auto& feature : cuboid_features) {
      DCHECK(!feature.empty());
      memcpy(cuboid_data_ptr, feature.data(), feature.size() * sizeof(float));
      vectornet_feature_processor_.RunPatchCuboidFeatureNormalizers(
          /*normalizer_input=*/{.feature_size =
                                    static_cast<int>(feature.size()),
                                .coordinates = local_coordinates},
          /*feature_list_ptr=*/cuboid_data_ptr);
      cuboid_data_ptr += feature.size();
      *cuboid_mask_ptr = true;
      cuboid_mask_ptr++;
    }
  }
  cuboid_data_ptr +=
      (config_.max_num_agents_with_lidar_patch() - num_background_agents) *
      config_.num_histories_in_cuboid_feature() * cuboid_feature_size;
  cuboid_mask_ptr +=
      (config_.max_num_agents_with_lidar_patch() - num_background_agents) *
      config_.num_histories_in_cuboid_feature();
}

int VectornetFeatureExtractor::
    PopulateLidarPatchAndPatchCuboidFeatureWithMaskTensors(
        const std::vector<const Agent*>& target_agents, int input_index,
        const std::unordered_map<ObjectId, std::vector<ObjectId>>&
            background_agent_ids,
        std::vector<neural_net::Tensor>& inputs) const {
  DCHECK_LE(input_index + 2 * kNumInputTensorsForFeatureWithMask,
            inputs.size());

  if (lidar_patch_features_.empty() || patch_cuboid_features_.empty()) {
    return 2 * kNumInputTensorsForFeatureWithMask;
  }

  const int start_tensor_idx = input_index;
  const auto [lidar_data_begin, lidar_data_end] =
      vectornet_util::GetTensorBeginAndEnd<float>(&inputs[input_index++], 0,
                                                  target_agents.size());
  const auto [lidar_mask_begin, lidar_mask_end] =
      vectornet_util::GetTensorBeginAndEnd<bool>(&inputs[input_index++], 0,
                                                 target_agents.size());
  const auto [cuboid_data_begin, cuboid_data_end] =
      vectornet_util::GetTensorBeginAndEnd<float>(&inputs[input_index++], 0,
                                                  target_agents.size());
  const auto [cuboid_mask_begin, cuboid_mask_end] =
      vectornet_util::GetTensorBeginAndEnd<bool>(&inputs[input_index++], 0,
                                                 target_agents.size());

  const int lidar_feature_len_for_target_agent =
      config_.max_num_agents_with_lidar_patch() *
      config_.num_histories_in_lidar_patch_feature() *
      config_.max_num_lidar_points();
  const int lidar_feature_size =
      vectornet_feature_processor_.lidar_patch_features_dimension();

  const int cuboid_feature_len_for_target_agent =
      config_.max_num_agents_with_lidar_patch() *
      config_.num_histories_in_lidar_patch_feature();
  const int cuboid_feature_size =
      vectornet_feature_processor_.patch_cuboid_features_dimension();

  // Parallel lidar patch feature normalize for each agent.
  auto fill_feature_task =
      [&target_agents, &background_agent_ids,
       lidar_feature_len_for_target_agent, lidar_feature_size,
       cuboid_feature_len_for_target_agent, cuboid_feature_size,
       lidar_data_begin = lidar_data_begin, lidar_mask_begin = lidar_mask_begin,
       cuboid_data_begin = cuboid_data_begin,
       cuboid_mask_begin = cuboid_mask_begin, this](int i) {
        const Agent& target_agent = *DCHECK_NOTNULL(target_agents[i]);
        const int64_t target_agent_id = target_agent.object_id();
        // Get normalize parameters.
        const LocalCoordinates local_coordinates(target_agent.tracked_object());

        // Gets background agents(including the target agent) for the target
        // agent.
        const auto iter = background_agent_ids.find(target_agent_id);
        DCHECK(iter != background_agent_ids.end());
        const std::vector<ObjectId>& selected_background_agents_ids =
            iter->second;
        DCHECK_GE(selected_background_agents_ids.size(), 1);
        DCHECK_EQ(selected_background_agents_ids[0], target_agent_id);

        // Fill lidar patch tensors for one agent.
        float* lidar_data_ptr =
            lidar_data_begin +
            i * lidar_feature_len_for_target_agent * lidar_feature_size;
        const float* filled_lidar_data_end =
            lidar_data_ptr +
            lidar_feature_len_for_target_agent * lidar_feature_size;
        bool* lidar_mask_ptr =
            lidar_mask_begin + i * lidar_feature_len_for_target_agent;
        const bool* filled_lidar_mask_end =
            lidar_mask_ptr + lidar_feature_len_for_target_agent;

        this->FillLidarPatchTensorForTargetAgent(
            selected_background_agents_ids, local_coordinates, lidar_data_ptr,
            lidar_mask_ptr);
        DCHECK_EQ(lidar_data_ptr, filled_lidar_data_end);
        DCHECK_EQ(lidar_mask_ptr, filled_lidar_mask_end);

        // Fill patch cuboid tensors for one agent.
        float* cuboid_data_ptr =
            cuboid_data_begin +
            i * cuboid_feature_len_for_target_agent * cuboid_feature_size;
        const float* filled_cuboid_data_end =
            cuboid_data_ptr +
            cuboid_feature_len_for_target_agent * cuboid_feature_size;
        bool* cuboid_mask_ptr =
            cuboid_mask_begin + i * cuboid_feature_len_for_target_agent;
        const bool* filled_cuboid_mask_end =
            cuboid_mask_ptr + cuboid_feature_len_for_target_agent;

        this->FillPatchCuboidTensorForTargetAgent(
            selected_background_agents_ids, local_coordinates, cuboid_data_ptr,
            cuboid_mask_ptr);
        DCHECK_EQ(cuboid_data_ptr, filled_cuboid_data_end);
        DCHECK_EQ(cuboid_mask_ptr, filled_cuboid_mask_end);
      };

  tbb::parallel_for(0, static_cast<int>(target_agents.size()),
                    fill_feature_task);
  return input_index - start_tensor_idx;
}

void VectornetFeatureExtractor::PopulateLidarContextTensorsForTargetAgent(
    const Agent& target_agent, const LocalCoordinates& local_coordinates,
    float* context_data_ptr, bool* context_mask_ptr) const {
  const int context_feature_size =
      vectornet_feature_processor_.lidar_context_features_dimension();

  // Calculate valid feature tensor end position for checking.
  const std::vector<ObstacleClusterKey>& obstacle_cluster_keys =
      agent_nearby_obstacle_clusters_.at(target_agent.object_id());
  const int valid_context_feature_len =
      obstacle_cluster_keys.size() * config_.max_num_obstacles();
  const float* valid_context_data_end =
      context_data_ptr + valid_context_feature_len * context_feature_size;
  const bool* valid_context_mask_end =
      context_mask_ptr + valid_context_feature_len;

  // Fill tensor for lidar context features.
  for (const auto& cluster_key : obstacle_cluster_keys) {
    // Get lidar context object feature.
    const VectorFeatures& context_object_feature =
        lidar_context_features_.at(cluster_key);
    for (const auto& feature : context_object_feature) {
      memcpy(context_data_ptr, feature.data(), feature.size() * sizeof(float));
      vectornet_feature_processor_.RunLidarContextFeatureNormalizers(
          /*normalizer_input=*/{.feature_size =
                                    static_cast<int>(feature.size()),
                                .coordinates = local_coordinates},
          /*feature_list_ptr=*/context_data_ptr);
      context_data_ptr += feature.size();
      *context_mask_ptr = true;
      context_mask_ptr++;
    }
    // Padding zero for one cluster.
    const int pad_zero_size_for_one_cluster =
        config_.max_num_obstacles() - context_object_feature.size();
    context_data_ptr += pad_zero_size_for_one_cluster * context_feature_size;
    context_mask_ptr += pad_zero_size_for_one_cluster;
  }
  DCHECK_EQ(context_data_ptr, valid_context_data_end);
  DCHECK_EQ(context_mask_ptr, valid_context_mask_end);
}

int VectornetFeatureExtractor::PopulateLidarContextFeatureAndMaskTensors(
    const std::vector<const Agent*>& target_agents, int input_index,
    std::vector<neural_net::Tensor>& inputs) const {
  DCHECK_LE(input_index + kNumInputTensorsForFeatureWithMask, inputs.size());
  if (agent_nearby_obstacle_clusters_.empty() ||
      lidar_context_features_.empty()) {
    return kNumInputTensorsForFeatureWithMask;
  }

  const int start_tensor_idx = input_index;
  const auto [context_data_begin, context_data_end] =
      vectornet_util::GetTensorBeginAndEnd<float>(&inputs[input_index++], 0,
                                                  target_agents.size());
  const auto [context_mask_begin, context_mask_end] =
      vectornet_util::GetTensorBeginAndEnd<bool>(&inputs[input_index++], 0,
                                                 target_agents.size());
  float* context_data_ptr = context_data_begin;
  bool* context_mask_ptr = context_mask_begin;

  const int context_feature_len_for_one_agent =
      config_.max_num_lidar_context_objects() * config_.max_num_obstacles();
  const int context_feature_size =
      vectornet_feature_processor_.lidar_context_features_dimension();

  for (const Agent* target_agent : target_agents) {
    DCHECK(target_agent);
    const int64_t target_agent_id = target_agent->object_id();

    // Get normalize parameters.
    const LocalCoordinates local_coordinates(target_agent->tracked_object());
    DCHECK(agent_nearby_obstacle_clusters_.find(target_agent_id) !=
           agent_nearby_obstacle_clusters_.end());

    // Fill lidar context tensor for one agent.
    PopulateLidarContextTensorsForTargetAgent(
        *target_agent, local_coordinates, context_data_ptr, context_mask_ptr);

    context_data_ptr +=
        context_feature_len_for_one_agent * context_feature_size;
    context_mask_ptr += context_feature_len_for_one_agent;
  }
  DCHECK_EQ(context_data_ptr, context_data_end);
  DCHECK_EQ(context_mask_ptr, context_mask_end);
  return input_index - start_tensor_idx;
}

void VectornetFeatureExtractor::PopulateAgentFeatureTensorForTargetAgent(
    const Agent& target_agent, const LocalCoordinates& local_coordinates,
    float* agent_data_ptr, bool* agent_mask_ptr,
    std::vector<AgentKey>* agent_keys) const {
  DCHECK(agent_data_ptr);
  DCHECK(agent_mask_ptr);

  const int64_t target_agent_id = target_agent.object_id();

  const std::vector<int> selected_background_agent_indexes =
      SelectBackgroundAgentIndexes(target_agent.tracked_object(),
                                   agents_features_, config_.max_num_agents(),
                                   GetViewRangeOrInf(config_.view_range()),
                                   config_.excluded_background_agent_tags(),
                                   config_.max_num_importance_rank_agent());
  DCHECK_EQ(agents_features_[selected_background_agent_indexes[0]].object_id,
            target_agent_id);

  if (target_agent_id != constants::kRobotObjId) {
    DCHECK_EQ(agents_features_[selected_background_agent_indexes[1]].object_id,
              constants::kRobotObjId);
  }

  if (agent_keys != nullptr) {
    for (const int background_agent_index : selected_background_agent_indexes) {
      agent_keys->emplace_back(
          agents_features_[background_agent_index].object_type,
          agents_features_[background_agent_index].object_id);
    }
  }

  const int agent_feature_size =
      vectornet_feature_processor_.agent_features_dimension();
  // Fill and normalize agent features.
  for (int i = 0;
       i < static_cast<int>(selected_background_agent_indexes.size()); ++i) {
    const int agent_index = selected_background_agent_indexes[i];
    const AgentFeature& agent_feature = agents_features_[agent_index];
    // Set target agent index value.
    DCHECK_LE(agent_feature.features.size(),
              config_.num_vectors_in_agent_feature());
    // Padding zeros for agents with short history.
    const int pad_zero_size_for_agent_mask =
        config_.num_vectors_in_agent_feature() - agent_feature.features.size();
    const int pad_zero_size_for_agent_data =
        pad_zero_size_for_agent_mask * agent_feature_size;
    agent_data_ptr += pad_zero_size_for_agent_data;
    agent_mask_ptr += pad_zero_size_for_agent_mask;
    for (const auto& feature : agent_feature.features) {
      DCHECK_EQ(feature.size(), agent_feature_size);
      memcpy(agent_data_ptr, feature.data(), feature.size() * sizeof(float));
      *agent_mask_ptr = true;

      // Special masking logics for some agent features.
      const bool is_target_agent = (i == constants::kTargetAgentIndex);
      const bool is_ego_agent =
          (agent_feature.object_id == constants::kRobotObjId);
      const bool mask_vehicle_signal_lights_status =
          ((is_target_agent && config_.mask_light_status_for_target_agent()) ||
           (is_ego_agent && config_.mask_light_status_for_ego_agent()));

      vectornet_feature_processor_.RunAgentFeatureNormalizers(
          /*normalizer_input=*/
          {
              .feature_size = static_cast<int>(feature.size()),
              .coordinates = local_coordinates,
              .mask_vehicle_signal_lights_status =
                  mask_vehicle_signal_lights_status,
          },
          /*feature_list_ptr=*/agent_data_ptr);
      agent_data_ptr += feature.size();
      ++agent_mask_ptr;
    }
  }
}

void VectornetFeatureExtractor::PopulateMapFeatureTensorForTargetAgent(
    const SceneContext& scene_context, const Agent& target_agent,
    const LocalCoordinates& local_coordinates, float* map_data_ptr,
    bool* map_mask_ptr, std::vector<MapObjectKey>* map_object_keys) const {
  DCHECK(map_data_ptr);
  DCHECK(map_mask_ptr);

  const std::vector<ConstMapFeatureIterator>
      selected_background_map_object_iterators =
          SelectBackgroundMapObjectIterators(target_agent.tracked_object(),
                                             map_objects_features_, config_);
  if (map_object_keys != nullptr) {
    for (const auto& iterator : selected_background_map_object_iterators) {
      map_object_keys->push_back(iterator->first);
    }
  }
  const int map_object_feature_size =
      vectornet_feature_processor_.map_object_features_dimension();
  // Fill and normalize map object features.
  for (const auto& iterator : selected_background_map_object_iterators) {
    const VectorFeatures& vector_features = iterator->second.features;
    DCHECK_LE(vector_features.size(),
              config_.num_vectors_in_map_object_feature());
    const MapObjectKey& map_object_key = iterator->first;
    const TrafficLightFeature tl_feature =
        GetTrafficLightFeatureForOneMapObject(
            map_object_key, scene_context,
            config_.extract_accurate_traffic_light_feature(),
            config_.set_occluded_traffic_light_color());
    for (const auto& features : vector_features) {
      DCHECK_EQ(features.size(), map_object_feature_size);
      memcpy(map_data_ptr, features.data(), features.size() * sizeof(float));
      *map_mask_ptr = true;
      vectornet_feature_processor_.RunMapObjectFeatureNormalizers(
          map_data_ptr, features.size(), local_coordinates, tl_feature);
      map_data_ptr += features.size();
      ++map_mask_ptr;
    }
    // Move the ptr to the next valid map object address.
    const int pad_zero_size_for_map_mask =
        config_.num_vectors_in_map_object_feature() - vector_features.size();
    const int pad_zero_size_for_map_data =
        pad_zero_size_for_map_mask * map_object_feature_size;
    map_mask_ptr += pad_zero_size_for_map_mask;
    map_data_ptr += pad_zero_size_for_map_data;
  }
}

int VectornetFeatureExtractor::PopulateNormalFeatureAndMaskTensors(
    const SceneContext& scene_context,
    const std::vector<const Agent*>& target_agents,
    const std::optional<std::map<int64_t, LocalCoordinates>>&
        nullable_local_coordinates,
    int inputs_index,
    std::map<int64_t, std::vector<AgentKey>>* background_agent_keys,
    std::map<int64_t, std::vector<MapObjectKey>>* background_map_object_keys,
    std::vector<neural_net::Tensor>& inputs) const {
  TRACE_EVENT_SCOPE(prediction,
                    VectornetFeatureExtractor_GetInputTensorsForAgentAndMap);
  DCHECK_EQ(num_input_tensors_, inputs.size())
      << "Inputs tensor must contain and only contain agent, agent_mask, "
         "map_object, map_object_mask tensors and other skeleton/route "
         "features"
         " and mask(if the other features are enabled.).";
  if (nullable_local_coordinates.has_value()) {
    DCHECK_EQ(target_agents.size(), nullable_local_coordinates->size())
        << "Each target agent should corresponds to one local_coordinates";
  }
  DCHECK_LE(inputs_index + 4, inputs.size());
  const int start_input_tensor_index = inputs_index;

  const auto [agent_data_begin, populate_agent_data_end] =
      vectornet_util::GetTensorBeginAndEnd<float>(&inputs[inputs_index++], 0,
                                                  target_agents.size());
  const auto [agent_mask_begin, populate_agent_mask_end] =
      vectornet_util::GetTensorBeginAndEnd<bool>(&inputs[inputs_index++], 0,
                                                 target_agents.size());
  const auto [map_data_begin, populate_map_data_end] =
      vectornet_util::GetTensorBeginAndEnd<float>(&inputs[inputs_index++], 0,
                                                  target_agents.size());
  const auto [map_mask_begin, populate_map_mask_end] =
      vectornet_util::GetTensorBeginAndEnd<bool>(&inputs[inputs_index++], 0,
                                                 target_agents.size());

  const int num_agent_feature_vectors =
      config_.max_num_agents() * config_.num_vectors_in_agent_feature();
  const int num_map_feature_vectors =
      config_.max_num_map_objects_segment() *
      config_.num_vectors_in_map_object_feature();

  // Initial debugging information for parallelism.
  if (background_agent_keys != nullptr) {
    for (const auto* agent : target_agents) {
      background_agent_keys->emplace(agent->object_id(),
                                     std::vector<AgentKey>{});
    }
  }
  if (background_map_object_keys != nullptr) {
    for (const auto* agent : target_agents) {
      background_map_object_keys->emplace(agent->object_id(),
                                          std::vector<MapObjectKey>{});
    }
  }

  // Parallel agent and map feature normalize for each agent.
  auto fill_feature_task = [&scene_context, &target_agents,
                            &nullable_local_coordinates,
                            &num_agent_feature_vectors,
                            &num_map_feature_vectors, background_agent_keys,
                            background_map_object_keys,
                            agent_data_begin = agent_data_begin,
                            agent_mask_begin = agent_mask_begin,
                            map_data_begin = map_data_begin,
                            map_mask_begin = map_mask_begin, this](int i) {
    const Agent& target_agent = *target_agents[i];
    const int64_t target_agent_id = target_agent.object_id();

    // Get normalize parameters. Use target agent for local_coordinates if
    // no explicit coordinates is given.
    const LocalCoordinates local_coordinates =
        nullable_local_coordinates.has_value()
            ? gtl::FindOrDie(*nullable_local_coordinates, target_agent_id)
            : LocalCoordinates(target_agent.tracked_object());
    // Fill agent feature tensors for one agent.
    float* agent_data_ptr =
        agent_data_begin +
        i * num_agent_feature_vectors *
            this->vectornet_feature_processor_.agent_features_dimension();
    bool* agent_mask_ptr = agent_mask_begin + i * num_agent_feature_vectors;
    std::vector<AgentKey>* agent_keys =
        background_agent_keys != nullptr
            ? &(*background_agent_keys)[target_agent_id]
            : nullptr;
    this->PopulateAgentFeatureTensorForTargetAgent(
        target_agent, local_coordinates, agent_data_ptr, agent_mask_ptr,
        agent_keys);

    // Fill map feature tensor for one agent.
    float* map_data_ptr =
        map_data_begin +
        i * num_map_feature_vectors *
            this->vectornet_feature_processor_.map_object_features_dimension();
    bool* map_mask_ptr = map_mask_begin + i * num_map_feature_vectors;
    std::vector<MapObjectKey>* map_object_keys =
        background_map_object_keys != nullptr
            ? &(*background_map_object_keys)[target_agent_id]
            : nullptr;
    this->PopulateMapFeatureTensorForTargetAgent(
        scene_context, target_agent, local_coordinates, map_data_ptr,
        map_mask_ptr, map_object_keys);
  };
  tbb::parallel_for(0, static_cast<int>(target_agents.size()),
                    fill_feature_task);
  return inputs_index - start_input_tensor_index;
}

SkeletonFeature VectornetFeatureExtractor::ExtractOneAgentSkeletonFeatures(
    const Agent& agent) const {
  SkeletonFeature skeleton_feature;
  const av_comm::HistoryBuffer<int64_t, AgentState>& agent_history_states =
      agent.history_states();

  if (!agent_history_states.empty()) {
    // Ensure there are at most config_.num_histories_in_skeleton() features.
    auto iter =
        agent_history_states.size() > config_.num_histories_in_skeleton()
            ? agent_history_states.end() - config_.num_histories_in_skeleton()
            : agent_history_states.begin();
    while (++iter != agent_history_states.end()) {
      const AgentState& current_agent_state = iter->second;
      VectorFeatures skeleton_features =
          vectornet_feature_processor_.RunSkeletonFeatureExtractors(
              current_agent_state);
      skeleton_feature.features.push_back(std::move(skeleton_features));
      skeleton_feature.mask.push_back(
          vectornet_feature_processor_.ExtractorSkeletonFeatureMask(
              current_agent_state));
    }
  }
  VectorFeatures skeleton_features =
      vectornet_feature_processor_.RunSkeletonFeatureExtractors(
          agent.current_state());
  skeleton_feature.features.push_back(std::move(skeleton_features));
  skeleton_feature.mask.push_back(
      vectornet_feature_processor_.ExtractorSkeletonFeatureMask(
          agent.current_state()));
  return skeleton_feature;
}

void VectornetFeatureExtractor::ExtractSkeletonFeatures(
    const std::vector<const Agent*>& target_agents) {
  if (!enable_skeleton_feature_extraction_) {
    return;
  }
  skeleton_features_.clear();

  for (const auto& agent_ptr : target_agents) {
    if (agent_ptr->object_type() == voy::perception::ObjectType::PED) {
      skeleton_features_[agent_ptr->object_id()] =
          ExtractOneAgentSkeletonFeatures(*agent_ptr);
    }
  }
}

int VectornetFeatureExtractor::PopulateNormalRouteFeatureAndMaskTensors(
    const std::vector<const Agent*>& target_agents,
    const std::vector<RouteFeature>& route_features, int inputs_index,
    std::vector<neural_net::Tensor>& inputs) const {
  DCHECK_LE(inputs_index + 2, inputs.size());
  const int start_input_tensor_index = inputs_index;

  const auto [route_data_begin, populate_route_data_end] =
      vectornet_util::GetTensorBeginAndEnd<float>(&inputs[inputs_index++], 0,
                                                  target_agents.size());
  const auto [route_mask_begin, populate_route_mask_end] =
      vectornet_util::GetTensorBeginAndEnd<bool>(&inputs[inputs_index++], 0,
                                                 target_agents.size());

  float* route_data_ptr = route_data_begin;
  bool* route_mask_ptr = route_mask_begin;

  for (const Agent* target_agent : target_agents) {
    const LocalCoordinates local_coordinates(target_agent->tracked_object());
    std::vector<neural_net::Tensor> route_tensors = GetRouteTensors(
        route_features, local_coordinates, config_.max_num_routes());
    memcpy(route_data_ptr, route_tensors[0].data(),
           route_tensors[0].num_bytes());
    memcpy(route_mask_ptr, route_tensors[1].data(),
           route_tensors[1].num_bytes());
    const auto size_route_i_data =
        vectornet_util::GetTensorSize(route_tensors[0]);
    const auto size_route_i_mask =
        vectornet_util::GetTensorSize(route_tensors[1]);
    route_data_ptr += size_route_i_data;
    route_mask_ptr += size_route_i_mask;
  }
  DCHECK_EQ(route_data_ptr, populate_route_data_end);
  DCHECK_EQ(route_mask_ptr, populate_route_mask_end);
  return inputs_index - start_input_tensor_index;
}

int VectornetFeatureExtractor::PopulateObstacleFeatureAndMaskTensors(
    const std::vector<const Agent*>& target_agents,
    const std::optional<std::map<int64_t, LocalCoordinates>>&
        nullable_local_coordinates,
    int inputs_index, std::vector<neural_net::Tensor>& inputs) const {
  DCHECK_LE(inputs_index + 2, inputs.size());
  if (nullable_local_coordinates.has_value()) {
    DCHECK_EQ(target_agents.size(), nullable_local_coordinates->size())
        << "Each target agent should corresponds to one local_coordinates";
  }
  const int start_input_tensor_index = inputs_index;
  const auto [obstacle_data_begin, populate_obstacle_data_end] =
      vectornet_util::GetTensorBeginAndEnd<float>(&inputs[inputs_index++], 0,
                                                  target_agents.size());
  const auto [obstacle_mask_begin, populate_obstacle_mask_end] =
      vectornet_util::GetTensorBeginAndEnd<bool>(&inputs[inputs_index++], 0,
                                                 target_agents.size());

  float* obstacle_data_ptr = obstacle_data_begin;
  bool* obstacle_mask_ptr = obstacle_mask_begin;

  const int num_target_agents = target_agents.size();
  for (int i = 0; i < num_target_agents; ++i) {
    const Agent& target_agent = *target_agents[i];
    const int64_t target_agent_id = target_agent.object_id();
    const LocalCoordinates local_coordinates =
        nullable_local_coordinates.has_value()
            ? gtl::FindOrDie(*nullable_local_coordinates, target_agent_id)
            : LocalCoordinates(target_agent.tracked_object());
    std::vector<neural_net::Tensor> obstacle_tensors = GetObstacleTensors(
        target_agent, local_coordinates, config_.max_num_obstacles_segment());
    memcpy(obstacle_data_ptr, obstacle_tensors[0].data(),
           obstacle_tensors[0].num_bytes());
    memcpy(obstacle_mask_ptr, obstacle_tensors[1].data(),
           obstacle_tensors[1].num_bytes());
    const auto size_obstacle_i_data =
        vectornet_util::GetTensorSize(obstacle_tensors[0]);
    const auto size_obstacle_i_mask =
        vectornet_util::GetTensorSize(obstacle_tensors[1]);
    obstacle_data_ptr += size_obstacle_i_data;
    obstacle_mask_ptr += size_obstacle_i_mask;
  }
  DCHECK_EQ(obstacle_data_ptr, populate_obstacle_data_end);
  DCHECK_EQ(obstacle_mask_ptr, populate_obstacle_mask_end);
  return inputs_index - start_input_tensor_index;
}

int VectornetFeatureExtractor::PopulateTrafficLightFeatureAndMaskTensors(
    const std::vector<const Agent*>& target_agents,
    const std::optional<std::map<int64_t, LocalCoordinates>>&
        nullable_local_coordinates,
    int inputs_index, std::vector<neural_net::Tensor>& inputs) const {
  DCHECK_LE(inputs_index + 2, inputs.size());
  if (nullable_local_coordinates.has_value()) {
    DCHECK_EQ(target_agents.size(), nullable_local_coordinates->size())
        << "Each target agent should corresponds to one local_coordinates";
  }
  const int start_input_tensor_index = inputs_index;
  const auto [traffic_light_data_begin, populate_traffic_light_data_end] =
      vectornet_util::GetTensorBeginAndEnd<float>(&inputs[inputs_index++], 0,
                                                  target_agents.size());
  const auto [traffic_light_mask_begin, populate_traffic_light_mask_end] =
      vectornet_util::GetTensorBeginAndEnd<bool>(&inputs[inputs_index++], 0,
                                                 target_agents.size());

  float* traffic_light_data_ptr = traffic_light_data_begin;
  bool* traffic_light_mask_ptr = traffic_light_mask_begin;

  const int num_target_agents = target_agents.size();
  for (int i = 0; i < num_target_agents; ++i) {
    const Agent& target_agent = *target_agents[i];
    const int64_t target_agent_id = target_agent.object_id();
    const LocalCoordinates local_coordinates =
        nullable_local_coordinates.has_value()
            ? gtl::FindOrDie(*nullable_local_coordinates, target_agent_id)
            : LocalCoordinates(target_agent.tracked_object());
    std::vector<neural_net::Tensor> traffic_light_tensors =
        GetTrafficLightTensors(target_agent, local_coordinates,
                               config_.max_num_traffic_lights());
    memcpy(traffic_light_data_ptr, traffic_light_tensors[0].data(),
           traffic_light_tensors[0].num_bytes());
    memcpy(traffic_light_mask_ptr, traffic_light_tensors[1].data(),
           traffic_light_tensors[1].num_bytes());
    const auto size_traffic_light_i_data =
        vectornet_util::GetTensorSize(traffic_light_tensors[0]);
    const auto size_traffic_light_i_mask =
        vectornet_util::GetTensorSize(traffic_light_tensors[1]);
    traffic_light_data_ptr += size_traffic_light_i_data;
    traffic_light_mask_ptr += size_traffic_light_i_mask;
  }
  DCHECK_EQ(traffic_light_data_ptr, populate_traffic_light_data_end);
  DCHECK_EQ(traffic_light_mask_ptr, populate_traffic_light_mask_end);
  return inputs_index - start_input_tensor_index;
}

void VectornetFeatureExtractor::CopyAndMaskFeaturesForIgnoreEgoInputs(
    const std::vector<const Agent*>& target_agents, int batch_size,
    const std::vector<int>& target_agent_idx_to_ignore_ego,
    std::vector<const Agent*>& batch_target_agents,
    std::vector<bool>& batch_is_ignoring_ego_mask,
    std::vector<neural_net::Tensor>& inputs) const {
  DCHECK_EQ(num_input_tensors_, inputs.size())
      << "Inputs tensor must contain and only contain agent, agent_mask, "
         "map_object, map_object_mask tensors and other skeleton/route "
         "features"
         " and mask(if the other features are enabled.).";
  DCHECK_LE(batch_size, batch_target_agents.capacity());
  DCHECK_EQ(target_agents.size(), batch_target_agents.size());
  DCHECK_LE(target_agents.size(), batch_size);
  DCHECK_LE(batch_size, batch_is_ignoring_ego_mask.capacity());
  DCHECK_EQ(target_agents.size(), batch_is_ignoring_ego_mask.size());
  // TODO(renhao): Refactor the definition method between features and the
  // indices of the input tensor.
  const int route_mask_index = enable_route_feature_extraction_
                                   ? (enable_skeleton_feature_extraction_
                                          ? kRouteMaskIndexWithSkeletonEnabled
                                          : kRouteMaskIndex)
                                   : kInvalidInputTensorIndex;
  const int num_input_tensors = inputs.size();
  // NOTE(renhao): Use bytes as the data processing unit to handle different
  // data types of the input tensors.
  for (int input_index = 0; input_index < num_input_tensors; ++input_index) {
    auto& input_feature = inputs[input_index];
    DCHECK_GT(input_feature.num_dims(), 1);
    DCHECK_EQ(batch_size, input_feature.dim_size(0));

    const std::vector<int64_t>& feature_dims_size = input_feature.dim_sizes();
    const int64_t single_sample_feature_bytes =
        input_feature.num_bytes() / batch_size;
    const int64_t data_type_bytes =
        neural_net::DataTypeSize(input_feature.data_type());

    std::byte* const data_begin = static_cast<std::byte*>(input_feature.data());
    std::byte* const data_end = data_begin + input_feature.num_bytes();

    std::byte* ignore_ego_feature_data_ptr =
        data_begin + single_sample_feature_bytes * target_agents.size();

    for (auto ignore_ego_agent_index : target_agent_idx_to_ignore_ego) {
      DCHECK_LT(ignore_ego_agent_index, target_agents.size());
      if (input_index == 0) {
        batch_target_agents.push_back(target_agents[ignore_ego_agent_index]);
        batch_is_ignoring_ego_mask.push_back(true);
      }
      // Finds out the orignal data of the certain target agent, and copies
      // for its ignoring ego input tensor.
      const std::byte* const agent_original_data_begin =
          data_begin + single_sample_feature_bytes * ignore_ego_agent_index;
      std::memcpy(ignore_ego_feature_data_ptr, agent_original_data_begin,
                  single_sample_feature_bytes);
      // Masks ego agent feature for ignoring ego prediction.
      if (input_index == kAgentMaskIndex) {
        DCHECK_EQ(feature_dims_size.size(), 3);
        const int64_t single_background_agent_mask_bytes =
            feature_dims_size[2] * data_type_bytes;
        std::byte* const ego_mask_bytes_begin =
            ignore_ego_feature_data_ptr +
            single_background_agent_mask_bytes * kEgoAgentIndexInMarginalModel;
        std::memset(ego_mask_bytes_begin, 0,
                    single_background_agent_mask_bytes);
      }
      // Masks route feature for ignoring ego prediction.
      if (input_index == route_mask_index) {
        std::memset(ignore_ego_feature_data_ptr, 0,
                    single_sample_feature_bytes);
      }
      ignore_ego_feature_data_ptr += single_sample_feature_bytes;
    }
    DCHECK_EQ(ignore_ego_feature_data_ptr, data_end);
  }
}

TrafficLightFeature GetTrafficLightFeatureForOneMapObject(
    const MapObjectKey& key, const SceneContext& scene_context,
    bool extract_accurate_traffic_light_feature,
    bool set_occluded_traffic_light_color) {
  static const std::unordered_set<pb::MapObject::MapObjectType>
      kMapObjectHasTrafficLight{pb::MapObject::LANE, pb::MapObject::CROSSWALK};
  // Convert the search pair for special map object types.
  pb::MapObject::MapObjectType map_object_type;
  int map_object_id;
  switch (key.map_object_type) {
    case pb::MapObject::CROSSWALK_CENTERLINE: {
      map_object_type = pb::MapObject::CROSSWALK;
      map_object_id = key.map_object_id;
    } break;
    // Use lane traffic light for stop lines and watch lines.
    case pb::MapObject::LANE_TRAFFIC_SIGNAL_STOP_LINE: {
      map_object_type = pb::MapObject::LANE;
      map_object_id = key.map_object_id_for_traffic_light;
    } break;
    case pb::MapObject::LANE_WATCH_LINE: {
      map_object_type = pb::MapObject::LANE;
      map_object_id = key.map_object_id_for_traffic_light;
    } break;
    default:
      map_object_type = key.map_object_type;
      map_object_id = key.map_object_id;
  }
  const auto map_object_key = std::make_pair(map_object_type, map_object_id);

  TrafficLightFeature tl_feature;

  // Set occluded traffic light to red for onboard post-processing purpose.
  const auto cur_iter =
      scene_context.map_obj_to_tl_state().find(map_object_key);
  if (set_occluded_traffic_light_color &&
      cur_iter != scene_context.map_obj_to_tl_state().end() &&
      cur_iter->second.is_current_enter_road_direction_unknown_occluded) {
    tl_feature.current_color = voy::TrafficLight::RED;
    tl_feature.associate_lane_type = cur_iter->second.associate_lane_type;
    return tl_feature;
  }

  // Return dummy feature if the traffic light is not found or may be
  // inaccurate.
  if (cur_iter == scene_context.map_obj_to_tl_state().end() ||
      (extract_accurate_traffic_light_feature &&
       (cur_iter->second.is_detection_suspicious ||
        cur_iter->second.is_reasoning_result))) {
    return tl_feature;
  }

  // Current snapshot feature.
  DCHECK(cur_iter != scene_context.map_obj_to_tl_state().end());
  const TrafficLightState& state = cur_iter->second;
  tl_feature.current_color =
      kMapObjectHasTrafficLight.count(map_object_type) > 0
          ? state.color
          : voy::TrafficLight::UNKNOWN_COLOR;
  tl_feature.countdown_number = state.countdown_number;
  tl_feature.is_flashing = state.is_flashing;
  tl_feature.detection_score = state.detection_score;
  tl_feature.is_detection_suspicious = state.is_detection_suspicious;
  tl_feature.is_reasoning_result = state.is_reasoning_result;
  tl_feature.associate_lane_type = state.associate_lane_type;

  // History feature.
  DCHECK_EQ(tl_feature.history_colors.size(),
            constants::kTrafficLightHistoryItemSizeForMLPlanner);
  DCHECK_EQ(scene_context.map_obj_to_tl_state_history().size(),
            constants::kTrafficLightHistoryItemSize);
  for (size_t i = 0; i < constants::kTrafficLightHistoryItemSizeForMLPlanner;
       ++i) {
    const auto& map_obj_to_tl_state =
        scene_context.map_obj_to_tl_state_history()
            [i * constants::kTrafficLightHistorySampleStep];
    const auto history_iter = map_obj_to_tl_state.find(map_object_key);
    if (history_iter != map_obj_to_tl_state.end() &&
        kMapObjectHasTrafficLight.count(map_object_type) > 0) {
      tl_feature.history_colors[i] = history_iter->second.color;
      tl_feature.history_is_flashings[i] = history_iter->second.is_flashing;
    }
  }

  return tl_feature;
}

}  // namespace prediction
