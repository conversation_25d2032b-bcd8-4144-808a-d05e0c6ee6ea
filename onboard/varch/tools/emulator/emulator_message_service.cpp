#include "varch/tools/emulator/emulator_message_service.h"

#include <vector>

#include "varch/base/date.h"
#include "varch/base/proto.h"
#include "varch/init.h"
#include "varch/tools/emulator/simulator_mode.h"
#include "varch/tools/launcher/vgraph_controller.h"
#include "varch/vnode/detail/deterministic/activity_service.h"
#include "varch/vnode/detail/publisher/recorder_publisher_intra.h"
#include "varch/vnode/detail/replay_test/replay_test_service.h"

namespace varch {
namespace tools {
namespace emulator {
namespace {
using SubscriberConfig = varch::vnode::detail::SubscriberConfig;
using VGraphController = varch::tools::launcher::VGraphController;
using RecorderPublisherIntra =
    varch::vnode::detail::publisher::RecorderPublisherIntra;
using PublisherOption = varch::vnode::detail::publisher::PublisherOption;
constexpr int kSmallTimeIntervalMs = 100;
constexpr int kAuxilliaryMessageQueueSize = 100;
}  // namespace

const std::unique_ptr<VNodeCore> &EmulatorMessageService::GetVNodeCore() {
  CHECK(vnode_core_.has_value());
  return vnode_core_.value().get();
}

std::vector<PeriodicTriggerInfo>
    &EmulatorMessageService::GetPeriodicTriggerInfos() {
  return periodic_trigger_infos_;
}

void EmulatorMessageService::InitVNodeCore() {
  vnode_core_ = VNodeCore::GetInstance();
}

EmulatorMessageService::EmulatorMessageService(SimulatorMode simulator_mode)
    : simulator_mode_(simulator_mode) {
  InitVNodeCore();
  SetUpAuxiliaryPublishers();
  vgraph_controller_ = std::make_unique<VGraphController>();
}

EmulatorMessageService::~EmulatorMessageService() {
  for (auto &[key, value] : publishers_map_) {
    if (value) {
      value->ShutDown();
      value.reset();
    }
  }

  if (vnode_play_time_sub_) {
    vnode_play_time_sub_->ShutDown();
    vnode_play_time_sub_.reset();
  }

  if (callback_queue_feedback_sub_) {
    callback_queue_feedback_sub_->ShutDown();
    callback_queue_feedback_sub_.reset();
  }

  if (play_time_pub_) {
    play_time_pub_->ShutDown();
    play_time_pub_.reset();
  }

  if (vnode_time_ack_pub_) {
    vnode_time_ack_pub_->ShutDown();
    vnode_time_ack_pub_.reset();
  }

  if (message_timeout_ack_pub_) {
    message_timeout_ack_pub_->ShutDown();
    message_timeout_ack_pub_.reset();
  }

  if (player_waiting_pub_) {
    player_waiting_pub_->ShutDown();
    player_waiting_pub_.reset();
  }

  if (timeout_message_sub_) {
    timeout_message_sub_->ShutDown();
    timeout_message_sub_.reset();
  }

  if (end_of_simulation_sub_) {
    end_of_simulation_sub_->ShutDown();
    end_of_simulation_sub_.reset();
  }

  if (potential_msg_missing_sub_) {
    potential_msg_missing_sub_->ShutDown();
    potential_msg_missing_sub_.reset();
  }

  vgraph_controller_.reset();
}

void EmulatorMessageService::RegisterPeriodicTriggersFromVGraphs(
    const std::vector<std::string> &vgraphs) {
  for (auto &vgraph : vgraphs) {
    RegisterPeriodicTriggersFromVGraph(vgraph);
  }
}

void EmulatorMessageService::RegisterPeriodicTriggersFromVGraph(
    const std::string &vgraph) {
  protos::vnode::VForestConfig vgraph_config;
  if (!base::ReadTextProtoFile(vgraph, &vgraph_config)) {
    LOG(FATAL) << "[Main] Get proto failed, file: " << vgraph;
  }

  for (const auto &vnode_conf : vgraph_config.vgraph().vnode()) {
    const std::string &clazz = vnode_conf.clazz();

    if (vnode::GetVArchGlobalData()->HdMapClasses().count(clazz) &&
        vnode::GetVArchGlobalData()->RegisteredHdMapPeriodicTriggers().count(
            clazz)) {
      continue;
    } else if (vnode::GetVArchGlobalData()->HdMapClasses().count(clazz)) {
      vnode::GetVArchGlobalData()->RegisteredHdMapPeriodicTriggers().insert(
          clazz);
    }

    if (vnode_conf.sub().mode() == protos::vnode::kPeriodic &&
        vnode_conf.sub().periodic_interval() > 0) {
      std::string topic_name = "/periodic_trigger/" + vnode_conf.name();
      auto time_interval = static_cast<int64_t>(base::MilliSecToNanoSec(
          static_cast<int64_t>(vnode_conf.sub().periodic_interval())));
      PeriodicTriggerInfo per_info{topic_name, time_interval, 0};
      periodic_trigger_infos_.emplace_back(per_info);
      if (topics_.count(topic_name)) {
        LOG(FATAL) << "[Emulator][Periodic Trigger Registry] "
                      "duplicate topic name found: "
                   << topic_name;
      }
      topics_.insert(topic_name);
    }

    if (!(vnode_conf.has_skip_acyclicity_check_in_simulation() &&
          vnode_conf.skip_acyclicity_check_in_simulation())) {
      for (const auto &pub : vnode_conf.pub()) {
        vnode::GetVArchGlobalData()->GetComputationalGraph()->UpdateGraph(
            vnode_conf.name(), pub.topic(), /*is_pub*/ true,
            /*is_trigger_topic*/ false);
      }

      int num_triggers = 0;
      if ((vnode_conf.sub().mode() != protos::vnode::kPeriodic) &&
          (vnode_conf.sub().mode() != protos::vnode::kCallOnce)) {
        num_triggers = vnode_conf.sub().trigger_num();
      }

      if (vnode_conf.sub().mode() == protos::vnode::kOneTrigger) {
        num_triggers = 1;
      }

      for (int i = 0; i < vnode_conf.sub().topic_size(); ++i) {
        vnode::GetVArchGlobalData()->GetComputationalGraph()->UpdateGraph(
            vnode_conf.name(), vnode_conf.sub().topic()[i].topic(),
            /*is_pub*/ false,
            /*is_trigger_topic*/ (i < num_triggers) ? true : false);
      }
    }
  }
}

void EmulatorMessageService::RegisterPeriodicTriggerTopic(
    const std::string &topic_name, int64_t time_interval_ns,
    int64_t start_timepoint_ns) {
  PeriodicTriggerInfo per_info{topic_name, time_interval_ns,
                               start_timepoint_ns - time_interval_ns};
  periodic_trigger_infos_.emplace_back(per_info);
  if (topics_.count(topic_name)) {
    LOG(FATAL) << "[Emulator][Periodic Trigger Registry] "
                  "duplicate topic name found: "
               << topic_name;
  }
  topics_.insert(topic_name);
}

void EmulatorMessageService::RegisterEmulatorTopics(
    const std::vector<std::string> &topics) {
  for (auto const &topic : topics) {
    topics_.insert(topic);
  }
}

void EmulatorMessageService::RegisterEmulatorTopic(
    const std::string &topic_name) {
  if (topics_.count(topic_name)) {
    return;
  } else {
    topics_.insert(topic_name);
  }
}

void EmulatorMessageService::SetUpEmulatorPublisher(
    const std::string &topic, const std::string &msg_type) {
  auto pub_iter = publishers_map_.find(topic);
  if (pub_iter == publishers_map_.end()) {
    auto pub = GetVNodeCore()->CreateRecorderPublisher<RecorderPublisherIntra>(
        topic, msg_type, TransportType::kIntraProcess);
    publishers_map_.emplace(topic, pub);
    pub_count_map_.emplace(topic, 0);
  }
}

void EmulatorMessageService::SetUpAuxiliaryPublishers() {
  // (minghao): initialize the publisher for sending the latest play time of the
  // dataplayer to all the other vnodes
  play_time_pub_ = GetVNodeCore()->CreatePublisher<PlayTimeDTO>(PublisherOption(
      "/simulation/data_player_play_time", TransportType::kIntraProcess));

  vnode_time_ack_pub_ =
      GetVNodeCore()->CreatePublisher<PlayTimeDTO>(PublisherOption(
          "/simulation/vnode_play_time_ack", TransportType::kIntraProcess));

  player_waiting_pub_ =
      GetVNodeCore()->CreatePublisher<PlayTimeDTO>(PublisherOption(
          "/simulation/data_player_waiting", TransportType::kIntraProcess));

  message_timeout_ack_pub_ =
      GetVNodeCore()->CreatePublisher<PlayTimeDTO>(PublisherOption(
          "/simulation/message_timeout_ack", TransportType::kIntraProcess));
}

void EmulatorMessageService::SetUpAuxiliarySubscribers(
    const std::function<void(std::shared_ptr<const PlayTimeDTO>,
                             std::shared_ptr<const MessageMetaInfo>)>
        &on_vnode_play_time_received,
    const std::function<void(std::shared_ptr<const FeedbackDTO>,
                             std::shared_ptr<const MessageMetaInfo>)>
        &on_callback_queue_feedback_received,
    const std::function<void(std::shared_ptr<const PlayTimeDTO>,
                             std::shared_ptr<const MessageMetaInfo>)>
        &on_end_of_simulation_received,
    const std::function<void(std::shared_ptr<const TimeoutMessageDTO>,
                             std::shared_ptr<const MessageMetaInfo>)>
        &on_timeout_message_received,
    const std::function<
        void(std::shared_ptr<const AlignedModeMissingMessageQuery>,
             std::shared_ptr<const MessageMetaInfo>)>
        &on_potential_msg_missing_query_received) {
  // (minghao): initialize the subscriber for receiving the emulation clock
  // time of other vnodes
  SubscriberConfig vnode_play_time_config{"/simulation/vnode_play_time",
                                          TransportType::kIntraProcess,
                                          kAuxilliaryMessageQueueSize};

  vnode_play_time_sub_ = GetVNodeCore()->CreateSubscriber<PlayTimeDTO>(
      vnode_play_time_config, on_vnode_play_time_received);

  // (minghao): initialize the subscriber for receiving notifications from the
  // callback queue (which may lead an advance in the play time of dataplayer)
  SubscriberConfig callback_queue_feedback_config{
      "/simulation/callback_queue_feedback", TransportType::kIntraProcess, 1};
  callback_queue_feedback_sub_ = GetVNodeCore()->CreateSubscriber<FeedbackDTO>(
      callback_queue_feedback_config, on_callback_queue_feedback_received);

  SubscriberConfig timeout_message_sub_config{
      "/simulation/varch_timeout_message", TransportType::kIntraProcess,
      kAuxilliaryMessageQueueSize};
  timeout_message_sub_ = GetVNodeCore()->CreateSubscriber<TimeoutMessageDTO>(
      timeout_message_sub_config, on_timeout_message_received);

  if (simulator_mode_ == kVirtualSim) {
    SubscriberConfig end_of_simulation_config{"/simulation/end_of_simulation",
                                              TransportType::kIntraProcess, 1};
    end_of_simulation_sub_ = GetVNodeCore()->CreateSubscriber<PlayTimeDTO>(
        end_of_simulation_config, on_end_of_simulation_received);
  }

  SubscriberConfig potential_msg_missing_query_sub_config{
      "/simulation/emulator/potential_missing_message_info_query",
      TransportType::kIntraProcess, kAuxilliaryMessageQueueSize};

  potential_msg_missing_sub_ =
      GetVNodeCore()->CreateSubscriber<AlignedModeMissingMessageQuery>(
          potential_msg_missing_query_sub_config,
          on_potential_msg_missing_query_received);

  // (minghao): wait for the subscriber to be completely initialized
  std::this_thread::sleep_for(std::chrono::milliseconds(kSmallTimeIntervalMs));
}

void EmulatorMessageService::PublishVNodeTimeAckMsg(
    const int64_t &publish_time_ns) {
  PlayTimeDTO vnode_time_ack_msg;
  vnode_time_ack_pub_->PublishSysMsg(vnode_time_ack_msg, publish_time_ns);
}

void EmulatorMessageService::PublishMessageTimeoutAckMsg(
    const int64_t &publish_time_ns) {
  PlayTimeDTO message_timeout_ack_msg;
  message_timeout_ack_pub_->PublishSysMsg(message_timeout_ack_msg,
                                          publish_time_ns);
}

void EmulatorMessageService::PublishPlayerWaitingMsg(
    const int64_t &publish_time_ns) {
  PlayTimeDTO player_waiting_msg;
  player_waiting_pub_->PublishSysMsg(player_waiting_msg, publish_time_ns);
}

void EmulatorMessageService::PublishEmulatorTimeToVNodes(
    const int64_t &emulator_time_ns) {
  PlayTimeDTO play_time_dto;
  play_time_dto.set_play_time(emulator_time_ns);
  play_time_pub_->PublishSysMsg(play_time_dto, emulator_time_ns);
}

void EmulatorMessageService::PublishSerializedMessage(
    const std::string &topic, SerializedDataWrapper &serialized_msg,
    const int64_t &record_time_ns) {
  VLOG_INFO << "[EmulatorMessageService::PublishSerializedMessage] Publish "
            << topic << " at " << record_time_ns;

  publishers_map_.find(topic)->second->template Publish<RawMessage>(
      serialized_msg.raw_bytes, record_time_ns);
  pub_count_map_.at(topic)++;
}

std::unique_ptr<VGraphController>
    &EmulatorMessageService::GetVGraphController() {
  CHECK(vgraph_controller_ != nullptr);
  return vgraph_controller_;
}

bool EmulatorMessageService::IsTopicRegistered(const std::string &topic_name) {
  return topics_.count(topic_name) > 0;
}

}  // namespace emulator
}  // namespace tools
}  // namespace varch
