#include "planner/path/reasoning/agent_intention/semantic_context/drivable_space_corridor_generator/drivable_space_corridor_generator.h"

#include <gtest/gtest.h>
#include <map>
#include <memory>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "log_utils/enum_utils.h"
#include "math/range.h"
#include "planner/path/reasoning/agent_intention/drivable_lane_corridor_generator/drivable_lane_corridor_generator_utility.h"
#include "planner/path/reasoning/agent_intention/semantic_context/test/test_utility.h"
#include "planner/speed/test_util/test_util.h"
#include "planner/utility/object_id/typed_object_id.h"
#include "voy_protos/trajectory.pb.h"

namespace planner {
namespace path {

namespace {

// Debug string for nudge segment infos.
std::string ShowNudgeSegment(
    const nudge_corridor::TaggedNudgeSegment& segment) {
  const auto show_range = [](const std::string& name,
                             const math::Range1d& range) {
    std::string str = name + "[";
    str += strings::StringPrintf("%.3lf", range.start_pos) + ",";
    str += strings::StringPrintf("%.3lf", range.end_pos) + "]";
    return str;
  };
  std::string str;
  str += "boundary level: " +
         std::string(log_utils::enum_to_string(segment.val.type)) +
         " boundary_violation_buffer: " +
         strings::StringPrintf("%.3lf", segment.val.violation_buffer);
  str += show_range(", lane_boundary_range: ", segment.range);
  str += ", side: " +
         std::to_string(static_cast<std::underlying_type<math::pb::Side>::type>(
             segment.tag.inlane_corridor_side));
  str += ", type: " +
         std::to_string(
             static_cast<
                 std::underlying_type<nudge_corridor::NudgeSegmentType>::type>(
                 segment.tag.type));
  str += show_range(", nominal_path_range: ", segment.tag.nominal_path_range);
  return str;
}

// Debug string for tagged nduge segement infos.
std::string ShowTaggedNudgeSegments(
    const std::string& name,
    const std::vector<nudge_corridor::TaggedNudgeSegment>&
        inlane_nudge_segments) {
  std::string str = name + "\n";
  for (const auto& segment : inlane_nudge_segments) {
    str += ShowNudgeSegment(segment);
    str += "\n";
  }
  return str;
}

// Add the lane boundary element.
void AddLaneBoundaryElement(const math::geometry::PolylineCurve2d& line_curve,
                            path::BoundaryType boundary_type,
                            std::vector<BoundaryElement>* boundary_elements) {
  boundary_elements->push_back(
      GetLaneBoundaryElement(line_curve, boundary_type,
                             /*required_lat_gap=*/std::nullopt,
                             /*max_violation=*/std::nullopt,
                             /*deviation_to_reference=*/std::nullopt,
                             /*min_adjusted_buffer=*/std::nullopt,
                             /*lane_marking_arc_length_range=*/
                             math::Range1d(line_curve.GetStartPoint().x(),
                                           line_curve.GetEndPoint().x())));
}

class DrivableSpaceCorridorGeneratorTest : public ::testing::Test {
 protected:
  DrivableSpaceCorridorGeneratorTest() : generator_(pb::LANE_KEEP) {}
  void SetUp() override {
    lane_sequence_geometry_.nominal_path =
        planner::test::ReadLine("LINESTRING(0.0 0.0,10.0 0.0)");
    lane_sequence_geometry_.left_lane_boundary =
        planner::test::ReadLine("LINESTRING(0.0 3.0,10.0 3.0)");
    lane_sequence_geometry_.right_lane_boundary =
        planner::test::ReadLine("LINESTRING(0.0 -3.0,10.0 -3.0)");
    lane_sequence_geometry_.left_hard_boundary_lines = {
        planner::test::ReadLine("LINESTRING(0.0 1.0,3.0 1.0)"),
        planner::test::ReadLine("LINESTRING(5.0 2.0,8.0 2.0)")};
    lane_sequence_geometry_.right_hard_boundary_lines = {
        planner::test::ReadLine("LINESTRING(1.0 -1.0,4.0 -1.0)"),
        planner::test::ReadLine("LINESTRING(6.0 -2.0,9.0 -2.0)")};
    lane_sequence_geometry_.lane_sequence_contour =
        lane_selection::GetLaneBoundaryBorder(
            lane_sequence_geometry_.left_lane_boundary,
            lane_sequence_geometry_.right_lane_boundary,
            lane_sequence_geometry_.nominal_path);

    planning_horizon_range_ = {
        lane_sequence_geometry_.nominal_path.GetStartPoint().x(),
        lane_sequence_geometry_.nominal_path.GetEndPoint().x()};

    ego_param_ = planner::test::GetEgoInLaneParams();
  }

  EgoInLaneParams ego_param_;
  lane_selection::LaneSequenceGeometry lane_sequence_geometry_;
  math::Range1d planning_horizon_range_;

  AgentInLaneStatesMap agent_state_map_;
  std::vector<ConstructionZoneInLaneState> construction_zones_state_;

  std::map<TypedObjectId, ObjectReasoningInfo> object_reasoning_info_map_;

  math::geometry::MaximumPossibleOffset max_lane_boundary_offset_ = {
      std::numeric_limits<double>::max(), std::numeric_limits<double>::max()};

  LaneMarkingBufferInfo lane_marking_buffer_info_ = {
      LaneMarkingBufferConfig(/*virtual_soft_lane_buffer=*/1.5,
                              /*virtual_hard_lane_buffer=*/1.0),
      LaneMarkingBufferConfig(/*virtual_soft_lane_buffer=*/1.5,
                              /*virtual_hard_lane_buffer=*/1.0)};

  DrivableSpaceCorridorGenerator generator_;
};

}  // namespace

/*
------------------------------------------------- left boundary 3.0m
     _________
    |_________| -- static agent 1   _____
                 _____             /    /
~~~~~~~~~~~~~~~ /_____/~~~~~~~~~~~/    /~~~~~~~~~~~   reference_line 0.0m
                dynamic 2        /    / cz 1
--------------------------------/    /----------right boundary -3.0m
                               /____/
*/
// we will extend lane marking with the inlane nudge objects
TEST_F(DrivableSpaceCorridorGeneratorTest, ObjectInLaneNudgeSegmentsTest) {
  agent_state_map_.clear();
  construction_zones_state_.clear();

  planner::test::ConstructStaticAgentInLaneState(
      lane_sequence_geometry_, /*obj_id=*/1,
      /*obj_polygon=*/
      math::geometry::Polygon2d({math::geometry::Point2d(2.0, 1.5),
                                 math::geometry::Point2d(4.0, 1.5),
                                 math::geometry::Point2d(4.0, 2.5),
                                 math::geometry::Point2d(2.0, 2.5)}),
      agent_state_map_);
  planner::test::ConstructDynamicAgentInLaneState(
      lane_sequence_geometry_, /*obj_id=*/2,
      /*x0=*/4.0, /*y0=*/0.0,
      /*width=*/4.0, /*speed=*/1.0, agent_state_map_);
  planner::test::ConstructConstructionZoneInLaneState(
      ego_param_, lane_sequence_geometry_, /*cz_id=*/1,
      /*cz_polygon=*/
      math::geometry::Polygon2d({math::geometry::Point2d(6.0, -4.0),
                                 math::geometry::Point2d(8.0, -4.0),
                                 math::geometry::Point2d(8.0, 2.0),
                                 math::geometry::Point2d(6.0, 2.0)}),
      &construction_zones_state_);

  object_reasoning_info_map_ = test::GenerateObjectReasoningInfoMap(
      agent_state_map_, construction_zones_state_,
      lane_sequence_geometry_.nominal_path);

  InLaneNudgeObjectsInfo inlane_nudge_objects_info;
  inlane_nudge_objects_info.pass_left_object_ids.push_back(
      TypedObjectId(/*object_id=*/1,
                    /*object_type=*/pb::ObjectSourceType::kConstructionZone));

  inlane_nudge_objects_info.pass_right_object_ids.push_back(TypedObjectId(
      /*object_id=*/1, /*object_type=*/pb::ObjectSourceType::kTrackedObject));
  inlane_nudge_objects_info.pass_right_object_ids.push_back(TypedObjectId(
      /*object_id=*/2, /*object_type=*/pb::ObjectSourceType::kTrackedObject));

  std::vector<nudge_corridor::TaggedNudgeSegment> left_static_nudge_segments;
  std::vector<nudge_corridor::TaggedNudgeSegment> left_dynamic_nudge_segments;
  std::vector<nudge_corridor::TaggedNudgeSegment> right_static_nudge_segments;
  std::vector<nudge_corridor::TaggedNudgeSegment> right_dynamic_nudge_segments;
  generator_.GetObjectInLaneNudgeSegmentsOneSide(
      /*robot_snapshot=*/planner::test::GenerateRobotStateSnapshot(
          /*front_pose=*/math::geometry::Point2d{0.1, 0.0}),
      lane_sequence_geometry_, planning_horizon_range_,
      object_reasoning_info_map_,
      inlane_nudge_objects_info.pass_left_object_ids,
      max_lane_boundary_offset_.left_distance, /*is_left_side=*/true,
      &left_static_nudge_segments, &left_dynamic_nudge_segments);
  generator_.GetObjectInLaneNudgeSegmentsOneSide(
      /*robot_snapshot=*/planner::test::GenerateRobotStateSnapshot(
          /*front_pose=*/math::geometry::Point2d{0.1, 0.0}),
      lane_sequence_geometry_, planning_horizon_range_,
      object_reasoning_info_map_,
      inlane_nudge_objects_info.pass_right_object_ids,
      max_lane_boundary_offset_.right_distance, /*is_left_side=*/false,
      &right_static_nudge_segments, &right_dynamic_nudge_segments);
  std::vector<nudge_corridor::TaggedNudgeSegment> left_nudge_segments =
      left_static_nudge_segments;
  left_nudge_segments.insert(left_nudge_segments.end(),
                             left_dynamic_nudge_segments.begin(),
                             left_dynamic_nudge_segments.end());
  std::vector<nudge_corridor::TaggedNudgeSegment> right_nudge_segments =
      right_static_nudge_segments;
  right_nudge_segments.insert(right_nudge_segments.end(),
                              right_dynamic_nudge_segments.begin(),
                              right_dynamic_nudge_segments.end());
  auto result = generator_.GenerateInLaneCorridorFromNudgeSegments(
      lane_sequence_geometry_, left_nudge_segments, right_nudge_segments);

  LOG(ERROR) << ShowTaggedNudgeSegments("left_nudge_segments: ",
                                        left_nudge_segments);
  LOG(ERROR) << ShowTaggedNudgeSegments("right_inlane_nudge_segments: ",
                                        right_nudge_segments);
  LOG(ERROR) << "left_boundary: " << result.left_boundary;
  LOG(ERROR) << "right_boundary: " << result.right_boundary;

  // Has soft blocking dynamic agent and construction_zone in left/right side,
  // respectively. lat boundary + buffer(2 + comfort_req_lat_gap + 1.864)
  // lon buffer = arc_length -+ ego_length = (6 - 2 * 4.9536)
  test::CheckBoundaryLine(
      result.left_boundary,
      planner::test::ReadLine(
          "LINESTRING(0 3.864, 10 3.864)"));  // construction_zone
  test::CheckBoundaryLine(
      result.right_boundary,
      planner::test::ReadLine(
          "LINESTRING(0 -3.864,10 -3.864)"));  // dynamic agent

  // Add test for object_reasoning_info_map built from object occupancy states
  ObjectOccupancyStateMap object_occupancy_state_map;
  // Construct a static object occupancy state.
  prediction::pb::Agent static_agent_proto;
  auto& static_tracked_object = *static_agent_proto.mutable_tracked_object();
  static_tracked_object.set_id(1);
  static_tracked_object.add_attributes(voy::perception::HAS_EXACT_SHAPE);
  math::geometry::Convert(/*obj_polygon=*/
                          math::geometry::Polygon2d(
                              {math::geometry::Point2d(2.0, 1.5),
                               math::geometry::Point2d(4.0, 1.5),
                               math::geometry::Point2d(4.0, 2.5),
                               math::geometry::Point2d(2.0, 2.5)}),
                          *static_tracked_object.mutable_contour());
  PlannerObject static_planner_object(
      static_agent_proto, /*timestamp=*/1,
      TrafficParticipantPose(/*timestamp=*/1, static_tracked_object));
  static_planner_object.set_is_primary_stationary(true);
  prediction::pb::PredictedTrajectory static_predicted_trajectory;
  static_predicted_trajectory.set_id(1);
  static_predicted_trajectory.add_traj_poses();
  std::vector<PredictedTrajectoryWrapper> predicted_trajectory_wrappers_1;
  predicted_trajectory_wrappers_1.emplace_back(voy::TrackedObject(),
                                               static_predicted_trajectory,
                                               /*is_primary_trajectory=*/true);
  ObjectOccupancyState static_object_state = GenerateObjectOccupancyState(
      static_planner_object, /*object_information=*/{},
      predicted_trajectory_wrappers_1, lane_sequence_geometry_.nominal_path,
      lane_sequence_geometry_.left_lane_boundary,
      lane_sequence_geometry_.right_lane_boundary, ego_param_,
      pb::MotionMode::FORWARD);
  // Construct a dynamic object occupancy state.
  prediction::pb::Agent dynamic_tracked_agent_proto;
  dynamic_tracked_agent_proto.mutable_tracked_object()->CopyFrom(
      planner::test::ConstructTrackedObjectFromDimension(
          /*object_id=*/2, /*x0=*/4.0,
          /*y0=*/0.0, /*width=*/4.0, /*length=*/2.0, /*height=*/2.0,
          /*velocity=*/1.0, /*heading=*/0.0));

  prediction::pb::PredictedTrajectory proto_traj;
  std::vector<PredictedTrajectoryWrapper> predicted_trajectory_wrappers_2;
  predicted_trajectory_wrappers_2.push_back(
      planner::test::ConstructPredictedTrajectoryWrapperForTest(
          dynamic_tracked_agent_proto.tracked_object(), proto_traj));
  PlannerObject dynamic_planner_object(
      dynamic_tracked_agent_proto, /*timestamp=*/1,
      TrafficParticipantPose(/*timestamp=*/1,
                             dynamic_tracked_agent_proto.tracked_object()));
  ObjectOccupancyState dynamic_object_state = GenerateObjectOccupancyState(
      dynamic_planner_object, /*object_information=*/{},
      predicted_trajectory_wrappers_2, lane_sequence_geometry_.nominal_path,
      lane_sequence_geometry_.left_lane_boundary,
      lane_sequence_geometry_.right_lane_boundary, ego_param_,
      pb::MotionMode::FORWARD);
  object_occupancy_state_map.emplace(
      static_object_state.planner_object().tracked_object().id(),
      std::make_unique<ObjectOccupancyState>(static_object_state));
  object_occupancy_state_map.emplace(
      dynamic_object_state.planner_object().tracked_object().id(),
      std::make_unique<ObjectOccupancyState>(dynamic_object_state));
  object_reasoning_info_map_ =
      test::GenerateObjectReasoningInfoMapFromOccupancyState(
          object_occupancy_state_map, construction_zones_state_,
          lane_sequence_geometry_.nominal_path, ego_param_);
  left_nudge_segments.clear();
  right_nudge_segments.clear();
  left_static_nudge_segments.clear();
  left_dynamic_nudge_segments.clear();
  right_static_nudge_segments.clear();
  right_dynamic_nudge_segments.clear();
  FLAGS_planning_enable_object_occupancy_state = true;
  generator_.GetObjectInLaneNudgeSegmentsOneSide(
      /*robot_snapshot=*/planner::test::GenerateRobotStateSnapshot(
          /*front_pose=*/math::geometry::Point2d{0.1, 0.0}),
      lane_sequence_geometry_, planning_horizon_range_,
      object_reasoning_info_map_,
      inlane_nudge_objects_info.pass_left_object_ids,
      max_lane_boundary_offset_.left_distance, /*is_left_side=*/true,
      &left_static_nudge_segments, &left_dynamic_nudge_segments);
  generator_.GetObjectInLaneNudgeSegmentsOneSide(
      /*robot_snapshot=*/planner::test::GenerateRobotStateSnapshot(
          /*front_pose=*/math::geometry::Point2d{0.1, 0.0}),
      lane_sequence_geometry_, planning_horizon_range_,
      object_reasoning_info_map_,
      inlane_nudge_objects_info.pass_right_object_ids,
      max_lane_boundary_offset_.right_distance, /*is_left_side=*/false,
      &right_static_nudge_segments, &right_dynamic_nudge_segments);
  left_nudge_segments = left_static_nudge_segments;
  left_nudge_segments.insert(left_nudge_segments.end(),
                             left_dynamic_nudge_segments.begin(),
                             left_dynamic_nudge_segments.end());
  right_nudge_segments = right_static_nudge_segments;
  right_nudge_segments.insert(right_nudge_segments.end(),
                              right_dynamic_nudge_segments.begin(),
                              right_dynamic_nudge_segments.end());
  auto result_2 = generator_.GenerateInLaneCorridorFromNudgeSegments(
      lane_sequence_geometry_, left_nudge_segments, right_nudge_segments);

  LOG(ERROR) << ShowTaggedNudgeSegments("left_nudge_segments: ",
                                        left_nudge_segments);
  LOG(ERROR) << ShowTaggedNudgeSegments("right_inlane_nudge_segments: ",
                                        right_nudge_segments);
  LOG(ERROR) << "left_boundary: " << result_2.left_boundary;
  LOG(ERROR) << "right_boundary: " << result_2.right_boundary;

  // Has soft blocking dynamic agent and construction_zone in left/right side,
  // respectively. lat boundary + buffer(2 + comfort_req_lat_gap + 1.864)
  // lon buffer = arc_length -+ ego_length = (6 - 2 * 4.9536)
  test::CheckBoundaryLine(
      result_2.left_boundary,
      planner::test::ReadLine(
          "LINESTRING(0 3.864, 10 3.864)"));  // construction_zone
  test::CheckBoundaryLine(
      result_2.right_boundary,
      planner::test::ReadLine(
          "LINESTRING(0 -3.864,10 -3.864)"));  // dynamic agent
}

/*       _____
        /_____/ ego
-------------------------  left boundary

~~~~~~~~~~~~~~~~~~~~~~~~~  reference_line

-------------------------  right boundary
*/
// We will extend the lane boundary if the ego state is not fully in lane
// boundary.
TEST_F(DrivableSpaceCorridorGeneratorTest, EgoInLaneNudgeSegmentsTest) {
  std::vector<nudge_corridor::TaggedNudgeSegment> left_nudge_segments;
  std::vector<nudge_corridor::TaggedNudgeSegment> right_nudge_segments;
  // Case 1: Out of lane, left = end_lateral_distance + buffer (5.0 + 0.5
  // * 1.864 + 1.0).
  // lon buffer = arc_length - buffer = (3 - 2)
  FLAGS_planning_enable_object_occupancy_state = false;
  generator_.GetEgoNominalPathRangeAndInLaneNudgeSegments(
      /*robot_snapshot=*/planner::test::GenerateRobotStateSnapshot(
          /*front_pose=*/math::geometry::Point2d{3.0, 5.0}),
      lane_sequence_geometry_, planning_horizon_range_,
      /*max_expandable_point=*/std::nullopt, &left_nudge_segments,
      &right_nudge_segments);
  auto result = generator_.GenerateInLaneCorridorFromNudgeSegments(
      lane_sequence_geometry_, left_nudge_segments, right_nudge_segments);

  LOG(ERROR) << ShowTaggedNudgeSegments("left_nudge_segments: ",
                                        left_nudge_segments);
  LOG(ERROR) << ShowTaggedNudgeSegments("right_inlane_nudge_segments: ",
                                        right_nudge_segments);
  LOG(ERROR) << "left_boundary: " << result.left_boundary;
  LOG(ERROR) << "right_boundary: " << result.right_boundary;

  test::CheckBoundaryLine(
      result.left_boundary,
      planner::test::ReadLine("LINESTRING(0 6.932,10 6.932)"));
  EXPECT_TRUE(result.right_boundary.empty());

  // Case 2: intersec with left_boundary, return the end_lateral_distance +
  // buffer (3.0 + 0.5 * 1.864 + 1.0).
  // lon buffer = arc_length - buffer = (3 - 2)
  left_nudge_segments.clear();
  right_nudge_segments.clear();
  generator_.GetEgoNominalPathRangeAndInLaneNudgeSegments(
      /*robot_snapshot=*/planner::test::GenerateRobotStateSnapshot(
          /*front_pose=*/math::geometry::Point2d{3.0, 3.0}),
      lane_sequence_geometry_, planning_horizon_range_,
      /*max_expandable_point=*/std::nullopt, &left_nudge_segments,
      &right_nudge_segments);
  result = generator_.GenerateInLaneCorridorFromNudgeSegments(
      lane_sequence_geometry_, left_nudge_segments, right_nudge_segments);

  LOG(ERROR) << ShowTaggedNudgeSegments("left_nudge_segments: ",
                                        left_nudge_segments);
  LOG(ERROR) << ShowTaggedNudgeSegments("right_inlane_nudge_segments: ",
                                        right_nudge_segments);
  LOG(ERROR) << "left_boundary: " << result.left_boundary;
  LOG(ERROR) << "right_boundary: " << result.right_boundary;

  test::CheckBoundaryLine(
      result.left_boundary,
      planner::test::ReadLine("LINESTRING(0 4.932,10 4.932)"));
  EXPECT_TRUE(result.right_boundary.empty());

  // Case 3: test max_expandable_point for lc.
  left_nudge_segments.clear();
  right_nudge_segments.clear();
  generator_.GetEgoNominalPathRangeAndInLaneNudgeSegments(
      /*robot_snapshot=*/planner::test::GenerateRobotStateSnapshot(
          /*front_pose=*/math::geometry::Point2d{3.0, 3.0}),
      lane_sequence_geometry_, planning_horizon_range_,
      /*max_expandable_point=*/math::geometry::Point2d{5.0, 0.0},
      &left_nudge_segments, &right_nudge_segments);
  result = generator_.GenerateInLaneCorridorFromNudgeSegments(
      lane_sequence_geometry_, left_nudge_segments, right_nudge_segments);

  LOG(ERROR) << ShowTaggedNudgeSegments("left_nudge_segments: ",
                                        left_nudge_segments);
  LOG(ERROR) << ShowTaggedNudgeSegments("right_inlane_nudge_segments: ",
                                        right_nudge_segments);
  LOG(ERROR) << "left_boundary: " << result.left_boundary;
  LOG(ERROR) << "right_boundary: " << result.right_boundary;
  // arc_length + ego_length + buffer= (3.0 + 4.9536 + 2.0)
  test::CheckBoundaryLine(
      result.left_boundary,
      planner::test::ReadLine("LINESTRING(0 4.932,9.9536 4.932)"));
  EXPECT_TRUE(result.right_boundary.empty());
}

/*
_____________------------------   left boundary 3.0m

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~   reference_line 0.0m


-------------________________ right boundary -3.0m
*/
TEST_F(DrivableSpaceCorridorGeneratorTest, BoundaryElementNudgeSegmentsTest) {
  // Case 1: has lane_marking_boundaries.
  LeftRightBoundaryPair lane_marking_boundaries;
  AddLaneBoundaryElement(
      /*line_curve=*/planner::test::ReadLine("LINESTRING(0.0 3.0,5.0 3.0)"),
      /*boundary_type=*/path::BoundaryType::kVirtualHard,
      &lane_marking_boundaries.left_boundary_elements);
  AddLaneBoundaryElement(
      /*line_curve=*/planner::test::ReadLine("LINESTRING(4.0 3.0,10.0 3.0)"),
      /*boundary_type=*/path::BoundaryType::kVirtualSoft,
      &lane_marking_boundaries.left_boundary_elements);
  AddLaneBoundaryElement(
      /*line_curve=*/planner::test::ReadLine("LINESTRING(0.0 -3.0,7.0 -3.0)"),
      /*boundary_type=*/path::BoundaryType::kVirtualSoft,
      &lane_marking_boundaries.right_boundary_elements);
  AddLaneBoundaryElement(
      /*line_curve=*/planner::test::ReadLine("LINESTRING(6.0 -3.0,10.0 -3.0)"),
      /*boundary_type=*/path::BoundaryType::kVirtualHard,
      &lane_marking_boundaries.right_boundary_elements);

  std::vector<nudge_corridor::TaggedNudgeSegment> left_nudge_segments;
  std::vector<nudge_corridor::TaggedNudgeSegment> right_nudge_segments;
  generator_.GetBoundaryElementInLaneNudgeSegmentsOneSide(
      lane_sequence_geometry_, lane_marking_boundaries.left_boundary_elements,
      planning_horizon_range_, lane_marking_buffer_info_.left_buffer_config,
      max_lane_boundary_offset_.left_distance, /*is_left_side=*/true,
      &left_nudge_segments);
  generator_.GetBoundaryElementInLaneNudgeSegmentsOneSide(
      lane_sequence_geometry_, lane_marking_boundaries.right_boundary_elements,
      planning_horizon_range_, lane_marking_buffer_info_.right_buffer_config,
      max_lane_boundary_offset_.right_distance, /*is_left_side=*/false,
      &right_nudge_segments);
  auto result = generator_.GenerateInLaneCorridorFromNudgeSegments(
      lane_sequence_geometry_, left_nudge_segments, right_nudge_segments);

  LOG(ERROR) << ShowTaggedNudgeSegments("left_nudge_segments: ",
                                        left_nudge_segments);
  LOG(ERROR) << ShowTaggedNudgeSegments("right_inlane_nudge_segments: ",
                                        right_nudge_segments);
  LOG(ERROR) << "left_boundary: " << result.left_boundary;
  LOG(ERROR) << "right_boundary: " << result.right_boundary;

  // solid buffer 1.0
  test::CheckBoundaryLine(
      result.left_boundary,
      planner::test::ReadLine("LINESTRING(0 4.0,4 4.0,4 4.5,10 4.5)"));
  // virtual buffer 1.5
  test::CheckBoundaryLine(
      result.right_boundary,
      planner::test::ReadLine("LINESTRING(0 -4.5,7 -4.5,7 -4.0,10 -4.0)"));

  // Case 2: empty boundary_elements
  left_nudge_segments.clear();
  right_nudge_segments.clear();
  const LaneMarkingBufferConfig buffer_config(
      /*virtual_soft_lane_buffer=*/0.0,
      /*virtual_hard_lane_buffer=*/0.0);
  generator_.GetBoundaryElementInLaneNudgeSegmentsOneSide(
      lane_sequence_geometry_,
      generator_.MaybeGenerateAlternativeBoundaryElements(
          /*boundary_elements=*/std::vector<BoundaryElement>{},
          lane_sequence_geometry_.left_lane_boundary,
          max_lane_boundary_offset_.left_distance, /*is_left_side=*/true),
      planning_horizon_range_, buffer_config,
      max_lane_boundary_offset_.left_distance, /*is_left_side=*/true,
      &left_nudge_segments);
  generator_.GetBoundaryElementInLaneNudgeSegmentsOneSide(
      lane_sequence_geometry_,
      generator_.MaybeGenerateAlternativeBoundaryElements(
          /*boundary_elements=*/std::vector<BoundaryElement>{},
          lane_sequence_geometry_.right_lane_boundary,
          max_lane_boundary_offset_.right_distance, /*is_left_side=*/false),
      planning_horizon_range_, buffer_config,
      max_lane_boundary_offset_.right_distance, /*is_left_side=*/false,
      &right_nudge_segments);
  result = generator_.GenerateInLaneCorridorFromNudgeSegments(
      lane_sequence_geometry_, left_nudge_segments, right_nudge_segments);

  LOG(ERROR) << ShowTaggedNudgeSegments("left_nudge_segments: ",
                                        left_nudge_segments);
  LOG(ERROR) << ShowTaggedNudgeSegments("right_inlane_nudge_segments: ",
                                        right_nudge_segments);
  LOG(ERROR) << "left_boundary: " << result.left_boundary;
  LOG(ERROR) << "right_boundary: " << result.right_boundary;
  // No buffer, same with lane sequence geometry.
  test::CheckBoundaryLine(result.left_boundary,
                          lane_sequence_geometry_.left_lane_boundary);
  test::CheckBoundaryLine(result.right_boundary,
                          lane_sequence_geometry_.right_lane_boundary);
}

/*       _____
        /_____/ ego
----------------_________________   left boundary 3.0m

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~   reference_line 0.0m
               _________
              |         | -- agent 1
              |_________|
______________------------------- right boundary -3.0m
*/
TEST_F(DrivableSpaceCorridorGeneratorTest, DrivableSpaceCorridorTest) {
  LeftRightBoundaryPair lane_marking_boundaries;
  AddLaneBoundaryElement(
      /*line_curve=*/planner::test::ReadLine("LINESTRING(0.0 3.0,4.0 3.0)"),
      /*boundary_type=*/path::BoundaryType::kVirtualSoft,
      &lane_marking_boundaries.left_boundary_elements);
  AddLaneBoundaryElement(
      /*line_curve=*/planner::test::ReadLine("LINESTRING(4.0 3.0,10.0 3.0)"),
      /*boundary_type=*/path::BoundaryType::kVirtualHard,
      &lane_marking_boundaries.left_boundary_elements);
  AddLaneBoundaryElement(
      /*line_curve=*/planner::test::ReadLine("LINESTRING(0.0 -3.0,6.0 -3.0)"),
      /*boundary_type=*/path::BoundaryType::kVirtualHard,
      &lane_marking_boundaries.right_boundary_elements);
  AddLaneBoundaryElement(
      /*line_curve=*/planner::test::ReadLine("LINESTRING(6.0 -3.0,10.0 -3.0)"),
      /*boundary_type=*/path::BoundaryType::kVirtualSoft,
      &lane_marking_boundaries.right_boundary_elements);

  agent_state_map_.clear();
  construction_zones_state_.clear();

  planner::test::ConstructStaticAgentInLaneState(
      lane_sequence_geometry_, /*obj_id=*/1,
      /*obj_polygon=*/
      math::geometry::Polygon2d({math::geometry::Point2d(2.0, -2.0),
                                 math::geometry::Point2d(7.0, -2.0),
                                 math::geometry::Point2d(7.0, -1.0),
                                 math::geometry::Point2d(2.0, -1.0)}),
      agent_state_map_);

  object_reasoning_info_map_ = test::GenerateObjectReasoningInfoMap(
      agent_state_map_, construction_zones_state_,
      lane_sequence_geometry_.nominal_path);

  InLaneNudgeObjectsInfo inlane_nudge_objects_info;
  inlane_nudge_objects_info.pass_left_object_ids.push_back(TypedObjectId(
      /*object_id=*/1, /*object_type=*/pb::ObjectSourceType::kTrackedObject));

  // Add test for object_reasoning_info_map built from object occupancy states
  ObjectOccupancyStateMap object_occupancy_state_map;
  // Construct a static object occupancy state.
  prediction::pb::Agent static_agent_proto;
  auto& static_tracked_object = *static_agent_proto.mutable_tracked_object();
  static_tracked_object.set_id(1);
  static_tracked_object.add_attributes(voy ::perception::HAS_EXACT_SHAPE);
  math::geometry::Convert(/*obj_polygon=*/
                          math::geometry::Polygon2d(
                              {math::geometry::Point2d(2.0, -2.0),
                               math::geometry::Point2d(7.0, -2.0),
                               math::geometry::Point2d(7.0, -1.0),
                               math::geometry::Point2d(2.0, -1.0)}),
                          *static_tracked_object.mutable_contour());
  PlannerObject static_planner_object(
      static_agent_proto, /*timestamp=*/1,
      TrafficParticipantPose(/*timestamp=*/1, static_tracked_object));
  static_planner_object.set_is_primary_stationary(true);
  prediction::pb::PredictedTrajectory static_predicted_trajectory;
  static_predicted_trajectory.set_id(1);
  static_predicted_trajectory.add_traj_poses();
  std::vector<PredictedTrajectoryWrapper> predicted_trajectory_wrappers;
  predicted_trajectory_wrappers.emplace_back(voy::TrackedObject(),
                                             static_predicted_trajectory,
                                             /*is_primary_trajectory=*/true);
  ObjectOccupancyState static_object_state = GenerateObjectOccupancyState(
      static_planner_object, /*object_information=*/{},
      predicted_trajectory_wrappers, lane_sequence_geometry_.nominal_path,
      lane_sequence_geometry_.left_lane_boundary,
      lane_sequence_geometry_.right_lane_boundary, ego_param_,
      pb::MotionMode::FORWARD);
  object_occupancy_state_map.emplace(
      static_object_state.planner_object().tracked_object().id(),
      std::make_unique<ObjectOccupancyState>(static_object_state));
  const auto object_reasoning_info_map_2 =
      test::GenerateObjectReasoningInfoMapFromOccupancyState(
          object_occupancy_state_map, construction_zones_state_,
          lane_sequence_geometry_.nominal_path, ego_param_);

  // 1. inlane corridor
  auto result = generator_.GenerateDrivableInLaneCorridor(
      /*robot_snapshot=*/planner::test::GenerateRobotStateSnapshot(
          /*front_pose=*/math::geometry::Point2d{1.0, 4.0}),
      lane_sequence_geometry_,
      /*lane_marking_boundaries=*/lane_marking_boundaries,
      max_lane_boundary_offset_, planning_horizon_range_,
      object_reasoning_info_map_, inlane_nudge_objects_info,
      /*avoid_nudge_objects_info=*/{}, lane_marking_buffer_info_);
  LOG(ERROR) << "left_boundary: " << result.left_boundary;
  LOG(ERROR) << "right_boundary: " << result.right_boundary;

  test::CheckBoundaryLine(
      result.left_boundary,
      planner::test::ReadLine(
          "LINESTRING(0 5.932,10 5.932)"));  // solid buffer 1.0
  test::CheckBoundaryLine(
      result.right_boundary,
      planner::test::ReadLine(
          "LINESTRING(0 -4.0,6 -4.0,6 -4.5,10 -4.5)"));  // virtual buffer 1.5
  // Test result from object occupancy state map.
  FLAGS_planning_enable_object_occupancy_state = true;
  DrivingCorridor result_2 = generator_.GenerateDrivableInLaneCorridor(
      /*robot_snapshot=*/planner::test::GenerateRobotStateSnapshot(
          /*front_pose=*/math::geometry::Point2d{1.0, 4.0}),
      lane_sequence_geometry_,
      /*lane_marking_boundaries=*/lane_marking_boundaries,
      max_lane_boundary_offset_, planning_horizon_range_,
      object_reasoning_info_map_2, inlane_nudge_objects_info,
      /*avoid_nudge_objects_info=*/{}, lane_marking_buffer_info_);
  LOG(ERROR) << "left_boundary: " << result_2.left_boundary;
  LOG(ERROR) << "right_boundary: " << result_2.right_boundary;

  test::CheckBoundaryLine(
      result_2.left_boundary,
      planner::test::ReadLine(
          "LINESTRING(0 5.932,10 5.932)"));  // solid buffer 1.0
  test::CheckBoundaryLine(
      result_2.right_boundary,
      planner::test::ReadLine(
          "LINESTRING(0 -4.0,6 -4.0,6 -4.5,10 -4.5)"));  // virtual buffer 1.5

  // 2. drivable space corridor
  FLAGS_planning_enable_object_occupancy_state = false;
  auto result_3 = generator_.GenerateDrivableSpaceCorridor(
      /*robot_snapshot=*/planner::test::GenerateRobotStateSnapshot(
          /*front_pose=*/math::geometry::Point2d{1.0, 4.0}),
      /*lane_sequence=*/std::vector<const pnc_map::Lane*>{},
      lane_sequence_geometry_, max_lane_boundary_offset_,
      planning_horizon_range_, object_reasoning_info_map_,
      inlane_nudge_objects_info, /*avoid_nudge_object_ids=*/{},
      lane_marking_buffer_info_, /*resolution=*/1.0,
      /*use_max_expandable_point=*/false, /*assist_instruction=*/nullptr,
      lateral_clearance::LateralClearanceData(),
      /*max_lateral_horizon=*/kMaxLateralHorizonInMeter);

  LOG(ERROR) << "left_boundary: " << result_3.driving_corridor.left_boundary;
  LOG(ERROR) << "right_boundary: " << result_3.driving_corridor.right_boundary;
  test::CheckBoundaryLine(
      result_3.driving_corridor.left_boundary,
      planner::test::ReadLine(
          "LINESTRING(0.0 1.0,3.0 1.0,4.0 5.932,5.0 2.0,8.0 2.0,9.0 "
          "5.932,10.0 5.932)"));  // solid buffer 1.0
  test::CheckBoundaryLine(
      result_3.driving_corridor.right_boundary,
      planner::test::ReadLine(
          "LINESTRING(0.0 -4.0,1.0 -1.0,4.0 -1.0,5.0 -4.0,6.0 "
          "-2.0,9.0 -2.0,10.0 -4.0)"));  // solid buffer 1.0
  // Test result from object occupancy state map.
  FLAGS_planning_enable_object_occupancy_state = true;
  auto result_4 = generator_.GenerateDrivableSpaceCorridor(
      /*robot_snapshot=*/planner::test::GenerateRobotStateSnapshot(
          /*front_pose=*/math::geometry::Point2d{1.0, 4.0}),
      /*lane_sequence=*/std::vector<const pnc_map::Lane*>{},
      lane_sequence_geometry_, max_lane_boundary_offset_,
      planning_horizon_range_, object_reasoning_info_map_2,
      inlane_nudge_objects_info, /*avoid_nudge_object_ids=*/{},
      lane_marking_buffer_info_, /*resolution=*/1.0,
      /*use_max_expandable_point=*/false, /*assist_instruction=*/nullptr,
      lateral_clearance::LateralClearanceData(),
      /*max_lateral_horizon=*/kMaxLateralHorizonInMeter);
  LOG(ERROR) << "left_boundary: " << result_4.driving_corridor.left_boundary;
  LOG(ERROR) << "right_boundary: " << result_4.driving_corridor.right_boundary;
  test::CheckBoundaryLine(
      result_4.driving_corridor.left_boundary,
      planner::test::ReadLine(
          "LINESTRING(0.0 1.0,3.0 1.0,4.0 5.932,5.0 2.0,8.0 2.0,9.0 "
          "5.932,10.0 5.932)"));  // solid buffer 1.0
  test::CheckBoundaryLine(
      result_4.driving_corridor.right_boundary,
      planner::test::ReadLine(
          "LINESTRING(0.0 -4.0,1.0 -1.0,4.0 -1.0,5.0 -4.0,6.0 "
          "-2.0,9.0 -2.0,10.0 -4.0)"));  // solid buffer 1.0
}

/*       _____
        /_____/ ego
---------------------------------______________________   left boundary 3.0m
                                        _________
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~------|---------|------   reference_line 0.0m
               _________               |         |
              |         | -- agent 1   |_________|  -- agent 2
              |_________|
_________________________________---------------------- right boundary -3.0m
*/
TEST_F(DrivableSpaceCorridorGeneratorTest,
       DrivableSpaceCorridorTestWithAvoidNudge) {
  lane_selection::LaneSequenceGeometry lane_sequence_geometry;
  lane_sequence_geometry.nominal_path =
      planner::test::ReadLine("LINESTRING(0.0 0.0,40.0 0.0)");
  lane_sequence_geometry.left_lane_boundary =
      planner::test::ReadLine("LINESTRING(0.0 3.0,40.0 3.0)");
  lane_sequence_geometry.right_lane_boundary =
      planner::test::ReadLine("LINESTRING(0.0 -3.0,40.0 -3.0)");
  lane_sequence_geometry.lane_sequence_contour =
      lane_selection::GetLaneBoundaryBorder(
          lane_sequence_geometry.left_lane_boundary,
          lane_sequence_geometry.right_lane_boundary,
          lane_sequence_geometry.nominal_path);

  const math::Range1d planning_horizon_range = {
      lane_sequence_geometry.nominal_path.GetStartPoint().x(),
      lane_sequence_geometry.nominal_path.GetEndPoint().x()};

  LeftRightBoundaryPair lane_marking_boundaries;
  AddLaneBoundaryElement(
      /*line_curve=*/planner::test::ReadLine("LINESTRING(0.0 3.0,25.0 3.0)"),
      /*boundary_type=*/path::BoundaryType::kVirtualSoft,
      &lane_marking_boundaries.left_boundary_elements);
  AddLaneBoundaryElement(
      /*line_curve=*/planner::test::ReadLine("LINESTRING(25.0 3.0,40.0 3.0)"),
      /*boundary_type=*/path::BoundaryType::kVirtualHard,
      &lane_marking_boundaries.left_boundary_elements);
  AddLaneBoundaryElement(
      /*line_curve=*/planner::test::ReadLine("LINESTRING(0.0 -3.0,25.0 -3.0)"),
      /*boundary_type=*/path::BoundaryType::kVirtualHard,
      &lane_marking_boundaries.right_boundary_elements);
  AddLaneBoundaryElement(
      /*line_curve=*/planner::test::ReadLine("LINESTRING(25.0 -3.0,40.0 -3.0)"),
      /*boundary_type=*/path::BoundaryType::kVirtualSoft,
      &lane_marking_boundaries.right_boundary_elements);

  agent_state_map_.clear();
  construction_zones_state_.clear();

  planner::test::ConstructStaticAgentInLaneState(
      lane_sequence_geometry, /*obj_id=*/1,
      /*obj_polygon=*/
      math::geometry::Polygon2d({math::geometry::Point2d(18.0, -2.0),
                                 math::geometry::Point2d(21.0, -2.0),
                                 math::geometry::Point2d(21.0, 3.5),
                                 math::geometry::Point2d(18.0, 3.5)}),
      agent_state_map_);

  planner::test::ConstructStaticAgentInLaneState(
      lane_sequence_geometry, /*obj_id=*/2,
      /*obj_polygon=*/
      math::geometry::Polygon2d({math::geometry::Point2d(27.0, -1.0),
                                 math::geometry::Point2d(32.0, -1.0),
                                 math::geometry::Point2d(32.0, 2.0),
                                 math::geometry::Point2d(27.0, 2.0)}),
      agent_state_map_);

  object_reasoning_info_map_ = test::GenerateObjectReasoningInfoMap(
      agent_state_map_, construction_zones_state_,
      lane_sequence_geometry.nominal_path);

  InLaneNudgeObjectsInfo inlane_nudge_objects_info;
  inlane_nudge_objects_info.pass_left_object_ids.push_back(TypedObjectId(
      /*object_id=*/1, /*object_type=*/pb::ObjectSourceType::kTrackedObject));
  std::vector<TypedObjectId> avoid_nudge_object_ids;
  avoid_nudge_object_ids.emplace_back(
      /*object_id=*/2, /*object_type=*/pb::ObjectSourceType::kTrackedObject);

  // Add test for object_reasoning_info_map built from object occupancy states
  ObjectOccupancyStateMap object_occupancy_state_map;

  // Construct static object occupancy states.
  prediction::pb::Agent static_agent_1_proto;
  auto& static_tracked_object_1 =
      *static_agent_1_proto.mutable_tracked_object();
  static_tracked_object_1.set_id(1);
  static_tracked_object_1.add_attributes(voy::perception::HAS_EXACT_SHAPE);
  math::geometry::Convert(/*obj_polygon=*/
                          math::geometry::Polygon2d(
                              {math::geometry::Point2d(18.0, -2.0),
                               math::geometry::Point2d(21.0, -2.0),
                               math::geometry::Point2d(21.0, 3.5),
                               math::geometry::Point2d(18.0, 3.5)}),
                          *static_tracked_object_1.mutable_contour());
  PlannerObject static_planner_object_1(
      static_agent_1_proto, /*timestamp=*/1,
      TrafficParticipantPose(/*timestamp=*/1, static_tracked_object_1));
  static_planner_object_1.set_is_primary_stationary(true);
  prediction::pb::PredictedTrajectory static_predicted_trajectory;
  static_predicted_trajectory.set_id(1);
  static_predicted_trajectory.add_traj_poses();
  std::vector<PredictedTrajectoryWrapper> predicted_trajectory_wrappers;
  predicted_trajectory_wrappers.emplace_back(voy::TrackedObject(),
                                             static_predicted_trajectory,
                                             /*is_primary_trajectory=*/true);
  ObjectOccupancyState static_object_state_1 = GenerateObjectOccupancyState(
      static_planner_object_1, /*object_information=*/{},
      predicted_trajectory_wrappers, lane_sequence_geometry.nominal_path,
      lane_sequence_geometry.left_lane_boundary,
      lane_sequence_geometry.right_lane_boundary, ego_param_,
      pb::MotionMode::FORWARD);
  object_occupancy_state_map.emplace(
      static_object_state_1.planner_object().tracked_object().id(),
      std::make_unique<ObjectOccupancyState>(static_object_state_1));

  prediction::pb::Agent static_agent_2_proto;
  auto& static_tracked_object_2 =
      *static_agent_2_proto.mutable_tracked_object();
  static_tracked_object_2.set_id(2);
  static_tracked_object_2.add_attributes(voy::perception::HAS_EXACT_SHAPE);
  math::geometry::Convert(/*obj_polygon=*/
                          math::geometry::Polygon2d(
                              {math::geometry::Point2d(27.0, -1.0),
                               math::geometry::Point2d(32.0, -1.0),
                               math::geometry::Point2d(32.0, 2.0),
                               math::geometry::Point2d(27.0, 2.0)}),
                          *static_tracked_object_2.mutable_contour());
  PlannerObject static_planner_object_2(
      static_agent_2_proto, /*timestamp=*/1,
      TrafficParticipantPose(/*timestamp=*/1, static_tracked_object_2));
  static_planner_object_2.set_is_primary_stationary(true);
  ObjectOccupancyState static_object_state_2 = GenerateObjectOccupancyState(
      static_planner_object_2, /*object_information=*/{},
      predicted_trajectory_wrappers, lane_sequence_geometry.nominal_path,
      lane_sequence_geometry.left_lane_boundary,
      lane_sequence_geometry.right_lane_boundary, ego_param_,
      pb::MotionMode::FORWARD);
  object_occupancy_state_map.emplace(
      static_object_state_2.planner_object().tracked_object().id(),
      std::make_unique<ObjectOccupancyState>(static_object_state_2));

  const auto object_reasoning_info_map_2 =
      test::GenerateObjectReasoningInfoMapFromOccupancyState(
          object_occupancy_state_map, construction_zones_state_,
          lane_sequence_geometry.nominal_path, ego_param_);

  // 1. Inlane corridor
  FLAGS_planning_enable_object_occupancy_state = false;
  auto result = generator_.GenerateDrivableInLaneCorridor(
      /*robot_snapshot=*/planner::test::GenerateRobotStateSnapshot(
          /*front_pose=*/math::geometry::Point2d{1.0, 3.0}),
      lane_sequence_geometry,
      /*lane_marking_boundaries=*/lane_marking_boundaries,
      max_lane_boundary_offset_, planning_horizon_range,
      object_reasoning_info_map_, inlane_nudge_objects_info,
      avoid_nudge_object_ids, lane_marking_buffer_info_);
  LOG(ERROR) << "left_boundary: " << result.left_boundary;
  LOG(ERROR) << "right_boundary: " << result.right_boundary;

  // Ego width is 1.864m and length is 4.9536m here, lateral extend boundary
  // with 1.0m buffer, longitudinal buffer in front of ego is 20.0m.
  // solid lane marking buffer is 1.0.
  test::CheckBoundaryLine(result.left_boundary,
                          planner::test::ReadLine("LINESTRING("
                                                  "0 4.932,"
                                                  "8.0928 4.932,"
                                                  "8.0928 5.364,"
                                                  "27 5.364,"
                                                  "27 3.0,"
                                                  "32 3.0,"
                                                  "32 4.0,"
                                                  "40 4.0"
                                                  ")"));
  test::CheckBoundaryLine(
      result.right_boundary,
      planner::test::ReadLine("LINESTRING(0 -4.0,25 -4.0,25 -4.5,40 -4.5)"));

  // Test result from object occupancy state map.
  FLAGS_planning_enable_object_occupancy_state = true;
  DrivingCorridor result_2 = generator_.GenerateDrivableInLaneCorridor(
      /*robot_snapshot=*/planner::test::GenerateRobotStateSnapshot(
          /*front_pose=*/math::geometry::Point2d{1.0, 3.0}),
      lane_sequence_geometry,
      /*lane_marking_boundaries=*/lane_marking_boundaries,
      max_lane_boundary_offset_, planning_horizon_range,
      object_reasoning_info_map_2, inlane_nudge_objects_info,
      avoid_nudge_object_ids, lane_marking_buffer_info_);
  LOG(ERROR) << "left_boundary: " << result_2.left_boundary;
  LOG(ERROR) << "right_boundary: " << result_2.right_boundary;

  test::CheckBoundaryLine(result_2.left_boundary,
                          planner::test::ReadLine("LINESTRING("
                                                  "0 4.932,"
                                                  "8.0928 4.932,"
                                                  "8.0928 5.364,"
                                                  "27 5.364,"
                                                  "27 3.0,"
                                                  "32 3.0,"
                                                  "32 4.0,"
                                                  "40 4.0"
                                                  ")"));
  test::CheckBoundaryLine(
      result_2.right_boundary,
      planner::test::ReadLine("LINESTRING(0 -4.0,25 -4.0,25 -4.5,40 -4.5)"));
}

/* _____
  /_____/ ego
---------------________------------_________________________  left boundary 3.0m
           ___ --- dynamic agent 3
          |___|                             _________
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~------_---|---------|------ reference_line 0.0m
                           ____            |         |
                          |    |--agent 1  |_________|  -- agent 2
                          |____|
_________________________________-------------------------- right boundary -3.0m
*/
TEST_F(DrivableSpaceCorridorGeneratorTest,
       DrivableSpaceCorridorTestWithStaticDynamicAndAvoidNudge) {
  lane_selection::LaneSequenceGeometry lane_sequence_geometry;
  lane_sequence_geometry.nominal_path =
      planner::test::ReadLine("LINESTRING(0.0 0.0,40.0 0.0)");
  lane_sequence_geometry.left_lane_boundary =
      planner::test::ReadLine("LINESTRING(0.0 3.0,40.0 3.0)");
  lane_sequence_geometry.right_lane_boundary =
      planner::test::ReadLine("LINESTRING(0.0 -3.0,40.0 -3.0)");
  lane_sequence_geometry.lane_sequence_contour =
      lane_selection::GetLaneBoundaryBorder(
          lane_sequence_geometry.left_lane_boundary,
          lane_sequence_geometry.right_lane_boundary,
          lane_sequence_geometry.nominal_path);

  const math::Range1d planning_horizon_range = {
      lane_sequence_geometry.nominal_path.GetStartPoint().x(),
      lane_sequence_geometry.nominal_path.GetEndPoint().x()};

  LeftRightBoundaryPair lane_marking_boundaries;
  AddLaneBoundaryElement(
      /*line_curve=*/planner::test::ReadLine("LINESTRING(0.0 3.0,7.0 3.0)"),
      /*boundary_type=*/path::BoundaryType::kVirtualSoft,
      &lane_marking_boundaries.left_boundary_elements);
  AddLaneBoundaryElement(
      /*line_curve=*/planner::test::ReadLine("LINESTRING(7.0 3.0,10.0 3.0)"),
      /*boundary_type=*/path::BoundaryType::kVirtualHard,
      &lane_marking_boundaries.left_boundary_elements);
  AddLaneBoundaryElement(
      /*line_curve=*/planner::test::ReadLine("LINESTRING(10.0 3.0,25.0 3.0)"),
      /*boundary_type=*/path::BoundaryType::kVirtualSoft,
      &lane_marking_boundaries.left_boundary_elements);
  AddLaneBoundaryElement(
      /*line_curve=*/planner::test::ReadLine("LINESTRING(25.0 3.0,40.0 3.0)"),
      /*boundary_type=*/path::BoundaryType::kVirtualHard,
      &lane_marking_boundaries.left_boundary_elements);
  AddLaneBoundaryElement(
      /*line_curve=*/planner::test::ReadLine("LINESTRING(0.0 -3.0,25.0 -3.0)"),
      /*boundary_type=*/path::BoundaryType::kVirtualHard,
      &lane_marking_boundaries.right_boundary_elements);
  AddLaneBoundaryElement(
      /*line_curve=*/planner::test::ReadLine("LINESTRING(25.0 -3.0,40.0 -3.0)"),
      /*boundary_type=*/path::BoundaryType::kVirtualSoft,
      &lane_marking_boundaries.right_boundary_elements);

  agent_state_map_.clear();
  construction_zones_state_.clear();

  planner::test::ConstructStaticAgentInLaneState(
      lane_sequence_geometry, /*obj_id=*/1,
      /*obj_polygon=*/
      math::geometry::Polygon2d({math::geometry::Point2d(18.0, -2.0),
                                 math::geometry::Point2d(21.0, -2.0),
                                 math::geometry::Point2d(21.0, 3.5),
                                 math::geometry::Point2d(18.0, 3.5)}),
      agent_state_map_);

  planner::test::ConstructStaticAgentInLaneState(
      lane_sequence_geometry, /*obj_id=*/2,
      /*obj_polygon=*/
      math::geometry::Polygon2d({math::geometry::Point2d(27.0, -1.0),
                                 math::geometry::Point2d(32.0, -1.0),
                                 math::geometry::Point2d(32.0, 2.0),
                                 math::geometry::Point2d(27.0, 2.0)}),
      agent_state_map_);

  // A hard-blocking dynamic agent runs in range [3.0, 6.0].
  planner::test::ConstructDynamicAgentInLaneState(
      lane_sequence_geometry, /*obj_id=*/3, /*x0=*/4.0, /*y0=*/0.0,
      /*width=*/8.0, /*speed=*/0.2, agent_state_map_);

  object_reasoning_info_map_ = test::GenerateObjectReasoningInfoMap(
      agent_state_map_, construction_zones_state_,
      lane_sequence_geometry.nominal_path);

  InLaneNudgeObjectsInfo inlane_nudge_objects_info;
  inlane_nudge_objects_info.pass_left_object_ids.push_back(TypedObjectId(
      /*object_id=*/1, /*object_type=*/pb::ObjectSourceType::kTrackedObject));
  inlane_nudge_objects_info.pass_left_object_ids.push_back(TypedObjectId(
      /*object_id=*/3, /*object_type=*/pb::ObjectSourceType::kTrackedObject));
  std::vector<TypedObjectId> avoid_nudge_object_ids;
  avoid_nudge_object_ids.emplace_back(
      /*object_id=*/2, /*object_type=*/pb::ObjectSourceType::kTrackedObject);

  // Add test for object_reasoning_info_map built from object occupancy states
  ObjectOccupancyStateMap object_occupancy_state_map;

  // Construct static object occupancy states.
  prediction::pb::Agent static_agent_1_proto;
  auto& static_tracked_object_1 =
      *static_agent_1_proto.mutable_tracked_object();
  static_tracked_object_1.set_id(1);
  static_tracked_object_1.add_attributes(voy::perception::HAS_EXACT_SHAPE);
  math::geometry::Convert(/*obj_polygon=*/
                          math::geometry::Polygon2d(
                              {math::geometry::Point2d(18.0, -2.0),
                               math::geometry::Point2d(21.0, -2.0),
                               math::geometry::Point2d(21.0, 3.5),
                               math::geometry::Point2d(18.0, 3.5)}),
                          *static_tracked_object_1.mutable_contour());
  PlannerObject static_planner_object_1(
      static_agent_1_proto, /*timestamp=*/1,
      TrafficParticipantPose(/*timestamp=*/1, static_tracked_object_1));
  static_planner_object_1.set_is_primary_stationary(true);
  prediction::pb::PredictedTrajectory static_predicted_trajectory;
  static_predicted_trajectory.set_id(1);
  static_predicted_trajectory.add_traj_poses();
  std::vector<PredictedTrajectoryWrapper> predicted_trajectory_wrappers;
  predicted_trajectory_wrappers.emplace_back(voy::TrackedObject(),
                                             static_predicted_trajectory,
                                             /*is_primary_trajectory=*/true);
  ObjectOccupancyState static_object_state_1 = GenerateObjectOccupancyState(
      static_planner_object_1, /*object_information=*/{},
      predicted_trajectory_wrappers, lane_sequence_geometry.nominal_path,
      lane_sequence_geometry.left_lane_boundary,
      lane_sequence_geometry.right_lane_boundary, ego_param_,
      pb::MotionMode::FORWARD);
  object_occupancy_state_map.emplace(
      static_object_state_1.planner_object().tracked_object().id(),
      std::make_unique<ObjectOccupancyState>(static_object_state_1));

  prediction::pb::Agent static_agent_2_proto;
  auto& static_tracked_object_2 =
      *static_agent_2_proto.mutable_tracked_object();
  static_tracked_object_2.set_id(2);
  static_tracked_object_2.add_attributes(voy::perception::HAS_EXACT_SHAPE);
  math::geometry::Convert(/*obj_polygon=*/
                          math::geometry::Polygon2d(
                              {math::geometry::Point2d(27.0, -1.0),
                               math::geometry::Point2d(32.0, -1.0),
                               math::geometry::Point2d(32.0, 2.0),
                               math::geometry::Point2d(27.0, 2.0)}),
                          *static_tracked_object_2.mutable_contour());
  PlannerObject static_planner_object_2(
      static_agent_2_proto, /*timestamp=*/1,
      TrafficParticipantPose(/*timestamp=*/1, static_tracked_object_2));
  static_planner_object_2.set_is_primary_stationary(true);
  ObjectOccupancyState static_object_state_2 = GenerateObjectOccupancyState(
      static_planner_object_2, /*object_information=*/{},
      predicted_trajectory_wrappers, lane_sequence_geometry.nominal_path,
      lane_sequence_geometry.left_lane_boundary,
      lane_sequence_geometry.right_lane_boundary, ego_param_,
      pb::MotionMode::FORWARD);
  object_occupancy_state_map.emplace(
      static_object_state_2.planner_object().tracked_object().id(),
      std::make_unique<ObjectOccupancyState>(static_object_state_2));

  // Construct a dynamic object occupancy state.
  prediction::pb::Agent dynamic_agent_3_proto;
  dynamic_agent_3_proto.mutable_tracked_object()->CopyFrom(
      planner::test::ConstructTrackedObjectFromDimension(
          /*object_id=*/3, /*x0=*/4.0,
          /*y0=*/0.0, /*width=*/8.0, /*length=*/2.0, /*height=*/2.0,
          /*velocity=*/0.2, /*heading=*/0.0));
  prediction::pb::PredictedTrajectory proto_traj;
  std::vector<PredictedTrajectoryWrapper> predicted_trajectory_wrappers_2;
  predicted_trajectory_wrappers_2.push_back(
      planner::test::ConstructPredictedTrajectoryWrapperForTest(
          dynamic_agent_3_proto.tracked_object(), proto_traj));
  PlannerObject dynamic_planner_object_3(
      dynamic_agent_3_proto, /*timestamp=*/1,
      TrafficParticipantPose(/*timestamp=*/1,
                             dynamic_agent_3_proto.tracked_object()));
  ObjectOccupancyState dynamic_object_state_3 = GenerateObjectOccupancyState(
      dynamic_planner_object_3, /*object_information=*/{},
      predicted_trajectory_wrappers_2, lane_sequence_geometry.nominal_path,
      lane_sequence_geometry.left_lane_boundary,
      lane_sequence_geometry.right_lane_boundary, ego_param_,
      pb::MotionMode::FORWARD);
  object_occupancy_state_map.emplace(
      dynamic_object_state_3.planner_object().tracked_object().id(),
      std::make_unique<ObjectOccupancyState>(dynamic_object_state_3));

  const auto object_reasoning_info_map_2 =
      test::GenerateObjectReasoningInfoMapFromOccupancyState(
          object_occupancy_state_map, construction_zones_state_,
          lane_sequence_geometry.nominal_path, ego_param_);

  // 1. Inlane corridor
  FLAGS_planning_enable_object_occupancy_state = false;
  auto result = generator_.GenerateDrivableInLaneCorridor(
      /*robot_snapshot=*/planner::test::GenerateRobotStateSnapshot(
          /*front_pose=*/math::geometry::Point2d{1.0, 0.0}),
      lane_sequence_geometry,
      /*lane_marking_boundaries=*/lane_marking_boundaries,
      max_lane_boundary_offset_, planning_horizon_range,
      object_reasoning_info_map_, inlane_nudge_objects_info,
      avoid_nudge_object_ids, lane_marking_buffer_info_);
  LOG(ERROR) << "left_boundary: " << result.left_boundary;
  LOG(ERROR) << "right_boundary: " << result.right_boundary;

  // Ego width is 1.864m and length is 4.9536m here, lateral extend boundary
  // with 1.0m buffer, longitudinal buffer in front of ego is 20.0m.
  // solid lane marking buffer is 1.0.
  test::CheckBoundaryLine(result.left_boundary,
                          planner::test::ReadLine("LINESTRING("
                                                  "0 5.864,"
                                                  "7 5.864,"
                                                  "7 4.0,"
                                                  "8.0928 4.0,"
                                                  "8.0928 5.864,"
                                                  "15.9072 5.864,"
                                                  "15.9072 5.364,"
                                                  "27 5.364,"
                                                  "27 3.0,"
                                                  "32 3.0,"
                                                  "32 4.0,"
                                                  "40 4.0"
                                                  ")"));
  test::CheckBoundaryLine(
      result.right_boundary,
      planner::test::ReadLine("LINESTRING(0 -4.0,25 -4.0,25 -4.5,40 -4.5)"));

  // Test result from object occupancy state map.
  FLAGS_planning_enable_object_occupancy_state = true;
  DrivingCorridor result_2 = generator_.GenerateDrivableInLaneCorridor(
      /*robot_snapshot=*/planner::test::GenerateRobotStateSnapshot(
          /*front_pose=*/math::geometry::Point2d{1.0, 0.0}),
      lane_sequence_geometry,
      /*lane_marking_boundaries=*/lane_marking_boundaries,
      max_lane_boundary_offset_, planning_horizon_range,
      object_reasoning_info_map_2, inlane_nudge_objects_info,
      avoid_nudge_object_ids, lane_marking_buffer_info_);
  LOG(ERROR) << "left_boundary: " << result_2.left_boundary;
  LOG(ERROR) << "right_boundary: " << result_2.right_boundary;

  test::CheckBoundaryLine(result_2.left_boundary,
                          planner::test::ReadLine("LINESTRING("
                                                  "0 5.864,"
                                                  "7 5.864,"
                                                  "7 4.0,"
                                                  "8.0928 4.0,"
                                                  "8.0928 5.864,"
                                                  "15.9072 5.864,"
                                                  "15.9072 5.364,"
                                                  "27 5.364,"
                                                  "27 3.0,"
                                                  "32 3.0,"
                                                  "32 4.0,"
                                                  "40 4.0"
                                                  ")"));
  test::CheckBoundaryLine(
      result_2.right_boundary,
      planner::test::ReadLine("LINESTRING(0 -4.0,25 -4.0,25 -4.5,40 -4.5)"));
}

}  // namespace path
}  // namespace planner
