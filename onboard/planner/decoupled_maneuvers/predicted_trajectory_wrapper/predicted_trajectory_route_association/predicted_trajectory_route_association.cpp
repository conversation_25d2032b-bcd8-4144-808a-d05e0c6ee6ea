#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_route_association/predicted_trajectory_route_association.h"

#include <algorithm>
#include <iterator>
#include <optional>
#include <queue>
#include <unordered_set>
#include <utility>

#include "geometry/model/point_2d.h"
#include "geometry/model/polyline_curve.h"
#include "hdmap/lib/hdmap.h"
#include "hdmap_protos/junction.pb.h"
#include "hdmap_protos/lane.pb.h"
#include "hdmap_protos/zone.pb.h"
#include "math/math_util.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_wrapper.h"
#include "planner_protos/predicted_trajectory_route_association.pb.h"
#include "pnc_map_service/map_elements/lane.h"
#include "pnc_map_service/map_elements/road.h"
#include "pnc_map_service/map_elements/section.h"
#include "pnc_map_service/map_elements/zone.h"
#include "trace/trace.h"
#include "voy_protos/math.pb.h"
#include "voy_trace/trace_planner.h"

namespace planner {

namespace route_association {

namespace {

constexpr int kSampledSize = 5;
constexpr double kMinRequiredOdomProgressForPoseSamplingInMeter = 5.0;
constexpr double kDistanceThresholdConsideredWithinInMeter = 0.1;
constexpr double kQueryRadiusInMeter = 0.0;
constexpr double kExpandedQueryRadiusInMeter = 3.0;
constexpr double kExpandedQueryRadiusThresholdForRoundaboutInMeter = 0.5;
constexpr double kMaxHeadingDiffForValidLaneOccupation = M_PI / 3;
// TODO(minhanli): Figure out a systematical way to manage the lifetime of map
// element infos, completely avoiding crash issues caused by vector
// reallocation.
constexpr int kEstimatedMaxNumberOfElementsOnePoseCanOccupy = 100;

constexpr double kCostContented = 0.0;
constexpr double kCostBrotherhood = 1.0;
constexpr double kCostSoftNeighborhood = 0.0;
constexpr double kCostNeighborhood = 3.0;
constexpr double kCostSuperNeighborhood = 7.0;
constexpr double kCostUnknown = 20.0;

using AssociationType =
    ::planner::pb::AgentPredictedTrajectoryRouteAssociationType;
using AssociationDebugProto =
    ::planner::pb::PredictedTrajectoryRouteAssociationDebug;

// Util to check whether two elements are spatially disjoint.
bool AreTwoElementsDisjoint(const MapElementAndPoseInfo& source_element,
                            const MapElementAndPoseInfo& target_element,
                            const AssociationType::Enum association_type) {
  if (association_type == AssociationType::kBrotherhood) {
    return false;
  }
  // NOTE: Do not handle zones for now.
  if (source_element.type == pb::MapElementType::ZONE ||
      target_element.type == pb::MapElementType::ZONE) {
    return false;
  }
  if (association_type != AssociationType::kContented &&
      association_type != AssociationType::kNeighborhood) {
    return true;
  }
  // Only consider lanes for now.
  // TODO(minhanli): Add logic for zones.
  if (source_element.type == pb::MapElementType::LANE &&
      target_element.type == pb::MapElementType::LANE) {
    const pnc_map::Lane* source_lane = source_element.lane_ptr;
    const pnc_map::Lane* target_lane = target_element.lane_ptr;
    DCHECK_NE(source_lane, nullptr);
    DCHECK_NE(target_lane, nullptr);
    if (source_lane->id() == target_lane->id()) {
      return false;
    }
    const bool is_longitudinal_connected = std::any_of(
        source_lane->successors().begin(), source_lane->successors().end(),
        [&target_lane](const pnc_map::Lane* lane) {
          return lane->id() == target_lane->id();
        });
    const bool is_lateral_connected =
        source_lane->IsAdjacentLane(
            *target_lane, routing::pb::LaneConnectionType::kLeftLaneChange) ||
        source_lane->IsAdjacentLane(
            *target_lane, routing::pb::LaneConnectionType::kRightLaneChange);
    if (is_longitudinal_connected || is_lateral_connected) {
      return false;
    } else {
      return true;
    }
  }
  return true;
}

// Util to populate lane bundles of merging brothers in source section.
void PopulateLaneBundlesInSourceSection(
    const pnc_map::Lane* source_lane,
    std::vector<std::vector<const pnc_map::Lane*>>&
        lane_bundles_sharing_boundary_line,
    size_t& source_lane_bundle_idx) {
  size_t outer_loop_idx = 0UL;
  while (outer_loop_idx < source_lane->section()->lanes().size()) {
    // Append a new lane bundle consisting of the lane being iterated through.
    const pnc_map::Lane* lane = source_lane->section()->lanes()[outer_loop_idx];
    lane_bundles_sharing_boundary_line.push_back({lane});
    if (lane->id() == source_lane->id()) {
      source_lane_bundle_idx = lane_bundles_sharing_boundary_line.size() - 1;
    }
    // Increment index and append merging brother lanes to the new bundle until
    // it is not a merging brother of the new lane.
    size_t inner_loop_idx = ++outer_loop_idx;
    while (inner_loop_idx < source_lane->section()->lanes().size()) {
      const pnc_map::Lane* lane_to_check =
          source_lane->section()->lanes()[inner_loop_idx];

      const bool is_merging_brother = std::any_of(
          lane->brothers().begin(), lane->brothers().end(),
          [&lane_to_check](const pnc_map::BrotherLane& brother_lane) {
            return brother_lane.lane()->id() == lane_to_check->id() &&
                   brother_lane.type() ==
                       pnc_map::BrotherLane::RelationType::kMerge;
          });
      if (is_merging_brother) {
        lane_bundles_sharing_boundary_line.back().push_back(lane_to_check);
        if (lane_to_check->id() == source_lane->id()) {
          source_lane_bundle_idx =
              lane_bundles_sharing_boundary_line.size() - 1;
        }
      } else {
        break;
      }
      outer_loop_idx = ++inner_loop_idx;
    }
  }
}

// Util to populate polylines which is used to represent exiting boundary of
// source section.
void PopulatePolylinesRepresentingSourceSectionExitingBoundary(
    const pnc_map::Lane* source_lane,
    const std::vector<std::vector<const pnc_map::Lane*>>&
        lane_bundles_sharing_boundary_line,
    math::geometry::Polyline2d& boundary_line,
    math::geometry::Polyline2d& simplified_boundary_line) {
  // Populate polyline segments.
  for (const std::vector<const pnc_map::Lane*>& lane_bundle :
       lane_bundles_sharing_boundary_line) {
    const math::geometry::Point2d& end_of_left_marking =
        lane_bundle.front()
            ->left_marking()
            ->lane_marking_segments()
            .back()
            .line_curve.polyline()
            .back();
    const math::geometry::Point2d& end_of_right_marking =
        lane_bundle.back()
            ->right_marking()
            ->lane_marking_segments()
            .back()
            .line_curve.polyline()
            .back();

    boundary_line.push_back(end_of_left_marking);

    if (lane_bundle.front()->id() ==
        source_lane->section()->lanes().front()->id()) {
      simplified_boundary_line.push_back(end_of_left_marking);
    }
    if (lane_bundle.back()->id() ==
        source_lane->section()->lanes().back()->id()) {
      boundary_line.push_back(end_of_right_marking);
      simplified_boundary_line.push_back(end_of_right_marking);
    }
  }
}

// Util to find the first point crossing a polyline along a given range of
// trajectory.
// NOTE: This function leverages binary search to speed up, thus assuming the
// range of trajectory has one, and only one, crossing point on the polyline.
math::geometry::Point2d FindFirstPointCrossingPolylineOnTrajectory(
    const PredictedTrajectoryWrapper& predicted_trajectory,
    const int start_pose_idx, const int end_pose_idx,
    const math::geometry::PolylineCurve2d& boundary_polyline) {
  DCHECK_GE(start_pose_idx, 0);
  DCHECK_LT(end_pose_idx, predicted_trajectory.poses().size());
  DCHECK_LE(start_pose_idx, end_pose_idx);
  DCHECK(!boundary_polyline.polyline().empty());

  const math::geometry::Point2d start_point = {
      predicted_trajectory.pose(start_pose_idx).x_pos(),
      predicted_trajectory.pose(start_pose_idx).y_pos()};
  const math::geometry::Point2d end_point = {
      predicted_trajectory.pose(end_pose_idx).x_pos(),
      predicted_trajectory.pose(end_pose_idx).y_pos()};

  const math::pb::Side start_pose_side = boundary_polyline.GetSide(start_point);
  const math::pb::Side end_pose_side = boundary_polyline.GetSide(end_point);
  const bool are_end_points_on_two_sides = start_pose_side != end_pose_side;
  if (!are_end_points_on_two_sides) {
    DLOG(WARNING)
        << " Object: " << predicted_trajectory.object_id()
        << " Traj: " << predicted_trajectory.id()
        << " start idx: " << start_pose_idx << " end idx: " << end_pose_idx
        << " boundary start x: " << boundary_polyline.polyline().front().x()
        << " boundary start y : " << boundary_polyline.polyline().front().y()
        << " boundary end x : " << boundary_polyline.polyline().back().x()
        << " boundary end y : " << boundary_polyline.polyline().back().y();
    return boundary_polyline.GetDistance(start_point,
                                         math::pb::UseExtensionFlag::kForbid) <
                   boundary_polyline.GetDistance(
                       end_point, math::pb::UseExtensionFlag::kForbid)
               ? start_point
               : end_point;
  }

  math::geometry::Point2d ans_point;

  int left_idx = start_pose_idx;
  int right_idx = end_pose_idx;
  while (left_idx <= right_idx) {
    int mid_idx = left_idx + (right_idx - left_idx) / 2;
    math::geometry::Point2d mid_point = {
        predicted_trajectory.pose(mid_idx).x_pos(),
        predicted_trajectory.pose(mid_idx).y_pos()};
    if (start_pose_side == boundary_polyline.GetSide(mid_point)) {
      left_idx = mid_idx + 1;
    } else {
      right_idx = mid_idx - 1;
      ans_point = mid_point;
    }
  }
  return ans_point;
}

const pnc_map::Lane* FindExitingLaneInSourceSection(
    const MapElementAndPoseInfo& source_element,
    const MapElementAndPoseInfo& target_element,
    const PredictedTrajectoryWrapper& predicted_trajectory,
    const pnc_map::Lane* source_lane,
    const math::geometry::PolylineCurve2d& source_section_exiting_boundary,
    const math::geometry::PolylineCurve2d&
        simplified_source_section_exiting_boundary,
    const std::vector<std::vector<const pnc_map::Lane*>>&
        lane_bundles_sharing_boundary_line,
    const size_t source_lane_bundle_idx) {
  // Search the first point on predicted trajectory crossing the simplified
  // source section exiting boundary.
  DCHECK(source_element.sampled_pose_index_in_trajectory_opt.has_value());
  DCHECK(target_element.sampled_pose_index_in_trajectory_opt.has_value());
  const math::geometry::Point2d first_crossing_point =
      FindFirstPointCrossingPolylineOnTrajectory(
          predicted_trajectory,
          source_element.sampled_pose_index_in_trajectory_opt.value(),
          target_element.sampled_pose_index_in_trajectory_opt.value(),
          simplified_source_section_exiting_boundary);
  // Find the closest lane bundle under source section to the first crossing
  // point as the exit lane bundle.
  const size_t exiting_lane_bundle_index_in_source_section =
      static_cast<size_t>(source_section_exiting_boundary
                              .GetProximity(first_crossing_point,
                                            math::pb::UseExtensionFlag::kForbid)
                              .closest_segment_index);
  DCHECK(exiting_lane_bundle_index_in_source_section <
         lane_bundles_sharing_boundary_line.size());

  const pnc_map::Lane* exiting_lane_in_source_section = nullptr;
  if (source_lane_bundle_idx == exiting_lane_bundle_index_in_source_section) {
    // Take the source as the exiting lane, if they are exactly the same.
    exiting_lane_in_source_section = source_lane;
  } else if (source_lane_bundle_idx >
             exiting_lane_bundle_index_in_source_section) {
    // The source is at the right side of the exiting bundle, take the
    // right-most lane in the bundle as exiting lane.
    exiting_lane_in_source_section =
        lane_bundles_sharing_boundary_line
            [exiting_lane_bundle_index_in_source_section]
                .back();
  } else {
    // The source is at the left side of the exiting bundle, take the left-most
    // lane in the bundle as exiting lane.
    exiting_lane_in_source_section =
        lane_bundles_sharing_boundary_line
            [exiting_lane_bundle_index_in_source_section]
                .front();
  }
  return exiting_lane_in_source_section;
}

const pnc_map::Lane* FindEnteringLaneInTargetSection(
    const pnc_map::Lane* exiting_lane_in_source_section,
    const pnc_map::Lane* target_lane) {
  const auto iter = std::find_if(
      exiting_lane_in_source_section->successors().begin(),
      exiting_lane_in_source_section->successors().end(),
      [&target_lane](const pnc_map::Lane* succeeding_lane) {
        return succeeding_lane->section()->id() == target_lane->section()->id();
      });
  // Numerical errors in |FindExitingLaneInSourceSection()| may propagate error
  // to here. Thus using DLOG rather that DCHECK.
  if (iter == exiting_lane_in_source_section->successors().end()) {
    DLOG(WARNING) << "Could not find entering lane.";
    return nullptr;
  }
  return *iter;
}

std::optional<MapElementAndPoseInfo> GetLatestIsolatedMapPoseInfo(
    const pnc_map::PncMapService* pnc_map_service,
    const pb::AgentMapElementOccupancySeeds& agent_map_element_occupancy_seeds,
    int64_t object_id, pb::MapElementType::Enum element_type) {
  const auto& iter =
      agent_map_element_occupancy_seeds.agent_element_occupancy_map().find(
          object_id);
  if (iter !=
      agent_map_element_occupancy_seeds.agent_element_occupancy_map().end()) {
    if (element_type == pb::MapElementType::LANE &&
        iter->second.has_latest_occupied_isolated_lane()) {
      DCHECK(iter->second.latest_occupied_isolated_lane().element_type() ==
             pb::MapElementType::LANE);
      MapElementAndPoseInfo isolated_map_pose_info;
      const int64_t seed_lane_id =
          iter->second.latest_occupied_isolated_lane().element_id();
      const pnc_map::Lane* seed_lane_ptr =
          pnc_map_service->GetLaneSequence({seed_lane_id}).front();
      if (seed_lane_ptr == nullptr) {
        DLOG(WARNING) << " Lane ID not exist:" << seed_lane_id;
        return std::nullopt;
      }
      isolated_map_pose_info.element_id = seed_lane_id;
      isolated_map_pose_info.type = pb::MapElementType::LANE;
      isolated_map_pose_info.lane_ptr = seed_lane_ptr;
      isolated_map_pose_info.is_inferred_element = true;
      return isolated_map_pose_info;
    }

    if (element_type == pb::MapElementType::ZONE &&
        iter->second.has_latest_occupied_isolated_zone()) {
      DCHECK(iter->second.latest_occupied_isolated_zone().element_type() ==
             pb::MapElementType::ZONE);
      const int64_t seed_zone_id =
          iter->second.latest_occupied_isolated_zone().element_id();
      const pnc_map::Zone* seed_zone_ptr =
          pnc_map_service->GetZones({seed_zone_id}).front();
      if (seed_zone_ptr == nullptr) {
        DLOG(WARNING) << " Zone ID not exist:" << seed_zone_id;
        return std::nullopt;
      }
      MapElementAndPoseInfo isolated_map_pose_info;
      isolated_map_pose_info.element_id = seed_zone_id;
      isolated_map_pose_info.type = pb::MapElementType::ZONE;
      isolated_map_pose_info.zone_ptr = seed_zone_ptr;
      isolated_map_pose_info.is_inferred_element = true;
      return isolated_map_pose_info;
    }
  }
  return std::nullopt;
}

// Util to check if an exit zone retrieved from seed whose arc length range
// projected on the road covers that of the first pose of predicted
// trajectory.
bool IsLatestExitZoneCoveringFirstPose(
    const PredictedTrajectoryWrapper& predicted_trajectory,
    const pnc_map::Zone* zone_ptr) {
  DCHECK(zone_ptr != nullptr);
  if (zone_ptr->type() == hdmap::Zone::ROAD_EXIT) {
    const math::geometry::Point2d& traj_first_pos =
        predicted_trajectory.prediction_first_pose().center_2d();
    for (const pnc_map::Road* road : zone_ptr->roads()) {
      const auto iter =
          std::find_if(road->zone_infos().begin(), road->zone_infos().end(),
                       [zone_ptr](const pnc_map::ZoneInfo& zone_info) {
                         return zone_info.id() == zone_ptr->id();
                       });
      if (iter != road->zone_infos().end()) {
        const math::ProximityQueryInfo pose_proximity =
            road->reference_line().GetProximity(
                traj_first_pos, math::pb::UseExtensionFlag::kForbid);
        if (pose_proximity.relative_position !=
            math::RelativePosition::kWithIn) {
          continue;
        }
        if (pose_proximity.arc_length >= iter->track_start.s() &&
            pose_proximity.arc_length <= iter->track_end.s()) {
          return true;
        }
      }
    }
  }
  return false;
}

}  // namespace

PredictedTrajectoryRouteAssociator::PredictedTrajectoryRouteAssociator(
    const PredictedTrajectoryWrapper& predicted_trajectory,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const pb::AgentMapElementOccupancySeeds& agent_map_element_occupancy_seeds,
    planner::pb::WorldModelDebug* debug_proto_ptr)
    : object_id_(predicted_trajectory.object_id()),
      trajectory_id_(predicted_trajectory.id()),
      sampled_pose_indices_(ComputeSamplingIndexForPredictedTrajectory(
          predicted_trajectory, kSampledSize,
          kMinRequiredOdomProgressForPoseSamplingInMeter)),
      cached_map_element_infos_({}),
      sampled_pose_infos_(PopulateSampledPoseInfos(predicted_trajectory,
                                                   joint_pnc_map_service)) {
  std::optional<MapElementAndPoseInfo> latest_zone_pose_info_opt =
      GetLatestIsolatedMapPoseInfo(joint_pnc_map_service.pnc_map_service(),
                                   agent_map_element_occupancy_seeds,
                                   object_id(), pb::MapElementType::ZONE);
  if (latest_zone_pose_info_opt.has_value() &&
      IsLatestExitZoneCoveringFirstPose(predicted_trajectory,
                                        latest_zone_pose_info_opt->zone_ptr)) {
    latest_exit_zone_info_in_seed_opt_ = latest_zone_pose_info_opt;
  }

  raw_association_cost_lookup_table_ =
      PopulateRawAssociationLookupTable(joint_pnc_map_service.pnc_map_service(),
                                        agent_map_element_occupancy_seeds);
  raw_equally_optimal_routes_ = GetRawEquallyOptimalRoutes();

  raw_equally_optimal_first_map_elements_.reserve(
      raw_equally_optimal_routes_.size());
  std::for_each(raw_equally_optimal_routes_.begin(),
                raw_equally_optimal_routes_.end(),
                [this](const RouteInfo& route) {
                  if (!route.empty()) {
                    raw_equally_optimal_first_map_elements_.push_back(
                        *DCHECK_NOTNULL(route.front()));
                  }
                });

  GenerateMostLikelyRoute(predicted_trajectory);

  // Update debug info.
  pb::AgentPredictedTrajectoryRoutesDebug* agent_association_debug =
      debug_proto_ptr != nullptr
          ? debug_proto_ptr->mutable_agent_prediction_route_debug()
          : nullptr;
  GetOrCreateMultiPredictedTrajectoryRouteAssociationDebug(
      agent_association_debug);
}

PredictedTrajectoryRouteAssociator::PredictedTrajectoryRouteAssociator(
    const PredictedTrajectoryWrapper& predicted_trajectory,
    const std::vector<int>& sampled_pose_indices,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const pb::AgentMapElementOccupancySeeds& agent_map_element_occupancy_seeds,
    planner::pb::WorldModelDebug* debug_proto_ptr)
    : object_id_(predicted_trajectory.object_id()),
      trajectory_id_(predicted_trajectory.id()),
      sampled_pose_indices_(sampled_pose_indices),
      cached_map_element_infos_({}),
      sampled_pose_infos_(PopulateSampledPoseInfos(predicted_trajectory,
                                                   joint_pnc_map_service)) {
  std::optional<MapElementAndPoseInfo> latest_zone_pose_info_opt =
      GetLatestIsolatedMapPoseInfo(joint_pnc_map_service.pnc_map_service(),
                                   agent_map_element_occupancy_seeds,
                                   object_id(), pb::MapElementType::ZONE);
  if (latest_zone_pose_info_opt.has_value() &&
      IsLatestExitZoneCoveringFirstPose(predicted_trajectory,
                                        latest_zone_pose_info_opt->zone_ptr)) {
    latest_exit_zone_info_in_seed_opt_ = latest_zone_pose_info_opt;
  }
  raw_association_cost_lookup_table_ =
      PopulateRawAssociationLookupTable(joint_pnc_map_service.pnc_map_service(),
                                        agent_map_element_occupancy_seeds);
  raw_equally_optimal_routes_ = GetRawEquallyOptimalRoutes();

  raw_equally_optimal_first_map_elements_.reserve(
      raw_equally_optimal_routes_.size());
  std::for_each(raw_equally_optimal_routes_.begin(),
                raw_equally_optimal_routes_.end(),
                [this](const RouteInfo& route) {
                  if (!route.empty()) {
                    raw_equally_optimal_first_map_elements_.push_back(
                        *DCHECK_NOTNULL(route.front()));
                  }
                });

  // No need to generate a complete route for non-stationary BPs, if we just
  // need to do association for the first pose. Generate a partial one instead.
  if (sampled_pose_indices.size() == 1UL && sampled_pose_indices.front() == 0 &&
      !predicted_trajectory.IsStationary()) {
    GeneratePartialRouteInferredByTheFirstPose();
  } else {
    GenerateMostLikelyRoute(predicted_trajectory);
  }

  // Update debug info.
  pb::AgentPredictedTrajectoryRoutesDebug* agent_association_debug =
      debug_proto_ptr != nullptr
          ? debug_proto_ptr->mutable_agent_prediction_route_debug()
          : nullptr;
  GetOrCreateMultiPredictedTrajectoryRouteAssociationDebug(
      agent_association_debug);
}

std::vector<PredictedTrajectoryRouteAssociator::SampledPoseInfo>
PredictedTrajectoryRouteAssociator::PreProcessingMapQueriedInfos(
    const PredictedTrajectoryWrapper& predicted_trajectory,
    const pnc_map::PncMapService* pnc_map_service,
    const std::unordered_map<int, std::vector<QueriedLaneAndApproxDistance>>&
        idx_unexpanded_dist_lane_pairs_map,
    const std::unordered_map<int, std::vector<QueriedLaneAndApproxDistance>>&
        idx_expanded_dist_lane_pairs_map,
    const std::unordered_map<int, std::vector<const hdmap::Junction*>>&
        idx_junctions_map,
    const std::unordered_map<int, std::vector<const hdmap::Zone*>>&
        idx_zones_map) {
  // From this line forward, pre-filtering elements and populating infos for
  // sampled poses.
  std::vector<MapElementAndPoseInfo> occupied_elements;
  occupied_elements.reserve(kEstimatedMaxNumberOfElementsOnePoseCanOccupy);
  std::vector<MapElementAndPoseInfo> invalid_occupied_elements;
  invalid_occupied_elements.reserve(
      kEstimatedMaxNumberOfElementsOnePoseCanOccupy);
  std::unordered_set<int64_t> last_pose_occupied_lanes;
  std::unordered_set<int64_t> last_pose_occupied_zones;
  cached_map_element_infos_.reserve(
      predicted_trajectory.size() *
      kEstimatedMaxNumberOfElementsOnePoseCanOccupy);

  std::vector<SampledPoseInfo> sampled_pose_infos;
  sampled_pose_infos.reserve(sampled_pose_indices_.size());

  for (int idx : sampled_pose_indices_) {
    std::unordered_set<int64_t> current_pose_occupied_lanes;
    std::unordered_set<int64_t> current_pose_occupied_zones;
    // Selecting zones and lanes of interest.
    if (!idx_zones_map.at(idx).empty()) {
      for (const hdmap::Zone* zone : idx_zones_map.at(idx)) {
        // Only process exit zone for now.
        if (zone->type() != hdmap::Zone::ROAD_EXIT) {
          continue;
        }
        current_pose_occupied_zones.insert(zone->id());
        const pnc_map::Zone* zone_ptr =
            pnc_map_service->GetZones({zone->id()}).front();
        const double zone_heading = zone_ptr->center_line()->GetInterpTheta(
            zone_ptr->center_line()->GetTotalArcLength());
        // Exit zone heading is from road end pointing towards zone end, plus pi
        // to flip the direction of zone heading.
        const double heading_diff = std::abs(math::AngleDiff(
            zone_heading + M_PI, predicted_trajectory.pose(idx).heading()));
        occupied_elements.push_back(MapElementAndPoseInfo{
            pb::MapElementType::ZONE, zone->id(),
            /*is_inferred_element=*/false, /*distance_opt=*/0.0,
            /*heading_diff_opt=*/heading_diff,
            /*sampled_pose_index_in_trajectory_opt=*/idx,
            /*lane_ptr=*/nullptr, /*zone_ptr=*/zone_ptr});
      }
    } else {
      // Only include lanes with expanded query range if there is a queried lane
      // possessing conflicting lanes (including uturn outside junction) or
      // there is no lane being found and the pose is in a junction.
      const auto& unexpanded_dist_lane_pairs =
          idx_unexpanded_dist_lane_pairs_map.at(idx);
      const bool has_conflicting_lanes = std::any_of(
          unexpanded_dist_lane_pairs.begin(), unexpanded_dist_lane_pairs.end(),
          [](const QueriedLaneAndApproxDistance& unexpanded_dist_lane_pair) {
            return !unexpanded_dist_lane_pair.lane->conflicting_lanes().empty();
          });
      const bool is_in_junction = !idx_junctions_map.at(idx).empty();
      if (unexpanded_dist_lane_pairs.empty() && !is_in_junction) {
        continue;
      }

      const bool should_expand_query_range =
          has_conflicting_lanes || is_in_junction;
      const std::vector<QueriedLaneAndApproxDistance>& dist_lane_pairs =
          should_expand_query_range
              ? idx_expanded_dist_lane_pairs_map.at(idx)
              : idx_unexpanded_dist_lane_pairs_map.at(idx);

      for (const QueriedLaneAndApproxDistance& dist_lane_pair :
           dist_lane_pairs) {
        const pnc_map::Lane* lane = dist_lane_pair.lane;
        const double distance = dist_lane_pair.distance;
        // Exclude regular and bus lanes with distance greater than threshold as
        // they are not in targeted scenes for range expansion.
        if ((lane->type() == hdmap::Lane_LaneType::Lane_LaneType_REGULAR ||
             lane->type() == hdmap::Lane_LaneType::Lane_LaneType_BUS) &&
            distance > kDistanceThresholdConsideredWithinInMeter) {
          continue;
        }
        // Exclude lanes throughout roundabout with distance greater than the
        // threshold to avoid the wrong result like
        // https://cooper.didichuxing.com/shares/Q6GVMqA2LxYO.
        if ((lane->section()->road()->is_through_roundabout()) &&
            distance > kExpandedQueryRadiusThresholdForRoundaboutInMeter) {
          continue;
        }
        // Exclude lanes not enclosing the sampled pose while lanes under the
        // same section do.
        if (distance > kDistanceThresholdConsideredWithinInMeter) {
          const bool has_lane_under_same_section_enclosing_pose = std::any_of(
              dist_lane_pairs.begin(), dist_lane_pairs.end(),
              [&lane](const QueriedLaneAndApproxDistance& dist_lane_pair) {
                return dist_lane_pair.distance <=
                           kDistanceThresholdConsideredWithinInMeter &&
                       lane->section()->id() ==
                           dist_lane_pair.lane->section()->id();
              });
          if (has_lane_under_same_section_enclosing_pose) continue;
        }

        const math::ProximityQueryInfo pose_proximity =
            lane->center_line().GetProximity(
                {predicted_trajectory.pose(idx).x_pos(),
                 predicted_trajectory.pose(idx).y_pos()},
                math::pb::kForbid);
        const double pose_projected_arclength = pose_proximity.arc_length;
        const double heading_diff = std::abs(math::AngleDiff(
            lane->center_line().GetInterpTheta(pose_projected_arclength),
            predicted_trajectory.pose(idx).heading()));

        const bool is_valid_lane =
            heading_diff <= kMaxHeadingDiffForValidLaneOccupation;
        if (is_valid_lane) {
          occupied_elements.push_back(MapElementAndPoseInfo{
              pb::MapElementType::LANE, lane->id(),
              /*is_inferred_element=*/false, distance, heading_diff,
              /*sampled_pose_index_in_trajectory_opt=*/idx,
              /*lane_ptr=*/lane, /*zone_ptr=*/nullptr});
          current_pose_occupied_lanes.insert(lane->id());
        } else {
          invalid_occupied_elements.push_back(MapElementAndPoseInfo{
              pb::MapElementType::LANE, lane->id(),
              /*is_inferred_element=*/false, distance, heading_diff,
              /*sampled_pose_index_in_trajectory_opt=*/idx,
              /*lane_ptr=*/lane, /*zone_ptr=*/nullptr});
          current_pose_occupied_lanes.insert(lane->id());
        }
      }
      // If there is no valid lane, appending invalid ones.
      if (occupied_elements.empty() && !invalid_occupied_elements.empty()) {
        occupied_elements.insert(
            occupied_elements.end(),
            std::make_move_iterator(invalid_occupied_elements.begin()),
            std::make_move_iterator(invalid_occupied_elements.end()));
      }
    }

    // Skip the pose if it does not occupy any elements or occupies the exactly
    // same elements as the last pose to speed up.
    if (occupied_elements.empty() ||
        (current_pose_occupied_lanes == last_pose_occupied_lanes &&
         current_pose_occupied_zones == last_pose_occupied_zones)) {
      occupied_elements.clear();
      invalid_occupied_elements.clear();
      sampled_pose_indices_being_skipped_.push_back(idx);
      continue;
    }
    last_pose_occupied_lanes = current_pose_occupied_lanes;
    last_pose_occupied_zones = current_pose_occupied_zones;
    const auto inserted_interator = cached_map_element_infos_.insert(
        cached_map_element_infos_.end(),
        std::make_move_iterator(occupied_elements.begin()),
        std::make_move_iterator(occupied_elements.end()));

    SampledPoseInfo sampled_pose_info;
    sampled_pose_info.sampled_pose_index_in_trajectory = idx;
    for (auto it = inserted_interator; it != cached_map_element_infos_.end();
         ++it) {
      sampled_pose_info.occupied_elements.push_back(&(*it));
    }
    sampled_pose_infos.push_back(std::move(sampled_pose_info));

    occupied_elements.clear();
    invalid_occupied_elements.clear();
  }
  return sampled_pose_infos;
}

std::vector<PredictedTrajectoryRouteAssociator::SampledPoseInfo>
PredictedTrajectoryRouteAssociator::PopulateSampledPoseInfos(
    const PredictedTrajectoryWrapper& predicted_trajectory,
    const pnc_map::JointPncMapService& joint_pnc_map_service) {
  TRACE_EVENT_SCOPE(planner, BP_RouteAssociation_Sample);
  // Check if indices are in ascending order and also not duplicated to avoid
  // duplicate computations while making it convenient for post-processing.
  DCHECK(std::is_sorted(sampled_pose_indices_.begin(),
                        sampled_pose_indices_.end()));
  DCHECK(std::adjacent_find(sampled_pose_indices_.begin(),
                            sampled_pose_indices_.end()) ==
         sampled_pose_indices_.end());

  std::unordered_map<int, std::vector<QueriedLaneAndApproxDistance>>
      idx_unexpanded_dist_lane_pairs_map;
  std::unordered_map<int, std::vector<QueriedLaneAndApproxDistance>>
      idx_expanded_dist_lane_pairs_map;
  std::unordered_map<int, std::vector<const hdmap::Junction*>>
      idx_junctions_map;
  std::unordered_map<int, std::vector<const hdmap::Zone*>> idx_zones_map;

  // Initialize maps with empty vectors.
  for (int idx : sampled_pose_indices_) {
    idx_unexpanded_dist_lane_pairs_map[idx] =
        std::vector<QueriedLaneAndApproxDistance>(0UL);
    idx_expanded_dist_lane_pairs_map[idx] =
        std::vector<QueriedLaneAndApproxDistance>(0UL);
    idx_junctions_map[idx] = std::vector<const hdmap::Junction*>(0UL);
    idx_zones_map[idx] = std::vector<const hdmap::Zone*>(0UL);
  }
  // Query map elements within specified radius.
  for (size_t index_of_sampled_indices = 0UL;
       index_of_sampled_indices < sampled_pose_indices_.size();
       ++index_of_sampled_indices) {
    const int idx = sampled_pose_indices_[index_of_sampled_indices];
    hdmap::Point queried_point;
    queried_point.set_z(0.0);
    queried_point.set_x(predicted_trajectory.pose(idx).x_pos());
    queried_point.set_y(predicted_trajectory.pose(idx).y_pos());

    idx_junctions_map.at(idx) =
        joint_pnc_map_service.pnc_map_service()->hdmap()->GetJunctions(
            queried_point, kQueryRadiusInMeter);

    idx_zones_map.at(idx) =
        joint_pnc_map_service.pnc_map_service()->hdmap()->GetZonesByType(
            queried_point, kQueryRadiusInMeter, hdmap::Zone::ROAD_EXIT);

    idx_expanded_dist_lane_pairs_map.at(idx) =
        GetLanesWithApproxDistanceInRadius(joint_pnc_map_service, queried_point,
                                           kExpandedQueryRadiusInMeter);

    for (const auto& pair : idx_expanded_dist_lane_pairs_map.at(idx)) {
      if (pair.distance <= kQueryRadiusInMeter) {
        idx_unexpanded_dist_lane_pairs_map.at(idx).push_back(pair);
      }
    }
  }

  std::vector<SampledPoseInfo> sampled_pose_infos =
      PreProcessingMapQueriedInfos(
          predicted_trajectory, joint_pnc_map_service.pnc_map_service(),
          idx_unexpanded_dist_lane_pairs_map, idx_expanded_dist_lane_pairs_map,
          idx_junctions_map, idx_zones_map);
  return sampled_pose_infos;
}

AssociationType::Enum
PredictedTrajectoryRouteAssociator::GetAssociationTypeBetweenTwoElements(
    const MapElementAndPoseInfo& source_element_info,
    const MapElementAndPoseInfo& target_element_info) {
  if (source_element_info.type == pb::MapElementType::LANE &&
      target_element_info.type == pb::MapElementType::LANE) {
    const pnc_map::Lane* target_lane = target_element_info.lane_ptr;
    const pnc_map::Lane* source_lane = source_element_info.lane_ptr;
    DCHECK_NE(target_lane, nullptr);
    DCHECK_NE(source_lane, nullptr);

    const bool is_neighbor =
        target_lane->section()->id() == source_lane->section()->id();
    const bool is_super_neighbor =
        target_lane->truncated_preceding_section_ids().find(
            source_lane->section()->id()) !=
        target_lane->truncated_preceding_section_ids().end();

    if (target_lane->id() == source_lane->id() ||
        target_lane->truncated_preceding_lanes().find(source_lane) !=
            target_lane->truncated_preceding_lanes().end()) {
      return AssociationType::kContented;
    } else if (!target_lane->IsInJunction() &&
               std::any_of(
                   target_lane->brothers().begin(),
                   target_lane->brothers().end(),
                   [&source_lane](const pnc_map::BrotherLane& brother_lane) {
                     return brother_lane.lane()->id() == source_lane->id();
                   })) {
      return AssociationType::kBrotherhood;
    } else if (target_lane->IsInJunction() &&
               (is_neighbor || is_super_neighbor)) {
      return AssociationType::kSoftNeighborhood;
    } else if (is_neighbor) {
      return AssociationType::kNeighborhood;
    } else if (is_super_neighbor) {
      return AssociationType::kSuperNeighborhood;
    } else {
      return AssociationType::kUnknown;
    }
  } else if (target_element_info.type == pb::MapElementType::ZONE ||
             source_element_info.type == pb::MapElementType::ZONE) {
    // NOTE: Only consider relationship between an exit zone and a lane for now.
    const MapElementAndPoseInfo* lane_element_info_ptr = nullptr;
    const MapElementAndPoseInfo* zone_element_info_ptr = nullptr;
    if (target_element_info.type == pb::MapElementType::ZONE &&
        source_element_info.type == pb::MapElementType::LANE) {
      lane_element_info_ptr = &source_element_info;
      zone_element_info_ptr = &target_element_info;
    } else if (target_element_info.type == pb::MapElementType::LANE &&
               source_element_info.type == pb::MapElementType::ZONE) {
      lane_element_info_ptr = &target_element_info;
      zone_element_info_ptr = &source_element_info;
    } else {
      return AssociationType::kUnknown;
    }
    if (zone_element_info_ptr->zone_ptr->type() != hdmap::Zone::ROAD_EXIT) {
      return AssociationType::kUnknown;
    }
    // If the exit zone and the lane are associated to the same road, treat them
    // as neighborhood.
    for (const pnc_map::Road* road : zone_element_info_ptr->zone_ptr->roads()) {
      if (lane_element_info_ptr->lane_ptr->section()->road()->id() ==
          road->id()) {
        return AssociationType::kNeighborhood;
      }
    }
    return AssociationType::kUnknown;
  } else {
    DCHECK(false) << "Element must be in lane or zone.";
  }
  return AssociationType::kUnknown;
}

PredictedTrajectoryRouteAssociator::ConnectionCostInfo
PredictedTrajectoryRouteAssociator::ComputeConnectionCostBetweenTwoElements(
    const MapElementAndPoseInfo& source_element_info,
    const MapElementAndPoseInfo& target_element_info) {
  AssociationType::Enum association_type = GetAssociationTypeBetweenTwoElements(
      source_element_info, target_element_info);
  switch (association_type) {
    case AssociationType::kContented:
      return {kCostContented, AssociationType::kContented};
    case AssociationType::kBrotherhood:
      return {kCostBrotherhood, AssociationType::kBrotherhood};
    case AssociationType::kSoftNeighborhood:
      return {kCostSoftNeighborhood, AssociationType::kSoftNeighborhood};
    case AssociationType::kNeighborhood:
      return {kCostNeighborhood, AssociationType::kNeighborhood};
    case AssociationType::kSuperNeighborhood:
      return {kCostSuperNeighborhood, AssociationType::kSuperNeighborhood};
    case AssociationType::kUnknown:
      return {kCostUnknown, AssociationType::kUnknown};
    default:
      return {kCostUnknown, AssociationType::kUnknown};
  }
}

std::vector<PredictedTrajectoryRouteAssociator::TargetElementAssociationResult>
PredictedTrajectoryRouteAssociator::ComputeFirstPoseSingleStepResults(
    const SampledPoseInfo& first_pose_info,
    const pnc_map::PncMapService* pnc_map_service,
    const pb::AgentMapElementOccupancySeeds&
        agent_map_element_occupancy_seeds) {
  std::vector<TargetElementAssociationResult> single_step_results;
  single_step_results.reserve(first_pose_info.occupied_elements.size());
  // NOTE: Currently, we only use the latest isolated lane in seed to
  // process the first queried element in junction.
  latest_isolated_lane_info_in_seed_opt_ = GetLatestIsolatedMapPoseInfo(
      pnc_map_service, agent_map_element_occupancy_seeds, object_id(),
      pb::MapElementType::LANE);

  for (const MapElementAndPoseInfo* map_element_info :
       first_pose_info.occupied_elements) {
    bool is_in_conflicting_region = false;
    if (map_element_info->type == pb::MapElementType::LANE) {
      const pnc_map::Lane* lane = map_element_info->lane_ptr;
      is_in_conflicting_region =
          lane->IsInJunction() || !lane->conflicting_lanes().empty();
    }

    if (latest_isolated_lane_info_in_seed_opt_.has_value() &&
        is_in_conflicting_region) {
      const ConnectionCostInfo initial_cost_info =
          ComputeConnectionCostBetweenTwoElements(
              latest_isolated_lane_info_in_seed_opt_.value(),
              *map_element_info);
      single_step_results.push_back(TargetElementAssociationResult{
          .min_association_cost_so_far = initial_cost_info.connection_cost,
          .target_element_info_ptr = map_element_info,
          .connection_cost_infos = {ConnectionCostInfo{
              .connection_cost = 0.0,
              .association_type = AssociationType::kUnknown}},
          .optimal_source_element_results = {}});

    } else {
      single_step_results.push_back(TargetElementAssociationResult{
          .min_association_cost_so_far = kCostUnknown,
          .target_element_info_ptr = map_element_info,
          .connection_cost_infos = {ConnectionCostInfo{
              .connection_cost = 0.0,
              .association_type = AssociationType::kUnknown}},
          .optimal_source_element_results = {}});
    }
  }
  return single_step_results;
}

std::vector<PredictedTrajectoryRouteAssociator::TargetElementAssociationResult>
PredictedTrajectoryRouteAssociator::ComputeNonFirstPoseSingleStepResults(
    const SampledPoseInfo& pose_info,
    const std::vector<TargetElementAssociationResult>&
        last_pose_single_step_results) {
  std::vector<TargetElementAssociationResult> single_step_results;
  single_step_results.reserve(pose_info.occupied_elements.size());

  for (const MapElementAndPoseInfo* map_element_info :
       pose_info.occupied_elements) {
    double min_association_cost_so_far = std::numeric_limits<double>().max();

    std::vector<ConnectionCostInfo> connection_cost_infos;
    connection_cost_infos.reserve(last_pose_single_step_results.size());

    std::vector<const TargetElementAssociationResult*>
        optimal_source_element_results;
    optimal_source_element_results.reserve(
        last_pose_single_step_results.size());

    for (const TargetElementAssociationResult& last_pose_single_step_result :
         last_pose_single_step_results) {
      if (last_pose_single_step_result.target_element_info_ptr == nullptr) {
        continue;
      }
      ConnectionCostInfo connection_cost =
          ComputeConnectionCostBetweenTwoElements(
              *last_pose_single_step_result.target_element_info_ptr,
              *map_element_info);
      const double association_cost_so_far =
          last_pose_single_step_result.min_association_cost_so_far +
          connection_cost.connection_cost;
      if (association_cost_so_far < min_association_cost_so_far) {
        min_association_cost_so_far = association_cost_so_far;
        optimal_source_element_results.clear();
        optimal_source_element_results.push_back(&last_pose_single_step_result);
      } else if (association_cost_so_far == min_association_cost_so_far) {
        optimal_source_element_results.push_back(&last_pose_single_step_result);
      }
      connection_cost_infos.push_back(std::move(connection_cost));
    }
    single_step_results.push_back(TargetElementAssociationResult{
        .min_association_cost_so_far = min_association_cost_so_far,
        .target_element_info_ptr = map_element_info,
        .connection_cost_infos = std::move(connection_cost_infos),
        .optimal_source_element_results =
            std::move(optimal_source_element_results)});
  }

  return single_step_results;
}

std::unordered_map<size_t, std::vector<PredictedTrajectoryRouteAssociator::
                                           TargetElementAssociationResult>>
PredictedTrajectoryRouteAssociator::PopulateRawAssociationLookupTable(
    const pnc_map::PncMapService* pnc_map_service,
    const pb::AgentMapElementOccupancySeeds&
        agent_map_element_occupancy_seeds) {
  TRACE_EVENT_SCOPE(planner, BP_RouteAssociation_DP);
  std::unordered_map<size_t, std::vector<TargetElementAssociationResult>> table;
  for (size_t idx = 0UL; idx < sampled_pose_infos_.size(); ++idx) {
    table[idx] = (idx == 0UL) ? ComputeFirstPoseSingleStepResults(
                                    sampled_pose_infos_[idx], pnc_map_service,
                                    agent_map_element_occupancy_seeds)
                              : ComputeNonFirstPoseSingleStepResults(
                                    sampled_pose_infos_[idx], table[idx - 1]);
  }
  return table;
}

void PredictedTrajectoryRouteAssociator::
    SearchOptimalRoutesGivenAssociationResult(
        const TargetElementAssociationResult* target_result_ptr,
        std::vector<RouteInfo>& optimal_routes_collection,
        RouteInfo& optimal_route_info_in_backtracking) {
  DCHECK(target_result_ptr != nullptr);
  // Empty optimal source indicates the result produced by the first map
  // element, promote the route in backtracking to the list of optimal routes.
  if (target_result_ptr->optimal_source_element_results.empty()) {
    optimal_route_info_in_backtracking.push_front(
        target_result_ptr->target_element_info_ptr);
    optimal_routes_collection.push_back(optimal_route_info_in_backtracking);
    optimal_route_info_in_backtracking.pop_front();
    return;
  }

  optimal_route_info_in_backtracking.push_front(
      target_result_ptr->target_element_info_ptr);
  for (const TargetElementAssociationResult* optimal_source_result_ptr :
       target_result_ptr->optimal_source_element_results) {
    SearchOptimalRoutesGivenAssociationResult(
        optimal_source_result_ptr, optimal_routes_collection,
        optimal_route_info_in_backtracking);
  }
  optimal_route_info_in_backtracking.pop_front();
  return;
}

std::vector<PredictedTrajectoryRouteAssociator::RouteInfo>
PredictedTrajectoryRouteAssociator::GetRawEquallyOptimalRoutes() {
  TRACE_EVENT_SCOPE(planner, BP_RouteAssociation_GetEqually);
  std::vector<RouteInfo> raw_equally_optimal_routes;
  if (raw_association_cost_lookup_table_.empty()) {
    return raw_equally_optimal_routes;
  }
  // Find the so-far minimum cumulative association cost for the last pose.
  const size_t last_pose_idx_in_map =
      raw_association_cost_lookup_table_.size() - 1UL;
  DCHECK(raw_association_cost_lookup_table_.find(last_pose_idx_in_map) !=
         raw_association_cost_lookup_table_.end());
  const std::vector<TargetElementAssociationResult>&
      last_pose_association_results =
          raw_association_cost_lookup_table_.at(last_pose_idx_in_map);

  const double min_association_cost =
      std::min_element(last_pose_association_results.begin(),
                       last_pose_association_results.end(),
                       [](const TargetElementAssociationResult& result_a,
                          const TargetElementAssociationResult& result_b) {
                         return result_a.min_association_cost_so_far <
                                result_b.min_association_cost_so_far;
                       })
          ->min_association_cost_so_far;
  // Search all routes with the minimum cost ending at the last pose.
  for (size_t element_idx = 0UL;
       element_idx < last_pose_association_results.size(); ++element_idx) {
    if (last_pose_association_results[element_idx]
            .min_association_cost_so_far == min_association_cost) {
      RouteInfo optimal_route_info_in_backtracking;
      SearchOptimalRoutesGivenAssociationResult(
          &(last_pose_association_results[element_idx]),
          raw_equally_optimal_routes, optimal_route_info_in_backtracking);
    }
  }
  return raw_equally_optimal_routes;
}

PredictedTrajectoryRouteAssociator::RouteInfo
PredictedTrajectoryRouteAssociator::SearchRouteBetweenContentedElements(
    const MapElementAndPoseInfo& source_element,
    const MapElementAndPoseInfo& target_element) {
  // Only consider lanes for now.
  // TODO(minhanli): Add logic for zones.
  if (source_element.type != pb::MapElementType::LANE ||
      target_element.type != pb::MapElementType::LANE) {
    return RouteInfo();
  }

  const pnc_map::Lane* source_lane = source_element.lane_ptr;
  const pnc_map::Lane* target_lane = target_element.lane_ptr;

  DCHECK(target_lane->truncated_preceding_lanes().find(source_lane) !=
         target_lane->truncated_preceding_lanes().end());

  // A queue to store all candidate routes along searching.
  std::queue<std::list<const pnc_map::Lane*>> queue_of_candidate_routes;
  std::unordered_set<int64_t> visited_lane_ids;
  queue_of_candidate_routes.push({source_lane});
  visited_lane_ids.insert(source_lane->id());
  // Breadth first search along all successors until the target gets found.
  while (!queue_of_candidate_routes.empty()) {
    std::list<const pnc_map::Lane*> candidate_route =
        queue_of_candidate_routes.front();
    queue_of_candidate_routes.pop();
    for (const pnc_map::Lane* lane : candidate_route.back()->successors()) {
      // The target lane is found along the candidate route, early return.
      if (lane->id() == target_lane->id()) {
        // Remove source lane from the sequence as it's already considered.
        candidate_route.pop_front();
        RouteInfo result;
        for (const pnc_map::Lane* candidate_lane : candidate_route) {
          cached_map_element_infos_.push_back(MapElementAndPoseInfo{
              pb::MapElementType::LANE, candidate_lane->id(),
              /*is_inferred_element=*/true,
              /*distance_opt=*/std::nullopt,
              /*heading_diff_opt=*/std::nullopt,
              /*sampled_pose_index_in_trajectory_opt=*/std::nullopt,
              /*lane_ptr=*/candidate_lane, /*zone_ptr=*/nullptr});
          result.push_back(&cached_map_element_infos_.back());
        }
        return result;
      }
      // Skip visited lanes.
      if (visited_lane_ids.find(lane->id()) != visited_lane_ids.end()) {
        continue;
      } else {
        visited_lane_ids.insert(lane->id());
      }
      candidate_route.push_back(lane);
      // Push the whole candidate to queue for later search.
      queue_of_candidate_routes.push(candidate_route);
      candidate_route.pop_back();
    }
  }
  return RouteInfo();
}

PredictedTrajectoryRouteAssociator::RouteInfo
PredictedTrajectoryRouteAssociator::SearchRouteBetweenNeighboringElements(
    const MapElementAndPoseInfo& source_element,
    const MapElementAndPoseInfo& target_element) {
  if (source_element.type != pb::MapElementType::LANE ||
      target_element.type != pb::MapElementType::LANE) {
    return RouteInfo();
  }
  const pnc_map::Lane* source_lane = source_element.lane_ptr;
  const pnc_map::Lane* target_lane = target_element.lane_ptr;

  DCHECK_EQ(source_lane->section()->id(), target_lane->section()->id());

  const int64_t source_index = std::distance(
      source_lane->section()->lanes().begin(),
      std::find(source_lane->section()->lanes().begin(),
                source_lane->section()->lanes().end(), source_lane));
  const int64_t target_index = std::distance(
      source_lane->section()->lanes().begin(),
      std::find(source_lane->section()->lanes().begin(),
                source_lane->section()->lanes().end(), target_lane));

  RouteInfo result;

  // Lanes are sorted from left to right in section, check relative position
  // between source and target by examining indices.
  const bool is_reverse_order = source_index > target_index;
  const int64_t start_idx = is_reverse_order ? target_index : source_index;
  const int64_t end_idx = is_reverse_order ? source_index : target_index;
  for (int64_t idx = start_idx + 1; idx < end_idx; ++idx) {
    cached_map_element_infos_.push_back(MapElementAndPoseInfo{
        pb::MapElementType::LANE, source_lane->section()->lanes()[idx]->id(),
        /*is_inferred_element=*/true,
        /*distance_opt=*/std::nullopt,
        /*heading_diff_opt=*/std::nullopt,
        /*sampled_pose_index_in_trajectory_opt=*/std::nullopt,
        /*lane_ptr=*/source_lane->section()->lanes()[idx],
        /*zone_ptr=*/nullptr});
    if (is_reverse_order) {
      result.push_front(&cached_map_element_infos_.back());
    } else {
      result.push_back(&cached_map_element_infos_.back());
    }
  }
  return result;
}

PredictedTrajectoryRouteAssociator::RouteInfo
PredictedTrajectoryRouteAssociator::SearchRouteBetweenSuperNeighboringElements(
    const MapElementAndPoseInfo& source_element,
    const MapElementAndPoseInfo& target_element,
    const PredictedTrajectoryWrapper& predicted_trajectory) {
  RouteInfo result;
  if (source_element.type != pb::MapElementType::LANE ||
      target_element.type != pb::MapElementType::LANE) {
    return result;
  }
  // NOTE: Do not bridge gap for elements with expanded queried range for now,
  // as they may not satisfy the assumption that start pose and end pose are on
  // different sides of the source section required by following searching.
  // TODO(minhanli): Figure out ways to handle elements queried with expanded
  // range.
  DCHECK(source_element.distance_opt.has_value() &&
         target_element.distance_opt.has_value());
  if (source_element.distance_opt.value() > kQueryRadiusInMeter ||
      target_element.distance_opt.value() > kQueryRadiusInMeter) {
    return result;
  }
  const pnc_map::Lane* source_lane = source_element.lane_ptr;
  const pnc_map::Lane* target_lane = target_element.lane_ptr;

  // NOTE: We currently only process the simplest super-neighborhood case where
  // two elements belonging to predecessor and successor sections respectively.
  // TODO(minhanli): Add logic for processing more general super-neighborhoods.
  const bool is_one_section_away =
      std::any_of(source_lane->section()->successors().begin(),
                  source_lane->section()->successors().end(),
                  [&target_lane](const pnc_map::Section* section) {
                    return section->id() == target_lane->section()->id();
                  });
  if (!is_one_section_away) {
    return result;
  }
  DCHECK(source_element.sampled_pose_index_in_trajectory_opt.has_value() &&
         target_element.sampled_pose_index_in_trajectory_opt.has_value());

  // A vector consisting of lane bundles, each corresponds to a vector of lanes
  // which are merging brothers in the source lane section (i.e., sharing the
  // same boundary line). Bundles and lanes are stored in their original order
  // in the section.
  std::vector<std::vector<const pnc_map::Lane*>>
      lane_bundles_sharing_boundary_line;
  // Index of lane bundle containing the source lanes.
  size_t source_lane_bundle_idx = 0;

  // Populate lane bundles in the source lane section while find the lane
  // bundle containing the source lane.
  PopulateLaneBundlesInSourceSection(
      source_lane, lane_bundles_sharing_boundary_line, source_lane_bundle_idx);

  // Polyline representing the source section exiting boundary (a polyline
  // shared with succeeding sections), which is constructed by sequential end
  // points of left and right lane-markings of all lanes under the source
  // section, ordered from left to right.
  math::geometry::Polyline2d boundary_line;
  // Simplified polyline which is constructed by the left lane-marking end point
  // of the left-most lane and the right lane-marking end point of the
  // right-most lane.
  math::geometry::Polyline2d simplified_boundary_line;
  // Populate polylines of source section exiting boundary.
  PopulatePolylinesRepresentingSourceSectionExitingBoundary(
      source_lane, lane_bundles_sharing_boundary_line, boundary_line,
      simplified_boundary_line);
  // Convert to PolylineCurve to reuse established util functions.
  const math::geometry::PolylineCurve2d source_section_exiting_boundary(
      std::move(boundary_line));
  const math::geometry::PolylineCurve2d
      simplified_source_section_exiting_boundary(
          std::move(simplified_boundary_line));

  // Find the lane exiting the source section along predicted trajectory.
  const pnc_map::Lane* exiting_lane_in_source_section =
      FindExitingLaneInSourceSection(
          source_element, target_element, predicted_trajectory, source_lane,
          source_section_exiting_boundary,
          simplified_source_section_exiting_boundary,
          lane_bundles_sharing_boundary_line, source_lane_bundle_idx);

  // Find the lane under target section which succeeds the exit lane.
  const pnc_map::Lane* entering_lane_in_target_section =
      FindEnteringLaneInTargetSection(exiting_lane_in_source_section,
                                      target_lane);
  // Could not find entering lane, return empty result.
  if (entering_lane_in_target_section == nullptr) {
    return result;
  }

  if (exiting_lane_in_source_section->id() != source_lane->id()) {
    MapElementAndPoseInfo exiting_element_info_in_source_section = {
        pb::MapElementType::LANE,
        exiting_lane_in_source_section->id(),
        /*is_inferred_element=*/true,
        /*distance_opt=*/std::nullopt,
        /*heading_diff_opt=*/std::nullopt,
        /*sampled_pose_index_in_trajectory_opt=*/std::nullopt,
        /*lane_ptr=*/exiting_lane_in_source_section,
        /*zone_ptr=*/nullptr};

    RouteInfo result_in_source_section = SearchRouteBetweenNeighboringElements(
        source_element, exiting_element_info_in_source_section);

    // Append elements between source and exiting element in source section, and
    // alter connection info for source element.
    result.insert(result.end(),
                  std::make_move_iterator(result_in_source_section.begin()),
                  std::make_move_iterator(result_in_source_section.end()));
    cached_map_element_infos_.push_back(
        std::move(exiting_element_info_in_source_section));

    result.push_back(&cached_map_element_infos_.back());
  }

  if (entering_lane_in_target_section->id() != target_lane->id()) {
    MapElementAndPoseInfo entering_element_info_in_target_section = {
        pb::MapElementType::LANE,
        entering_lane_in_target_section->id(),
        /*is_inferred_element=*/true,
        /*distance_opt=*/std::nullopt,
        /*heading_diff_opt=*/std::nullopt,
        /*sampled_pose_index_in_trajectory_opt=*/std::nullopt,
        /*lane_ptr=*/entering_lane_in_target_section,
        /*zone_ptr=*/nullptr};

    RouteInfo result_in_target_section = SearchRouteBetweenNeighboringElements(
        entering_element_info_in_target_section, target_element);

    // Append elements between element entering target section and target.
    cached_map_element_infos_.push_back(
        std::move(entering_element_info_in_target_section));
    result.push_back(&cached_map_element_infos_.back());
    result.insert(result.end(),
                  std::make_move_iterator(result_in_target_section.begin()),
                  std::make_move_iterator(result_in_target_section.end()));
  }
  return result;
}

PredictedTrajectoryRouteAssociator::RouteInfo
PredictedTrajectoryRouteAssociator::FindMissedRouteBetweenDisjointElements(
    const MapElementAndPoseInfo& source_element,
    const MapElementAndPoseInfo& target_element,
    const AssociationType::Enum association_type,
    const PredictedTrajectoryWrapper& predicted_trajectory) {
  DCHECK(GetAssociationTypeBetweenTwoElements(source_element, target_element) ==
         association_type);
  switch (association_type) {
    case AssociationType::kContented:
      return SearchRouteBetweenContentedElements(source_element,
                                                 target_element);
    case AssociationType::kNeighborhood:
      return SearchRouteBetweenNeighboringElements(source_element,
                                                   target_element);
    case AssociationType::kSuperNeighborhood:
      return SearchRouteBetweenSuperNeighboringElements(
          source_element, target_element, predicted_trajectory);
    case AssociationType::kBrotherhood:
    case AssociationType::kSoftNeighborhood:
    case AssociationType::kUnknown:
    default:
      return RouteInfo();
  }
}

void PredictedTrajectoryRouteAssociator::ConnectingDisjointElementsInRoute(
    const PredictedTrajectoryWrapper& predicted_trajectory,
    RouteInfo& route_info) {
  for (auto iter = route_info.begin(); iter != route_info.end(); ++iter) {
    if (iter == route_info.begin()) {
      continue;
    }
    const MapElementAndPoseInfo* source_element_ptr = *std::prev(iter);
    const MapElementAndPoseInfo* target_element_ptr = *iter;
    const AssociationType::Enum association_type =
        GetAssociationTypeBetweenTwoElements(*source_element_ptr,
                                             *target_element_ptr);
    if (!AreTwoElementsDisjoint(*source_element_ptr, *target_element_ptr,
                                association_type)) {
      continue;
    }
    RouteInfo missed_route_info = FindMissedRouteBetweenDisjointElements(
        *source_element_ptr, *target_element_ptr, association_type,
        predicted_trajectory);

    route_info.insert(iter, std::make_move_iterator(missed_route_info.begin()),
                      std::make_move_iterator(missed_route_info.end()));
  }
  return;
}

std::vector<PredictedTrajectoryRouteAssociator::RouteInfo>
PredictedTrajectoryRouteAssociator::RemoveDuplicateElementsAlongRawRoutes(
    const std::vector<RouteInfo>& route_infos) {
  std::vector<RouteInfo> route_infos_without_duplicate = route_infos;
  std::for_each(route_infos_without_duplicate.begin(),
                route_infos_without_duplicate.end(), [](RouteInfo& info) {
                  info.unique([](const MapElementAndPoseInfo* lhs,
                                 const MapElementAndPoseInfo* rhs) {
                    return *lhs == *rhs;
                  });
                });
  return route_infos_without_duplicate;
}

PredictedTrajectoryRouteAssociator::RouteInfo
PredictedTrajectoryRouteAssociator::RemoveDuplicateElementsAlongRawRoute(
    const RouteInfo& route_info) {
  RouteInfo route_info_without_duplicate = route_info;
  route_info_without_duplicate.unique(
      [](const MapElementAndPoseInfo* lhs, const MapElementAndPoseInfo* rhs) {
        return *lhs == *rhs;
      });
  return route_info_without_duplicate;
}

size_t PredictedTrajectoryRouteAssociator::SelectMostLikelyRouteIndex(
    const std::vector<RouteInfo>& equally_optimal_routes_with_duplicate) {
  size_t selected_route_index = 0UL;

  // NOTE: We so far only implement the tie-braking for agent going through
  // a junction, otherwise default return the first route.
  std::vector<PncLaneAndAssociatedHeadingDiff>
      first_lanes_and_heading_diff_in_junction;

  std::for_each(
      equally_optimal_routes_with_duplicate.begin(),
      equally_optimal_routes_with_duplicate.end(),
      [&first_lanes_and_heading_diff_in_junction](const RouteInfo& route_info) {
        PncLaneAndAssociatedHeadingDiff lane_and_heading_diff =
            FindFirstLanePtrInConflictingRegionAlongRoute(route_info);
        if (lane_and_heading_diff.lane_ptr != nullptr) {
          first_lanes_and_heading_diff_in_junction.push_back(
              lane_and_heading_diff);
        }
      });

  // Do not handle the case when there are any tied-optimal routes not going
  // through a junction or region with conflicting lanes.
  if (first_lanes_and_heading_diff_in_junction.size() !=
      equally_optimal_routes_with_duplicate.size()) {
    return selected_route_index;
  }

  // Select the most likely route based off of heading diff.
  double min_abs_heading_diff = std::numeric_limits<double>::infinity();
  for (size_t idx = 0UL; idx < first_lanes_and_heading_diff_in_junction.size();
       ++idx) {
    const double abs_heading_diff =
        first_lanes_and_heading_diff_in_junction[idx].heading_diff;
    if (abs_heading_diff < min_abs_heading_diff) {
      min_abs_heading_diff = abs_heading_diff;
      selected_route_index = idx;
    }
  }

  return selected_route_index;
}

PredictedTrajectoryRouteAssociator::PncLaneAndAssociatedHeadingDiff
PredictedTrajectoryRouteAssociator::
    FindFirstLanePtrInConflictingRegionAlongRoute(const RouteInfo& route_info) {
  const pnc_map::Lane* ret_lane = nullptr;
  double heading_diff = std::numeric_limits<double>::infinity();
  for (const MapElementAndPoseInfo* map_element_info_ptr : route_info) {
    if (map_element_info_ptr->type == pb::MapElementType::LANE) {
      const pnc_map::Lane* lane_along_route = map_element_info_ptr->lane_ptr;
      if (lane_along_route->IsInJunction() ||
          !lane_along_route->conflicting_lanes().empty()) {
        ret_lane = lane_along_route;
        heading_diff = map_element_info_ptr->heading_diff_opt.value_or(
            std::numeric_limits<double>::infinity());
        break;
      }
    }
  }
  return {ret_lane, heading_diff};
}

void PredictedTrajectoryRouteAssociator::
    GeneratePartialRouteInferredByTheFirstPose() {
  TRACE_EVENT_SCOPE(planner, BP_RouteAssociation_Post_Partial);
  if (raw_equally_optimal_routes_.empty()) {
    return;
  }
  const size_t selected_route_index =
      SelectMostLikelyRouteIndex(raw_equally_optimal_routes_);
  const RouteInfo& selected_unique_equally_optimal_route =
      raw_equally_optimal_routes_[selected_route_index];
  if (selected_unique_equally_optimal_route.empty()) {
    return;
  }
  DCHECK_EQ(selected_unique_equally_optimal_route.size(), 1UL);
  partial_route_inferred_by_the_first_pose_opt_ =
      *selected_unique_equally_optimal_route.front();
  return;
}

void PredictedTrajectoryRouteAssociator::GenerateMostLikelyRoute(
    const PredictedTrajectoryWrapper& predicted_trajectory) {
  TRACE_EVENT_SCOPE(planner, BP_RouteAssociation_Post);
  most_likely_route_.reserve(predicted_trajectory.size());
  most_likely_route_with_duplicate_.reserve(predicted_trajectory.size());
  if (raw_equally_optimal_routes_.empty()) {
    return;
  }

  // Bridging broken sequence in routes.
  for (RouteInfo& raw_route : raw_equally_optimal_routes_) {
    ConnectingDisjointElementsInRoute(predicted_trajectory, raw_route);
  }

  const size_t selected_route_index =
      SelectMostLikelyRouteIndex(raw_equally_optimal_routes_);

  const RouteInfo& selected_equally_optimal_route_with_duplicate =
      raw_equally_optimal_routes_[selected_route_index];

  const RouteInfo selected_unique_equally_optimal_route =
      RemoveDuplicateElementsAlongRawRoute(
          selected_equally_optimal_route_with_duplicate);
  if (selected_unique_equally_optimal_route.empty()) {
    return;
  }
  std::for_each(selected_unique_equally_optimal_route.begin(),
                selected_unique_equally_optimal_route.end(),
                [this](const MapElementAndPoseInfo* element_info_ptr) {
                  this->most_likely_route_.push_back(*element_info_ptr);
                });
  std::for_each(
      selected_equally_optimal_route_with_duplicate.begin(),
      selected_equally_optimal_route_with_duplicate.end(),
      [this](const MapElementAndPoseInfo* element_info_ptr) {
        this->most_likely_route_with_duplicate_.push_back(*element_info_ptr);
      });
  return;
}

// Convert results into debug infos.
AssociationDebugProto PredictedTrajectoryRouteAssociator::ToProto() const {
  AssociationDebugProto debug_proto;
  pb::RawLookUpTableDebug* raw_lookup_table_proto =
      debug_proto.mutable_raw_lookup_table();

  debug_proto.set_object_id(object_id_);
  debug_proto.set_trajectory_id(trajectory_id_);
  if (latest_isolated_lane_info_in_seed_opt_.has_value()) {
    pb::OptimalSourceElementInfo* debug_latest_element_info_ptr =
        debug_proto.add_latest_isolated_map_elements();
    debug_latest_element_info_ptr->set_is_lane(
        latest_isolated_lane_info_in_seed_opt_->type ==
        pb::MapElementType::LANE);
    debug_latest_element_info_ptr->set_optimal_source_id(
        latest_isolated_lane_info_in_seed_opt_->element_id);
  }
  if (latest_exit_zone_info_in_seed_opt_.has_value()) {
    pb::OptimalSourceElementInfo* debug_latest_element_info_ptr =
        debug_proto.add_latest_isolated_map_elements();
    debug_latest_element_info_ptr->set_is_lane(
        latest_exit_zone_info_in_seed_opt_->type == pb::MapElementType::LANE);
    debug_latest_element_info_ptr->set_optimal_source_id(
        latest_exit_zone_info_in_seed_opt_->element_id);
  }
  DCHECK_EQ(sampled_pose_infos_.size(),
            raw_association_cost_lookup_table_.size());
  // Populate debug infos on raw look up table.
  for (size_t idx = 0UL; idx < sampled_pose_infos_.size(); ++idx) {
    pb::SampledPoseAssociationInfoDebug* sampled_pose_association_infos_proto =
        raw_lookup_table_proto->add_sampled_pose_association_infos();
    sampled_pose_association_infos_proto->set_sampled_pose_index_in_trajectory(
        sampled_pose_infos_[idx].sampled_pose_index_in_trajectory);

    for (const TargetElementAssociationResult&
             target_element_transition_result :
         raw_association_cost_lookup_table_.at(idx)) {
      pb::TargetElementAssociationResultDebug*
          target_element_transition_result_debug_proto =
              sampled_pose_association_infos_proto
                  ->add_target_element_association_results();
      target_element_transition_result_debug_proto
          ->set_min_association_cost_so_far(
              target_element_transition_result.min_association_cost_so_far);
      target_element_transition_result_debug_proto->set_target_id(
          target_element_transition_result.target_element_info_ptr->element_id);
      target_element_transition_result_debug_proto->set_is_lane(
          target_element_transition_result.target_element_info_ptr->type ==
          pb::MapElementType::LANE);

      for (const TargetElementAssociationResult* optimal_result_ptr :
           target_element_transition_result.optimal_source_element_results) {
        const MapElementAndPoseInfo& optimal_source_element_info =
            *optimal_result_ptr->target_element_info_ptr;
        pb::OptimalSourceElementInfo* optimal_source_element_info_proto =
            target_element_transition_result_debug_proto
                ->add_optimal_source_elements();
        optimal_source_element_info_proto->set_optimal_source_id(
            optimal_source_element_info.element_id);
        optimal_source_element_info_proto->set_is_lane(
            optimal_source_element_info.type == pb::MapElementType::LANE);
        if (optimal_source_element_info.distance_opt.has_value()) {
          optimal_source_element_info_proto->set_distance(
              optimal_source_element_info.distance_opt.value());
        }
        if (optimal_source_element_info.heading_diff_opt.has_value()) {
          optimal_source_element_info_proto->set_heading_diff(
              optimal_source_element_info.heading_diff_opt.value());
        }
      }
    }
  }
  // Populate debug infos on raw equally optimal routes.
  for (const RouteInfo& route : raw_equally_optimal_routes_) {
    pb::OptimalElementSequence* add_routes_proto =
        debug_proto.mutable_equally_optimal_routes()->add_routes();
    std::for_each(
        route.begin(), route.end(),
        [add_routes_proto](const MapElementAndPoseInfo* info_ptr) {
          add_routes_proto->add_element_id_along_route(info_ptr->element_id);
          add_routes_proto->add_element_type_along_route(info_ptr->type);
        });
  }
  // Populate debug info of route.
  if (partial_route_inferred_by_the_first_pose_opt_.has_value()) {
    DCHECK(most_likely_route_.empty());
    debug_proto.set_is_only_current_pose_inferred(true);
    pb::OptimalElementSequence* current_pose_inferred_route_proto =
        debug_proto.mutable_current_pose_inferred_route();
    current_pose_inferred_route_proto->add_element_id_along_route(
        partial_route_inferred_by_the_first_pose_opt_.value().element_id);
    current_pose_inferred_route_proto->add_element_type_along_route(
        partial_route_inferred_by_the_first_pose_opt_.value().type);
  } else {
    pb::OptimalElementSequence* most_likely_route_proto =
        debug_proto.mutable_most_likely_route();
    std::for_each(
        most_likely_route_.begin(), most_likely_route_.end(),
        [most_likely_route_proto](const MapElementAndPoseInfo& element_info) {
          most_likely_route_proto->add_element_id_along_route(
              element_info.element_id);
          most_likely_route_proto->add_element_type_along_route(
              element_info.type);
        });
  }

  return debug_proto;
}

void PredictedTrajectoryRouteAssociator::
    GetOrCreateMultiPredictedTrajectoryRouteAssociationDebug(
        planner::pb::AgentPredictedTrajectoryRoutesDebug*
            agent_association_debug_ptr) const {
  if (agent_association_debug_ptr != nullptr) {
    pb::MultiPredictedTrajectoryRouteAssociationDebug&
        predicted_trajectory_route_association_debugs_proto =
            (*agent_association_debug_ptr
                  ->mutable_agent_predicted_trajectory_routes())[object_id_];
    *(predicted_trajectory_route_association_debugs_proto
          .add_predicted_trajectory_route_association_debugs()) = ToProto();
  }
  return;
}

}  // namespace route_association
}  // namespace planner
