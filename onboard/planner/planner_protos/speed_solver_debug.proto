syntax = "proto3";

package planner.speed.pb;

import "planner_protos/risk_model.proto";
import "planner_protos/speed_profile.proto";
import "planner_protos/speed_constraint.proto";
import "planner_protos/speed_decision.proto";
import "planner_protos/speed_generator_config.proto";
import "planner_protos/speed_limiters.proto";
import "planner_protos/speed_seed.proto";
import "planner_protos/speed_solver_enum.proto";
import "planner_protos/speed_timemap.proto";
import "planner_protos/speed_yielding_fence.proto";

// This file contains debug info for the speed profile in the speed generator.

// NEXT Available ID: 16
message PerDiscomfortDebug {
  // Message stores dominant constraint at each discomfort in each iteration of
  // the Search function.
  int32 discomfort_idx = 1;
  double discomfort_level = 2;
  // The earliest brake time ix during speed search. The constraint to yield for
  // this brake is the dominant constraint.
  int32 earliest_brake_time_ix = 11;
  int32 dominant_constraint_ix = 3;
  // The earliest accel time index to adjust the profile for pass. Here are the
  // possible values:
  // a. -1, it means that this solution fails;
  // b. 0, it means the data is at default value and it is not populated (This
  // should be for legacy bag);
  // c. 80, it means that AdjustForPass is triggered, but not successful;
  // d. INT_MAX: it means that AdjustForPass is never triggered for this
  // search solution;
  // e. t ∈ (0, 80), it means that AdjustForPass is triggered,
  // and the earliest accel time ix = t. Usually this value should not be
  // larger than 10 when UB/HB may occur.
  int32 earliest_accel_time_ix_for_adjust_for_pass = 14;
  bool success = 4;
  SpeedDiversityOption diversity_option = 12;
  // The initial decisions of each constraint at this discomfort.
  repeated SpeedDecision initial_decisions = 5;
  // Debug of the IDP decision making at this discomfort level.
  InitialDecisionProviderDebug idp_debug = 13;
  // The profile for each iteration.
  repeated IterativeProfile iterative_profiles = 6;
  // True if the profile needs to be updated due to no block constraint.
  bool no_block_need_update = 7;
  // Continuous slow speed duration in no block.
  double slow_v_duration_in_no_block = 8;
  // The time map at this discomfort.
  TimeMap time_map = 9;
  // TODO(Tingran): Add risk time map.
  // Reason for increasing the discomfort level.
  // NEXT Available ID: 11
  enum DiscomfortIncreaseSource {
    kNA = 0;
    // We failed to set no pass decision to a no-pass-ignore constraint which
    // is:
    // 1. a no yield constraint with NA yield_option in constraint settings;
    // 2. a gap align constraint with pass as current decision;
    // 3. a yield-impossible with current discomfort larger than
    // yield_if_possible_below_discomfort.
    kSetNoPassFailure = 1;
    // Cannot brake to avoid conflict at current discomfort level.
    kCannotBrake = 2;
    // Cannot achieve the minimum speed limit set by the limiters.
    kCannotReachMinV = 3;
    // There is a constraint that has discomfort_for_progress value
    // greater than the current discomfort level and we yield to it.
    kForProgress = 4;
    // During tree search, the algorithm runs for max number of iterations and
    // cannot converge.
    kTreeSearchFailDueToMaxIteration = 5;
    // During adjusting no block constraints, the algorithm runs for max number
    // of iterations and cannot converge.
    kAdjustNoBlockFailDueToMaxIteration = 6;
    // The initial profile provided by the reference generator regulation
    // already has an unresolvable conflict with a constraint.
    kInitialProfileUnresolvableConflict = 7;
    // In a gap align problem, a gap align constraint that was not allowed to be
    // ignored is ignored by IDP due to neither pass nor yield is feasible.
    kInfeasibleGapAlign = 8;
    // IDP fails to generate a no-pass decision for a constraint that we intend
    // to yield with yield diversity.
    kInfeasibleYieldDiversity = 9;
    // Initial accel is out of current discomfort level's limits.
    kInitialStateOutOfDiscomfortLimits = 10;
  }
  DiscomfortIncreaseSource discomfort_increase_source = 10;
  // Indicate if this solution should be accepted according to its speed
  // diversity options and the existing search solutions from previous per
  // discomfort searches.
  bool should_accept = 15;
}

// NEXT Available ID: 13
message IterativeProfile {
  bool adjust_for_tree_branch = 11;
  // Result profile for this iteration.
  Profile result_profile = 1;
  // Index of the constraint which causes the profile to adjust.
  int64 conflict_constraint_ix = 2;
  // ID of the constraint which causes the profile to adjust.
  string conflict_constraint_id = 3;
  // The decision of the conflict constraint.
  SpeedDecision conflict_decision = 10;
  // The time ix of the conflict before adjustment.
  double t_conflict = 4;
  // The x of the conflict before adjustment.
  double x_conflict = 5;
  // The time ix to adjust the conflict.
  double t_brake = 6;
  // The new x after adjustment at t_conflict.
  double x_resolve = 7;
  // NEXT Available ID: 12
  enum AdjustType {
    // Default value which means the adjust type is unknown.
    kUnknown = 0;
    // The first profile which is the ref profile before adjusting for conflict.
    kInitiateFromRefProfile = 9;
    // Change from not decied to yield decision.
    kNotDecidedToYield = 1;
    // Slow down for yield constraints.
    kBrakeForYield = 2;
    // Change from pass to ignore decision.
    kPassToIgnore = 3;
    // Stay ignore decision.
    kStayIgnore = 4;
    kResolveNotDecided = 5;
    // Update tree/main profile to its corresponding profiles for a new switch
    // time is updated.
    kUpdateProfileForNewSwitchTime = 6;
    // Update tree/main profile to its corresponding profiles for a brake
    // happens before the switch time.
    kUpdateProfileForBrakeBeforeSwitchTime = 7;
    // The profile is updated to accelerate toward the reference profile.
    kAccelTowardReferenceProfile = 8;
    // Adjust the profile to resolve a pass conflict by earliest accel from
    // brake profile.
    kAdjustProfileForPassByEarliestAccelFromBrakeProfile = 10;
    // Fail to adjust the profile to resolve a pass conflict by earliest accel
    // from brake profile.
    kFailToAdjustProfileForPassByEarliestAccelFromBrakeProfile = 11;
  }
  AdjustType adjust_type = 8;
  // The decisions for this profile.
  repeated SpeedDecision curr_decisions = 9;
  // The switch time for this profile. It is only meaningful for tree search
  // when consolidating profiles from another branch and accelerating toward ref
  // profile for adjustment in this iteration.
  int64 tree_switch_time_ix = 12;
}

// NEXT Available ID: 9
// Initial decision debug for one single constraint.
message InitialDecisionDebug {
  bool can_pass = 1;
  bool can_yield = 2;
  SpeedDecision initial_decision = 3;

  Profile pass_profile = 4;
  Profile yield_profile = 5;
  GapAlignInfo gap_align_info = 6;

  // The actually-used gap align max tive time in IDP
  // for the constraint. It could be either from the
  // constraint setting (if it is set to a valid value)
  // or default global setting.
  double gap_align_pass_max_active_time = 7;
  double gap_align_yield_max_active_time = 8;
}

// NEXT Available ID: 4
message InitialDecisionProviderDebug {
  // Initial decision debug info for all constraints. Index align with
  // constraint index.
  repeated InitialDecisionDebug initial_decision_debugs = 1;
  double gap_align_pass_max_active_time = 2 [ deprecated = true ];
  double gap_align_yield_max_active_time = 3 [ deprecated = true ];
}

// NEXT Available ID: 4
message GapDiversitySettings {
  int32 extra_gap_amount_slower_than_default_gap = 1;
  int32 extra_gap_amount_faster_than_default_gap = 2;
  double delta_discomfort_for_gap_diversity = 3;
}

// A pb message for GlobalSpeedSolverSettings. For detailed definition people
// can refer to the structure definition.
// NEXT Available ID: 13
message GlobalSpeedSolverSettings {
  double global_discomfort_for_progress = 1;
  bool progress_for_kinematic = 2;
  double gap_align_max_discomfort = 3;
  double estimated_half_cross_lane_duration_in_second = 9;
  double global_gap_align_pass_max_active_time = 4;
  double global_gap_align_yield_max_active_time = 5;
  bool globally_allow_emergency_brake = 6;
  bool enable_extra_diversity_by_risk_evaluator = 7;
  GapDiversitySettings gap_diversity_settings = 8;
  bool attraction_for_progress = 10;
  bool disable_speed_attractor = 11;
  bool enable_opt_profile_validation = 12;
}

// The conflict evaluation result of one conflict resolving candidate.
// NEXT Available ID: 7
message ConflictEvaluationResult {
  // A value between [0, inf) capturing the total conflict severity
  // of the candidate. A larger value indicates stronger conflict.
  double severity_score = 1;
  // Time to collision of the captured conflict.
  double ttc = 2;
  // The distance that we needs to stay free of conflict (for pass, we
  // need to pass more, for yield, we need to yield more). It is a
  // non-negative value if a conflict actual exists. A larger value
  // indicates stronger conflict.
  double max_displacement_wo_conflict = 5;
  // The ego speed when the captured conflict occurs.
  double ego_conflicting_speed = 3;
  // The absolute agent speed (along ego path) when the captured conflict
  // occurs.
  double abs_agent_longitudinal_conflicting_speed = 4;
  // The constraint index corresponding to this conflict.
  int32 c_ix = 6;
}

// The debug information of a conflict resolving candidate.
// NEXT Available ID: 12
message ConflictResolvingCandidateDebug {
  ConflictResolvingType.Enum candidate_type = 1;
  repeated int32 violated_constraint_ixs = 2;
  // Decisions corresponding to the above violated constraints.
  repeated SpeedDecision violated_constraint_decisions = 11;
  int32 earliest_conflict_cix = 3;
  double collision_risk = 8;
  int32 max_risk_cix = 6;
  double ttc_from_risk_model = 7;
  // Multiple collision frames from different perturb trajectories.
  repeated planner.pb.CollisionFrameInfo collision_risk_infos = 5;
  // One-to-one corresponding perturb types to the above collision
  // risk infos.
  repeated planner.pb.PerturbType.Enum perturb_types = 9;
  ConflictEvaluationResult eval_result = 4;
  Profile profile = 10;
}

// NEXT Available ID: 8
message ConflictResolverDebug {
  // The SCR candidate type that was executed in the last planning cycle.
  ConflictResolvingType.Enum previous_result = 2;
  // True if we skip the conflict evaluation and early return.
  bool early_return_without_calculation = 3;
  optional string extra_debug_str = 4 [ deprecated = true ];
  optional string st_eval_debug_str = 6;
  optional string risk_eval_debug_str = 5;
  // The min range used in SCR st evaluation.
  MinRange min_range = 7;
  repeated ConflictResolvingCandidateDebug candidates = 1;
}

// NEXT available ID: 9
message RiskMitigationDebug {
  double risk_discomfort = 1;
  int32 risk_switch_time_ix = 2;
  // True if the main profile is adjusted before switch time due to earlier
  // brake in risk branch profile search.
  bool risk_mitigation_active = 8;

  // This is the highest speed profile that ego could reach.
  // It is generated using profile_limit that has different limits before and
  // after switch time.
  Profile risk_max_speed_profile = 3;
  Profile risk_profile = 4;
  // This is the lowest speed profile that ego could reach.
  // It is generated using profile_limit that has different limits before and
  // after switch time.
  Profile risk_min_speed_profile = 5;
  repeated SpeedDecision risk_decisions = 6;
  TimeMap risk_time_map = 7;
}

// NEXT Available ID: 2
message DiscomfortForProgressDebug {
  // NEXT Available ID: 6
  message ProblemDiffDebug {
    double prev_problem_discomfort = 1;
    double curr_problem_discomfort = 2;
    repeated int32 decision_changed_constraint_ix = 3;
    repeated SpeedDecision prev_decision = 4;
    repeated SpeedDecision curr_decision = 5;
  }
  repeated ProblemDiffDebug problem_diff_debug = 1;
}

// NEXT Available ID: 2
message SpeedDiversityDebug {
  DiscomfortForProgressDebug discomfort_for_progress_debug = 1;
  // other types of diversity debug can be added here.
}

// NEXT Available ID: 30
message SpeedSearchDebug {
  reserved 15;
  reserved "gap_align_info_map";

  ReferenceProfile selected_reference = 1;
  // Selected discomfort level's longitudinal kinematic limits.
  LongitudinalKinematicLimitsForDiscomfortLevel
      selected_longitudinal_kinematic_limits = 12;
  SearchResult.Enum search_result = 2;
  bool is_coming_to_stop = 28;
  string stay_stop_debug_str = 29;
  ConflictResolvingType.Enum conflict_resolving_type = 22;
  ConflictResolverDebug conflict_resolver_debug = 23;
  SpeedDiversityOption diversity_option = 17;
  Profile search_profile = 3;
  repeated Constraint constraints = 4;
  repeated SpeedDecision decisions = 5;
  TimeMap time_map = 6;
  // Speed limits from road_limiter, lateral kinetic limiters,
  // cautious driving limiter and slow region limiter.
  SpeedLimiters speed_limiters = 7;
  // Reference profile speeds at the selected discomfort value.
  SpeedLimitsAtDiscomfort reference_profile_limits = 8;
  int32 earliest_brake_time_ix = 21;
  int32 dominant_constraint_ix = 9;
  // The earliest object that this speed profile that brakes for or has conflict
  // with. -1 means such object doesn't exist.
  int64 dominant_constraint_obj_id = 16;
  repeated PerDiscomfortDebug discomforts = 10;
  // Min speed bound profile.
  Profile min_speed_profile = 11;
  // Discomforts debug for gap align search.
  // Non gap align and gap align search results may have different decisions,
  // time map, dominant constraint, iterative profiles, etc.
  // So it is better to store the results into separate fields.
  // This also keeps the debug population logic clean.
  repeated PerDiscomfortDebug gap_align_discomforts = 13;
  // When LC preparation is requested, speed solver will prepare this field
  // which contains the searched gap.
  SpeedSolverLcGuideSeed lc_guide_seed = 14;
  TreeSearchType.Enum tree_search_type = 24;
  int32 tree_switch_time_ix = 25;
  // Debug for IDP corresponding to the selected solution.
  InitialDecisionProviderDebug initial_decision_provider_debug = 18;
  // The min range at selected discomfort.
  MinRange min_range = 19;
  // The global speed solver settings.
  GlobalSpeedSolverSettings global_speed_solver_settings = 20;
  optional RiskMitigationDebug risk_mitigation_debug = 26;
  SpeedDiversityDebug speed_diversity_debug = 27;
}

// NEXT Available ID: 6
message LongitudinalKinematicLimitsForDiscomfortLevel {
  int32 discomfort_level_ix = 1;
  // The acceleration rate limit in a braking process.
  Bound brake_a = 2;  // m/s^2
  // The acceleration rate limit in an acceleration process.
  Bound accel_a = 3;  // m/s^2
  // The jerk limit in a braking process.
  Bound brake_j = 4;  // m/s^3
  // The jerk limit in an acceleration process.
  Bound accel_j = 5;  // m/s^3
}

// Next Available ID: 3
message MinRange {
  // The pass min range at selected discomfort.
  double pass_min_range = 1;
  // The yield min range at selected discomfort.
  double yield_min_range = 2;
}

// NEXT Available ID: 22
message SingleIterationCost {
  // Message stores the costs in one iteration of the lqr solver.
  double total_cost = 1;
  double state_barrier_cost = 2;
  double state_repeller_cost = 21;
  double state_static_range_cost = 3;
  double accel_barrier_cost = 4;
  double jerk_barrier_cost = 5;
  double min_speed_barrier_cost = 6;
  double max_speed_barrier_cost = 7;
  double accel_effort_cost = 8;
  double jerk_effort_cost = 9;
  double lateral_accel_effort_cost = 14;
  reserved 15;
  reserved "lateral_jerk_effort_cost";
  double speed_attractor_cost = 10;
  double length_attractor_cost = 11;
  double follower_attractor_cost = 12;
  double jerk_attractor_cost = 18;
  double dynamic_range_cost = 13;
  double pass_dynamic_range_cost = 17;
  double proximity_speed_cost = 16;
  double max_speed_repeller_cost = 19;
  double lateral_jerk_repeller_cost = 20;
}

// Debug for state boundary at one state. Since proto needs to be backward
// compatible, the previous old message type StateBarrierDebug are retained.
// NEXT Available ID: 3
message StateBarrierDebug {
  // The largest pass barrier position among all the pass barriers at the
  // state.
  optional double largest_pass_x = 1;
  // The small yield barrier position among all the yield barriers at the state.
  optional double smallest_yield_x = 2;
}

// Debug for static ranges at one state.
// NEXT Available ID: 6
message StaticRangeDebug {
  // Static range positions for pass objects at the state.
  repeated double pass_x = 1;
  // Static range positions for yield objects at the state.
  repeated double yield_x = 2;
  reserved 3;
  reserved "is_softer";
  // Static range positions for softer pass objects at the state.
  repeated double pass_x_softer = 4;
  // Static range positions for softer yield objects at the state.
  repeated double yield_x_softer = 5;
}

// Debug for dynamic ranges at one state.
// NEXT Available ID: 7
message DynamicRangeDebug {
  // Positions and related stop positions for pass objects at the state.
  repeated double pass_x = 1;
  repeated double pass_stop_x = 2;
  // The estimated max distance used to normalize pass dynamic range distance.
  repeated double pass_nominal_max_diff = 5;

  // Positions and related stop positions for yield objects at the state.
  repeated double yield_x = 3;
  repeated double yield_stop_x = 4;
  // The estimated max distance used to normalize yield dynamic range distance.
  repeated double yield_nominal_max_diff = 6;
}

// Debug for one proximity speed constraint state.
// NEXT Available ID: 5
message ProximitySpeedStateDebug {
  int32 time_ix = 1;
  double start_x = 2;
  double end_x = 3;
  double speed_limit = 4;
}

// Debug for one proximity speed constraint.
// NEXT Available ID: 6
message ProximitySpeedConstraintDebug {
  reserved 2, 3;
  reserved "pass_buffer_dist", "yield_buffer_dist";
  string unique_constraint_id = 1;
  repeated ProximitySpeedStateDebug states = 4;
  // True for speed lower bound and false for speed upper bound.
  bool sign = 5;
}

// Debug for following attractor at one state.
// NEXT Available ID: 3
message FollowingAttractorDebug {
  int32 state_ix = 1;
  double x = 2;
}

// Debug for potentials in speed optimization.
// NEXT Available ID: 9
message SpeedOptPotentialDebug {
  // State barrier debug, the size equals to the size of states.
  repeated StateBarrierDebug state_barriers = 1;
  // State repeller debug, the size equals to the size of states.
  repeated StateBarrierDebug state_repellers = 7;
  // Static range debug, the size equals to the size of states.
  repeated StaticRangeDebug static_ranges = 2;
  // Dynamic range debug, the size equals to the size of states.
  repeated DynamicRangeDebug dynamic_ranges = 3;
  // The assumed constant ego deceleration for dynamic range.
  double ego_sudden_decel = 4;
  // Proximity speed constraint debug, the size equals to the number of
  // constraint.
  repeated ProximitySpeedConstraintDebug proximity_speed_constraints = 5;
  // Following attractor debug, the size is less than or equal to the
  // size of states.
  repeated FollowingAttractorDebug following_attractors = 6;
  // Position attractor debug, the size is less than or equal to the
  // size of states.
  repeated FollowingAttractorDebug position_attractors = 8;
}

// Debug for warm starts.
// NEXT Available ID: 5
message SpeedOptWarmStartDebug {
  // Costs debug for difference warm starts.
  repeated SingleIterationCost warm_start_costs = 1;
  int32 selected_warm_start_index = 2;
  string debug_str = 3;
  Profile last_profile = 4;
}

// Iteration meta info for the ilqr solver, including cost reduction, line
// search status, etc.
// NEXT Available ID: 10
message ILqrSolverIterationMeta {
  double initial_cost = 1;
  double potential_cost_reduction = 2;
  double potential_rel_cost_reduction = 3;
  // Line search debug.
  bool line_search_success = 4;
  int32 line_search_iteration = 5;
  double optimal_alpha = 6;
  double actual_cost_reduction = 7;
  double actual_rel_cost_reduction = 8;
  double regularization_param_for_next_iter = 9;
}

// Debug for optimizer settings.
// NEXT Available ID: 6
message SpeedOptSettingDebug {
  bool stay_stopped = 1;
  bool enable_fast_acceleration = 2;
  double desired_stopping_x = 3;
  bool enable_profile_validation = 4;
  bool disable_speed_attractor = 5;
}

// NEXT Available ID: 10
message SpeedOptDebug {
  SpeedOptResult.Enum optimization_result = 1;
  Profile optimized_profile = 2;
  string debug_str = 8;

  // Setting related debug.
  SpeedOptSettingDebug setting_debug = 9;

  // Potentials related debug.
  SpeedOptPotentialDebug potential_debug = 5;

  // Warm start related debug.
  SpeedOptWarmStartDebug warm_start_debug = 7;

  // Per iteration debug.
  repeated SingleIterationCost all_iter_costs = 3;
  repeated Profile all_iter_profiles = 4;
  repeated ILqrSolverIterationMeta solver_iter_meta = 6;
}

// Message stands for the debug info for speed search and optimization,
// including the selected reference with discomfort index and selected
// discomfort index, search result, profile found by speed searcher,  the
// final profile given by optimizer, constraints and decisions.
// NEXT Available ID: 12
message SpeedSolverDebug {
  reserved 7;
  SpeedSearchDebug search_debug = 1;
  SpeedOptDebug opt_debug = 2;
  SolverResult.Enum solver_result = 3;
  double plan_init_time_in_sec = 4;
  double ra_to_fb_shift = 9;
  double ra_to_rb_shift = 10;
  double ego_max_speed = 11;
  FenceList speed_yielding_fences = 5;
  Profile final_profile = 6;
  Profile intention_selected_profile = 8;
}
