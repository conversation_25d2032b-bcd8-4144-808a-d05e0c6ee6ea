#include "planner/ops_warning/ops_warning_utility.h"

#include <algorithm>
#include <limits>
#include <unordered_map>
#include <vector>

#include "av_comm/calib_config.h"
#include "av_comm/calib_config/vehicle_config.h"
#include "av_comm/car_id.h"
#include "geometry/model/oriented_box.h"
#include "math/math_util.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_wrapper.h"
#include "planner/ops_warning/st_agent/st_agent.h"
#include "planner/ops_warning/st_agent/st_agent_utility.h"
#include "trace/trace.h"
#include "voy_trace/trace_planner.h"

namespace planner {
namespace ops_warning {

namespace {
// The minimum acceleration that the robot can take.
constexpr double kMinAccelerationMpss = -5.0;
// The reaction time of a human driver in US.
constexpr int64_t kUsOpsReactionTimeInMSec = 500;
// The reaction time of a human driver in CN.
constexpr int64_t kCnOpsReactionTimeInMSec = 1100;
// The minimum acceleration that a human driver can take.
constexpr double kMinTrailingVehicleAccelerationInMpss = -8.0;
constexpr int64_t kTotalSamplingTimeInMSec = 5000;
// The time resolution used to do sampling.
constexpr int64_t kSamplingTimeResolutionInMSec = 200;
// The minimum heading difference to ignore.
constexpr double kMinHeadingDifferenceToIgnoreInRad = M_PI_2;
// The low speed of robot.
constexpr double kRobotLowSpeedInMSec = 4.5;
// The super low speed of robot.
constexpr double kRobotSuperLowSpeedInMSec = 2.0;
// Distance threshold to determine ego car is too close to hard boundary.
constexpr double kNearHardBoundaryDistanceThresholdInMeter = 0.2;

// Returns true if traffic participant is not behind robot or it's heading
// difference reaches the kMinHeadingDifferenceToIgnoreInRad.
bool ShouldIgnore(const voy::Pose& curr_pose,
                  const PredictedPose& first_predicted_pose) {
  const auto global_to_car_center = math::GlobalToCarCenter(curr_pose);
  const glm::dvec4 point =
      global_to_car_center * glm::dvec4(first_predicted_pose.center().x(),
                                        first_predicted_pose.center().y(),
                                        curr_pose.z(), 1.);
  const double heading = math::NormalizeMinusPiToPi(
      first_predicted_pose.heading() - curr_pose.yaw());
  return point.x > 0 || std::fabs(heading) > kMinHeadingDifferenceToIgnoreInRad;
}

// Gets the poses from the nearest point to end.
pb::TrajectoryPose GetClosestPoses(const voy::Pose& curr_pose,
                                   const pb::Trajectory& trajectory) {
  // Finds the nearest point.
  const auto nearest_iterator = std::min_element(
      trajectory.poses().begin(), trajectory.poses().end(),
      [&curr_pose](const pb::TrajectoryPose& first,
                   const pb::TrajectoryPose& second) {
        const double first_pt_time_delta =
            std::abs(first.timestamp() - curr_pose.timestamp());
        const double second_pt_time_delta =
            std::abs(second.timestamp() - curr_pose.timestamp());
        return first_pt_time_delta < second_pt_time_delta;
      });
  DCHECK(nearest_iterator != trajectory.poses().end());
  return *nearest_iterator;
}

STAgentParam GenerateAgentParamForPredictedTrajectory(
    const PredictedTrajectoryWrapper& predicted_trajectory) {
  pb::Trajectory trajectory;
  const auto& predicted_poses =
      predicted_trajectory.predicted_poses(/*use_legacy_logic=*/true);

  for (const auto& pose : predicted_poses) {
    pb::TrajectoryPose& trajectory_pose = *trajectory.add_poses();
    trajectory_pose.set_x_pos(pose.center().x());
    trajectory_pose.set_y_pos(pose.center().y());
    trajectory_pose.set_z_pos(0.0);
    trajectory_pose.set_heading(pose.heading());
    // There is no accel info in the TrafficParticipantPose class.
    trajectory_pose.set_accel(0.0);
    trajectory_pose.set_speed(pose.speed());
    trajectory_pose.set_odom(pose.odom());
    trajectory_pose.set_timestamp(pose.timestamp());
  }
  return {trajectory, predicted_poses.begin()->width(),
          predicted_poses.begin()->length()};
}

bool HasCollideWithTrafficParticipant(const voy::Pose& curr_pose,
                                      const STAgent& ego_car_agent,
                                      const PlannerObject& planner_object,
                                      int64_t reaction_time) {
  // The sample info for traffic participant.
  const std::vector<OdomSampleInfo> kTrafficParticipantSampleInfo = {
      {reaction_time, 0.0},
      {kTotalSamplingTimeInMSec, kMinTrailingVehicleAccelerationInMpss}};
  for (const auto& predicted_trajectory : planner_object.predicted_trajectories(
           /*use_filtered_trajectories=*/true)) {
    if (ShouldIgnore(curr_pose, predicted_trajectory
                                    ->predicted_poses(/*use_legacy_logic=*/true)
                                    .front())) {
      continue;
    }
    STAgent traffic_participant_agent(
        GenerateAgentParamForPredictedTrajectory(*predicted_trajectory));
    traffic_participant_agent.UpdateOdomSamples(kTrafficParticipantSampleInfo,
                                                ego_car_agent.base_time_ms(),
                                                kSamplingTimeResolutionInMSec);

    if (ego_car_agent.Collide(traffic_participant_agent)) {
      return true;
    }
  }
  return false;
}

int64_t DetermineReactionTime(double speed, double accel) {
  const av_comm::CarId& car_id = av_comm::CarId::Get();

  if (speed < kRobotSuperLowSpeedInMSec ||
      (speed < kRobotLowSpeedInMSec && accel < 0.0)) {
    // There are two conditions we need to set the reaction time to 0
    // 1: Robot is driving in super low speed.
    // 2: Robot is driving in low speed and robot is is slowing down.
    return 0;
  } else {
    if (car_id.region() == av_comm::CarRegion::kUS) {
      return kUsOpsReactionTimeInMSec;
    } else {
      return kCnOpsReactionTimeInMSec;
    }
  }
}

// Computes squared distance from ego bounding box to a hard road boundary.
double ComparableDistanceFromEgoToHardRoadBoundary(
    const std::vector<math::geometry::Polyline2d>& boundary_lines,
    const math::geometry::OrientedBox2d& ego_bounding_box) {
  double min_clearance_sq = std::numeric_limits<double>::max();
  for (const auto& boundary_line : boundary_lines) {
    const double clearance_sq =
        math::geometry::ComparableDistance(ego_bounding_box, boundary_line);
    min_clearance_sq = std::min(clearance_sq, min_clearance_sq);
  }

  return min_clearance_sq;
}

}  // namespace

bool CheckRearEnding(
    const voy::Pose& curr_pose,
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const pb::Trajectory& trajectory) {
  TRACE_EVENT_SCOPE(planner, OpsWarning_CheckRearEnding);

  const calibration::VehicleConfig& vehicle_conf =
      calibration::CalibConfig::GetInstance()->vehicle();

  // Generates STAgent for ego car.
  const pb::TrajectoryPose nearest_pose =
      GetClosestPoses(curr_pose, trajectory);
  STAgent ego_car_agent({trajectory, vehicle_conf.width, vehicle_conf.length});
  // The sample info for ego car.
  const std::vector<OdomSampleInfo> kEgoCarSampleInfo = {
      {kTotalSamplingTimeInMSec, kMinAccelerationMpss}};
  ego_car_agent.UpdateOdomSamples(kEgoCarSampleInfo, nearest_pose.timestamp(),
                                  kSamplingTimeResolutionInMSec);
  // Determin the reaction time.
  const int64_t reaction_time =
      DetermineReactionTime(nearest_pose.speed(), nearest_pose.accel());

  // We loop through all the traffic participant, and generate warning if some
  // vehicles are too close to the robot.
  return std::any_of(planner_object_map.begin(), planner_object_map.end(),
                     [&curr_pose, &ego_car_agent,
                      reaction_time](const auto& planner_object_pair) {
                       return (planner_object_pair.second.object_type() ==
                               voy::perception::ObjectType::VEHICLE) &&
                              HasCollideWithTrafficParticipant(
                                  curr_pose, ego_car_agent,
                                  planner_object_pair.second, reaction_time);
                     });
}

bool IsNearRoadBoundary(
    const planner::LaneGeometryDirective& lane_geometry_directive,
    const math::geometry::OrientedBox2d& ego_bounding_box) {
  const double left_clearance_sq = ComparableDistanceFromEgoToHardRoadBoundary(
      lane_geometry_directive.left_hard_boundary_lines, ego_bounding_box);
  const double right_clearance_sq = ComparableDistanceFromEgoToHardRoadBoundary(
      lane_geometry_directive.right_hard_boundary_lines, ego_bounding_box);
  return std::min(left_clearance_sq, right_clearance_sq) <
         math::Sqr(kNearHardBoundaryDistanceThresholdInMeter);
}

}  // namespace ops_warning
}  // namespace planner
