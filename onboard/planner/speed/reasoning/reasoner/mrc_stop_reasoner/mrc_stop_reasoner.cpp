#include "planner/speed/reasoning/reasoner/mrc_stop_reasoner/mrc_stop_reasoner.h"

#include <algorithm>
#include <iterator>
#include <map>
#include <utility>
#include <vector>

#include "math/range.h"
#include "planner/speed/solver/searcher/simple_profile_searcher.h"
#include "planner/world_model/immediate_pull_over/immediate_pull_over_utility.h"

namespace planner {
namespace speed {
namespace reasoner {
namespace {
// The stop decel to keep ego static once enters MRC.
constexpr double kKeepStaticDecelInMpss = -2.0;
// The default comfort stop decel.
constexpr double kDefaultStopDecelInMpss = -3.0;
// The speed threshold below which ego is considered as stationary.
constexpr double kDefaultStationarySpeedInMps = 0.1;
// The default stop buffer for longitudinal error.
constexpr double kDefaultStopErrorBufferInMeters = 1.0;
// The buffer for stay stopped below which considerred to enter MRC.
constexpr double kStayStoppedBufferInMeters = 5.0;
// The lateral encroachment buffer for inlane check.
constexpr double kDefaultLateralEncroachmentBufferInMeters = 0.5;
// The default backward extend length for first valid range.
constexpr double kRangeBackwardExtendArclengthInMeters = 5.0;
// The min arclength range for vehicle to stop within. Note that length of Volvo
// XC90 is 4.953 meters.
constexpr double kMinRangeArclengthInMeters = 7.0;
// The front & back encroachment buffer for arclength.
constexpr double kFrontEncroachmentBufferInMeters = 8.0;
constexpr double kBackEncroachmentBufferInMeters = 5.0;
// If ego speed is below this limit under MRM_INLANE for any reason, it is
// expected to keep under it.
constexpr double kNoSpeedUpInlaneSpeedLimitInMps = 8.333;

// The max inlane stop distances for the nearest and the second nearest
// ZONE_INLANE, defined in:
// https://cooper.didichuxing.com/docs2/document/2202025934603
constexpr double kRelaxedInlaneStopDistanceInMeters = 15.0;
// The distance buffer before and after exit zone.
constexpr double kExitZonePriorBufferInMeters = 1.0;
constexpr double kExitZoneAfterBufferInMeters = 3.0;
// The distance buffer before and after junction.
constexpr double kJunctionPriorBufferInMeters = 30.0;
constexpr double kJunctionAfterBufferInMeters = 30.0;
// The distance buffer before and after right turn single lane.
constexpr double kRightTurnSingleLaneBufferInMeters = 30.0;
// The distance buffer before and after uturn lane.
constexpr double kUTurnBufferInMeters = 30.0;
// The lateral distance above which other vehicle could pass.
constexpr double kHardBoundaryLateralBufferInMeters = 3.0;
// The distance threshold to check whether ego is near a stop cell.
constexpr double kDistThresholdToCheckEgoNearStopCellInMeter = 1.5;
// The duration ego stopped to check ego has entered mrc.
constexpr int64_t kEgoStoppedDurationToCheckHasEnteredMRCInMSec = 3000;
// The default emergency brake distance.
// By default ego is expected to stop ASAP using EMERGENCY_BRAKE.
// Different stop distances are defined for post collision handling, see:
// https://cooper.didichuxing.com/docs2/document/2202919554643
const std::map<mrc::pb::UrgencyStopLevel, std::pair<double, double>>
    kUrgencyStopDirectives{
        {mrc::pb::STOP_COMFORTABLY,
         {/*max_stop_distance=*/46.0, /*min_brake_accel=*/-3.0}},
        {mrc::pb::STOP_QUICKLY,
         {/*max_stop_distance=*/28.0, /*min_brake_accel=*/-5.0}},
        {mrc::pb::STOP_IMMEDIATELY,
         {/*max_stop_distance=*/20.0, /*min_brake_accel=*/-7.0}}};

// Gets the minimal stop distance given current speed & accel.
// TODO(speed): Move this method into common utils.
double GetMinStopDistanceGivenMaxDecel(
    const WorldContext& world_context,
    const pb::DiscomfortVaryingLimitsConfig& limits_config,
    double min_accel = kDefaultStopDecelInMpss) {
  const DiscomfortVaryingLimits all_limits(limits_config);
  const int selected_discomfort_ix =
      all_limits.GetDiscomfortIxFromBrakeAMin(min_accel);
  const Limits& limits =
      all_limits.LimitsAtDiscomfortIx(selected_discomfort_ix);
  const RobotStateSnapshot& state_snapshot =
      world_context.robot_state().current_state_snapshot();

  Profile stopped_profile;
  GenerateStopProfileWithLimits(
      State{/*t_in=*/0.0, /*x_in=*/0.0, /*v_in=*/state_snapshot.speed(),
            /*a_in=*/state_snapshot.acceleration(), /*j_in=*/0.0},
      /*num_steps=*/constants::kTrajectoryStateNum, limits, &stopped_profile);
  DCHECK_GT(stopped_profile.size(), 0);

  return stopped_profile.back().x;
}

// Checks if current lane is right turn and is wrapped by hard boundary.
bool IsSingleLaneInRightTurn(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const std::vector<traffic_rules::LaneInfoInLaneSequence>& lane_seq,
    size_t idx) {
  DCHECK(idx >= 0 && idx < lane_seq.size());
  const traffic_rules::LaneInfoInLaneSequence& lane = lane_seq[idx];
  // Return true if current lane is in exclusive right turn.
  if (lane.lane->section()->road()->side_road_type() ==
      hdmap::Road::EXCLUSIVE_RIGHT_TURN) {
    return true;
  }
  // Early return if not right most drivable lane.
  if (!lane.lane->IsRightmostDrivableLane()) {
    return false;
  }

  const auto dynamic_lane =
      joint_pnc_map_service.GetObjectSharedPtr<pnc_map::Lane>(
          lane.lane->id(), pnc_map::ElementQueryType::kOnlineFirst);
  // Early return if not single lane.
  if (std::all_of(dynamic_lane->left_hard_boundary_info().begin(),
                  dynamic_lane->left_hard_boundary_info().end(),
                  [](const auto& hb_info) {
                    return hb_info.dist_to_lane_marking >=
                           kHardBoundaryLateralBufferInMeters;
                  })) {
    return false;
  }
  // Return true if exit type of current lane is right turn.
  if (dynamic_lane->turn() == hdmap::Lane_Turn_RIGHT) {
    if (idx < lane_seq.size() - 1 &&
        lane_seq[idx + 1].lane_turn_type == hdmap::Lane_Turn_RIGHT)
      return true;
  }
  // Return true if exit type of the predecessor of current lane is right
  // turn.
  if (idx > 0 && lane_seq[idx - 1].lane_turn_type == hdmap::Lane_Turn_RIGHT) {
    return true;
  }
  return false;
}

// Gets invalid ranges from keep clear zone.
void GenerateInvalidRangesForNoStopZones(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    std::vector<math::Range1d>* invalid_ranges) {
  DCHECK(invalid_ranges != nullptr);
  for (const traffic_rules::ZoneInLaneSequence& zone :
       trajectory_info.traffic_rules().zones) {
    if (zone.zone_ptr->type() == hdmap::Zone_ZoneType_KEEP_CLEAR ||
        zone.zone_ptr->type() == hdmap::Zone_ZoneType_NO_PARKING) {
      for (const math::Range1d& intersected_range : zone.intersected_ranges) {
        invalid_ranges->emplace_back(
            intersected_range.start_pos - kExitZonePriorBufferInMeters -
                world_context.ra_to_leading_bumper(),
            intersected_range.end_pos + kExitZoneAfterBufferInMeters -
                world_context.ra_to_trailing_bumper_shift());
        DVLOG(2) << "[MrcStopReasoner] no stop zone: "
                 << invalid_ranges->back().start_pos << ","
                 << invalid_ranges->back().end_pos;
      }
    }
  }
}

// Gets invalid ranges from junctions.
void GenerateInvalidRangesForJunction(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    std::vector<math::Range1d>* invalid_ranges) {
  DCHECK(invalid_ranges != nullptr);
  for (const traffic_rules::JunctionInLaneSequence& junction :
       trajectory_info.traffic_rules().lane_sequence_junctions) {
    const math::Range1d& ra_arclength_range_m = junction.ra_arclength_range_m;
    // Skip if start & end of the range are not sorted, e.g., in uturn.
    if (ra_arclength_range_m.start_pos >= ra_arclength_range_m.end_pos) {
      continue;
    }
    invalid_ranges->emplace_back(
        ra_arclength_range_m.start_pos - kJunctionPriorBufferInMeters -
            world_context.ra_to_leading_bumper(),
        ra_arclength_range_m.end_pos + kJunctionAfterBufferInMeters -
            world_context.ra_to_trailing_bumper_shift());
    DVLOG(2) << "[MrcStopReasoner] junction: "
             << invalid_ranges->back().start_pos << ","
             << invalid_ranges->back().end_pos;
  }
}

// Gets invalid ranges from rightmost drivable lanes around exit zones.
void GenerateInvalidRangesForRightmostExitZone(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    std::vector<math::Range1d>* invalid_ranges) {
  DCHECK(invalid_ranges != nullptr);
  std::map<int64_t,
           std::reference_wrapper<const traffic_rules::ZoneInLaneSequence>>
      exit_zones;
  for (const traffic_rules::ZoneInLaneSequence& zone :
       trajectory_info.traffic_rules().zones) {
    if (zone.zone_ptr->type() == hdmap::Zone_ZoneType_ROAD_EXIT) {
      exit_zones.emplace(zone.zone_ptr->id(), std::cref(zone));
    }
  }
  // Early return if there's no exit zone along lane sequence.
  if (exit_zones.empty()) {
    return;
  }
  for (const traffic_rules::LaneInfoInLaneSequence& lane :
       trajectory_info.traffic_rules().lane_sequence_lanes) {
    if (!lane.lane->IsRightmostDrivableLane()) {
      continue;
    }
    for (const pnc_map::ZoneInfo& zone : lane.lane->zones()) {
      // Skip if exit zone is not on current lane sequence.
      if (exit_zones.count(zone.id()) == 0) {
        continue;
      }
      const traffic_rules::ZoneInLaneSequence& exit_zone =
          exit_zones.at(zone.id());
      invalid_ranges->emplace_back(
          exit_zone.start_ra_arclength - kExitZonePriorBufferInMeters -
              world_context.ra_to_leading_bumper(),
          exit_zone.end_ra_arclength + kExitZoneAfterBufferInMeters -
              world_context.ra_to_trailing_bumper_shift());
      DVLOG(2) << "[MrcStopReasoner] rightmost exit zone: "
               << invalid_ranges->back().start_pos << ","
               << invalid_ranges->back().end_pos;
    }
  }
}

// Gets invalid ranges for single lane of right turn.
void GenerateInvalidRangesForRightTurnSingleLane(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    std::vector<math::Range1d>* invalid_ranges) {
  DCHECK(invalid_ranges != nullptr);
  const auto& lane_seq = trajectory_info.traffic_rules().lane_sequence_lanes;
  for (size_t i = 0; i < lane_seq.size(); ++i) {
    if (!IsSingleLaneInRightTurn(*world_context.joint_pnc_map_service(),
                                 lane_seq, i)) {
      continue;
    }
    invalid_ranges->emplace_back(
        lane_seq[i].start_ra_arclength - kRightTurnSingleLaneBufferInMeters -
            world_context.ra_to_leading_bumper(),
        lane_seq[i].end_ra_arclength + kRightTurnSingleLaneBufferInMeters -
            world_context.ra_to_trailing_bumper_shift());
    DVLOG(2) << "[MrcStopReasoner] right turn lane: "
             << invalid_ranges->back().start_pos << ","
             << invalid_ranges->back().end_pos;
  }
}

// Gets invalid ranges for complex Uturn.
void GenerateInvalidRangesForUTurn(const WorldContext& world_context,
                                   const TrajectoryInfo& trajectory_info,
                                   std::vector<math::Range1d>* invalid_ranges) {
  DCHECK(invalid_ranges != nullptr);
  for (const traffic_rules::LaneInfoInLaneSequence& lane :
       trajectory_info.traffic_rules().lane_sequence_lanes) {
    if (lane.lane_turn_type != hdmap::Lane::U_TURN) {
      continue;
    }
    invalid_ranges->emplace_back(
        lane.start_ra_arclength - kUTurnBufferInMeters -
            world_context.ra_to_leading_bumper(),
        lane.end_ra_arclength + kUTurnBufferInMeters -
            world_context.ra_to_trailing_bumper_shift());
    DVLOG(2) << "[MrcStopReasoner] uturn lane: "
             << invalid_ranges->back().start_pos << ","
             << invalid_ranges->back().end_pos;
  }
}

// Gets invalid ranges from crosswalks.
void GenerateInvalidRangesForCrosswalk(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    std::vector<math::Range1d>* invalid_ranges) {
  DCHECK(invalid_ranges != nullptr);
  for (const traffic_rules::CrosswalkInLaneSequence& crosswalk :
       trajectory_info.traffic_rules().crosswalks) {
    for (const traffic_rules::YieldZoneInfo& yield_zone_info :
         crosswalk.yield_zone_infos) {
      const math::Range1d ra_arclength_range =
          yield_zone_info.ra_arclength_range;
      invalid_ranges->emplace_back(
          ra_arclength_range.start_pos - world_context.ra_to_leading_bumper(),
          ra_arclength_range.end_pos -
              world_context.ra_to_trailing_bumper_shift());
      DVLOG(2) << "[MrcStopReasoner] xwalk: "
               << invalid_ranges->back().start_pos << ","
               << invalid_ranges->back().end_pos;
    }
  }
}

// Gets invalid ranges from lateral encroachment.
// TODO(jinhao): Move the encroachment handling into common utils.
void GenerateInvalidRangesForLateralEncroachment(
    const WorldContext& world_context,
    const pb::PathOutOfLaneInfo& lane_encroachment_info,
    std::vector<math::Range1d>* invalid_ranges) {
  DCHECK(invalid_ranges != nullptr);
  static const double kEncroachmentWidth =
      0.5 * world_context.robot_state().GetWidth() +
      kDefaultLateralEncroachmentBufferInMeters;
  auto is_encroaching = [&lane_encroachment_info](int idx) {
    return std::abs(lane_encroachment_info.signed_distance(idx)) <
           kEncroachmentWidth;
  };
  const ::google::protobuf::RepeatedField<double>& ra_arc_length_on_path =
      lane_encroachment_info.ra_arc_length_on_path();
  for (int i = 0; i < lane_encroachment_info.out_of_lane_segments_size(); ++i) {
    // Early return if largest encroachment of the segment is small.
    const pb::LongitudinalRange& segment =
        lane_encroachment_info.out_of_lane_segments(i);
    if (lane_encroachment_info.lateral_encroachment_distance(i) <
        kEncroachmentWidth) {
      invalid_ranges->emplace_back(
          segment.start() - kBackEncroachmentBufferInMeters,
          segment.end() + kFrontEncroachmentBufferInMeters);
      DVLOG(2) << "[MrcStopReasoner] lane encrochment: "
               << invalid_ranges->back().start_pos << ","
               << invalid_ranges->back().end_pos;
      continue;
    }
    // Find the start index of rear axle arclength for this segment.
    const int min_idx = std::distance(
        ra_arc_length_on_path.begin(),
        std::lower_bound(ra_arc_length_on_path.begin(),
                         ra_arc_length_on_path.end(), segment.start(),
                         [](double ra_arc_length, double start) {
                           return ra_arc_length < start;
                         }));
    // Find all invalid ranges within which encroachment is small.
    int start = min_idx, curr = min_idx;
    while (curr < ra_arc_length_on_path.size() &&
           ra_arc_length_on_path[curr] < segment.end()) {
      if (!is_encroaching(curr)) {
        ++curr;
        continue;
      }
      if (curr == min_idx || !is_encroaching(curr - 1)) {
        start = curr;
      }
      while (curr < ra_arc_length_on_path.size() &&
             ra_arc_length_on_path[curr] < segment.end() &&
             is_encroaching(curr)) {
        ++curr;
      }
      invalid_ranges->emplace_back(
          ra_arc_length_on_path[start] - kBackEncroachmentBufferInMeters,
          ra_arc_length_on_path[curr - 1] + kFrontEncroachmentBufferInMeters);
      DVLOG(2) << "[MrcStopReasoner] lane encrochment: "
               << invalid_ranges->back().start_pos << ","
               << invalid_ranges->back().end_pos;
    }
  }
}

// Gets invalid ranges from critical regions w.r.t. merge lane.
// Here we only consider xlane merge to avoid lane marking violation.
void GenerateInvalidRangesForCriticalRegion(
    const TrajectoryInfo& trajectory_info,
    std::vector<math::Range1d>* invalid_ranges) {
  DCHECK(invalid_ranges != nullptr);
  for (const CriticalRange1d& region : trajectory_info.critical_regions()) {
    if (region.tag == planner::pb::CriticalRegionType::LeftMerge ||
        region.tag == planner::pb::CriticalRegionType::RightMerge) {
      invalid_ranges->emplace_back(
          region.range.start_pos - kBackEncroachmentBufferInMeters,
          region.range.end_pos + kFrontEncroachmentBufferInMeters);
      DVLOG(2) << "[MrcStopReasoner] merge lane: "
               << invalid_ranges->back().start_pos << ","
               << invalid_ranges->back().end_pos;
    }
  }
}

// The following regions are excluded from legal `ZONE_INLANE`:
// 1) junction, 2) keep clear zone, 3) rightmost drivable lane before exit zone,
// 4) single lane at right turn.
// https://cooper.didichuxing.com/docs2/document/2202025934603
std::vector<math::Range1d> GetValidInlaneStopRanges(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    speed::pb::SpeedReasonerSeeds* mutable_current_iter_seed,
    pb::MrcStopReasonerDebug* mrc_debug) {
  std::vector<math::Range1d> invalid_ranges;
  if (trajectory_info.IsXLaneNudgingToOncomingLane()) {
    return invalid_ranges;
  }

  // Generate hard invalid ranges for map elements.
  GenerateInvalidRangesForNoStopZones(world_context, trajectory_info,
                                      &invalid_ranges);
  GenerateInvalidRangesForJunction(world_context, trajectory_info,
                                   &invalid_ranges);
  GenerateInvalidRangesForRightmostExitZone(world_context, trajectory_info,
                                            &invalid_ranges);
  GenerateInvalidRangesForRightTurnSingleLane(world_context, trajectory_info,
                                              &invalid_ranges);
  GenerateInvalidRangesForUTurn(world_context, trajectory_info,
                                &invalid_ranges);
  GenerateInvalidRangesForCrosswalk(world_context, trajectory_info,
                                    &invalid_ranges);

  // Set is inlane valid if ego pos is valid for MRM_INLANE.
  const math::Range1d ego_bbox_range{
      world_context.ra_to_trailing_bumper_shift(),
      world_context.ra_to_leading_bumper()};
  if (std::none_of(invalid_ranges.begin(), invalid_ranges.end(),
                   [&ego_bbox_range](const auto& range) {
                     return math::AreRangesOverlapping(range, ego_bbox_range);
                   })) {
    mutable_current_iter_seed->mutable_mrc_stop_reasoner_seed()
        ->set_is_inlane_degrade_allowed(true);
  }

  // Generate soft invalid ranges for lane encroachment.
  GenerateInvalidRangesForCriticalRegion(trajectory_info, &invalid_ranges);
  GenerateInvalidRangesForLateralEncroachment(
      world_context, trajectory_info.lane_encroachment_info(), &invalid_ranges);

  // Merge ranges since subtract operation requires non-overlapping inputs.
  std::function<bool(const math::Range1d&, const math::Range1d&)>
      should_trigger_merge =
          [](const math::Range1d& prev_range, const math::Range1d& cur_range) {
            return cur_range.start_pos < prev_range.end_pos;
          };
  invalid_ranges =
      math::MergeRange(std::move(invalid_ranges), should_trigger_merge);

  // Remove invalid ranges from horizon.
  std::vector<math::Range1d> valid_ranges;
  valid_ranges.emplace_back(-kRangeBackwardExtendArclengthInMeters,
                            trajectory_info.path().GetTotalArcLength() -
                                world_context.ra_to_leading_bumper());
  valid_ranges = math::SubtractRanges(valid_ranges, invalid_ranges);

  // Remove valid ranges not long enough. We won't remove first one since it's
  // always updated by ego car.
  valid_ranges.erase(
      std::remove_if(valid_ranges.begin() + 1, valid_ranges.end(),
                     [](const auto& it) {
                       return it.end_pos - it.start_pos <
                              kMinRangeArclengthInMeters;
                     }),
      valid_ranges.end());

  if (mrc_debug != nullptr) {
    mrc_debug->mutable_valid_ranges()->Reserve(valid_ranges.size());
    for (const auto& range : valid_ranges) {
      auto* pb_range = mrc_debug->mutable_valid_ranges()->Add();
      pb_range->set_start_pos(range.start_pos);
      pb_range->set_end_pos(range.end_pos);
    }
  }

  return valid_ranges;
}

// Sets up stop point seed using stop ra arclength.
void SetUpStopPointSeed(const TrajectoryInfo& trajectory_info,
                        double stop_ra_arclength, bool is_soft,
                        pb::MrcStopReasonerSeed* mutable_seed) {
  auto* pb_stop_point = is_soft ? mutable_seed->mutable_soft_point()
                                : mutable_seed->mutable_hard_point();
  const math::geometry::Point2d stop_point =
      trajectory_info.path().GetInterpPoint(
          std::min(trajectory_info.path().GetTotalArcLength(),
                   trajectory_info.plan_init_ra_arc_length_on_extended_path() +
                       stop_ra_arclength));
  pb_stop_point->set_x(stop_point.x());
  pb_stop_point->set_y(stop_point.y());
}

// Sets up stop point seed using last frame seed.
void SetUpStopPointSeed(bool is_soft,
                        const pb::MrcStopReasonerSeed& mrc_stop_reasoner_seed,
                        pb::MrcStopReasonerSeed* mutable_seed) {
  DCHECK(  // NOLINT, FP when checking macro DCHECK
      (is_soft && mrc_stop_reasoner_seed.has_soft_point()) ||
      (!is_soft && mrc_stop_reasoner_seed.has_hard_point()));
  const voy::Point2d& seed_point = is_soft
                                       ? mrc_stop_reasoner_seed.soft_point()
                                       : mrc_stop_reasoner_seed.hard_point();
  auto* pb_stop_point = is_soft ? mutable_seed->mutable_soft_point()
                                : mutable_seed->mutable_hard_point();
  pb_stop_point->set_x(seed_point.x());
  pb_stop_point->set_y(seed_point.y());
}

// Gets stop rear axle arclength for urgency stop.
double GetUrgencyStopRaArclength(
    const TrajectoryInfo& trajectory_info,
    const planner::pb::DecoupledManeuverSeed& previous_iter_seed,
    speed::pb::SpeedReasonerSeeds* mutable_current_iter_seed,
    mrc::pb::UrgencyStopLevel level, bool is_soft) {
  DCHECK(  // NOLINT, FP when checking macro DCHECK
      level == mrc::pb::STOP_COMFORTABLY || level == mrc::pb::STOP_QUICKLY ||
      level == mrc::pb::STOP_IMMEDIATELY);

  auto* mutable_seed =
      mutable_current_iter_seed->mutable_mrc_stop_reasoner_seed();
  const pb::MrcStopReasonerSeed& mrc_stop_reasoner_seed =
      previous_iter_seed.speed_seed()
          .speed_reasoner_seeds()
          .mrc_stop_reasoner_seed();
  double stop_ra_arclength = 0.0;
  // We won't use previous seed if:
  // 1) urgency level does not match, OR
  // 2) soft constraint is expected but none, OR
  // 3) hard constraint is expected but none.
  if (mrc_stop_reasoner_seed.level() != level ||
      (is_soft && !mrc_stop_reasoner_seed.has_soft_point()) ||
      (!is_soft && !mrc_stop_reasoner_seed.has_hard_point())) {
    stop_ra_arclength = is_soft ? 0.0 : kUrgencyStopDirectives.at(level).first;
    SetUpStopPointSeed(trajectory_info, stop_ra_arclength, is_soft,
                       mutable_seed);
  } else {
    // Use previous seed and latch seed.
    const voy::Point2d& seed_point = is_soft
                                         ? mrc_stop_reasoner_seed.soft_point()
                                         : mrc_stop_reasoner_seed.hard_point();
    stop_ra_arclength =
        trajectory_info.path().GetProjectionArcLength(
            {seed_point.x(), seed_point.y()},
            math::pb::UseExtensionFlag::kForbid) -
        trajectory_info.plan_init_ra_arc_length_on_extended_path();
    SetUpStopPointSeed(is_soft, mrc_stop_reasoner_seed, mutable_seed);
  }
  return stop_ra_arclength;
}

// Gets the stop cell type where the ego located. Return UNKNOWN_TYPE if ego
// does not locate any stop cells.
hdmap::StopCellEntity::Type GetStopCellTypeWhereEgoLocate(
    const std::vector<hdmap::StopCellEntity>& stop_cells,
    const math::geometry::Point2d& ego_position) {
  // Find the stop cell where ego is located.
  for (const auto& stop_cell : stop_cells) {
    if (math::geometry::Within(
            ego_position,
            math::geometry::Convert<math::geometry::PolygonWithCache2d>(
                stop_cell.border()))) {
      return stop_cell.type();
    }

    // If the projection of the ego on the cell is within the range of the
    // cell and the ego is close to the cell right boundary, we can determine
    // that the ego is located on this cell.
    const auto cell_right_boundary =
        math::geometry::PolylineCurve2d(stop_cell.right_boundary().points());
    const math::ProximityQueryInfo query_info =
        cell_right_boundary.GetProximity(ego_position,
                                         math::pb::UseExtensionFlag::kForbid);
    if (query_info.relative_position == math::RelativePosition::kWithIn &&
        std::abs(query_info.dist) <
            kDistThresholdToCheckEgoNearStopCellInMeter) {
      return stop_cell.type();
    }
  }

  return hdmap::StopCellEntity::UNKNOWN_TYPE;
}

// TODO(peterxu): Use the raw_stop_cells from pdz directly when we only use
// virtual pdz.
// Gets the stop cells near the ego position when pull over is completed.
std::vector<hdmap::StopCellEntity> GetStopCellsWhenPullOverCompleted(
    const PickupDropoffZoneInfo& pdz_info,
    const pnc_map::PncMapService* pnc_map_service,
    const math::geometry::Point2d& ego_position) {
  return pdz_info.pickup_dropoff_zone_type == PickupDropoffZoneType::VIRTUAL
             ? pdz_info.raw_stop_cells
             : QueryStopCellEntitiesByPoint(*pnc_map_service, ego_position);
}

// Checks if ego has entered MRC when urgency or inlane is triggered.
bool HasEgoEnteredMrc(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const planner::pb::DecoupledManeuverSeed& previous_iter_seed,
    const std::vector<math::Range1d>& valid_ranges,
    pb::MrcStopReasonerDebug* mrc_debug) {
  DCHECK(mrc_debug != nullptr);

  // Add extra constraint if ego enters MRC in last cycle to prohibit
  // stationary check flicker.
  const pb::MrcStopReasonerSeed& mrc_stop_reasoner_seed =
      previous_iter_seed.speed_seed()
          .speed_reasoner_seeds()
          .mrc_stop_reasoner_seed();
  if (mrc_stop_reasoner_seed.has_entered_mrc()) {
    mrc_debug->set_mrc_condition(pb::MrcStopReasonerDebug::LAST_CYCLE);
    return true;
  }

  // Early return if ego has not stopped.
  if (!world_context.robot_state().IsStationary() &&
      std::abs(world_context.ego_speed()) > kDefaultStationarySpeedInMps) {
    return false;
  }

  // Set enter mrc if ego stops in MRM_URGENCY or in reverse driving for any
  // reason.
  if (world_context.mrc_request_ptr()->mrm_type() == mrc::pb::MRM_URGENCY) {
    mrc_debug->set_mrc_condition(pb::MrcStopReasonerDebug::URGENCY);
    return true;
  }
  if (world_context.motion_mode() == planner::pb::MotionMode::BACKWARD) {
    mrc_debug->set_mrc_condition(pb::MrcStopReasonerDebug::BACKWARD);
    return true;
  }

  // Check if ego car is in valid range.
  auto is_in_valid_range = [&valid_ranges](double leading_buffer) {
    return !valid_ranges.empty() &&
           valid_ranges[0].start_pos < leading_buffer &&
           valid_ranges[0].end_pos > 0.0;
  };

  // Set enter mrc if ego stops inside valid ranges.
  if (is_in_valid_range(kDefaultStopErrorBufferInMeters)) {
    mrc_debug->set_mrc_condition(pb::MrcStopReasonerDebug::IN_RANGE);
    return true;
  }

  // Set enter mrc if ego prefers to stay stopped within certain distance.
  if (is_in_valid_range(kStayStoppedBufferInMeters) &&
      previous_iter_seed.speed_seed().speed_solver_result().solver_result() ==
          pb::SolverResult::kStayedStopped) {
    mrc_debug->set_mrc_condition(pb::MrcStopReasonerDebug::STAY_STOPPED);
    return true;
  }

  // Set enter mrc if ego reaches (pull over) destination before entering valid
  // ranges.
  if (world_context.distance_to_destination_m() -
          world_context.ra_to_leading_bumper() <
      kDefaultStopErrorBufferInMeters) {
    mrc_debug->set_mrc_condition(pb::MrcStopReasonerDebug::REACH_GOAL);
    return true;
  }
  if (trajectory_info.is_pull_over_triggered() &&
      trajectory_info.pull_over_progress() ==
          planner::pb::PullOverProgress::kCompleted) {
    mrc_debug->set_mrc_condition(pb::MrcStopReasonerDebug::PULL_OVER_FINISH);
    return true;
  }

  // Set enter mrc if ego is stuck.
  if (world_context.robot_state().is_ego_stuck()) {
    mrc_debug->set_mrc_condition(pb::MrcStopReasonerDebug::STUCKED);
    return true;
  }

  return false;
}

// Checks if ego has entered MRC when pullright or harbor is triggered.
bool HasEgoEnteredMrc(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const planner::pb::DecoupledManeuverSeed& previous_iter_seed,
    pb::MrcStopReasonerDebug* mrc_debug) {
  DCHECK(mrc_debug != nullptr);

  // Add extra constraint if ego enters MRC in last cycle to prohibit
  // stationary check flicker.
  const pb::MrcStopReasonerSeed& mrc_stop_reasoner_seed =
      previous_iter_seed.speed_seed()
          .speed_reasoner_seeds()
          .mrc_stop_reasoner_seed();
  if (mrc_stop_reasoner_seed.has_entered_mrc()) {
    mrc_debug->set_mrc_condition(pb::MrcStopReasonerDebug::LAST_CYCLE);
    return true;
  }

  const math::geometry::Point2d& ego_position =
      world_context.robot_state().current_state_snapshot().rear_axle_position();
  // Check if ego has entered mrc by non-pullover completed status.
  if (!trajectory_info.is_pull_over_trajectory() ||
      trajectory_info.pull_over_progress() !=
          planner::pb::PullOverProgress::kCompleted) {
    if (world_context.ego_almost_stopped_duration_ms() <
        kEgoStoppedDurationToCheckHasEnteredMRCInMSec) {
      return false;
    }

    const std::vector<hdmap::StopCellEntity> stop_cells =
        QueryStopCellEntitiesByPoint(*world_context.pnc_map_service(),
                                     ego_position);
    const hdmap::StopCellEntity::Type stop_cell_type =
        GetStopCellTypeWhereEgoLocate(stop_cells, ego_position);
    for (const auto& forbidden_stop_type :
         world_context.forbidden_stop_types_for_mrc()) {
      if (stop_cell_type == forbidden_stop_type) {
        return false;
      }
    }
    return stop_cell_type != hdmap::StopCellEntity::UNKNOWN_TYPE;
  }

  // Don't set mrc state if ego is in exit zone range when pullright or harbor
  // is triggered.
  if (world_context.pickup_dropoff_zone_infos().empty() ||
      IsEgoInExitZoneRange(world_context.robot_state(),
                           world_context.pickup_dropoff_zone_infos().front())) {
    return false;
  }

  const std::vector<hdmap::StopCellEntity> stop_cells =
      GetStopCellsWhenPullOverCompleted(
          world_context.pickup_dropoff_zone_infos().front(),
          world_context.pnc_map_service(), ego_position);
  const hdmap::StopCellEntity::Type stop_cell_type =
      GetStopCellTypeWhereEgoLocate(stop_cells, ego_position);
  for (const auto& forbidden_stop_type :
       world_context.forbidden_stop_types_for_mrc()) {
    if (stop_cell_type == forbidden_stop_type) {
      return false;
    }
  }
  return stop_cell_type != hdmap::StopCellEntity::UNKNOWN_TYPE;
}

}  // namespace

bool MrcStopReasoner::Reason(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const pb::DiscomfortVaryingLimitsConfig& limits_config,
    const planner::pb::DecoupledManeuverSeed& previous_iter_seed,
    speed::pb::SpeedReasonerSeeds* mutable_current_iter_seed,
    CautiousDrivingLimiter* reference_limiter_ptr,
    ConstraintCreator* constraint_creator_ptr,
    speed::pb::ReasoningDebug* debug_proto_ptr) {
  DCHECK(constraint_creator_ptr != nullptr);

  bool constraint_added = false;
  if (!world_context.should_planner_respond_mrc() ||
      world_context.mrc_request_ptr() == nullptr) {
    return constraint_added;
  }

  const mrc::pb::MrcRequest& mrc_request = *world_context.mrc_request_ptr();
  if (mrc_request.mrm_type() == mrc::pb::UNDEFINED) {
    return constraint_added;
  }

  if (debug_proto_ptr) {
    debug_proto_ptr->mutable_reasoner_debug()
        ->mutable_mrc_stop_reasoner()
        ->set_mrm_type(mrc_request.mrm_type());
  }

  if (mrc_request.mrm_type() == mrc::pb::MRM_HARBOR ||
      mrc_request.mrm_type() == mrc::pb::MRM_PULLRIGHT) {
    constraint_added |= AddConstraintForHarborOrPullrightTriggeredMrc(
        world_context, trajectory_info, previous_iter_seed,
        mutable_current_iter_seed, constraint_creator_ptr,
        debug_proto_ptr ? debug_proto_ptr->mutable_reasoner_debug()
                              ->mutable_mrc_stop_reasoner()
                        : nullptr);
    return constraint_added;
  }

  DCHECK(mrc_request.mrm_type() == mrc::pb::MRM_INLANE ||
         mrc_request.mrm_type() == mrc::pb::MRM_URGENCY);
  auto* mutable_seed =
      mutable_current_iter_seed->mutable_mrc_stop_reasoner_seed();
  mutable_seed->set_level(mrc_request.urgency_stop_level());
  mutable_seed->set_mrm_type(mrc_request.mrm_type());

  if (world_context.motion_mode() == planner::pb::MotionMode::BACKWARD) {
    constraint_added |= AddConstraintForReverseDriving(
        world_context, trajectory_info, previous_iter_seed,
        mutable_current_iter_seed, constraint_creator_ptr,
        debug_proto_ptr ? debug_proto_ptr->mutable_reasoner_debug()
                              ->mutable_mrc_stop_reasoner()
                        : nullptr);
    return constraint_added;
  }

  if (mrc_request.mrm_type() == mrc::pb::MRM_INLANE) {
    constraint_added |= AddConstraintForInlaneStop(
        world_context, trajectory_info, limits_config, previous_iter_seed,
        mutable_current_iter_seed, reference_limiter_ptr,
        constraint_creator_ptr,
        debug_proto_ptr ? debug_proto_ptr->mutable_reasoner_debug()
                              ->mutable_mrc_stop_reasoner()
                        : nullptr);
  }

  if (mrc_request.mrm_type() == mrc::pb::MRM_URGENCY) {
    constraint_added |= AddConstraintForUrgencyStop(
        world_context, trajectory_info, previous_iter_seed,
        mutable_current_iter_seed, constraint_creator_ptr,
        debug_proto_ptr ? debug_proto_ptr->mutable_reasoner_debug()
                              ->mutable_mrc_stop_reasoner()
                        : nullptr);
  }

  return constraint_added;
}

bool MrcStopReasoner::AddConstraintForHarborOrPullrightTriggeredMrc(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const planner::pb::DecoupledManeuverSeed& previous_iter_seed,
    speed::pb::SpeedReasonerSeeds* mutable_current_iter_seed,
    ConstraintCreator* constraint_creator_ptr,
    pb::MrcStopReasonerDebug* mrc_debug) {
  bool constraint_added = false;
  const bool has_entered_mrc = HasEgoEnteredMrc(world_context, trajectory_info,
                                                previous_iter_seed, mrc_debug);
  if (has_entered_mrc) {
    mutable_current_iter_seed->mutable_mrc_stop_reasoner_seed()
        ->set_has_entered_mrc(has_entered_mrc);
    constraint_added |= AddKeepStaticConstraint(
        trajectory_info, previous_iter_seed, mutable_current_iter_seed,
        constraint_creator_ptr);
  }
  return constraint_added;
}

bool MrcStopReasoner::AddConstraintForInlaneStop(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const pb::DiscomfortVaryingLimitsConfig& limits_config,
    const planner::pb::DecoupledManeuverSeed& previous_iter_seed,
    speed::pb::SpeedReasonerSeeds* mutable_current_iter_seed,
    CautiousDrivingLimiter* reference_limiter_ptr,
    ConstraintCreator* constraint_creator_ptr,
    pb::MrcStopReasonerDebug* mrc_debug) {
  bool constraint_added = false;

  // Get all valid inlane stop ranges.
  const std::vector<math::Range1d> valid_ranges = GetValidInlaneStopRanges(
      world_context, trajectory_info, mutable_current_iter_seed, mrc_debug);
  const bool has_entered_mrc =
      HasEgoEnteredMrc(world_context, trajectory_info, previous_iter_seed,
                       valid_ranges, mrc_debug);

  pb::MrcStopReasonerSeed* mutable_seed =
      mutable_current_iter_seed->mutable_mrc_stop_reasoner_seed();
  if (has_entered_mrc) {
    mutable_seed->set_has_entered_mrc(has_entered_mrc);
    constraint_added |= AddKeepStaticConstraint(
        trajectory_info, previous_iter_seed, mutable_current_iter_seed,
        constraint_creator_ptr);
    return constraint_added;
  }

  // Add speed limit for MRM_INLANE if ego speed is under certain limit, such
  // that it would not speed up again.
  // https://cooper.didichuxing.com/docs2/document/2202025934603
  if (previous_iter_seed.speed_seed()
          .speed_reasoner_seeds()
          .mrc_stop_reasoner_seed()
          .is_speed_up_forbidden() ||
      world_context.ego_speed() <= kNoSpeedUpInlaneSpeedLimitInMps) {
    mutable_seed->set_is_speed_up_forbidden(true);
    reference_limiter_ptr->AddReferenceLimit(
        kNoSpeedUpInlaneSpeedLimitInMps, kNoSpeedUpInlaneSpeedLimitInMps,
        /*start_arc_length=*/0.0, trajectory_info.path().GetTotalArcLength(),
        kDefaultStopDecelInMpss,
        /*object_id=*/-1);
  }

  if (valid_ranges.empty()) {
    return constraint_added;
  }

  // This is the minimal brake distance under given min decel.
  const double min_brake_distance =
      std::max(GetMinStopDistanceGivenMaxDecel(world_context, limits_config),
               valid_ranges[0].start_pos);
  const auto iter = std::find_if(
      valid_ranges.begin(), valid_ranges.end(),
      [min_brake_distance](const auto& range) {
        return range.start_pos <
                   min_brake_distance + kDefaultStopErrorBufferInMeters &&
               range.end_pos >
                   min_brake_distance - kDefaultStopErrorBufferInMeters;
      });
  if (iter != valid_ranges.end()) {
    // Add hard constraint to ensure ego would stop within selected range, soft
    // constraint to keep minimal decel.
    const double soft_stop_arclength =
        min_brake_distance + world_context.ra_to_leading_bumper();
    constraint_creator_ptr
        ->AddStopPoint(
            /*start_time=*/0, /*end_time=*/constants::kTrajectoryHorizonInSec,
            soft_stop_arclength, pb::FenceType::kMrcStop, id(),
            /*constraint_id=*/"mrc-inlane-soft")
        .set_soft_yield_with_min_a(kDefaultStopDecelInMpss);
    SetUpStopPointSeed(trajectory_info, soft_stop_arclength, /*is_soft=*/true,
                       mutable_seed);

    const double hard_stop_arclength =
        iter == valid_ranges.begin()
            ? std::max(
                  min_brake_distance + world_context.ra_to_leading_bumper(),
                  iter->end_pos)
            : std::min(iter->start_pos + kRelaxedInlaneStopDistanceInMeters,
                       iter->end_pos);
    constraint_creator_ptr->AddExactStopPoint(
        /*start_time=*/0, /*end_time=*/constants::kTrajectoryHorizonInSec,
        hard_stop_arclength, pb::FenceType::kMrcStop, id(), "mrc-inlane-hard");
    SetUpStopPointSeed(trajectory_info, hard_stop_arclength, /*is_soft=*/false,
                       mutable_seed);

    constraint_added = true;
  }

  if (mrc_debug != nullptr) {
    mrc_debug->set_brake_distance(min_brake_distance);
  }

  return constraint_added;
}

bool MrcStopReasoner::AddConstraintForUrgencyStop(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const planner::pb::DecoupledManeuverSeed& previous_iter_seed,
    speed::pb::SpeedReasonerSeeds* mutable_current_iter_seed,
    ConstraintCreator* constraint_creator_ptr,
    pb::MrcStopReasonerDebug* mrc_debug) {
  bool constraint_added = false;
  const bool has_entered_mrc =
      HasEgoEnteredMrc(world_context, trajectory_info, previous_iter_seed,
                       /*valid_ranges=*/{}, mrc_debug);
  if (has_entered_mrc) {
    mutable_current_iter_seed->mutable_mrc_stop_reasoner_seed()
        ->set_has_entered_mrc(has_entered_mrc);
    constraint_added |= AddKeepStaticConstraint(
        trajectory_info, previous_iter_seed, mutable_current_iter_seed,
        constraint_creator_ptr);
    return constraint_added;
  }

  const mrc::pb::MrcRequest& mrc_request = *world_context.mrc_request_ptr();
  if (mrc_request.urgency_stop_level() == mrc::pb::STOP_EMERGENCY ||
      mrc_request.urgency_stop_level() == mrc::pb::UNDEFINED_LEVEL) {
    // Add an avoid region right on ego and allow EB to it. Ego is expected
    // to brake in the hardest way possible (EMERGENCY_STOP).
    // Note that we won't use latched stop point in this case since ego is
    // expected to brake in the hardest way.
    constraint_creator_ptr
        ->AddAvoidRegion(/*start_time=*/0.0,
                         /*end_time=*/constants::kTrajectoryHorizonInSec,
                         /*ra_start_x=*/0.0,
                         /*ra_end_x=*/world_context.ra_to_leading_bumper(),
                         /*fence_type=*/pb::FenceType::kMrcStop, id(),
                         "mrc-urgency")
        .set_allow_emergency_brake();
    if (mrc_debug != nullptr) {
      mrc_debug->set_brake_distance(world_context.ra_to_leading_bumper());
    }
  } else {
    // Add hard constraint to ensure ego would stop within given distance, soft
    // constraint to keep minimal decel.
    const double soft_stop_ra_arclength =
        GetUrgencyStopRaArclength(
            trajectory_info, previous_iter_seed, mutable_current_iter_seed,
            mrc_request.urgency_stop_level(), /*is_soft=*/true) +
        world_context.ra_to_leading_bumper();
    constraint_creator_ptr
        ->AddStopPoint(
            /*start_time=*/0, /*end_time=*/constants::kTrajectoryHorizonInSec,
            soft_stop_ra_arclength, pb::FenceType::kMrcStop, id(),
            "mrc-urgency-soft")
        .set_soft_yield_with_min_a(
            kUrgencyStopDirectives.at(mrc_request.urgency_stop_level()).second);
    constraint_creator_ptr->AddExactStopPoint(
        /*start_time=*/0, /*end_time=*/constants::kTrajectoryHorizonInSec,
        GetUrgencyStopRaArclength(
            trajectory_info, previous_iter_seed, mutable_current_iter_seed,
            mrc_request.urgency_stop_level(), /*is_soft=*/false) +
            world_context.ra_to_leading_bumper(),
        pb::FenceType::kMrcStop, id(), "mrc-urgency-hard");
    if (mrc_debug != nullptr) {
      mrc_debug->set_brake_distance(soft_stop_ra_arclength);
    }
  }
  constraint_added = true;

  return constraint_added;
}

bool MrcStopReasoner::AddConstraintForReverseDriving(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const planner::pb::DecoupledManeuverSeed& previous_iter_seed,
    speed::pb::SpeedReasonerSeeds* mutable_current_iter_seed,
    ConstraintCreator* constraint_creator_ptr,
    pb::MrcStopReasonerDebug* mrc_debug) {
  bool constraint_added = false;

  const bool has_entered_mrc =
      HasEgoEnteredMrc(world_context, trajectory_info, previous_iter_seed,
                       /*valid_ranges=*/{}, mrc_debug);
  if (has_entered_mrc) {
    mutable_current_iter_seed->mutable_mrc_stop_reasoner_seed()
        ->set_has_entered_mrc(has_entered_mrc);
    constraint_added |= AddKeepStaticConstraint(
        trajectory_info, previous_iter_seed, mutable_current_iter_seed,
        constraint_creator_ptr);
    return constraint_added;
  }

  const pb::MrcStopReasonerSeed& mrc_stop_reasoner_seed =
      previous_iter_seed.speed_seed()
          .speed_reasoner_seeds()
          .mrc_stop_reasoner_seed();
  double stop_ra_arclength = world_context.ra_to_trailing_bumper_shift();
  if (mrc_stop_reasoner_seed.has_hard_point()) {
    stop_ra_arclength =
        trajectory_info.path().GetProjectionArcLength(
            {mrc_stop_reasoner_seed.hard_point().x(),
             mrc_stop_reasoner_seed.hard_point().y()},
            math::pb::UseExtensionFlag::kForbid) -
        trajectory_info.plan_init_ra_arc_length_on_extended_path();
  }

  const mrc::pb::MrcRequest& mrc_request = *world_context.mrc_request_ptr();
  if (mrc_request.mrm_type() == mrc::pb::MRM_INLANE) {
    constraint_creator_ptr
        ->AddStopPoint(
            /*start_time=*/0, /*end_time=*/constants::kTrajectoryHorizonInSec,
            stop_ra_arclength, pb::FenceType::kMrcStop, id(),
            /*constraint_id=*/"mrc-inlane-reverse")
        .set_soft_yield_with_min_a(kDefaultStopDecelInMpss);
  } else {
    constraint_creator_ptr
        ->AddAvoidRegion(
            /*start_time=*/0.0,
            /*end_time=*/constants::kTrajectoryHorizonInSec,
            /*ra_start_x=*/0.0, stop_ra_arclength,
            /*fence_type=*/pb::FenceType::kMrcStop, id(), "mrc-urgency-reverse")
        .set_allow_emergency_brake();
  }
  constraint_added = true;

  SetUpStopPointSeed(
      trajectory_info, stop_ra_arclength,
      /*is_soft=*/false,
      mutable_current_iter_seed->mutable_mrc_stop_reasoner_seed());

  if (mrc_debug != nullptr) {
    mrc_debug->set_brake_distance(
        std::abs(world_context.ra_to_trailing_bumper_shift()));
  }

  return constraint_added;
}

bool MrcStopReasoner::AddKeepStaticConstraint(
    const TrajectoryInfo& trajectory_info,
    const planner::pb::DecoupledManeuverSeed& previous_iter_seed,
    speed::pb::SpeedReasonerSeeds* mutable_current_iter_seed,
    ConstraintCreator* constraint_creator_ptr) {
  const pb::MrcStopReasonerSeed& mrc_stop_reasoner_seed =
      previous_iter_seed.speed_seed()
          .speed_reasoner_seeds()
          .mrc_stop_reasoner_seed();
  double stop_ra_arclength = 0.0;
  if (mrc_stop_reasoner_seed.has_soft_point()) {
    stop_ra_arclength =
        trajectory_info.path().GetProjectionArcLength(
            {mrc_stop_reasoner_seed.soft_point().x(),
             mrc_stop_reasoner_seed.soft_point().y()},
            math::pb::UseExtensionFlag::kForbid) -
        trajectory_info.plan_init_ra_arc_length_on_extended_path();
  }
  SetUpStopPointSeed(
      trajectory_info, stop_ra_arclength,
      /*is_soft=*/true,
      mutable_current_iter_seed->mutable_mrc_stop_reasoner_seed());
  DCHECK_NOTNULL(constraint_creator_ptr)
      ->AddExactStopPoint(
          /*start_time=*/0, /*end_time=*/constants::kTrajectoryHorizonInSec,
          stop_ra_arclength, pb::FenceType::kMrcStop, id(), "mrc-hold")
      .set_soft_yield_with_min_a(kKeepStaticDecelInMpss);
  return true;
}
}  // namespace reasoner
}  // namespace speed
}  // namespace planner
