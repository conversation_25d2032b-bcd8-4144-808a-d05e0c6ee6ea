#ifndef ONBOARD_PLANNER_UTILITY_PATH_PATH_COSTING_H_
#define ONBOARD_PLANNER_UTILITY_PATH_PATH_COSTING_H_

#include "control_util/costing_base.h"
#include "planner/utility/primitive/primitive_definition/path_primitive.h"
#include "vehicle_model/utils.h"

namespace control_util {
// Trait for PathFormula specialization.
template <>
struct FormulaTraits<DdpProblemType::kPath> {
  using StateControlSpecs = vehicle_model::GeometricModel;

  using PbState = vehicle_model::pb::GeometricState;
  using PbControl = vehicle_model::pb::GeometricControl;

  using PbFeatureType = control_util::pb::PathFeatureType;
  using PbWeightAdjustSpec = control_util::pb::CostingWeightAdjustSpec;
  using PbFeatureParameter = control_util::pb::PathFeatureParameter;
  using PbCostingTerm = control_util::pb::PathCostingTerm;
  struct StepwisePrimitives {
    vehicle_model::VehicleRepresentation ego_representation;
  };

  using PbSolution = planner::pb::PathSolution;
  using PbWarmstarterDebug = planner::pb::PathWarmstarterDebug;
  using PbSolverDebug = planner::pb::PathSolverDebug;
  using PbSolverInterationDebug = planner::pb::PathSolverIteration;
  using PbWarmstartType = planner::pb::PathWarmstartType;

  using Primitives = planner::primitive::PathPrimitives;

  static constexpr int FEATURE_DIM = pb::PathFeatureType_ARRAYSIZE;
};
}  // namespace control_util

namespace planner {
namespace path {

using PathFormula = control_util::Formula<control_util::DdpProblemType::kPath>;

// PathFeatureParameter includes parameters defined through motion/shape model
// that are not included in the top level costing .conf files since they are
// vehicle-specific. This helper function merges feature parameter from .conf
// file with motion model to create the full PathFeatureParameter object.
control_util::pb::PathFeatureParameter MergeWithMotionModelParams(
    control_util::pb::PathFeatureParameter feature_param,
    const vehicle_model::GeometricModelWithAxleRectangularShape&
        motion_model_with_shape);

}  // namespace path
}  // namespace planner

namespace control_util {

// Computes the feature given type.
template <>
void GetFeature<DdpProblemType::kPath>(
    int step, int total_step_size,
    const planner::path::PathFormula::PbCostingTerm& costing_term,
    const planner::path::PathFormula::PbFeatureParameter& feature_param,
    const planner::path::PathFormula::State& x,
    const planner::path::PathFormula::Control& u,
    const planner::path::PathFormula::StepwisePrimitives& stepwise_primitives,
    const planner::path::PathFormula::Primitives& primitives,
    planner::path::PathFormula::Feature* feature);

template <>
planner::path::PathFormula::StepwisePrimitives
planner::path::PathFormula::GetStepwisePrimitives(
    int step, bool is_terminal, const State& x, const Control& u,
    const Primitives& primitives) const;

}  // namespace control_util

#endif  // ONBOARD_PLANNER_UTILITY_PATH_PATH_COSTING_H_
