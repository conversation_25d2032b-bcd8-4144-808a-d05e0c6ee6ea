/**
 * @file acu_orin_monitor_bsp.cc
 * <AUTHOR>
 * @brief
 * @version 0.1
 * @date 2024-06-17
 *
 * @copyright Copyright (c) 2024
 *
 */

#include "acu_orin_module.h"

#include <atomic>
#include <chrono>
#include <iostream>
#include <sstream>
#include <string>
#include <thread>
#include <unordered_map>

#include "base_tools.hpp"
#include "vh_glog.hpp"

namespace vh = voyager::hardware;
static const std::string ModuleDebug("[ModuleDebug]: ");
int vh::AcuOrinModule::get_orin_id(uint8_t *id) {
  static const char *cmd1 = "gpioget gpiochip0 131";
  static const char *cmd2 = "gpioget gpiochip0 132";
  static std::atomic_flag lck = ATOMIC_FLAG_INIT;
  static uint8_t orin_id = 2;

  while (lck.test_and_set(std::memory_order_acquire));
  if (orin_id == 2) {
    char out1[16] = {0};
    char out2[16] = {0};
    get_system_output(cmd1, out1, sizeof(out1) - 1);
    get_system_output(cmd2, out2, sizeof(out1) - 1);
    if (std::stod(out1) == 1 && std::stod(out2) == 0)
      orin_id = kAcuOrinIdA;
    else if (std::stod(out1) == 0 && std::stod(out2) == 1)
      orin_id = kAcuOrinIdB;
    else
      orin_id = kAcuOrinIdUnknown;
  }
  lck.clear(std::memory_order_release);
  *id = orin_id;
  return 0;
}

bool vh::AcuOrinModule::dv_init(void *arg) {
  static_cast<void>(arg);
  static std::atomic<bool> flag(true);
  bool expected = true;
  if (!flag.compare_exchange_strong(expected, false)) {
    LOG(WARNING) << ModuleDebug << "dv_init again!!!\n";
    return false;
  }

  LOG(WARNING) << ModuleDebug << "dv_init\n";
  return true;
}
void vh::AcuOrinModule::dv_deinit() {
  LOG(WARNING) << ModuleDebug << "dv_deinit\n";
}
int vh::AcuOrinModule::get_dv_msg(AcuDvOrinBaseInfoUn &module) {
  int16_t invalid_s16 = 0xFFFF;
  uint8_t invalid_u8 = 0xFF;
  int32_t tmp;
  module.msg_.tj_temp_ =
      ((get_orin_tj_temp(&tmp) < 0) ? invalid_s16 : (tmp / 1000));
  module.msg_.cpu_temp_ =
      ((get_orin_cpu_temp(&tmp) < 0) ? invalid_s16 : (tmp / 1000));
  module.msg_.gpu_temp_ =
      ((get_orin_gpu_temp(&tmp) < 0) ? invalid_s16 : (tmp / 1000));
  module.msg_.gpu_load_ = ((get_orin_gpu_load(&tmp) < 0) ? invalid_u8 : tmp);

  return 0;
}
std::string vh::AcuOrinModule::print_dv_msg(const AcuDvOrinBaseInfoUn &module) {
  std::stringstream ss;
  ss << ModuleDebug << "tj_temp=" << module.msg_.tj_temp_
     << ", cpu_temp=" << module.msg_.cpu_temp_
     << ", gpu_temp=" << module.msg_.gpu_temp_
     << ", gpu_load=" << (int)module.msg_.gpu_load_;
  return ss.str();
}
int vh::AcuOrinModule::test_dv_msg(int repeat, uint16_t ms) {
  dv_init(nullptr);
  do {
    AcuDvOrinBaseInfoUn module;
    get_dv_msg(module);
    std::cout << print_dv_msg(module) << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(ms));
  } while (--repeat);
  dv_deinit();
  return 0;
}

bool vh::AcuOrinModule::mt_init(void *arg) {
  static_cast<void>(arg);
  static std::atomic<bool> flag(true);
  bool expected = true;
  if (!flag.compare_exchange_strong(expected, false)) {
    LOG(WARNING) << ModuleDebug << "mt_init again!!!\n";
    return false;
  }

  LOG(WARNING) << ModuleDebug << "mt_init\n";
  return true;
}
void vh::AcuOrinModule::mt_deinit() {
  LOG(WARNING) << ModuleDebug << "mt_deinit\n";
}
int vh::AcuOrinModule::get_mt_msg(AcuMtOrinModuleSt &module) {
  int32_t tmp;
  module.cpu_temp_ = ((get_orin_cpu_temp(&tmp) < 0) ? INT32_MAX : tmp);
  module.gpu_temp_ = ((get_orin_gpu_temp(&tmp) < 0) ? INT32_MAX : tmp);
  module.soc0_temp_ = ((get_orin_soc0_temp(&tmp) < 0) ? INT32_MAX : tmp);
  module.soc1_temp_ = ((get_orin_soc1_temp(&tmp) < 0) ? INT32_MAX : tmp);
  module.soc2_temp_ = ((get_orin_soc2_temp(&tmp) < 0) ? INT32_MAX : tmp);
  module.tj_temp_ = ((get_orin_tj_temp(&tmp) < 0) ? INT32_MAX : tmp);
  module.tboard_temp_ = ((get_orin_tb_temp(&tmp) < 0) ? INT32_MAX : tmp);
  module.tdiode_temp_ = ((get_orin_tdiode_temp(&tmp) < 0) ? INT32_MAX : tmp);

  int32_t volt[4] = {0};
  int32_t curr[4] = {0};
  if (!get_orin_gpu_soc_volt(&volt[0]) && !get_orin_gpu_soc_curr(&curr[0]))
    module.vdd_gpu_soc_ = static_cast<int32_t>(volt[0] * curr[0] / 1000.0);
  else
    module.vdd_gpu_soc_ = INT32_MAX;

  if (!get_orin_cpu_cv_volt(&volt[1]) && !get_orin_cpu_cv_curr(&curr[1]))
    module.vdd_cpu_cv_ = static_cast<int32_t>(volt[1] * curr[1] / 1000.0);
  else
    module.vdd_cpu_cv_ = INT32_MAX;

  if (!get_orin_sys_5v0_volt(&volt[2]) && !get_orin_sys_5v0_curr(&curr[2]))
    module.vin_sys_5v0_ = static_cast<int32_t>(volt[2] * curr[2] / 1000.0);
  else
    module.vin_sys_5v0_ = INT32_MAX;
  if (!get_orin_vdd2_1v8a0_volt(&volt[3]) &&
      !get_orin_vdd2_1v8a0_curr(&curr[3]))
    module.vddq_vdd2_1v8ao_ = static_cast<int32_t>(volt[3] * curr[3] / 1000.0);
  else
    module.vddq_vdd2_1v8ao_ = INT32_MAX;

  return 0;
}
std::string vh::AcuOrinModule::print_mt_msg(const AcuMtOrinModuleSt &module) {
  std::stringstream ss;
  ss << ModuleDebug << "cpu_temp=" << module.cpu_temp_
     << ", gpu_temp=" << module.gpu_temp_ << ", soc0_temp=" << module.soc0_temp_
     << ", soc1_temp=" << module.soc1_temp_
     << ", soc2_temp=" << module.soc2_temp_ << ", tj_temp=" << module.tj_temp_
     << ", tb_temp=" << module.tboard_temp_
     << ", tdiode_temp=" << module.tdiode_temp_
     << ", gpu_soc_power=" << module.vdd_gpu_soc_
     << ", cpu_cv_power=" << module.vdd_cpu_cv_
     << ", sys_5v0_power=" << module.vin_sys_5v0_
     << ", vdd2_1v8a0_power=" << module.vddq_vdd2_1v8ao_;
  return ss.str();
}
int vh::AcuOrinModule::test_mt_msg(int repeat, uint16_t ms) {
  mt_init(nullptr);
  do {
    AcuMtOrinModuleSt module;
    get_mt_msg(module);
    std::cout << print_mt_msg(module) << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(ms));
  } while (--repeat);
  mt_deinit();
  return 0;
}

int vh::AcuOrinModule::get_orin_temp_interface(int32_t *temp,
                                               const char *name) {
  std::unordered_map<std::string, std::string> names = {
      {"cpu_temp", "/sys/class/thermal/thermal_zone0/temp"},
      {"gpu_temp", "/sys/class/thermal/thermal_zone1/temp"},
      {"soc0_temp", "/sys/class/thermal/thermal_zone5/temp"},
      {"soc1_temp", "/sys/class/thermal/thermal_zone6/temp"},
      {"soc2_temp", "/sys/class/thermal/thermal_zone7/temp"},
      {"tj_temp", "/sys/class/thermal/thermal_zone8/temp"},
      {"tb_temp", "/sys/class/thermal/thermal_zone9/temp"},
      {"tdiode_temp", "/sys/class/thermal/thermal_zone10/temp"},
  };
  if (names.find(name) == names.end()) return -1;
  FILE *fp = nullptr;
  if ((fp = fopen(names[name].c_str(), "r")) == nullptr) return -1;
  if (fscanf(fp, "%d", temp) == EOF) goto err_exit;  // unit 0.001'C
  fclose(fp);
  return 0;
err_exit:
  if (fp != nullptr) fclose(fp);
  *temp = INT32_MIN;
  return -1;
}
int vh::AcuOrinModule::get_orin_cpu_temp(int32_t *temp) {
  return get_orin_temp_interface(temp, "cpu_temp");
}
int vh::AcuOrinModule::get_orin_gpu_temp(int32_t *temp) {
  return get_orin_temp_interface(temp, "gpu_temp");
}
int vh::AcuOrinModule::get_orin_soc0_temp(int32_t *temp) {
  return get_orin_temp_interface(temp, "soc0_temp");
}
int vh::AcuOrinModule::get_orin_soc1_temp(int32_t *temp) {
  return get_orin_temp_interface(temp, "soc1_temp");
}
int vh::AcuOrinModule::get_orin_soc2_temp(int32_t *temp) {
  return get_orin_temp_interface(temp, "soc2_temp");
}
int vh::AcuOrinModule::get_orin_tb_temp(int32_t *temp) {
  return get_orin_temp_interface(temp, "tb_temp");
}
int vh::AcuOrinModule::get_orin_tj_temp(int32_t *temp) {
  return get_orin_temp_interface(temp, "tj_temp");
}
int vh::AcuOrinModule::get_orin_tdiode_temp(int32_t *temp) {
  return get_orin_temp_interface(temp, "tdiode_temp");
}

int vh::AcuOrinModule::get_orin_volt_interface(int32_t *volt,
                                               const char *name) {
  std::unordered_map<std::string, std::string> names = {
      {"gpu_soc_volt", "/sys/class/hwmon/hwmon1/in1_input"},
      {"cpu_cv_volt", "/sys/class/hwmon/hwmon1/in2_input"},
      {"sys_5v0_volt", "/sys/class/hwmon/hwmon1/in3_input"},
      {"vdd2_1v8a0_volt", "/sys/class/hwmon/hwmon2/in2_input"},
  };
  if (names.find(name) == names.end()) return -1;
  FILE *fp = nullptr;
  if ((fp = fopen(names[name].c_str(), "r")) == nullptr) return -1;
  if (fscanf(fp, "%d", volt) == EOF) goto err_exit;
  fclose(fp);
  return 0;
err_exit:
  if (fp != nullptr) fclose(fp);
  *volt = INT32_MIN;
  return -1;
}
int vh::AcuOrinModule::get_orin_gpu_soc_volt(int32_t *volt) {
  return get_orin_volt_interface(volt, "gpu_soc_volt");
}
int vh::AcuOrinModule::get_orin_cpu_cv_volt(int32_t *volt) {
  return get_orin_volt_interface(volt, "cpu_cv_volt");
}
int vh::AcuOrinModule::get_orin_sys_5v0_volt(int32_t *volt) {
  return get_orin_volt_interface(volt, "sys_5v0_volt");
}
int vh::AcuOrinModule::get_orin_vdd2_1v8a0_volt(int32_t *volt) {
  return get_orin_volt_interface(volt, "vdd2_1v8a0_volt");
}

int vh::AcuOrinModule::get_orin_curr_interface(int32_t *curr,
                                               const char *name) {
  std::unordered_map<std::string, std::string> names = {
      {"gpu_soc_curr", "/sys/class/hwmon/hwmon1/curr1_input"},
      {"cpu_cv_curr", "/sys/class/hwmon/hwmon1/curr2_input"},
      {"sys_5v0_curr", "/sys/class/hwmon/hwmon1/curr3_input"},
      {"vdd2_1v8a0_curr", "/sys/class/hwmon/hwmon2/curr2_input"},
  };
  if (names.find(name) == names.end()) return -1;
  FILE *fp = nullptr;
  if ((fp = fopen(names[name].c_str(), "r")) == nullptr) return -1;
  if (fscanf(fp, "%d", curr) == EOF) goto err_exit;
  fclose(fp);
  return 0;
err_exit:
  if (fp != nullptr) fclose(fp);
  *curr = INT32_MIN;
  return -1;
}
int vh::AcuOrinModule::get_orin_gpu_soc_curr(int32_t *curr) {
  return get_orin_curr_interface(curr, "gpu_soc_curr");
}
int vh::AcuOrinModule::get_orin_cpu_cv_curr(int32_t *curr) {
  return get_orin_curr_interface(curr, "cpu_cv_curr");
}
int vh::AcuOrinModule::get_orin_sys_5v0_curr(int32_t *curr) {
  return get_orin_curr_interface(curr, "sys_5v0_curr");
}
int vh::AcuOrinModule::get_orin_vdd2_1v8a0_curr(int32_t *curr) {
  return get_orin_curr_interface(curr, "vdd2_1v8a0_curr");
}

int vh::AcuOrinModule::get_orin_gpu_load(int32_t *load) {
  static const std::string path("/sys/devices/platform/17000000.gpu/load");
  FILE *fp = nullptr;
  if ((fp = fopen(path.c_str(), "r")) == nullptr) return -1;
  if (fscanf(fp, "%d", load) == EOF) goto err_exit;
  *load /= 10;
  fclose(fp);
  return 0;
err_exit:
  if (fp != nullptr) fclose(fp);
  *load = INT32_MIN;
  return -1;
}
int vh::AcuOrinModule::get_orin_gpu_freq(int32_t *freq) {
  static const std::string path(
      "/sys/devices/platform/17000000.gpu/devfreq/17000000.gpu/cur_freq ");
  FILE *fp = nullptr;
  if ((fp = fopen(path.c_str(), "r")) == nullptr) return -1;
  if (fscanf(fp, "%d", freq) == EOF) goto err_exit;
  fclose(fp);
  return 0;
err_exit:
  if (fp != nullptr) fclose(fp);
  *freq = INT32_MIN;
  return -1;
}
