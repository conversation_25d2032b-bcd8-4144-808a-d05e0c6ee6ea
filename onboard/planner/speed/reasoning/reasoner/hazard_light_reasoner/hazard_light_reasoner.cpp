#include "planner/speed/reasoning/reasoner/hazard_light_reasoner/hazard_light_reasoner.h"

#include "planner_protos/speed_reasoning_debug.pb.h"
#include "rt_event/rt_event.h"
#include "voy_rt_event/rt_event_planner.h"

namespace planner {
namespace speed {
namespace reasoner {
bool HazardLightReasoner::Reason(
    const WorldContext& world_context,
    const speed::TrajectoryInfo& trajectory_info,
    const std::unordered_map<ConstructionZoneId, const ConstructionZone*>&
        cz_ptr_map,
    HazardLightRequest* hazard_light_request_ptr,
    speed::pb::ReasoningDebug* debug_proto_ptr) {
  pb::HazardLightReasonerDebug* hazard_light_reasoner_debug =
      debug_proto_ptr ? debug_proto_ptr->mutable_reasoner_debug()
                            ->mutable_hazard_light_reasoner()
                      : nullptr;

  if (ShouldActivateHazardLightForMRC(world_context,
                                      hazard_light_reasoner_debug)) {
    hazard_light_request_ptr->set_activate_hazard_light_for_mrc(true);
  }

  // When pull over is completed, hazard light needs to be turned on.
  if (trajectory_info.pull_over_progress() ==
      planner::pb::PullOverProgress::kCompleted) {
    if (hazard_light_reasoner_debug != nullptr) {
      hazard_light_reasoner_debug->set_debug_str(
          "Hazard light request for pull over completed.");
      hazard_light_reasoner_debug->set_hazard_light_state(
          speed::pb::HazardLightReasonerDebug::ON_BY_PLANNER);
    }
    hazard_light_request_ptr->set_activate_hazard_light_for_pull_over(true);
  }

  // Add map-changed cz to hazard light request.
  if (AddHazardLightAllowedConstructionZonesToRequest(
          cz_ptr_map, hazard_light_request_ptr)) {
    if (hazard_light_reasoner_debug != nullptr) {
      hazard_light_reasoner_debug->set_debug_str(
          "Hazard light request for map changed.");
      hazard_light_reasoner_debug->set_hazard_light_state(
          speed::pb::HazardLightReasonerDebug::ON_BY_PLANNER);
    }
  }

  return true;
}

bool HazardLightReasoner::AddHazardLightAllowedConstructionZonesToRequest(
    const std::unordered_map<ConstructionZoneId, const ConstructionZone*>&
        cz_ptr_map,
    HazardLightRequest* hazard_light_request_ptr) {
  bool added_cz_request = false;

  for (const auto& [cz_id, cz_ptr] : cz_ptr_map) {
    if (cz_ptr->IsMapChangedArea()) {
      hazard_light_request_ptr->add_hazard_light_construction_zone(cz_id);
      added_cz_request = true;
    }
  }

  return added_cz_request;
}

bool HazardLightReasoner::ShouldActivateHazardLightForMRC(
    const WorldContext& world_context,
    pb::HazardLightReasonerDebug* hazard_light_reasoner_debug) {
  if (!world_context.should_planner_respond_mrc() ||
      world_context.mrc_request_ptr() == nullptr) {
    // Returns early.
    return false;
  }

  const auto& mrc_request = *world_context.mrc_request_ptr();

  // When planner receives mrm inlane or mrm urgency, hazard light needs to
  // be turned on.
  if (mrc_request.mrm_progress() == mrc::pb::ONGOING &&
      (mrc_request.mrm_type() == mrc::pb::MRM_INLANE ||
       mrc_request.mrm_type() == mrc::pb::MRM_URGENCY)) {
    if (hazard_light_reasoner_debug != nullptr) {
      hazard_light_reasoner_debug->set_debug_str(
          "Hazard light request for mrm_inlane or mrm_urgency.");
      hazard_light_reasoner_debug->set_hazard_light_state(
          speed::pb::HazardLightReasonerDebug::ON_BY_PLANNER);
    }
    return true;
  }

  // When ego has entered mrc state, hazard light needs to be
  // turned on.
  if (mrc_request.mrm_progress() == mrc::pb::FINISHED) {
    if (hazard_light_reasoner_debug != nullptr) {
      hazard_light_reasoner_debug->set_debug_str(
          "Hazard light request for entering mrc state.");
      hazard_light_reasoner_debug->set_hazard_light_state(
          speed::pb::HazardLightReasonerDebug::ON_BY_PLANNER);
    }
    return true;
  }

  return false;
}

}  // namespace reasoner
}  // namespace speed
}  // namespace planner
