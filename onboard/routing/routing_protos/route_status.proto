syntax = "proto3";

package routing.pb;

import "routing_protos/constraint_boundary.proto";
import "routing_protos/route_command.proto";
import "routing_protos/route_confirmation.proto";
import "routing_protos/route_query.proto";
import "routing_protos/route_solution.proto";
import "routing_protos/routing_debug.proto";
import "voy_protos/point.proto";
import "voy_protos/pose.proto";
import "voy_protos/routing.proto";

// An upcoming waypoint contains the waypoint info with distance and time from
// ego car.
message UpcomingWaypoint {
  WaypointInfo waypoint_info = 1;
  double dist_from_ego_car_m = 2;
  double time_from_ego_car_s = 3;
}

enum ProposingReason {
  kNewRoute = 0;
  kReplanning = 1;
  kCancellation = 2;
  kCompletion = 3;
}

// It is used to represent struct BrokenTrafficLightsInfo from real_time_map.
// Next Available ID: 4
message BrokenTrafficLightsInfo {
  // Timestamp of latest received RealtimeMapTrafficLights's
  // latest_label_timestamp.
  int64 updated_timestamp = 1;
  bool has_broken_traffic_lights = 2;
  repeated int64 broken_light_associated_lane_ids = 3;
}

// It is used to represent struct DynamicMapElementInfo from real_time_map.
// Next Available ID: 5
message ConstructionZoneInfo {
  int64 updated_timestamp = 1 [ deprecated = true ];
  // The update timestamp of construction zones.
  int64 construction_zone_updated_timestamp = 3;
  // The update timestamp of map change areas.
  int64 map_change_area_update_timestamp = 4;
  repeated int64 construction_zone_associated_lane_ids = 2;
}

// It is the immediate pull over state, will be used by order service.
// Next Available ID: 4
enum ImmediatePullOverState {
  // Default state.
  kNotTriggered = 0;
  // Change to Triggered and keep once received from route_command.
  kTriggered = 1;
  // Change to Failed when received terminate signal from planning lane
  // sequence.
  kFailed = 2;
  // Change to Success state when pullover successes (Route Complete).
  kSuccess = 3;
}

// Reasons why immediate pull over state changes to kFailed.
// Next Available ID: 5
enum ImmediatePullOverFailedReason {
  kNotFailed = 0;
  // Planner failed to pull over.
  kPullOverFailed = 1;
  // Order triggered immediate pull over, but signal is timeout to pass to
  // planner node.
  kTriggerFailed = 2;
  // Order cancelled immediate pull over, but signal is timeout to pass to
  // planner node.
  kCancelFailed = 3;
  // Immediate pull over completet, order failed to continue trip.
  kContinueTripFailed = 4;
}

// Immediate pull over info, includes its state, parking location and source of
// request.
// Next Available ID: 6
message ImmediatePullOverInfo {
  // Routing node's immediate pullover state.
  ImmediatePullOverState immediate_pullover_state = 1;
  // Immediate pull over point selected by behavior.
  voy.Point2d immediate_pullover_point = 2;
  // Immediate pullover request's source.
  ImmediatePullOverSource immediate_pullover_source = 3;
  // Planner node's immediate pullover state which is does not include the
  // immediate pull over triggered by mrc.
  ImmediatePullOverState planner_immediate_pullover_state = 4;
  // Reasons why immediate pull over state changes to kFailed.
  optional ImmediatePullOverFailedReason immediate_pullover_failed_reason = 5;
}

// Constraint info, includes name, major version, minor version, whether we
// use constraint from cloud and is default. It also holds user defined
// constraint definitions used in routing engine, which will be used to draw
// constraints in offboard monitor.
// Next Available ID: 8
message ConstraintInfo {
  string constraint_name = 1;
  string major_version = 2;
  string minor_version = 3;
  bool is_from_cloud = 4;
  bool is_default = 5;
  ConstraintDefinitions constraint_definitions = 6;
  string constraint_error_msg = 7;
}

// Some special requests for planner to reponse.
// Next Available ID: 7
enum SpecialPlannerResponseRequest {
  kNoSpecialResponseRequest = 0;
  // Indicate should complete pull over sooner. This will be used when ego is in
  // static state and ops has confirmed ego can terminate pull over.
  kCompletePullOverSooner = 1;
  // Indicate when start a new query, pull out does not need to perform. Now it
  // is used when passenger changes destination.
  kNoNeedToPullOut = 2;
  // Indicate should help keep ego in static state. This is used when routing
  // changes to idle, planner should not response to route, but keep static.
  // Note: Currently we only use this when ego is already in static condition.
  kKeepEgoInStaticState = 3;
  // Indicate routing has searched failed with unknown reasons consecutively.
  // Planner node shall be alert to trigger MRC.
  kTriggerMRCDueToRoutingFailure = 4;
  // Indicate routing skipped a route by sending route complete in idle state,
  // and do not want to sync this route to planner node.
  kNotUpdateRouteStatusForSkippedRouteDuringIdle = 5;
  // Indicate should not consider as new trip for planner node, even if mission
  // id updates from order service node.
  kNotConsiderAsNewTripForMissionChangedRoute = 6;
}

// It holds the basic message of a route.
message RouteMetaBase {
  // The ID of a route.
  string route_id = 1;
}

// It holds the information of the route change.
// Next Available ID: 4
message RouteChangeInfo {
  // It indicate whether the route has changed.
  bool is_route_change = 1;
  // The enums to indicate the change reason.
  enum RouteChangeReason {
    // The unknown change reason.
    kUnknownRouteChangeReason = 0;
    // The route change for a new route.
    kRouteChangeForNewRideRideQuery = 1;
    // The route change for the old route is not passable anymore and the
    // planner change to a new route.
    kRouteChangeForRerouteForGlobalRouteUnPassable = 2;
  }
  // The change reasons.
  repeated RouteChangeReason change_reasons = 2;
  // Stores route change factors from planner if the route change reason is
  // |kRouteChangeForRerouteForGlobalRouteUnPassable|.
  repeated PlannerActiveRerouteFactors active_reroute_factors = 3;
}

// It hold the basic information about the (current/last) route and the change
// reason.
message RouteMeta {
  // The map region.
  string region = 1;
  // The version of geometry in hdmap.
  string geometry_version = 2;
  // The basic information of the current route.
  RouteMetaBase current_route_meta = 3;
  // The basic information of the last route.
  optional RouteMetaBase last_route_meta = 4;
  // The information about route change.
  RouteChangeInfo route_change_info = 5;
}

// A message to store light route.
// Next Available ID: 5
message LightRoute {
  // Total distance of this route, in meters.
  double total_dist_m = 1;
  // Total estimate travel time, in seconds.
  double total_travel_time_s = 2;
  // The onboard route.
  repeated RouteLane route_lanes = 3;
  // The source of route lanes.
  RouteLanesSource route_lanes_source = 4;
}

// The enums to indicate the reason of not following cloud route.
// Next Available ID: 10
enum InfeasibleReason {
  // The unknown reject reason.
  kUnknownRejectReason = 0;
  // The cloud route is empty.
  kEmptyCloudRoute = 1;
  // The cloud route is not in hdmap.
  kCloudRouteBeyondHdMap = 2;
  // The origin is not on cloud route.
  kOriginNotOnCloudRoute = 3;
  // The cloud route is not in cost map.
  kCloudRouteNotInCostMap = 4;
  // The cloud route is blocked by construction and the onboard is
  // not.
  kCloudRouteIsBlockedByCZ = 5;
  // The cloud route is hard constrained.
  kCloudRouteHardConstrained = 6;
  // The onboard route is loop back route.
  kLoopBackRoute = 7;
  // The hdmap version of the cloud route is not match with the
  // hdmap version on the vehicle.
  kHdmapVersionMismatch = 8;
  // The last lane of cloud route is not in destination lane candidates.
  kLastRouteLaneNotInDestLaneCandidates = 9;
}

// Selection meta between cloud route and onboard route.
// Next Available ID: 5
message CloudRouteSelectionMeta {
  // A boolean indicate whether the cloud route is feasible.
  bool is_cloud_route_feasible = 1;
  // Infeasible reasons of cloud route.
  repeated InfeasibleReason infeasible_reasons = 2;
  // The cloud route info from order command.
  RouteInfo cloud_route_info = 3;
  // The light onboard route replaced by cloud route.
  optional LightRoute replaced_route = 4;
}

// The enums to indicate current route display state.
// Next Available ID: 4
enum RouteDisplayState {
  kUnknownState = 0;
  kDisplay = 1;
  kNotDisplay = 2;
  kNotDisplayETA = 3;
}

// A message of route state.
// Next Available ID: 43
message RouteStatus {
  // Indicates the distance and time are valid, user of those should check this
  // flag.
  bool is_valid = 1;
  // Current used route solution which is accepted by planning..
  RouteSolution current_route_solution = 2;
  // Latest proposed route solution to planning.
  RouteSolution proposed_route_solution = 3;
  // Current vehicle pose.
  voy.Pose current_pose = 4;
  // Current lane ids where vehicle is on, from planning node.
  repeated int64 current_lane_ids = 5;
  // Distance to destination in meters.
  double dist_to_dest_m = 6;
  // Time to destination in seconds.
  double time_to_dest_s = 7;
  // Distance to this route end, it will be same as dist_to_dest_m if this is
  // the last partial route.
  double dist_to_route_end_m = 8 [ deprecated = true ];
  // Time to this route end, it will be same as dist_to_dest_m if this is
  // the last partial route.
  double time_to_route_end_s = 9 [ deprecated = true ];
  // Upcoming waypoints with dist and time from ego car, none if all waypoints
  // are passed.
  repeated UpcomingWaypoint upcoming_waypoints = 10;

  // The user defined constraint definitions used in routing engine.
  // This is used for draw constraints in offboard monitor.
  ConstraintDefinitions constraint_definitions = 11 [ deprecated = true ];

  // The points of current route, for displaying on app only.
  repeated voy.Point2d current_display_route = 12;

  // Proposing reason for planning, different reason may have different logic.
  ProposingReason proposing_reason = 13;

  // The original ride query for the accepted route solution.
  RouteQuery ride_route_query = 14;

  // The current route query corresponding to the current route solution above
  // at #2.
  RouteQuery current_route_query = 15;

  // Current route query is replanning query or not.
  bool is_replanning_query = 16;

  // Current waypoint index.
  int32 waypoint_index = 17;

  // Source of the query, ride query OR replaced from map, seed OR replaced from
  // map, etc.
  SourceOfQuery source_of_query = 18;

  // Internal routing mission state, idle, proposing, ontrip, cancelling,
  // completing.
  MissionState mission_state = 19;

  // The route status for the current query, e.g, routing success/failure,
  // planning acceptance status OR cancellation/completion status.
  RouteState route_state = 20;

  // The failure reasons of routing. If succeed, it should be empty.
  repeated string routing_failure_reasons = 21;

  // The route divergence warning.
  voy.RouteDivergenceWarning route_divergence_warning = 22;

  // The number of waypoints ego has passed for the current route solution.
  // Refer to the waypoints in the current route query.
  int32 waypoint_count = 23;

  // The proposed waypoint count together with proposed route solution.
  int32 proposed_waypoint_count = 24;

  // Stores the arrived section ids after passing a route point.
  // The route point can be route origin or a route waypoint.
  // If ego does not start at the route point, then first section id is from the
  // ego start section.
  repeated int64 arrived_section_ids_from_current_route_point = 25;

  // The arrived section ids after passing a route point for the proposed route
  // solution.
  repeated int64 proposed_arrived_section_ids_from_current_route_point = 26;

  // The proposed route query corresponding to the proposed route solution above
  // at #3.
  RouteQuery proposed_route_query = 27;

  // A list of lane ids that are associated with broken traffic lights and its
  // published timestamp for the whole region.
  BrokenTrafficLightsInfo broken_traffic_lights_info = 28;

  // Info that used for immediate pull over, includes its state, parking
  // location and source of request.
  ImmediatePullOverInfo immediate_pullover_info = 29;

  // Constraint info, includes name, major_version, minor_version,
  // is_from_cloud.
  ConstraintInfo constraint_info = 30;

  // Ride start vehicle pose. It may not be the order origin if ego are not at
  // order origin when ride query is received.
  voy.Pose ride_start_pose = 31;

  // When propose new route solution to planner, use this field.
  voy.Pose proposed_ride_start_pose = 32;

  // Stores the arrived road ids since ride start.
  // If ego does not start at the order origin, then first road id is from the
  // ego start section.
  repeated int64 arrived_road_ids_from_ride_start = 33;

  // When propose new route solution to planner, use this field.
  // In this way, we don't loose the original arrived_road_ids_from_ride_start
  // if the proposed solution is not accepted.
  repeated int64 proposed_arrived_road_ids_from_ride_start = 34;
  // A list of lane ids that are associated with construction zones and its
  // published timestamp for the whole region.
  ConstructionZoneInfo construction_zones_info = 35;
  // The mission id for local route, which might be different from current route
  // solution mission id.
  int64 mission_id_for_local_route = 42;
  // Distance to destination in meters. It will keep coincident with local
  // route.
  double dist_to_dest_in_local_m = 36;
  // Time to destination in seconds. It will keep coincident with local route.
  double time_to_dest_in_local_s = 37;
  // A special request that prefer planner to reponse.
  SpecialPlannerResponseRequest special_planner_response_request = 38;
  // The meta information of current and last route.
  optional RouteMeta route_meta_data = 39;
  // The selection between cloud route and onboard route.
  optional CloudRouteSelectionMeta cloud_route_selection_meta = 40;
  // Indicate display route of current route.
  RouteDisplayState route_display_state = 41;
}