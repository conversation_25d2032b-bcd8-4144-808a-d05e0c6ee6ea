#include "planner/path/reasoning/agent_intention/semantic_context/frame_analyzer/ego_lane_frame_analyzer.h"

#include <limits>
#include <map>
#include <vector>

#include <gtest/gtest.h>

#include "planner/path/reasoning/agent_intention/semantic_context/frame_analyzer/road_section_frame_analyzer.h"
#include "planner/path/reasoning/agent_intention/semantic_context/test/test_utility.h"
#include "planner/speed/reasoning/test/reasoning_test_fixture.h"
#include "planner/speed/test_util/test_util.h"
#include "planner_protos/behavior_reasoner_seed.pb.h"
#include "planner_protos/path_reasoning_seed.pb.h"

namespace planner {
namespace path {

/*
 _____
|_____| -- dynamic agent 2
----------------------------   left boundary 1.0m
          _____
~~~~~~~~~|_____|~~~~~~~~~~~~   reference_line 0.0m
          cz 1
---------------------------- right boundary -1.0m
                 ____
                |____| -- static agent 3
*/
class EgoLaneFrameAnalyzerTest : public ::testing::Test,
                                 public speed::ReasoningTestFixture {
 protected:
  void SetUp() override {
    SetUpSceneMap(hdmap::test_util::SceneType::kForklane);
    lane_sequence_geometry_.nominal_path =
        planner::test::ReadLine("LINESTRING(0.0 0.0,10.0 0.0)");
    lane_sequence_geometry_.left_lane_boundary =
        planner::test::ReadLine("LINESTRING(0.0 1.0,10.0 1.0)");
    lane_sequence_geometry_.right_lane_boundary =
        planner::test::ReadLine("LINESTRING(0.0 -1.0,10.0 -1.0)");
    lane_sequence_geometry_.left_hard_boundary_lines = {
        planner::test::ReadLine("LINESTRING(0.0 3.0,3.0 3.0)"),
        planner::test::ReadLine("LINESTRING(5.0 3.0,8.0 3.0)")};
    lane_sequence_geometry_.right_hard_boundary_lines = {
        planner::test::ReadLine("LINESTRING(1.0 -3.0,4.0 -3.0)"),
        planner::test::ReadLine("LINESTRING(6.0 -3.0,9.0 -3.0)")};
    lane_sequence_geometry_.lane_sequence_contour =
        lane_selection::GetLaneBoundaryBorder(
            lane_sequence_geometry_.left_lane_boundary,
            lane_sequence_geometry_.right_lane_boundary,
            lane_sequence_geometry_.nominal_path);

    planning_horizon_range_ = {
        lane_sequence_geometry_.nominal_path.GetStartPoint().x(),
        lane_sequence_geometry_.nominal_path.GetEndPoint().x()};

    ego_param_ = planner::test::GetEgoInLaneParams();

    planner::test::ConstructConstructionZoneInLaneState(
        ego_param_, lane_sequence_geometry_, /*cz_id=*/1,
        /*cz_polygon=*/
        math::geometry::Polygon2d({math::geometry::Point2d(3.0, -0.8),
                                   math::geometry::Point2d(5.0, -0.8),
                                   math::geometry::Point2d(5.0, 0.8),
                                   math::geometry::Point2d(3.0, 0.8)}),
        &construction_zones_state_);
    planner::test::ConstructDynamicAgentInLaneState(
        lane_sequence_geometry_, /*obj_id=*/2,
        /*x0=*/1.0, /*y0=*/2.0,
        /*width=*/1.0, /*speed=*/1.0, agent_state_map_);
    planner::test::ConstructStaticAgentInLaneState(
        lane_sequence_geometry_, /*obj_id=*/3,
        /*obj_polygon=*/
        math::geometry::Polygon2d({math::geometry::Point2d(6.0, -2.5),
                                   math::geometry::Point2d(8.0, -2.5),
                                   math::geometry::Point2d(8.0, -1.5),
                                   math::geometry::Point2d(6.0, -1.5)}),
        agent_state_map_);

    object_reasoning_info_map_ = test::GenerateObjectReasoningInfoMap(
        agent_state_map_, construction_zones_state_,
        lane_sequence_geometry_.nominal_path);
    // Generate the object reasoning info map from the object occupancy states.
    // Construct a static object occupancy state.
    auto& static_tracked_object = *static_agent_proto_.mutable_tracked_object();
    static_tracked_object.set_id(10);
    static_tracked_object.add_attributes(voy::perception::HAS_EXACT_SHAPE);
    static_tracked_object.set_object_type(voy::perception::ObjectType::VEHICLE);
    math::geometry::Convert(/*obj_polygon=*/
                            math::geometry::Polygon2d(
                                {math::geometry::Point2d(6.0, -2.5),
                                 math::geometry::Point2d(8.0, -2.5),
                                 math::geometry::Point2d(8.0, -1.5),
                                 math::geometry::Point2d(6.0, -1.5)}),
                            *static_tracked_object.mutable_contour());
    static_planner_object_ = std::make_unique<PlannerObject>(
        static_agent_proto_, /*timestamp=*/1,
        TrafficParticipantPose(/*timestamp=*/1, static_tracked_object));
    static_planner_object_->set_is_primary_stationary(true);
    prediction::pb::PredictedTrajectory static_predicted_trajectory;
    static_predicted_trajectory.set_id(1);
    static_predicted_trajectory.add_traj_poses();
    predicted_trajectory_wrappers_.reserve(10);
    predicted_trajectory_wrappers_.push_back({PredictedTrajectoryWrapper(
        voy::TrackedObject(), static_predicted_trajectory,
        /*is_primary_trajectory=*/true)});
    ObjectOccupancyState static_object_state = GenerateObjectOccupancyState(
        *static_planner_object_, /*object_information=*/{},
        predicted_trajectory_wrappers_.back(),
        lane_sequence_geometry_.nominal_path,
        lane_sequence_geometry_.left_lane_boundary,
        lane_sequence_geometry_.right_lane_boundary, ego_param_,
        pb::MotionMode::FORWARD);
    // Construct a dynamic object occupancy state.
    dynamic_agent_proto_.mutable_tracked_object()->CopyFrom(
        planner::test::ConstructTrackedObjectFromDimension(
            /*obj_id=*/20,
            /*x0=*/1.0,
            /*y0=*/2.0,
            /*width=*/1.0, /*length=*/2.0, /*height=*/2.0, /*velocity=*/1.0,
            /*heading=*/0.0));
    predicted_trajectory_wrappers_.push_back(
        {planner::test::ConstructPredictedTrajectoryWrapperForTest(
            dynamic_agent_proto_.tracked_object(), proto_traj_)});
    dynamic_planner_object_ = std::make_unique<PlannerObject>(
        dynamic_agent_proto_, /*timestamp=*/1,
        TrafficParticipantPose(/*timestamp=*/1,
                               dynamic_agent_proto_.tracked_object()));
    ObjectOccupancyState dynamic_object_state = GenerateObjectOccupancyState(
        *dynamic_planner_object_, /*object_information=*/{},
        predicted_trajectory_wrappers_.back(),
        lane_sequence_geometry_.nominal_path,
        lane_sequence_geometry_.left_lane_boundary,
        lane_sequence_geometry_.right_lane_boundary, ego_param_,
        pb::MotionMode::FORWARD);
    object_occupancy_state_map_.emplace(
        static_object_state.planner_object().tracked_object().id(),
        std::make_unique<ObjectOccupancyState>(static_object_state));
    object_occupancy_state_map_.emplace(
        dynamic_object_state.planner_object().tracked_object().id(),
        std::make_unique<ObjectOccupancyState>(dynamic_object_state));

    object_reasoning_info_map_2 =
        test::GenerateObjectReasoningInfoMapFromOccupancyState(
            object_occupancy_state_map_, construction_zones_state_,
            lane_sequence_geometry_.nominal_path, ego_param_);

    // Initialize pnc map service.
    lane_sequence_ = GetLaneSequenceFromId({128229, 128253, 128237});

    speed_upper_bound_ = speed::CreateConstantSpeedProfileFromTime(
        /*t0=*/0.0, /*x0=*/0.0, /*v0=*/8.0, /*num_step=*/80, /*dt=*/0.1);

    agent_intention_config_ =
        PlannerConfigCenter::GetInstance().GetAgentIntentionConfig(
            pb::LANE_KEEP);
  }

  EgoInLaneParams ego_param_;
  lane_selection::LaneSequenceGeometry lane_sequence_geometry_;
  math::Range1d planning_horizon_range_;

  AgentInLaneStatesMap agent_state_map_;
  std::vector<ConstructionZoneInLaneState> construction_zones_state_;

  std::map<TypedObjectId, ObjectReasoningInfo> object_reasoning_info_map_;

  prediction::pb::Agent static_agent_proto_;
  std::unique_ptr<PlannerObject> static_planner_object_ = nullptr;
  prediction::pb::Agent dynamic_agent_proto_;
  std::unique_ptr<PlannerObject> dynamic_planner_object_ = nullptr;
  std::vector<std::vector<PredictedTrajectoryWrapper>>
      predicted_trajectory_wrappers_;
  prediction::pb::PredictedTrajectory proto_traj_;
  ObjectOccupancyStateMap object_occupancy_state_map_;
  std::map<TypedObjectId, ObjectReasoningInfo> object_reasoning_info_map_2;
  std::vector<const pnc_map::Lane*> lane_sequence_;
  speed::Profile speed_upper_bound_;
  pb::AgentIntentionConfig agent_intention_config_;

  RoadSectionFrameAnalyzer road_section_frame_analyzer_ =
      RoadSectionFrameAnalyzer(pb::BehaviorType::LANE_KEEP);
  lane_selection::LaneSequenceTrafficRules traffic_rules_;
  pb::NarrowMeetingSeed last_narrow_meeting_seed_;
};

TEST_F(EgoLaneFrameAnalyzerTest, InLaneFrameTest) {
  EgoLaneFrameAnalyzer ego_lane_frame_analyzer(pb::BehaviorType::LANE_KEEP,
                                               agent_intention_config_);
  // Interface 1
  FrameAnalyzerResult result = ego_lane_frame_analyzer.Analyze(
      ego_param_, traffic_rules_, object_reasoning_info_map_);
  EXPECT_EQ(result.object_ids.size(), 1);
  EXPECT_EQ(result.lane_blockage_info.potential_stuck_object_ids.size(), 0);
  EXPECT_TRUE(result.lane_blockage_info.blockage_object_ids.empty());
  EXPECT_EQ(result.traffic_flow_info.traffic_flow_ids.size(), 0);
  EXPECT_EQ(result.traffic_flow_info.traffic_flow_queue.size(), 0);
  EXPECT_EQ(result.traffic_flow_info.slow_moving_vehicle_ids.size(), 0);

  EXPECT_EQ(result.inlane_nudge_objects_info.pass_left_object_ids.size(), 1);
  EXPECT_EQ(result.inlane_nudge_objects_info.pass_right_object_ids.size(), 0);
  // Test result from object occupancy states.
  FLAGS_planning_enable_object_occupancy_state = true;
  FrameAnalyzerResult result_2 = ego_lane_frame_analyzer.Analyze(
      ego_param_, traffic_rules_, object_reasoning_info_map_2);
  EXPECT_EQ(result_2.object_ids.size(), 1);
  EXPECT_EQ(result_2.lane_blockage_info.potential_stuck_object_ids.size(), 0);
  EXPECT_TRUE(result_2.lane_blockage_info.blockage_object_ids.empty());
  EXPECT_EQ(result_2.traffic_flow_info.traffic_flow_ids.size(), 0);
  EXPECT_EQ(result_2.traffic_flow_info.traffic_flow_queue.size(), 0);
  EXPECT_EQ(result_2.traffic_flow_info.slow_moving_vehicle_ids.size(), 0);

  EXPECT_EQ(result_2.inlane_nudge_objects_info.pass_left_object_ids.size(), 1);
  EXPECT_EQ(result_2.inlane_nudge_objects_info.pass_right_object_ids.size(), 0);
  // Interface 2
  // we extend lane marking buffer with 1.0m
  FLAGS_planning_enable_object_occupancy_state = false;
  pb::DecoupledManeuverSeed previous_iter_seed;
  pb::PathReasoningSeed current_seed;
  SetEgoPose(/*x=*/0.1, /*y=*/0.0, /*yaw=*/0.0, /*speed=*/0.0, /*accel=*/0.0);
  result = ego_lane_frame_analyzer.Analyze(
      world_model(), speed_upper_bound_,
      lane_selection::LaneSequenceCandidates(/*waypoint_cost_map_in=*/nullptr),
      /*current_lane=*/*DCHECK_NOTNULL(lane_sequence_[0]), lane_sequence_,
      lane_sequence_geometry_, traffic_rules_, planning_horizon_range_,
      ego_param_, object_reasoning_info_map_,
      /*assist_instruction=*/nullptr, lateral_clearance::LateralClearanceData(),
      previous_iter_seed, &current_seed, /*debug=*/nullptr);

  EXPECT_EQ(result.object_ids.size(), 1);
  // lane_corridor.left_boundary: LINESTRING(0 2,10 2)
  // lane_corridor.right_boundary: LINESTRING(0 -2,10 -2)
  // potential_stuck_object is obtained from lane_corridor with cz1 and
  // static_agent3.
  EXPECT_EQ(result.lane_blockage_info.potential_stuck_object_ids.size(), 3);
  // stuck is detected in inlane frame with cz1.
  EXPECT_EQ(result.lane_blockage_info.blockage_object_ids.size(), 1);
  EXPECT_EQ(result.lane_blockage_info.blockage_type,
            pb::FrameBlockageInfo::IS_STUCK);
  EXPECT_EQ(result.traffic_flow_info.traffic_flow_ids.size(), 0);
  EXPECT_EQ(result.traffic_flow_info.traffic_flow_queue.size(), 0);
  EXPECT_EQ(result.traffic_flow_info.slow_moving_vehicle_ids.size(), 0);
  // Test results from object occupancy states.
  FLAGS_planning_enable_object_occupancy_state = true;
  result_2 = ego_lane_frame_analyzer.Analyze(
      world_model(), speed_upper_bound_,
      lane_selection::LaneSequenceCandidates(/*waypoint_cost_map_in=*/nullptr),
      /*current_lane=*/*DCHECK_NOTNULL(lane_sequence_[0]), lane_sequence_,
      lane_sequence_geometry_, traffic_rules_, planning_horizon_range_,
      ego_param_, object_reasoning_info_map_2,
      /*assist_instruction=*/nullptr, lateral_clearance::LateralClearanceData(),
      previous_iter_seed, &current_seed, /*debug=*/nullptr);

  EXPECT_EQ(result_2.object_ids.size(), 1);
  // lane_corridor.left_boundary: LINESTRING(0 2,10 2)
  // lane_corridor.right_boundary: LINESTRING(0 -2,10 -2)
  // potential_stuck_object is obtained from lane_corridor with cz1 and
  // static_agent3.
  EXPECT_EQ(result_2.lane_blockage_info.potential_stuck_object_ids.size(), 3);
  // stuck is detected in inlane frame with cz1.
  EXPECT_EQ(result_2.lane_blockage_info.blockage_object_ids.size(), 1);
  EXPECT_EQ(result_2.traffic_flow_info.traffic_flow_ids.size(), 0);
  EXPECT_EQ(result_2.traffic_flow_info.traffic_flow_queue.size(), 0);
  EXPECT_EQ(result_2.traffic_flow_info.slow_moving_vehicle_ids.size(), 0);
}

TEST_F(EgoLaneFrameAnalyzerTest, RoadSectionFrameTest) {
  FLAGS_planning_enable_object_occupancy_state = false;
  FrameAnalyzerResult result = road_section_frame_analyzer_.Analyze(
      lane_sequence_geometry_, planning_horizon_range_,
      /*robot_snapshot=*/
      planner::test::GenerateRobotStateSnapshot(
          /*front_pose=*/math::geometry::Point2d{0.1, 0.0}),
      ego_param_, /*stationary_duration_map=*/{}, object_reasoning_info_map_,
      /*path_reasoning_seed=*/pb::PathReasoningSeed(),
      /*assist_instassist_instructionruciton=*/nullptr,
      lateral_clearance::LateralClearanceData(), last_narrow_meeting_seed_);

  EXPECT_EQ(result.object_ids.size(), 3);
  EXPECT_EQ(result.lane_blockage_info.potential_stuck_object_ids.size(), 2);
  EXPECT_TRUE(result.lane_blockage_info.blockage_object_ids.empty());
  EXPECT_EQ(result.lane_blockage_info.blockage_type,
            pb::FrameBlockageInfo::CAN_PASS_BOTH);
  EXPECT_EQ(result.traffic_flow_info.traffic_flow_ids.size(), 2);
  EXPECT_EQ(result.traffic_flow_info.traffic_flow_queue.size(), 2);
  EXPECT_EQ(result.traffic_flow_info.slow_moving_vehicle_ids.size(), 2);

  // Test results from object occupancy states.
  FLAGS_planning_enable_object_occupancy_state = true;
  FrameAnalyzerResult result_2 = road_section_frame_analyzer_.Analyze(
      lane_sequence_geometry_, planning_horizon_range_,
      /*robot_snapshot=*/
      planner::test::GenerateRobotStateSnapshot(
          /*front_pose=*/math::geometry::Point2d{0.1, 0.0}),
      ego_param_, /*stationary_duration_map=*/{}, object_reasoning_info_map_2,
      /*inlane_nudge_objects=*/{}, /*assist_instruction=*/nullptr,
      lateral_clearance::LateralClearanceData(), last_narrow_meeting_seed_);

  EXPECT_EQ(result_2.object_ids.size(), 3);
  EXPECT_EQ(result_2.lane_blockage_info.potential_stuck_object_ids.size(), 2);
  EXPECT_TRUE(result_2.lane_blockage_info.blockage_object_ids.empty());
  EXPECT_EQ(result_2.traffic_flow_info.traffic_flow_ids.size(), 2);
  EXPECT_EQ(result_2.traffic_flow_info.traffic_flow_queue.size(), 2);
  EXPECT_EQ(result_2.traffic_flow_info.slow_moving_vehicle_ids.size(), 2);
}

}  // namespace path
}  // namespace planner
