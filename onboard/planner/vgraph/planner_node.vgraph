# proto-file: varch/protos/vnode/vgraph_conf.proto
# proto-message: VForestConfig

vgraph {
  mode: kReality
  scheduler {
    worker_num: 10
    cpu_affinity: kRange
    worker: [
      {
        ids: [0,1]
        os_sched_policy: kSchedOther
        os_prio: 0
        stealing_from: [0,1]
      },
      {
        ids: [2]
        os_sched_policy: kSchedOther
        os_prio: 0
        stealing_from: [2]
      },
      {
        ids: [3]
        os_sched_policy: kSchedOther
        os_prio: 0
        stealing_from: [3]
      },
      {
        ids: [4]
        os_sched_policy: kSchedOther
        os_prio: 0
        stealing_from: [4]
      },
      {
        ids: [5,6,7]
        os_sched_policy: kSchedOther
        os_prio: 0
        stealing_from: [5,6,7]
      },
      {
        ids: [8]
        os_sched_policy: kSchedOther
        os_prio: 0
        stealing_from: [8]
      },
      {
        ids: [9]
        os_sched_policy: kSchedOther
        os_prio: 0
        stealing_from: [9]
      }
    ]
    group: [
      {
        name: "hdmap_group"
        workers: "0-1"
      },
      {
        name: "snapshot_group"
        workers: "2"
      },
      {
        name: "path_planner_group"
        workers: "3"
      },
      {
        name: "planning_publish_group"
        workers: "4"
      },
      {
        name: "publish_group"
        workers: "5-7"
      },
      {
        name: "speed_planner_group"
        workers: "8"
      },
      {
        name: "remote_assist_model_group"
        workers: "9"
      }
    ]
  }

  vnode: [
     {
      library: "${VOY_LIB_DIR}/libplanner_node.so"
      clazz: "PlannerPreCreateVNode"
      name: "planner_pre_create_vnode"
      sub {mode: kCallOnce}
     },
     {
      library: "${VOY_LIB_DIR}/libhdmap_vnodes.so"
      clazz: "HdmapVNode"
      name: "planner_hdmap"
      sub {
        mode: kPeriodic
        periodic_interval: 10
        topic: [
          {
            topic: "/planning/planning_lane_sequence"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/simulation/pose"
            transport_option: { type: kUDP }
            queue_size: 1
          }
        ]
      }

      pub: [
        {
          name: "tracker_message_pub"
          topic: "/planner/hdmap/tracker_message"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "hdmap_pose_pub"
          topic: "/planner/hdmap/pose"
          transport_option: { type: kIntraProcess }
        }
      ]
      params: [
        { key: "vnode_name", value: "planner" }
      ]
      group: "hdmap_group"
      prio: 1
    },
    {
      library: "${VOY_LIB_DIR}/libhdmap_vnodes.so"
      clazz: "MapTrackerVNode"
      name: "planner_map_tracker"
      sub {
        mode: kOneTrigger
        topic: [{
          topic: "/planner/hdmap/tracker_message"
          transport_option: { type: kIntraProcess }
          queue_size: 1
        }]
      }
      params: [{ key: "vnode_name", value: "planner" }]
      group: "hdmap_group"
      prio: 1
    },
    {
      library: "${VOY_LIB_DIR}/libhdmap_vnodes.so"
      clazz: "PriorMapVNode"
      name: "planner_prior_map"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/realtime_map/update_data"
            transport_option: { type: kUDP }
            queue_size: 1
          }
        ]
      }
      params: [
        { key: "vnode_name", value: "planner" }
      ]
      group: "hdmap_group"
      prio: 1
    },
    {
      library: "${VOY_LIB_DIR}/libplanner_node.so"
      clazz: "SnapshotVNode"
      # (minghao): this name is explicitly used in latency analysis
      name: "snapshot"
      sub {
        mode: kXTrigger
        trigger_num: 2
        topic: [
          {
            topic: "/prediction/predicted_objects"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/bag/planning/snapshot_seed"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/simulation/pose"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/traffic_light_detect"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/gateway"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/simulation/vehicle"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/obstacle_list"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/perception/construction_zone_list"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/routing/route_status"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/planning/assist_response"
            transport_option: { type: kUDP }
            queue_size: 10
            send_ack: true
          },
          {
            topic: "/planning/fault_detector_response"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/trajectory_guider/guide_trajectory"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/order_service"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/trip_comment"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/perception/lidar_detected_curb_list"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/health/localization_node"
            transport_option: { type: kUDP }
            queue_size: 10
          },
          {
            topic: "/planning/mrc_immediate_pullover"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/control/replan_request"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/localization/localization_fault"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/mrc/mrc_request"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/hdmap/map_change_preprocess"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/planning/remote_speed_limit"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/action_factory/vehicle_exception_status"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/mapping/map_change_area_list"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/realtime_map/empirical_publish_data"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/exception_handler/global_init_state"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/exception_handler/trajectory"
            transport_option: { type: kUDP }
            queue_size: 1
          }
        ]
      }

      pub: [
        {
          name: "snapshot_pub"
          topic: "/planning/snapshot"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "snapshot_seed_pub"
          topic: "/planning/snapshot_seed"
          transport_option: { type: kUDP }
        },
        {
          name: "planning_intra_speed_completion_signal"
          topic: "/planning/intra/speed_completion_signal"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "snapshot_ra_pub"
          topic: "/planning/intra/snapshot_ra"
          transport_option: { type: kIntraProcess }
        }
      ]

      group: "snapshot_group"
      prio: 11

      enable_message_event_timestamp_record: true
    },
    {
      library: "${VOY_LIB_DIR}/libplanner_node.so"
      clazz: "PlanningPathVNode"
      # (minghao): this name is explicitly used in latency analysis
      name: "planning_path"
      sub {
        mode: kXTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/planning/snapshot"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/bag/planning/seed"
            transport_option: { type: kUDP }
            queue_size: 10
          },
          {
            name: "speed_completion_signal"
            topic: "/planning/intra/speed_completion_signal"
            transport_option: { type: kIntraProcess }
            queue_size: 4
          },
          {
            topic: "/planning/intra/snapshot_ra"
            transport_option: { type: kIntraProcess }
            queue_size: 10
          },
          {
            topic: "/bag/planner/state"
            transport_option: { type: kUDP }
            queue_size: 1
          }
        ]
      }
      pub: [
        {
          name: "planning_speed_parameters"
          topic: "/planning/intra/speed_planner_params"
          transport_option: { type: kIntraProcess }
          timeout: 400
        },
        {
          name: "routing_route_state_pub"
          topic: "/routing/planning_route_state"
          transport_option: { type: kUDP }
        },
        {
          name: "planning_message_metadata_input_pub",
          topic: "/planning/message_metadata_input",
          transport_option: { type: kUDP }
        },
        {
          name: "planning_empirical_raw_data_pub",
          topic: "/planning/planning_empirical_raw_data",
          transport_option: { type: kUDP }
        }
      ]
      group: "path_planner_group"
      prio: 12
      skip_acyclicity_check_in_simulation: true

      enable_message_event_timestamp_record: true
    },
    {
      library: "${VOY_LIB_DIR}/libplanner_node.so"
      clazz: "PlanningSpeedVNode"
      name: "planning_speed"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/planning/intra/speed_planner_params"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/planning/intra/remote_assist_model_output"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/bag/planning/seed"
            transport_option: { type: kUDP }
            queue_size: 10
          },
          {
            topic: "/exception_handler/debug_for_perception"
            transport_option: { type: kUDP }
            queue_size: 3
          },
          {
            topic: "/system_state"
            transport_option: { type: kUDP }
            queue_size: 3
          },
          {
            topic: "/se_early_warning"
            transport_option: { type: kUDP }
            queue_size: 3
          },
          {
            topic: "/perception/collision_detection"
            transport_option: { type: kUDP }
            queue_size: 3
          },
          {
            topic: "/perception/sensor_abnormal_pub"
            transport_option: { type: kUDP }
            queue_size: 3
          },
          {
            topic: "/perception/camera_abnormal_pub"
            transport_option: { type: kUDP }
            queue_size: 3
          }
        ]
      }
      pub: [
        {
          name: "planning_ops_warning_pub"
          topic: "/planning/ops_warning"
          transport_option: { type: kUDP }
          timeout: 400
        },
        {
          name: "planning_debug_wrapper_pub"
          topic: "/planning/intra/planning_debug_wrapper"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "planning_planning_seed_pub"
          topic: "/planning/seed"
          transport_option: { type: kUDP }
        },
        {
          name: "planning_planner_state_pub"
          topic: "/planner/state"
          transport_option: { type: kUDP }
        },
        {
          name: "planning_planning_lane_sequence_pub"
          topic: "/planning/planning_lane_sequence"
          transport_option: { type: kUDP }
          timeout: 400
        },
        {
          name: "planning_customer_monitor_visual_pub"
          topic: "/planning/customer_monitor_visual"
          transport_option: { type: kUDP }
          timeout: 400
        },
        {
          name: "planning_pullout_request_pub"
          topic: "/planning/pullout_request"
          transport_option: { type: kUDP }
        },
        {
          name: "planning_cloud_cell_request_pub"
          topic: "/planning/cloud_cell_request"
          transport_option: { type: kUDP }
        },
        {
          name: "planning_stuck_detection_recall_signal_pub"
          topic: "/planning/stuck_detection_recall_signal"
          transport_option: { type: kUDP }
        },
        {
          name: "planning_assist_request_pub"
          topic: "/planning/assist_request"
          transport_option: { type: kUDP }
        },
        {
          name: "remote_warning_signal_pub"
          topic: "/planning/remote_warning_signal"
          transport_option: { type: kUDP }
        },
        {
          name: "routing_route_command_pub"
          topic: "/routing/planner_route_command"
          transport_option: { type: kUDP }
        },
        {
          name: "planning_trajectory_pub"
          topic: "/planning/trajectory"
          transport_option: { type: kUDP }
          timeout: 400
        },
        {
          name: "selectnet_sample_pub"
          topic: "/planning/selectnet_sample"
          transport_option: { type: kUDP }
        },
        {
          name: "trajectory_result_pub"
          topic: "/planning/trajectory_result"
          transport_option: { type: kUDP }
        },
        {
          name: "planning_message_metadata_pub"
          topic: "/planning/message_metadata"
          transport_option: { type: kUDP }
        },
        {
          name: "ml_planner_close_loop_assist_trajectory_pub"
          topic: "/planning/ml_planner_close_loop_assist_trajectory"
          transport_option: { type: kUDP }
        },
        {
          name: "planning_intra_speed_completion_signal"
          topic: "/planning/intra/speed_completion_signal"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "planner_issue_tag_change_pub"
          topic: "/issue_tag_change"
          transport_option: { type: kUDP }
        },
        {
          name: "remote_assist_model_input_pub"
          topic: "/planning/intra/remote_assist_model_input"
          transport_option: { type: kIntraProcess }
        }
      ]

      group: "speed_planner_group"
      prio: 12
      skip_acyclicity_check_in_simulation: true

      enable_message_event_timestamp_record: true
    },
    {
      library: "${VOY_LIB_DIR}/libplanner_node.so"
      clazz: "PublishVNode"
      name: "planner_publish"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/planning/intra/planning_debug_wrapper"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          }
        ]
      }

      pub: [
        {
          name: "planning_planning_debug_pub"
          topic: "/planning/planning_debug"
          transport_option: { type: kUDP }
        }
      ]

      group: "planning_publish_group"
      prio: 12
    },
    {
      library: "${VOY_LIB_DIR}/libplanner_node.so"
      clazz: "RemoteAssistModelVNode"
      name: "remote_assist_model"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/planning/intra/remote_assist_model_input"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          }
        ]
      }

      pub: [
        {
          name: "remote_assist_model_output_pub"
          topic: "/planning/intra/remote_assist_model_output"
          transport_option: { type: kIntraProcess }
        }
      ]

      group: "remote_assist_model_group"
      prio: 12
      skip_acyclicity_check_in_simulation: true

      enable_message_event_timestamp_record: true
    }
  ]
}
