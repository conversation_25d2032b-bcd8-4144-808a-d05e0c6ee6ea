#ifndef ONBOARD_PLANNER_OPEN_SPACE_MOTION_PLAN_GENERATOR_OPEN_SPACE_PATH_SEARCHER_OPEN_SPACE_PATH_SEARCH_RESULT_H_
#define ONBOARD_PLANNER_OPEN_SPACE_MOTION_PLAN_GENERATOR_OPEN_SPACE_PATH_SEARCHER_OPEN_SPACE_PATH_SEARCH_RESULT_H_

#include <utility>
#include <vector>

#include "planner/open_space/motion_plan_generator/open_space_path_searcher/open_space_node.h"

namespace planner {

namespace open_space {

// Defines a path segment that with the same direction (forward or
// backward).
struct MotionSegment {
  MotionSegment() = default;
  explicit MotionSegment(open_space::PathSegment&& path_in)
      : path(std::move(path_in)) {}
  open_space::PathSegment path;

  bool IsForward() const {
    DCHECK(!path.empty());
    return path.front_state().is_forward;
  }
  double InitSteeringAngle() const {
    DCHECK(!path.empty());
    return path.front_state().steering_angle;
  }
  const open_space::PathState& target_pose() const {
    DCHECK(!path.empty());
    return path.back_state();
  }
};

// Defines the open space search result, which is actually a motion sequence
// containing a series of either forward / backward motion segments.
// TODO(liwen): If there's only the `motion_sequence` field in the
// `OpenSpacePathSearchResult`, we consider to remove the struct
// `OpenSpacePathSearchResult` here, use `using` instead.
struct OpenSpacePathSearchResult {
  explicit OpenSpacePathSearchResult(
      std::vector<MotionSegment>&& path_segment_sequence_in)
      : motion_sequence(std::move(path_segment_sequence_in)) {}
  std::vector<MotionSegment> motion_sequence;
};

}  // namespace open_space

}  // namespace planner

#endif  // ONBOARD_PLANNER_OPEN_SPACE_MOTION_PLAN_GENERATOR_OPEN_SPACE_PATH_SEARCHER_OPEN_SPACE_PATH_SEARCH_RESULT_H_
