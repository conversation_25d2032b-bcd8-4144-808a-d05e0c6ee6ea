#!/usr/bin/env python3
"""Handles collectd upload service in PerfTest, should be always on."""
# pylint: disable=C0301,W1203,W0718,W0311,W1514,R1705,W0707,W0105
import os
import subprocess
import re
import logging
import sys
import base64
import json
import requests
import fcntl
import threading
from pathlib import Path
import concurrent.futures
from datetime import datetime
from contextlib import contextmanager
import time

# Global state management for service restart
RESTART_STATE = {
    "last_restart": 0.0,  # Timestamp of last restart attempt
    "cooldown": 120.0,  # Minimum seconds between restarts (2 minutes)
}
RESTART_LOCK = threading.Lock()

# Define all paths and constants at the top level
CAR_ID_FILE_PATH = "/opt/voyager/etc/catkin/profile.d/voy_car_id.sh"
TARGET_DIR = Path("/dev/shm/collectd")
API_URL = "http://10.88.128.83/sys_server/sys_server/collectd/"
LOCK_FILE = "/tmp/pbz_upload_lock"
# TODO(yutongzhouzhou): Adjust upload speed and thread pool usage
# Strategy: Whether the transmission is successful/Remaining pbz file/whether running PerfTest.
NUM_THREADS = 3

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    stream=sys.stdout,
)

# Import necessary protobuf modules
try:
    import voy_perf2.tools.collectd.collectd_data_pb2 as collectd
    import voy_perf2.tools.collectd.telemetry_pb2 as telemetry
except ImportError:
    logging.error(
        "Required protobuf modules not found. Make sure collectd_data_pb2.py and telemetry_pb2.py are available."
    )
    sys.exit(1)


@contextmanager
def singleton_lock():
    """Create a file lock to ensure only one instance is running"""
    lock_file = open(LOCK_FILE, "w")
    try:
        fcntl.flock(lock_file, fcntl.LOCK_EX | fcntl.LOCK_NB)
        yield
    except IOError:
        logging.error("Another instance is already running")
        sys.exit(1)
    finally:
        fcntl.flock(lock_file, fcntl.LOCK_UN)
        lock_file.close()


def is_file_ready(file_path: Path, stable_seconds: int = 30) -> bool:
    """Comprehensive check to determine if the file is safe to upload"""
    # 1. The file is not locked by another process (exclusive lock)
    if not can_acquire_shared_lock(file_path):
        return False

    # 2. The file size remains unchanged over a period of time
    if not is_size_stable(file_path, check_interval=5, max_checks=3):
        return False

    # 3. (Optional) The file modification time has stabilized
    try:
        current_mtime = file_path.stat().st_mtime
        time.sleep(stable_seconds)
        return file_path.stat().st_mtime == current_mtime
    except FileNotFoundError:
        return False


def can_acquire_shared_lock(file_path: Path) -> bool:
    """Attempts to acquire a shared lock (non-blocking) to verify file readability."""
    try:
        with open(file_path, "rb") as f:
            # Allows other processes to read but prevents writes.
            fcntl.flock(f, fcntl.LOCK_SH | fcntl.LOCK_NB)
            return True
    except (BlockingIOError, PermissionError):
        return False  # Returns false if file is exclusively locked by another process.
    except Exception as e:
        logging.warning(f"Lock check failed for {file_path}: {str(e)}")
        return False


def is_size_stable(
    file_path: Path, check_interval: float = 1.0, max_checks: int = 3
) -> bool:
    """
    Check if file size remains constant over multiple checks.
    There is no operation to continue writing this pbz.
    """
    last_size = None
    for _ in range(max_checks):
        try:
            current_size = file_path.stat().st_size
        except FileNotFoundError:
            return False
        if last_size is not None and current_size != last_size:
            return False
        last_size = current_size
        time.sleep(check_interval)
    return True


def safe_remove(file_path: Path) -> None:
    """Performs secure file deletion that resists recovery attempts:
    - Stage 1: Overwrites file with null bytes (destroys content)
    - Stage 2: Deletes filesystem entry
    More secure than standard deletion against:
    - File carving recovery tools
    - Basic undelete utilities
    """
    try:
        with open(file_path, "wb") as f:
            f.truncate(0)  # Atomic content destruction (better than random overwrites)
        os.remove(file_path)
    except Exception as e:
        logging.error(f"Failed to safely remove {file_path}: {str(e)}")


def get_car_id():
    """Extract VOY_CAR_ID from the car ID file"""
    try:
        with open(CAR_ID_FILE_PATH, "r") as f:
            content = f.read()
            # Look for export VOY_CAR_ID=NUMBER pattern
            match = re.search(r"export\s+VOY_CAR_ID=(\d+)", content)
            if match:
                return match.group(1)
            else:
                raise ValueError("Could not find VOY_CAR_ID in the file")
    except Exception as e:
        logging.error("Error reading car ID.", exc_info=e)
        # Fallback to using subprocess to read the file
        try:
            result = subprocess.run(
                ["cat", CAR_ID_FILE_PATH], capture_output=True, text=True, check=True
            )
            match = re.search(r"export\s+VOY_CAR_ID=(\d+)", result.stdout)
            if match:
                return match.group(1)
            else:
                raise ValueError("Could not find VOY_CAR_ID in the command output")
        except Exception as sub_e:
            logging.error(f"Error with fallback method: {sub_e}")
            return None


def process_pbz_file(file_path: Path, car_id):
    """Process a single pbz file and return success status"""
    try:
        if not is_file_ready(file_path):
            return False

        with open(file_path, "rb") as f:
            fcntl.flock(f, fcntl.LOCK_SH)
            data = f.read()
            file_size = len(data)

        # Handle 29-byte files
        if file_size == 29:
            current_time = time.time()

            with RESTART_LOCK:  # Thread-safe state management
                time_since_last = current_time - RESTART_STATE["last_restart"]

                # Check cooldown period
                if time_since_last >= RESTART_STATE["cooldown"]:
                    try:
                        logging.warning(
                            f"29-byte file detected: {file_path}. Restarting service..."
                        )
                        subprocess.run(
                            [
                                "systemctl",
                                "restart",
                                "voy-onboard-collectd-recorder.service",
                            ],
                            check=True,
                            timeout=10,  # Prevent hanging
                        )
                        RESTART_STATE["last_restart"] = current_time
                        logging.warning(
                            f"Service restarted. Next available at: {datetime.fromtimestamp(current_time + 120).strftime('%Y-%m-%d %H:%M:%S')}"
                        )
                    except subprocess.TimeoutExpired:
                        logging.error("Service restart timed out after 10 seconds")
                    except Exception as e:
                        logging.error(
                            "Service: voy-onboard-collectd-recorder.service restart failed.", exc_info=e
                        )
                else:
                    remaining = RESTART_STATE["cooldown"] - time_since_last
                    logging.warning(f"Restart cooldown active: {remaining:.1f}s remaining")

            # Always remove 29-byte files
            safe_remove(file_path)
            logging.warning(f"Removed 29-byte file: {file_path}")
            return True

        # Normal processing flow
        collectd_data = collectd.CollectdData()
        now = datetime.now()
        collectd_data.timestamp = int(now.timestamp() * 1000)
        collectd_data.carid = str(car_id)
        collectd_data.filename = str(file_path)
        collectd_data.data = data

        # Define the telemetry message
        telemetry_message = telemetry.TelemetryMessage()
        telemetry_message.msg_type = "CollectdData"
        telemetry_message.msg_value = collectd_data.SerializeToString()

        # Serialize and encode
        telemetry_message_binary = telemetry_message.SerializeToString()
        payload = base64.b64encode(telemetry_message_binary).decode("utf-8")
        payload_dict = {"payload": payload}
        json_payload = json.dumps(payload_dict)
        post_data = {"params": [json_payload]}

        # Send the message
        success = send_telemetry_message(post_data)

        if success:
            # Remove file after successful upload
            safe_remove(file_path)
            return True
        else:
            logging.error(f"Failed to upload file: {file_path}")
            return False

    except Exception as e:
        logging.error(f"Error processing file {file_path}.", exc_info=e)
        return False


def send_telemetry_message(post_data):
    """Send telemetry data to the API endpoint"""
    try:
        response = requests.post(API_URL, json=post_data, timeout=60)
        if response.status_code != 200:
            logging.error(f"Failed to send data to API: {response.status_code}")
            return False
        return True
    except Exception as e:
        logging.error("Error sending telemetry message.", exc_info=e)
        return False


def main():
    """Main function to scan for .pbz files and upload them periodically"""
    with singleton_lock():
        # Get car ID once at the beginning
        car_id = get_car_id()
        if not car_id:
            logging.error("Failed to get car ID. Exiting.")
            return
        logging.info(f"Using car ID: {car_id}")
        while True:
            cycle_start = time.time()
            pbz_files = []  # Initialize to empty list for each cycle
            try:
                # Check if directory exists
                target_dir = Path(TARGET_DIR)
                if not target_dir.exists():
                    logging.error(f"Directory {TARGET_DIR} does not exist")
                    continue
                # Find all .pbz files
                pbz_files = list(target_dir.glob("**/*.pbz"))
                if not pbz_files:
                    continue
                logging.info(f"Found {len(pbz_files)} .pbz files")
                # Process files in parallel using a thread pool
                with concurrent.futures.ThreadPoolExecutor(
                    max_workers=NUM_THREADS
                ) as executor:
                    future_to_file = {
                        executor.submit(process_pbz_file, pbz_file, car_id): pbz_file
                        for pbz_file in pbz_files
                    }
                    for future in concurrent.futures.as_completed(future_to_file):
                        pbz_file = future_to_file[future]
                        try:
                            future.result()
                        except Exception as e:
                            logging.error(f"Exception processing {pbz_file}.", exc_info=e)
            except KeyboardInterrupt:
                logging.error("Received keyboard interrupt. Exiting loop.")
                break
            except Exception as e:
                logging.error("Unexpected error during processing.", exc_info=e)
            finally:
                # Calculate sleep time based on whether files were found
                cycle_duration = time.time() - cycle_start
                base_interval = 60 if len(pbz_files) == 0 else 10
                sleep_time = max(base_interval - cycle_duration, 0)
                time.sleep(sleep_time)


if __name__ == "__main__":
    main()
