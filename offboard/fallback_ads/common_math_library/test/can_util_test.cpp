#include "common_math_library/can_util.h"

#include <gtest/gtest.h>

namespace fallback_adas {
namespace common {

TEST(Uint64ToBytes, Uint64ToBytesTest) {
  uint64_t value = 0x123456789ABCDEF0;
  uint8_t array[8] = {0};
  Uint64ToBytes(value, array, sizeof(array), true);

  EXPECT_EQ(array[0], 0x12);
  EXPECT_EQ(array[1], 0x34);
  EXPECT_EQ(array[2], 0x56);
  EXPECT_EQ(array[3], 0x78);
  EXPECT_EQ(array[4], 0x9A);
  EXPECT_EQ(array[5], 0xBC);
  EXPECT_EQ(array[6], 0xDE);
  EXPECT_EQ(array[7], 0xF0);
}

TEST(Uint64ToBytes, ArraySizeBelow8Test) {
  uint64_t value = 0x123456789ABCDEF0;
  uint8_t array[6] = {0};
  Uint64ToBytes(value, array, 6, true);

  EXPECT_EQ(array[0], 0x56);
  EXPECT_EQ(array[1], 0x78);
  EXPECT_EQ(array[2], 0x9A);
  EXPECT_EQ(array[3], 0xBC);
  EXPECT_EQ(array[4], 0xDE);
  EXPECT_EQ(array[5], 0xF0);
}

TEST(BytesToUint64, BytesToUint64Test) {
  uint8_t array[8] = {0x12, 0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0};
  uint64_t value = 0;
  BytesToUint64(array, sizeof(array), &value, true);

  EXPECT_EQ(value, 0x123456789ABCDEF0);
}

TEST(BytesToUint64, ArraySizeBelow8Test) {
  uint8_t array[6] = {0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF0};
  uint64_t value = 0;
  BytesToUint64(array, sizeof(array), &value, true);

  EXPECT_EQ(value, 0x56789ABCDEF0);
}

}  // namespace common
}  // namespace fallback_adas
