#include "light_assist_response_reply_observer.h"

#include <utility>

#include "av_comm/topics.h"
#include "glog/logging.h"

namespace remote_assist {

LightAssistResponseReplyObserver::LightAssistResponseReplyObserver(
    LightAssistResponseReplyCallback callback)
    : light_assist_response_reply_callback_(std::move(callback)) {}

bool LightAssistResponseReplyObserver::IsInterestedIn(
    const teleassist::pb::TeleassistRequest& message) {
  return (message.message_type() == kLightAssistResponseReply);
}

std::shared_ptr<google::protobuf::Message>
LightAssistResponseReplyObserver::OnMessage(
    const teleassist::pb::TeleassistRequest& message,
    DataChannel data_channel) {
  auto light_assist_response_reply =
      std::make_shared<teleassist::pb::LightAssistResponseReply>();
  if (!light_assist_response_reply->ParseFromString(message.message_data())) {
    LOG(ERROR)
        << "Received error LightAssistResponseReply message format, len = "
        << message.message_data().length();
    return nullptr;
  }
  if (light_assist_response_reply_callback_) {
    light_assist_response_reply_callback_(light_assist_response_reply,
                                          data_channel);
  }
  return light_assist_response_reply;
}

}  // namespace remote_assist
