#ifndef ONBOARD_PLANNER_DECOUPLED_MANEUVERS_PULL_OVER_TARGET_LINE_SNAPSHOT_INFO_H_
#define ONBOARD_PLANNER_DECOUPLED_MANEUVERS_PULL_OVER_TARGET_LINE_SNAPSHOT_INFO_H_

#include <utility>
#include <vector>

#include "geometry/model/frenet_axis_aligned_box.h"
#include "geometry/model/oriented_box.h"
#include "geometry/model/polygon_with_cache.h"
#include "geometry/model/polyline_curve.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_wrapper.h"
#include "planner/decoupled_maneuvers/pull_over/pull_over_utils.h"
#include "planner/utility/object_id/typed_object_id.h"
#include "planner/world_model/construction_zone/construction_zone.h"
#include "planner/world_model/planner_object/planner_object.h"
#include "planner_protos/pull_over_info_debug.pb.h"

namespace planner {

namespace pull_over {

// Class TargetLineSnapshotInfo defines the set of necessary infos describing an
// object's attributes, contour as well as the relative position w.r.t. the jump
// in target line.
class TargetLineSnapshotInfo {
 public:
  TargetLineSnapshotInfo() = default;
  // Stationary planner object constructor.
  TargetLineSnapshotInfo(const math::geometry::PolylineCurve2d& target_line,
                         const PlannerObject& planner_object)
      : typed_object_id_(planner_object.id(),
                         pb::ObjectSourceType::kTrackedObject),
        plan_init_timestamp_(planner_object.pose_at_plan_init_ts().timestamp()),
        planner_obj_ptr_(&planner_object),
        contour_(planner_object.pose_at_plan_init_ts().contour()),
        sl_bbox_(target_line, contour_.points()) {
    DCHECK(is_stationary());
    DCHECK_EQ(primary_bp_ptr_, nullptr);
    DCHECK_NE(planner_obj_ptr_, nullptr);
    DCHECK_EQ(construction_zone_ptr_, nullptr);
  }
  // Predicted dynamic snapshot constructor.
  TargetLineSnapshotInfo(const math::geometry::PolylineCurve2d& target_line,
                         const PlannerObject& planner_object,
                         const PredictedTrajectoryWrapper& primary_bp,
                         int primary_bp_idx)
      : typed_object_id_(planner_object.id(),
                         pb::ObjectSourceType::kTrackedObject),
        plan_init_timestamp_(planner_object.pose_at_plan_init_ts().timestamp()),
        primary_bp_idx_(primary_bp_idx),
        primary_bp_ptr_(&primary_bp),
        planner_obj_ptr_(&planner_object) {
    DCHECK_NE(primary_bp_ptr_, nullptr);
    DCHECK_NE(planner_obj_ptr_, nullptr);
    DCHECK_EQ(construction_zone_ptr_, nullptr);

    sl_bbox_ = math::geometry::FrenetAxisAlignedBox2d(
        target_line, GetAgentBoxFromPose(primary_bp_ptr_->pose(primary_bp_idx_),
                                         planner_object.tracked_object())
                         .CornerPoints());
  }
  // CZ constructor.
  TargetLineSnapshotInfo(const math::geometry::PolylineCurve2d& target_line,
                         const ConstructionZone& cz,
                         int64_t plan_init_timestamp)
      : typed_object_id_(cz.id, pb::ObjectSourceType::kConstructionZone),
        plan_init_timestamp_(plan_init_timestamp),
        construction_zone_ptr_(&cz),
        contour_(cz.contour),
        sl_bbox_(target_line, contour_.points()) {
    DCHECK(is_stationary());
    DCHECK_EQ(primary_bp_ptr_, nullptr);
    DCHECK_EQ(planner_obj_ptr_, nullptr);
    DCHECK_NE(construction_zone_ptr_, nullptr);
  }

  // Const accessors.
  const TypedObjectId& typed_obj_id() const { return typed_object_id_; }
  bool is_stationary() const {
    return typed_obj_id().is_construction_zone()
               ? true
               : DCHECK_NOTNULL(planner_obj_ptr_)->is_stationary();
  }
  int primary_bp_idx() const { return primary_bp_idx_; }
  int64_t timestamp() const {
    return is_stationary() ? plan_init_timestamp_
                           : DCHECK_NOTNULL(primary_bp_ptr_)
                                 ->pose(primary_bp_idx())
                                 .timestamp();
  }
  const PredictedTrajectoryWrapper& primary_bp() const {
    // This accessor should not be invoked for stationary objects.
    return *DCHECK_NOTNULL(primary_bp_ptr_);
  }
  const PlannerObject& planner_object() const {
    return *DCHECK_NOTNULL(planner_obj_ptr_);
  }
  const PlannerObject* planner_object_ptr() const { return planner_obj_ptr_; }
  const ConstructionZone& construction_zone() const {
    return *DCHECK_NOTNULL(construction_zone_ptr_);
  }
  const ConstructionZone* construction_zone_ptr() const {
    return construction_zone_ptr_;
  }
  const math::geometry::PolygonWithCache2d& contour() const { return contour_; }
  const math::geometry::FrenetAxisAlignedBox2d& sl_bbox() const {
    return sl_bbox_;
  }

  // Setter.
  // NOTE: This setter should be invoked when the snapshot's original SL box's
  // target line is changed, so that a new SL box should be updated.
  void set_sl_bbox(math::geometry::FrenetAxisAlignedBox2d&& sl_bbox_in) {
    sl_bbox_ = std::move(sl_bbox_in);
  }

  void ToProto(planner::pb::TargetLineSnapshotDebug* debug) const {
    debug->mutable_id()->set_object_id(typed_object_id_.id);
    debug->mutable_id()->set_object_type(typed_object_id_.type);
    // TODO(Liwen): Add `sl_points` interface to the `FrenetAxisAlignedBox`.
    math::geometry::PolygonWithCache2d sl_coord_contour(
        {math::geometry::Point2d(sl_bbox().s_range.start_pos,
                                 sl_bbox().l_range.start_pos),
         math::geometry::Point2d(sl_bbox().s_range.end_pos,
                                 sl_bbox().l_range.start_pos),
         math::geometry::Point2d(sl_bbox().s_range.end_pos,
                                 sl_bbox().l_range.end_pos),
         math::geometry::Point2d(sl_bbox().s_range.start_pos,
                                 sl_bbox().l_range.end_pos)});
    math::geometry::Convert(sl_coord_contour, *debug->mutable_contour());
    debug->set_timestamp(timestamp());
  }

 private:
  // The typed object ID inidicating whether object is tracked object or CZ and
  // the exact tracked object/CZ ID.
  TypedObjectId typed_object_id_;

  // The plan_init_state timestamp.
  int64_t plan_init_timestamp_ = 0;

  // The predicted trajectory pose idx and corresponding pointer regarding the
  // PredictedTrajectoryWrapper of this planner object's PRIMARY prediction.
  // NOTE: For primary stationary objects, the timestamp will be the same as the
  // plan init timestamp with ZERO predicted trajectory idx and nullptr to the
  // predicted trajectory.
  int primary_bp_idx_ = 0;
  const PredictedTrajectoryWrapper* primary_bp_ptr_ = nullptr;

  // The ptrs referencing the original planner object/CZ object.
  // NOTE: Ptrs will be non-null based on object type.
  const PlannerObject* planner_obj_ptr_ = nullptr;
  const ConstructionZone* construction_zone_ptr_ = nullptr;

  // The contour polygon of the snapshot.
  // NOTE: For snapshot from dynamic agent bps, the contour corresponds to the
  // oriented box generated from the bp pose at the index of interest.
  math::geometry::PolygonWithCache2d contour_;

  // The bounding box (s-range and l-range) of snapshot contour in reference to
  // a jump in target line.
  math::geometry::FrenetAxisAlignedBox2d sl_bbox_;
};

// Struct TargetLineOccupiedRangeEndPt defines the set of info associated with
// the (starting/ending) edge of a range occupied a TargetLineSnapshotInfo.
struct TargetLineOccupiedRangeEndPt {
  // True if, in the direction of increasing arc length, the target line goes
  // into an occupied range. Vice versa for false.
  bool is_entering_occupancy = true;
  double target_line_arc_length = 0.0;
  const TargetLineSnapshotInfo* snapshot_info_ptr = nullptr;
};

}  // namespace pull_over

}  // namespace planner

#endif  // ONBOARD_PLANNER_DECOUPLED_MANEUVERS_PULL_OVER_TARGET_LINE_SNAPSHOT_INFO_H_
