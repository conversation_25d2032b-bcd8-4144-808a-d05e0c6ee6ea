#include "acu_signal_input/pose_decode/pose_decode.h"

#include <algorithm>
#include <cmath>
#include <cstddef>
#include <cstdint>

#include "_out/Appl/GenData/Components/Rte_AcuSigInput.h"
#include "_out/Appl/GenData/Components/Rte_AcuSigInput_Type.h"
#include "_out/Appl/GenData/Rte_Type.h"
#include "common_math_library/can_util.h"
#include "common_math_library/common_method.h"

namespace fallback_adas {
namespace acu_signal_input {

void LocalizationPoseDecode(LocalizationPoseInput* pose_ptr) {
  // timestamp
  uint8_t* timestamp_bytes_ptr =
      Rte_IRead_PoseInput_10ms_AcuPose_PoseTimeStamp();

  uint64_t timestamp_ms = 0;
  common::BytesToUint64(timestamp_bytes_ptr, 6, &timestamp_ms, true);
  pose_ptr->timestamp_ms = static_cast<uint32_t>(timestamp_ms % 1000000000L);

  // position x
  pose_ptr->x = Rte_IRead_PoseInput_10ms_AcuPose_PosePositionX() * 0.001F;

  // position y
  pose_ptr->y = Rte_IRead_PoseInput_10ms_AcuPose_PosePositionY() * 0.001F;

  // yaw
  pose_ptr->yaw = Rte_IRead_PoseInput_10ms_AcuPose_PoseYaw() * 1e-6F;

  // pitch
  pose_ptr->pitch = Rte_IRead_PoseInput_10ms_AcuPose_PosePitch() * 1e-6F;

  // vel x
  pose_ptr->vel_x = Rte_IRead_PoseInput_10ms_AcuPose_PoseVelocityX() * 0.001F;

  // vel y
  pose_ptr->vel_y = Rte_IRead_PoseInput_10ms_AcuPose_PoseVelocityY() * 0.001F;

  // vel yaw
  pose_ptr->vel_yaw = Rte_IRead_PoseInput_10ms_AcuPose_PoseYawRate() * 1e-6F;

  // accel x
  pose_ptr->acc_forward =
      Rte_IRead_PoseInput_10ms_AcuPose_PoseAccelerationX() * 0.001F;

  // accel y
  pose_ptr->acc_right =
      Rte_IRead_PoseInput_10ms_AcuPose_PoseAccelerationY() * 0.001F;

  // TODO(lifanjie): "Pose Status" is not used in the current version.
}

void GnssPoseDecode(GnssPoseInput* gnss_pose_ptr) {
  // Timestamp, ms.
  uint8_t* timestamp_bytes_ptr =
      Rte_IRead_PoseInput_10ms_AcuPose_GnssPoseTimeStamp();

  uint64_t timestamp_ms = 0;
  common::BytesToUint64(timestamp_bytes_ptr, sizeof(GnssPoseTimeStamp),
                        &timestamp_ms, true);
  gnss_pose_ptr->timestamp_ms =
      static_cast<uint32_t>(timestamp_ms % 1000000000L);

  // position x
  gnss_pose_ptr->x =
      Rte_IRead_PoseInput_10ms_AcuPose_GnssPosePositionX() * 0.001F;

  // position y
  gnss_pose_ptr->y =
      Rte_IRead_PoseInput_10ms_AcuPose_GnssPosePositionY() * 0.001F;

  // yaw
  gnss_pose_ptr->yaw = Rte_IRead_PoseInput_10ms_AcuPose_GnssPoseYaw() * 1e-6F;

  // heading
  gnss_pose_ptr->heading =
      Rte_IRead_PoseInput_10ms_AcuPose_GnssPoseHeading() * 1e-6F;

  // position type
  gnss_pose_ptr->pos_type = static_cast<McuPositionType>(
      Rte_IRead_PoseInput_10ms_AcuPose_GnssPosePosType());

  // heading type
  gnss_pose_ptr->heading_type = static_cast<McuPositionType>(
      Rte_IRead_PoseInput_10ms_AcuPose_GnssPoseHeadingType());

  // number of satellites tracked
  gnss_pose_ptr->num_sats_tracked =
      Rte_IRead_PoseInput_10ms_AcuPose_GnssPoseNumSatsTracked();

  // vel x
  gnss_pose_ptr->velocity_x =
      Rte_IRead_PoseInput_10ms_AcuPose_GnssPoseVelocityX() * 0.001F;

  // vel y
  gnss_pose_ptr->velocity_y =
      Rte_IRead_PoseInput_10ms_AcuPose_GnssPoseVelocityY() * 0.001F;
}

}  // namespace acu_signal_input
}  // namespace fallback_adas
