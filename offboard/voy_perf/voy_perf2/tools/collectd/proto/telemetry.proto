syntax = "proto3";

option go_package = "../protocol;protocol";

package pb;

// Represents messages sent to remote or received from remote.
message TelemetryMessage {
  // Time telemetry message published.
  int64 timestamp = 1;
  string car_id = 2;
  // Concrete type of protobuf message sub/pub from/to ros topic.
  string msg_type = 3;
  // Serialized binary string of protobuf message.
  bytes msg_value = 4;
}

// Wraps ros params as a topic message for sending to remote.
message RosParamMessage { string region = 1; }

// This message is used to control which onboard ros topic is need
// (or not) by teleoperation center. If we enable one topic that
// means data(in the topic) would be sent to remote,
// disable is otherwise.
message TopicControlMessage {
  // The time when the control message sending.
  uint64 timestamp = 1;
  enum TopicControlMode {
    UNKNOWN = 0;
    ENABLE = 1;
    DISABLE = 2;
  }
  TopicControlMode mode = 2;
  // Identify which ros topic been controlled and should be same as
  // topics defined in av_comm/topics.h
  string topic_name = 3;
  // The interval time of sending data to remote.(in ms)
  // If interval is smaller than ros topic publisher, would adjust same to it.
  // The interval is controlled by internal timer, so it is a
  // approximate implementation.
  // If control mode is disable, this field is ignored.
  uint32 interval = 4;
  // The total time for a single sending procedure in seconds. Max 60 seconds.
  // If received a new duration, it will refresh internal timer.
  uint32 duration = 5;
};
