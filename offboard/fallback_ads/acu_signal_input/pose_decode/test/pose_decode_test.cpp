#include "acu_signal_input/pose_decode/pose_decode.h"

#include <gtest/gtest.h>

#include <climits>
#include <cmath>
#include <cstdint>
#include <cstring>

#include "_out/Appl/GenData/Components/Rte_AcuSigInput.h"
#include "_out/Appl/GenData/Rte_Type.h"
#include "common_math_library/can_util.h"
#include "offboard/fallback_ads/fallback_simulation.h"

namespace fallback_adas {
namespace acu_signal_input {

TEST(LocalizationPoseDecode, TimestampTest) {
  // ref_timestamp_ms = 0x19618d955d8.
  uint64_t ref_timestamp_ms = 1744173618648;
  uint8_t timestamp_bytes[6] = {0};
  common::Uint64ToBytes(ref_timestamp_ms, timestamp_bytes,
                        sizeof(timestamp_bytes), true);
  std::memcpy(Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms
                  .Rte_AcuPose_PoseTimeStamp.value,
              timestamp_bytes, sizeof(timestamp_bytes));

  LocalizationPoseInput pose_input;
  LocalizationPoseDecode(&pose_input);

  // Verify the timestamp
  EXPECT_EQ(static_cast<uint32_t>(pose_input.timestamp_ms),
            static_cast<uint32_t>(ref_timestamp_ms % 1000000000L));
}

TEST(LocalizationPoseDecode, LocalizationPoseDecodeTest) {
  auto& raw_pose_input =
      Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms;

  for (int32_t i = -30; i <= 30; ++i) {
    auto ref_value = static_cast<float>(i);
    // TODO(lifanjie): Verify timestamp separately for the next version
    raw_pose_input.Rte_AcuPose_PosePositionX.value = ref_value / 0.001F;

    raw_pose_input.Rte_AcuPose_PosePositionY.value = ref_value / 0.001F;
    raw_pose_input.Rte_AcuPose_PoseYaw.value = ref_value / 1e-6F;
    raw_pose_input.Rte_AcuPose_PosePitch.value = ref_value / 1e-6F;
    raw_pose_input.Rte_AcuPose_PoseVelocityX.value = ref_value / 0.001F;
    raw_pose_input.Rte_AcuPose_PoseVelocityY.value = ref_value / 0.001F;
    raw_pose_input.Rte_AcuPose_PoseYawRate.value = ref_value / 1e-6F;
    raw_pose_input.Rte_AcuPose_PoseAccelerationX.value = ref_value / 0.001F;
    raw_pose_input.Rte_AcuPose_PoseAccelerationY.value = ref_value / 0.001F;

    LocalizationPoseInput pose_input;
    LocalizationPoseDecode(&pose_input);

    EXPECT_NEAR(pose_input.x, ref_value, 0.001F);
    EXPECT_NEAR(pose_input.y, ref_value, 0.001F);
    EXPECT_NEAR(pose_input.yaw, ref_value, 1e-6F);
    EXPECT_NEAR(pose_input.pitch, ref_value, 1e-6F);
    EXPECT_NEAR(pose_input.vel_x, ref_value, 0.001F);
    EXPECT_NEAR(pose_input.vel_y, ref_value, 0.001F);
    EXPECT_NEAR(pose_input.vel_yaw, ref_value, 1e-6F);
    EXPECT_NEAR(pose_input.acc_forward, ref_value, 0.001F);
    EXPECT_NEAR(pose_input.acc_right, ref_value, 0.001F);
  }
}

TEST(GnssPoseDecode, TimestampTest) {
  // ref_timestamp_ms = 0x19618d955d8.
  uint64_t ref_timestamp_ms = 1744173618648;
  uint8_t timestamp_bytes[6] = {0};
  common::Uint64ToBytes(ref_timestamp_ms, timestamp_bytes,
                        sizeof(timestamp_bytes), true);
  std::memcpy(Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms
                  .Rte_AcuPose_GnssPoseTimeStamp.value,
              timestamp_bytes, sizeof(timestamp_bytes));

  // Create a GnssPoseInput object
  GnssPoseInput pose_input;
  GnssPoseDecode(&pose_input);

  // Verify the timestamp
  EXPECT_EQ(static_cast<uint32_t>(pose_input.timestamp_ms),
            static_cast<uint32_t>(ref_timestamp_ms % 1000000000L));
}

TEST(GnssPoseDecode, GnssPoseDecodeTest) {
  auto& raw_pose_input =
      Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms;

  for (int32_t i = -30; i <= 30; ++i) {
    auto ref_value = static_cast<float>(i);
    raw_pose_input.Rte_AcuPose_GnssPosePositionX.value = ref_value / 0.001F;
    raw_pose_input.Rte_AcuPose_GnssPosePositionY.value = ref_value / 0.001F;
    raw_pose_input.Rte_AcuPose_GnssPoseYaw.value = ref_value / 1e-6F;
    raw_pose_input.Rte_AcuPose_GnssPoseHeading.value = ref_value / 1e-6F;

    // pos_type
    auto ref_pos_type = static_cast<McuPositionType>(std::abs(i) % 18);
    raw_pose_input.Rte_AcuPose_GnssPosePosType.value = ref_pos_type;

    // heading_type
    auto ref_heading_type = static_cast<McuPositionType>(std::abs(i) % 18);
    raw_pose_input.Rte_AcuPose_GnssPoseHeadingType.value = ref_heading_type;

    // Number of satellites tracked.
    uint8_t num_sats_tracked = std::abs(i);
    raw_pose_input.Rte_AcuPose_GnssPoseNumSatsTracked.value = num_sats_tracked;

    raw_pose_input.Rte_AcuPose_GnssPoseVelocityX.value = ref_value / 0.001F;
    raw_pose_input.Rte_AcuPose_GnssPoseVelocityY.value = ref_value / 0.001F;

    // Create a GnssPoseInput object
    GnssPoseInput pose_input;
    GnssPoseDecode(&pose_input);

    EXPECT_NEAR(pose_input.x, ref_value, 0.001F);
    EXPECT_NEAR(pose_input.y, ref_value, 0.001F);
    EXPECT_NEAR(pose_input.yaw, ref_value, 1e-6F);
    EXPECT_NEAR(pose_input.heading, ref_value, 1e-6F);
    EXPECT_EQ(pose_input.pos_type, ref_pos_type);
    EXPECT_EQ(pose_input.heading_type, ref_heading_type);
    EXPECT_EQ(pose_input.num_sats_tracked, num_sats_tracked);
    EXPECT_NEAR(pose_input.velocity_x, ref_value, 0.001F);
    EXPECT_NEAR(pose_input.velocity_y, ref_value, 0.001F);
  }
}

TEST(GnssPoseDecode, PosTypeTest) {
  auto& pos_type = Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms
                       .Rte_AcuPose_GnssPosePosType.value;

  // Create a GnssPoseInput object
  GnssPoseInput pose_input;

  // MCU_UNKNOWN_TYPE test
  pos_type = MCU_UNKNOWN_TYPE;
  GnssPoseDecode(&pose_input);
  EXPECT_EQ(pos_type, MCU_UNKNOWN_TYPE);

  // MCU_NARROW_INT test
  pos_type = MCU_NARROW_INT;
  GnssPoseDecode(&pose_input);
  EXPECT_EQ(pos_type, MCU_NARROW_INT);
}

TEST(GnssPoseDecode, HeadingTypeTest) {
  auto& heading_type =
      Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms
          .Rte_AcuPose_GnssPosePosType.value;

  // Create a GnssPoseInput object
  GnssPoseInput pose_input;

  // MCU_UNKNOWN_TYPE test
  heading_type = MCU_UNKNOWN_TYPE;
  GnssPoseDecode(&pose_input);
  EXPECT_EQ(heading_type, MCU_UNKNOWN_TYPE);

  // MCU_NARROW_INT test
  heading_type = MCU_NARROW_INT;
  GnssPoseDecode(&pose_input);
  EXPECT_EQ(heading_type, MCU_NARROW_INT);
}

}  // namespace acu_signal_input
}  // namespace fallback_adas
