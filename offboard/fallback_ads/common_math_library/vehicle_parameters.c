#include "common_math_library/vehicle_parameters.h"

// vehicle parameter
const volatile float kVehMass = 2663;                            // kg
const volatile float kVehLength = 4.953;                         // meter
const volatile float kVehWidth = 1.958;                          // meter
const volatile float kVehWheelBase = 2.984;                      // meter
const volatile float kVehFrontWheelCorneringStiffness = 73624;   // N/rad
const volatile float kVehRearWheelCorneringStiffness = 111694;   // N/rad
const volatile float kVehFrontAxleToCogDistance = 1.492;         // meter
const volatile float kVehRearAxleToCogDistance = 1.492;          // meter
const volatile float kVehRearAxleToFrontBumperDistance = 3.855;  // meter
const volatile float kVehInertia = 5547.0F;                      // kg*m^2
const volatile float kVehSteerRatio = 16.0F;

float GetVehicleMass() { return kVehMass; }

float GetVehicleLength() { return kVehLength; }

float GetVehicleWidth() { return kVehWidth; }

float GetVehicleWheelBase() { return kVehWheelBase; }

float GetVehicleFrontWheelCorneringStiffness() {
  return kVehFrontWheelCorneringStiffness;
}

float GetVehicleRearWheelCorneringStiffness() {
  return kVehRearWheelCorneringStiffness;
}

float GetVehicleFrontAxleToCogDistance() { return kVehFrontAxleToCogDistance; }

float GetVehicleRearAxleToCogDistance() { return kVehRearAxleToCogDistance; }

float GetVehicleRearAxleToFrontBumperDistance() {
  return kVehRearAxleToFrontBumperDistance;
}

float GetVehicleInertia() { return kVehInertia; }

float GetVehicleSteerRatio() { return kVehSteerRatio; }
