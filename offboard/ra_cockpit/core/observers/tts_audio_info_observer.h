#ifndef OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_TTS_AUDIO_INFO_OBSERVER_H_
#define OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_TTS_AUDIO_INFO_OBSERVER_H_

#include <functional>
#include <memory>

#include "../data_observer.h"
#include "protos/ra_cockpit.pb.h"
#include "teleassist_protos/teleassist.pb.h"

namespace remote_assist {

class TTSAudioInfoObserver : public IDataObserver {
 public:
  using TTSAudioInfoCallback = std::function<void(
      std::shared_ptr<ra_cockpit::pb::TTSAudioInfo>, DataChannel)>;

  explicit TTSAudioInfoObserver(TTSAudioInfoCallback callback);

  bool IsInterestedIn(
      const teleassist::pb::TeleassistRequest& message) override;

  std::shared_ptr<google::protobuf::Message> OnMessage(
      const teleassist::pb::TeleassistRequest& message,
      DataChannel data_channel) override;

 private:
  TTSAudioInfoCallback callback_;
};

}  // namespace remote_assist

#endif  // OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_TTS_AUDIO_INFO_OBSERVER_H_
