#include "planner/path/reasoning/agent_intention/object_occupancy_state/object_occupancy_state.h"

#include <utility>

#include "planner/path/reasoning/agent_intention/object_occupancy_state/object_stamped_snapshot_info.h"
#include "planner/world_model/planner_object/planner_object.h"

namespace planner {

namespace {
constexpr double kMinProbabilityToStoreOtherTrajectories = 0.1;  // 10%
constexpr int64 kMaximumPredictionAgentRank = 5;

// TODO(Harry): Integrate this function & the same name function in
// tracked_object.cpp
bool ShouldIgnoreTrajectoryForPath(
    const prediction::pb::PredictedTrajectory& trajectory,
    std::optional<int64> prediction_agent_rank) {
  if (!prediction_agent_rank.has_value() ||
      *prediction_agent_rank > kMaximumPredictionAgentRank) {
    return true;
  }
  if (!trajectory.is_multi_output_trajectory()) {
    return true;
  }
  return trajectory.probability_in_ppm() * 1e-6 <
         kMinProbabilityToStoreOtherTrajectories;
}

}  // namespace

ObjectOccupancyState::ObjectOccupancyState(
    const PlannerObject& planner_object,
    const ObjectInformation& object_information,
    const std::vector<PredictedTrajectoryWrapper>&
        predicted_trajectory_wrappers,
    const math::geometry::PolylineCurve2d& nominal_path,
    const math::geometry::PolylineCurve2d& left_corridor_boundary,
    const math::geometry::PolylineCurve2d& right_corridor_boundary,
    const EgoInLaneParams& ego_params,
    ObjectStampedSnapshotInfo&& snapshot_info_at_plan_init_time,
    pb::MotionMode motion_mode,
    pb::AgentImaginaryActualType agent_imaginary_actual_type)
    : planner_object_(planner_object),
      current_snapshot_info_(std::move(snapshot_info_at_plan_init_time)),
      agent_imaginary_actual_type_(agent_imaginary_actual_type) {
  for (const auto& predicted_trajectory_wrapper :
       predicted_trajectory_wrappers) {
    if (!predicted_trajectory_wrapper.is_primary_trajectory() &&
        ShouldIgnoreTrajectoryForPath(
            predicted_trajectory_wrapper.predicted_trajectory_proto(),
            planner_object.prediction_agent_rank())) {
      continue;
    }
    predicted_trajectory_occupancy_states_.insert(
        {predicted_trajectory_wrapper.id(),
         ObjectTrajectoryStates(nominal_path, left_corridor_boundary,
                                right_corridor_boundary, object_information,
                                predicted_trajectory_wrapper, ego_params,
                                motion_mode)});
    if (!predicted_trajectory_wrappers.empty()) {
      const auto primary_predicted_trajectory = std::find_if(
          predicted_trajectory_wrappers.begin(),
          predicted_trajectory_wrappers.end(),
          [](const PredictedTrajectoryWrapper& predicted_trajectory) {
            return predicted_trajectory.is_primary_trajectory();
          });
      DCHECK(primary_predicted_trajectory !=
             predicted_trajectory_wrappers.end())
          << "Primary predicted trajectory is not found.";
      primary_trajectory_id_ = primary_predicted_trajectory->id();
      // Add start to move trajectory.
      if (predicted_trajectory_wrapper.MightStartToMoveSoon()) {
        start_to_move_trajectory_ =
            &predicted_trajectory_wrapper.predicted_trajectory_proto();
      }
    }
  }
}

}  // namespace planner
