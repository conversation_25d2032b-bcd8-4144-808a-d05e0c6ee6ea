#include "varch/tools/launcher/vgraph_controller.h"

#include <chrono>
#include <mutex>
#include <ros/ros.h>

#include <algorithm>
#include <csignal>
#include <cuda_runtime.h>
#include <set>
#include <sstream>
#include <string>
#include <thread>
#include <utility>

#include "av_comm/thread_pool.h"
#include "varch/base/file.h"
#include "varch/base/proto.h"
#include "varch/base/strings/split.h"
#include "varch/base/thread.h"
#include "varch/init.h"
#include "varch/protos/vnode/vgraph_conf.pb.h"
#include "varch/scheduler/all.h"
#include "varch/tools/launcher/launcher_flags.h"
#include "varch/trace.h"
#include "varch/utils/log.h"
#include "varch/utils/parallel/parallel_impl.h"
#include "varch/vnode/detail/deterministic/activity_service.h"
#include "varch/vnode/detail/transport/sub_process_pool.h"
#include "varch/vnode/global.h"

#ifndef VARCH_NO_VOY_DEPENDENCY
#include "av_comm/gflag_utils.h"
#include "av_comm/node_manifest.h"
#include "av_comm/topics.h"
#endif

namespace varch {
namespace tools {
namespace launcher {

namespace {
/* keep file opened then mmap whole file with locked */
static int mlock_a_file(const char* file) {
  int fd = open(file, O_RDONLY);
  if (fd < 0) {
    VLOG_INFO << "failed to open " << file;
    return -1;
  }

  /* get file size */
  long size = (long)lseek(fd, 0, SEEK_END);
  if (size == -1) {
    VLOG_INFO << "failed to lseek end of file: " << file;
    close(fd);
    return -1;
  }

  void* p = mmap(NULL, size, PROT_READ, MAP_LOCKED | MAP_SHARED, fd, 0);
  if (p == MAP_FAILED) {
    VLOG_INFO << "failed to mmap file" << file << "," << strerror(errno);
    close(fd);
    return -1;
  }
  VLOG_INFO << "success to mmap file " << file;
  return 0;
}

static void pin_mapped_pages() {
  if (FLAGS_mlock_all) {
    if (mlockall(MCL_CURRENT) < 0) {
      VLOG_ERROR << "failed to pin all mapped pages: " << strerror(errno);
      return;
    }
    VLOG_INFO << "success to pin all mapped pages";
  } else {
    auto files = strings::Split(FLAGS_mlock_binaries, ',');
    for (auto& file : files) {
      mlock_a_file(file.c_str());
    }
  }
}

struct SignalManager {
 private:
  static inline std::function<void(int)> signalHandlerFunc;

  static void signalHandler(int signal) { signalHandlerFunc(signal); }

 public:
  static void Init(std::function<void(int)> f) {
    signalHandlerFunc = std::move(f);
    std::signal(SIGINT, SignalManager::signalHandler);
  }
};

// Returns true if CUDA devices are explicitly hidden.
// This is used to avoid calling cuda APIs which trigger ASAN failures due
// to memory leaks in cuda library.
// Copied from onboard/common/av_comm/src/gpu_util.cpp
bool AreCudaDevicesHidden() {
  const char* cuda_visible_devices = std::getenv("CUDA_VISIBLE_DEVICES");
  if (cuda_visible_devices && (strcmp(cuda_visible_devices, "") == 0)) {
    VLOG_INFO << "All CUDA devices are explicitly hidden.";
    return true;
  }
  return false;
}

// Returns true if CUDA devices are available.
bool AreCudaDevicesAvailable() {
  if (AreCudaDevicesHidden()) {
    return false;
  }
  // Get the GPU devices by cuda api.
  int device_num = 0;
  cudaError_t error_id = cudaGetDeviceCount(&device_num);
  if (error_id != cudaSuccess) {
    LOG(INFO) << "cudaGetDeviceCount returned:" << static_cast<int>(error_id)
              << "->" << cudaGetErrorString(error_id);
    return false;
  }
  if (device_num == 0) {
    LOG(ERROR) << "There are no available device(s) that support CUDA.";
    return false;
  }
  return true;
}

int GetTotalFreeGPUMemoryInMB() {
  CHECK(AreCudaDevicesAvailable());
  size_t free_mem = 0;
  size_t total_mem = 0;
  // Get the total and free memory of the GPU
  cudaError_t error_id = cudaMemGetInfo(&free_mem, &total_mem);
  if (error_id != cudaSuccess) {
    LOG(INFO) << "cudaDeviceGetMemoryInfo returned:"
              << static_cast<int>(error_id) << "->"
              << cudaGetErrorString(error_id);
    return 0;
  }

  int free_mem_in_mb = static_cast<int>(free_mem / (1024 * 1024));
  LOG(INFO) << "[GPU Memory] free GPU memory: " << free_mem_in_mb << " MB";
  LOG(INFO) << "[GPU Memory] Total GPU memory: " << total_mem / (1024 * 1024)
            << " MB";
  return free_mem_in_mb;
}

int SetNumberOfThreadsForParaInit() {
  int free_gpu_mem_in_mb = GetTotalFreeGPUMemoryInMB();
  int num_threads = 0;
  // (minghao): GPU free memory is one of the bottlenecks of inference model
  // intialization parallelization. Here, we restrict the number of threads for
  // initialization based on different GPU free memory thresholds.
  if (free_gpu_mem_in_mb <= 4096 /*4GB */) {
    num_threads = 1;
  } else if (free_gpu_mem_in_mb <= 8192 /*8GB*/) {
    num_threads = 2;
  } else if (free_gpu_mem_in_mb <= 12288 /*12GB*/) {
    num_threads = 4;
  } else {
    num_threads = 6;
  }
  LOG(INFO) << "[Init in Parallel] Use " << num_threads
            << " thread(s) for VNode initialization.";
  return num_threads;
}

}  // namespace

using VNodeRunningMode = varch::protos::vnode::VNodeRunningMode;

VGraphController::VGraphController() { VNodeCore::GetInstance(); }

VGraphController::VGraphController(const VGraphArgument& args) : args_(args) {
  auto graph_config = GetVGraphConfig();
  InitVArchPool(graph_config);
  InitSubProcessPool(graph_config);
  VNodeCore::GetInstance();
}

void VGraphController::Run() {
  InitScheduler();
  SignalManager::Init([this](int signum) {
    {
      std::lock_guard<std::mutex> lock(signal_mutex_);
      signal_status_ = signum;
    }
    health_reporter_->Stop();
    signal_cv_.notify_one();
  });

#ifndef VARCH_NO_VOY_DEPENDENCY
  const auto& node_name = ros::this_node::getName();
  health_reporter_ =
      std::make_unique<health_reporter::HealthReporter>(node_name);

  LOG(INFO) << "Created health reporter for node " << node_name
            << ", health topic: " << av_comm::topic::HealthTopic(node_name);
#endif

  if (vnode::GetVArchGlobalData()->GetEnableRecurringFrame()) {
    frame_publish_service_ = std::make_unique<FramePublishService::Enabler>(
        vnode::GetVArchGlobalData()->GetFramePublishIntervalMs());
  }

  LoadAll();
  pin_mapped_pages();
#ifndef VARCH_NO_VOY_DEPENDENCY
  av_comm::gflag_utils::LogNonDefaultFlagValues();
#endif
  Go();

  if (health_reporter_) {
    health_reporter_->Start();
  }

  base::FormatAndLogThreadInfos();

  std::unique_lock<std::mutex> lock(signal_mutex_);
  signal_cv_.wait(lock, [this] { return 0 != signal_status_; });
}

void VGraphController::InitScheduler() {
  VForestConfig vforest_config;
  auto path = args_.GetVGraphConfList().front();
  if (!base::ReadTextProtoFile(path, &vforest_config)) {
    VLOG_FATAL << "Error parsing vgraph file: " << path;
  }
  VLOG_INFO << "Load scheduler configuration from " << path;

  InitScheduler(vforest_config);
}

void VGraphController::InitScheduler(const VForestConfig& vforest_config) {
  auto vgraph = vforest_config.vgraph();

  if (vnode::GetVArchGlobalData()->IsReplayTestMode()) {
    //
  } else {
    auto worker_num = vgraph.scheduler().worker_num();

    auto sched_conf = vgraph.scheduler();
    scheduler::Config cfg = scheduler::ConfigBuilder::CreateConfig(sched_conf);

    scheduler::InitScheduler(cfg);

    const auto& groups = vgraph.scheduler().group();

    for (const auto& task_group_conf : groups) {
      auto workers = base::ReadLocalQueueSet(task_group_conf.workers());
      scheduler::GroupOption group;
      group.name = task_group_conf.name();
      group.worker_range = workers;

      if (base::IsLocalQueueSetValid(worker_num, workers)) {
        scheduler::RegisterGroup(group);
        if (task_group_conf.has_task_num_scale()) {
          utils::ParalConf::GetInstance()->SetParallelTaskNumScale(
              task_group_conf.task_num_scale());
        }
      } else {
        VLOG_FATAL << "queues [" << task_group_conf.workers()
                   << "] is invalid! It should range from [0, "
                   << worker_num - 1 << "]";
      }
    }
  }
}

void VGraphController::LoadAll() {
  std::vector<std::string> paths;
  for (auto& vgraph_conf : args_.GetVGraphConfList()) {
    std::string vgraph_path = vgraph_conf;

    total_vnode_nums_ += GetVNodeNum(vgraph_path);
    paths.emplace_back(std::move(vgraph_path));
  }

  for (const auto& vgraph_path : paths) {
    VLOG_INFO << "Start initialize vgraph: " << vgraph_path;
    LoadVGraph(vgraph_path, FLAGS_init_in_parallel);
  }
  VNodeInitInParallel();
  VLOG_INFO << "\n" << scheduler::ToString();
}

void VGraphController::LoadVGraph(const VForestConfig& vforest_config,
                                  bool init_in_parallel) {
  const auto& vgraph = vforest_config.vgraph();
  [[maybe_unused]] const auto& run_mode = vgraph.mode();

  if (vgraph.has_listener_port()) {
    vnode::GetVArchGlobalData()->AddListenerPort(vgraph.listener_port());
  }

  for (const auto& peer_locator : vgraph.peer_locator()) {
    vnode::GetVArchGlobalData()->AddPeerLocator(peer_locator);
  }

  if (vgraph.has_sub_proc_type()) {
    vnode::GetVArchGlobalData()->SetProcessorType(vgraph.sub_proc_type());
  }

  // Parse all configuration for get tcp config

  for (const auto& vnode_conf : vforest_config.vgraph().vnode()) {
    auto start_time = std::chrono::steady_clock::now();
    std::string load_path = vnode_conf.library();

    base::ParsePath(load_path);
    if (!base::PathExists(load_path)) {
      VLOG_FATAL << "class binary file wasn't found. Path=" << load_path;
    }

    const std::string& clazz = vnode_conf.clazz();

    TRACE_EVENT_SCOPE(varch, VNode_Initialization, clazz);

    // (minghao): to guarantee that only one copy of hdmap vnodes exists
    if (vnode::GetVArchGlobalData()->HdMapClasses().count(clazz) &&
        vnode::GetVArchGlobalData()->LaunchedHdMapClasses().count(clazz)) {
      continue;
    } else if (vnode::GetVArchGlobalData()->HdMapClasses().count(clazz)) {
      vnode::GetVArchGlobalData()->LaunchedHdMapClasses().insert(clazz);
    }

    // FIXME(minghao): long-time wait in initialization is encountered when
    // running on Orion cpu workers. Revisit later.
    if (init_in_parallel && AreCudaDevicesAvailable()) {
      VNodeConfigWrapper vnode_config_wrapper;
      vnode_config_wrapper.load_path = load_path;
      vnode_config_wrapper.vnode_config = vnode_conf;
      vnode_config_wrapper.running_mode = vforest_config.vgraph().mode();

      // FIXME(minghao): early return the case when multiple VNodes have the
      // same vnode_conf.name();
      CHECK_EQ(uninitialized_vnode_list_.count(vnode_conf.name()), 0);

      uninitialized_vnode_list_.emplace(vnode_conf.name(),
                                        vnode_config_wrapper);
    } else {
      TRACE_EVENT_SCOPE(varch, VNode_Initialization_Impl, clazz);

      std::shared_ptr<VNodeBase> base;
      base.reset(CreateVNode(load_path, clazz, handler_list_));
      const auto& mode = vforest_config.vgraph().mode();
      if (base == nullptr) {
        VLOG_FATAL << "vgraph_launcher"
                   << "||msg=Fail to create class, ensure class has been  "
                      "registered."
                   << "||classname=" << clazz << "||lib=" << load_path;
      } else {
        VLOG_INFO << "vgraph_launcher"
                  << "||msg=Success to create class"
                  << "||classname=" << clazz << "||lib=" << load_path;
      }

      if (!vnode::GetVArchGlobalData()->IsReplayTestMode() &&
          vnode_conf.has_parallel_executor()) {
        auto worker_num = vforest_config.vgraph().scheduler().worker_num();

        auto queues =
            base::ReadLocalQueueSet(vnode_conf.parallel_executor().workers());
        if (!base::IsLocalQueueSetValid(worker_num, queues)) {
          VLOG_FATAL << "queues [" << vnode_conf.parallel_executor().workers()
                     << "] is invalid! It should range from [0, "
                     << worker_num - 1 << "]";
        }
      }

      if (!base->Initialize(vnode_conf, VNodeCore::GetInstance(), mode)) {
        VLOG_WARN << "Fail to initialize class " << clazz;
        base->ShutDown();
        base.reset();
        auto end_time = std::chrono::steady_clock::now();
        VLOG_INFO << "Load vnode " << clazz << "(" << load_path
                  << ") elapsed time "
                  << std::chrono::duration_cast<std::chrono::milliseconds>(
                         end_time - start_time)
                         .count()
                  << " ms.";
        continue;
      }

      vnode_list_.emplace_back(std::move(base));
      auto end_time = std::chrono::steady_clock::now();
      VLOG_INFO << "Load vnode " << clazz << "(" << load_path
                << ") elapsed time "
                << std::chrono::duration_cast<std::chrono::milliseconds>(
                       end_time - start_time)
                       .count()
                << " ms.";
    }
  }
}

std::shared_ptr<VNodeBase> VGraphController::InitSingleVNode(
    const VNodeConfigWrapper& vnode_config_wrapper,
    std::vector<void*>& handler_list) {
  std::shared_ptr<VNodeBase> base;
  const auto& load_path = vnode_config_wrapper.load_path;
  const auto& class_name = vnode_config_wrapper.vnode_config.clazz();

  base.reset(CreateVNode(load_path, class_name, handler_list));
  if (!base->Initialize(vnode_config_wrapper.vnode_config,
                        VNodeCore::GetInstance(),
                        vnode_config_wrapper.running_mode)) {
    VLOG_WARN << "Fail to initialize class " << class_name;
    base->ShutDown();
    base.reset();
  }
  return base;
}

void VGraphController::VNodeInitInParallel() {
  if (uninitialized_vnode_list_.empty()) {
    LOG(INFO) << "No VNodes will be initialized in parallel.";
    return;
  }

  std::set<std::string> vnode_in_parallel_names;

  // (minghao): first initialize VNode with name ends with pre_create_vnode
  for (auto& [vnode_name, vnode_config_wrapper] : uninitialized_vnode_list_) {
    if (vnode_name.rfind("pre_create_vnode") != std::string::npos) {
      TRACE_EVENT_SCOPE(varch, VNode_Initialization_Impl,
                        vnode_config_wrapper.vnode_config.clazz());

      auto vnode = InitSingleVNode(vnode_config_wrapper, handler_list_);
      if (vnode != nullptr) {
        vnode_list_.emplace_back(vnode);
      }
    } else {
      vnode_in_parallel_names.insert(vnode_config_wrapper.vnode_config.name());
    }
  }

  // (minghao): then initialize Hdmap VNodes
  for (auto& [vnode_name, vnode_config_wrapper] : uninitialized_vnode_list_) {
    const auto& class_name = vnode_config_wrapper.vnode_config.clazz();
    if (vnode::GetVArchGlobalData()->HdMapClasses().count(class_name)) {
      TRACE_EVENT_SCOPE(varch, VNode_Initialization_Impl, class_name);
      auto vnode = InitSingleVNode(vnode_config_wrapper, handler_list_);
      if (vnode != nullptr) {
        vnode_list_.emplace_back(vnode);
      }
      vnode_in_parallel_names.erase(vnode_name);
    }
  }

  // (minghao): then some special VNodes
  // FIXME(minghao): revisit later
  static std::set<std::string> VNodeClassNeedSequentialInit = {"BEVFusionVNode",
                                                               "RoutingVNode"};
  for (auto& [vnode_name, vnode_config_wrapper] : uninitialized_vnode_list_) {
    const auto& class_name = vnode_config_wrapper.vnode_config.clazz();
    if (VNodeClassNeedSequentialInit.count(class_name)) {
      TRACE_EVENT_SCOPE(varch, VNode_Initialization_Impl, class_name);
      auto vnode = InitSingleVNode(vnode_config_wrapper, handler_list_);
      if (vnode != nullptr) {
        vnode_list_.emplace_back(vnode);
      }
      vnode_in_parallel_names.erase(vnode_name);
    }
  }

  // Log the names of VNodes to be initialized in parallel
  for (const auto& name : vnode_in_parallel_names) {
    LOG(INFO) << "VNode to be initialized in parallel: " << name;
  }

  // (minghao): the remaining VNodes will be initialized in parallel
  av_comm::ThreadPool thread_pool(
      std::min(static_cast<int>(std::thread::hardware_concurrency()),
               SetNumberOfThreadsForParaInit()));
  std::vector<std::future<void>> init_task_vec;
  init_task_vec.reserve(vnode_in_parallel_names.size());
  std::mutex vnode_list_mtx;

  for (auto& [vnode_name, vnode_config_wrapper] : uninitialized_vnode_list_) {
    if (vnode_in_parallel_names.count(vnode_name)) {
      init_task_vec.emplace_back(thread_pool.PushTask(
          [&, vnode_config_wrapper = vnode_config_wrapper] {
            TRACE_EVENT_SCOPE(varch, VNode_Initialization_Impl,
                              vnode_config_wrapper.vnode_config.clazz());

            auto vnode = InitSingleVNode(vnode_config_wrapper, handler_list_);
            if (vnode != nullptr) {
              std::unique_lock<std::mutex> lck(vnode_list_mtx);
              vnode_list_.emplace_back(vnode);
            }
          }));
    }
  }

  for (auto& task : init_task_vec) {
    task.get();
  }

  uninitialized_vnode_list_.clear();
}

void VGraphController::Go() {
  for (auto& vnode : vnode_list_) {
    vnode->Go();
  }
}

void VGraphController::LoadVGraph(const std::string& path,
                                  bool init_in_parallel) {
  // Check whether vgraph is valid in simulation, since simulator will directly
  // call this method.
  if (vnode::GetVArchGlobalData()->IsReplayTestMode()) {
    if (!vgraph_checker_.IsVGraphValid(path)) {
      VLOG_FATAL << "VGraph [" << path << "] is invalid!";
    }
  }

  const auto start_time = std::chrono::steady_clock::now();
  VForestConfig vgraph_config;
  VLOG_INFO << "Parse configuration file " << path;
  if (!base::ReadTextProtoFile(path, &vgraph_config)) {
    VLOG_FATAL << "Error parsing vgraph file: " << path;
  }
  LoadVGraph(vgraph_config, init_in_parallel);
  const auto end_time = std::chrono::steady_clock::now();
  VLOG_INFO << "Load vgraph " << path << " elapsed time "
            << std::chrono::duration_cast<std::chrono::milliseconds>(end_time -
                                                                     start_time)
                   .count()
            << " ms.";
}

int VGraphController::GetVNodeNum(const std::string& path) {
  VForestConfig vforest_config;
  int vnode_nums = 0;
  if (base::ReadTextProtoFile(path, &vforest_config)) {
    vnode_nums += vforest_config.vgraph().vnode_size();
  }
  return vnode_nums;
}

VGraphConfig VGraphController::GetVGraphConfig() {
  auto paths = args_.GetVGraphConfList();
  CHECK_GT(paths.size(), 0);
  auto path = paths.front();
  // Check whether vgraph is valid
  if (!vgraph_checker_.IsVGraphValid(path)) {
    VLOG_FATAL << "VGraph [" << path << "] is invalid!";
  }

  VForestConfig vgraph_config;
  VLOG_INFO << "Parse configuration file " << path;
  if (!base::ReadTextProtoFile(path, &vgraph_config)) {
    VLOG_FATAL << "Error parsing vgraph file: " << path;
  }

  return vgraph_config.vgraph();
}

void VGraphController::InitVArchPool(const VGraphConfig& vgraph) {
  if (FLAGS_varch_pool_size < 0 && !vgraph.has_varch_pool_size()) {
    varch::base::ThreadPool::GetInstance();
  } else {
    auto pool_size = FLAGS_varch_pool_size < 0 ? vgraph.varch_pool_size()
                                               : FLAGS_varch_pool_size;
    CHECK_GT(pool_size, 0) << "varch pool size:" << pool_size;
    varch::base::ThreadPool::GetInstance(pool_size);
  }
}

void VGraphController::InitSubProcessPool(const VGraphConfig& vgraph) {
  if (FLAGS_sub_proc_pool_size < 0 && !vgraph.has_sub_proc_pool_size()) {
    varch::vnode::detail::transport::SubProcessPool::GetInstance();
  } else {
    auto pool_size = FLAGS_sub_proc_pool_size < 0 ? vgraph.sub_proc_pool_size()
                                                  : FLAGS_sub_proc_pool_size;
    CHECK_GE(pool_size, 0) << "sub process pool size:" << pool_size;
    varch::vnode::detail::transport::SubProcessPool::GetInstance(pool_size);
  }
}

VGraphController::~VGraphController() {
  VLOG_INFO << "Destory||status=start";

  base::FormatAndLogThreadInfos();

  // Shutdown FramePublishService
  if (frame_publish_service_) {
    frame_publish_service_.reset();
    frame_publish_service_ = nullptr;
  }

  // Shutdown VNodes also shutdown the pubs and subs
  VLOG_INFO << "Shutdown VNode";
  for (auto&& vnode : vnode_list_) {
    vnode->ShutDown();
  }

  // Release the shared reference to vnode explicitly
  VLOG_INFO << "Destroy VNode";
  for (auto&& vnode_ptr : vnode_list_) {
    vnode_ptr.reset();
  }

  VLOG_INFO << "Close health";
#ifndef VARCH_NO_VOY_DEPENDENCY
  health_reporter_.reset();
#endif

  VLOG_INFO << "Clear Vnode";
  varch::Clear();
  // Stop scheduler and reclaim resource
  VLOG_INFO << "Clear scheduler";
  varch::scheduler::Clear();

  VLOG_INFO << "Destroy Handler";
  for (auto& handler : handler_list_) {
    VLOG_INFO << "CloseVNode";
    if (handler) CloseVNode(handler);
  }

  VLOG_INFO << "Destory||status=finish";
}

std::vector<std::shared_ptr<VNodeBase>>& VGraphController::GetVNodeList() {
  return vnode_list_;
}

const std::unique_ptr<VNodeCore>& VGraphController::GetVNodeCore() {
  return VNodeCore::GetInstance();
}

void VGraphController::AddVNodeToVNodeList(std::shared_ptr<VNodeBase>&& vnode) {
  vnode_list_.emplace_back(std::move(vnode));
}

}  // namespace launcher
}  // namespace tools
}  // namespace varch
