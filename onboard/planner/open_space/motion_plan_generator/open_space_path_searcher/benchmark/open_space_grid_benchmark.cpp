#include "planner/open_space/motion_plan_generator/open_space_path_searcher/grid.h"

#include <benchmark/benchmark.h>

#include <google/protobuf/text_format.h>

#include "onboard/planner/open_space/constraints/motion_plan_problem_manager.h"
#include "planner/behavior/util/lane_sequence/test/test_utility.h"

namespace planner {

namespace open_space {

namespace {

class BM_OpenSpaceGridUpdate : public benchmark::Fixture {
 public:
  void SetUp(const ::benchmark::State& state) {
    (void)state;
    CHECK(::google::protobuf::TextFormat::ParseFromString(
        R"pb(
          xy_grid_resolution_in_meters: 0.5
          heading_slot_resolution_in_rad: 0.1)pb",
        &config_));
  }
  pb::OpenSpaceGridConfig config_;
};

}  // namespace

BENCHMARK_DEFINE_F(BM_OpenSpaceGridUpdate, GridUpdate)
(benchmark::State& state) {  // NOLINT
  const RobotState robot_state = test_utility::ConstructRobotState(
      /*robot_x=*/0.0, /*robot_y=*/0.0, /*vel_x=*/0.0, /*vel_y=*/0.0);
  MotionPlanProblemManager motion_plan_problem_manager(robot_state);
  motion_plan_problem_manager.SetStartPoseInfoFromRobotState(
      /*initial_motion_mode_guidance=*/std::nullopt);
  motion_plan_problem_manager.AppendTargetPoseInfo(
      /*x=*/10.0, /*y=*/15.0, /*heading=*/0.0,
      /*terminal_motion_mode_guidance=*/std::nullopt,
      /*xy_tolerance_roi=*/std::nullopt,
      /*heading_tolerance_range=*/std::nullopt);
  motion_plan_problem_manager.SetMotionPlanROIFromStartAndTargetPoses(
      /*start_pose_xy_buffer=*/2.0 * robot_state.GetLength(),
      /*target_pose_xy_buffer=*/2.0 * robot_state.GetLength());

  math::geometry::PolylineCurve2d right_hard_boundary(
      std::vector<math::geometry::Point2d>{{0.0, -10.0},
                                           {1.0, -8.0},
                                           {2.0, -6.0},
                                           {3.0, -4.0},
                                           {4.0, -2.0},
                                           {5.0, 0.0},
                                           {6.0, 2.0},
                                           {7.0, 4.0},
                                           {8.0, 6.0},
                                           {9.0, 8.0},
                                           {10.0, 10.0}});
  motion_plan_problem_manager.AppendBoundary(std::move(right_hard_boundary),
                                             RequiredClearance(
                                                 /*critical_clearance=*/0.4,
                                                 /*comfort_clearance=*/0.75),
                                             ViolationTolerance::FORBID,
                                             Boundary::PassSide::LEFT,
                                             ViolationPenaltyLevel::CRITICAL);

  const MotionPlanProblem& motion_plan_problem =
      motion_plan_problem_manager.motion_plan_problem();
  Grid grid(config_);

  while (state.KeepRunning()) {
    grid.Update(motion_plan_problem);
  }
}

BENCHMARK_REGISTER_F(BM_OpenSpaceGridUpdate, GridUpdate);

}  // namespace open_space

}  // namespace planner

BENCHMARK_MAIN();

/*
----------------------------------------------------------------------------
Benchmark                                  Time             CPU   Iterations
----------------------------------------------------------------------------
BM_OpenSpaceGridUpdate/GridUpdate    9640850 ns      9640380 ns           56
*/
