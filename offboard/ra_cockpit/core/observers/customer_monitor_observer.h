#ifndef OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_CUSTOMER_MONITOR_OBSERVER_H_
#define OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_CUSTOMER_MONITOR_OBSERVER_H_

#include <functional>
#include <memory>

#include "../data_observer.h"
#include "planner_protos/customer_monitor_visual.pb.h"
#include "teleassist_protos/teleassist.pb.h"

namespace remote_assist {

class CustomerMonitorObserver : public IDataObserver {
 public:
  using CustomerMonitorCallback = std::function<void(
      std::shared_ptr<planner::pb::CustomerMonitorVisual>, DataChannel)>;

  explicit CustomerMonitorObserver(CustomerMonitorCallback callback);

  bool IsInterestedIn(
      const teleassist::pb::TeleassistRequest& message) override;

  std::shared_ptr<google::protobuf::Message> OnMessage(
      const teleassist::pb::TeleassistRequest& message,
      DataChannel data_channel) override;

 private:
  CustomerMonitorCallback callback_;
};

}  // namespace remote_assist

#endif  // OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_CUSTOMER_MONITOR_OBSERVER_H_
