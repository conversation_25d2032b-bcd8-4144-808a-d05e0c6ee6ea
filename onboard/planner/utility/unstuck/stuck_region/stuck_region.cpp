#include "planner/utility/unstuck/stuck_region/stuck_region.h"

#include <algorithm>
#include <utility>
#include <vector>

#include "absl/container/flat_hash_set.h"
#include "adv_geom/concave_hull.h"
#include "geometry/algorithms/convert.h"
#include "math/math_util.h"
#include "math/range.h"
#include "planner/behavior/util/lane_sequence_geometry/lane_sequence_geometry_utility.h"
#include "planner_protos/behavior_reasoners_debug.pb.h"
#include "planner_protos/lane_blockage.pb.h"
#include "pnc_map_service/map_elements/lane.h"

namespace planner {
namespace {
constexpr int kDefaultPolygonPointNum = 20;
// The default longitudinal distance buffer to check if the object occupies the
// lane. Here we set it 1.0m because we can assume that this buffer space
// before/after the object is also occupied by the object due to safe buffer.
constexpr double kOccupiedLongitudinalBufferInMeter = 1.0;

// Updates lanes occupied by the stuck region on the lane sequence. We
// guarantee that longitudinal blocking range refers to the reference line.
void UpdateOccupiedLanes(const std::vector<const pnc_map::Lane*>& lane_sequence,
                         const math::geometry::PolylineCurve2d& reference_line,
                         const math::Range1d& longitudinal_blocking_range,
                         std::vector<const pnc_map::Lane*>& occupied_lanes) {
  DCHECK(!lane_sequence.empty());
  DCHECK(math::IsValidRange(longitudinal_blocking_range));

  occupied_lanes.clear();
  occupied_lanes.reserve(lane_sequence.size());

  const auto lane_range = lane_selection::FindLaneRangeCoveringPath(
      lane_sequence, reference_line, planner::pb::MotionMode::FORWARD);

  for (int idx = lane_range.first; idx <= lane_range.second; ++idx) {
    const auto* lane = lane_sequence[idx];
    DCHECK(lane != nullptr);

    const auto& [lane_start_arclength, lane_end_arclength] =
        lane_selection::FindLaneArcRangeOnACurve(lane, reference_line);

    // Lane sequence is ordered, no need to check following lanes if current
    // lane is beyond blocking range.
    if (lane_start_arclength > (longitudinal_blocking_range.end_pos +
                                kOccupiedLongitudinalBufferInMeter)) {
      break;
    }

    // Continue to check next lane if current lane is before the blocking range.
    if (lane_end_arclength < (longitudinal_blocking_range.start_pos -
                              kOccupiedLongitudinalBufferInMeter)) {
      continue;
    }

    occupied_lanes.push_back(lane);
  }

  DCHECK(!occupied_lanes.empty())
      << " Longitudinal range: (" << longitudinal_blocking_range.start_pos
      << ", " << longitudinal_blocking_range.end_pos << ") "
      << " Ref Line: " << reference_line;
}
}  // namespace

StuckRegion::StuckRegion(const std::vector<const pnc_map::Lane*>& lane_sequence,
                         const std::vector<StuckObjectMeta>& stuck_meta_list,
                         const math::geometry::PolylineCurve2d& reference_line,
                         double freshness)
    : original_lane_sequence_(lane_sequence), freshness_(freshness) {
  DCHECK(!stuck_meta_list.empty());
  std::vector<math::geometry::Point2d> contour_points;
  absl::flat_hash_set<int64_t> object_associated_lanes;
  contour_points.reserve(stuck_meta_list.size() * kDefaultPolygonPointNum);
  for (const StuckObjectMeta& meta : stuck_meta_list) {
    const auto ret = stuck_objects_.emplace(meta.id, meta);
    DCHECK(ret.second);
    // Update stuck region longitudinal & lateral blocking range, and in-lane
    // clearance.
    UpdateBlockingRangeAndClearance(meta);

    contour_points.insert(contour_points.end(), meta.contour->points().begin(),
                          meta.contour->points().end());
    has_ra_confirmed_blockage_ |=
        (meta.blocking_type == pb::BlockingType::kRaBlockage);
    is_restored_from_last_seed_ |= meta.is_restored_from_last_seed;
    std::for_each(meta.associated_lanes.begin(), meta.associated_lanes.end(),
                  [&object_associated_lanes](const pnc_map::Lane* const lane) {
                    object_associated_lanes.insert(lane->id());
                  });
  }
  contour_ = math::geometry::PolygonWithCache2d(
      adv_geom::ConcaveHull(std::move(contour_points)));

  bool found_stuck_lane = false;
  for (auto lane_iter = lane_sequence.rbegin();
       lane_iter != lane_sequence.rend(); ++lane_iter) {
    if (!found_stuck_lane &&
        !object_associated_lanes.contains((*lane_iter)->id())) {
      continue;
    }
    lane_sequence_identifier_.push_back(*lane_iter);
    found_stuck_lane = true;
  }
  std::reverse(lane_sequence_identifier_.begin(),
               lane_sequence_identifier_.end());
  if (lane_sequence_identifier_.empty()) {
    lane_sequence_identifier_ = lane_sequence;
  }

  UpdateOccupiedLanes(lane_sequence, reference_line,
                      longitudinal_blocking_range_, occupied_lanes_);
}

void StuckRegion::ToProto(pb::StuckRegion* stuck_region) const {
  if (stuck_region == nullptr) return;

  for (const auto& [id, _] : stuck_objects()) {
    pb::StuckObjectInfo* stuck_object_info = stuck_region->add_stuck_objects();
    stuck_object_info->set_object_id(id.id);
    stuck_object_info->set_object_type(id.type);
  }

  for (const auto* lane : lane_sequence_identifier_) {
    stuck_region->add_identifier(DCHECK_NOTNULL(lane)->id());
  }
  stuck_region->mutable_longitudinal_range()->set_start_pos(
      longitudinal_blocking_range_.start_pos);
  stuck_region->mutable_longitudinal_range()->set_end_pos(
      longitudinal_blocking_range_.end_pos);
  math::geometry::Convert(contour(), *stuck_region->mutable_contour());
  stuck_region->set_is_confirmed_by_ra(has_ra_confirmed_blockage_);
}

bool StuckRegion::Merge(const math::geometry::PolylineCurve2d& reference_line,
                        const StuckRegion& new_region) {
  // Return false if new_region belongs to different lane sequence.
  const auto& main_identifier =
      (identifier().size() > new_region.identifier().size())
          ? identifier()
          : new_region.identifier();
  const auto& sub_identifier =
      (identifier().size() > new_region.identifier().size())
          ? new_region.identifier()
          : identifier();
  for (size_t i = 0; i < sub_identifier.size(); ++i) {
    if (sub_identifier.at(i) != main_identifier.at(i)) {
      return false;
    }
  }
  lane_sequence_identifier_ = main_identifier;

  MergeStuckObjects(new_region);

  MergeBlockingRangeAndClearance(
      new_region.lateral_range(), new_region.longitudinal_range(),
      new_region.fully_longitudinal_range(), new_region.left_inlane_clearance(),
      new_region.right_inlane_clearance());

  UpdateOccupiedLanes(identifier(), reference_line, longitudinal_range(),
                      occupied_lanes_);

  MergeContour(new_region.contour());
  return true;
}

void StuckRegion::Merge(const math::geometry::PolylineCurve2d& reference_line,
                        const std::vector<StuckObjectMeta>& stuck_meta_list) {
  if (stuck_meta_list.empty()) {
    return;
  }

  absl::flat_hash_set<int64_t> object_associated_lanes;
  std::vector<math::geometry::Point2d> contour_points;
  contour_points.reserve(stuck_meta_list.size() * kDefaultPolygonPointNum);
  for (const StuckObjectMeta& meta : stuck_meta_list) {
    const auto ret = stuck_objects_.emplace(meta.id, meta);
    DCHECK(ret.second);
    // Update stuck region longitudinal & lateral blocking range, and in-lane
    // clearance.
    UpdateBlockingRangeAndClearance(meta);

    contour_points.insert(contour_points.end(), meta.contour->points().begin(),
                          meta.contour->points().end());
    has_ra_confirmed_blockage_ |=
        (meta.blocking_type == pb::BlockingType::kRaBlockage);
    is_restored_from_last_seed_ |= meta.is_restored_from_last_seed;
    std::for_each(meta.associated_lanes.begin(), meta.associated_lanes.end(),
                  [&object_associated_lanes](const pnc_map::Lane* const lane) {
                    object_associated_lanes.insert(lane->id());
                  });
  }

  lane_sequence_identifier_.clear();
  bool found_stuck_lane = false;
  for (auto lane_iter = original_lane_sequence_.rbegin();
       lane_iter != original_lane_sequence_.rend(); ++lane_iter) {
    if (!found_stuck_lane &&
        !object_associated_lanes.contains((*lane_iter)->id())) {
      continue;
    }
    lane_sequence_identifier_.push_back(*lane_iter);
    found_stuck_lane = true;
  }
  std::reverse(lane_sequence_identifier_.begin(),
               lane_sequence_identifier_.end());
  if (lane_sequence_identifier_.empty()) {
    lane_sequence_identifier_ = original_lane_sequence_;
  }

  UpdateOccupiedLanes(identifier(), reference_line, longitudinal_range(),
                      occupied_lanes_);

  MergeContour(math::geometry::PolygonWithCache2d(std::move(contour_points)));
}

std::optional<TypedObjectId> StuckRegion::GetDominantObjectId() const {
  std::optional<TypedObjectId> dominant_obj_id = std::nullopt;
  double closest_dist = std::numeric_limits<double>::max();
  for (const auto& [typed_id, meta] : stuck_objects_) {
    if (!meta.is_post_group_object &&
        math::UpdateMin(meta.s_range.start_pos, closest_dist)) {
      dominant_obj_id.emplace(typed_id);
    }
  }
  return dominant_obj_id;
}

pb::BlockingType StuckRegion::GetFirstBlockageType(double start_pos) const {
  pb::BlockingType first_blocking_type = pb::BlockingType::kInvalid;
  double first_start_pos = std::numeric_limits<double>::max();
  for (const auto& [_, meta] : stuck_objects_) {
    if (!meta.is_post_group_object && meta.s_range.end_pos > start_pos &&
        math::UpdateMin(meta.s_range.start_pos, first_start_pos)) {
      first_blocking_type = meta.blocking_type;
    }
  }
  return first_blocking_type;
}

int64_t StuckRegion::GetMaxTempParkedDuration() const {
  int64_t max_temp_parked_duration = 0;
  for (const auto& [_, meta] : stuck_objects_) {
    math::UpdateMax(meta.temp_parked_duration, max_temp_parked_duration);
  }
  return max_temp_parked_duration;
}

void StuckRegion::UpdateBlockingRangeAndClearance(const StuckObjectMeta& meta) {
  math::UpdateMin(meta.s_range.start_pos,
                  longitudinal_blocking_range_.start_pos);
  math::UpdateMax(meta.s_range.end_pos, longitudinal_blocking_range_.end_pos);
  math::UpdateMin(meta.fully_s_range.start_pos,
                  fully_longitudinal_blocking_range_.start_pos);
  math::UpdateMax(meta.fully_s_range.end_pos,
                  fully_longitudinal_blocking_range_.end_pos);
  math::UpdateMin(meta.l_range.start_pos, lateral_blocking_range_.start_pos);
  math::UpdateMax(meta.l_range.end_pos, lateral_blocking_range_.end_pos);
  math::UpdateMin(meta.left_inlane_clearance, left_inlane_clearance_);
  math::UpdateMin(meta.right_inlane_clearance, right_inlane_clearance_);
}

void StuckRegion::MergeStuckObjects(const StuckRegion& new_region) {
  // Update freshness, the basic update equation is:
  //   `final_freshness_ = (A_new_obj_num + B_new_obj_num) / AB_total_obj_num`
  const int total = stuck_objects_.size() + new_region.stuck_objects().size();
  DCHECK_GE(total, 1);
  const double base_weight = 1.0 * stuck_objects_.size() / total;
  freshness_ =
      freshness_ * base_weight + new_region.freshness() * (1.0 - base_weight);
  has_ra_confirmed_blockage_ |= new_region.has_ra_confirmed_blockage();
  is_restored_from_last_seed_ |= new_region.is_restored_from_last_seed();

  // Update stuck objects.
  for (const auto& [id, meta] : new_region.stuck_objects()) {
    stuck_objects_.emplace(id, meta);
  }
}

void StuckRegion::MergeBlockingRangeAndClearance(
    const math::Range1d& new_lateral_range,
    const math::Range1d& new_longitudinal_range,
    const math::Range1d& new_fully_longitudinal_range,
    double new_left_inlane_clearance, double new_right_inlane_clearance) {
  DCHECK(math::IsValidRange(new_lateral_range));
  DCHECK(math::IsValidRange(new_longitudinal_range));
  math::UpdateMin(new_lateral_range.start_pos,
                  lateral_blocking_range_.start_pos);
  math::UpdateMax(new_lateral_range.end_pos, lateral_blocking_range_.end_pos);
  math::UpdateMin(new_longitudinal_range.start_pos,
                  longitudinal_blocking_range_.start_pos);
  math::UpdateMax(new_longitudinal_range.end_pos,
                  longitudinal_blocking_range_.end_pos);
  math::UpdateMin(new_fully_longitudinal_range.start_pos,
                  fully_longitudinal_blocking_range_.start_pos);
  math::UpdateMax(new_fully_longitudinal_range.end_pos,
                  fully_longitudinal_blocking_range_.end_pos);
  math::UpdateMin(new_left_inlane_clearance, left_inlane_clearance_);
  math::UpdateMin(new_right_inlane_clearance, right_inlane_clearance_);
}

void StuckRegion::MergeContour(
    const math::geometry::PolygonWithCache2d& new_contour) {
  std::vector<math::geometry::Point2d> contour_points;
  contour_points.reserve(contour_.points().size() +
                         new_contour.points().size());
  contour_points.insert(contour_points.end(), contour_.begin(), contour_.end());
  contour_points.insert(contour_points.end(), new_contour.begin(),
                        new_contour.end());
  contour_ = math::geometry::PolygonWithCache2d(
      adv_geom::ConcaveHull(std::move(contour_points)));
}

}  // namespace planner
