#include "tts_audio_info_observer.h"

#include <utility>

#include "av_comm/topics.h"
#include "glog/logging.h"

namespace remote_assist {

TTSAudioInfoObserver::TTSAudioInfoObserver(TTSAudioInfoCallback callback)
    : callback_(std::move(callback)) {}

bool TTSAudioInfoObserver::IsInterestedIn(
    const teleassist::pb::TeleassistRequest& message) {
  return (message.message_type() ==
          ra_cockpit::pb::TTSAudioInfo::GetDescriptor()->name());
}

std::shared_ptr<google::protobuf::Message> TTSAudioInfoObserver::OnMessage(
    const teleassist::pb::TeleassistRequest& message,
    DataChannel data_channel) {
  auto msg = std::make_shared<ra_cockpit::pb::TTSAudioInfo>();
  if (!msg->ParseFromString(message.message_data())) {
    LOG(ERROR) << "Received error TTSAudioInfo message format, len = "
               << message.message_data().length();
    return nullptr;
  }
  if (callback_) {
    callback_(msg, data_channel);
  }
  return msg;
}

}  // namespace remote_assist
