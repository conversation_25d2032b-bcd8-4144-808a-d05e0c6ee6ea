#ifndef ONBOARD_PLANNER_TRAJECTORY_EVALUATION_TRAJECTORY_EVALUATOR_H_
#define ONBOARD_PLANNER_TRAJECTORY_EVALUATION_TRAJECTORY_EVALUATOR_H_

#include <vector>

#include "planner/trajectory/evaluation/trajectory_evaluator_clerk.h"
#include "planner/world_model/world_model.h"
#include "planner_protos/trajectory_config.pb.h"
#include "stats/stats.h"
#include "voy_protos/trajectory.pb.h"

namespace planner {
// This class provides API to evaluate a given trajectory, including:
// - Checking for extreme dynamics.
// - Checking for boundary violations.
// - Checking for obstacle collisions (with WorldModel input).
class TrajectoryEvaluator {
 public:
  // The default constructor which is only used by ddp_mock_planner.
  TrajectoryEvaluator() = delete;

  explicit TrajectoryEvaluator(const pb::TrajectoryEvaluationConfig& config)
      : trajectory_evaluator_clerk_(/*is_enabled=*/STATS_ENABLED()),
        config_(config) {}

  // Checks for trajectory violations.
  TrajectoryEvaluationResult Evaluate(
      const WorldModel& world_model, const pb::Trajectory& trajectory,
      const std::vector<math::geometry::Polyline2d>& hard_boundary_lines,
      pb::TrajectoryEvaluationDebug* debug) const;

  void AddTrajectoryAnalytics(
      const TrajectoryEvaluationResult& evaluation_result,
      const pb::Trajectory& trajectory,
      pb::TrajectoryProblemType trajectory_problem_type) const;

 private:
  analytics::TrajectoryEvaluatorClerk trajectory_evaluator_clerk_;
  const pb::TrajectoryEvaluationConfig config_;
};
}  // namespace planner

#endif  // ONBOARD_PLANNER_TRAJECTORY_EVALUATION_TRAJECTORY_EVALUATOR_H_
