#include "planner/decoupled_maneuvers/lane_sequence_proposer/lane_sequence_proposer.h"

#include <algorithm>
#include <limits>
#include <numeric>
#include <optional>
#include <set>
#include <sstream>
#include <utility>
#include <vector>

#include <absl/container/flat_hash_set.h>
#include <glog/logging.h>

#include "adv_geom/path2d_with_juke.h"
#include "base/optional_value_accessor.h"
#include "geometry/algorithms/specialization/within_polygon_with_cache.h"
#include "geometry/model/point_2d.h"
#include "geometry/model/polyline_curve.h"
#include "math/enum.h"
#include "math/math_util.h"
#include "math/range.h"
#include "planner/behavior/util/agent_state/agent_in_lane_param_generation.h"
#include "planner/behavior/util/lane_common/lane_sequence_result_definition.h"
#include "planner/behavior/util/lane_sequence/planning_segment_sequence_utility.h"
#include "planner/behavior/util/lane_sequence/route_preview_utility.h"
#include "planner/behavior/util/lane_sequence_geometry/lane_sequence_geometry_utility.h"
#include "planner/decoupled_maneuvers/lane_change/lane_change_sequence_proposer.h"
#include "planner/path/reasoning/agent_intention/semantic_context/semantic_context_result.h"
#include "planner/selection/trajectory_meta.h"
#include "planner/speed/reasoning_input/trajectory_info.h"
#include "planner/utility/common/lane_search_util.h"
#include "planner/utility/grouping/group_util.h"
#include "planner_protos/behavior_reasoner_seed.pb.h"
#include "planner_protos/lane_sequence_proposal.pb.h"
#include "planner_protos/path_reasoning_seed.pb.h"
#include "planner_protos/planning_lane_sequence.pb.h"
#include "pnc_map_service/map_elements/lane.h"
#include "rt_event/rt_event.h"
#include "trace/trace.h"
#include "voy_protos/math.pb.h"
#include "voy_protos/trajectory.pb.h"
#include "voy_rt_event/rt_event_planner.h"
#include "voy_trace/trace_planner.h"

namespace planner {
namespace {
// The debug log header for the lane sequence proposer.
static constexpr const char* kLogHeader = "[LaneSequenceProposer]\n";
// The default threshold hold to check if the stuck region is closed to turn.
constexpr double kDefaultClosedDistToTurnInMeter = 20.0;
// The default look ahead planning duration to check if the ego pose will
// encroach the neighbor lane, or not.
constexpr int64_t kDefaultLookAheadPlanningDurationInMSec = 3000;
// The minimum collision risk to identify the emergency scenario.
constexpr double kMinimumCollisionRiskForEmergency = 1.0;
constexpr double kExtremelyHighRiskyCollisionThreshold = 5.0;
// The minimum distance to regard that the previewed route is effortless. This
// distance is from preview point to the end pos of the next lane change
// segment.
constexpr double kMinimumEffortlessDistanceToNextLaneChangeInMeter = 20.0;
// Default heading align threshold in radius.
constexpr double kDefaultAlignedHeadingToleranceInRadian = 5.0 / 180.0 * M_PI;
// Default max search distance to check if two lanes are connective. It should
// be further farther than planned path.
constexpr double kMaxDistanceToCheckLaneConnectiveInMeter = 200.0;
// Default allowable longitudinal encroachment distance nearby pull over
// destination.
constexpr double kMaxAllowableLongitudinalDistanceInMeter = 10.0;
// Minimum encroachment distance to make ego consider to switch lane sequence.
// Here we select 0.4m which is close to 'tire‘s width + lane marking width'.
constexpr double kMinLateralDistanceToSwithLaneInMete = 0.4;
// Maximum tolerable accumulated encroachment distance and duration of the
// trajectory. If the trajectory will encroach out for a longer distance, or a
// longer time duration than the corresponding threshold, try to propose
// lane-switch proposal.
constexpr double kMaxTolerableEncroachmentDistanceInInMeter = 30.0;
constexpr int64_t kMaxTolerableEncroachmentDurationInInMSec = 2000;

// Clamps planning segment sequence according to the trimmed lane sequence.
bool ClampXLaneNudgeLatchedSegmentSequence(
    const speed::TrajectoryInfo& trajectory_info,
    pb::PlanningSegmentSequence* segment_sequence) {
  DCHECK_NE(segment_sequence, nullptr);
  pb::PlanningSegmentSequence latched_seq;
  for (auto lane_iter = trajectory_info.lane_sequence_iterator().current_lane();
       lane_iter != trajectory_info.lane_sequence_iterator().end();
       ++lane_iter) {
    pb::Segment* seg = segment_sequence->add_planning_segments()
                           ->mutable_lane_follow_segment()
                           ->mutable_lf_segment();
    seg->set_lane_id(DCHECK_NOTNULL(*lane_iter)->id());
    seg->set_start_arclength(0.0);
    seg->set_end_arclength(DCHECK_NOTNULL(*lane_iter)->length());
  }

  return true;
}

bool GenerateWaypointAssistLatchedSegmentSequence(
    const std::shared_ptr<const LaneSequenceResult>&
        waypoint_proposed_lane_sequence,
    pb::PlanningSegmentSequence* segment_sequence) {
  DCHECK(waypoint_proposed_lane_sequence);
  for (const auto& lane_ptr : waypoint_proposed_lane_sequence->lane_sequence) {
    segment_sequence->add_planning_segments()->CopyFrom(
        lane_selection::ConstructLaneFollowPlanningSegment(*lane_ptr, 0,
                                                           lane_ptr->length()));
  }
  return !segment_sequence->planning_segments().empty();
}

// Returns true if the ego is closed to the point where is prone to
// re-route if lane sequence changed.
// TODO(tianxi): polish this logic by using route preview in the next step.
bool IsXLaneNudgeCloseToReroutePoint(
    const selection::TrajectoryMetaData& trajectory_meta) {
  const auto& lane_seq_result = trajectory_meta.lane_sequence_result();
  DCHECK(lane_seq_result.lane_sequence_type ==
             pb::LaneSequenceCandidate::LANE_FOLLOW ||
         lane_seq_result.lane_sequence_type ==
             pb::LaneSequenceCandidate::ALTERNATIVE_LANE_FOLLOW ||
         lane_seq_result.lane_sequence_type ==
             pb::LaneSequenceCandidate::STUCK_AVOIDANCE_LANE_FOLLOW ||
         lane_seq_result.lane_sequence_type ==
             pb::LaneSequenceCandidate::BACKUP_LANE_FOLLOW);
  DCHECK_NE(lane_seq_result.current_lane, nullptr);

  const auto& trajectory_info = trajectory_meta.trajectory_info;

  const auto junction_turn_lane_iter = std::find_if(
      trajectory_info->lane_sequence_iterator().current_lane(),
      trajectory_info->lane_sequence_iterator().end(),
      [](const pnc_map::Lane* lane) {
        return lane->turn() != hdmap::Lane::STRAIGHT && lane->IsInJunction();
      });
  // Return false if there is no turning lane in current lane sequence.
  if (junction_turn_lane_iter ==
      trajectory_info->lane_sequence_iterator().end()) {
    return false;
  }

  const math::geometry::PolylineCurve2d& reference_line =
      trajectory_meta.lane_sequence_geometry().nominal_path;
  const double turning_lane_start_pos =
      reference_line
          .GetProximity(
              (*junction_turn_lane_iter)->center_line().GetStartPoint(),
              math::pb::UseExtensionFlag::kForbid)
          .arc_length;

  const pb::PathReasoningSeed& path_reasoning_seed =
      trajectory_meta.path_reasoning_seed();

  DCHECK(path_reasoning_seed.has_xlane_nudge_seed());
  const math::pb::Range& stuck_range_on_reference_line =
      path_reasoning_seed.xlane_nudge_seed()
          .target_stuck_region()
          .longitudinal_range();

  // Return true if the turning lane is close to the stuck range.
  return math::IsInRange(turning_lane_start_pos,
                         stuck_range_on_reference_line.start_pos() -
                             kDefaultClosedDistToTurnInMeter,
                         stuck_range_on_reference_line.end_pos() +
                             kDefaultClosedDistToTurnInMeter);
}

// Returns true if current scenario needs to exclusively latch lane sequence for
// the selected x-lane nudge.
bool IsExclusiveLatchScenarioForXLaneNudge(
    const selection::TrajectoryMetaData& trajectory_meta) {
  if (!trajectory_meta.IsCrossLaneNudge()) {
    return false;
  }

  // Exclusively latch lane sequence if the ego is close the point where is
  // prone to re-route.
  if (IsXLaneNudgeCloseToReroutePoint(trajectory_meta)) {
    return true;
  }

  return false;
}

// Returns the preview lane point on the lane sequence of the given side.
std::optional<lane_selection::PreviewLanePoint> GetPreviewLanePoint(
    const selection::TrajectoryMetaData& selected_trajectory,
    double dist_to_destination, bool is_left, std::ostringstream& debug_oss) {
  const std::vector<const pnc_map::Lane*>& lane_sequence =
      selected_trajectory.lane_sequence();
  DCHECK(!lane_sequence.empty());
  const speed::TrajectoryInfo& trajectory_info =
      *DCHECK_NOTNULL(selected_trajectory.trajectory_info);
  // Get the first merge back point on the path.
  const pb::EgoCurrentLaneRelativeState::Enum ego_current_lane_relative_state =
      trajectory_info.ego_current_lane_relative_state();
  const speed::pb::PathOutOfLaneInfo& out_lane_info =
      is_left ? (ego_current_lane_relative_state ==
                         pb::EgoCurrentLaneRelativeState::FullyOnLeftOutside
                     ? trajectory_info.right_lane_encroachment_info()
                     : trajectory_info.left_lane_encroachment_info())
              : (ego_current_lane_relative_state ==
                         pb::EgoCurrentLaneRelativeState::FullyOnRightOutside
                     ? trajectory_info.left_lane_encroachment_info()
                     : trajectory_info.right_lane_encroachment_info());
  // Return std::nullopt if there is no out-of-lane segments.
  if (out_lane_info.out_of_lane_segments().empty()) {
    return std::nullopt;
  }

  double merge_back_arclength =
      (ego_current_lane_relative_state ==
           pb::EgoCurrentLaneRelativeState::FullyOnLeftOutside ||
       ego_current_lane_relative_state ==
           pb::EgoCurrentLaneRelativeState::FullyOnRightOutside)
          ? out_lane_info.out_of_lane_segments().cbegin()->start()
          : out_lane_info.out_of_lane_segments().cbegin()->end();
  constexpr double kPullOverPreparationDistanceBufferInMeter = 10.0;
  merge_back_arclength = math::Clamp(
      dist_to_destination - kPullOverPreparationDistanceBufferInMeter, 0.0,
      merge_back_arclength);
  const adv_geom::Path2dWithJuke& unextended_path =
      trajectory_info.unextended_path_for_overlap().ego_path_juke();
  const math::geometry::Point2d merge_back_point =
      math::IsApprox(merge_back_arclength, unextended_path.GetTotalArcLength())
          ? unextended_path.points().back()
          : unextended_path.GetLinearInterpPoint(merge_back_arclength);
  // Find the lane where ego merges back to, and get a preview point on its
  // neighbor lane. Set default merge back lane to the last lane of
  // lane_sequence.
  const pnc_map::Lane* merge_back_lane = lane_sequence.back();
  for (const auto* lane : lane_sequence) {
    const math::geometry::PolylineCurve2d& center_line = lane->center_line();
    if (center_line
            .GetProximity(merge_back_point, math::pb::UseExtensionFlag::kForbid)
            .relative_position == math::RelativePosition::kWithIn) {
      merge_back_lane = lane;
      break;
    }
  }
  // Consider the adjacent lane at first, and then consider it brother lanes.
  DCHECK_NE(merge_back_lane, nullptr);
  const pnc_map::Lane* neighbor_lane =
      is_left ? merge_back_lane->adjacent_left_lane()
              : merge_back_lane->adjacent_right_lane();
  if (neighbor_lane == nullptr) {
    neighbor_lane =
        is_left ? merge_back_lane->left_lane() : merge_back_lane->right_lane();
  }

  // Return std::nullopt if there is no valid neighbor lane.
  if (neighbor_lane == nullptr || !neighbor_lane->IsRobotDrivable()) {
    return std::nullopt;
  }

  DCHECK_NE(neighbor_lane, nullptr);
  const double preview_point_arclength =
      neighbor_lane->center_line()
          .GetProximity(merge_back_point, math::pb::UseExtensionFlag::kForbid)
          .arc_length;
  debug_oss << "Preview Point: (" << neighbor_lane->id() << ", "
            << preview_point_arclength << ").\n";
  return lane_selection::PreviewLanePoint{
      .lane = neighbor_lane, .arc_length = preview_point_arclength};
}

// Returns true if the ego will hard encroach the given neighbor lane.
bool WillEgoHardEncroachNeighborLane(
    const selection::TrajectoryMetaData& selected_trajectory, bool is_left,
    int64_t look_ahead_time_duration, std::ostringstream& debug_oss) {
  DCHECK(!selected_trajectory.trajectory.poses().empty());
  // Get distance bias from front bumper to the center of wheel base.
  const double front_corner_x_offset =
      selected_trajectory.rear_axle_to_front_bumper_m() -
      0.5 * selected_trajectory.ego_in_lane_params().wheel_base;
  const double half_ego_width =
      selected_trajectory.ego_in_lane_params().width_m * 0.5;
  const double front_corner_y_offset =
      is_left ? half_ego_width : -half_ego_width;
  // Function to get the outlier front corner point of ego car based on ego's
  // pose.
  const auto get_outlier_front_corner_pt =
      [front_corner_x_offset,
       front_corner_y_offset](const pb::TrajectoryPose& pose) {
        const double sin_op = math::FastSin(pose.heading());
        const double cos_op = math::GetCosBySin(pose.heading(), sin_op);
        return math::geometry::Point2d(
            pose.x_pos() + cos_op * front_corner_x_offset -
                sin_op * front_corner_y_offset,
            pose.y_pos() + sin_op * front_corner_x_offset +
                cos_op * front_corner_y_offset);
      };

  const int64_t look_ahead_timestamp =
      selected_trajectory.trajectory.poses().at(0).timestamp() +
      look_ahead_time_duration;
  const math::geometry::PolylineCurve2d& current_ls_boundary =
      is_left
          ? selected_trajectory.lane_sequence_geometry().left_lane_boundary
          : selected_trajectory.lane_sequence_geometry().right_lane_boundary;

  double accumulated_encroached_dist_m = 0.0;
  int64_t accumulated_encroached_duration_ms = 0;
  const pb::TrajectoryPose* prev_pose =
      &(selected_trajectory.trajectory.poses().at(0));
  for (const pb::TrajectoryPose& pose :
       selected_trajectory.trajectory.poses()) {
    // Stop check further poses.
    if (pose.timestamp() > look_ahead_timestamp) {
      break;
    }

    // TrajectoryPose records the center point of the wheel basse, we should get
    // front corner point at first.
    const math::geometry::Point2d front_cornet_pt =
        get_outlier_front_corner_pt(pose);
    const math::ProximityQueryInfo proximity = current_ls_boundary.GetProximity(
        front_cornet_pt, math::pb::UseExtensionFlag::kForbid);
    const double encroach_out_dist_m =
        is_left ? proximity.signed_dist * 1 : proximity.signed_dist * -1;

    // Return true if ego will encroach half width out of current lane sequence
    // boundary.
    if (look_ahead_timestamp > pose.timestamp() &&
        encroach_out_dist_m > half_ego_width) {
      debug_oss << "Will encroach neighbor lane with half ego width in "
                << kDefaultLookAheadPlanningDurationInMSec << "ms.\n";
      return true;
    }

    // Accumulate the trajectory encroachment distance and duration, and update
    // previous pose pointer.
    if (encroach_out_dist_m > kMinLateralDistanceToSwithLaneInMete) {
      accumulated_encroached_dist_m += pose.odom() - prev_pose->odom();
      accumulated_encroached_duration_ms +=
          pose.timestamp() - prev_pose->timestamp();
    }
    prev_pose = &pose;

    // If the ego will encroach out lane sequence boundary for a long time and
    // a long distance, return true to propose lane-switch proposal.
    if (accumulated_encroached_dist_m >
            kMaxTolerableEncroachmentDistanceInInMeter &&
        accumulated_encroached_duration_ms >
            kMaxTolerableEncroachmentDurationInInMSec) {
      debug_oss << "Will encroach neighbor lane for long distance("
                << kMaxTolerableEncroachmentDistanceInInMeter << "m) & time("
                << kMaxTolerableEncroachmentDurationInInMSec << "ms).\n";
      return true;
    }
  }

  return false;
}

// Returns the planning segment sequence based on the given lane.
pb::PlanningSegmentSequence GenerateSingleLaneSequenceSegment(
    const pnc_map::Lane& lane) {
  pb::PlanningSegmentSequence seg;
  pb::Segment* lane_seg = seg.add_planning_segments()
                              ->mutable_lane_follow_segment()
                              ->mutable_lf_segment();
  lane_seg->set_lane_id(lane.id());
  lane_seg->set_start_arclength(0.0);
  lane_seg->set_end_arclength(lane.length());

  return seg;
}

// Returns true if the ego state is aligned with the target lane. In detail,
// ego's related heading to the lane should match with its lane-switch
// direction,
bool IsEgoStateAlignedWithLaneSwitchIntention(
    const pb::Trajectory& trajectory, const RobotStateSnapshot& plan_init_state,
    const pnc_map::Lane& target_lane, bool is_left,
    std::ostringstream& debug_oss) {
  // A function to check if the heading match with left/right direction. We
  // regard that left-direction matches with positive heading, and
  // right-direction matches with negative heading.
  const auto is_heading_match_direction = [](double heading,
                                             bool is_left) -> bool {
    return (heading * (is_left ? 1.0 : -1.0)) >
           -kDefaultAlignedHeadingToleranceInRadian;
  };

  const math::ProximityQueryInfo reference_proximity_info =
      target_lane.center_line().GetProximity(
          plan_init_state.front_bumper_position(),
          math::pb::UseExtensionFlag::kForbid);
  const double reference_heading = target_lane.center_line().GetInterpTheta(
      reference_proximity_info.arc_length);
  const double relative_heading =
      math::AngleDiff(plan_init_state.heading(), reference_heading);
  // Check if the ego heading shows merge-back intention through relative
  // heading to the target lane.
  if (!is_heading_match_direction(relative_heading, is_left)) {
    debug_oss << "Current Lane:" << target_lane.id()
              << " Relative Heading: " << relative_heading << "\n";
    return false;
  }

  // Planner will receive the requested lane sequence in the next cycle at
  // least. If ego is merging back and diverging from the target lane, ego might
  // generate unreasonable S-traj behavior to switch to the target lane.
  // To avoid that situation, we would check ego's state in the future whether
  // still allow ego to switch to the target lane.
  static constexpr int64_t kMaxPlanningOneCycleLatencyInMSec = 500;
  const int64_t look_forward_timestamp =
      plan_init_state.timestamp() + kMaxPlanningOneCycleLatencyInMSec;
  DCHECK(!trajectory.poses().empty());
  const auto iter =
      std::lower_bound(trajectory.poses().begin(), trajectory.poses().end(),
                       look_forward_timestamp,
                       [](const pb::TrajectoryPose& pose, double timestamp) {
                         return pose.timestamp() < timestamp;
                       });
  const pb::TrajectoryPose& future_pose =
      (iter == trajectory.poses().end()) ? *trajectory.poses().cbegin() : *iter;
  const double reference_arclength =
      target_lane.center_line()
          .GetProximityWithFixedHint(
              {future_pose.x_pos(), future_pose.y_pos()},
              math::pb::UseExtensionFlag::kForbid,
              reference_proximity_info.closest_segment_index)
          .arc_length;
  const double future_reference_heading =
      target_lane.center_line().GetInterpTheta(reference_arclength);
  const double future_relative_heading =
      math::AngleDiff(future_pose.heading(), future_reference_heading);
  debug_oss << "Future Relative Heading: " << future_relative_heading << "\n";

  return is_heading_match_direction(future_relative_heading, is_left);
}

// Returns true if the lane point is on the given lane segments.
bool IsPreviewPointOnLaneSegments(
    const lane_selection::PreviewLanePoint& preview_point,
    const ::google::protobuf::RepeatedPtrField<pb::Segment>& lane_segments) {
  DCHECK_NE(preview_point.lane, nullptr);
  return std::any_of(lane_segments.begin(), lane_segments.end(),
                     [&preview_point](const auto& segment) {
                       return segment.lane_id() == preview_point.lane->id() &&
                              math::IsInRange(preview_point.arc_length,
                                              segment.start_arclength(),
                                              segment.end_arclength());
                     });
}

// Returns the distance from preview point to the end of the lane segments. If
// the point is out of the segments, returns the entire length of the lane
// segments.
double GetDistanceFromPreviewPointToLaneSegmentsEnd(
    const ::google::protobuf::RepeatedPtrField<pb::Segment>& lane_segments,
    const lane_selection::PreviewLanePoint& preview_point) {
  double dist_from_start_to_end = 0.0;
  double dist_from_start_to_preview_point = 0.0;
  for (const auto& seg : lane_segments) {
    if (seg.lane_id() == preview_point.lane->id() &&
        math::IsInRange(preview_point.arc_length, seg.start_arclength(),
                        seg.end_arclength())) {
      dist_from_start_to_preview_point =
          dist_from_start_to_end +
          (preview_point.arc_length - seg.start_arclength());
    }
    dist_from_start_to_end += (seg.end_arclength() - seg.start_arclength());
  }

  return dist_from_start_to_end - dist_from_start_to_preview_point;
}

// Returns true if the preview route is on-route and it's also effortless. Here
// we consider the route is effortless if ego doesn't need to lane change back
// in a long distance.
bool HasEffortlessOnRoutePreviewResult(
    const std::optional<lane_selection::PreviewRouteResult>& preview_result_opt,
    const lane_selection::PreviewLanePoint& lane_point_to_pass, bool is_left,
    std::ostringstream& debug_oss) {
  if (!preview_result_opt.has_value() ||
      !(preview_result_opt->on_route_preview_route_result.has_value())) {
    debug_oss << "No valid on-route preview result.\n";
    return false;
  }

  const lane_selection::PreviewRouteInfo& preview_route =
      preview_result_opt->on_route_preview_route_result.value()
          .previewed_route_info;
  const pb::LaneChangeMode effortless_lane_change_mode =
      is_left ? pb::LaneChangeMode::LEFT_LANE_CHANGE
              : pb::LaneChangeMode::RIGHT_LANE_CHANGE;

  // Init the next lane change mode as effortless mode to handle the case that
  // ego doesn't need to do lane change.
  pb::LaneChangeMode next_lane_change_mode = effortless_lane_change_mode;
  bool has_passed_preview_point = false;
  double dist_to_next_lane_change_end = 0.0;
  for (const pb::PlanningSegment& segment :
       preview_route.planning_segment_sequence.planning_segments()) {
    // If has lane follow segment, accumulate distance to the end_pos of the
    // next lane change segment.
    if (segment.has_lane_follow_segment()) {
      const pb::Segment& lf_segment =
          segment.lane_follow_segment().lf_segment();
      // Add segment length if it has passed the preview point.
      if (has_passed_preview_point) {
        dist_to_next_lane_change_end +=
            lf_segment.end_arclength() - lf_segment.start_arclength();
        continue;
      }
      // If not passed the preview point and the preview point is not in this
      // lane, continue.
      if (lf_segment.lane_id() != lane_point_to_pass.lane->id()) {
        continue;
      }

      // If the preview point is in this lane follow segment, update the
      // dist_to_next_lane_change_end, and set has_passed_preview_point to true.
      // Otherwise, keep them as before.
      const double dist_from_preview_point_to_seg_end =
          lf_segment.end_arclength() - lane_point_to_pass.arc_length;
      has_passed_preview_point = math::UpdateMax(
          dist_from_preview_point_to_seg_end, dist_to_next_lane_change_end);
      continue;
    }

    // Handle the lane change segment.
    if (segment.has_lane_change_segment()) {
      // If the preview point is passed, accumulate the distance from the end of
      // this lane change segments, record the lane change direction and break
      // out of loop.
      const pb::LaneChangeSegment& lc_segment = segment.lane_change_segment();
      if (has_passed_preview_point) {
        dist_to_next_lane_change_end +=
            GetDistanceFromPreviewPointToLaneSegmentsEnd(
                lc_segment.lc_source_segments(), lane_point_to_pass);
        next_lane_change_mode = lc_segment.lane_change_direction();
        break;
      } else {
        // If the preview point is not passed, check if the preview point is on
        // this lane change segment. If it's on, update flag and start to
        // accumulate the dist_to_next_lane_change_end.
        if (IsPreviewPointOnLaneSegments(lane_point_to_pass,
                                         lc_segment.lc_target_segments())) {
          has_passed_preview_point = true;
          dist_to_next_lane_change_end +=
              GetDistanceFromPreviewPointToLaneSegmentsEnd(
                  lc_segment.lc_target_segments(), lane_point_to_pass);
        } else if (IsPreviewPointOnLaneSegments(
                       lane_point_to_pass, lc_segment.lc_source_segments())) {
          // Directly break out of loop if the preview point is on lane change
          // source segments, and record the next lane change mode.
          has_passed_preview_point = true;
          dist_to_next_lane_change_end +=
              GetDistanceFromPreviewPointToLaneSegmentsEnd(
                  lc_segment.lc_source_segments(), lane_point_to_pass);
          next_lane_change_mode = lc_segment.lane_change_direction();
          break;
        }
      }
    }
  }

  debug_oss << "Has Passed Preview Point: " << has_passed_preview_point << "\n"
            << "Need LC Back: "
            << (next_lane_change_mode != effortless_lane_change_mode) << "\n"
            << "Dist to LC End: " << dist_to_next_lane_change_end << "\n";
  // Return true if the seach successfully passed the preview point and distance
  // to next lane change is greater than the pre-defined threshold.
  return has_passed_preview_point &&
         (next_lane_change_mode == effortless_lane_change_mode ||
          dist_to_next_lane_change_end >
              kMinimumEffortlessDistanceToNextLaneChangeInMeter);
}

// Returns segment sequence for redirecting regional path before junction, as
// well as whether it's lane change or not.
std::pair<pb::PlanningSegmentSequence, /*need_lane_change=*/bool>
CheckAndReturnSegmentSequenceBeforeRedirectingJunction(
    const LightRegionalPath& light_regional_path) {
  bool has_lane_change = false;
  pb::PlanningSegmentSequence planning_segment_sequence;

  const pnc_map::Lane* prev_lane = nullptr;
  for (const pnc_map::Lane* curr_lane : light_regional_path.path_lanes) {
    if (prev_lane == nullptr) {
      prev_lane = curr_lane;
      continue;
    }

    if (prev_lane->IsSuccessor(*curr_lane)) {
      planning_segment_sequence.add_planning_segments()->CopyFrom(
          lane_selection::ConstructLaneFollowPlanningSegment(
              *curr_lane, 0.0, curr_lane->length()));
    } else {
      has_lane_change = true;
      pb::PlanningSegment* planning_segment =
          planning_segment_sequence.add_planning_segments();
      pb::LaneChangeSegment* lane_change_segment =
          planning_segment->mutable_lane_change_segment();
      pb::Segment* source_segment =
          lane_change_segment->add_lc_source_segments();
      source_segment->set_lane_id(prev_lane->id());
      source_segment->set_start_arclength(0.0);
      source_segment->set_end_arclength(prev_lane->length());

      pb::Segment* target_segment =
          lane_change_segment->add_lc_target_segments();
      target_segment->set_lane_id(curr_lane->id());
      target_segment->set_start_arclength(0.0);
      target_segment->set_end_arclength(curr_lane->length());

      const pb::LaneChangeMode lane_change_direction =
          (prev_lane->right_lane() != nullptr &&
           prev_lane->right_lane()->id() == curr_lane->id()) ||
                  (prev_lane->adjacent_right_lane() != nullptr &&
                   prev_lane->adjacent_right_lane()->id() == curr_lane->id())
              ? pb::LaneChangeMode::RIGHT_LANE_CHANGE
              : pb::LaneChangeMode::LEFT_LANE_CHANGE;
      lane_change_segment->set_lane_change_direction(lane_change_direction);
    }

    if (curr_lane->IsInJunction()) {
      break;
    }

    prev_lane = curr_lane;
  }

  return {planning_segment_sequence, /*need_lane_change=*/has_lane_change};
}

// Returns true if the we should disable requesting lane-switch proposal because
// some pull-over related reasons.
bool ShouldDisableSwitchLaneSequenceForPullOver(
    const speed::TrajectoryInfo& trajectory_info, double dist_to_destination,
    bool is_left) {
  // Not forbid ego making proposal if pull over is not triggered.
  if (!trajectory_info.should_trigger_pull_over()) {
    return false;
  }

  // Once the pullover is triggered, disable requesting lane-switch proposal if:
  // - the lane-switch direction is right, or
  // - ego is not in the right-most vehicle lane, or
  // - pull over is prepared.
  if (!is_left || !trajectory_info.IsEgoInRightMostVehicleLane() ||
      trajectory_info.pull_over_info().status().execution_state() !=
          pb::PullOverExecutionState::NOT_TRIGGERED) {
    return true;
  }

  // Get the first encroaching path segment info.
  const pb::EgoCurrentLaneRelativeState::Enum ego_current_lane_relative_state =
      trajectory_info.ego_current_lane_relative_state();
  const speed::pb::PathOutOfLaneInfo& out_lane_info =
      is_left ? (ego_current_lane_relative_state ==
                         pb::EgoCurrentLaneRelativeState::FullyOnLeftOutside
                     ? trajectory_info.right_lane_encroachment_info()
                     : trajectory_info.left_lane_encroachment_info())
              : (ego_current_lane_relative_state ==
                         pb::EgoCurrentLaneRelativeState::FullyOnRightOutside
                     ? trajectory_info.left_lane_encroachment_info()
                     : trajectory_info.right_lane_encroachment_info());
  if (out_lane_info.out_of_lane_segments().empty()) {
    return true;
  }

  const double encroachment_distance =
      out_lane_info.out_of_lane_segments().cbegin()->end() -
      out_lane_info.out_of_lane_segments().cbegin()->start();
  // If ego can fully merge back before destination and only rides out of lane
  // sequence for a short distance, don't request for lane-switch proposal.
  if ((encroachment_distance + kMaxAllowableLongitudinalDistanceInMeter) <
          dist_to_destination &&
      encroachment_distance < kMaxAllowableLongitudinalDistanceInMeter) {
    return true;
  }

  return false;
}

// Returns all longitudinal ranges of lane sequence that are blocked by
// unmovable construction objects and physical boundaries. It means that ego
// cannot enter the lane sequence from current side in these range.
std::vector<math::Range1d> ComputeLaneSequenceLongitudinalBlockedRanges(
    const selection::TrajectoryMetaData& selected_trajectory,
    const LaneBlockageDetectorSpeedData& lane_blockage_speed_data,
    const math::ProximityQueryInfo& ls_ego_proximity) {
  const auto& lane_sequence_geometry =
      selected_trajectory.lane_sequence_geometry();
  const math::pb::Side ego_side = ls_ego_proximity.side;

  absl::flat_hash_set<int64_t> lane_sequence_lane_ids;
  for (const auto& lane : selected_trajectory.lane_sequence()) {
    lane_sequence_lane_ids.insert(lane->id());
  }

  // Step 1: Calculate longitudinal occupancy ranges of blockages.
  std::vector<AgentSnapshotInLaneParam> blockage_inlane_params;
  for (const auto& [lane_id, blockage] :
       lane_blockage_speed_data.lane_blockages()) {
    if (!lane_sequence_lane_ids.contains(lane_id)) {
      continue;
    }

    for (const auto& object_info : blockage.blocking_objects) {
      // Only consider traffic cone, barrier, construction zone and occ so far.
      if (object_info.type() != pb::kTrafficCone &&
          object_info.type() != pb::kConstructionZone &&
          object_info.type() != pb::kOccupancy) {
        continue;
      }

      const AgentSnapshotInLaneParam inlane_param =
          lane_selection::ComputeObjectStaticInLaneParam(
              lane_sequence_geometry.nominal_path,
              lane_sequence_geometry.left_lane_boundary,
              lane_sequence_geometry.right_lane_boundary,
              lane_sequence_geometry.lane_sequence_contour,
              object_info.contour());
      blockage_inlane_params.emplace_back(std::move(inlane_param));
    }
  }

  // Step 2: Simply group occupancy ranges.
  std::function<bool(const AgentSnapshotInLaneParam&,
                     const AgentSnapshotInLaneParam&)>
      group_rule = [](const AgentSnapshotInLaneParam& lhs,
                      const AgentSnapshotInLaneParam& rhs) -> bool {
    constexpr double kMaxGroupDistanceThresholdInMeter = 8.0;
    return std::fabs(rhs.full_body_end_arclength_m -
                     lhs.full_body_start_arclength_m) <
               kMaxGroupDistanceThresholdInMeter ||
           std::fabs(rhs.full_body_start_arclength_m -
                     lhs.full_body_end_arclength_m) <
               kMaxGroupDistanceThresholdInMeter;
  };
  const std::vector<std::vector<int>> group_indices =
      group_util::GroupBy(blockage_inlane_params, group_rule);
  std::vector<math::Range1d> grouped_lon_occupancy_ranges;
  for (const std::vector<int>& indices : group_indices) {
    math::Range1d lon_range{std::numeric_limits<double>::max(),
                            std::numeric_limits<double>::lowest()};
    bool is_ignorable = true;
    for (const int index : indices) {
      DCHECK_GT(blockage_inlane_params.size(), index);
      const auto& inlane_param = blockage_inlane_params.at(index);
      is_ignorable &= (inlane_param.center_line_side != ego_side &&
                       inlane_param.center_line_side != math::pb::kOn);
      math::UpdateMin(inlane_param.full_body_start_arclength_m,
                      lon_range.start_pos);
      math::UpdateMax(inlane_param.full_body_end_arclength_m,
                      lon_range.end_pos);
    }
    if (is_ignorable) {
      continue;
    }
    grouped_lon_occupancy_ranges.emplace_back(std::move(lon_range));
  }

  // Step 3: Add ranges of physical boundaries. Ego cannot return to the
  // original lane sequence by crossing physical boundaries.
  const auto& hard_boundary_curves =
      (ego_side == math::pb::kLeft)
          ? lane_sequence_geometry.left_hard_boundary_lines
          : lane_sequence_geometry.right_hard_boundary_lines;
  math::Range1d hard_boundary_occupancy_range{
      std::numeric_limits<double>::max(),
      std::numeric_limits<double>::lowest()};
  for (const auto& curve : hard_boundary_curves) {
    const math::ProximityQueryInfo start_proximity =
        lane_sequence_geometry.nominal_path.GetProximity(
            curve.points().front(),
            /*allow_extension_flag=*/math::pb::UseExtensionFlag::kForbid);
    const math::ProximityQueryInfo end_proximity =
        lane_sequence_geometry.nominal_path.GetProximity(
            curve.points().back(),
            /*allow_extension_flag=*/math::pb::UseExtensionFlag::kForbid);
    if (start_proximity.dist > ls_ego_proximity.dist ||
        end_proximity.dist > ls_ego_proximity.dist) {
      continue;
    }
    if (start_proximity.arc_length <= ls_ego_proximity.arc_length &&
        end_proximity.arc_length <= ls_ego_proximity.arc_length) {
      continue;
    }

    const double start_arc_length =
        std::min(start_proximity.arc_length, end_proximity.arc_length);
    // Note that ego should return to the lane sequence before passing the first
    // physical boundary, so we update the end_arc_length with the lane sequence
    // end here to promise that no available gap can be found after the first
    // physical boundary.
    const double end_arc_length =
        std::max({start_proximity.arc_length, end_proximity.arc_length,
                  lane_sequence_geometry.nominal_path.GetTotalArcLength()});
    math::UpdateMin(start_arc_length, hard_boundary_occupancy_range.start_pos);
    math::UpdateMax(end_arc_length, hard_boundary_occupancy_range.end_pos);
  }
  if (math::IsValidRange(hard_boundary_occupancy_range)) {
    grouped_lon_occupancy_ranges.push_back(hard_boundary_occupancy_range);
  }
  constexpr double kRangeEpsilonInMeter = 1.0;
  grouped_lon_occupancy_ranges.emplace_back(
      lane_sequence_geometry.nominal_path.GetTotalArcLength(),
      lane_sequence_geometry.nominal_path.GetTotalArcLength() +
          kRangeEpsilonInMeter);

  return math::MergeRange(grouped_lon_occupancy_ranges);
}

// Returns true if there is available longitudinal gap for ego to enter the lane
// sequence.
bool HasAvailableLongitudinalGapToEnter(
    const std::vector<math::Range1d>& lon_blocked_ranges,
    const math::ProximityQueryInfo& ls_ego_proximity) {
  double start_position = 0.0;
  for (const math::Range1d& occupancy_range : lon_blocked_ranges) {
    const double gap_start_pos = start_position;
    const double gap_end_pos = occupancy_range.start_pos;
    start_position = occupancy_range.end_pos;

    // Already pass this range.
    if (ls_ego_proximity.arc_length > gap_end_pos) {
      continue;
    }

    constexpr double kMinLongitudinalSpaceInMeter = 20.0;
    const double longitudinal_space =
        gap_end_pos - std::max(ls_ego_proximity.arc_length, gap_start_pos);
    if (longitudinal_space > kMinLongitudinalSpaceInMeter) {
      return true;
    }
  }
  return false;
}

// Gets primary bp collision level of the agent with max risk.
voy::perception::CollisionDetection::CollisionLevel GetPrimaryCollisionLevel(
    const selection::RiskEvalResult& risk_eval_result,
    double* primary_collision_prob, double* ttc_in_sec) {
  const selection::AgentRiskInfo& max_risk_agent =
      risk_eval_result.max_risk_agent;
  // Return NO_COLLISION_LEVEL if there is no constraints_eval_results.
  if (max_risk_agent.constraints_eval_results.empty()) {
    return voy::perception::CollisionDetection::NO_COLLISION_LEVEL;
  }

  // Get the the primary bp risk info.
  const selection::RiskConstraintEvalResult& primary_bp_risk =
      max_risk_agent.constraints_eval_results.front();
  constexpr double kInterestedTtcProbInFutureTimeInSec = 2.0;
  *primary_collision_prob = primary_bp_risk.ttc_cumulative_distribution(
      kInterestedTtcProbInFutureTimeInSec);
  *ttc_in_sec = primary_bp_risk.ttc_in_sec;
  return primary_bp_risk.severity_level;
}
}  // namespace

void LaneSequenceProposer::Propose(
    const selection::TrajectoryMetaData& selected_trajectory,
    const std::shared_ptr<const LaneSequenceResult>&
        waypoint_proposed_lane_sequence,
    const std::optional<LightRegionalPath>& redirecting_regional_path,
    const RobotStateSnapshot& plan_init_state,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    pb::DecoupledManeuverSeed* decoupled_maneuver_seed) {
  TRACE_EVENT_SCOPE(planner, LaneSequenceProposer_Propose);
  DCHECK_NE(decoupled_maneuver_seed, nullptr);

  std::ostringstream debug_oss;
  // Lane change sequence proposer needs previous proposal.
  if (FLAGS_planning_enable_lane_sequence_proposal_for_lane_change) {
    lane_change_sequence_proposer_.Propose(
        joint_pnc_map_service, plan_init_state, *decoupled_maneuver_seed,
        selected_trajectory);
  }
  decoupled_maneuver_seed->clear_lane_sequence_proposal();
  decoupled_maneuver_seed->clear_latch_current_lane();

  // Add lane follow proposal.
  pb::LaneSequenceProposal* proposal =
      decoupled_maneuver_seed->mutable_lane_sequence_proposal();
  // Add latch lane sequence proposal according to the selected trajectory.
  const bool added_lane_keep_latch_proposal = AddLaneKeepLatchProposal(
      selected_trajectory, waypoint_proposed_lane_sequence,
      proposal->mutable_lane_seq_latch_signal(), decoupled_maneuver_seed);

  // Add lane change proposal.
  bool added_lane_change_latch_proposal = false;
  if (FLAGS_planning_enable_lane_sequence_proposal_for_lane_change) {
    added_lane_change_latch_proposal = AddLaneChangeLatchProposal(
        selected_trajectory, joint_pnc_map_service,
        proposal->mutable_lane_change_latch_signal(), decoupled_maneuver_seed);
  }

  // Add redirecting regional path related proposal.
  if (redirecting_regional_path.has_value() &&
      (!added_lane_keep_latch_proposal && !added_lane_change_latch_proposal)) {
    const std::pair<pb::PlanningSegmentSequence, /*need_lane_change=*/bool>
        segment_sequence_before_junction =
            CheckAndReturnSegmentSequenceBeforeRedirectingJunction(
                redirecting_regional_path.value());

    if (!segment_sequence_before_junction.first.planning_segments().empty()) {
      pb::LaneSequenceLatchSignal* latch_signal =
          segment_sequence_before_junction.second
              ? proposal->mutable_lane_change_latch_signal()
              : proposal->mutable_lane_seq_latch_signal();
      PopulateLaneSequencePrefix(
          std::move(segment_sequence_before_junction.first),
          /*allow_to_extend=*/true, /*need_request_reroute=*/true,
          (/*source=*/segment_sequence_before_junction.second
               ? pb::LaneSequencePrefix::LC_IMMEDIATE_PULLOVER
               : pb::LaneSequencePrefix::LK_IMMEDIATE_PULLOVER),
          latch_signal->add_prefixes());
    }
  }

  // Request for new lane keep lane sequence.
  if (FLAGS_planning_enable_generate_neighbor_lane_sequence_proposal) {
    RequestForLaneKeepLaneSequence(selected_trajectory, plan_init_state,
                                   proposal->mutable_lane_seq_latch_signal(),
                                   debug_oss);
  }

  // LOG debug str.
  if (!debug_oss.str().empty()) {
    LOG(INFO) << kLogHeader << debug_oss.str();
  }
}

bool LaneSequenceProposer::AddLaneKeepLatchProposal(
    const selection::TrajectoryMetaData& selected_trajectory,
    const std::shared_ptr<const LaneSequenceResult>&
        waypoint_proposed_lane_sequence,
    pb::LaneSequenceLatchSignal* latch_proposal,
    pb::DecoupledManeuverSeed* decoupled_maneuver_seed) {
  if (!selected_trajectory.IsLaneKeep()) {
    return false;
  }

  // Populate latch proposal for Waypoint and early return if it successfully
  // add proposal.
  if (MaybePopulateLaneKeepProposalForWaypoint(selected_trajectory,
                                               waypoint_proposed_lane_sequence,
                                               latch_proposal)) {
    latch_proposal->set_is_exclusive(true);
    return true;
  }

  // Populate latch proposal for XLaneNudge and early return if it successfully
  // add proposal.
  if (MaybePopulateLaneKeepProposalForXLaneNudge(
          selected_trajectory, latch_proposal, decoupled_maneuver_seed)) {
    latch_proposal->set_is_exclusive(
        IsExclusiveLatchScenarioForXLaneNudge(selected_trajectory));
    return true;
  }

  // Explicitly set is_exclusive to false when all behaviors don't need to
  // exclusively latch proposal.
  latch_proposal->set_is_exclusive(false);
  return false;
}

bool LaneSequenceProposer::MaybePopulateLaneKeepProposalForWaypoint(
    const selection::TrajectoryMetaData& selected_trajectory,
    const std::shared_ptr<const LaneSequenceResult>&
        waypoint_proposed_lane_sequence,
    pb::LaneSequenceLatchSignal* lane_sequence_proposal) {
  if (!selected_trajectory.IsWaypointAssistTrajectory()) {
    return false;
  }

  // Always request to reroute.
  // TODO(Waypoint): polish this logic.
  const bool request_reroute = true;

  DCHECK_NE(lane_sequence_proposal, nullptr);
  pb::PlanningSegmentSequence segment_sequence;
  if (!waypoint_proposed_lane_sequence ||
      !GenerateWaypointAssistLatchedSegmentSequence(
          waypoint_proposed_lane_sequence, &segment_sequence)) {
    return false;
  }

  PopulateLaneSequencePrefix(std::move(segment_sequence),
                             /*allow_to_extend=*/true,
                             /*need_request_reroute=*/request_reroute,
                             /*source=*/pb::LaneSequencePrefix::LK_WAYPOINT,
                             lane_sequence_proposal->add_prefixes());
  return true;
}

bool LaneSequenceProposer::MaybePopulateLaneKeepProposalForXLaneNudge(
    const selection::TrajectoryMetaData& selected_trajectory,
    pb::LaneSequenceLatchSignal* lane_sequence_proposal,
    pb::DecoupledManeuverSeed* decoupled_maneuver_seed) {
  // TODO(tianxi): deprecate latch_current_lane and use lane sequence
  // proposal.
  if (!selected_trajectory.IsCrossLaneNudge()) {
    LatchCurrentLane(decoupled_maneuver_seed);
    return false;
  }

  DCHECK_NE(lane_sequence_proposal, nullptr);
  pb::PlanningSegmentSequence segment_sequence;
  const bool is_success = ClampXLaneNudgeLatchedSegmentSequence(
      *DCHECK_NOTNULL(selected_trajectory.trajectory_info), &segment_sequence);
  if (!is_success) {
    // TODO(tianxi): maybe use the entire planning segment sequence if it is
    // failed to clamp planning segment sequence. Currently the entire lane
    // follow planning segment segment may have lane change planning segments.
    // It's not expected now.
    LatchCurrentLane(decoupled_maneuver_seed);
    return false;
  }

  PopulateLaneSequencePrefix(std::move(segment_sequence),
                             /*allow_to_extend=*/true,
                             /*need_request_reroute=*/false,
                             /*source=*/pb::LaneSequencePrefix::LK_XLANE,
                             lane_sequence_proposal->add_prefixes());
  return true;
}

bool LaneSequenceProposer::AddLaneChangeLatchProposal(
    const selection::TrajectoryMetaData& selected_trajectory,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    pb::LaneSequenceLatchSignal* latch_proposal,
    pb::DecoupledManeuverSeed* decoupled_maneuver_seed) {
  decoupled_maneuver_seed->clear_lane_change_sequence_proposal_metadata();
  DCHECK(latch_proposal != nullptr);
  const std::optional<pb::LaneChangeSequenceProposalMetadata>&
      lc_sequence_proposal_metadata = lane_change_sequence_proposer_.metadata();
  if (!lc_sequence_proposal_metadata.has_value()) {
    return false;
  }

  const pb::PlanningSegmentSequence& prefix_segment_sequence =
      lc_sequence_proposal_metadata.value().prefix_segment_sequence();
  const bool is_none_type_proposal =
      lc_sequence_proposal_metadata.value().type() ==
      pb::LaneChangeSequenceProposalType::kLaneChangeSequenceProposalNone;
  const bool is_empty_prefix_segment_sequence =
      prefix_segment_sequence.planning_segments().empty();
  if (is_none_type_proposal || is_empty_prefix_segment_sequence) {
    return false;
  }
  const bool need_request_reroute =
      IsRerouteProposalTypeForLc(
          lc_sequence_proposal_metadata.value().type()) ||
      IsPrefixDivergedWithRegionalPath(
          selected_trajectory.current_lane(),
          lc_sequence_proposal_metadata.value().prefix_segment_sequence(),
          regional_map_, joint_pnc_map_service);
  const pb::LaneSequencePrefix::Source source_type =
      GetSourceTypeOfLcSeqProposalType(
          lc_sequence_proposal_metadata.value().type());
  PopulateLaneSequencePrefix(prefix_segment_sequence,
                             /*allow_to_extend=*/true, need_request_reroute,
                             source_type, latch_proposal->add_prefixes());
  latch_proposal->set_is_exclusive(false);
  decoupled_maneuver_seed->mutable_lane_change_sequence_proposal_metadata()
      ->CopyFrom(lc_sequence_proposal_metadata.value());
  return true;
}

void LaneSequenceProposer::RequestForLaneKeepLaneSequence(
    const selection::TrajectoryMetaData& selected_trajectory,
    const RobotStateSnapshot& plan_init_state,
    pb::LaneSequenceLatchSignal* lk_lane_sequence_proposal,
    std::ostringstream& debug_oss) {
  DCHECK_NE(lk_lane_sequence_proposal, nullptr);
  // Always try to request for lane keep lane sequence when ego is in some
  // abnormal behavior.
  MaybeRequestLaneKeepProposalnMergeBackFailureScenario(
      selected_trajectory, plan_init_state, lk_lane_sequence_proposal,
      debug_oss);

  // Once the lane keep lane sequence proposal is exclusively latched, we prefer
  // to respected current lane sequence proposal.
  if (lk_lane_sequence_proposal->is_exclusive()) {
    return;
  }

  // Only request for lane keep lane sequence during lane keep behavior. Note
  // that lane change abort is also a lane keep behavior. Generally, it
  // indicates that side lane sequence is not safe enough. So we prefer to avoid
  // switch lane keep sequence in this stage for now.
  if (!selected_trajectory.IsLaneKeep() ||
      selected_trajectory.trajectory_info->is_lane_change_in_abort() ||
      selected_trajectory.IsPullOutJumpOut() ||
      selected_trajectory.IsWaypointAssistTrajectory() ||
      selected_trajectory.IsStuckAvoidJumpOut()) {
    return;
  }

  // Currently, we propose new lane keep lane sequence only if ego is crossing
  // out current lane, or already fully out of the current lane.
  const pb::EgoCurrentLaneRelativeState::Enum current_lane_relative_state =
      selected_trajectory.trajectory_info->ego_current_lane_relative_state();
  const pnc_map::Lane* current_lane =
      DCHECK_NOTNULL(selected_trajectory.current_lane());
  switch (current_lane_relative_state) {
    case pb::EgoCurrentLaneRelativeState::FullyOnLeftOutside:
    case pb::EgoCurrentLaneRelativeState::CrossOutFromLeftMarking:
      MaybeGenerateLaneKeepProposal(selected_trajectory, plan_init_state,
                                    current_lane->adjacent_left_lane(),
                                    /*is_left=*/true, lk_lane_sequence_proposal,
                                    debug_oss);
      break;
    case pb::EgoCurrentLaneRelativeState::FullyOnRightOutside:
    case pb::EgoCurrentLaneRelativeState::CrossOutFromRightMarking:
      MaybeGenerateLaneKeepProposal(selected_trajectory, plan_init_state,
                                    current_lane->adjacent_right_lane(),
                                    /*is_left=*/false,
                                    lk_lane_sequence_proposal, debug_oss);
      break;
    case pb::EgoCurrentLaneRelativeState::CrossInFromLeftMarking:
    case pb::EgoCurrentLaneRelativeState::CrossInFromRightMarking:
    case pb::EgoCurrentLaneRelativeState::FullyOnTheLane:
      return;
    default:
      DCHECK(false) << "relative state should not be '"
                    << pb::EgoCurrentLaneRelativeState::Enum_Name(
                           current_lane_relative_state)
                    << "' here.";
      return;
  }
}

void LaneSequenceProposer::
    MaybeRequestLaneKeepProposalnMergeBackFailureScenario(
        const selection::TrajectoryMetaData& selected_trajectory,
        const RobotStateSnapshot& plan_init_state,
        pb::LaneSequenceLatchSignal* lk_lane_sequence_proposal,
        std::ostringstream& debug_oss) {
  if (selected_trajectory.trajectory.poses().empty()) {
    return;
  }

  const pb::EgoCurrentLaneRelativeState::Enum current_lane_relative_state =
      selected_trajectory.trajectory_info->ego_current_lane_relative_state();
  const bool is_ls_blocked =
      IsLaneSequenceStrictlyBlocked(selected_trajectory, plan_init_state);
  // Currently, only propose physical current lane proposal when xlane nudge
  // abnormally abort or the selected lane sequence is completely blocked.
  if (!selected_trajectory.trajectory_info
           ->is_xlane_nudge_aborted_abnormally() &&
      !is_ls_blocked) {
    return;
  }

  DCHECK(current_lane_relative_state ==
             pb::EgoCurrentLaneRelativeState::FullyOnLeftOutside ||
         current_lane_relative_state ==
             pb::EgoCurrentLaneRelativeState::FullyOnRightOutside);
  const bool is_on_current_lane_left_side =
      current_lane_relative_state ==
      pb::EgoCurrentLaneRelativeState::FullyOnLeftOutside;
  const std::vector<const pnc_map::Lane*> aligned_current_lanes =
      GetDrivingAlignedCurrentLanes(
          selected_trajectory.trajectory,
          *DCHECK_NOTNULL(selected_trajectory.current_lane()), plan_init_state,
          is_on_current_lane_left_side, debug_oss);
  for (const auto* lane : aligned_current_lanes) {
    pb::PlanningSegmentSequence segment_sequence =
        GenerateSingleLaneSequenceSegment(*lane);
    rt_event::PostRtEvent<
        rt_event::planner::RequestLKProposalForXLaneNudgeAbort>();
    PopulateLaneSequencePrefix(segment_sequence, /*allow_to_extend=*/true,
                               /*need_request_reroute=*/true,
                               pb::LaneSequencePrefix::LK_LANE_SEQUENCE_BLOKCED,
                               lk_lane_sequence_proposal->add_prefixes());
  }
}

bool LaneSequenceProposer::IsLaneSequenceStrictlyBlocked(
    const selection::TrajectoryMetaData& selected_trajectory,
    const RobotStateSnapshot& plan_init_state) {
  const pb::EgoCurrentLaneRelativeState::Enum ego_relative_side =
      selected_trajectory.trajectory_info->ego_current_lane_relative_state();
  if (ego_relative_side !=
          pb::EgoCurrentLaneRelativeState::FullyOnLeftOutside &&
      ego_relative_side !=
          pb::EgoCurrentLaneRelativeState::FullyOnRightOutside) {
    return false;
  }

  const auto& lane_sequence_geometry =
      selected_trajectory.lane_sequence_geometry();
  const auto& ego_position = plan_init_state.front_bumper_position();
  const math::ProximityQueryInfo ls_ego_proximity =
      lane_sequence_geometry.nominal_path.GetProximity(
          ego_position,
          /*allow_extension_flag=*/math::pb::UseExtensionFlag::kForbid);

  const std::vector<math::Range1d> merged_lon_occupancy_ranges =
      ComputeLaneSequenceLongitudinalBlockedRanges(
          selected_trajectory, lane_blockage_speed_data_, ls_ego_proximity);

  return !HasAvailableLongitudinalGapToEnter(merged_lon_occupancy_ranges,
                                             ls_ego_proximity);
}

// Gets current lanes aligns with the ego's driving intention.
std::vector<const pnc_map::Lane*>
LaneSequenceProposer::GetDrivingAlignedCurrentLanes(
    const pb::Trajectory& selected_trajectory,
    const pnc_map::Lane& logical_current_lane,
    const RobotStateSnapshot& plan_init_state,
    bool is_on_current_lane_left_side, std::ostringstream& debug_oss) const {
  const int64_t logical_current_section_id =
      logical_current_lane.section()->id();
  std::vector<const pnc_map::Lane*> result;
  result.reserve(current_lanes_.size());
  for (const pnc_map::Lane* lane : current_lanes_) {
    // Skip the lane which is not aligned with the ego's trajectory.
    if (!IsEgoStateAlignedWithLaneSwitchIntention(
            selected_trajectory, plan_init_state, *lane,
            is_on_current_lane_left_side, debug_oss)) {
      continue;
    }
    // Skip the lane which is not in the same section with ego's logical current
    // lane in the junction scenario.
    if (logical_current_lane.IsInJunction() &&
        logical_current_section_id != lane->section()->id()) {
      continue;
    }
    result.push_back(lane);
  }
  return result;
}

void LaneSequenceProposer::MaybeGenerateLaneKeepProposal(
    const selection::TrajectoryMetaData& selected_trajectory,
    const RobotStateSnapshot& plan_init_state, const pnc_map::Lane* target_lane,
    bool is_left, pb::LaneSequenceLatchSignal* lk_lane_sequence_proposal,
    std::ostringstream& debug_oss) {
  // Nullptr target_lane means there are no valid neighbor lane on othe side.
  // Directly return if the target_lane is invalid, or the selected trajectory
  // is empty.
  if (target_lane == nullptr || !target_lane->IsRobotDrivable() ||
      selected_trajectory.trajectory.poses().empty()) {
    return;
  }

  // Check if ego is close to pull over destination and should disable
  // requesting lane-switch proposal for pullover preparation reasons.
  if (ShouldDisableSwitchLaneSequenceForPullOver(
          *selected_trajectory.trajectory_info, dist_to_destination(),
          is_left)) {
    debug_oss << "Disable for PullOver.\n";
    return;
  }

  // To avoid strange S-traj behavior, we prefer to forbid requesting for the
  // lane-switch proposal if ego has shown merging back intention.
  if (!IsEgoStateAlignedWithLaneSwitchIntention(selected_trajectory.trajectory,
                                                plan_init_state, *target_lane,
                                                is_left, debug_oss)) {
    return;
  }

  // If current selected trajectory is not in the necessary scenario ego should
  // request for new lane keep sequence, return.
  if (!IsNecessaryToRequestForNewLaneKeepSequence(
          selected_trajectory, *target_lane, is_left, debug_oss)) {
    return;
  }

  // Populate new lane sequence proposal.
  pb::PlanningSegmentSequence segment_sequence =
      GenerateSingleLaneSequenceSegment(*target_lane);
  rt_event::PostRtEvent<rt_event::planner::RequestLKLaneSequenceProposal>();
  PopulateLaneSequencePrefix(segment_sequence, /*allow_to_extend=*/true,
                             /*need_request_reroute=*/false,
                             pb::LaneSequencePrefix::LK_LANE_SWITCH,
                             lk_lane_sequence_proposal->add_prefixes());
}

bool LaneSequenceProposer::IsNecessaryToRequestForNewLaneKeepSequence(
    const selection::TrajectoryMetaData& selected_trajectory,
    const pnc_map::Lane& new_lane, bool is_left,
    std::ostringstream& debug_oss) const {
  // Get the preview lane point based on the path shape, and check if the
  // preview point is still on-route.
  const std::optional<lane_selection::PreviewLanePoint> preview_point =
      GetPreviewLanePoint(selected_trajectory, dist_to_destination(), is_left,
                          debug_oss);
  const bool is_preview_point_on_route = IsPreviewPointStillOnRoute(
      selected_trajectory.trajectory_info->pull_over_info(), preview_point,
      new_lane, is_left, debug_oss);
  DCHECK_NE(selected_trajectory.path_option.geometric_constraint
                .semantic_context_result,
            nullptr);
  const path::FrameAnalyzerResult& frame_context =
      is_left ? selected_trajectory.path_option.geometric_constraint
                    .semantic_context_result
                    ->frame_analyzer_results[pb::FrameAnalysis::LEFT_LANE]
              : selected_trajectory.path_option.geometric_constraint
                    .semantic_context_result
                    ->frame_analyzer_results[pb::FrameAnalysis::RIGHT_LANE];
  const bool is_side_safe = frame_context.hazardous_object_ids.empty();

  double primary_collision_prob = 0.0, ttc_in_sec = 0.0;
  const auto severity_level =
      GetPrimaryCollisionLevel(selected_trajectory.risk_eval_result,
                               &primary_collision_prob, &ttc_in_sec);
  debug_oss << "Request LS side: " << is_left << "\n"
            << "Is side safe: " << is_side_safe << "\n"
            << "Primary Collision Prob in 2s: " << primary_collision_prob
            << "\n"
            << "TTC in sec: " << ttc_in_sec << "\n"
            << "Severity Level: "
            << voy::perception::CollisionDetection::CollisionLevel_Name(
                   severity_level)
            << "\n";
  // If side lane sequence is not safe, do not request for a new lane keep
  // sequence.
  if (!is_side_safe) {
    return false;
  }

  // Check if ego is in high-risky collision scenario. If the collision risk is
  // higher than the threshold, return true to request for a new lane sequence
  // even if ego might reroute.
  const double collision_risk = selected_trajectory.collision_risk;
  if (collision_risk > kMinimumCollisionRiskForEmergency) {
    return collision_risk < kExtremelyHighRiskyCollisionThreshold
               ? is_preview_point_on_route
               : true;
  }

  // Check if the ego will greatly encroach the neighbor lane, such as:
  // - encroached out for larger lateral distance. or
  // - will encroach for a long time and distance.
  if (WillEgoHardEncroachNeighborLane(selected_trajectory, is_left,
                                      /*look_ahead_time_duration=*/
                                      kDefaultLookAheadPlanningDurationInMSec,
                                      debug_oss)) {
    return is_preview_point_on_route;
  }

  debug_oss << "Have not greatly encroached neighbor lane.\n";
  return false;
}

bool LaneSequenceProposer::IsPreviewPointStillOnRoute(
    const pull_over::PullOverInfo& pull_over_info,
    const std::optional<lane_selection::PreviewLanePoint>&
        lane_point_to_pass_opt,
    const pnc_map::Lane& base_lane, bool is_left,
    std::ostringstream& debug_oss) const {
  if (!lane_point_to_pass_opt.has_value()) {
    debug_oss << "No Valid Preview Point.\n";
    return false;
  }

  // Return false if the preview point is not reachable.
  if (!CanReachTargetLane(
          base_lane, *DCHECK_NOTNULL(lane_point_to_pass_opt.value().lane),
          /*max_search_distance=*/kMaxDistanceToCheckLaneConnectiveInMeter,
          /*enable_section_level_connection=*/true)) {
    debug_oss << "Preview Point is not reachable.\n";
    return false;
  }

  // Return true if ego is in pull over request by planning.
  if (pull_over_info.immediate_pull_over_info().is_triggered_by_planner()) {
    debug_oss << "Ignore PreviewResult during pullover by planner.\n";
    return true;
  }

  // Returns true if the neighbor lane is still on-route.
  const std::optional<lane_selection::PreviewRouteResult> preview_result_opt =
      lane_sequence_candidates_.GetPreviewRouteResult(
          pnc_map_service_, regional_map_, global_route_solution_,
          lane_point_to_pass_opt.value(),
          /*compared_sequence_type=*/
          lane_selection::PreviewRequestOptimalSequenceType::kPreviewRouteOnly,
          lane_selection::PreviewRouteSearchStrategy::kDefaultByMinCost,
          /*debug=*/nullptr);

  return HasEffortlessOnRoutePreviewResult(
      preview_result_opt, lane_point_to_pass_opt.value(), is_left, debug_oss);
}

void LaneSequenceProposer::PopulateLaneSequencePrefix(
    const pb::PlanningSegmentSequence& segment_sequence, bool allow_to_extend,
    bool need_request_reroute, pb::LaneSequencePrefix::Source source,
    pb::LaneSequencePrefix* prefix) {
  DCHECK_NE(prefix, nullptr);
  prefix->set_id(GenerateId());
  prefix->mutable_planning_segment_sequence()->CopyFrom(segment_sequence);
  // In some cases, entire lane sequence might be blocked by parked cars. To
  // make sure the ego could nudge all those blockages and won't be blocked by
  // the end_of_lane_sequence, we prefer to allow the planning segment to be
  // extended as the ego going ahead.
  prefix->set_allow_to_extend(allow_to_extend);
  // Currently, this field is set to false by default.
  prefix->set_request_reroute(need_request_reroute);
  prefix->set_source(source);
}
}  // namespace planner
