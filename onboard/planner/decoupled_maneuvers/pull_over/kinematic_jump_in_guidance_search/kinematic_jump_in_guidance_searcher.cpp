#include "planner/decoupled_maneuvers/pull_over/kinematic_jump_in_guidance_search/kinematic_jump_in_guidance_searcher.h"

#include <algorithm>
#include <limits>
#include <optional>
#include <tuple>
#include <unordered_set>
#include <utility>
#include <vector>

#include <tbb/parallel_for.h>

#include "base/optional_value_accessor.h"
#include "base/wall_clock_elapsed_timer.h"
#include "geometry/algorithms/equals.h"
#include "geometry/model/frenet_axis_aligned_box.h"
#include "geometry/model/oriented_box.h"
#include "geometry/model/point_2d.h"
#include "geometry/model/polyline_curve.h"
#include "math/constants.h"
#include "math/curve2d.h"
#include "math/curve2d_util.h"
#include "math/interpolation.h"
#include "math/math_util.h"
#include "math/range.h"
#include "math/unit_conversion.h"
#include "planner/behavior/util/lane_sequence_geometry/lane_sequence_geometry.h"
#include "planner/behavior/util/pull_over/pull_over_utility.h"
#include "planner/decoupled_maneuvers/pull_over/destination_initial_decision.h"
#include "planner/decoupled_maneuvers/pull_over/kinematic_jump_in_guidance_search/jump_in_curve_costing_engine.h"
#include "planner/decoupled_maneuvers/pull_over/kinematic_jump_in_guidance_search/jump_in_motion_primitive.h"
#include "planner/decoupled_maneuvers/pull_over/pull_over_utils.h"
#include "planner/decoupled_maneuvers/pull_over/target_line_feasible_range.h"
#include "planner/decoupled_maneuvers/pull_over/target_line_snapshot_info.h"
#include "planner/decoupled_maneuvers/pull_over/target_line_traffic_info.h"
#include "planner/path/path_solver/nominal_path_searcher/state_lattice_search/speed_bvp_solver/constant_jerk_speed_bvp_solver.h"
#include "planner/utility/lattice_planner/bvp_solver.h"
#include "planner/utility/lattice_planner/cubic_polynomial_spiral.h"
#include "planner/utility/lattice_planner/cubic_polynomial_spiral_state.h"
#include "planner/utility/lattice_planner/lattice_planner_util.h"
#include "planner/utility/object_id/typed_object_id.h"
#include "planner/utility/unstuck/unstuck_util.h"
#include "planner/world_model/pickup_dropoff_zone/pickup_dropoff_zone_info.h"
#include "planner/world_model/planner_object/planner_object.h"
#include "planner/world_model/snapshot/robot_state.h"
#include "planner/world_model/snapshot/robot_state_snapshot.h"
#include "trace/trace.h"
#include "vehicle_model/motion_model_with_shape.h"
#include "voy_trace/trace_planner.h"

namespace planner {

namespace pull_over {

namespace {

// The generated jump in curves should not have curvature or lateral
// acceleration that exceed the thresholds below.
constexpr double kMaxAllowedJumpInCurveAbsoluteCurvature = 0.234;
// TODO(liwen): Use a more reasonable lateral acceleration thresold.
constexpr double kMaxAllowedAbsoluteLateralAccelerationMpss = 3.0;

// The comfort braking deceleration for decelerating for (routing/pull over)
// destination constraints.
constexpr double kComfortableBrakingAccelForDestInMpss = -1.0;

// The value is used to check if a sampling step is near zero, or make sure that
// the step value used in for loop is not zero.
constexpr double kEpsilonForSamplingStep = 1e-3;

// Returns jump in start speed estimated by assumption that ego jump in along
// the spiral , constantly and comfortably decelerate to exactly stop at the end
// state.
double EstimateJumpInStartSpeed(const StateLatticeSpiral& spiral) {
  // NOTE: As the jump in curve is at least 5m (see `IsStartEndStateValid`), the
  // jump in start speed is at least 3.16 m/s (11.38 km/h).
  return std::sqrt(2.0 * spiral.spiral_parameters().final_arc_length *
                   std::abs(kComfortableBrakingAccelForDestInMpss));
}

// Returns the spiral state that created with specific longitudinal / lateral
// offset from a origin point along a reference line.
SpiralState GenerateSpiralState(
    const math::geometry::Point2d& origin_point,
    const math::geometry::PolylineCurve2d& reference_line,
    const math::Curve2d& smoothed_reference_line, double lon_pos,
    double lat_pos) {
  // Generate spiral state.
  const math::ProximityQueryInfo proximity = reference_line.GetProximity(
      origin_point, math::pb::UseExtensionFlag::kAllow);
  const double sampled_arc_length = proximity.arc_length + lon_pos;
  const math::geometry::Point2d sampled_point =
      reference_line.GetPointOnArclengthAndLateralOffset(sampled_arc_length,
                                                         lat_pos);
  const math::ProximityQueryInfo smoothed_proximity =
      smoothed_reference_line.GetProximity(sampled_point,
                                           math::pb::UseExtensionFlag::kAllow);
  return SpiralState(
      sampled_point.x(), sampled_point.y(),
      smoothed_reference_line.GetInterpTheta(smoothed_proximity.arc_length),
      smoothed_reference_line.GetInterpCurvature(
          smoothed_proximity.arc_length));
}

// Returns true if both start and end state are valid with some pre-checks.
bool IsStartEndStateValid(
    const RobotStateSnapshot& robot_snapshot,
    const math::geometry::PolylineCurve2d& lane_seq_centerline,
    const math::geometry::Point2d& pull_over_dest,
    const JumpInMotionPrimitive::State& start_state,
    const JumpInMotionPrimitive::State& end_state) {
  // Check if start state behind ego rear axis with a buffer, if so, we do not
  // sample the start state. Note that this logic is to align the upstream
  // feature, where if ego has been passed the jump in start point, they will
  // generate jump in curve starting at ego position.
  const double ego_ra_arc_length =
      lane_seq_centerline
          .GetProximity(robot_snapshot.rear_axle_position(),
                        math::pb::UseExtensionFlag::kForbid)
          .arc_length;

  const double start_arc_length =
      lane_seq_centerline
          .GetProximity(start_state.GetPoint(),
                        math::pb::UseExtensionFlag::kForbid)
          .arc_length;
  static constexpr double kMaxAllowedDistanceJumpInStartPtBehindEgoInMeters =
      5.0;
  //  Here we allow jump in start point to be generated earlier and the lateral
  //  pull over motion can be more assertive.
  if (start_arc_length <
      ego_ra_arc_length - kMaxAllowedDistanceJumpInStartPtBehindEgoInMeters) {
    return false;
  }

  // Check if end state is too far from destination. It is not expected that
  // jump in end with a place far from destination and ego drive along right
  // lane (especially when pull over to non-vehicle lane) too long. So we ignore
  // the sampled end states that too far from pull over destination.
  const double dest_arc_length =
      lane_seq_centerline
          .GetProximity(pull_over_dest, math::pb::UseExtensionFlag::kForbid)
          .arc_length;
  const double end_arc_length =
      lane_seq_centerline
          .GetProximity(end_state.GetPoint(),
                        math::pb::UseExtensionFlag::kForbid)
          .arc_length;

  static constexpr double kMaxAllowedJumpInEndToDestDistanceInMeters = 30.0;
  if (end_arc_length <
      dest_arc_length - kMaxAllowedJumpInEndToDestDistanceInMeters) {
    return false;
  }

  // Check longitudinal displacement. The jump in curve should not be too short
  // longitudinally. This threshold is also roughly aligned with upstream limit.
  static constexpr double kMinRequiredLongitudinalRangeForJumpInCurveInMeters =
      5.0;

  return end_arc_length - start_arc_length >=
         kMinRequiredLongitudinalRangeForJumpInCurveInMeters;
}

// Returns the pointer to the matched feasible range where the input end state
// of jump in curve lies in. The pointer would be nullptr if there's no matched
// feasible range found.
const TargetLineFeasibleRange* FindTargetLineFeasibleRange(
    const JumpInMotionPrimitive::State& end_state,
    const math::geometry::PolylineCurve2d& target_line,
    const std::vector<TargetLineFeasibleRange>& feasible_ranges) {
  // Check collsion at end.
  const double target_line_based_end_arc_length =
      target_line
          .GetProximity(end_state.GetPoint(),
                        math::pb::UseExtensionFlag::kAllow)
          .arc_length;
  for (const TargetLineFeasibleRange& feasible_range : feasible_ranges) {
    if (feasible_range.Contains(target_line_based_end_arc_length)) {
      return &feasible_range;
    }
  }
  return nullptr;
}

// Returns true if the end state not lies in the no-parking or road exit zones.
bool IsEndStateParkable(const JumpInMotionPrimitive::State& end_state,
                        const PickupDropoffZoneInfo& pdz_info) {
  if (!pdz_info.extended_nominal_path.has_value()) {
    // When the pdz info is non-virtual (maybe in some simulation scenarios
    // only), we skip the check.
    return true;
  }

  // Compute end point arc length based on the PDZ extended nominal path.
  const math::geometry::PolylineCurve2d& pdz_nominal_path =
      pdz_info.extended_nominal_path.value();
  const double end_pt_arc_length_in_pdz =
      pdz_nominal_path.GetProximity(end_state.GetPoint(), math::pb::kAllow)
          .arc_length;

  // Check if the end point is in the no parking range or exit zone range
  // longitudinally.
  const auto is_end_point_longitudinally_in_range =
      [end_pt_arc_length_in_pdz](const std::vector<math::Range1d>& ranges) {
        return std::any_of(
            ranges.begin(), ranges.end(),
            [end_pt_arc_length_in_pdz](const math::Range1d& range) {
              return math::IsInRange(end_pt_arc_length_in_pdz, range.start_pos,
                                     range.end_pos);
            });
      };

  return !is_end_point_longitudinally_in_range(pdz_info.no_parking_ranges) &&
         !is_end_point_longitudinally_in_range(pdz_info.exit_zone_ranges);
}

// Returns speed state at queried arc length calculated based on the constant
// acceleration assumption.
speed::State CalculateSpeedStateAtQueriedArcLength(double init_speed,
                                                   double deceleration,
                                                   double arc_length) {
  const double speed_at_arc_length = std::sqrt(
      std::max(math::Sqr(init_speed) + 2.0 * deceleration * arc_length, 0.0));
  const double relative_time_at_arc_length =
      (speed_at_arc_length - init_speed) / deceleration;
  return speed::State(relative_time_at_arc_length, arc_length,
                      speed_at_arc_length, deceleration, /*j_in=*/0.0);
}

// Returns the uniformly sampled spiral states, the returned vector may be empty
// if there's any sampled state failed in validation.
std::vector<JumpInMotionPrimitive::State> GenerateSpiralSampledStates(
    const StateLatticeSpiral& spiral,
    const JumpInMotionPrimitive::State& start_state,
    const JumpInMotionPrimitive::State& end_state) {
  // Calculate the lateral accelerations along the lattice spiral, and early
  // return for max curvature / lateral acceleration exceeded.
  std::vector<JumpInMotionPrimitive::State> sampled_states;
  sampled_states.reserve(
      static_cast<size_t>(spiral.spiral_parameters().final_arc_length /
                          kJumpInCurveSamplingResolutionInMeters) +
      1);

  const double jump_in_start_speed = start_state.speed_state.v;
  for (double arc_length = 0.0;
       arc_length <= spiral.spiral_parameters().final_arc_length;
       arc_length += kJumpInCurveSamplingResolutionInMeters) {
    const SpiralState sampled_state = spiral.GetSampledState(arc_length);
    if (sampled_state.curvature_ > kMaxAllowedJumpInCurveAbsoluteCurvature) {
      // Curvature exceed max limit, early return.
      return {};
    }
    speed::State speed_state = CalculateSpeedStateAtQueriedArcLength(
        jump_in_start_speed, kComfortableBrakingAccelForDestInMpss, arc_length);
    const double lat_accel =
        sampled_state.curvature_ * math::Sqr(speed_state.v);
    if (std::abs(lat_accel) > kMaxAllowedAbsoluteLateralAccelerationMpss) {
      return {};
    }
    SpiralState global_state = planner::TransformLocalStateToGlobal(
        start_state.spiral_state, sampled_state);

    // Before appending the end state, avoid to append the possibly duplicated
    // last sampled state.
    static constexpr double kDistThresholdToRemoveTheLastSampledStateInMeters =
        0.1;
    if (arc_length > spiral.spiral_parameters().final_arc_length -
                         kJumpInCurveSamplingResolutionInMeters &&
        math::geometry::TolerantEquals(
            math::geometry::Point2d(global_state.x_pos_, global_state.y_pos_),
            end_state.GetPoint(),
            /*max_threshold=*/
            kDistThresholdToRemoveTheLastSampledStateInMeters)) {
      break;
    }
    sampled_states.emplace_back(std::move(global_state), std::move(speed_state),
                                lat_accel);
  }

  // Snap the last sample state to the jump in end state to make the sampled
  // states more accurate.
  const SpiralState& end_spiral_state = end_state.spiral_state;
  speed::State final_speed_state = CalculateSpeedStateAtQueriedArcLength(
      jump_in_start_speed, kComfortableBrakingAccelForDestInMpss,
      spiral.final_arc_length());
  sampled_states.emplace_back(SpiralState(end_spiral_state),
                              std::move(final_speed_state),
                              /*lat_accel_in=*/end_spiral_state.curvature_ *
                                  math::Sqr(final_speed_state.v));

  return sampled_states;
}

// Projects the input xy curve to sl coordinate according to input nominal path.
void ProjectXYToSL(const math::geometry::PolylineCurve2d& nominal_path,
                   math::pb::Curve2d* curve) {
  DCHECK_GT(curve->segments_size(), 0);
  for (math::pb::Point2d& point :
       *curve->mutable_segments(0)->mutable_points()) {
    const math::ProximityQueryInfo proximity = nominal_path.GetProximity(
        math::geometry::Point2d(point.x(), point.y()), math::pb::kAllow);
    point.set_x(proximity.arc_length);
    point.set_y(proximity.signed_dist);
  }
}

// Returns the expected dry steering angle estimated based on current ego pose
// and possible front objects.
double ComputeDrySteeringAngleToUnstuck(
    const RobotStateSnapshot& robot_snapshot,
    const JumpInMotionPrimitive::State& end_state,
    const TargetLineFeasibleRange& feasible_range) {
  const std::vector<const TargetLineSnapshotInfo*>& leading_snapshots =
      feasible_range.leading_snapshots();
  if (leading_snapshots.empty()) {
    return 0.0;
  }
  voy::Pose end_ego_pose;
  end_ego_pose.set_x(end_state.spiral_state.x_pos_);
  end_ego_pose.set_y(end_state.spiral_state.y_pos_);
  end_ego_pose.set_yaw(end_state.spiral_state.heading_);
  // TODO(liwen): As the leading snapshots are sorted with ascending distance to
  // ego, we just use the closest snapshot for now, which could be improved
  // later.
  const math::geometry::OrientedBox2d end_ego_bounding_box =
      GetEgoBoundingBoxFromRearAxlePose(
          end_ego_pose,
          robot_snapshot.car_model_with_shape().shape_measurement());
  const std::optional<double> expected_dry_steering_angle =
      GetUnstuckTargetSteeringAngle(end_ego_pose, end_ego_bounding_box,
                                    robot_snapshot.car_model_with_shape(),
                                    leading_snapshots.front()->contour(),
                                    /*nudge_from_left=*/true);

  if (expected_dry_steering_angle.has_value()) {
    return expected_dry_steering_angle.value();
  }

  // If could not pull out by dry steering, we decide to mostly penalize
  // the node rather than exclude it, as the dry steering angle estimation
  // is not that accurate.
  return robot_snapshot.car_model_with_shape()
      .param()
      .limit()
      .steering_limit()
      .max_val();
}

// Gets the pull over target pose.
// NOTE: this funciton is mostly aligned with upstream implementation, the only
// difference is use target line rather than reference path to get the target
// pose, so projection method is not aligned.
math::Pose2d GetPullOverTargetPoseByTargetLine(
    const math::geometry::PolylineCurve2d& target_line,
    const vehicle_model::pb::AxleRectangularMeasurement& shape_measurement,
    const math::geometry::Point2d& pull_over_dest) {
  const double rear_axle_to_front_bumper =
      shape_measurement.length() - shape_measurement.rear_bumper_to_rear_axle();
  // Ego is expected to stop at position where its front bumper center is
  // aligned with pull over destination.
  const double ego_ra_arc_length =
      target_line.GetProximity(pull_over_dest, math::pb::kAllow).arc_length -
      rear_axle_to_front_bumper;
  const math::geometry::Point2d target_point =
      target_line.GetInterp(ego_ra_arc_length, math::pb::kAllow);
  return math::Pose2d(target_point.x(), target_point.y(),
                      target_line.GetInterpTheta(ego_ra_arc_length));
}

// Returns the reference curve that stitchced from lane keep center line, jump
// in curve and target line, extended to pull over dest if needed.
math::geometry::PolylineCurve2d StitchReferenceCurve(
    const math::geometry::PolylineCurve2d& source_line,
    const JumpInMotionPrimitive& jump_in_primitive,
    const math::geometry::PolylineCurve2d& target_line,
    const math::geometry::Point2d& pull_over_dest) {
  std::vector<math::geometry::Point2d> concatenated_points;
  concatenated_points.reserve(source_line.size() + target_line.size() +
                              jump_in_primitive.sampled_states.size());

  // Append pull over lane sequence centerline.
  DCHECK(!jump_in_primitive.sampled_states.empty());
  // NOTE: Must use jump in curve first / last point rather than jump in start
  // / end point to get start / end arc length, in case the jump in primitive
  // is generated from upstream geometry, where the smoothed jump in curve
  // range is not equal to the range [jump_in_start_pt , jump_in_end_pt].
  const double jump_in_start_arc_length =
      source_line
          .GetProximity(jump_in_primitive.sampled_states.front().GetPoint(),
                        math::pb::UseExtensionFlag::kForbid)
          .arc_length;
  if (jump_in_start_arc_length > 0.0) {
    const std::vector<math::geometry::Point2d> lane_keep_points =
        source_line.GetSkeletonInRange(0.0, jump_in_start_arc_length);
    DCHECK(!lane_keep_points.empty());
    if (!lane_keep_points.empty()) {
      concatenated_points.insert(concatenated_points.end(),
                                 lane_keep_points.begin(),
                                 std::prev(lane_keep_points.end()));
    }
  }

  // Append refined jump in path.
  for (const JumpInMotionPrimitive::State& state :
       jump_in_primitive.sampled_states) {
    concatenated_points.emplace_back(state.GetPoint());
  }

  // Append target line path.
  const math::ProximityQueryInfo& jump_in_end_proximity =
      target_line.GetProximity(concatenated_points.back(),
                               math::pb::UseExtensionFlag::kForbid);

  if (jump_in_end_proximity.arc_length < target_line.GetTotalArcLength()) {
    const std::vector<math::geometry::Point2d> target_line_points =
        target_line.GetSkeletonInRange(jump_in_end_proximity.arc_length,
                                       target_line.GetTotalArcLength());
    if (!target_line_points.empty()) {
      concatenated_points.insert(concatenated_points.end(),
                                 std::next(target_line_points.begin()),
                                 target_line_points.end());
    }
  }

  // Extend reference path to pull over dest.
  const double pull_over_dest_arc_length =
      target_line
          .GetProximity(pull_over_dest, math::pb::UseExtensionFlag::kAllow)
          .arc_length;
  const double end_pt_arc_length =
      target_line
          .GetProximity(concatenated_points.back(),
                        math::pb::UseExtensionFlag::kAllow)
          .arc_length;

  if (pull_over_dest_arc_length > end_pt_arc_length) {
    concatenated_points.push_back(pull_over_dest);
  }

  return math::geometry::PolylineCurve2d(math::geometry::Simplify(
      concatenated_points, math::constants::kDefaultSimplifyThreshold,
      /*assert_no_self_intersection=*/false));
}

// Regulates the end state heading when it has significant diff with lane center
// line projection.
void RegulateEndStateHeading(const math::Curve2d& lane_keep_center_line,
                             JumpInMotionPrimitive::State* end_state) {
  const math::ProximityQueryInfo lane_center_line_proximity =
      lane_keep_center_line.GetProximity(end_state->GetPoint(),
                                         math::pb::UseExtensionFlag::kAllow);
  const double proximity_heading = lane_keep_center_line.GetInterpTheta(
      lane_center_line_proximity.arc_length);

  static constexpr double kMaxAllowedHeadingDiffBetweenJumpInStartAndEndInRad =
      0.524;  // 30 degree
  if (std::fabs(math::AngleDiff(end_state->spiral_state.heading_,
                                proximity_heading)) >
      kMaxAllowedHeadingDiffBetweenJumpInStartAndEndInRad) {
    end_state->spiral_state.heading_ = proximity_heading;
    end_state->spiral_state.curvature_ = 0.0;
  }
}

// Returns true if generated jump in spiral has collision with hard boundaries.
// Aligned with upstream check.
bool HasHardBoundaryCollision(
    std::vector<JumpInMotionPrimitive::State>& sampled_states,
    const lane_selection::LaneSequenceGeometry& lane_sequence_geometry,
    const PickupDropoffZoneInfo& pdz_info,
    const vehicle_model::pb::AxleRectangularMeasurement& shape_measurement) {
  if (sampled_states.empty()) {
    return false;
  }
  const double buffer_dist = 0.5 * shape_measurement.width();
  std::vector<math::geometry::Point2d> jump_in_points;
  jump_in_points.reserve(sampled_states.size());
  for (const JumpInMotionPrimitive::State& state : sampled_states) {
    jump_in_points.emplace_back(state.GetPoint());
  }

  const math::geometry::PolygonWithCache2d polygon = math::geometry::Buffer(
      math::geometry::Polyline2d(std::move(jump_in_points)), buffer_dist,
      buffer_dist);

  // Local lambda to check if pull over curve collides with given hard boundary
  // lines.
  auto has_boundary_collision = [&polygon](const auto& hard_boundary_lines) {
    return std::any_of(hard_boundary_lines.begin(), hard_boundary_lines.end(),
                       [&polygon](const auto& line) {
                         return math::geometry::Intersects(polygon, line);
                       });
  };

  // Check if pull over curve collides with any hard boundary lines.
  return has_boundary_collision(
             lane_sequence_geometry.left_hard_boundary_lines) ||
         has_boundary_collision(
             lane_sequence_geometry.right_hard_boundary_lines) ||
         has_boundary_collision(
             lane_sequence_geometry.middle_hard_boundary_lines) ||
         math::geometry::Intersects(polygon, pdz_info.right_boundary);
}

// Returns the feasible range that newly generated with on-frame target line
// traffic info.
const TargetLineFeasibleRange* RegenerateFeasibleRange(
    const math::geometry::PolylineCurve2d& target_line,
    const math::geometry::Point2d& jump_in_end_pt,
    const std::vector<TargetLineFeasibleRange>& feasible_ranges) {
  const double jump_in_end_arc_length =
      target_line
          .GetProximity(jump_in_end_pt, math::pb::UseExtensionFlag::kAllow)
          .arc_length;
  for (const TargetLineFeasibleRange& feasible_range : feasible_ranges) {
    if (feasible_range.Contains(jump_in_end_arc_length)) {
      return &feasible_range;
    }
  }
  return nullptr;
}

const TargetLineFeasibleRange* LoadFeasibleRangeFromSeed(
    const planner::pb::JumpInGuidanceSeed& seed,
    const TargetLineTrafficInfo& target_line_traffic_info,
    std::vector<TargetLineFeasibleRange>* feasible_ranges) {
  const math::Range1d feasible_range =
      math::Range1d(seed.selected_feasible_range().start_pos(),
                    seed.selected_feasible_range().end_pos());
  // Fake a feasible range object for jump in search result population to
  // fit either feasible range found or not found cases.
  // NOTE: Here we set a ROI min value is mainly for protection of invalid range
  // issue in the interface `math::AreRangesOverlapping`.
  static constexpr double kMinAllowedROILengthInMeters = 0.1;
  const double trailing_roi_length = std::max(
      kMinAllowedROILengthInMeters,
      feasible_range.start_pos -
          target_line_traffic_info.region_of_interest().s_range.start_pos);
  const double leading_roi_length =
      std::max(kMinAllowedROILengthInMeters,
               target_line_traffic_info.region_of_interest().s_range.end_pos -
                   feasible_range.end_pos);
  TargetLineFeasibleRange fake_feasible_range(
      feasible_range.start_pos, trailing_roi_length, feasible_range.end_pos,
      leading_roi_length,
      seed.leading_objects_size() + seed.trailing_objects_size(),
      /*dynamic_snapshot_num=*/0);

  for (const pb::TypedObjectId& typed_id : seed.leading_objects()) {
    const auto snapshot_iter =
        target_line_traffic_info.stationary_snapshot_info_map().find(
            typed_id.object_id());
    if (snapshot_iter !=
        target_line_traffic_info.stationary_snapshot_info_map().end()) {
      fake_feasible_range.AppendSnapshotByCategoryIfLongitudinallyInRange(
          snapshot_iter->second, fake_feasible_range.GetLeadingROIRange(),
          TargetLineFeasibleRange::SnapshotCategory::kLeadingStationary);
    }
  }
  for (const pb::TypedObjectId& typed_id : seed.trailing_objects()) {
    const auto snapshot_iter =
        target_line_traffic_info.stationary_snapshot_info_map().find(
            typed_id.object_id());
    if (snapshot_iter !=
        target_line_traffic_info.stationary_snapshot_info_map().end()) {
      fake_feasible_range.AppendSnapshotByCategoryIfLongitudinallyInRange(
          snapshot_iter->second,
          fake_feasible_range.GetRangeWithLeadingAndTrailingROI(),
          TargetLineFeasibleRange::SnapshotCategory::kTrailingStationary);
    }
  }
  fake_feasible_range.AppendJumpInRangeTrailingSnapshots(
      target_line_traffic_info.jump_in_range_snapshot_info_map());
  feasible_ranges->emplace_back(std::move(fake_feasible_range));
  return &(feasible_ranges->back());
}

// Returns the index of closest state to ego ra position spatially, which is
// computed by querying distance from each state to ego and find min value.
int GetClosestStateIndexToEgo(
    const std::vector<JumpInMotionPrimitive::State>& sampled_states,
    const math::geometry::Point2d& plan_init_ra_position) {
  DCHECK(!sampled_states.empty());
  std::vector<double> comparable_distance(sampled_states.size());
  std::transform(
      sampled_states.begin(), sampled_states.end(), comparable_distance.begin(),
      [&plan_init_ra_position](const JumpInMotionPrimitive::State& state) {
        return math::geometry::ComparableDistance(plan_init_ra_position,
                                                  state.GetPoint());
      });
  const auto min_dist_iter =
      std::min_element(comparable_distance.begin(), comparable_distance.end(),
                       [](double lhs, double rhs) { return lhs < rhs; });
  DCHECK(min_dist_iter != comparable_distance.end());
  return std::distance(comparable_distance.begin(), min_dist_iter);
}

}  // namespace

int JumpInCurveSamplingSpaceParams::GetNumberOfSamples() const {
  DCHECK(IsValid());
  if (!IsValid()) {
    return 0;
  }

  const auto compute_num_of_samples = [](const math::Range1d& range,
                                         double sample_res) {
    // If some dimension has no configuration, we think the sample along the
    // dimension is 1, use the following expression to avoid divide-by-zero
    // error. Note that the sample resolution is not calculated but
    // configurated, so it is expected to be a non-zero positive number.
    return !math::NearZero(sample_res, kEpsilonForSamplingStep)
               ? static_cast<int>((range.end_pos - range.start_pos) /
                                  std::fabs(sample_res)) +
                     1
               : 1;
  };

  const int num_start_lon_samples =
      compute_num_of_samples(start_lon_range, start_lon_sample_res_in_m);

  const int num_end_lon_samples =
      compute_num_of_samples(end_lon_range, end_lon_sample_res_in_m);

  const int num_target_line_lat_samples = compute_num_of_samples(
      target_line_lat_range, target_line_lat_sample_res_in_m);

  return num_start_lon_samples * num_end_lon_samples *
         num_target_line_lat_samples;
}

std::optional<JumpInGuidanceSearchResult>
KinematicJumpInGuidanceSearcher::SearchJumpInGuidanceImpl(
    const JumpInGuidanceSearchInput& input,
    planner::pb::KinematicJumpInGuidanceDebug* debug) {
  TRACE_EVENT_SCOPE(
      planner, KinematicJumpInGuidanceSearcher_SearchKinematicJumpInGuidance);

  const JumpInCurveSamplingSpaceParams sample_space_params(input.sample_config);
  std::vector<JumpInMotionPrimitive> jump_in_primitives =
      GenerateJumpInMotionPrimitives(input, sample_space_params);

  CalculateJumpInPrimitiveCosts(input, sample_space_params, jump_in_primitives);
  JumpInMotionPrimitive* best_jump_in_primitive =
      SelectBestJumpInPrimitive(jump_in_primitives);

  if (best_jump_in_primitive == nullptr || !best_jump_in_primitive->IsValid()) {
    LOG(ERROR) << "The best candidate is invalid, which means the search "
                  "failed, no valid result.";
    return std::nullopt;
  }
  DCHECK(best_jump_in_primitive->IsValid());
  DCHECK(best_jump_in_primitive->target_line_feasible_range != nullptr);
  PopulateDebug(jump_in_primitives, best_jump_in_primitive, sample_space_params,
                debug);

  return CollectSearchResultFromBestJumpInPrimitive(
      *best_jump_in_primitive,
      math::geometry::Offset(
          target_line(),
          best_jump_in_primitive->decision.jump_in_end_pt_lat_pos));
}

std::optional<JumpInGuidanceSearchResult>
KinematicJumpInGuidanceSearcher::SearchKinematicJumpInGuidance(
    planner::pb::KinematicJumpInGuidanceDebug* debug) {
  // Build jump in guidance search input.
  const JumpInGuidanceSearchInput input(
      init_jump_in_start_pt(), init_jump_in_end_pt(), config_.sample_config(),
      config_.costing_weight(), /*is_soft_latching_in=*/false);

  return SearchJumpInGuidanceImpl(input, debug);
}

std::optional<JumpInGuidanceSearchResult>
KinematicJumpInGuidanceSearcher::GenerateSearchResultWithUpstreamHint(
    planner::pb::KinematicJumpInGuidanceDebug* debug) {
  // Step 1: Generate target line feasible ranges.
  const std::vector<TargetLineFeasibleRange> feasible_ranges =
      target_line_traffic_info_.GetFeasiblelRangesFromTargetLineLateralOffset(
          /*lateral_offset=*/0.0);

  // Step 2: Generate jump in spiral with upstream start / end.
  // The sample resolution for sampling the CSpline skeleton of smoothed
  // reference path or attraction segments.
  static constexpr double kSmoothedPathSampleResolutionInMeter = 2.0;
  const math::Curve2d smoothed_source_line(
      source_line().GetSampledPoints(
          /*start_pos=*/0.0, /*end_pos=*/source_line().GetTotalArcLength(),
          /*interval=*/kSmoothedPathSampleResolutionInMeter,
          math::pb::UseExtensionFlag::kForbid),
      math::pb::Interpolation1dType::kCSpline);
  const math::Curve2d smoothed_target_line(
      target_line().GetSampledPoints(
          /*start_pos=*/0.0, /*end_pos=*/target_line().GetTotalArcLength(),
          /*interval=*/kSmoothedPathSampleResolutionInMeter,
          math::pb::UseExtensionFlag::kForbid),
      math::pb::Interpolation1dType::kCSpline);

  JumpInMotionPrimitive::State end_state(
      GenerateSpiralState(init_jump_in_end_pt(), target_line(),
                          smoothed_target_line,
                          /*lon_pos=*/0.0,
                          /*lat_pos=*/0.0),
      /*speed=*/0.0);
  const TargetLineFeasibleRange* feasible_range =
      FindTargetLineFeasibleRange(end_state, target_line(), feasible_ranges);
  if (feasible_range == nullptr) {
    // No valid feasible range for generated end state.
    return std::nullopt;
  }

  // Estimate jump in start speed and timestamp with current ego info.
  // TODO(liwen): In current implementation, we use ego current speed to
  // estimate jump in speed profile, which might be unstable and inaccurate. The
  // jump in speed profile will be soon updated to be estimated by supposed end
  // state (nearly stop at jump in end), and constant deceleration along jump in
  // spiral. The estimated speed profile will be then used in lateral
  // accleration cost, ego braking to jump in start cost and downstream speed
  // limit.
  JumpInMotionPrimitive::State start_state(
      GenerateSpiralState(init_jump_in_start_pt(), source_line(),
                          smoothed_source_line, /*lon_pos=*/0.0,
                          /*lat_pos=*/0.0));

  // Regulate heading of end state, align with its projection on source line if
  // target line heading has significant diff with lane follow. This operation
  // is to avoid some strange jump in spiral generated when target line is not
  // well shaped.
  RegulateEndStateHeading(smoothed_source_line, &end_state);

  // Step 3: Generate spiral.
  BoundaryProblemSolver bvp_solver;
  StateLatticeSpiral spiral;
  const bool solved = bvp_solver.Solve(start_state.spiral_state,
                                       end_state.spiral_state, spiral, nullptr);
  if (!solved) {
    // Spiral generation failed.
    return std::nullopt;
  }
  start_state.speed_state.v = EstimateJumpInStartSpeed(spiral);
  std::vector<JumpInMotionPrimitive::State> sampled_states =
      GenerateSpiralSampledStates(spiral, start_state, end_state);
  if (sampled_states.empty()) {
    // Either geometric or kinematic limits are check failed.
    return std::nullopt;
  }
  // Check road boundary collision.
  if (HasHardBoundaryCollision(sampled_states, lane_sequence_geometry_,
                               dest_init_decision_.pdz_info(),
                               plan_init_state_snapshot()
                                   .car_model_with_shape()
                                   .shape_measurement())) {
    return std::nullopt;
  }

  JumpInMotionPrimitive jump_in_primitive(
      std::move(spiral), std::move(sampled_states), std::move(start_state),
      std::move(end_state),
      JumpInMotionPrimitive::SampleDecision(/*jump_in_start_pt_lon_pos_in=*/0.0,
                                            /*jump_in_end_pt_lon_pos_in=*/0.0,
                                            /*jump_in_end_pt_lat_pos_in=*/0.0),
      feasible_range, /*jump_in_end_dry_steering_angle_in=*/0.0,
      /*closest_state_idx_to_plan_init_ra_in=*/kInvalidClosestStateIndex,
      /*jump_in_end_to_dest_dist_in=*/0.0);

  PopulateDebug({jump_in_primitive}, &jump_in_primitive,
                JumpInCurveSamplingSpaceParams(), debug);

  return CollectSearchResultFromBestJumpInPrimitive(
      jump_in_primitive, math::geometry::PolylineCurve2d(target_line()));
}

std::optional<JumpInGuidanceSearchResult>
KinematicJumpInGuidanceSearcher::SearchJumpInGuidanceWithLatchedResult(
    planner::pb::KinematicJumpInGuidanceDebug* debug) {
  if (prev_iter_seed_ == nullptr) {
    return std::nullopt;
  }

  // Load jump in start / end points.
  const planner::pb::JumpInGuidanceSeed& seed = *prev_iter_seed_;
  const math::geometry::Point2d jump_in_start_pt =
      math::geometry::Convert<math::geometry::Point2d>(
          seed.jump_in_curve_seed().jump_in_start_pt());
  const math::geometry::Point2d jump_in_end_pt =
      math::geometry::Convert<math::geometry::Point2d>(
          seed.jump_in_curve_seed().jump_in_end_pt());

  const JumpInGuidanceSearchInput input(
      jump_in_start_pt, jump_in_end_pt, config_.soft_latch_sample_config(),
      config_.soft_latch_costing_weight(), /*is_soft_latching_in=*/true);

  return SearchJumpInGuidanceImpl(input, debug);
}

std::optional<JumpInGuidanceSearchResult>
KinematicJumpInGuidanceSearcher::LoadJumpInGuidanceSearchResult() const {
  if (prev_iter_seed_ == nullptr) {
    return std::nullopt;
  }

  // Step 1: Load jump in primitive.
  const planner::pb::JumpInGuidanceSeed& seed = *prev_iter_seed_;
  math::geometry::Point2d jump_in_start_pt =
      math::geometry::Convert<math::geometry::Point2d>(
          seed.jump_in_curve_seed().jump_in_start_pt());
  math::geometry::Point2d jump_in_end_pt =
      math::geometry::Convert<math::geometry::Point2d>(
          seed.jump_in_curve_seed().jump_in_end_pt());
  math::geometry::PolylineCurve2d jump_in_polyline(
      seed.jump_in_curve_seed().jump_in_curve());
  JumpInMotionPrimitive jump_in_primitive(
      jump_in_polyline.points(), jump_in_start_pt, jump_in_end_pt,
      seed.jump_in_start_speed(),
      seed.jump_in_curve_seed().target_line_offset());

  // Step 2: Re-generate feasible range & target line, if failed, rebuild from
  // the latched data.
  std::vector<TargetLineFeasibleRange> feasible_ranges =
      target_line_traffic_info_.GetFeasiblelRangesFromTargetLineLateralOffset(
          seed.jump_in_curve_seed().target_line_offset());

  jump_in_primitive.target_line_feasible_range =
      RegenerateFeasibleRange(target_line(), jump_in_end_pt, feasible_ranges);
  math::geometry::PolylineCurve2d refined_target_line = math::geometry::Offset(
      target_line(), seed.jump_in_curve_seed().target_line_offset());
  if (jump_in_primitive.target_line_feasible_range == nullptr) {
    jump_in_primitive.target_line_feasible_range = LoadFeasibleRangeFromSeed(
        seed, target_line_traffic_info_, &feasible_ranges);
    refined_target_line = math::geometry::PolylineCurve2d(seed.target_line());
  }

  return CollectSearchResultFromBestJumpInPrimitive(
      jump_in_primitive, std::move(refined_target_line));
}

std::vector<JumpInMotionPrimitive>
KinematicJumpInGuidanceSearcher::GenerateJumpInMotionPrimitives(
    const JumpInGuidanceSearchInput& input,
    const JumpInCurveSamplingSpaceParams& params) {
  TRACE_EVENT_SCOPE(
      planner, KinematicJumpInGuidanceSearcher_GenerateJumpInMotionPrimitives);
  if (!params.IsValid()) {
    return {};
  }

  // For each dimension, generate spiral states, and generate a tuple pointer
  // list, then use tbb parallel to generate jump in spirals.
  const math::Curve2d smoothed_source_line(
      source_line().GetSampledPoints(
          /*start_pos=*/0.0, /*end_pos=*/source_line().GetTotalArcLength(),
          /*interval=*/2.0, math::pb::UseExtensionFlag::kForbid),
      math::pb::Interpolation1dType::kCSpline);
  const math::Curve2d smoothed_target_line(
      target_line().GetSampledPoints(
          /*start_pos=*/0.0, /*end_pos=*/target_line().GetTotalArcLength(),
          /*interval=*/2.0, math::pb::UseExtensionFlag::kForbid),
      math::pb::Interpolation1dType::kCSpline);
  std::vector<
      std::tuple<JumpInMotionPrimitive::State, JumpInMotionPrimitive::State,
                 JumpInMotionPrimitive::SampleDecision,
                 const TargetLineFeasibleRange*, double>>
      jump_in_curve_inputs;
  jump_in_curve_inputs.reserve(params.GetNumberOfSamples());

  // Generate target line feasible ranges with sampled lateral offsets.
  GenerateTargetLineFeasibleRangeWithLateralOffsets(
      params.target_line_lat_range, params.target_line_lat_sample_res_in_m);

  // Query jump in end point laterally sampled positions.
  for (FeasibleRangeSearchCandidates& feasible_range_candidates :
       target_line_feasible_range_candidates_) {
    const double jump_in_end_lat_pos = feasible_range_candidates.lateral_offset;
    std::vector<TargetLineFeasibleRange>& feasible_ranges =
        feasible_range_candidates.feasible_ranges;
    // Query jump in end point longitudinally sampled positions.
    // NOTE: Use loop step that not equals to 0 to make sure for loop works.
    // When there's only one sampled needed along the dimension, the step
    // might be zero.
    const double end_lon_pos_loop_step_in_m =
        std::max(params.end_lon_sample_res_in_m, kEpsilonForSamplingStep);
    for (double jump_in_end_lon_pos = params.end_lon_range.start_pos;
         jump_in_end_lon_pos <= params.end_lon_range.end_pos;
         jump_in_end_lon_pos += end_lon_pos_loop_step_in_m) {
      JumpInMotionPrimitive::State end_state(
          GenerateSpiralState(input.jump_in_end_pt, target_line(),
                              smoothed_target_line, jump_in_end_lon_pos,
                              jump_in_end_lat_pos),
          /*speed=*/0.0);
      RegulateEndStateHeading(smoothed_source_line, &end_state);
      const TargetLineFeasibleRange* feasible_range =
          FindTargetLineFeasibleRange(end_state, target_line(),
                                      feasible_ranges);
      if (feasible_range == nullptr) {
        // The sampled jump in end point is not within any feasible range
        // longitudinally, ignore the sample.
        continue;
      }
      if (!IsEndStateParkable(end_state, dest_init_decision_.pdz_info())) {
        // The sampled jump in end point is invalid based on upstream info about
        // no parking or road exit limitations.
        continue;
      }
      // Pre-compute pull out dry steering angle here for performance.
      const double expected_dry_steering_angle_for_jump_out =
          ComputeDrySteeringAngleToUnstuck(plan_init_state_snapshot(),
                                           end_state, *feasible_range);

      // Query jump in start point longitudinally sampled positions.
      // NOTE: Use loop step that not equals to 0 to make sure for loop works.
      // When there's only one sampled needed along the dimension, the step
      // might be zero.
      const double start_lon_pos_loop_step_in_m =
          std::max(params.start_lon_sample_res_in_m, kEpsilonForSamplingStep);
      for (double jump_in_start_lon_pos = params.start_lon_range.start_pos;
           jump_in_start_lon_pos <= params.start_lon_range.end_pos;
           jump_in_start_lon_pos += start_lon_pos_loop_step_in_m) {
        JumpInMotionPrimitive::State start_state(
            GenerateSpiralState(input.jump_in_start_pt, source_line(),
                                smoothed_source_line, jump_in_start_lon_pos,
                                /*lat_pos=*/0.0));
        // NOTE: When it is in soft latching stage, pull over should have been
        // in EXECUTE stage, which means the start / end lon position is almost
        // fixed, should not be checked anymore.
        if (!input.is_soft_latching &&
            !IsStartEndStateValid(plan_init_state_snapshot(), source_line(),
                                  dest_init_decision_.reference_dest,
                                  start_state, end_state)) {
          continue;
        }
        jump_in_curve_inputs.emplace_back(
            std::move(start_state), std::move(end_state),
            JumpInMotionPrimitive::SampleDecision(jump_in_start_lon_pos,
                                                  jump_in_end_lon_pos,
                                                  jump_in_end_lat_pos),
            feasible_range, expected_dry_steering_angle_for_jump_out);
      }
    }
  }

  std::vector<JumpInMotionPrimitive> jump_in_curve_results(
      jump_in_curve_inputs.size());
  BoundaryProblemSolver bvp_solver;

  tbb::parallel_for(
      0, static_cast<int>(jump_in_curve_inputs.size()),
      [this, &jump_in_curve_inputs, &jump_in_curve_results,
       &bvp_solver](int jump_in_curve_idx) {
        auto& [jump_in_start_state, jump_in_end_state, sample_offset,
               feasible_range, expected_dry_steering_angle] =
            jump_in_curve_inputs[jump_in_curve_idx];
        StateLatticeSpiral spiral;
        const bool solved =
            bvp_solver.Solve(jump_in_start_state.spiral_state,
                             jump_in_end_state.spiral_state, spiral, nullptr);
        if (!solved) {
          return;
        }
        // Estimate jump in start speed.
        jump_in_start_state.speed_state.v = EstimateJumpInStartSpeed(spiral);
        std::vector<JumpInMotionPrimitive::State> sampled_states =
            GenerateSpiralSampledStates(spiral, jump_in_start_state,
                                        jump_in_end_state);
        if (sampled_states.empty()) {
          // Either geometric or kinematic limits are check failed.
          return;
        }
        // Check road boundary collision.
        if (HasHardBoundaryCollision(sampled_states, lane_sequence_geometry_,
                                     dest_init_decision_.pdz_info(),
                                     plan_init_state_snapshot()
                                         .car_model_with_shape()
                                         .shape_measurement())) {
          return;
        }
        // Project plan init position to the jump in curve to find closest state
        // index.
        const int closest_state_idx = GetClosestStateIndexToEgo(
            sampled_states, plan_init_state_snapshot().rear_axle_position());
        const double jump_in_end_to_dest_dist =
            target_line()
                .GetProximity(dest_init_decision_.reference_dest,
                              math::pb::kAllow)
                .arc_length -
            target_line()
                .GetProximity(jump_in_end_state.GetPoint(), math::pb::kAllow)
                .arc_length;
        jump_in_curve_results[jump_in_curve_idx] = JumpInMotionPrimitive(
            std::move(spiral), std::move(sampled_states),
            std::move(jump_in_start_state), std::move(jump_in_end_state),
            std::move(sample_offset), feasible_range,
            expected_dry_steering_angle, closest_state_idx,
            jump_in_end_to_dest_dist);
      });
  return jump_in_curve_results;
}

void KinematicJumpInGuidanceSearcher::CalculateJumpInPrimitiveCosts(
    const JumpInGuidanceSearchInput& input,
    const JumpInCurveSamplingSpaceParams& sample_space_params,
    std::vector<JumpInMotionPrimitive>& jump_in_primitives) const {
  TRACE_EVENT_SCOPE(
      planner, KinematicJumpInGuidanceSearcher_CalculateJumpInPrimitiveCosts);
  // Load last path.
  const std::optional<pb::JumpInCurveSeed> last_cycle_jump_in_curve_seed =
      prev_iter_seed_ != nullptr
          ? std::make_optional(prev_iter_seed_->jump_in_curve_seed())
          : std::nullopt;

  JumpInCurveCostingEngine costing_engine(
      plan_init_state_snapshot(), lane_sequence_geometry_, dest_init_decision_,
      target_line_traffic_info_, last_cycle_jump_in_curve_seed,
      input.jump_in_start_pt, sample_space_params.start_lon_range,
      input.costing_weight, input.is_soft_latching);

  tbb::parallel_for(
      0, static_cast<int>(jump_in_primitives.size()),
      [&jump_in_primitives, &costing_engine](int primitive_idx) {
        JumpInMotionPrimitive& jump_in_primitive =
            jump_in_primitives[primitive_idx];
        costing_engine.CalculateJumpInPrimitiveCost(jump_in_primitive);
      });
}

JumpInMotionPrimitive*
KinematicJumpInGuidanceSearcher::SelectBestJumpInPrimitive(
    std::vector<JumpInMotionPrimitive>& jump_in_primitives) const {
  if (jump_in_primitives.empty()) {
    return nullptr;
  }
  // Sort jump in primitives by costing.
  std::sort(
      jump_in_primitives.begin(), jump_in_primitives.end(),
      [](const JumpInMotionPrimitive& lhs, const JumpInMotionPrimitive& rhs) {
        if (!lhs.IsValid()) {
          return false;
        }
        if (!rhs.IsValid()) {
          return true;
        }
        return lhs.costing_info.total_cost() < rhs.costing_info.total_cost();
      });
  return jump_in_primitives.front().IsValid() ? &jump_in_primitives.front()
                                              : nullptr;
}

void KinematicJumpInGuidanceSearcher::PopulateDebug(
    const std::vector<JumpInMotionPrimitive>& jump_in_primitives,
    const JumpInMotionPrimitive* best_jump_in_primitive,
    const JumpInCurveSamplingSpaceParams& config,
    planner::pb::KinematicJumpInGuidanceDebug* debug) const {
  TRACE_EVENT_SCOPE(planner, KinematicJumpInGuidanceSearcher_PopulateDebug);
  // Populate jump in curve debug.
  for (const JumpInMotionPrimitive& jump_in_primitive : jump_in_primitives) {
    if (!jump_in_primitive.IsValid()) {
      continue;
    }
    pb::JumpInMotionPrimitiveDebug* curve_debug = debug->add_curves();
    jump_in_primitive.ToProto(curve_debug);
    // Project curve.
    ProjectXYToSL(target_line(), curve_debug->mutable_curve());
    if (best_jump_in_primitive == &jump_in_primitive) {
      curve_debug->set_is_selected_candidate(true);
    }
  }

  // Populate trimmed ref path and target line.
  DCHECK(dest_init_decision_.jump_in_geom.has_value());
  const double start_arc_length =
      source_line()
          .GetProximity(init_jump_in_start_pt(),
                        math::pb::UseExtensionFlag::kForbid)
          .arc_length;
  static constexpr double kMargin = 1.0;
  std::vector<math::geometry::Point2d> trimmed_nominal_path_points =
      source_line().GetSkeletonInRange(
          start_arc_length + config.start_lon_range.start_pos - kMargin,
          start_arc_length + config.start_lon_range.end_pos + kMargin);
  DCHECK_GE(trimmed_nominal_path_points.size(),
            math::kMinimumInterpolationPointNum);
  const math::geometry::PolylineCurve2d trimmed_nominal_path(
      std::move(trimmed_nominal_path_points));
  trimmed_nominal_path.ToProto(debug->mutable_source_line());
  ProjectXYToSL(target_line(), debug->mutable_source_line());

  const double end_arc_length =
      target_line()
          .GetProximity(init_jump_in_end_pt(),
                        math::pb::UseExtensionFlag::kAllow)
          .arc_length;

  std::vector<math::geometry::Point2d> trimmed_target_line_points =
      target_line().GetSkeletonInRange(
          end_arc_length + config.end_lon_range.start_pos - kMargin,
          end_arc_length + config.end_lon_range.end_pos + kMargin);
  if (trimmed_target_line_points.size() < math::kMinimumInterpolationPointNum) {
    trimmed_target_line_points = target_line().points();
  }
  DCHECK_GE(trimmed_target_line_points.size(),
            math::kMinimumInterpolationPointNum);
  const math::geometry::PolylineCurve2d trimmed_target_line(
      std::move(trimmed_target_line_points));
  trimmed_target_line.ToProto(debug->mutable_target_line());
  ProjectXYToSL(target_line(), debug->mutable_target_line());

  // Populate target line traffic infos.
  for (const FeasibleRangeSearchCandidates& feasible_range_candidates :
       target_line_feasible_range_candidates_) {
    const double target_line_lat_offset =
        feasible_range_candidates.lateral_offset;
    const std::vector<TargetLineFeasibleRange>& feasible_ranges =
        feasible_range_candidates.feasible_ranges;
    for (const TargetLineFeasibleRange& range : feasible_ranges) {
      planner::pb::TargetLineFeasibleRangeDebug* range_debug =
          debug->add_target_line_feasible_ranges();
      range_debug->mutable_start_pt()->set_x(range.start_arc_length());
      range_debug->mutable_start_pt()->set_y(target_line_lat_offset);
      range_debug->mutable_end_pt()->set_x(range.end_arc_length());
      range_debug->mutable_end_pt()->set_y(target_line_lat_offset);
      if (&range == best_jump_in_primitive->target_line_feasible_range) {
        range_debug->set_is_selected_feasible_range(true);
      }

      // Populate static snapshots.
      for (const TargetLineSnapshotInfo* snapshot :
           range.stationary_snapshots()) {
        planner::pb::TargetLineSnapshotDebug* snapshot_debug =
            range_debug->add_static_snapshots();
        snapshot->ToProto(snapshot_debug);
      }

      // Populate dynamic snapshots.
      for (const TargetLineSnapshotInfo* snapshot : range.dynamic_snapshots()) {
        planner::pb::TargetLineSnapshotDebug* snapshot_debug =
            range_debug->add_dynamic_snapshots();
        DCHECK_NOTNULL(snapshot)->ToProto(snapshot_debug);
      }
    }
  }
}

JumpInGuidanceSearchResult
KinematicJumpInGuidanceSearcher::CollectSearchResultFromBestJumpInPrimitive(
    const JumpInMotionPrimitive& jump_in_primitive,
    math::geometry::PolylineCurve2d&& target_line) const {
  TRACE_EVENT_SCOPE(
      planner,
      KinematicJumpInGuidanceSearcher_CollectSearchResultFromBestJumpInPrimitive);
  const math::geometry::Point2d& pull_over_dest =
      dest_init_decision_.reference_dest;
  const TargetLineFeasibleRange& feasible_range =
      *DCHECK_NOTNULL(jump_in_primitive.target_line_feasible_range);

  // Generate refined pull over destination and target pose.
  const double pull_over_dest_arc_length =
      target_line
          .GetProximity(pull_over_dest, math::pb::UseExtensionFlag::kAllow)
          .arc_length;
  const double jump_in_end_arc_length =
      target_line
          .GetProximity(jump_in_primitive.GetEndPoint(),
                        math::pb::UseExtensionFlag::kAllow)
          .arc_length;
  // Postpone pull over destination if the jump in end pt exceeds it.
  math::geometry::Point2d refined_pull_over_destination = target_line.GetInterp(
      std::max(pull_over_dest_arc_length, jump_in_end_arc_length));

  // TODO(liwen): Here we use target line rather than reference path to find the
  // pull over target pose. Actually I found that it make ego end position more
  // regular. Need further test.
  math::Pose2d refined_target_pose = GetPullOverTargetPoseByTargetLine(
      target_line,
      world_model_.robot_state().car_model_with_shape().shape_measurement(),
      refined_pull_over_destination);

  // Generate reference path and related geometry.
  math::geometry::PolylineCurve2d reference_path =
      StitchReferenceCurve(source_line(), jump_in_primitive, target_line,
                           refined_pull_over_destination);

  math::Range1d selected_feasible_range(feasible_range.start_arc_length(),
                                        feasible_range.end_arc_length());
  const double jump_in_start_speed =
      jump_in_primitive.start_state.speed_state.v;

  // Convert leading / trailing objects.
  const auto convert_snapshots_to_object_infos =
      [](const std::vector<const TargetLineSnapshotInfo*>& snapshots) {
        std::vector<PullOverObjectInfo> pull_over_objects;
        pull_over_objects.reserve(snapshots.size());
        for (const TargetLineSnapshotInfo* snapshot : snapshots) {
          if (!snapshot->is_stationary()) {
            // NOTE: Currently we only regard static objects as leading /
            // trailing object, which is for stability in downstream usage.
            continue;
          }
          pull_over_objects.emplace_back(
              snapshot->typed_obj_id(), snapshot->contour(),
              snapshot->sl_bbox(), snapshot->planner_object_ptr(),
              snapshot->construction_zone_ptr());
        }
        return pull_over_objects;
      };

  std::vector<PullOverObjectInfo> leading_objects =
      convert_snapshots_to_object_infos(feasible_range.leading_snapshots());
  std::vector<PullOverObjectInfo> trailing_objects =
      convert_snapshots_to_object_infos(feasible_range.trailing_snapshots());

  return JumpInGuidanceSearchResult(
      std::move(reference_path), std::move(target_line),
      jump_in_primitive.GetStartPoint(), jump_in_primitive.GetEndPoint(),
      std::move(refined_pull_over_destination), std::move(refined_target_pose),
      std::move(selected_feasible_range),
      std::make_optional(jump_in_primitive.spiral), std::move(leading_objects),
      std::move(trailing_objects),
      JumpInMotionPrimitive::SampleDecision(jump_in_primitive.decision),
      jump_in_start_speed);
}

void KinematicJumpInGuidanceSearcher::
    GenerateTargetLineFeasibleRangeWithLateralOffsets(
        const math::Range1d& sample_range, double sample_res_in_m) {
  const size_t sample_size =
      !math::NearZero(sample_res_in_m)
          ? static_cast<size_t>(
                (sample_range.end_pos - sample_range.start_pos) /
                sample_res_in_m) +
                1
          : 1;
  const DestinationInitialDecision::JumpInGeometry& jump_in_geom =
      base::CheckAndGetValue(dest_init_decision_.jump_in_geom);
  static constexpr double kMinLonClearanceToLeadingAndTrailingObjInMeter = 3.0;
  const double min_feasible_range_length =
      plan_init_state_snapshot()
          .car_model_with_shape()
          .shape_measurement()
          .length() +
      2.0 * kMinLonClearanceToLeadingAndTrailingObjInMeter;
  const int max_feasible_range_size =
      static_cast<int>((jump_in_geom.jump_in_range.end_pos -
                        jump_in_geom.jump_in_range.start_pos) /
                       min_feasible_range_length) +
      1;
  target_line_feasible_range_candidates_.reserve(sample_size *
                                                 max_feasible_range_size);

  // NOTE: Use loop step that not equals to 0 to make sure for loop works.
  // When there's only one sampled needed along the dimension, the step
  // might be zero.
  const double lat_pos_loop_step_in_m =
      std::max(sample_res_in_m, kEpsilonForSamplingStep);
  for (double target_line_offset = sample_range.start_pos;
       target_line_offset <= sample_range.end_pos;
       target_line_offset += lat_pos_loop_step_in_m) {
    target_line_feasible_range_candidates_.emplace_back(
        target_line_traffic_info_.GetFeasiblelRangesFromTargetLineLateralOffset(
            target_line_offset),
        target_line_offset);
  }
}

}  // namespace pull_over

}  // namespace planner
