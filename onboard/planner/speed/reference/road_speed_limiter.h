#ifndef ONBOARD_PLANNER_SPEED_REFERENCE_ROAD_SPEED_LIMITER_H_
#define ONBOARD_PLANNER_SPEED_REFERENCE_ROAD_SPEED_LIMITER_H_

#include <ctime>
#include <memory>
#include <string>
#include <vector>

#include "adv_geom/path2d_with_juke.h"
#include "planner/speed/reasoning_input/traffic_rules/speed_limit_traffic_rule.h"
#include "planner/speed/reference/limiter_interface.h"
#include "planner/speed/reference/reference_profile.h"
#include "planner_protos/speed_generator_config.pb.h"
#include "planner_protos/speed_limiters.pb.h"
#include "pnc_map_service/map_elements/lane.h"
#include "pnc_map_service/pnc_map_service.h"

namespace planner {
namespace speed {

// This class regulates the speed profile max speed given the road speed
// limit.
class RoadSpeedLimiter : public LimiterInterface {
 public:
  RoadSpeedLimiter(
      const std::vector<const pnc_map::Lane*>& lane_sequence,
      const pb::RoadSpeedLimiterConfig& config,
      double relax_ego_exceeding_road_speed_limit_factor,
      std::optional<double> discomfort_to_decay_ego_exceeding_road_speed_limit,
      std::vector<traffic_rules::LaneSpeedLimit> traffic_rule_speed_limits,
      planner::pb::MotionMode motion_mode, const std::tm* order_start_time_info,
      ReferenceProfile* reference_profile_ptr,
      const std::string& current_region);

  RoadSpeedLimiter(
      const std::vector<const pnc_map::Lane*>& lane_sequence,
      const pb::RoadSpeedLimiterConfig& config,
      double relax_ego_exceeding_road_speed_limit_factor,
      std::optional<double> discomfort_to_decay_ego_exceeding_road_speed_limit,
      ReferenceProfile* reference_profile_ptr,
      const std::string& current_region);

  // Updates 'reference_profile_ptr_' with comfort and max discomfort road
  // speed limits.
  void Update() final;

  void SetRoadSpeedLimitForEntirePath(double speed);

  bool updated() const { return updated_; }

  const pb::RoadSpeedLimiter& speed_limits_debug() const {
    return road_speed_limits_debug_;
  }

  const std::vector<traffic_rules::LaneSpeedLimit>& traffic_rule_speed_limits()
      const {
    return traffic_rule_speed_limits_;
  }

  // Returns the speed limit in meter per second that depends on the time ego
  // operates, aka, the order starting time. Generally ego drives slower at
  // night times.
  static double ComputeSpeedLimitBasedOnOperationTime(
      const std::tm& operation_time, const pb::RoadSpeedLimiterConfig& config);

 private:
  // Sets max/zero discomfort road speed limit for the path range stating from
  // start_length to end_length.
  void SetMaxDiscomfortRoadSpeedLimit(double speed, double start_ra_arclength,
                                      double end_ra_arclength);

  void SetZeroDiscomfortRoadSpeedLimit(double speed, double start_ra_arclength,
                                       double end_ra_arclength);

  // Sets the road speed limits for max and zero discomfort for the
  // given arclength and also passes the data to protobuf.
  void SetZeroAndMaxDiscomfortRoadSpeedLimit(
      double speed, double start_ra_arclength, double end_ra_arclength,
      pb::SpeedLimitForArcLength* speed_limit_at_arclength_debug);

  // Sets discomfort varying speed limit given 'discomfort_varying_speed_limit'
  // at 'discomforts'. They must be of the same size. Note that, 'discomforts'
  // should cover all discomfort levels defined in discomforts.h/.cpp. O.W.,
  // there can be discomfort level whose speed limit is left as infinity.
  void SetDiscomfortVaryingRoadSpeedLimit(
      const std::vector<double>& discomforts,
      const std::vector<double>& discomfort_varying_speed_limit,
      double start_ra_arclength, double end_ra_arclength,
      pb::SpeedLimitForArcLength* speed_limit_at_arclength_debug);

  // Computes and sets discomfort varying road speed limit w.r.t. the relaxation
  // factor and the previous discomfort.
  void ComputeAndSetDiscomfortVaryingRoadSpeedLimit(
      double max_road_speed_limit, double init_state_speed,
      double ego_max_speed, double previous_discomfort_to_decay_relaxation,
      double start_ra_arclength, double end_ra_arclength,
      pb::SpeedLimitForArcLength* speed_limit_at_arclength_debug);

  // Sets fixed location speed limit patch for occlusion.
  // 2024-2025 occlusion-related speed limit patch:
  // https://cooper.didichuxing.com/docs2/sheet/2203275557565?sheetId=q2ycv
  //
  // The speed limit patches are due to the limited capability of the current
  // occlusion reasoner. We include them into some new cautious driving
  // strategies in the future:
  //  1. occlusion in large exit zone (cn13392591);
  //  2. occlusion in large junction with tunnels/underpass (cn12994425);
  //  3. occlusion in unprotected right turn bike lane (cn12293075);
  //  4. occlusion in large junction for oncoming UPL/U-Turn vehicles
  //     (cn11825409);
  //  5. occlusion in lane-merge (cn11462161);
  //  6. occlusion in intersection (cn18769231);
  //  7. narrow space (cn18916751);

  // TODO(zhihaoruan) remove the speed limit patch after implementing the above
  // new occlusion scenarios.
  void SetFixedSpeedLimitPatchForOcclusion(
      const adv_geom::Path2dWithJuke& path,
      const std::vector<const pnc_map::Lane*>& lane_sequence);

  //
  // Data.
  //
  const std::vector<const pnc_map::Lane*>& lane_sequence_;
  // We relax the road speed limit to be above real road speed limit by 20%.
  double relax_ego_exceeding_road_speed_limit_factor_ = 0.0;
  // The discomfort from which the road limit relaxation should be decayed
  // gradually, in order to make the speed limit come back to the original road
  // limit smoothly when the discomfort drops.
  std::optional<double> discomfort_to_decay_ego_exceeding_road_speed_limit_;
  ReferenceProfile* reference_profile_ptr_ = nullptr;
  // If Update function is called.
  bool updated_ = false;
  // Speed limits for the path.
  pb::RoadSpeedLimiter road_speed_limits_debug_;
  // Speed limits from the traffic_rule.
  // NOTE(zekixu): The traffic rule speed limit set to 5km/h in reverse driving.
  // It will be used to replace the map road speed limit when reversing.
  std::vector<traffic_rules::LaneSpeedLimit> traffic_rule_speed_limits_ = {};
  // Motion mode.
  planner::pb::MotionMode motion_mode_ = planner::pb::MotionMode::FORWARD;
  // order start time info from world model
  const std::tm* order_start_time_info_ = nullptr;
  const pb::RoadSpeedLimiterConfig& config_;
  const std::string current_region_;
};

}  // namespace speed
}  // namespace planner

#endif  // ONBOARD_PLANNER_SPEED_REFERENCE_ROAD_SPEED_LIMITER_H_
