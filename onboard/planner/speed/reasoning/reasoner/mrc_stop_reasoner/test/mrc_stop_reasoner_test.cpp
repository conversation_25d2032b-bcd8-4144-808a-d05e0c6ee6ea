#include "planner/speed/reasoning/reasoner/mrc_stop_reasoner/mrc_stop_reasoner.h"

#include <gtest/gtest.h>

#include "planner/planning_gflags.h"
#include "planner/speed/reasoning/test/reasoning_test_fixture.h"

namespace planner {
namespace speed {

class MrcStopReasonerTest : public ::testing::Test,
                            public ReasoningTestFixture {
 public:
  MrcStopReasonerTest() {
    SetUpSceneMap(hdmap::test_util::SceneType::kJunction);
  }

  bool Reason() {
    reasoner::MrcStopReasoner reasoner;
    speed::pb::ReasoningDebug debug;
    ConstraintCreator constraint_creator = GenerateConstraintCreator();
    ReferenceGenerator ref_generator = CreateReferenceGenerator();

    return reasoner.Reason(
        world_context(), trajectory_info(),
        GetSpeedGeneratorConfig().for_car_type().limits(), previous_iter_seed(),
        mutable_current_iter_seed()->mutable_speed_reasoner_seeds(),
        &ref_generator.mutable_cautious_driving_limiter(), &constraint_creator,
        &debug);
  }
};

TEST_F(MrcStopReasonerTest, AddConstraintForInlaneStopTest) {
  SetEgoPose(/*lane_id=*/9023, /*portion=*/0.0);
  SetEgoCanbus(/*is_auto=*/false);
  CreateLaneFollowPath();
  UpdateWorldModel();
  SetShouldPlannerRespondMRC(/*should_planner_respond_mrc=*/true);
  Update();
  EXPECT_FALSE(Reason());

  mrc::pb::MrcRequest mrc_request;
  mrc_request.set_mrm_type(mrc::pb::MrmType::MRM_INLANE);
  const double trailing_bumper_offset = rear_axle_to_rear_bumper();

  {
    SetEgoPose(/*lane_id=*/10527, /*portion=*/0.0, /*speed=*/10.0);
    SetEgoCanbus(/*is_auto=*/false);
    CreateLaneFollowPath();
    UpdateWorldModel(mrc_request);
    SetShouldPlannerRespondMRC(/*should_planner_respond_mrc=*/true);
    Update();
    EXPECT_TRUE(Reason());

    EXPECT_EQ(2, constraints().size());
    const Constraint& added_constraint = constraints().back();
    EXPECT_EQ("MRC_STOP", added_constraint.generating_reasoner_id);
    EXPECT_EQ("mrc-inlane-hard", added_constraint.unique_constraint_id);
    EXPECT_EQ(pb::Constraint::STOP_POINT, added_constraint.type);

    EXPECT_EQ(1, added_constraint.states.size());
    const ConstraintState& state = added_constraint.states.back();
    EXPECT_NEAR(state.end_x(Discomforts::kMin) - trailing_bumper_offset,
                526.464, 1e-3);

    constraints_.clear();
  }

  {
    mutable_previous_iter_seed().mutable_speed_seed()->CopyFrom(
        *mutable_current_iter_seed());

    SetEgoPose(/*lane_id=*/10527, /*portion=*/0.05, /*speed=*/10.0);
    SetEgoCanbus(/*is_auto=*/false);
    CreateLaneFollowPath();
    UpdateWorldModel(mrc_request);
    SetShouldPlannerRespondMRC(/*should_planner_respond_mrc=*/true);
    Update();
    EXPECT_TRUE(Reason());

    EXPECT_EQ(2, constraints().size());
    const Constraint& added_constraint = constraints().back();
    EXPECT_EQ("MRC_STOP", added_constraint.generating_reasoner_id);
    EXPECT_EQ("mrc-inlane-hard", added_constraint.unique_constraint_id);
    EXPECT_EQ(pb::Constraint::STOP_POINT, added_constraint.type);

    EXPECT_EQ(1, added_constraint.states.size());
    const ConstraintState& state = added_constraint.states.back();
    EXPECT_NEAR(state.end_x(Discomforts::kMin) - trailing_bumper_offset,
                526.464, 1e-3);

    constraints_.clear();
  }

  {
    mutable_previous_iter_seed().mutable_speed_seed()->Clear();

    SetEgoPose(/*lane_id=*/10527, /*portion=*/0.3, /*speed=*/10.0);
    SetEgoCanbus(/*is_auto=*/false);
    CreateLaneFollowPath();
    UpdateWorldModel(mrc_request);
    SetShouldPlannerRespondMRC(/*should_planner_respond_mrc=*/true);
    Update();
    EXPECT_TRUE(Reason());

    EXPECT_EQ(2, constraints().size());
    const Constraint& added_constraint = constraints().back();
    EXPECT_EQ("MRC_STOP", added_constraint.generating_reasoner_id);
    EXPECT_EQ("mrc-inlane-hard", added_constraint.unique_constraint_id);
    EXPECT_EQ(pb::Constraint::STOP_POINT, added_constraint.type);

    EXPECT_EQ(1, added_constraint.states.size());
    const ConstraintState& state = added_constraint.states.back();
    EXPECT_NEAR(state.end_x(Discomforts::kMin) - trailing_bumper_offset,
                526.464, 1e-3);

    constraints_.clear();
  }
}

TEST_F(MrcStopReasonerTest, AddConstraintForUrgencyStopTest) {
  SetEgoPose(/*lane_id=*/10527, /*portion=*/0.0, /*speed=*/0.1);
  SetEgoCanbus(/*is_auto=*/false);
  CreateLaneFollowPath();
  Update();
  EXPECT_FALSE(Reason());

  mrc::pb::MrcRequest mrc_request;
  mrc_request.set_mrm_type(mrc::pb::MrmType::MRM_URGENCY);
  const double trailing_bumper_offset = rear_axle_to_rear_bumper();
  // Test for immediate stop when ego is nearly stationary.
  {
    mrc_request.set_urgency_stop_level(
        mrc::pb::UrgencyStopLevel::STOP_IMMEDIATELY);
    UpdateWorldModel(mrc_request);
    SetShouldPlannerRespondMRC(/*should_planner_respond_mrc=*/true);
    Update();
    EXPECT_TRUE(Reason());

    EXPECT_EQ(1, constraints().size());
    const Constraint& added_constraint = constraints().back();
    EXPECT_EQ("MRC_STOP", added_constraint.generating_reasoner_id);
    EXPECT_EQ("mrc-hold", added_constraint.unique_constraint_id);
    EXPECT_EQ(pb::Constraint::STOP_POINT, added_constraint.type);

    EXPECT_EQ(1, added_constraint.states.size());
    const ConstraintState& state = added_constraint.states.back();
    EXPECT_NEAR(state.end_x(Discomforts::kMin), trailing_bumper_offset, 1e-3);

    constraints_.clear();
  }

  SetEgoPose(/*lane_id=*/10527, /*portion=*/0.0, /*speed=*/30.0);
  SetEgoCanbus(/*is_auto=*/true);

  // Test for immediate stop.
  {
    mrc_request.set_urgency_stop_level(
        mrc::pb::UrgencyStopLevel::STOP_IMMEDIATELY);
    CreateLaneFollowPath();
    UpdateWorldModel(mrc_request);
    SetShouldPlannerRespondMRC(/*should_planner_respond_mrc=*/true);
    Update();
    EXPECT_TRUE(Reason());

    EXPECT_EQ(2, constraints().size());
    const Constraint& added_constraint = constraints().back();
    EXPECT_EQ("MRC_STOP", added_constraint.generating_reasoner_id);
    EXPECT_EQ("mrc-urgency-hard", added_constraint.unique_constraint_id);
    EXPECT_EQ(pb::Constraint::STOP_POINT, added_constraint.type);

    EXPECT_EQ(1, added_constraint.states.size());
    const ConstraintState& state = added_constraint.states.back();
    EXPECT_DOUBLE_EQ(state.end_x(Discomforts::kMin) - trailing_bumper_offset,
                     23.859);

    constraints_.clear();
  }

  // Test for quick stop.
  {
    mrc_request.set_urgency_stop_level(mrc::pb::UrgencyStopLevel::STOP_QUICKLY);
    UpdateWorldModel(mrc_request);
    SetShouldPlannerRespondMRC(/*should_planner_respond_mrc=*/true);
    Update();
    EXPECT_TRUE(Reason());

    EXPECT_EQ(2, constraints().size());
    const Constraint& added_constraint = constraints().back();
    EXPECT_EQ("MRC_STOP", added_constraint.generating_reasoner_id);
    EXPECT_EQ("mrc-urgency-hard", added_constraint.unique_constraint_id);
    EXPECT_EQ(pb::Constraint::STOP_POINT, added_constraint.type);

    EXPECT_EQ(1, added_constraint.states.size());
    const ConstraintState& state = added_constraint.states.back();
    EXPECT_DOUBLE_EQ(state.end_x(Discomforts::kMin) - trailing_bumper_offset,
                     28.0 + world_context().ra_to_leading_bumper());

    constraints_.clear();
  }

  // Test for comfortable stop.
  {
    mrc_request.set_urgency_stop_level(
        mrc::pb::UrgencyStopLevel::STOP_COMFORTABLY);
    UpdateWorldModel(mrc_request);
    SetShouldPlannerRespondMRC(/*should_planner_respond_mrc=*/true);
    Update();
    EXPECT_TRUE(Reason());

    EXPECT_EQ(2, constraints().size());
    const Constraint& added_constraint = constraints().back();
    EXPECT_EQ("MRC_STOP", added_constraint.generating_reasoner_id);
    EXPECT_EQ("mrc-urgency-hard", added_constraint.unique_constraint_id);
    EXPECT_EQ(pb::Constraint::STOP_POINT, added_constraint.type);

    EXPECT_EQ(1, added_constraint.states.size());
    const ConstraintState& state = added_constraint.states.back();
    EXPECT_DOUBLE_EQ(state.end_x(Discomforts::kMin) - trailing_bumper_offset,
                     46.0 + world_context().ra_to_leading_bumper());

    constraints_.clear();
  }

  // Test for comfortable stop w/ seed.
  {
    SetEgoPose(/*lane_id=*/10527, /*portion=*/0.05, /*speed=*/30.0);
    SetEgoCanbus(/*is_auto=*/false);

    mrc_request.set_urgency_stop_level(
        mrc::pb::UrgencyStopLevel::STOP_COMFORTABLY);
    CreateLaneFollowPath();
    UpdateWorldModel(mrc_request);
    SetShouldPlannerRespondMRC(/*should_planner_respond_mrc=*/true);
    Update();
    EXPECT_TRUE(Reason());

    EXPECT_EQ(2, constraints().size());
    const Constraint& added_constraint = constraints().back();
    EXPECT_EQ("MRC_STOP", added_constraint.generating_reasoner_id);
    EXPECT_EQ("mrc-urgency-hard", added_constraint.unique_constraint_id);
    EXPECT_EQ(pb::Constraint::STOP_POINT, added_constraint.type);

    EXPECT_EQ(1, added_constraint.states.size());
    const ConstraintState& state = added_constraint.states.back();
    EXPECT_NEAR(state.end_x(Discomforts::kMin) - trailing_bumper_offset, 49.859,
                1e-3);
    const auto* lane = GetLaneFromId(10527);
    EXPECT_NEAR(lane->length() * 0.05,
                33.0 + world_context().ra_to_leading_bumper() - 34.975, 1e-3);

    constraints_.clear();
  }
}

}  // namespace speed
}  // namespace planner
