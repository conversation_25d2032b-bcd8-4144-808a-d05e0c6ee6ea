#ifndef OFFBOARD_FALLBACK_ADS_COMMON_MATH_LIBRARY_CAN_UTIL_H_
#define OFFBOARD_FALLBACK_ADS_COMMON_MATH_LIBRARY_CAN_UTIL_H_

#include <cstddef>
#include <cstdint>

namespace fallback_adas {
namespace common {

void Uint64ToBytes(uint64_t value, uint8_t* array, std::size_t size,
                   bool is_big_endian);

void BytesToUint64(const uint8_t* array, std::size_t size, uint64_t* value,
                   bool is_big_endian);

}  // namespace common
}  // namespace fallback_adas

#endif  // OFFBOARD_FALLBACK_ADS_COMMON_MATH_LIBRARY_CAN_UTIL_H_
