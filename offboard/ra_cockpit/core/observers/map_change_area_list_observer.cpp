#include "map_change_area_list_observer.h"

#include <utility>

#include "av_comm/topics.h"
#include "glog/logging.h"

namespace remote_assist {

MapChangeAreaListObserver::MapChangeAreaListObserver(
    MapChangeAreaListCallback callback)
    : callback_(std::move(callback)) {}

bool MapChangeAreaListObserver::IsInterestedIn(
    const teleassist::pb::TeleassistRequest& message) {
  return message.message_type() == kMapChangeAreaList;
}

std::shared_ptr<google::protobuf::Message> MapChangeAreaListObserver::OnMessage(
    const teleassist::pb::TeleassistRequest& message,
    DataChannel data_channel) {
  // 首先解析外层的 MapChangeAreaList 消息
  teleassist::pb::MapChangeAreaList map_change_area_list_msg;
  if (!map_change_area_list_msg.ParseFromString(message.message_data())) {
    LOG(ERROR) << "Failed to parse MapChangeAreaList message, len = "
               << message.message_data().length();
    return nullptr;
  }

  // 然后获取内部的 ConstructionZoneList
  auto construction_zone_list = std::make_shared<voy::ConstructionZoneList>(
      map_change_area_list_msg.map_change_zone_list());

  LOG(INFO) << "Received MapChangeAreaList with "
            << construction_zone_list->construction_zones_size()
            << " construction zones and "
            << construction_zone_list->construction_zones_real_time_map_size()
            << " real-time map zones";

  if (callback_) {
    callback_(construction_zone_list, data_channel);
  }
  return construction_zone_list;
}

}  // namespace remote_assist
