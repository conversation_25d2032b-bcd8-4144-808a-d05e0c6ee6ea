#include "vehicle_assist_state_observer.h"

#include <utility>

#include "av_comm/topics.h"
#include "glog/logging.h"

namespace remote_assist {

VehicleAssistStateObserver::VehicleAssistStateObserver(
    VehicleAssistStateCallback callback)
    : vehicle_assist_state_callback_(std::move(callback)) {}

bool VehicleAssistStateObserver::IsInterestedIn(
    const teleassist::pb::TeleassistRequest& message) {
  return (message.message_type() == kVehicleAssistState);
}

std::shared_ptr<google::protobuf::Message>
VehicleAssistStateObserver::OnMessage(
    const teleassist::pb::TeleassistRequest& message,
    DataChannel data_channel) {
  auto vehicle_assist_state =
      std::make_shared<teleassist::pb::VehicleAssistState>();
  if (!vehicle_assist_state->ParseFromString(message.message_data())) {
    LOG(ERROR) << "Received error VehicleAssistState message format, len = "
               << message.message_data().length();
    return nullptr;
  }

  if (vehicle_assist_state_callback_) {
    vehicle_assist_state_callback_(vehicle_assist_state, data_channel);
  }
  return vehicle_assist_state;
}

}  // namespace remote_assist
