#include "planner/utility/path/juke_path_costing_util.h"

#include "math/math_util.h"
#include "math/range.h"

#include "planner/planning_gflags.h"

namespace {

using StateIndex = planner::path::JukeIntegratedPathFormula::StateIndex;
using ControlIndex = planner::path::JukeIntegratedPathFormula::ControlIndex;
using WeightAdjustSpec =
    planner::path::JukeIntegratedPathFormula::PbWeightAdjustSpec;

constexpr int X_DIM =
    planner::path::JukeIntegratedPathFormula::StateIndex::X_DIM;
[[maybe_unused]] constexpr int U_DIM =
    planner::path::JukeIntegratedPathFormula::ControlIndex::U_DIM;

// A buffer added to curvature limit, to avoid the violation of its kinematics
// limit at large turning scenario.
constexpr double kCurvatureBarrierTolerance = 0.004;
// The Volvo_xc90 max steering angle 535 degree, relative wheel angle 0.61 rad
// , max curvature 0.234. We added a curvature tolerance buffer to be 0.225,
// and the relative steering angel 520 degree.
constexpr double kCurvatureBufferForController = 0.009;
// The mild juke used to estimate the max steering rate in normal driving
// context.
constexpr double kMildJukeForMaxSteeringRateMpsss = 2.0;
// the min speed below which we don't apply experienced model to limit steering
// rate, but only limit it by vehicle exxcutor capability. To keep continuous,
// we apply kTruncationSteeringRate under super low speed.
constexpr double kMinSpeedForSteeringRateLimit = 0.1;
constexpr double kSqrMinSpeedForSteeringRateLimit =
    math::Sqr(kMinSpeedForSteeringRateLimit);
// The truncated steering rate, the speed based steering rate limit should never
// exceed this value. Unit: rad/s.
constexpr double kTruncationSteeringRate = 0.3154159;
// The attraction factor for lane keep.
constexpr double kAttractionFactorForLaneKeep = 1.6;
// To keep cost system stable, the number of attraction sources
// for one single ddp state is limited to this threshold.
constexpr int kMaxAttractionSourceForOneState = 3;
// Additional param for ml sigmoid region length when ignore all.
constexpr double kAdditionalParamForMlCostingIgnore = 0.2;
// Additional param for ml sigmoid region length when homotopy is not ignore
// all.
constexpr double kAdditionalParamForMlCostingNonIgnore = 0.4;

// Returns weight adjust coefficient based on attraction strength.
double GetAttractionWeightAdjust(
    planner::path::AttractionStrength strength,
    const control_util::pb::WeightAdjustForAttraction& weight_adjust) {
  switch (strength) {
    case planner::path::AttractionStrength::kWeak:
      return weight_adjust.for_weak();
      break;
    case planner::path::AttractionStrength::kModerate:
      return weight_adjust.for_moderate();
      break;
  }
  DCHECK(false) << "Wrong strength type!";
}

// Returns additional param used for attraction segment costing.
std::optional<double> GetAdditionalParamForAttraction(
    planner::path::AttractionSource attraction_source,
    planner::pb::IntentionResult::IntentionHomotopy homotopy) {
  // TODO(guanao): add more logic to handle other types of attraction.
  if (attraction_source != planner::path::AttractionSource::kML) {
    return std::nullopt;
  }
  return homotopy == planner::pb::IntentionResult::IGNORE_ALL
             ? std::optional<double>(kAdditionalParamForMlCostingIgnore)
             : std::optional<double>(kAdditionalParamForMlCostingNonIgnore);
}

}  // namespace

namespace planner {
namespace path {
double GetReferenceSpeedSquare(
    const std::vector<planner::primitive::SpeedReferenceRegionPrimitive>&
        speed_regions,
    const State& x, double ego_speed_squared,
    math::geometry::PolylineCurve2dProximityCache* cached_poly) {
  if (speed_regions.empty()) {
    return ego_speed_squared;
  }

  const math::EVector<2> query_pt =
      (math::EVector<2>() << x[StateIndex::X_POS], x[StateIndex::Y_POS])
          .finished();
  double dist = 0.0;
  math::EVector<2> d_dist = math::EVector<2>::Zero();
  double arclength = 0.0;
  DCHECK_NOTNULL(cached_poly)->Query(query_pt, &dist, &d_dist, &arclength);

  if (arclength < speed_regions[0].region.start_pos) {
    return math::Sqr(speed_regions[0].speed_reference);
  }

  if (arclength > speed_regions.back().region.end_pos) {
    return math::Sqr(speed_regions.back().speed_reference);
  }
  // Determine the reference region based on arclength to extract the
  // corresponding speed.
  auto lower_bound_it = std::lower_bound(
      speed_regions.begin(), speed_regions.end(), arclength,
      [](const planner::primitive::SpeedReferenceRegionPrimitive& a, double b) {
        return a.region.start_pos < b;
      });

  if (lower_bound_it == speed_regions.begin()) {
    return math::Sqr(speed_regions.begin()->speed_reference);
  }
  return math::Sqr((*(lower_bound_it - 1)).speed_reference);
}

bool ConstraintKappa(const State& x,
                     const vehicle_model::pb::JukeIntegratedModelLimit& limit,
                     Feature* feature) {
  const double kappa = x[StateIndex::KAPPA];

  const double kappa_violation = control_util::ComputeViolation(
      kappa,
      limit.kappa_limit().min_val() + kCurvatureBufferForController +
          kCurvatureBarrierTolerance,
      limit.kappa_limit().max_val() - kCurvatureBufferForController -
          kCurvatureBarrierTolerance);

  feature->components.resize(1);
  feature->components[0].val = kappa_violation;
  if (!math::NearZero(feature->components[0].val)) {
    feature->components[0].jacob[StateIndex::KAPPA] = 1.0;
  }
  return true;
}

bool ALConstraintKappa(const State& x,
                       const vehicle_model::pb::JukeIntegratedModelLimit& limit,
                       Feature* feature) {
  const double kappa = x[StateIndex::KAPPA];

  // NOTE(path): Considering that not all costs are computed using
  // ComputeViolation, so here we still prefer to set the upper and lower
  // constraints when calculating the juke cost.
  feature->components.resize(2);
  feature->components[0].val =
      kappa - (limit.kappa_limit().max_val() - kCurvatureBufferForController -
               kCurvatureBarrierTolerance);
  feature->components[0].jacob[StateIndex::KAPPA] = 1.0;
  feature->components[1].val =
      -kappa + (limit.kappa_limit().min_val() + kCurvatureBufferForController +
                kCurvatureBarrierTolerance);
  feature->components[1].jacob[StateIndex::KAPPA] = -1.0;
  return true;
}

bool ConstraintEta(const State& x,
                   const vehicle_model::pb::JukeIntegratedModelLimit& limit,
                   Feature* feature) {
  const double eta = x[StateIndex::ETA];
  const double eta_violation = control_util::ComputeViolation(
      eta, limit.eta_limit().min_val(), limit.eta_limit().max_val());

  feature->components.resize(1);
  feature->components[0].val = eta_violation;
  if (!math::NearZero(feature->components[0].val)) {
    feature->components[0].jacob[StateIndex::ETA] = 1.0;
  }
  return true;
}

bool ConstraintGamma(const Control& u, double max_abs_gamma, Feature* feature) {
  DCHECK_GT(max_abs_gamma, 0.0);
  const double gamma = u[ControlIndex::GAMMA];
  const double gamma_violation =
      control_util::ComputeViolation(gamma, -max_abs_gamma, max_abs_gamma);

  feature->components.resize(1);
  feature->components[0].val = gamma_violation;
  if (!math::NearZero(feature->components[0].val)) {
    feature->components[0].jacob[X_DIM + ControlIndex::GAMMA] = 1.0;
  }

  return true;
}

bool ConstraintApproximateLateralAccel(
    const State& x, double speed_reference, double max_abs_lateral_accel_mpss,
    math::geometry::PolylineCurve2dProximityCache* /*cached_poly*/,
    Feature* feature) {
  const double speed_reference_squared = math::Sqr(speed_reference);

  const double lateral_accel = x[StateIndex::KAPPA] * speed_reference_squared;
  const double lateral_accel_violation = control_util::ComputeViolation(
      lateral_accel, -max_abs_lateral_accel_mpss, max_abs_lateral_accel_mpss);

  feature->components.resize(1);
  feature->components[0].val = lateral_accel_violation;
  if (!math::NearZero(feature->components[0].val)) {
    feature->components[0].jacob[StateIndex::KAPPA] = speed_reference_squared;
  }

  return true;
}

bool ConstraintApproximateLateralJerk(
    const State& x, double speed_reference, double max_abs_lateral_jerk_mpss,
    math::geometry::PolylineCurve2dProximityCache* /*cached_poly*/,
    Feature* feature) {
  const double speed_reference_cube = math::Cube(speed_reference);
  const double lateral_jerk = x[StateIndex::ETA] * speed_reference_cube;
  const double lateral_jerk_violation = control_util::ComputeViolation(
      lateral_jerk, -max_abs_lateral_jerk_mpss, max_abs_lateral_jerk_mpss);

  feature->components.resize(1);
  feature->components[0].val = lateral_jerk_violation;
  if (!math::NearZero(feature->components[0].val)) {
    feature->components[0].jacob[StateIndex::ETA] = speed_reference_cube;
  }

  return true;
}

void ConstraintTerminalHeading(
    const State& x, double target_heading, double heading_tolerance, int step,
    int total_step_size, double weight_adjust,
    math::Range<int> target_index_range,
    const control_util::pb::CostingWeightAdjustSpec& weight_adjust_spec,
    Feature* feature) {
  (void)total_step_size;
  const int range_length =
      target_index_range.end_pos - target_index_range.start_pos;
  if (!math::IsInRange(step, target_index_range.start_pos,
                       target_index_range.end_pos) ||
      range_length == 0) {
    return;
  }
  const double heading = x[StateIndex::THETA];
  const double heading_diff = math::AngleDiff(heading, target_heading);
  const double heading_violation = control_util::ComputeViolation(
      heading_diff, -heading_tolerance, heading_tolerance);

  feature->components.resize(1);
  feature->components[0].val = math::Radian2Degree(heading_violation);
  if (!math::NearZero(feature->components[0].val)) {
    feature->components[0].jacob[StateIndex::THETA] = 180.0 / M_PI;
  }
  DCHECK(range_length != 0);
  feature->components[0].weight_adjust =
      weight_adjust *
      planner::path::GetCostWeightByType(
          weight_adjust_spec.type(),
          static_cast<double>(step - target_index_range.start_pos) /
              range_length,
          weight_adjust_spec.weight_limit().min_val(),
          weight_adjust_spec.weight_limit().max_val());
}

void PrimitiveSoftPathAttraction(
    const math::geometry::Point2d& front_bumper_pos, double theta,
    const vehicle_model::pb::JukeIntegratedMotionModelParam& param,
    const vehicle_model::pb::AxleRectangularMeasurement& shape_measurement,
    const std::vector<planner::primitive::WeightAdjustedRegionPrimitive>&
        weight_adjusted_regions,
    int step, int total_step_size,
    math::geometry::PolylineCurve2dProximityCache* cached_poly,
    const control_util::pb::CostingWeightAdjustSpec& weight_adjust_spec,
    bool should_enhance_attraction, Feature* feature) {
  (void)param;
  const double l =
      vehicle_model::GetWheelBaseFromShapeMeasurement(shape_measurement) +
      shape_measurement.front_bumper_to_front_axle();

  math::EVector<2> query_pt;
  query_pt << front_bumper_pos.x(), front_bumper_pos.y();

  double dist = 0.0;
  math::EVector<2> d_dist = math::EVector<2>::Zero();
  double arclength = 0.0;
  if (cached_poly == nullptr) return;

  cached_poly->Query(query_pt, &dist, &d_dist, &arclength);

  feature->components.resize(1);
  auto& first_component = feature->components[0];
  first_component.val = dist;
  first_component.jacob[StateIndex::X_POS] = d_dist(0);
  first_component.jacob[StateIndex::Y_POS] = d_dist(1);
  first_component.jacob[StateIndex::THETA] =
      -l * sin(theta) * d_dist(0) + l * cos(theta) * d_dist(1);

  for (const auto& weight_adjusted_region : weight_adjusted_regions) {
    if (weight_adjusted_region.region.start_pos < arclength &&
        arclength < weight_adjusted_region.region.end_pos) {
      first_component.weight_adjust = weight_adjusted_region.weight_adjust;
      break;
    }
  }

  first_component.weight_adjust *=
      (should_enhance_attraction ? kAttractionFactorForLaneKeep : 1.0) *
      planner::path::GetCostWeightByType(
          weight_adjust_spec.type(),
          static_cast<double>(step) / total_step_size,
          weight_adjust_spec.weight_limit().min_val(),
          weight_adjust_spec.weight_limit().max_val());
}

void ComputeReferenceHeadingAttraction(
    const State& x, double heading_tolerance, int step, int total_step_size,
    math::geometry::PolylineCurve2dProximityCache* cached_poly,
    const control_util::pb::CostingWeightAdjustSpec& weight_adjust_spec,
    bool should_enhance_attraction, Feature* feature) {
  if (cached_poly == nullptr) {
    return;
  }
  math::EVector<2> query_pt;
  query_pt << x[StateIndex::X_POS], x[StateIndex::Y_POS];

  double dist = 0.0;
  math::EVector<2> d_dist = math::EVector<2>::Zero();
  double arclength = 0.0;

  cached_poly->Query(query_pt, &dist, &d_dist, &arclength);

  const double heading = x[StateIndex::THETA];
  const double reference_heading =
      cached_poly->curve().GetInterpTheta(arclength);
  const double heading_diff = math::AngleDiff(heading, reference_heading);
  const double heading_violation = control_util::ComputeViolation(
      heading_diff, -heading_tolerance, heading_tolerance);

  feature->components.resize(1);
  feature->components[0].val = heading_violation;
  if (!math::NearZero(feature->components[0].val)) {
    feature->components[0].jacob[StateIndex::THETA] = 1.0;
  }

  feature->components[0].weight_adjust *=
      (should_enhance_attraction ? kAttractionFactorForLaneKeep : 1.0) *
      planner::path::GetCostWeightByType(
          weight_adjust_spec.type(),
          static_cast<double>(step) / total_step_size,
          weight_adjust_spec.weight_limit().min_val(),
          weight_adjust_spec.weight_limit().max_val());
}

void ComputeReferenceCurvatureAttraction(
    const State& x, double curvature_tolerance, int step, int total_step_size,
    math::geometry::PolylineCurve2dProximityCache* cached_poly,
    const adv_geom::PathCurve2d& attraction_path_curve,
    const control_util::pb::CostingWeightAdjustSpec& weight_adjust_spec,
    bool should_enhance_attraction, Feature* feature) {
  if (cached_poly == nullptr) {
    return;
  }
  math::EVector<2> query_pt;
  query_pt << x[StateIndex::X_POS], x[StateIndex::Y_POS];

  double dist = 0.0;
  math::EVector<2> d_dist = math::EVector<2>::Zero();
  double arclength = 0.0;

  cached_poly->Query(query_pt, &dist, &d_dist, &arclength);

  const double curvature = x[StateIndex::KAPPA];

  const double curvature_diff =
      curvature - attraction_path_curve.GetInterpCurvature(arclength);
  const double curvature_violation = control_util::ComputeViolation(
      curvature_diff, -curvature_tolerance, curvature_tolerance);

  feature->components.resize(1);
  feature->components[0].val = curvature_violation;
  if (!math::NearZero(feature->components[0].val)) {
    feature->components[0].jacob[StateIndex::KAPPA] = 1.0;
  }

  feature->components[0].weight_adjust *=
      (should_enhance_attraction ? kAttractionFactorForLaneKeep : 1.0) *
      planner::path::GetCostWeightByType(
          weight_adjust_spec.type(),
          static_cast<double>(step) / total_step_size,
          weight_adjust_spec.weight_limit().min_val(),
          weight_adjust_spec.weight_limit().max_val());
}

void EffortKappa(const State& x, Feature* feature) {
  const double kappa = x[StateIndex::KAPPA];
  feature->components.resize(1);
  feature->components[0].val = kappa;
  feature->components[0].jacob[StateIndex::KAPPA] = 1.0;
}

void EffortEta(const State& x, Feature* feature) {
  const double eta = x[StateIndex::ETA];

  feature->components.resize(1);
  feature->components[0].val = eta;
  feature->components[0].jacob[StateIndex::ETA] = 1.0;
}

// Computes curvature 2nd order derivative(juke) effort. i.e., 2nd order
// derivative of curvature w.r.t delta-s.
void EffortGamma(const Control& u, Feature* feature) {
  const double gamma = u[ControlIndex::GAMMA];
  feature->components.resize(1);
  feature->components[0].val = gamma;
  feature->components[0].jacob[X_DIM + ControlIndex::GAMMA] = 1.0;
}

void EffortApproximateSteeringRate(
    const State& x, double wheel_base, double speed_reference, int step,
    int total_step_size,
    math::geometry::PolylineCurve2dProximityCache* /*cached_poly*/,
    const control_util::pb::CostingWeightAdjustSpec& weight_adjust_spec,
    Feature* feature) {
  const double speed_reference_squared = math::Sqr(speed_reference);
  const double denominator =
      1 / (1 + x[StateIndex::KAPPA] * x[StateIndex::KAPPA] * wheel_base *
                   wheel_base);
  const double coefficient =
      wheel_base * sqrt(speed_reference_squared) * denominator;
  const double omega = x[StateIndex::ETA] * coefficient;

  feature->components.resize(1);
  feature->components[0].val = omega;
  feature->components[0].jacob[StateIndex::KAPPA] =
      -2 * wheel_base * wheel_base * x[StateIndex::KAPPA] * omega * denominator;
  feature->components[0].jacob[StateIndex::ETA] = coefficient;
  feature->components[0].weight_adjust = planner::path::GetCostWeightByType(
      weight_adjust_spec.type(), static_cast<double>(step) / total_step_size);
}

// Computes approximate steering accel effort cost.
void EffortApproximateSteeringAccel(
    const Control& u, double wheel_base, double speed_reference, int step,
    int total_step_size,
    const control_util::pb::CostingWeightAdjustSpec& weight_adjust_spec,
    Feature* feature) {
  const double wheel_base_mul_speed_square =
      wheel_base * math::Sqr(speed_reference);
  const double approximate_steering_accel =
      wheel_base_mul_speed_square * u[ControlIndex::GAMMA];

  feature->components.resize(1);
  feature->components[0].val = approximate_steering_accel;
  feature->components[0].jacob[X_DIM + ControlIndex::GAMMA] =
      wheel_base_mul_speed_square;
  feature->components[0].weight_adjust = planner::path::GetCostWeightByType(
      weight_adjust_spec.type(), static_cast<double>(step) / total_step_size,
      weight_adjust_spec.weight_limit().min_val(),
      weight_adjust_spec.weight_limit().max_val());
}

// Computes approximate lateral acceleration effort.
bool EffortApproximateLateralAccel(
    const State& x, double speed_reference, int step, int total_step_size,
    const control_util::pb::CostingWeightAdjustSpec& weight_adjust_spec,
    math::geometry::PolylineCurve2dProximityCache* /*cached_poly*/,
    Feature* feature) {
  const double speed_reference_squared = math::Sqr(speed_reference);
  const double lateral_accel = x[StateIndex::KAPPA] * speed_reference_squared;

  feature->components.resize(1);
  feature->components[0].val = lateral_accel;
  if (!math::NearZero(feature->components[0].val)) {
    feature->components[0].jacob[StateIndex::KAPPA] = speed_reference_squared;
  }
  feature->components[0].weight_adjust = planner::path::GetCostWeightByType(
      weight_adjust_spec.type(), static_cast<double>(step) / total_step_size);
  return true;
}

bool EffortApproximateLateralJerk(
    const State& x, double speed_reference, int step, int total_step_size,
    const control_util::pb::CostingWeightAdjustSpec& weight_adjust_spec,
    math::geometry::PolylineCurve2dProximityCache* /*cached_poly*/,
    Feature* feature) {
  const double speed_reference_cube = math::Cube(speed_reference);

  const double lateral_jerk = x[StateIndex::ETA] * speed_reference_cube;

  feature->components.resize(1);
  feature->components[0].val = lateral_jerk;
  if (!math::NearZero(feature->components[0].val)) {
    feature->components[0].jacob[StateIndex::ETA] = speed_reference_cube;
  }
  feature->components[0].weight_adjust = planner::path::GetCostWeightByType(
      weight_adjust_spec.type(), static_cast<double>(step) / total_step_size,
      weight_adjust_spec.weight_limit().min_val(),
      weight_adjust_spec.weight_limit().max_val());
  return true;
}

math::Range1d ComputeSteeringRateLimit(double wheel_base,
                                       double ego_speed_squared,
                                       double max_abs_steering_rate) {
  if (ego_speed_squared < kSqrMinSpeedForSteeringRateLimit) {
    return {-max_abs_steering_rate, max_abs_steering_rate};
  }

  DCHECK(!math::NearZero(ego_speed_squared));
  const double steering_rate_limit = std::min(
      kMildJukeForMaxSteeringRateMpsss * wheel_base / ego_speed_squared,
      max_abs_steering_rate);
  DCHECK_GT(steering_rate_limit, 0.0);
  return {-steering_rate_limit, steering_rate_limit};
}

bool PathAttractionWithTolerance(
    const math::geometry::Point2d& front_bumper_pos, double theta,
    const double cons_min, const double cons_max,
    const vehicle_model::pb::JukeIntegratedMotionModelParam& /* param */,
    const vehicle_model::pb::AxleRectangularMeasurement& shape_measurement,
    int /* step */, int /* total_step_size */, double weight_adjust,
    const std::optional<double>& additional_param,
    math::geometry::PolylineCurve2dProximityCache* cached_poly,
    FeatureLet* component) {
  if (cached_poly == nullptr) {
    return false;
  }
  const double l =
      vehicle_model::GetWheelBaseFromShapeMeasurement(shape_measurement) +
      shape_measurement.front_bumper_to_front_axle();

  math::EVector<2> query_pt;
  query_pt << front_bumper_pos.x(), front_bumper_pos.y();
  double dist = 0.0;
  math::EVector<2> d_dist = math::EVector<2>::Zero();
  cached_poly->Query(query_pt, &dist, &d_dist);

  const double dist_violation =
      control_util::ComputeViolation(dist, cons_min, cons_max);
  component->additional_param = additional_param;
  component->val = dist_violation;
  if (!math::NearZero(component->val)) {
    component->jacob[StateIndex::X_POS] = d_dist(0);
    component->jacob[StateIndex::Y_POS] = d_dist(1);
    component->jacob[StateIndex::THETA] =
        -l * sin(theta) * d_dist(0) + l * cos(theta) * d_dist(1);
  }
  component->weight_adjust = weight_adjust;
  return true;
}

void ConstraintApproximateSteeringAccel(const Control& u,
                                        double speed_reference,
                                        double wheel_base,
                                        double max_abs_steering_accel,
                                        Feature* feature) {
  const double speed_reference_squared = math::Sqr(speed_reference);
  const double wheel_base_mul_speed_square =
      wheel_base * speed_reference_squared;

  const double approximate_steering_accel =
      wheel_base_mul_speed_square * u[ControlIndex::GAMMA];
  const double approximate_steering_accel_violation =
      control_util::ComputeViolation(approximate_steering_accel,
                                     max_abs_steering_accel);

  feature->components.resize(1);
  feature->components[0].val = approximate_steering_accel_violation;
  if (!math::NearZero(approximate_steering_accel_violation)) {
    feature->components[0].jacob[X_DIM + ControlIndex::GAMMA] =
        wheel_base_mul_speed_square;
  }
}

void ALConstraintApproximateSteeringAccel(const Control& u,
                                          double speed_reference,
                                          double wheel_base,
                                          double max_abs_steering_accel,
                                          Feature* feature) {
  const double speed_reference_squared = math::Sqr(speed_reference);
  const double wheel_base_mul_speed_square =
      wheel_base * speed_reference_squared;

  const double approximate_steering_accel =
      wheel_base_mul_speed_square * u[ControlIndex::GAMMA];
  feature->components.resize(2);
  feature->components[0].val =
      approximate_steering_accel - max_abs_steering_accel;
  feature->components[0].jacob[X_DIM + ControlIndex::GAMMA] =
      wheel_base_mul_speed_square;
  feature->components[1].val =
      -approximate_steering_accel - max_abs_steering_accel;
  feature->components[1].jacob[X_DIM + ControlIndex::GAMMA] =
      -wheel_base_mul_speed_square;
}

void ComputeAttractionSegmentCost(
    const math::geometry::Point2d& front_bumper_pos, double theta,
    const path_problem::Attractions& attractions,
    const primitive::PathManeuverPrimitive& maneuver_primitive,
    const WeightAdjustSpec& weight_adjust_spec,
    const vehicle_model::pb::JukeIntegratedMotionModelParam& param,
    const vehicle_model::pb::AxleRectangularMeasurement& shape_measurement,
    int step, int total_step_size, Feature* feature) {
  const auto& ddp_state_ix_to_segment_ix_map =
      attractions.ddp_state_ix_to_segment_ix_map();
  const auto attraction_indices = ddp_state_ix_to_segment_ix_map.find(step);
  if (attraction_indices == ddp_state_ix_to_segment_ix_map.end() ||
      attraction_indices->second.empty()) {
    return;
  }
  const int attractions_cnt = attraction_indices->second.size();
  DCHECK_LT(attractions_cnt, kMaxAttractionSourceForOneState);
  feature->components.resize(attractions_cnt);
  for (size_t i = 0; i < attraction_indices->second.size(); ++i) {
    const auto [segment_idx, range_idx] = attraction_indices->second[i];
    const auto& attraction_segment =
        attractions.attraction_segments()[segment_idx];
    const auto& attraction_range =
        attraction_segment.attraction_ranges()[range_idx];
    DCHECK(attraction_range.is_valid());
    DCHECK(weight_adjust_spec.has_weight_adjust_for_attraction());
    double weight_adjust = GetAttractionWeightAdjust(
        attraction_range.strength(),
        weight_adjust_spec.weight_adjust_for_attraction());
    const auto attraction_index_range =
        attraction_range.attraction_index_range();
    // TODO(huanhui): Define cost decay util functions and use them here.
    if (math::IsValidRange(attraction_index_range) &&
        math::IsInRange(step, attraction_index_range.start_pos,
                        attraction_index_range.end_pos)) {
      DCHECK(!math::NearZero(attraction_index_range.end_pos));
      weight_adjust *= planner::path::GetCostWeightByType(
          weight_adjust_spec.type(),
          static_cast<double>(step - attraction_index_range.start_pos) /
              (attraction_index_range.end_pos -
               attraction_index_range.start_pos),
          /*weight_lower_bound=*/0.0, /*weight_upper_bound=*/1.0);
    }

    const std::optional<double> additional_param =
        GetAdditionalParamForAttraction(attraction_segment.attraction_source(),
                                        maneuver_primitive.intention_homotopy);

    PathAttractionWithTolerance(
        front_bumper_pos, theta, attraction_range.lateral_range().start_pos,
        attraction_range.lateral_range().end_pos, param, shape_measurement,
        step, total_step_size, weight_adjust, additional_param,
        attraction_segment.attraction_polyline().get(),
        &feature->components[i]);
  }
}

void ConstraintApproximateSteeringRate(
    const State& x, double ego_speed_squared, double speed_reference,
    double wheel_base,
    math::geometry::PolylineCurve2dProximityCache* /*cached_poly*/,
    Feature* feature) {
  const double speed_reference_squared = math::Sqr(speed_reference);

  const auto steering_rate_limit = ComputeSteeringRateLimit(
      wheel_base, ego_speed_squared, kTruncationSteeringRate);

  const double sqr_cos_steering =
      1 / (1 + x[StateIndex::KAPPA] * x[StateIndex::KAPPA] * wheel_base *
                   wheel_base);
  const double coefficient =
      wheel_base * sqrt(speed_reference_squared) * sqr_cos_steering;
  const double steering_rate = x[StateIndex::ETA] * coefficient;

  const double steering_rate_violation =
      control_util::ComputeViolation(steering_rate, steering_rate_limit);

  feature->components.resize(1);
  feature->components[0].val = steering_rate_violation;
  if (!math::NearZero(steering_rate_violation)) {
    feature->components[0].jacob[StateIndex::KAPPA] =
        -2 * wheel_base * wheel_base * x[StateIndex::KAPPA] * steering_rate *
        sqr_cos_steering;
    feature->components[0].jacob[StateIndex::ETA] = coefficient;
  }
}

}  // namespace path
}  // namespace planner
