#ifndef OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_DEGRADATION_INFO_OBSERVER_H_
#define OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_DEGRADATION_INFO_OBSERVER_H_

#include <functional>
#include <memory>

#include "../data_observer.h"
#include "protos/ra_cockpit.pb.h"

namespace remote_assist {

class DegradationInfoObserver : public IDataObserver {
 public:
  using DegradationInfoCallback = std::function<void(
      std::shared_ptr<ra_cockpit::pb::DegradationInfo>, DataChannel)>;

  explicit DegradationInfoObserver(DegradationInfoCallback callback);

  bool IsInterestedIn(
      const teleassist::pb::TeleassistRequest& message) override;

  std::shared_ptr<google::protobuf::Message> OnMessage(
      const teleassist::pb::TeleassistRequest& message,
      DataChannel data_channel) override;

 private:
  DegradationInfoCallback degradation_info_callback_;
};

}  // namespace remote_assist

#endif  // OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_DEGRADATION_INFO_OBSERVER_H_
