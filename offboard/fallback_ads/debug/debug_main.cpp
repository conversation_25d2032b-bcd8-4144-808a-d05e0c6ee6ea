#include "debug/debug_main.h"

#include <cstdint>

#include "_out/Appl/GenData/Components/Rte_Debug.h"
#include "_out/Appl/GenData/Rte_Type.h"
#include "debug/control_debug_encode/control_debug_encode.h"
#include "debug/estimation_debug_encode/estimation_debug_encode.h"

static ControlDebug* ReadControlDebug() {
  return Rte_IRead_Debug_60ms_ControlDebug_ControlDebug();
}

static EstimationDebug* ReadEstimationDebug() {
  return Rte_IRead_Debug_60ms_EstimationDebug_EstimationDebug();
}

extern "C" void Debug_Init() {}

extern "C" void Debug_60ms() {
  // read signals from autosar rte
  const ControlDebug& control_debug = *ReadControlDebug();
  const EstimationDebug& estimation_debug = *ReadEstimationDebug();

  // encode signals to autosar rte
  fallback_adas::debug::ControlDebugEncode(control_debug);
  fallback_adas::debug::EstimationDebugEncode(estimation_debug);
}
