#include "planner/path/reasoning/agent_intention/lateral_blocking_state_generator/sequence_blocking_state_computer.h"

#include <algorithm>
#include <limits>
#include <utility>
#include <vector>

#include "geometry/model/polygon_with_cache.h"
#include "math/enum.h"
#include "math/math_util.h"
#include "math/range.h"
#include "planner/behavior/util/agent_state/agent_in_lane_param_generation.h"
#include "planner/behavior/util/agent_state/point_in_lane_param.h"

namespace planner {
namespace path {
namespace {
// The invalid index.
constexpr int kInvalidIndex = -1;

// Gets blocking sequence type from blocking state.
pb::BlockingSequenceType CovertToSequenceBlockingType(
    pb::LateralBlockingState::BlockingState blocking_state) {
  switch (blocking_state) {
    case pb::LateralBlockingState::NON_BLOCKING:
      return pb::BlockingSequenceType::kNonBlocking;
    case pb::LateralBlockingState::SOFT_BLOCKING:
      return pb::BlockingSequenceType::kPartialBlocking;
    case pb::LateralBlockingState::HARD_BLOCKING:
      return pb::BlockingSequenceType::kCompleteBlocking;
    default:
      return pb::BlockingSequenceType::kMixedBlocking;
  }
}

// Gets the two blocking sequences gap.
int GetBlockingSequenceIndexGap(const pb::BlockingSequence& lhs,
                                const pb::BlockingSequence& rhs) {
  return rhs.range().start_index() - lhs.range().end_index() - 1;
}

// Returns the blocking sequence length.
int GetBlockingSequenceLength(const pb::BlockingSequence& sequence) {
  return sequence.range().end_index() - sequence.range().start_index() + 1;
}
}  // namespace

SequenceBlockingStateComputer::SequenceBlockingStateComputer(
    const DrivableSpaceCorridor& drivable_space_corridor,
    const math::geometry::PolylineCurve2d& nominal_path,
    const math::geometry::PolygonWithCache2d& drivable_space_corridor_contour,
    const EgoInLaneParams& ego_in_lane_params, double lateral_clearance_buffer)
    : left_drivable_space_boundary_(
          drivable_space_corridor.driving_corridor.left_boundary),
      right_drivable_space_boundary_(
          drivable_space_corridor.driving_corridor.right_boundary),
      reference_line_(nominal_path),
      drivable_space_polygon_(drivable_space_corridor_contour),
      ego_in_lane_params_(ego_in_lane_params),
      lateral_clearance_buffer_(lateral_clearance_buffer) {}

AgentBlockingSequenceInfo SequenceBlockingStateComputer::Compute(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data) const {
  AgentBlockingSequenceInfo blocking_sequence_info(blocking_state_data);
  const auto& primary_trajectory_state_iter =
      agent_inlane_state.predicted_trajectories.find(
          agent_inlane_state.primary_trajectory_id);
  if (primary_trajectory_state_iter ==
      agent_inlane_state.predicted_trajectories.end()) {
    DCHECK(false) << "primary trajectory should not be missed";
    return blocking_sequence_info;
  }

  const auto& primary_trajectory_state = primary_trajectory_state_iter->second;
  if (primary_trajectory_state.predicted_states.empty()) {
    return blocking_sequence_info;
  }

  const std::vector<math::Range<int>>& effective_ranges =
      blocking_state_data.effective_intention_index_ranges;
  if (effective_ranges.empty()) {
    return blocking_sequence_info;
  }

  // Effective ranges are the agent prediction states which has overlap with the
  // s-t area bounded by the speed upper and lower bound. We only need to
  // recompute those blocking states.
  std::vector<SnapshotBlockingState> sequence_blocking_states;
  sequence_blocking_states.reserve(effective_ranges.back().end_pos -
                                   effective_ranges.front().start_pos + 1);
  for (const math::Range<int>& effective_range : effective_ranges) {
    ComputeBlockingStateForPosesInRange(
        primary_trajectory_state, effective_range, &sequence_blocking_states);
  }

  // Convert to blocking sequence.
  std::sort(sequence_blocking_states.begin(), sequence_blocking_states.end());
  const std::vector<pb::BlockingSequence> raw_blocking_sequence =
      GetBlockingSequences(sequence_blocking_states);

  // Reconstruct to the agent blocking sequence info.
  return ReconstructBlockingSequenceInfo(raw_blocking_sequence);
}

void SequenceBlockingStateComputer::ComputeBlockingStateForPosesInRange(
    const AgentTrajectoryInLaneStates& primary_trajectory_state,
    const math::Range<int>& effective_range,
    std::vector<SnapshotBlockingState>* sequence_blocking_states) const {
  DCHECK_LE(effective_range.start_pos, effective_range.end_pos);
  for (int index = effective_range.start_pos; index < effective_range.end_pos;
       ++index) {
    // Get agent snapshot pose.
    DCHECK_LT(index, primary_trajectory_state.predicted_states.size());
    const math::geometry::PolygonWithCache2d& snapshot_polygon =
        primary_trajectory_state.predicted_states[index]
            .inlane_state.inlane_param.pose.contour();
    sequence_blocking_states->emplace_back(
        index, GetClearanceBasedBlockingStateForSnapshot(snapshot_polygon));
  }
}

pb::LateralBlockingState::BlockingState
SequenceBlockingStateComputer::GetClearanceBasedBlockingStateForSnapshot(
    const math::geometry::PolygonWithCache2d& polygon) const {
  // Get points in lane parameters.
  const std::vector<PointInLaneParam> points_params =
      lane_selection::GetAgentContourPointsInLaneParams(
          reference_line_, left_drivable_space_boundary_,
          right_drivable_space_boundary_, drivable_space_polygon_, polygon);

  // Get left and right clearance.
  double left_clearance = std::numeric_limits<double>::max();
  double right_clearance = std::numeric_limits<double>::max();
  double min_dist_to_boundary = std::numeric_limits<double>::max();
  bool is_fully_out_drivable_space = true;
  bool exist_point_out_of_left_drivable_space_boundary = false;
  bool exist_point_out_of_right_drivable_space_boundary = false;
  for (const PointInLaneParam& point_param : points_params) {
    const bool is_in_drivable_space =
        point_param.left_boundary_dist.side == math::pb::Side::kRight &&
        point_param.right_boundary_dist.side == math::pb::Side::kLeft &&
        point_param.center_line_dist.relative_position ==
            math::RelativePosition::kWithIn;
    is_fully_out_drivable_space &= !is_in_drivable_space;
    exist_point_out_of_left_drivable_space_boundary |=
        point_param.left_boundary_dist.side == math::pb::Side::kLeft;
    exist_point_out_of_right_drivable_space_boundary |=
        point_param.right_boundary_dist.side == math::pb::Side::kRight;
    min_dist_to_boundary =
        std::min({point_param.left_boundary_dist.dist,
                  point_param.right_boundary_dist.dist, min_dist_to_boundary});
    if (is_in_drivable_space) {
      math::UpdateMin(point_param.left_boundary_dist.dist, left_clearance);
      math::UpdateMin(point_param.right_boundary_dist.dist, right_clearance);
    }
  }

  const bool has_crossed_left_boundary =
      exist_point_out_of_left_drivable_space_boundary &&
      (!is_fully_out_drivable_space ||
       exist_point_out_of_right_drivable_space_boundary);
  const bool has_crossed_right_boundary =
      exist_point_out_of_right_drivable_space_boundary &&
      (!is_fully_out_drivable_space ||
       exist_point_out_of_left_drivable_space_boundary);
  const bool has_no_overlap_with_drivable_space = is_fully_out_drivable_space &&
                                                  !has_crossed_left_boundary &&
                                                  !has_crossed_right_boundary;
  // Return NON_BLOCKING if the agent contour is fully out of the drivable space
  // boundary with a buffer.
  if (has_no_overlap_with_drivable_space &&
      min_dist_to_boundary > lateral_clearance_buffer_) {
    return pb::LateralBlockingState::NON_BLOCKING;
  }

  // Set across side clearance to 0.0, and return the blocking state based on
  // the greater clearance. Note that an extra buffer is considered here because
  // ego should keep a lateral gap to the nudged agent, and this lateral gap
  // should be greater than the min required lateral gap of all objects.
  left_clearance = has_crossed_left_boundary ? 0.0 : left_clearance;
  right_clearance = has_crossed_right_boundary ? 0.0 : right_clearance;
  return std::max(left_clearance, right_clearance) >
                 (ego_in_lane_params_.width_m + lateral_clearance_buffer_)
             ? pb::LateralBlockingState::SOFT_BLOCKING
             : pb::LateralBlockingState::HARD_BLOCKING;
}

std::vector<pb::BlockingSequence>
SequenceBlockingStateComputer::GetBlockingSequences(
    const std::vector<SnapshotBlockingState>& sequence_blocking_states) const {
  if (sequence_blocking_states.empty()) {
    return {};
  }

  std::vector<pb::BlockingSequence> blocking_sequences;
  blocking_sequences.reserve(sequence_blocking_states.size());

  // Declare the start_index, prev_index, and last_blocking_state to record the
  // effective ranges.
  int start_index = kInvalidIndex;
  int prev_end_index = kInvalidIndex;
  pb::LateralBlockingState::BlockingState last_blocking_state =
      pb::LateralBlockingState::NON_BLOCKING;

  // Lambda function to add new blocking sequence and update the start_index,
  // prev_index, and last_blocking_state.
  const auto add_blocking_sequence_and_update_index =
      [&start_index, &prev_end_index, &blocking_sequences,
       &last_blocking_state](
          const SnapshotBlockingState& current_state) -> void {
    // No need to add non-blocking sequence.
    if (last_blocking_state != pb::LateralBlockingState::NON_BLOCKING) {
      pb::BlockingSequence new_blocking_sequence;
      new_blocking_sequence.mutable_range()->set_start_index(start_index);
      new_blocking_sequence.mutable_range()->set_end_index(prev_end_index);
      new_blocking_sequence.set_blocking_state(last_blocking_state);
      blocking_sequences.emplace_back(std::move(new_blocking_sequence));
    }

    // Update the start_index, prev_end_index, and last_blocking_state of the
    // new round.
    start_index = current_state.first;
    prev_end_index = current_state.first;
    last_blocking_state = current_state.second;
  };

  for (const SnapshotBlockingState& snapshot_blocking_state :
       sequence_blocking_states) {
    // Init start_index, prev_end_index, and last_blocking_state.
    if (start_index == kInvalidIndex) {
      start_index = snapshot_blocking_state.first;
      prev_end_index = snapshot_blocking_state.first;
      last_blocking_state = snapshot_blocking_state.second;
      continue;
    }

    int current_index = snapshot_blocking_state.first;
    // If the current index is not next to the prev_end_index or the blocking
    // state changed it compared to the last_blocking_state, it indicates that
    // continuous effective range is broken here, and record the last continuous
    // effective range.
    if (current_index != prev_end_index + 1 ||
        snapshot_blocking_state.second != last_blocking_state) {
      add_blocking_sequence_and_update_index(snapshot_blocking_state);
      continue;
    }

    // Update the prev_end_index.
    prev_end_index = current_index;
  }

  // Enclose the last effective range if the last blocking state is not
  // non-blocking.
  add_blocking_sequence_and_update_index(sequence_blocking_states.back());

  return blocking_sequences;
}

AgentBlockingSequenceInfo
SequenceBlockingStateComputer::ReconstructBlockingSequenceInfo(
    const std::vector<pb::BlockingSequence>& raw_blocking_sequences) const {
  AgentBlockingSequenceInfo blocking_sequence_info;
  if (raw_blocking_sequences.empty()) {
    return blocking_sequence_info;
  }

  std::vector<pb::BlockingSequence>& blocking_sequences =
      blocking_sequence_info.blocking_sequences;
  blocking_sequences.clear();
  blocking_sequences.reserve(raw_blocking_sequences.size());
  int waiting_seq_idx = kInvalidIndex;
  int current_blocking_sequence_idx = 0;

  for (const pb::BlockingSequence& seq : raw_blocking_sequences) {
    // Try to populate current sequence into the blocking sequence. If it's
    // inserted successfully, reset the waiting sequence index. Otherwise,
    // current sequence should be the new waiting sequence.
    waiting_seq_idx =
        MaybePopulateBlockingSequence(raw_blocking_sequences, waiting_seq_idx,
                                      seq, &blocking_sequences)
            ? kInvalidIndex
            : current_blocking_sequence_idx;
    ++current_blocking_sequence_idx;
  }

  blocking_sequence_info.type = GetBlockingSequenceType(blocking_sequences);

  return blocking_sequence_info;
}

bool SequenceBlockingStateComputer::MaybePopulateBlockingSequence(
    const std::vector<pb::BlockingSequence>& raw_blocking_sequences,
    int waiting_seq_idx, const pb::BlockingSequence& cur_sequence,
    std::vector<pb::BlockingSequence>* blocking_sequences) const {
  DCHECK_NE(blocking_sequences, nullptr);
  constexpr int kMinBlockingSequenceLength = 2;
  // If there is waiting merge sequence and its gap to current seq is short,
  // try to merge with waiting sequence and populate result into the blocking
  // sequence. If successful, reset waiting sequence index and update current
  // blocking sequence index.
  if (waiting_seq_idx != kInvalidIndex &&
      GetBlockingSequenceIndexGap(raw_blocking_sequences[waiting_seq_idx],
                                  cur_sequence) < kMinBlockingSequenceLength) {
    pb::BlockingSequence new_blocking_sequence;
    new_blocking_sequence.mutable_range()->set_start_index(
        raw_blocking_sequences[waiting_seq_idx].range().start_index());
    new_blocking_sequence.mutable_range()->set_end_index(
        cur_sequence.range().end_index());
    new_blocking_sequence.set_blocking_state(cur_sequence.blocking_state());
    blocking_sequences->emplace_back(std::move(new_blocking_sequence));
    return true;
  }

  // If blocking sequence is not empty, and the gap to current sequence is
  // short, and last blocking sequence has same blocking state with current,
  // merge with last blocking sequence.
  if (!blocking_sequences->empty() &&
      GetBlockingSequenceIndexGap(blocking_sequences->back(), cur_sequence) <
          kMinBlockingSequenceLength &&
      blocking_sequences->back().blocking_state() ==
          cur_sequence.blocking_state()) {
    blocking_sequences->back().mutable_range()->set_end_index(
        cur_sequence.range().end_index());
    return true;
  }

  // Current blocking sequence is long enough to be inserted.
  if (GetBlockingSequenceLength(cur_sequence) >= kMinBlockingSequenceLength) {
    blocking_sequences->emplace_back(cur_sequence);
    return true;
  }

  return false;
}

pb::BlockingSequenceType SequenceBlockingStateComputer::GetBlockingSequenceType(
    const std::vector<pb::BlockingSequence>& blocking_sequences) const {
  if (blocking_sequences.empty()) {
    return pb::BlockingSequenceType::kNonBlocking;
  }

  pb::LateralBlockingState::BlockingState last_blocking_state =
      blocking_sequences.front().blocking_state();
  for (const pb::BlockingSequence& seq : blocking_sequences) {
    DCHECK(seq.blocking_state() == pb::LateralBlockingState::SOFT_BLOCKING ||
           seq.blocking_state() == pb::LateralBlockingState::HARD_BLOCKING);
    if (seq.blocking_state() != last_blocking_state) {
      return pb::BlockingSequenceType::kMixedBlocking;
    }
  }

  return CovertToSequenceBlockingType(last_blocking_state);
}
AgentBlockingSequenceInfo SequenceBlockingStateComputer::Compute(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data) const {
  AgentBlockingSequenceInfo blocking_sequence_info(blocking_state_data);
  const auto& primary_trajectory_state_iter =
      object_occupancy_state.predicted_trajectory_occupancy_states().find(
          object_occupancy_state.primary_trajectory_id());
  if (primary_trajectory_state_iter ==
      object_occupancy_state.predicted_trajectory_occupancy_states().end()) {
    DCHECK(false) << "primary trajectory should not be missed";
    return blocking_sequence_info;
  }

  const auto& primary_trajectory_state = primary_trajectory_state_iter->second;
  if (primary_trajectory_state.predicted_states().empty()) {
    return blocking_sequence_info;
  }

  const std::vector<math::Range<int>>& effective_ranges =
      blocking_state_data.effective_intention_index_ranges;
  if (effective_ranges.empty()) {
    return blocking_sequence_info;
  }

  // Effective ranges are the agent prediction states which has overlap with the
  // s-t area bounded by the speed upper and lower bound. We only need to
  // recompute those blocking states.
  std::vector<SnapshotBlockingState> sequence_blocking_states;
  sequence_blocking_states.reserve(effective_ranges.back().end_pos -
                                   effective_ranges.front().start_pos + 1);
  for (const math::Range<int>& effective_range : effective_ranges) {
    ComputeBlockingStateForPosesInRange(
        primary_trajectory_state, effective_range, &sequence_blocking_states);
  }

  // Convert to blocking sequence.
  std::sort(sequence_blocking_states.begin(), sequence_blocking_states.end());
  const std::vector<pb::BlockingSequence> raw_blocking_sequence =
      GetBlockingSequences(sequence_blocking_states);

  // Reconstruct to the agent blocking sequence info.
  return ReconstructBlockingSequenceInfo(raw_blocking_sequence);
}

void SequenceBlockingStateComputer::ComputeBlockingStateForPosesInRange(
    const ObjectTrajectoryStates& primary_trajectory_state,
    const math::Range<int>& effective_range,
    std::vector<SnapshotBlockingState>* sequence_blocking_states) const {
  DCHECK_LE(effective_range.start_pos, effective_range.end_pos);
  for (int index = effective_range.start_pos; index < effective_range.end_pos;
       ++index) {
    // Get agent snapshot pose.
    DCHECK_LT(index, primary_trajectory_state.predicted_states().size());
    const math::geometry::PolygonWithCache2d& snapshot_polygon =
        primary_trajectory_state.predicted_states()[index].pose().contour();
    sequence_blocking_states->emplace_back(
        index, GetClearanceBasedBlockingStateForSnapshot(snapshot_polygon));
  }
}

}  // namespace path
}  // namespace planner
