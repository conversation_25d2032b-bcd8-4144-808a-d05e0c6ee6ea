#include "planner/speed/overlap/overlap_lib_util.h"

#include <algorithm>
#include <cmath>
#include <cstddef>
#include <iomanip>
#include <limits>
#include <map>
#include <optional>
#include <utility>
#include <vector>

#include <glog/logging.h>

#include "geometry/algorithms/arithmetic.h"
#include "geometry/algorithms/convert.h"
#include "geometry/algorithms/distance.h"
#include "geometry/algorithms/intersection.h"
#include "geometry/algorithms/intersects.h"
#include "geometry/algorithms/length.h"
#include "geometry/algorithms/transform.h"
#include "geometry/model/axis_aligned_box.h"
#include "geometry/model/oriented_box.h"
#include "geometry/model/point_2d.h"
#include "geometry/model/polygon.h"
#include "geometry/model/polygon_with_cache.h"
#include "geometry/model/segment.h"
#include "geometry/model/transformation_matrix.h"
#include "latency/latency_stat.h"
#include "math/constants.h"
#include "math/curve2d_util.h"
#include "math/enum.h"
#include "math/interpolation.h"
#include "math/math_util.h"
#include "math/range.h"
#include "math/unit_conversion.h"
#include "planner/constants.h"
#include "planner/planning_gflags.h"
#include "planner/world_model/traffic_participant/predicted_pose.h"
#include "planner_protos/overlap.pb.h"
#include "trace/trace.h"
#include "voy_protos/point.pb.h"
#include "voy_protos/polyline.pb.h"
#include "voy_protos/tracked_objects.pb.h"
#include "voy_trace/trace_planner.h"
#if defined(__AVX2__)
#include "xbase/geometry/algorithms/detail/avx_intrin_maker.h"
#include "xbase/geometry/algorithms/distance.h"
#include "xbase/geometry/model/vpoint_2d.h"
#include "xbase/geometry/utils/load_points.h"
#include "xbase/geometry/utils/math_util.h"
#endif

namespace planner {
namespace speed {

namespace {
using math::geometry::OrientedBox2d;
using math::geometry::Point2d;
using math::geometry::Polygon2d;
using math::geometry::PolygonWithCache2d;
using math::geometry::Polyline2d;
using math::geometry::Segment2d;

// Thereshold in change of heading to set a max padding cap.
constexpr double kHeadingsDeltaThreshold = 0.001;

// Correction coefficient for small angle approximation.
constexpr double kMaxPaddingCorrectionCoeff = 1.1;

// For OverlapV2 when the agent is completely behind the ego
// or completely after the end of the path, the precision for
// computing lateral gap is less since the agent is not lateral
// to the ego.
constexpr double kRelaxedLatGapPrecisionThresholdInMeter = 0.01;

// The precision of the computed lateral gap.
constexpr double kLatGapPrecisionInMeter = 1e-4;

// Returns true if the two rectangles are separated by the axis.
// Note that perp_axis_to_rect1_edge is obtained from the first rectange,
// and its direction is either forward or to the left. The rect1_edge_length
// is the length of the edge of rect1 parallel to perp_axis_to_rect1_edge.
bool DoesAxisSeparateRectangles(const Point2d& perp_axis_to_rect1_edge,
                                const Point2d& rect1_rear_right_point,
                                double rect1_edge_length,
                                const std::vector<Point2d>& rectangle2) {
  // Compute the projection of the first rectangle onto its own axis.
  const double rear_right_dot_product = math::geometry::DotProduct(
      rect1_rear_right_point, perp_axis_to_rect1_edge);
  // The maximum and minimum values of the projections of the first rectangle.
  const double max1 = rear_right_dot_product + rect1_edge_length;
  const double min1 = rear_right_dot_product;
  // Compute the projection of the second rectangle onto the axis.
  // The maximum and minimum values of the projections of the second rectangle.
  double max2 =
      math::geometry::DotProduct(rectangle2[0], perp_axis_to_rect1_edge);
  double min2 = max2;
  for (size_t i = 1; i < rectangle2.size(); ++i) {
    const double projection =
        math::geometry::DotProduct(rectangle2[i], perp_axis_to_rect1_edge);
    max2 = std::max(max2, projection);
    min2 = std::min(min2, projection);
    // Return false if the two rectangles are not separated by the axis.
    if (max1 > min2 && min1 < max2) {
      return false;
    }
  }
  return true;
}

// Computes the RLG varying arc length in the case where the padded overlap
// start is negative. Returns true if the overlap is contained within
// the first pose of ego. Returns false if the overlap extends beyond the
// first pose. The overlap is stored in arc_lengths. If there is no overlap
// the arc_lengths are left unchanged.
bool ComputeRLGVarArcLengthForFirstPoseOverlap(
    const pb::OverlapSlice& slice, double required_lateral_gap_in_meter,
    const int slice_length,
    std::vector<std::pair<double, double>>& arc_lengths) {
  const double padded_start_arc_length =
      slice.ego_moving_distance_padded().start();
  const double padded_end_arc_length = slice.ego_moving_distance_padded().end();
  bool within_lat_gap_first_pose = false;
  // We start from padded_start_arc_length and move the ego box forward in
  // kEgoFirstPoseSamplingSpaceInMeter to get the first arc length that there is
  // overlap. Here the assumption is that the req lat gap used for
  // padded_start_arc_length must be >=required_lateral_gap_in_meter.
  for (int start_idx = 0;
       start_idx < slice.signed_lateral_gap_to_first_ego_pose_boxes_size();
       ++start_idx) {
    within_lat_gap_first_pose =
        std::abs(slice.signed_lateral_gap_to_first_ego_pose_boxes(start_idx)) <=
        required_lateral_gap_in_meter;
    if (!within_lat_gap_first_pose) {
      continue;
    }
    const double start_arc_length =
        padded_start_arc_length +
        std::max(start_idx - 1, 0) * kEgoFirstPoseSamplingSpaceInMeter;
    // Compute the end overlap. End overlap is always greater than 0.
    for (int end_idx = slice_length; end_idx > 0; --end_idx) {
      if (std::abs(slice.signed_lateral_gap_to_ego_poses(end_idx - 1)) >
          required_lateral_gap_in_meter) {
        continue;
      }
      arc_lengths.push_back(std::make_pair(start_arc_length, 0.0));
      // We use dense sampling here since end overlap will always be greater
      // than 0 and we already have lateral gap info computed for these poses
      // with the dense sampling.
      arc_lengths.back().second =
          padded_end_arc_length - std::max((slice_length - 1 - end_idx), 0) *
                                      kEgoPathPointDenseSamplingSpacingInMeter;
      DCHECK_GT(arc_lengths.back().second, 0.0);
      return within_lat_gap_first_pose;
    }
  }
  return within_lat_gap_first_pose;
}

}  // namespace

PathForOverlap::PathForOverlap(const adv_geom::Path2dWithJuke& ego_path_juke,
                               const VehicleParamsForOverlap& vehicle_params,
                               bool compute_turn_based_padding,
                               bool should_use_linear_interp_heading)
    : ego_path_juke_(ego_path_juke),
      vehicle_params_(vehicle_params),
      arc_lengths_(ego_path_juke.arc_lengths()),
      should_use_linear_interp_heading_(should_use_linear_interp_heading) {
  TRACE_EVENT_SCOPE(planner, DecoupledForwardManeuver_PathForOverlap);
  VOY_LATENCY_STAT_RECORD_PLANNER_S1(
      LAT_STAT_DecoupledForwardManeuver_PathForOverlap);
  Init(compute_turn_based_padding);
}

PathForOverlap::PathForOverlap(adv_geom::Path2dWithJuke&& ego_path_juke,
                               VehicleParamsForOverlap&& vehicle_params,
                               bool compute_turn_based_padding,
                               bool should_use_linear_interp_heading)
    : ego_path_juke_(std::move(ego_path_juke)),
      vehicle_params_(std::move(vehicle_params)),
      arc_lengths_(ego_path_juke_.arc_lengths()),
      should_use_linear_interp_heading_(should_use_linear_interp_heading) {
  TRACE_EVENT_SCOPE(planner, DecoupledForwardManeuver_PathForOverlap);
  VOY_LATENCY_STAT_RECORD_PLANNER_S1(
      LAT_STAT_DecoupledForwardManeuver_PathForOverlap);
  Init(compute_turn_based_padding);
}

void PathForOverlap::Init(bool compute_turn_based_padding) {
  const std::vector<double>& headings = ego_path_juke_.headings();
  sin_headings_.reserve(headings.size());
  cos_headings_.reserve(headings.size());
  headings_delta_.reserve(headings.size());
  max_padding_left_.reserve(headings.size());
  max_padding_right_.reserve(headings.size());
  for (size_t i = 0; i < headings.size(); ++i) {
    sin_headings_.push_back(std::sin(headings[i]));
    cos_headings_.push_back(std::cos(headings[i]));
    headings_delta_.push_back(
        (i + 1 == headings.size())
            ? 0
            : math::Wrap(headings[i + 1] - headings[i], -M_PI, M_PI));
    arc_lengths_delta_.push_back((i + 1 == headings.size())
                                     ? 0
                                     : GetArcLengthAtIndex(i + 1) -
                                           GetArcLengthAtIndex(i));
    // Max turning padding is arc_lengths_delta / tan(heading_delta).
    // Since heading_delta is small, a safer padding cap is
    // arc_lengths_delta / (kMaxPaddingCorrectionCoeff * heading_delta).
    max_padding_left_.push_back(
        (headings_delta_.back() > kHeadingsDeltaThreshold)
            ? arc_lengths_delta_.back() /
                  (kMaxPaddingCorrectionCoeff * headings_delta_.back())
            : std::numeric_limits<double>::max());
    max_padding_right_.push_back(
        (headings_delta_.back() < -kHeadingsDeltaThreshold)
            ? -arc_lengths_delta_.back() /
                  (kMaxPaddingCorrectionCoeff * headings_delta_.back())
            : std::numeric_limits<double>::max());
    DCHECK_GE(max_padding_left_.back(), 0.0);
    DCHECK_GE(max_padding_right_.back(), 0.0);
    // TODO(ruiwu): Re-enable this check after debug.
    // DCHECK_LT(std::abs(headings_delta.back()), M_PI_2);
  }
  if (compute_turn_based_padding) {
    // Precomputes and stores padding information.
    ComputePathPaddings();
  } else {
    // fill the turn_padding_left_ and turn_padding_right_ with zeros.
    turn_padding_left_.assign(size(), 0);
    turn_padding_right_.assign(size(), 0);
  }
  // Precomputes and stores strict path boundary.
  strict_path_boundary_ = ConstructPathBoundary(*this, /*width_padding=*/0.0);
  // Precomputes and stores strict ra to rb box.
  strict_ra_to_rb_box_ = ConstructRaToRbBox(*this, /*width_padding=*/0.0);

  // Precomputes densified path information.
  ComputeDensifiedPath();
  // Precomputes and stores ego oriented boxes with 0.1m interval.
  strict_path_oriented_boxes_ =
      ConstructPathOrientedBoxes(/*width_padding=*/0.0);
  // Precomputes the stores first pose densified boxes with 0.5m interval.
  strict_first_pose_boxes_ =
      ComputeDensfiedFirstPoseBoxes(/*width_padding=*/0.0);
}

PathForOverlap::PathForOverlap(
    adv_geom::Path2dWithJuke ego_path_juke,
    VehicleParamsForOverlap vehicle_params, std::vector<double> sin_headings,
    std::vector<double> cos_headings, std::vector<double> headings_delta,
    std::vector<double> arc_lengths, std::vector<double> arc_lengths_delta,
    std::vector<double> turn_padding_left,
    std::vector<double> turn_padding_right,
    std::vector<double> max_padding_left, std::vector<double> max_padding_right)
    : ego_path_juke_(std::move(ego_path_juke)),
      vehicle_params_(vehicle_params),
      sin_headings_(std::move(sin_headings)),
      cos_headings_(std::move(cos_headings)),
      headings_delta_(std::move(headings_delta)),
      arc_lengths_(std::move(arc_lengths)),
      arc_lengths_delta_(std::move(arc_lengths_delta)),
      turn_padding_left_(std::move(turn_padding_left)),
      turn_padding_right_(std::move(turn_padding_right)),
      max_padding_left_(std::move(max_padding_left)),
      max_padding_right_(std::move(max_padding_right)) {
  // Precomputes and stores strict path boundary.
  strict_path_boundary_ = ConstructPathBoundary(*this, /*width_padding=*/0.0);
}

double PathForOverlap::GetArcLengthAtIndex(int index) const {
  DCHECK_GE(index, 0);
  DCHECK_LT(index, arc_lengths().size());
  return arc_lengths()[index];
}

double PathForOverlap::GetTotalArcLength() const {
  DCHECK(!arc_lengths_.empty());
  return arc_lengths_.back();
}

std::vector<OrientedBox2d> PathForOverlap::ConstructPathOrientedBoxes(
    double width_padding) const {
  std::vector<OrientedBox2d> oriented_boxes;
  oriented_boxes.reserve(densified_center_points_.size());
  for (size_t i = 0; i < densified_center_points_.size(); ++i) {
    oriented_boxes.push_back(OrientedBox2d(
        densified_center_points_[i], vehicle_params_.length,
        vehicle_params_.width + 2 * width_padding, densified_headings_[i]));
  }
  return oriented_boxes;
}

void PathForOverlap::ComputePathPaddings() {
  if (empty()) {
    return;
  }

  DCHECK_EQ(turn_padding_left_.size(), 0);
  DCHECK_EQ(turn_padding_right_.size(), 0);
  turn_padding_left_.reserve(headings_delta_.size());
  turn_padding_right_.reserve(headings_delta_.size());

  for (size_t i = 0; i < headings_delta_.size(); i++) {
    // Calculates the padding needed for turning.
    double accu_pos_angle = 0;
    double accu_neg_angle = 0;

    for (int curr_step = i; curr_step > 0; curr_step--) {
      // Only back tracks for the length of ra_to_front_bumper.
      if (GetArcLengthAtIndex(i) - GetArcLengthAtIndex(curr_step) >=
          vehicle_params_.leading_bumper_offset) {
        break;
      }
      if (headings_delta_[curr_step - 1] > 0) {
        accu_pos_angle += headings_delta_[curr_step - 1];
      } else {
        accu_neg_angle += headings_delta_[curr_step - 1];
      }
    }

    // The negation is to make sure the turn_padding_left is >=0.
    turn_padding_left_.push_back(std::min(
        vehicle_params_.max_turn_based_padding,
        -vehicle_params_.leading_bumper_offset_corrected * accu_neg_angle));
    turn_padding_right_.push_back(std::min(
        vehicle_params_.max_turn_based_padding,
        vehicle_params_.leading_bumper_offset_corrected * accu_pos_angle));
  }
}

void PathForOverlap::ComputeDensifiedPath() {
  const auto& ego_params = vehicle_params();
  const double ra_to_center_dist =
      ego_params.half_length - ego_params.trailing_bumper_offset;

  const double total_arc_length = ego_path_juke().GetTotalArcLength();
  DCHECK_GT(total_arc_length, 0.0);
  const int densified_size =
      static_cast<int>(total_arc_length /
                       kEgoPathPointDenseSamplingSpacingInMeter) +
      1;
  densified_arc_lengths_.reserve(densified_size);
  densified_center_points_.reserve(densified_size);
  densified_headings_.reserve(densified_size);
  for (int i = 0; i < densified_size; ++i) {
    const double cur_ra_arc_length =
        i * kEgoPathPointDenseSamplingSpacingInMeter;
    const Point2d cur_ra_point =
        ego_path_juke().GetInterpPoint(cur_ra_arc_length);
    double cur_heading = 0;
    if (should_use_linear_interp_heading_) {
      cur_heading = ego_path_juke().GetLinearInterpHeading(cur_ra_arc_length);
    } else {
      cur_heading = ego_path_juke().GetInterpHeading(cur_ra_arc_length);
    }
    const Point2d length_unit =
        Point2d(std::cos(cur_heading), std::sin(cur_heading));
    const Point2d ra_to_center_vec = Multiply(length_unit, ra_to_center_dist);
    const Point2d cur_center_point = Add(cur_ra_point, ra_to_center_vec);

    densified_arc_lengths_.push_back(cur_ra_arc_length);
    densified_headings_.push_back(cur_heading);
    densified_center_points_.push_back(std::move(cur_center_point));
  }
}

std::vector<OrientedBox2d> PathForOverlap::ComputeDensfiedFirstPoseBoxes(
    double width_padding) const {
  const auto& ego_params = vehicle_params();
  const double heading = ego_path_juke().headings()[0];
  const Point2d length_unit = Point2d(std::cos(heading), std::sin(heading));
  const Point2d& ra_point = ego_path_juke().points()[0];

  const Point2d ra_to_rb_vec =
      Multiply(length_unit, -ego_params.trailing_bumper_offset);
  const Point2d rb_point = Add(ra_point, ra_to_rb_vec);

  std::vector<OrientedBox2d> first_pose_boxes;
  for (double arc_length = 0.0; arc_length < ego_params.length;
       arc_length += kEgoFirstPoseSamplingSpaceInMeter) {
    const double box_length = std::min(kEgoFirstPoseSamplingSpaceInMeter,
                                       ego_params.length - arc_length);
    const double rb_to_center_dist = arc_length + 0.5 * box_length;
    const Point2d rb_to_center_vec = Multiply(length_unit, rb_to_center_dist);
    const Point2d center_point = Add(rb_point, rb_to_center_vec);
    first_pose_boxes.push_back(
        OrientedBox2d(center_point, box_length,
                      ego_params.width + 2 * width_padding, heading));
  }
  return first_pose_boxes;
}

OverlapSkippingThresholds ********************************(
    const VehicleParamsForOverlap& vehicle_params,
    const TrafficParticipantPose& agent_pose,
    double required_lateral_gap_in_meter, bool should_use_extended_bbox) {
  const double padded_ego_half_width =
      vehicle_params.half_width + required_lateral_gap_in_meter;
  if (agent_pose.should_use_contour() || should_use_extended_bbox) {
    // If the agent pose is irregular, use the contour length and width to
    // compute padded distance max. Since the agent pose minimum radius is
    // unknown, use 0 to be safe.
    const double agent_length =
        std::max(agent_pose.contour_length(), agent_pose.extended_length());
    const double agent_width =
        std::max(agent_pose.contour_width(), agent_pose.extended_width());
    const double padded_distance_max =
        0.5 * math::FastHypot(agent_length, agent_width) +
        math::FastHypot(vehicle_params.half_length, padded_ego_half_width);
    const double padded_distance_min = 0.0;
    return OverlapSkippingThresholds(padded_distance_max, padded_distance_min);
  }

  const double agent_half_length = 0.5 * agent_pose.length();
  const double agent_half_width = 0.5 * agent_pose.width();
  const double padded_distance_max =
      math::FastHypot(agent_half_length, agent_half_width) +
      math::FastHypot(vehicle_params.half_length, padded_ego_half_width);
  const double padded_distance_min =
      std::min(agent_half_length, agent_half_width) +
      std::min(vehicle_params.half_length, padded_ego_half_width);
  return OverlapSkippingThresholds(padded_distance_max, padded_distance_min);
}

OverlapSkippingThresholds ********************************(
    const VehicleParamsForOverlap& vehicle_params,
    const ConstructionZone& construction_zone,
    double required_lateral_gap_in_meter) {
  const double padded_ego_half_width =
      vehicle_params.half_width + required_lateral_gap_in_meter;
  // Since construction zones are irregular and stationary, we use the axis
  // aligned boundng box to compute overlap skipping thresholds.
  const math::geometry::AxisAlignedBox2d& box =
      construction_zone.contour.bounding_box();
  const double padded_distance_max =
      0.5 * math::FastHypot(box.CalcXLength(), box.CalcYLength()) +
      math::FastHypot(vehicle_params.half_length, padded_ego_half_width);
  const double padded_distance_min = 0.0;
  return OverlapSkippingThresholds(padded_distance_max, padded_distance_min);
}

std::pair<double, double> ComputeLateralGapFromEgoBox(
    const OrientedBox2d& ego_box, const std::vector<Point2d>& agent_contour,
    bool is_polyline, std::vector<Point2d>& transformed_agent_contour_buffer,
    bool no_intersect) {
  // If the no_intersect flag is not set and the agent contour is not a
  // polyline, first check if the ego box intersects with the agent contour for
  // quick exit.
  if (!no_intersect && !is_polyline &&
      math::geometry::Intersects(ego_box, Polygon2d(agent_contour))) {
    return std::make_pair(0.0, 0.0);
  }
  DCHECK_EQ(agent_contour.size(), transformed_agent_contour_buffer.size());
  // Shift and rotate the agent points and ego box such that
  // the ego box is centered at the origin and has a heading of 0.
  const math::geometry::TransfMat2D transformation_matrix =
      (math::geometry::TransfMat2D() << ego_box.cos_heading(),
       ego_box.sin_heading(), -ego_box.sin_heading(), ego_box.cos_heading())
          .finished();
  for (size_t i = 0; i < agent_contour.size(); ++i) {
    const Point2d& point = agent_contour[i];
    transformed_agent_contour_buffer[i] =
        Transform(Subtract(point, ego_box.center()), transformation_matrix);
  }
  // The left segment of ego box is {(-half_length, half_width), {half_length,
  // half_width}}. the right segment of ego box is  {(-half_length,
  // -half_width), {half_length, -half_width}}.
  const double half_length = 0.5 * ego_box.length();
  const double half_width = 0.5 * ego_box.width();
  // The minimum y-coordinate of a point on the agent contour such that its
  // x-coordinate is within [-half_length, half_length].
  double left_min_y = std::numeric_limits<double>::infinity();
  double right_max_y = -std::numeric_limits<double>::infinity();
  for (size_t i = 0; i < transformed_agent_contour_buffer.size(); ++i) {
    const auto& p1 = transformed_agent_contour_buffer[i];
    // If p1's x-coordinate is within [-half_length, half_length], update
    // left_min_y or right_max_y with p1's y-coordinate.
    if (math::IsInRange(p1.x(), -half_length, half_length)) {
      if (p1.y() > 0) {
        math::UpdateMin(p1.y(), left_min_y);
      } else {
        math::UpdateMax(p1.y(), right_max_y);
      }
    }

    // If the input agent contour is a polyline, skip the segment between the
    // first and last point, since a polyline is not self enclosing.
    if (is_polyline && i == agent_contour.size() - 1) {
      break;
    }

    const auto& p2 = transformed_agent_contour_buffer
        [(i + 1) % transformed_agent_contour_buffer.size()];
    const double max_x = std::max(p1.x(), p2.x());
    const double min_x = std::min(p1.x(), p2.x());
    // If p1-p2 is crossing the line x=half_length, find the segment's
    // y-coordinate at x=half_length and update left_min_y or right_max_y.
    if (max_x > half_length && min_x < half_length) {
      const double interpolated_y = math::GetLinearInterpolatedY(
          /*x1=*/p1.x(), /*x2=*/p2.x(), /*y1=*/p1.y(),
          /*y2=*/p2.y(), /*x_eval=*/half_length);
      if (interpolated_y > 0) {
        math::UpdateMin(interpolated_y, left_min_y);
      } else {
        math::UpdateMax(interpolated_y, right_max_y);
      }
    }
    // If p1-p2 is crossing the line x=-half_length, find the segment's
    // y-coordinate at x=-half_length and update left_min_y or right_max_y.
    if (max_x > -half_length && min_x < -half_length) {
      const double interpolated_y = math::GetLinearInterpolatedY(
          /*x1=*/p1.x(), /*x2=*/p2.x(), /*y1=*/p1.y(),
          /*y2=*/p2.y(), /*x_eval=*/-half_length);
      if (interpolated_y > 0) {
        math::UpdateMin(interpolated_y, left_min_y);
      } else {
        math::UpdateMax(interpolated_y, right_max_y);
      }
    }
  }

  // The ego box doesn't interect with the agent contour if both left gap and
  // right gap are larger than 0. Returns the pair of left and right lateral
  // gap.
  const double left_gap = left_min_y - half_width;
  const double right_gap = -half_width - right_max_y;
  if (left_gap <= 0.0 || right_gap <= 0.0) {
    return std::make_pair(0.0, 0.0);
  }
  return std::make_pair(left_gap, right_gap);
}

std::pair<double, double> ComputeLateralGapFromEgoBoxAVX(
    const OrientedBox2d& ego_box, const std::vector<Point2d>& agent_contour,
    bool is_polyline, std::vector<Point2d>& transformed_agent_contour_buffer,
    bool no_intersect, bool store_transformed_agent_contour) {
#if defined(__AVX2__)
  // If the no_intersect flag is not set and the agent contour is not a
  // polyline, first check if the ego box intersects with the agent contour for
  // quick exit.
  if (!no_intersect && !is_polyline &&
      math::geometry::Intersects(ego_box, Polygon2d(agent_contour))) {
    return std::make_pair(0.0, 0.0);
  }
  DCHECK_EQ(agent_contour.size(), transformed_agent_contour_buffer.size());

  const double half_length = 0.5 * ego_box.length();
  const double half_width = 0.5 * ego_box.width();

  using CType = double;
  namespace xg = xbase::geometry;
  using vPoint = xg::vPoint2<CType>;
  using AVXType = vPoint::AVXType;
  constexpr size_t kLanes = vPoint::kLanes;
  const auto rot00 = xg::v256_set1_packed<AVXType>(ego_box.cos_heading());
  const auto rot01 = xg::v256_set1_packed<AVXType>(ego_box.sin_heading());
  // These two packed vectors form the basis of transformation matrix.
  const vPoint rot_v1 = vPoint(rot00, rot01);
  const vPoint rot_v2 = vPoint(-rot01, rot00);

  // Function used for packed store of agent contour.
  auto store_transformed_contour = [&transformed_agent_contour_buffer,
                                    &agent_contour](AVXType px, AVXType py,
                                                    size_t pos) {
    if ((pos + kLanes) <= agent_contour.size()) {
      auto* ptr =
          reinterpret_cast<double*>(&transformed_agent_contour_buffer[pos]);
      const auto x1y1x3y3 =
          xg::v256_shuffle_packed<AVXType, 0b0000'0000>(px, py);
      const auto x2y2x4y4 =
          xg::v256_shuffle_packed<AVXType, 0b0000'1111>(px, py);
      const auto x1y1x2y2 = xg::v256_permute2f128_packed<AVXType, 0b0010'0000>(
          x1y1x3y3, x2y2x4y4);
      const auto x3y3x4y4 = xg::v256_permute2f128_packed<AVXType, 0b0011'0001>(
          x1y1x3y3, x2y2x4y4);
      xg::v256_storeu_packed<AVXType>(ptr, x1y1x2y2);
      xg::v256_storeu_packed<AVXType>(ptr + kLanes, x3y3x4y4);
    } else {
      vPoint points(px, py);
      for (size_t i = 0; i < kLanes; ++i) {
        if ((pos + i) < agent_contour.size()) {
          transformed_agent_contour_buffer[pos + i] = points.GetPoint(i);
        } else {
          break;
        }
      }
    }
  };

  vPoint v_agent_center;
  v_agent_center.SetPoint(ego_box.center());
  const auto v_half_length = xg::v256_set1_packed<AVXType>(half_length);
  const auto kVEpsilon =
      xg::v256_set1_packed<AVXType>(math::constants::kEpsilon);

  // AVX version of math::IsInRange
  auto in_range_mask = [&v_half_length, &kVEpsilon](AVXType val) {
    return xg::v256_and_packed<AVXType>(
        xg::v256_cmp_packed<AVXType, _CMP_LE_OQ>(val,
                                                 v_half_length + kVEpsilon),
        xg::v256_cmp_packed<AVXType, _CMP_GE_OQ>(val,
                                                 -v_half_length - kVEpsilon));
  };

  auto less_than_tol = [&kVEpsilon](AVXType val) {
    return xg::v256_and_packed<AVXType>(
        xg::v256_cmp_packed<AVXType, _CMP_LT_OQ>(val, kVEpsilon),
        xg::v256_cmp_packed<AVXType, _CMP_GT_OQ>(val, -kVEpsilon));
  };

  auto v_left_min_y =
      xg::v256_set1_packed<AVXType>(std::numeric_limits<CType>::infinity());
  auto v_right_max_y =
      xg::v256_set1_packed<AVXType>(-std::numeric_limits<CType>::infinity());

  // Do packed math::UpdateMin and UpdateMax in one function.
  auto update_min_max_y = [&](AVXType py, AVXType in_range_mask) {
    const auto left_mask = xg::v256_cmp_packed<AVXType, _CMP_GT_OQ>(
        py, xg::v256_setzero_packed<AVXType>());
    const auto mask = xg::v256_and_packed<AVXType>(left_mask, in_range_mask);
    // Update min
    if (xg::v256_movemask_packed<AVXType>(mask) > 0) {
      const auto immed_left_min =
          xg::v256_min_packed<AVXType>(py, v_left_min_y);
      v_left_min_y =
          xg::v256_blendv_packed<AVXType>(v_left_min_y, immed_left_min, mask);
    }

    // Update max
    const auto right_mask = xg::v256_and_packed<AVXType>(
        xg::v256_not_packed(left_mask), in_range_mask);
    if (xg::v256_movemask_packed<AVXType>(right_mask) > 0) {
      const auto immed_right_max =
          xg::v256_max_packed<AVXType>(py, v_right_max_y);
      v_right_max_y = xg::v256_blendv_packed<AVXType>(
          v_right_max_y, immed_right_max, right_mask);
    }
  };

  // ratio = (p2y - p1y) / (p2x - p1x)
  auto get_interp_ratio = [&](AVXType p1x, AVXType p2x, AVXType p1y,
                              AVXType p2y) {
    AVXType denom = p2x - p1x;
    denom =
        xg::v256_blendv_packed<AVXType>(denom, kVEpsilon, less_than_tol(denom));
    const auto ratio = xg::v256_div_packed<AVXType>(p2y - p1y, denom);
    return ratio;
  };

  // linear interplate: eval_y = (x_eval - p1x) * ration + p1y
  auto packed_linear_interp_y = [&](AVXType ratio, AVXType x_eval, AVXType p1x,
                                    AVXType p1y) {
    const auto eval_v = xg::v256_mul_packed<AVXType>(x_eval - p1x, ratio) + p1y;
    return eval_v;
  };

  const bool ring = !is_polyline;
  constexpr size_t kBitShift = kLanes == 4 ? 2 : 3;
  const size_t agent_corners_size = agent_contour.size();
  const size_t loop_cnt = (agent_corners_size + kLanes - 1) >> kBitShift;

  int corner_idx = 0;
  vPoint first_corners_batch =
      xg::util::LoadPoints(agent_contour, corner_idx, ring);
  corner_idx += kLanes;
  for (size_t i = 0; i < loop_cnt; ++i) {
    auto next_corners_batch =
        (agent_contour.size() <= kLanes && ring)
            ? first_corners_batch
            : xg::util::LoadPoints(agent_contour, corner_idx, ring);
    const auto pre_rot_p1 = first_corners_batch - v_agent_center;
    // Performs coordinate transformation.
    auto p1_x = rot_v1 * pre_rot_p1;
    const auto p1_y = rot_v2 * pre_rot_p1;

    if (store_transformed_agent_contour) {
      store_transformed_contour(p1_x, p1_y, corner_idx - kLanes);
    }

    const auto p1_x_in_range_mask = in_range_mask(p1_x);
    if (xg::v256_movemask_packed<AVXType>(p1_x_in_range_mask) > 0) {
      update_min_max_y(p1_y, p1_x_in_range_mask);
    }

    const auto pre_rot_p2 =
        first_corners_batch.BlendByShiftingOneLane(next_corners_batch) -
        v_agent_center;
    auto p2_x = rot_v1 * pre_rot_p2;
    const auto p2_y = rot_v2 * pre_rot_p2;

    const auto v_max_x = xg::v256_max_packed<AVXType>(p1_x, p2_x);
    const auto v_min_x = xg::v256_min_packed<AVXType>(p1_x, p2_x);

    const auto cross_mask_positive = xg::v256_and_packed<AVXType>(
        xg::v256_cmp_packed<AVXType, _CMP_LT_OQ>(v_min_x, v_half_length),
        xg::v256_cmp_packed<AVXType, _CMP_GT_OQ>(v_max_x, v_half_length));
    const auto cross_mask_negtive = xg::v256_and_packed<AVXType>(
        xg::v256_cmp_packed<AVXType, _CMP_LT_OQ>(v_min_x, -v_half_length),
        xg::v256_cmp_packed<AVXType, _CMP_GT_OQ>(v_max_x, -v_half_length));

    const size_t cross_mask_value_p =
        xg::v256_movemask_packed<AVXType>(cross_mask_positive);
    const size_t cross_mask_value_n =
        xg::v256_movemask_packed<AVXType>(cross_mask_negtive);
    if (cross_mask_value_p > 0 || cross_mask_value_n > 0) {
      const auto ratio = get_interp_ratio(p1_x, p2_x, p1_y, p2_y);
      if (cross_mask_value_p > 0) {
        const auto interp_y =
            packed_linear_interp_y(ratio, v_half_length, p1_x, p1_y);
        update_min_max_y(interp_y, cross_mask_positive);
      }

      if (cross_mask_value_n > 0) {
        const auto interp_y =
            packed_linear_interp_y(ratio, -v_half_length, p1_x, p1_y);
        update_min_max_y(interp_y, cross_mask_negtive);
      }
    }

    first_corners_batch = next_corners_batch;
    corner_idx += kLanes;
  }

  const double left_min_y = xg::util::MinInLane(v_left_min_y);
  const double right_max_y = xg::util::MaxInLane(v_right_max_y);
  const double left_gap = left_min_y - half_width;
  const double right_gap = -half_width - right_max_y;
  if (left_gap <= 0.0 || right_gap <= 0.0) {
    return std::make_pair(0.0, 0.0);
  }
  return std::make_pair(left_gap, right_gap);
#else
  (void)store_transformed_agent_contour;
  return ComputeLateralGapFromEgoBox(ego_box, agent_contour, is_polyline,
                                     transformed_agent_contour_buffer,
                                     no_intersect);
#endif
}

void PostProcessOverlapSliceV2(const PathForOverlap& path_for_overlap,
                               const Polygon2d& agent_polygon, double heading,
                               double speed,
                               double plan_init_pose_arc_length_in_meter,
                               double required_lateral_gap_in_meter,
                               pb::OverlapSlice& slice) {
  DCHECK(slice.has_ego_moving_distance_padded());
  DCHECK_LE(slice.ego_moving_distance_padded().start(),
            slice.ego_moving_distance_padded().end() +
                kLongitudinalRangeErrorInMeter);
  DCHECK_GT(slice.padded_end_path_idx(), slice.padded_start_path_idx());
  if (slice.has_ego_moving_distance_strict()) {
    DCHECK_LE(slice.ego_moving_distance_padded().start(),
              slice.ego_moving_distance_strict().start() +
                  kLongitudinalRangeErrorInMeter);
    DCHECK_LE(slice.ego_moving_distance_strict().end(),
              slice.ego_moving_distance_padded().end() +
                  kLongitudinalRangeErrorInMeter);
    DCHECK_LE(slice.ego_moving_distance_strict().start(),
              slice.ego_moving_distance_strict().end() +
                  kLongitudinalRangeErrorInMeter);
    DCHECK_GT(slice.strict_end_path_idx(), slice.strict_start_path_idx());
    DCHECK_EQ(slice.signed_lateral_gap(), 0);
  }
  const std::vector<OrientedBox2d>& strict_path_oriented_boxes =
      path_for_overlap.strict_path_oriented_boxes();

  // Add agent bbox points to the slice.
  *slice.mutable_agent_bbox_points()->mutable_points() =
      math::geometry::Convert<google::protobuf::RepeatedPtrField<voy::Point2d>>(
          agent_polygon);

  // Calculate the agent snapshot's speed projections on the ego path. Use the
  // mid point of the padded arc length start and end as the agent's
  // projection point on the ego path. This is not the most accurate way, but
  // it should provide some usable values of the relative_heading and the
  // speed projections. The agent_pose.heading() is the agent's bounding box
  // heading direction. Add back the plan_init_pose_arc_length_in_meter because
  // the ra_arc_length_padded starts from the plan init state pose.
  const double ego_moving_distance_mid =
      std::max(0.5 * (slice.ego_moving_distance_padded().start() +
                      slice.ego_moving_distance_padded().end()) +
                   plan_init_pose_arc_length_in_meter,
               0.0);
  const double projection_point_heading =
      path_for_overlap.ego_path_juke().GetLinearInterpHeading(
          ego_moving_distance_mid);
  const double relative_heading_rad =
      math::AngleDiff(heading, projection_point_heading);
  slice.set_relative_heading(relative_heading_rad);
  slice.set_motion_type(RelativeHeadingToMotionType(relative_heading_rad));
  slice.set_signed_longitudinal_speed(speed *
                                      math::FastCos(relative_heading_rad));
  slice.set_signed_lateral_speed(speed * math::FastSin(relative_heading_rad));

  // Calculate the signed lateral gap.
  DCHECK_LT(slice.padded_start_path_idx(), strict_path_oriented_boxes.size());
  slice.mutable_signed_lateral_gap_to_ego_poses()->Reserve(
      slice.padded_end_path_idx() - slice.padded_start_path_idx() + 1);

  double min_lat_gap = std::numeric_limits<double>::max();
  // Agent contour buffer to reduce dynamic memory allocation.
  std::vector<Point2d> agent_contour_buffer(agent_polygon.size());
  // Calculate signed lat gap for first pose ego boxes. This is used to compute
  // RLG varying states when overlap starts from behind. For each box in
  // strict_first_pose_boxes, we compute the left lateral gap and right
  // lateral gap to the agent_polygon and then store the value which has the
  // smaller magnitude in signed_lateral_gap_to_first_ego_pose_boxes. Left
  // is +ve and right is -ve.
  if (slice.ego_moving_distance_padded().start() < 0) {
    const int first_pose_boxes_size =
        static_cast<int>(path_for_overlap.strict_first_pose_boxes().size());
    for (int box_idx = slice.padded_first_pose_box_start_idx();
         box_idx < first_pose_boxes_size; ++box_idx) {
      // If there is strict overlap, then lateral gap is 0.
      if (slice.has_ego_moving_distance_strict() &&
          math::IsInRange(box_idx, slice.strict_first_pose_box_start_idx(),
                          first_pose_boxes_size, /*tol=*/0)) {
        slice.add_signed_lateral_gap_to_first_ego_pose_boxes(0);
        continue;
      }
      const auto [left_lat_gap, right_lat_gap] = ComputeLateralGapFromEgoBoxAVX(
          path_for_overlap.strict_first_pose_boxes()[box_idx], agent_polygon,
          /*is_polyline=*/false, agent_contour_buffer, /*no_intersect=*/true,
          /*store_transformed_agent_contour=*/false);
      if (left_lat_gap <= right_lat_gap) {
        slice.add_signed_lateral_gap_to_first_ego_pose_boxes(left_lat_gap);
      } else {
        slice.add_signed_lateral_gap_to_first_ego_pose_boxes(-right_lat_gap);
      }
    }
  }
  // Calculate signed lat gap for rest of the poses.
  for (int path_idx = slice.padded_start_path_idx();
       path_idx <= slice.padded_end_path_idx(); path_idx++) {
    double curr_lat_gap = 0.0;
    if (slice.has_ego_moving_distance_strict() &&
        math::IsInRange(path_idx, slice.strict_start_path_idx(),
                        slice.strict_end_path_idx(), /*tol=*/0)) {
      // If the ego pose has strict overlap with the agent pose,
      // the lateral gap is zero.
      min_lat_gap = 0;
    } else {
      // If the ego pose only has padded overlap with the agent pose,
      // compute the lateral gap.
      const auto [left_lat_gap, right_lat_gap] = ComputeLateralGapFromEgoBoxAVX(
          strict_path_oriented_boxes[path_idx], agent_polygon,
          /*is_polyline=*/false, agent_contour_buffer, /*no_intersect=*/true,
          /*store_transformed_agent_contour=*/false);
      if (left_lat_gap <= right_lat_gap) {
        curr_lat_gap = left_lat_gap;
      } else {
        curr_lat_gap = -right_lat_gap;
      }
      UpdateMinMagnitude(curr_lat_gap, min_lat_gap);
    }
    slice.add_signed_lateral_gap_to_ego_poses(curr_lat_gap);
  }

  // The magnitude of the measured lateral gap must be smaller or equal to
  // the required lateral gap.
  // If overlap is completely behind the ego (slice.padded_end_path_idx() <= 1)
  // or if the overlap is completely after the end of the path
  // (slice.padded_start_path_idx() + 2 >=
  // static_cast<int>(strict_path_oriented_boxes.size()) use a relaxed
  // tolerance else use the normal tolerance.
  const double kLatGapTol =
      (slice.padded_end_path_idx() <= 1 ||
       slice.padded_start_path_idx() + 2 >=
           static_cast<int>(strict_path_oriented_boxes.size()))
          ? kRelaxedLatGapPrecisionThresholdInMeter
          : kLatGapPrecisionInMeter;
  DCHECK_LE(std::abs(min_lat_gap), required_lateral_gap_in_meter + kLatGapTol)
      << std::setprecision(12) << " min lat gap: " << std::abs(min_lat_gap)
      << " req lat gap plus tol: " << required_lateral_gap_in_meter + kLatGapTol
      << " padded start idx: " << slice.padded_start_path_idx()
      << " padded end idx: " << slice.padded_end_path_idx()
      << " strict oriented boxes size: " << strict_path_oriented_boxes.size()
      << " padded arc length start: " << GetPaddedOverlapStart(slice)
      << " padded arc length end: " << GetPaddedOverlapEnd(slice);
  const int padded_overlap_size =
      slice.padded_end_path_idx() - slice.padded_start_path_idx() + 1;
  DCHECK_EQ(slice.signed_lateral_gap_to_ego_poses_size(), padded_overlap_size);
  // Since we extended padded overlap start and end indices by 1 to be safe, we
  // may have infinity at the start and/or end. We correct this by using the
  // value at index start + 1 and end - 1.
  if (slice.signed_lateral_gap_to_ego_poses(0) ==
      std::numeric_limits<double>::infinity()) {
    DCHECK_GT(padded_overlap_size, 1);
    slice.set_signed_lateral_gap_to_ego_poses(
        0, slice.signed_lateral_gap_to_ego_poses(1));
  }
  if (slice.signed_lateral_gap_to_ego_poses(padded_overlap_size - 1) ==
      std::numeric_limits<double>::infinity()) {
    DCHECK_GT(padded_overlap_size, 1);
    slice.set_signed_lateral_gap_to_ego_poses(
        padded_overlap_size - 1,
        slice.signed_lateral_gap_to_ego_poses(padded_overlap_size - 2));
  }

  slice.set_signed_lateral_gap(min_lat_gap);
}

double AgentToPathBoundaryEncroachmentDistance(
    const Polyline2d& boundary_polyline,
    const std::vector<Point2d>& agent_contour, math::pb::Side side,
    int start_idx, int end_idx) {
  DCHECK_GE(start_idx, 0);
  DCHECK_LE(start_idx, end_idx);
  DCHECK_LT(end_idx + 1, boundary_polyline.size());
  // agent_min_dist stores minimum distance to the strict path boundary for
  // every point on agent contour.
  std::vector<double> agent_min_dist(agent_contour.size(),
                                     std::numeric_limits<double>::max());
  // Loop through the strict path boundary with strict overlaps to find the
  // encroachment distance.
  for (int path_idx = start_idx; path_idx <= end_idx; ++path_idx) {
    for (size_t i = 0; i < agent_contour.size(); ++i) {
      if (agent_min_dist[i] == 0) {
        continue;
      }
      const math::pb::Side point_side =
          GetSide(boundary_polyline[path_idx], boundary_polyline[path_idx + 1],
                  agent_contour[i]);
      if (point_side == side) {
        agent_min_dist[i] = 0;
      } else {
        const double dist =
            ComparableDistance(agent_contour[i], boundary_polyline[path_idx],
                               boundary_polyline[path_idx + 1]);
        agent_min_dist[i] = std::min(agent_min_dist[i], dist);
      }
    }
  }
  const double max_dist =
      *std::max_element(agent_min_dist.begin(), agent_min_dist.end());
  return std::sqrt(max_dist);
}

void PostProcessOverlapRegionV2(pb::OverlapRegion& region) {
  DCHECK(!region.overlap_slices().empty());
  region.set_motion_type(region.overlap_slices(0).motion_type());
  // Range trackers:
  RangeTracker<int> agent_state_ix_padded;
  RangeTracker<int> agent_state_ix_strict;
  RangeTracker<double> ego_moving_distance_padded;
  RangeTracker<double> ego_moving_distance_strict;
  RangeTracker<int64_t> agent_state_relative_time_padded;
  RangeTracker<int64_t> agent_state_relative_time_strict;

  for (const auto& slice : region.overlap_slices()) {
    // TODO(Tianxiao): restore DCHECK when overlap splitting is enabled;
    // DCHECK_EQ(region.motion_type(), slice.motion_type());
    DCHECK(slice.has_ego_moving_distance_padded());
    agent_state_ix_padded.Update(slice.timestamp_index());
    agent_state_relative_time_padded.Update(slice.relative_time_in_msec());
    ego_moving_distance_padded.Update(
        slice.ego_moving_distance_padded().start());
    ego_moving_distance_padded.Update(slice.ego_moving_distance_padded().end());

    if (slice.has_ego_moving_distance_strict()) {
      agent_state_ix_strict.Update(slice.timestamp_index());
      agent_state_relative_time_strict.Update(slice.relative_time_in_msec());
      ego_moving_distance_strict.Update(
          slice.ego_moving_distance_strict().start());
      ego_moving_distance_strict.Update(
          slice.ego_moving_distance_strict().end());
    }
  }
  DCHECK(agent_state_ix_padded.HasUpdate());
  region.set_first_padded_agent_state_ix(agent_state_ix_padded.min());
  region.set_last_padded_agent_state_ix(agent_state_ix_padded.max());
  region.set_start_padded_relative_time_in_msec(
      agent_state_relative_time_padded.min());
  region.set_start_padded_relative_time_in_sec(
      math::Ms2Sec(region.start_padded_relative_time_in_msec()));
  region.set_end_padded_relative_time_in_msec(
      agent_state_relative_time_padded.max());
  region.set_end_padded_relative_time_in_sec(
      math::Ms2Sec(region.end_padded_relative_time_in_msec()));
  region.mutable_ego_moving_distance_padded()->set_start(
      ego_moving_distance_padded.min());
  region.mutable_ego_moving_distance_padded()->set_end(
      ego_moving_distance_padded.max());
  region.set_contain_strict_overlap(false);
  if (agent_state_ix_strict.HasUpdate()) {
    DCHECK_LE(agent_state_ix_padded.min(), agent_state_ix_strict.min());
    DCHECK_GE(agent_state_ix_padded.max(), agent_state_ix_strict.max());
    DCHECK_LE(agent_state_relative_time_padded.min(),
              agent_state_relative_time_strict.min());
    DCHECK_GE(agent_state_relative_time_padded.max(),
              agent_state_relative_time_strict.max());
    DCHECK_LE(
        ego_moving_distance_padded.min(),
        ego_moving_distance_strict.min() + kLongitudinalRangeErrorInMeter);
    DCHECK_GE(
        ego_moving_distance_padded.max(),
        ego_moving_distance_strict.max() - kLongitudinalRangeErrorInMeter);
    region.set_contain_strict_overlap(true);

    region.set_first_strict_agent_state_ix(agent_state_ix_strict.min());
    region.set_last_strict_agent_state_ix(agent_state_ix_strict.max());
    region.set_start_strict_relative_time_in_msec(
        agent_state_relative_time_strict.min());
    region.set_start_strict_relative_time_in_sec(
        math::Ms2Sec(region.start_strict_relative_time_in_msec()));
    region.set_end_strict_relative_time_in_msec(
        agent_state_relative_time_strict.max());
    region.set_end_strict_relative_time_in_sec(
        math::Ms2Sec(region.end_strict_relative_time_in_msec()));
    region.mutable_ego_moving_distance_strict()->set_start(
        ego_moving_distance_strict.min());
    region.mutable_ego_moving_distance_strict()->set_end(
        ego_moving_distance_strict.max());

    DCHECK_LE(region.first_padded_agent_state_ix(),
              region.first_strict_agent_state_ix());
    DCHECK_LE(region.first_strict_agent_state_ix(),
              region.last_strict_agent_state_ix());
    DCHECK_LE(region.last_strict_agent_state_ix(),
              region.last_padded_agent_state_ix());
  }
}

std::optional<pb::OverlapRegion> OffsetOverlapRegionArcLengthsV2(
    const pb::OverlapRegion& region, const double offset_arc_length) {
  DCHECK(!region.overlap_slices().empty());
  pb::OverlapRegion mapped_overlap_region = EmptyOverlapRegion();

  for (int i = 0; i < region.overlap_slices_size(); i++) {
    pb::OverlapSlice slice = region.overlap_slices(i);
    const double ego_moving_distance_padded_start =
        slice.ego_moving_distance_padded().start() - offset_arc_length;
    const double ego_moving_distance_padded_end =
        slice.ego_moving_distance_padded().end() - offset_arc_length;

    slice.mutable_ego_moving_distance_padded()->set_start(
        ego_moving_distance_padded_start);
    slice.mutable_ego_moving_distance_padded()->set_end(
        ego_moving_distance_padded_end);

    if (slice.has_ego_moving_distance_strict()) {
      const double ego_moving_distance_strict_start =
          slice.ego_moving_distance_strict().start() - offset_arc_length;
      const double ego_moving_distance_strict_end =
          slice.ego_moving_distance_strict().end() - offset_arc_length;
      slice.mutable_ego_moving_distance_strict()->set_start(
          ego_moving_distance_strict_start);
      slice.mutable_ego_moving_distance_strict()->set_end(
          ego_moving_distance_strict_end);
    }
    *mapped_overlap_region.add_overlap_slices() = std::move(slice);
  }
  if (!mapped_overlap_region.overlap_slices().empty()) {
    mapped_overlap_region.set_region_id(region.region_id());
    mapped_overlap_region.set_object_id(region.object_id());
    mapped_overlap_region.set_trajectory_id(region.trajectory_id());
    mapped_overlap_region.set_padded_boundary_req_lat_gap(
        region.padded_boundary_req_lat_gap());
    PostProcessOverlapRegionV2(mapped_overlap_region);
    return mapped_overlap_region;
  }
  return std::nullopt;
}

void AddOverlapSliceToRegionsV2(const PredictedTrajectoryWrapper* agent_traj,
                                const ConstructionZone* constr_zone,
                                pb::OverlapSlice&& slice,
                                std::vector<pb::OverlapRegion>& regions,
                                int& region_id) {
  for (auto& region : regions) {
    DCHECK(!region.overlap_slices().empty());
    if (AreOverlapSlicesConnectedV2(
            region.overlap_slices(region.overlap_slices_size() - 1), slice)) {
      *region.add_overlap_slices() = std::move(slice);
      return;
    }
  }
  // Didn't find any connection to existing regions. Create a new region.
  pb::OverlapRegion overlap_region = EmptyOverlapRegion();
  overlap_region.set_region_id(region_id++);
  if (agent_traj != nullptr) {
    DCHECK(constr_zone == nullptr);
    overlap_region.set_object_id(agent_traj->object_id());
    overlap_region.set_trajectory_id(agent_traj->id());
    overlap_region.set_is_primary(agent_traj->is_primary_trajectory());
  } else {
    DCHECK(constr_zone != nullptr);
    overlap_region.set_construction_zone_id(constr_zone->id);
  }
  *overlap_region.add_overlap_slices() = std::move(slice);
  regions.push_back(std::move(overlap_region));
}

PathBoundary ConstructPathBoundary(const PathForOverlap& path_for_overlap,
                                   double width_padding) {
  DCHECK(!path_for_overlap.empty());
  PathBoundary path_boundary;
  path_boundary.left_polyline.reserve(path_for_overlap.size());
  path_boundary.right_polyline.reserve(path_for_overlap.size());
  for (int i = 0; i < path_for_overlap.size(); i++) {
    AppendLeftRightPointForPathContour(path_for_overlap, i, width_padding,
                                       &path_boundary.left_polyline,
                                       &path_boundary.right_polyline);
  }
  return path_boundary;
}

OrientedBox2d ConstructRaToRbBox(const PathForOverlap& path_for_overlap,
                                 double width_padding) {
  DCHECK(!path_for_overlap.empty());
  const auto& ego_params = path_for_overlap.vehicle_params();
  const double heading = path_for_overlap.ego_path_juke().headings()[0];
  const Point2d length_unit = Point2d(std::cos(heading), std::sin(heading));
  const Point2d& ra_point = path_for_overlap.ego_path_juke().points()[0];
  const Point2d ra_to_center_vec =
      Multiply(length_unit, -0.5 * ego_params.trailing_bumper_offset);
  const Point2d center_point = Add(ra_point, ra_to_center_vec);

  return OrientedBox2d(center_point, ego_params.trailing_bumper_offset,
                       ego_params.width + 2 * width_padding, heading);
}

std::unordered_map<OverlapRegionId, const pb::OverlapRegion*>
GenerateOverlapIdToRegionMap(
    const std::map<ObjectId, std::vector<pb::OverlapRegion>>&
        object_overlap_region_map) {
  std::unordered_map<OverlapRegionId, const pb::OverlapRegion*>
      overlap_id_to_region_ptrs;
  for (const auto& [object_id, overlap_regions] : object_overlap_region_map) {
    for (const auto& region : overlap_regions) {
      overlap_id_to_region_ptrs.insert(
          std::make_pair(GetOverlapRegionUniqueId(region), &region));
    }
  }
  return overlap_id_to_region_ptrs;
}

double GetOverlapRegionMinAbsLateralGap(
    const pb::OverlapRegion& overlap_region) {
  DCHECK(!overlap_region.overlap_slices().empty());

  const auto slice_iter = std::min_element(
      overlap_region.overlap_slices().begin(),
      overlap_region.overlap_slices().end(),
      [](const pb::OverlapSlice& lhs, const pb::OverlapSlice& rhs) {
        return std::abs(lhs.signed_lateral_gap()) <
               std::abs(rhs.signed_lateral_gap());
      });

  return std::abs(slice_iter->signed_lateral_gap());
}

voy::Polyline2d PolylineToProto(const Polyline2d& poly) {
  voy::Polyline2d ret;
  for (const auto p : poly) {
    auto* new_point = ret.add_points();
    new_point->set_x(p.x());
    new_point->set_y(p.y());
  }
  return ret;
}

pb::PathBoundary PathBoundary::ToProto() const {
  pb::PathBoundary ret;
  *(ret.mutable_left_boundary()) = PolylineToProto(left_polyline);
  *(ret.mutable_right_boundary()) = PolylineToProto(right_polyline);
  return ret;
}

bool AreOverlapRegionConnected(const pb::OverlapRegion& region1,
                               const pb::OverlapRegion& region2) {
  // Quick exit if the two overlap regions are not connected.
  if (GetPaddedOverlapStart(region1) > GetPaddedOverlapEnd(region2) ||
      GetPaddedOverlapStart(region2) > GetPaddedOverlapEnd(region1) ||
      region1.start_padded_relative_time_in_msec() >
          region2.end_padded_relative_time_in_msec() ||
      region2.start_padded_relative_time_in_msec() >
          region1.end_padded_relative_time_in_msec()) {
    return false;
  }

  // i and j are the the indices of overlap slices in region1 and region2
  // respectively.
  int i = 0;
  int j = 0;
  while (i < region1.overlap_slices_size() &&
         j < region2.overlap_slices_size()) {
    const pb::OverlapSlice& slice1 = region1.overlap_slices(i);
    const pb::OverlapSlice& slice2 = region2.overlap_slices(j);
    // Increment the index for the slice with a smaller relative time.
    if (slice1.relative_time_in_msec() < slice2.relative_time_in_msec()) {
      i++;
      continue;
    }
    if (slice1.relative_time_in_msec() > slice2.relative_time_in_msec()) {
      j++;
      continue;
    }
    // Returns true if the two slices at the same timestamp are spatially
    // connected.
    if (GetPaddedOverlapEnd(slice1) >= GetPaddedOverlapStart(slice2)) {
      if (GetPaddedOverlapEnd(slice2) >= GetPaddedOverlapStart(slice1)) {
        return true;
      }
      // Increment j if the second slice has a smaller arc length.
      j++;
    } else {
      // Otherwise increment i.
      i++;
    }
  }
  return false;
}

bool IntersectsBySeparatingAxis(const OrientedBox2d& ego_box,
                                const OrientedBox2d& agent_box) {
  const std::vector<Point2d>& ego_rect = ego_box.CornerPoints();
  const std::vector<Point2d>& agent_rect = agent_box.CornerPoints();

  const Point2d& ego_rear_right_point =
      ego_box.CornerPoint(math::geometry::Box2dCornerType::kRearRight);
  const Point2d& agent_rear_right_point =
      agent_box.CornerPoint(math::geometry::Box2dCornerType::kRearRight);
  // We check if any of the two axes of each rectangle separates the two boxes.
  // If there is an axis separating the two rectangles, then they do not
  // intersect.
  // Note that we check ego's width unit vector first for better performance
  // since it's more likely that most agents do not interfere with ego's
  // trajectory.
  // TODO(Tianxiao): adjust the order by the relative heading of agent.
  return !(
      DoesAxisSeparateRectangles(ego_box.width_unit(), ego_rear_right_point,
                                 ego_box.width(), agent_rect) ||
      DoesAxisSeparateRectangles(ego_box.length_unit(), ego_rear_right_point,
                                 ego_box.length(), agent_rect) ||
      DoesAxisSeparateRectangles(agent_box.width_unit(), agent_rear_right_point,
                                 agent_box.width(), ego_rect) ||
      DoesAxisSeparateRectangles(agent_box.length_unit(),
                                 agent_rear_right_point, agent_box.length(),
                                 ego_rect));
}

int MatchOverlap(const pb::OverlapRegion& original_overlap,
                 const std::vector<pb::OverlapRegion>& new_overlaps) {
  const int new_overlaps_length = new_overlaps.size();
  DCHECK_GT(new_overlaps_length, 0);
  if (new_overlaps_length == 1) {
    return 0;
  }
  int same_motion_type_matched_overlap_index = -1;
  int64_t same_motion_type_min_timestamp_diff =
      std::numeric_limits<int64_t>::max();
  int different_motion_type_matched_overlap_index = -1;
  int64_t different_motion_type_min_timestamp_diff =
      std::numeric_limits<int64_t>::max();
  bool found_same_motion_type = false;
  // If there exists same motion type of OverlapRegion,
  // match the closest start timestamp OverlapRegion within same motion type.
  // Else, match within different motion type.
  for (int i = 0; i < new_overlaps_length; i++) {
    int64_t timestamp_diff =
        std::abs(new_overlaps[i].start_padded_relative_time_in_msec() -
                 original_overlap.start_padded_relative_time_in_msec());
    if (new_overlaps[i].motion_type() == original_overlap.motion_type()) {
      if (timestamp_diff < same_motion_type_min_timestamp_diff) {
        same_motion_type_min_timestamp_diff = timestamp_diff;
        same_motion_type_matched_overlap_index = i;
      }
      found_same_motion_type = true;
    } else {
      if (timestamp_diff < different_motion_type_min_timestamp_diff) {
        different_motion_type_min_timestamp_diff = timestamp_diff;
        different_motion_type_matched_overlap_index = i;
      }
    }
  }
  if (found_same_motion_type) {
    DCHECK_NE(same_motion_type_matched_overlap_index, -1);
    return same_motion_type_matched_overlap_index;
  }
  DCHECK_NE(different_motion_type_matched_overlap_index, -1);
  return different_motion_type_matched_overlap_index;
}

adv_geom::Path2dWithJuke CreateDummyPath2dWithJuke(int size, double curvature,
                                                   double pinch, double juke) {
  constexpr double kSamplingDistanceForPath = 0.5;
  std::vector<math::geometry::Point2d> points(size);
  int index = 0;
  std::generate(points.begin(), points.end(), [&]() {
    return math::geometry::Point2d(kSamplingDistanceForPath * (index++), 0);
  });
  std::vector<double> headings(size, 0.0);
  std::vector<double> curvatures(size, curvature);
  std::vector<double> pinches(size, pinch);
  std::vector<double> jukes(size, juke);
  return adv_geom::Path2dWithJuke(std::move(points), std::move(headings),
                                  std::move(curvatures), std::move(pinches),
                                  std::move(jukes));
}

PathForOverlap CreateDummyPathForOverlap(int size, double curvature,
                                         double pinch, double juke,
                                         double max_curvature,
                                         av_comm::CarType car_type) {
  const vehicle_model::pb::AxleRectangularMeasurement ego_shape =
      vehicle_model::GetEgoAxleRectangularMeasurementByType(car_type);
  const VehicleParamsForOverlap vehicle_params(ego_shape, max_curvature);
  return PathForOverlap(CreateDummyPath2dWithJuke(size, curvature, pinch, juke),
                        vehicle_params);
}

std::optional<speed::pb::OverlapSlice> GetFirstOverLapSliceAfterDist(
    const std::vector<speed::pb::OverlapRegion>& overlap_regions,
    const double dist_in_meter, const double ra_to_rb_dist) {
  // First, find the first overlap region that's after dist_in_meter.
  int overlap_region_idx = -1;
  const double trailing_bumper_offset =
      FLAGS_planning_enable_overlap_v2 ? ra_to_rb_dist : 0;
  for (size_t i = 0; i < overlap_regions.size(); i++) {
    if (GetPaddedOverlapEnd(overlap_regions[i]) - trailing_bumper_offset >
        dist_in_meter) {
      overlap_region_idx = i;
      break;
    }
  }

  if (overlap_region_idx < 0) {
    // No region found.
    return std::nullopt;
  }

  // Second, find the first overlap slice in the overlap region that's after
  // dist_in_meter.
  const speed::pb::OverlapRegion& overlap_region =
      overlap_regions[overlap_region_idx];
  for (int i = 0; i < overlap_region.overlap_slices_size(); i++) {
    if (GetPaddedOverlapEnd(overlap_region.overlap_slices(i)) -
            trailing_bumper_offset >
        dist_in_meter) {
      return std::make_optional<speed::pb::OverlapSlice>(
          overlap_region.overlap_slices(i));
    }
  }

  // No slice found.
  return std::nullopt;
}

std::optional<speed::pb::OverlapSlice> GetLastOverLapSliceBeforeDist(
    const std::vector<speed::pb::OverlapRegion>& overlap_regions,
    const double dist_in_meter, const double ra_to_rb_dist) {
  if (overlap_regions.empty()) {
    return std::nullopt;
  }

  // First, find the first overlap region that's after dist_in_meter.
  int overlap_region_idx = -1;
  const double trailing_bumper_offset =
      FLAGS_planning_enable_overlap_v2 ? ra_to_rb_dist : 0;
  for (size_t i = 0; i < overlap_regions.size(); i++) {
    if (GetPaddedOverlapEnd(overlap_regions[i]) - trailing_bumper_offset >
        dist_in_meter) {
      overlap_region_idx = i;
      break;
    }
  }

  if (overlap_region_idx < 0) {
    // Return the last overlap slice on the last overlap region.
    const speed::pb::OverlapRegion& overlap_region =
        overlap_regions[overlap_regions.size() - 1];
    const int slice_index = overlap_region.overlap_slices().size() - 1;
    const speed::pb::OverlapSlice& overlap_slice =
        overlap_region.overlap_slices(slice_index);
    return std::make_optional<speed::pb::OverlapSlice>(overlap_slice);
  }

  // Second, find the last overlap slice in the overlap region that's before
  // dist_in_meter.
  const speed::pb::OverlapRegion& overlap_region =
      overlap_regions[overlap_region_idx];
  for (int i = overlap_region.overlap_slices_size() - 1; i >= 0; --i) {
    if (GetPaddedOverlapEnd(overlap_region.overlap_slices(i)) -
            trailing_bumper_offset <
        dist_in_meter) {
      return std::make_optional<speed::pb::OverlapSlice>(
          overlap_region.overlap_slices(i));
    }
  }

  // No slice found.
  return std::nullopt;
}

std::optional<math::Range1d> GetLatestOverlapRangeBeforeTime(
    const std::vector<speed::pb::OverlapRegion>& overlap_regions,
    const int64_t relative_time_in_msec,
    int64_t& actual_relative_time_in_msec) {
  // First, find the last overlap region that's before relative_time_in_msec.
  int overlap_region_idx = -1;
  for (size_t i = 0; i < overlap_regions.size(); i++) {
    if (overlap_regions[i].end_padded_relative_time_in_msec() <=
        relative_time_in_msec) {
      overlap_region_idx = i;
      continue;
    }

    if (overlap_regions[i].start_padded_relative_time_in_msec() <=
        relative_time_in_msec) {
      overlap_region_idx = i;
      continue;
    }

    // The overlap region is beyond relative_time_in_msec, stop.
    break;
  }

  if (overlap_region_idx < 0) {
    // No region found.
    return std::nullopt;
  }

  // Second, find the actual slice in the overlap region that's before
  // relative_time_in_msec.
  const speed::pb::OverlapRegion& overlap_region =
      overlap_regions[overlap_region_idx];
  int overlap_slice_idx = -1;
  for (int i = 0; i < overlap_region.overlap_slices_size(); i++) {
    if (overlap_region.overlap_slices(i).relative_time_in_msec() <=
        relative_time_in_msec) {
      overlap_slice_idx = i;
      continue;
    }

    break;
  }

  if (overlap_slice_idx < 0) {
    // No slice found.
    return std::nullopt;
  }

  const speed::pb::OverlapSlice& overlap_slice =
      overlap_region.overlap_slices(overlap_slice_idx);

  actual_relative_time_in_msec = overlap_slice.relative_time_in_msec();
  return std::make_optional<math::Range1d>(GetPaddedOverlapStart(overlap_slice),
                                           GetPaddedOverlapEnd(overlap_slice));
}

std::vector<std::pair<double, double>> ComputeArcLengthAtRequiredLatGap(
    const pb::OverlapSlice& slice, double required_lateral_gap_in_meter) {
  const int slice_length = slice.signed_lateral_gap_to_ego_poses_size();
  DCHECK_EQ(slice_length,
            slice.padded_end_path_idx() - slice.padded_start_path_idx() + 1);
  std::vector<std::pair<double, double>> arc_lengths;

  // If the minimum measured lateral gap is larger than the required lateral
  // gap, return an empty vector.
  if (std::abs(slice.signed_lateral_gap()) > required_lateral_gap_in_meter) {
    return arc_lengths;
  }

  // Early return if required lat gap is 0.
  if (math::IsApprox(required_lateral_gap_in_meter, 0.0)) {
    DCHECK(slice.has_ego_moving_distance_strict());
    arc_lengths.push_back(
        std::make_pair(slice.ego_moving_distance_strict().start(),
                       slice.ego_moving_distance_strict().end()));
    return arc_lengths;
  }

  // Separately handle the case when the overlap starts from behind the ego.
  // This is because we don't compute lateral gaps for ego poses that start
  // behind plan init time ego pose with the same resolution.
  if (slice.ego_moving_distance_padded().start() < 0 &&
      ComputeRLGVarArcLengthForFirstPoseOverlap(
          slice, required_lateral_gap_in_meter, slice_length, arc_lengths)) {
    DCHECK(!arc_lengths.empty());
    return arc_lengths;
  }

  // If slice.ego_moving_distance_padded().start() is negative and we reach here
  // it means that the overlap starts beyond the first pose of ego.
  const double padded_start_arc_length =
      std::max(0.0, slice.ego_moving_distance_padded().start());
  const double padded_end_arc_length = slice.ego_moving_distance_padded().end();
  bool prev_within_lat_gap = false;
  for (int i = 0; i < slice_length; ++i) {
    const bool within_lat_gap =
        std::abs(slice.signed_lateral_gap_to_ego_poses(i)) <=
        required_lateral_gap_in_meter;
    if (within_lat_gap && !prev_within_lat_gap) {
      // Note that the arc length starts at index i - 1. This is a conservative
      // estimation and is consistent with the original overlap computation.
      const double start_arc_length =
          padded_start_arc_length +
          std::max(i - 1, 0) * kEgoPathPointDenseSamplingSpacingInMeter;
      if (!arc_lengths.empty() &&
          arc_lengths.back().second >
              start_arc_length - math::constants::kEpsilon) {
        // If the current interval is overlapping with the last interval,
        // merge the current interval with the last one.
        arc_lengths.back().second = 0.0;
      } else {
        // Start a new pair of arc lengths at the rising edge.
        arc_lengths.push_back(std::make_pair(start_arc_length, 0.0));
      }
    } else if (!within_lat_gap && prev_within_lat_gap) {
      // End the pair of arc lengths at the falling edge
      DCHECK(!arc_lengths.empty());
      DCHECK_EQ(arc_lengths.back().second, 0.0);
      // Note that the arc length ends at index i. This is a conservative
      // estimation and is consistent with the original overlap computation.
      arc_lengths.back().second = padded_start_arc_length +
                                  std::min(i, slice_length - 1) *
                                      kEgoPathPointDenseSamplingSpacingInMeter;
    }
    prev_within_lat_gap = within_lat_gap;
  }

  // Finish the pair of arc lengths at the end of overlap slice.
  if (prev_within_lat_gap) {
    DCHECK(!arc_lengths.empty());
    DCHECK_EQ(arc_lengths.back().second, 0.0);
    arc_lengths.back().second = padded_end_arc_length;
  }
  DCHECK(!arc_lengths.empty());
  return arc_lengths;
}

double GetPaddedOverlapStart(const pb::OverlapSlice& slice, bool use_original) {
  if (FLAGS_planning_enable_overlap_v2) {
    DCHECK(slice.has_ego_moving_distance_padded());
    return slice.ego_moving_distance_padded().start();
  }
  if (use_original) {
    DCHECK(slice.has_ra_arc_length_padded_original());
    return slice.ra_arc_length_padded_original().start();
  }
  DCHECK(slice.has_ra_arc_length_padded());
  return slice.ra_arc_length_padded().start();
}

double GetPaddedOverlapEnd(const pb::OverlapSlice& slice, bool use_original) {
  if (FLAGS_planning_enable_overlap_v2) {
    DCHECK(slice.has_ego_moving_distance_padded());
    return slice.ego_moving_distance_padded().end();
  }
  if (use_original) {
    DCHECK(slice.has_ra_arc_length_padded_original());
    return slice.ra_arc_length_padded_original().end();
  }
  DCHECK(slice.has_ra_arc_length_padded());
  return slice.ra_arc_length_padded().end();
}

double GetStrictOverlapStart(const pb::OverlapSlice& slice, bool use_original) {
  if (FLAGS_planning_enable_overlap_v2) {
    DCHECK(slice.has_ego_moving_distance_strict());
    return slice.ego_moving_distance_strict().start();
  }
  if (use_original) {
    DCHECK(slice.has_ra_arc_length_strict_original());
    return slice.ra_arc_length_strict_original().start();
  }
  DCHECK(slice.has_ra_arc_length_strict());
  return slice.ra_arc_length_strict().start();
}

double GetStrictOverlapEnd(const pb::OverlapSlice& slice, bool use_original) {
  if (FLAGS_planning_enable_overlap_v2) {
    DCHECK(slice.has_ego_moving_distance_strict());
    return slice.ego_moving_distance_strict().end();
  }
  if (use_original) {
    DCHECK(slice.has_ra_arc_length_strict_original());
    return slice.ra_arc_length_strict_original().end();
  }
  DCHECK(slice.has_ra_arc_length_strict());
  return slice.ra_arc_length_strict().end();
}

double GetPaddedOverlapStart(const pb::OverlapRegion& region) {
  if (FLAGS_planning_enable_overlap_v2) {
    DCHECK(region.has_ego_moving_distance_padded());
    return region.ego_moving_distance_padded().start();
  }
  DCHECK(region.has_ra_arc_length_padded());
  return region.ra_arc_length_padded().start();
}
double GetPaddedOverlapEnd(const pb::OverlapRegion& region) {
  if (FLAGS_planning_enable_overlap_v2) {
    DCHECK(region.has_ego_moving_distance_padded());
    return region.ego_moving_distance_padded().end();
  }
  DCHECK(region.has_ra_arc_length_padded());
  return region.ra_arc_length_padded().end();
}
double GetStrictOverlapStart(const pb::OverlapRegion& region) {
  if (FLAGS_planning_enable_overlap_v2) {
    DCHECK(region.has_ego_moving_distance_strict());
    return region.ego_moving_distance_strict().start();
  }
  DCHECK(region.has_ra_arc_length_strict());
  return region.ra_arc_length_strict().start();
}

double GetStrictOverlapEnd(const pb::OverlapRegion& region) {
  if (FLAGS_planning_enable_overlap_v2) {
    DCHECK(region.has_ego_moving_distance_strict());
    return region.ego_moving_distance_strict().end();
  }
  DCHECK(region.has_ra_arc_length_strict());
  return region.ra_arc_length_strict().end();
}

pb::LongitudinalRange GetStrictOverlap(const pb::OverlapSlice& slice,
                                       bool use_original) {
  if (FLAGS_planning_enable_overlap_v2) {
    DCHECK(slice.has_ego_moving_distance_strict());
    return slice.ego_moving_distance_strict();
  }
  if (use_original) {
    DCHECK(slice.has_ra_arc_length_strict_original());
    return slice.ra_arc_length_strict_original();
  }
  DCHECK(slice.has_ra_arc_length_strict());
  return slice.ra_arc_length_strict();
}

pb::LongitudinalRange GetPaddedOverlap(const pb::OverlapSlice& slice,
                                       bool use_original) {
  if (FLAGS_planning_enable_overlap_v2) {
    DCHECK(slice.has_ego_moving_distance_padded());
    return slice.ego_moving_distance_padded();
  }
  if (use_original) {
    DCHECK(slice.has_ra_arc_length_padded_original());
    return slice.ra_arc_length_padded_original();
  }
  DCHECK(slice.has_ra_arc_length_padded());
  return slice.ra_arc_length_padded();
}

pb::LongitudinalRange FindEarliestStrictOverlapSliceLongitudinalRange(
    const speed::pb::OverlapRegion& overlap) {
  pb::LongitudinalRange res;
  res.set_start(std::numeric_limits<double>::infinity());
  res.set_end(std::numeric_limits<double>::infinity());
  for (const auto& slice : overlap.overlap_slices()) {
    // Loop over all the slices to find the earliest slice, it is likely that
    // more than one overlap slice share the same timestamp (when ego path is
    // curved, e.g. uturn). In this situation, we want to find the overlap slice
    // which is closer to ego(has smaller arc length).
    if (slice.relative_time_in_msec() ==
        overlap.start_strict_relative_time_in_msec()) {
      if (HasStrictOverlap(slice)) {
        pb::LongitudinalRange curr_range = GetStrictOverlap(slice);
        if (curr_range.start() < res.start()) {
          res = curr_range;
        }
      }
    }
  }
  return res;
}

bool HasStrictOverlap(const pb::OverlapSlice& slice, bool use_original) {
  if (FLAGS_planning_enable_overlap_v2) {
    return slice.has_ego_moving_distance_strict();
  }
  if (use_original) {
    return slice.has_ra_arc_length_strict_original();
  }
  return slice.has_ra_arc_length_strict();
}

bool HasPaddedOverlap(const pb::OverlapSlice& slice, bool use_original) {
  if (FLAGS_planning_enable_overlap_v2) {
    return slice.has_ego_moving_distance_padded();
  }
  if (use_original) {
    return slice.has_ra_arc_length_padded_original();
  }
  return slice.has_ra_arc_length_padded();
}

pb::LongitudinalRange GetPaddedOverlap(const pb::OverlapRegion& region) {
  if (FLAGS_planning_enable_overlap_v2) {
    DCHECK(region.has_ego_moving_distance_padded());
    return region.ego_moving_distance_padded();
  }
  DCHECK(region.has_ra_arc_length_padded());
  return region.ra_arc_length_padded();
}

bool HasStrictOverlap(const pb::OverlapRegion& region) {
  return region.contain_strict_overlap();
}

}  // namespace speed
}  // namespace planner
