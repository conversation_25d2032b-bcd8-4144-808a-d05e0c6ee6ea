package(default_visibility = ["//visibility:public"])

cc_library(
    name = "general_unstuck_planner",
    srcs = [
        "general_unstuck_planner.cpp",
    ],
    hdrs = [
        "general_unstuck_planner.h",
    ],
    include_prefix = "planner/open_space/unstuck_planner",
    deps = [
        ":base_unstuck_planner",
        "//onboard/common/rt_event",
        "//onboard/planner:voy_rt_event_planner",
        "//onboard/planner/open_space/motion_plan_generator/open_space_path_searcher",
        "//protobuf_cpp:protos_cpp",
    ],
)

cc_library(
    name = "uturn_unstuck_planner_util",
    srcs = [
        "uturn_unstuck_planner_util.cpp",
    ],
    hdrs = [
        "uturn_unstuck_planner_util.h",
    ],
    include_prefix = "planner/open_space/unstuck_planner",
    deps = [
        "//onboard/hdmap:lib",
        "//onboard/planner:voy_trace_provider_planner",
        "//onboard/planner/behavior/util/lane_sequence_geometry",
        "//onboard/planner/open_space/motion_plan_generator/open_space_path_searcher:reeds_shepp_path_generator",
        "//onboard/planner/selection:trajectory_meta",
        "//onboard/planner/speed/constraint:constraint_util",
        "//onboard/planner/world_model",
        "//protobuf_cpp:protos_cpp",
    ],
)

cc_library(
    name = "base_unstuck_planner",
    srcs = [
        "base_unstuck_planner.cpp",
    ],
    hdrs = [
        "base_unstuck_planner.h",
    ],
    include_prefix = "planner/open_space/unstuck_planner",
    deps = [
        "//onboard/common/math",
        "//onboard/planner/behavior:lane_common",
        "//onboard/planner/behavior/util/lane_sequence_geometry",
        "//onboard/planner/open_space/constraints:motion_plan_problem_manager",
        "//onboard/planner/open_space/unstuck_planner:unstuck_planner_util",
        "//onboard/planner/selection:trajectory_meta",
        "//onboard/planner/world_model",
        "//protobuf_cpp:protos_cpp",
    ],
)

cc_library(
    name = "uturn_unstuck_motion_plan_roi",
    srcs = [
        "uturn_unstuck_motion_plan_roi.cpp",
    ],
    hdrs = [
        "uturn_unstuck_motion_plan_roi.h",
    ],
    include_prefix = "planner/open_space/unstuck_planner",
    deps = [
        ":uturn_unstuck_planner_util",
        "//onboard/common/rt_event",
        "//onboard/planner:voy_rt_event_planner",
        "//onboard/planner/behavior/util/lane_sequence_geometry",
        "//onboard/planner/selection:trajectory_meta",
    ],
)

cc_library(
    name = "uturn_unstuck_motion_plan_target_info",
    srcs = [
        "uturn_unstuck_motion_plan_target_info.cpp",
    ],
    hdrs = [
        "uturn_unstuck_motion_plan_target_info.h",
    ],
    include_prefix = "planner/open_space/unstuck_planner",
    deps = [
        ":uturn_unstuck_motion_plan_roi",
        ":uturn_unstuck_planner_util",
        "//onboard/planner/open_space/motion_plan_generator/open_space_path_searcher:reeds_shepp_path_generator",
        "@voy-sdk//:glog",
    ],
)

cc_library(
    name = "uturn_unstuck_planner",
    srcs = [
        "uturn_unstuck_planner.cpp",
    ],
    hdrs = [
        "uturn_unstuck_planner.h",
    ],
    include_prefix = "planner/open_space/unstuck_planner",
    deps = [
        ":base_unstuck_planner",
        ":uturn_unstuck_motion_plan_generator",
        ":uturn_unstuck_motion_plan_roi",
        ":uturn_unstuck_motion_plan_target_info",
        ":uturn_unstuck_planner_util",
        "//onboard/common/rt_event",
        "//onboard/planner:voy_rt_event_planner",
    ],
)

cc_library(
    name = "pull_out_unstuck_planner_util",
    srcs = [
        "pull_out_unstuck_planner_util.cpp",
    ],
    hdrs = [
        "pull_out_unstuck_planner_util.h",
    ],
    include_prefix = "planner/open_space/unstuck_planner",
    deps = [
        "//onboard/common/math",
        "//onboard/planner:voy_planner_gflags",
        "//onboard/planner/elective_lane_change:elective_lane_change_decider_utils",
        "//onboard/planner/selection:trajectory_meta",
        "//onboard/planner/selection/scenario_selectors/pull_out_scenario_selector:pull_out_meta_data",
        "//onboard/planner/utility/unstuck",
        "//onboard/planner/world_model",
        "//protobuf_cpp:protos_cpp",
    ],
)

cc_library(
    name = "pull_out_unstuck_planner",
    srcs = [
        "pull_out_unstuck_planner.cpp",
    ],
    hdrs = [
        "pull_out_unstuck_planner.h",
    ],
    include_prefix = "planner/open_space/unstuck_planner",
    deps = [
        ":base_unstuck_planner",
        ":pull_out_unstuck_planner_util",
        "//onboard/common/av_comm:voy_common_common",
        "//onboard/planner:voy_rt_event_planner",
    ],
)

cc_library(
    name = "unstuck_planner_util",
    srcs = [
        "unstuck_planner_util.cpp",
    ],
    hdrs = [
        "unstuck_planner_util.h",
    ],
    include_prefix = "planner/open_space/unstuck_planner",
    deps = [
        ":uturn_unstuck_planner_util",
        "//onboard/common/rt_event",
        "//onboard/planner:voy_rt_event_planner",
        "//onboard/planner/selection:trajectory_meta",
        "//onboard/planner/selection/scenario_selectors/pull_out_scenario_selector:pull_out_meta_data",
        "//onboard/planner/world_model",
        "//protobuf_cpp:protos_cpp",
        "@voy-sdk//:glog",
    ],
)

cc_library(
    name = "uturn_unstuck_motion_plan_generator",
    srcs = [
        "uturn_unstuck_motion_plan_generator.cpp",
    ],
    hdrs = [
        "uturn_unstuck_motion_plan_generator.h",
    ],
    include_prefix = "planner/open_space/unstuck_planner",
    deps = [
        ":uturn_unstuck_motion_plan_roi",
        ":uturn_unstuck_motion_plan_target_info",
        "//onboard/planner/open_space/motion_plan_generator/open_space_path_searcher:arc_path_2d",
        "//onboard/planner/open_space/motion_plan_generator/open_space_path_searcher:reeds_shepp_path_generator",
        "//onboard/planner/world_model",
        "//protobuf_cpp:protos_cpp",
        "@voy-sdk//:glog",
    ],
)

cc_library(
    name = "hard_boundary_unstuck_planner",
    srcs = [
        "hard_boundary_unstuck_planner.cpp",
    ],
    hdrs = [
        "hard_boundary_unstuck_planner.h",
    ],
    include_prefix = "planner/open_space/unstuck_planner",
    deps = [
        ":base_unstuck_planner",
        "//onboard/planner/utility/unstuck",
        "//protobuf_cpp:protos_cpp",
    ],
)

cc_library(
    name = "unstuck_planner_invoker",
    srcs = [
        "unstuck_planner_invoker.cpp",
    ],
    hdrs = [
        "unstuck_planner_invoker.h",
    ],
    include_prefix = "planner/open_space/unstuck_planner",
    deps = [
        ":base_unstuck_planner",
        ":general_unstuck_planner",
        ":hard_boundary_unstuck_planner",
        ":pull_out_unstuck_planner",
        ":uturn_unstuck_planner",
    ],
)
