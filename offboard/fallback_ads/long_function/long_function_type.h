
#ifndef OFFBOARD_FALLBACK_ADS_LONG_FUNCTION_LONG_FUNCTION_TYPE_H_
#define OFFBOARD_FALLBACK_ADS_LONG_FUNCTION_LONG_FUNCTION_TYPE_H_

#include <stdbool.h>
#include <stdint.h>

typedef struct {
  float distance_x;
  float velocity_x;
  float accel_x;
  float distance_y;
  float velocity_y;
  float accel_y;
} MotionState;

typedef enum {
  LANE_MEASURE_TYPE_UNKNOWN = 0,
  LANE_MEASURE_TYPE_VIRTUAL_PARALLEL = 1,
  LANE_MEASURE_TYPE_MEASURED_PARALLEL = 2,
  LANE_MEASURE_TYPE_VIRTUAL = 3,
  LANE_MEASURE_TYPE_MEASURED = 4
} LaneMeasureType;

typedef enum {
  LANE_TYPE_SOLID = 0,
  LANE_TYPE_DASHED = 1,
  LANE_TYPE_DOUBLE_DASHED_DASHED = 2,
  LANE_TYPE_DOUBLE_DASHED_SOLID = 3,
  LANE_TYPE_DOUBLE_SOLID_DASHED = 4,
  LANE_TYPE_DOUBLE_SOLID_SOLID = 5,
  LANE_TYPE_UNKNOWN = 14,
} LaneType;

typedef enum {
  LANE_COLOR_NOT_DETECTED = 0,
  LANE_COLOR_WHITE = 1,
  LANE_COLOR_YELLOW = 2,
  LANE_COLOR_WHITE_AND_YELLOW = 3,
  LANE_COLOR_UNKNOWN = 4,
} LaneColor;

typedef struct {
  uint8_t lane_id;
  LaneMeasureType measure_type;
  float exist_probability;
  float parse_confidence;
  float lane_fitting_rmse;
  LaneType lane_type;
  float lane_width;
  LaneColor lane_color;
  float dx_start;
  float dx_start_std;
  float dx_end;
  float dx_end_std;
  float lateral_distance;
  float heading_angle;
  float curvature;
  float curvature_rate;
  bool obstacle_flag;
} LaneInfo;

typedef enum {
  STANDBY = 0,
  EMERGENCY_BRAKE = 1,
  OBJECT_FOLLOWING_BRAKE = 2,
  LANE_FOLLOWING_BRAKE = 3,
  COMBINE_BRAKE = 4,
  FREE_STOP = 5
} LongSystemState;

#endif  // OFFBOARD_FALLBACK_ADS_LONG_FUNCTION_LONG_FUNCTION_TYPE_H_
