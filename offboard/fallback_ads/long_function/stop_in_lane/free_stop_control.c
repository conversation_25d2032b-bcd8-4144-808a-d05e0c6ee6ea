#include "long_function/stop_in_lane/free_stop_control.h"

#include <math.h>

#include "common_math_library/common_method.h"
#include "common_math_library/vehicle_parameters.h"
#include "long_function/long_function_parameter.h"
#include "long_function/long_function_type.h"

float CalculateFreeStopDeceleration(float ego_velocity) {
  static float accel_request_filter = 0.0F;

  float raw_accel_request =
      CML_Interpolation1D(ego_velocity, GetFscFreeStopAccRequest(),
                          FSC_FREE_STOP_ACC_REQUEST_TABLE_ROWS);
  // gradient filter
  CML_RateLimit(raw_accel_request, GetFscMinJerk(), GetFscMaxJerk(),
                LGT_CONTROL_TASK_CYCLE_TIME, &accel_request_filter);

  // avoid jolting when the vehicle is about to stop
  float accel_request = 0.0F;
  if (ego_velocity < GetFscEgoVelocityThreshold()) {
    accel_request = GetFscAccelRequestForStop();
  } else {
    accel_request = accel_request_filter;
  }

  return accel_request;
}

float CalculateAccelRequestByLaneLength(float ego_velocity,
                                        LaneInfo left_lane_info,
                                        LaneInfo right_lane_info) {
  float accel_request = 0.0F;

  bool both_lanes_exist =
      (left_lane_info.lane_id != 0U) && (right_lane_info.lane_id != 0U);

  if (both_lanes_exist) {
    // simple method, s = v^2 / (2 * a)
    float lane_length = fminf(left_lane_info.dx_end, right_lane_info.dx_end);
    float brake_distance = lane_length -
                           ego_velocity * GetFscEgoBrakeResponseTime() -
                           GetVehicleRearAxleToFrontBumperDistance();
    accel_request =
        -0.5F * ego_velocity * ego_velocity / fmaxf(brake_distance, 0.01F);
  } else {
    if (ego_velocity < GetFscEgoVelocityThreshold()) {
      accel_request = GetFscAccelRequestForStop();
    } else {
      // todo, 20250102, to be implemented
      accel_request = -10.0F;
    }
  }

  return accel_request;
}
