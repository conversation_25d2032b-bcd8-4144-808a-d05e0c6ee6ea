#include "planner/world_model/immediate_pull_over/immediate_pull_over_utility.h"

#include <algorithm>
#include <map>
#include <optional>
#include <utility>

#include "planner/behavior/util/lane_common/lane_sequence_iterator.h"
#include "planner/behavior/util/lane_sequence_geometry/lane_sequence_geometry_utility.h"
#include "planner/utility/physics/motion_model_1d.h"
#include "planner/world_model/pickup_dropoff_zone/pickup_dropoff_zone_info_util.h"

namespace planner {
namespace {
// The comfort deceleration rate.
constexpr double kComfortDecelerationInMpss = -2.0;
// The pull over prepare time.
constexpr double kPullOverPrepareTimeInSec = 2.0;
// The distance that ego crosses destination, within which planning is allowed
// to send immediate pullover request.
constexpr double
    kMaxAccumulatedDistanceToTryRoutingSourceImmediatePullOverInMeter = 100.0;
// If the distance between ego current position and routing destination point
// exceeds this threshold, planner will refuse to trigger and terminate
// routing-source immediate pull over.
constexpr double kThresholdDistToRefuseImmediatePullOverInMeter = 120.0;
// When dist_to_destination is within this threshold, we prefer to trigger the
// regular pull over.
constexpr double kThresholdDistToDestToExecuteRegularPullOver = 120.0;
// The distance buffer to check whether pdz near junction.
constexpr double kBufferDistanceToJunctionInMeters = 30.0;
// The distance buffer before and after single lane.
constexpr double kRightTurnSingleLaneBufferInMeters = 10.0;
// The pull over distance buffer.
constexpr double kImmediatePullOverDistanceBuffer = 10.0;
// The speed threshold for considering the ego is moving slowly.
constexpr double kEgoSlowMoveSpeedInMps = 0.8;
// The buffer distance to update the pdz when ego in the rightmost vehicle lane.
constexpr double kBufferDistanceToUpdatePDZInMeter = 30.0;
// When distance to next junction is within this threshold, don't request
// immediate right lane change and don't restrict left lane change if pdz has
// not be found.
constexpr double kThresholdDistanceToJunctionForLaneChangeRequestInMeter =
    150.0;
// The offset distance to check if the pdz left boundary near a median strip.
constexpr double kOffsetDistanceToCheckWhetherPDZNearMedianStrip = 1.0;
// The max duration for trying proposal.
int64_t kMaxDurationForTryingProposalInMSec = 1000;

// Returns true if pdz near a junction.
bool IsPDZNearJunction(const PickupDropoffZoneInfo& pdz_info) {
  DCHECK(!pdz_info.extended_overlapped_lanes.empty());
  DCHECK(pdz_info.extended_nominal_path.has_value());

  const double pdz_start_point_arclength =
      pdz_info.extended_nominal_path
          ->GetProximity(pdz_info.nominal_path.GetStartPoint(),
                         math::pb::UseExtensionFlag::kAllow)
          .arc_length;
  const double pdz_end_point_arclength =
      pdz_info.extended_nominal_path
          ->GetProximity(pdz_info.nominal_path.GetEndPoint(),
                         math::pb::UseExtensionFlag::kAllow)
          .arc_length;
  const math::Range1d pdz_range_with_buffer(
      pdz_start_point_arclength - kBufferDistanceToJunctionInMeters,
      pdz_end_point_arclength + kBufferDistanceToJunctionInMeters);
  // Check if the pdz is close to the successor junction of the end of extended
  // overlapped lanes.
  for (const auto& successor :
       pdz_info.extended_overlapped_lanes.back()->successors()) {
    if (!(*successor).IsInJunction()) {
      continue;
    }

    const auto junction_start_point_arclength =
        pdz_info.extended_nominal_path
            ->GetProximity((*successor).center_line().GetStartPoint(),
                           math::pb::UseExtensionFlag::kAllow)
            .arc_length;
    if (math::IsInRange(junction_start_point_arclength,
                        pdz_range_with_buffer.start_pos,
                        pdz_range_with_buffer.end_pos)) {
      return true;
    }
  }

  // Check if the pdz is close to the predecessor junction of the begin of
  // extended overlapped lanes.
  for (const auto& predecessor :
       pdz_info.extended_overlapped_lanes.front()->predecessors()) {
    if (!(*predecessor).IsInJunction()) {
      continue;
    }

    const auto junction_end_point_arclength =
        pdz_info.extended_nominal_path
            ->GetProximity((*predecessor).center_line().GetEndPoint(),
                           math::pb::UseExtensionFlag::kAllow)
            .arc_length;
    if (math::IsInRange(junction_end_point_arclength,
                        pdz_range_with_buffer.start_pos,
                        pdz_range_with_buffer.end_pos)) {
      return true;
    }
  }

  return false;
}

// Returns true if pdz locate or near the right turn signal lane.
bool IsPDZLocateOrNearRightTurnSingleLane(
    const PickupDropoffZoneInfo& pdz_info) {
  auto is_single_lane = [](const pnc_map::Lane* lane_ptr) {
    return (lane_ptr->IsRightmostDrivableLane() &&
            lane_ptr->IsLeftmostDrivableLane());
  };

  DCHECK(!pdz_info.extended_overlapped_lanes.empty());
  DCHECK(pdz_info.extended_nominal_path.has_value());
  const double pdz_start_point_arclength =
      pdz_info.extended_nominal_path
          ->GetProximity(pdz_info.nominal_path.GetStartPoint(),
                         math::pb::UseExtensionFlag::kAllow)
          .arc_length;
  const double pdz_end_point_arclength =
      pdz_info.extended_nominal_path
          ->GetProximity(pdz_info.nominal_path.GetEndPoint(),
                         math::pb::UseExtensionFlag::kAllow)
          .arc_length;
  const math::Range1d pdz_range_with_buffer(
      pdz_start_point_arclength - kRightTurnSingleLaneBufferInMeters,
      pdz_end_point_arclength + kRightTurnSingleLaneBufferInMeters);

  for (const auto lane : pdz_info.extended_overlapped_lanes) {
    if (lane->turn_signal() == hdmap::Lane_TurnSignal_RIGHT_SIGNAL &&
        is_single_lane(lane)) {
      const double lane_start_arclength =
          pdz_info.extended_nominal_path
              ->GetProximity(lane->center_line().GetStartPoint(),
                             math::pb::UseExtensionFlag::kAllow)
              .arc_length;
      const double lane_end_arclength =
          pdz_info.extended_nominal_path
              ->GetProximity(lane->center_line().GetEndPoint(),
                             math::pb::UseExtensionFlag::kAllow)
              .arc_length;
      const math::Range1d lane_range =
          lane_start_arclength < lane_end_arclength
              ? math::Range1d(lane_start_arclength, lane_end_arclength)
              : math::Range1d(lane_end_arclength, lane_start_arclength);
      if (math::AreRangesOverlapping(pdz_range_with_buffer, lane_range)) {
        return true;
      }
    }
  }
  return false;
}

// Returns true if pdz is located at the forbidden stop cell.
bool IsPDZLocatedAtForbiddenStopCell(
    const PickupDropoffZoneInfo& pdz_info,
    const std::vector<hdmap::StopCellEntity::Type>&
        forbidden_stop_types_for_mrc) {
  const auto pdz_dest_cell_type = pdz_info.dest_cell.type();
  return std::any_of(
      forbidden_stop_types_for_mrc.begin(), forbidden_stop_types_for_mrc.end(),
      [&pdz_dest_cell_type](hdmap::StopCellEntity::Type stop_cell_type) {
        return pdz_dest_cell_type == stop_cell_type;
      });
}

// Returns true if the right lane of input lane is on the route.
bool IsRightLaneOnRoute(
    const std::map<int64_t, lane_selection::LaneToNextJunctionInfo>&
        lane_to_junction_info_map,
    const pnc_map::Lane* input_lane) {
  if (input_lane == nullptr || input_lane->IsRightmostDrivableLane() ||
      input_lane->right_lane() == nullptr) {
    return false;
  }

  const auto lane_it =
      lane_to_junction_info_map.find(input_lane->right_lane()->id());
  if (lane_it == lane_to_junction_info_map.end()) {
    return false;
  }
  return lane_it->second.side() == pb::SideOfRoute::kOnRoute;
}

// Finds target lane from lane sequence by a input point.
std::vector<const pnc_map::Lane*>::const_iterator GetTargetLaneByPoint(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const math::geometry::Point2d& target_point) {
  const auto target_lane_iter = std::find_if(
      lane_sequence.begin(), lane_sequence.end(),
      [&target_point](const auto* lane_ptr) {
        DCHECK(lane_ptr != nullptr);
        const auto query_info = lane_ptr->center_line().GetProximity(
            target_point, math::pb::UseExtensionFlag::kForbid);
        return query_info.relative_position ==
                   math::RelativePosition::kWithIn &&
               math::geometry::Within(target_point,
                                      lane_ptr->section()->border());
      });
  return target_lane_iter;
}

// Returns true if the lane before the junction in the last lane follow lane
// sequence, whose right lane is on the route; or there is not a junction
// between ego and pdz.
bool IsRightLaneBeforeJunctionOnRoute(
    const RobotState& robot_state,
    const std::vector<const pnc_map::Lane*>& last_lane_follow_lane_sequence,
    const std::map<int64_t, lane_selection::LaneToNextJunctionInfo>&
        lane_to_junction_info_map,
    const pnc_map::Lane& current_lane, const PickupDropoffZoneInfo& pdz_info) {
  bool has_junction_between_ego_and_pdz = false;
  auto iter = LaneSequenceIterator::FindLane(last_lane_follow_lane_sequence,
                                             current_lane.id());
  if (iter == last_lane_follow_lane_sequence.end()) {
    const math::geometry::Point2d ego_position(
        robot_state.current_state_snapshot().x(),
        robot_state.current_state_snapshot().y());
    iter = GetTargetLaneByPoint(last_lane_follow_lane_sequence, ego_position);
    if (iter == last_lane_follow_lane_sequence.end()) {
      return false;
    }
  }

  // Find the lane before the junction which is between ego and pdz.
  for (; std::next(iter) != last_lane_follow_lane_sequence.end(); ++iter) {
    if ((*iter)
            ->center_line()
            .GetProximity(pdz_info.nominal_path.GetStartPoint(),
                          math::pb::UseExtensionFlag::kForbid)
            .relative_position == math::RelativePosition::kWithIn) {
      break;
    }

    if ((*std::next(iter))->IsInJunction()) {
      has_junction_between_ego_and_pdz = true;
      break;
    }
  }

  // If there is not a junction between ego and pdz, right lane change is
  // allowed.
  if (!has_junction_between_ego_and_pdz) {
    return true;
  }

  return IsRightLaneOnRoute(lane_to_junction_info_map, *iter);
}

// Returns true if the pdz if near the lane follow lane sequence.
bool IsPDZNearLaneFollowLaneSequence(
    const std::vector<const pnc_map::Lane*>& lane_follow_lane_sequence,
    const std::optional<PickupDropoffZoneInfo>& pdz_info) {
  if (!pdz_info.has_value() || lane_follow_lane_sequence.empty()) {
    return false;
  }

  const auto target_lane_iter = GetTargetLaneByPoint(
      lane_follow_lane_sequence, pdz_info.value().projection_point);
  const pnc_map::Lane* lane_closest_to_pdz =
      target_lane_iter == lane_follow_lane_sequence.end()
          ? lane_follow_lane_sequence.back()
          : *target_lane_iter;
  return pull_over::IsPDZNearLaneSequence(
      lane_follow_lane_sequence, *lane_closest_to_pdz, pdz_info.value());
}

// Avoid pdz whose left boundary near a median strip, which will stuck other
// agents.
bool IsPDZLeftBoundaryNearMedianStrip(const PickupDropoffZoneInfo& pdz_info) {
  DCHECK(pdz_info.road != nullptr);
  DCHECK(pdz_info.left_boundary.has_value());
  if (std::none_of(pdz_info.road->zone_infos().begin(),
                   pdz_info.road->zone_infos().end(), [](const auto& zone) {
                     return zone.zone->type() ==
                            hdmap::Zone_ZoneType::Zone_ZoneType_MEDIAN_STRIP;
                   })) {
    return false;
  }

  const math::geometry::PolylineCurve2d offset_left_boundary =
      math::geometry::Offset(pdz_info.left_boundary.value(),
                             kOffsetDistanceToCheckWhetherPDZNearMedianStrip);
  return std::any_of(
      pdz_info.road->zone_infos().begin(), pdz_info.road->zone_infos().end(),
      [&offset_left_boundary](const auto& zone) {
        return zone.zone->type() ==
                   hdmap::Zone_ZoneType::Zone_ZoneType_MEDIAN_STRIP &&
               math::geometry::Intersects(zone.zone->border(),
                                          offset_left_boundary);
      });
}
}  // namespace

bool IsPDZIntersectsWithConstructionZone(
    const PickupDropoffZoneInfo& pdz_info,
    const std::vector<ConstructionZone>& construction_zones) {
  return std::any_of(construction_zones.begin(), construction_zones.end(),
                     [&pdz_info](const ConstructionZone& construction_zone) {
                       return pdz_info.border.has_value() &&
                              math::geometry::Intersects(
                                  construction_zone.contour,
                                  pdz_info.border.value());
                     });
}

bool IsPDZQualifiedForMRC(const PickupDropoffZoneInfo& pdz_info,
                          const std::vector<hdmap::StopCellEntity::Type>&
                              forbidden_stop_types_for_mrc) {
  // Avoid pdz near junction.
  if (IsPDZNearJunction(pdz_info)) {
    return false;
  }

  // Avoid pdz located at forbidden stop cell.
  if (IsPDZLocatedAtForbiddenStopCell(pdz_info, forbidden_stop_types_for_mrc)) {
    return false;
  }

  // Avoid pdz locate or near the right turn single lane.
  return !IsPDZLocateOrNearRightTurnSingleLane(pdz_info);
}

pb::RouteDirection ConvertToRouteDirection(const pb::TurnInfo& path_turn_info) {
  switch (path_turn_info.turn_type()) {
    // If regional path does not have junction, it will return UNKNOWN_TURN.
    case hdmap::Lane_Turn_UNKNOWN_TURN:
      return pb::kBeforeJunction;
    case hdmap::Lane_Turn_STRAIGHT:
      return pb::kStraightAfterJunction;
    case hdmap::Lane_Turn_LEFT:
      return pb::kLeftTurnAfterJunction;
    case hdmap::Lane_Turn_RIGHT:
      return pb::kRightTurnAfterJunction;
    case hdmap::Lane_Turn_U_TURN:
      return pb::kUTurnAfterJunction;
    default:
      return pb::kUnknownDirection;
  }
}

double GetRequiredLongitudinalDistForComfortPullOver(double ego_speed) {
  const double pull_over_prepare_distance =
      ego_speed * kPullOverPrepareTimeInSec;
  const double deceleration_distance = physics::GetOdom(
      /*o=*/0.0, /*v=*/ego_speed,
      /*a=*/kComfortDecelerationInMpss, /*j=*/0.0,
      /*t=*/-ego_speed / kComfortDecelerationInMpss);
  return pull_over_prepare_distance + deceleration_distance +
         kImmediatePullOverDistanceBuffer;
}

bool IsLongitudinalDistanceBetweenEgoAndPDZSufficient(
    const std::vector<const pnc_map::Lane*>& last_lane_follow_lane_sequence,
    const RobotState& robot_state, const PickupDropoffZoneInfo& pdz_info) {
  if (last_lane_follow_lane_sequence.empty()) {
    return false;
  }

  // If ego is moving slowly, the check of longitudinal distance doesn't need to
  // be too strict.
  if (robot_state.current_state_snapshot().speed() < kEgoSlowMoveSpeedInMps) {
    return true;
  }

  const math::geometry::PolylineCurve2d nominal_path =
      planner::lane_selection::GetLaneSequenceCurve(
          last_lane_follow_lane_sequence,
          planner::lane_selection::LaneCurveType::kCenterLine);
  const auto middle_point_of_pdz = pdz_info.nominal_path.GetInterp(
      0.5 * pdz_info.nominal_path.GetTotalArcLength());
  const auto pdz_query_info = nominal_path.GetProximity(
      middle_point_of_pdz, math::pb::UseExtensionFlag::kForbid);
  if (pdz_query_info.relative_position != math::RelativePosition::kWithIn) {
    return true;
  }
  const double ego_arclength =
      nominal_path
          .GetProximity(
              robot_state.current_state_snapshot().front_bumper_position(),
              math::pb::UseExtensionFlag::kForbid)
          .arc_length;
  return pdz_query_info.arc_length - ego_arclength >
         GetRequiredLongitudinalDistForComfortPullOver(
             robot_state.current_state_snapshot().speed());
}

bool IsRedirectingModePDZBetterThanLocalModePDZ(
    const RobotState& robot_state,
    const PickupDropoffZoneInfo& pdz_info_from_redirecting_mode,
    const PickupDropoffZoneInfo& pdz_info_from_local_mode) {
  // Select the pdz that is closer to the ego current position.
  const math::geometry::Point2d ego_position(
      robot_state.current_state_snapshot().x(),
      robot_state.current_state_snapshot().y());
  const double dist_to_pdz_info_from_local_mode =
      math::geometry::ComparableDistance(
          ego_position, pdz_info_from_local_mode.projection_point);
  const double dist_to_pdz_info_from_redirecting_mode =
      math::geometry::ComparableDistance(
          ego_position, pdz_info_from_redirecting_mode.projection_point);
  return dist_to_pdz_info_from_redirecting_mode <
         dist_to_pdz_info_from_local_mode;
}

bool ShouldIgnoreOrTerminateRoutingSourceImmediatePullOver(
    const RegionalMap& regional_map, const RobotState& robot_state,
    const routing::pb::RoutePoint& destination,
    pb::PullOverProgress pull_over_progress, double accumulated_distance) {
  // Terminate when the mode is not auto mode.
  // 1. When receiving a new routing-source immediate pull over request, ego is
  // not in auto mode;
  // 2. When ego is executing routing-source immediate pull over, the mode
  // switches from auto mode to other mode.
  if (!robot_state.IsInAutonomousMode()) {
    return true;
  }

  // When receiving a new routing-source immediate pull over request, if
  // distance_to_destination_m is less than threshold of distance to destination
  // to trigger pull over with pdz, execute the regular pull over instead of
  // routing-source immediate pull over.
  if (regional_map.regional_path.distance_to_destination_m <=
      kThresholdDistToDestToExecuteRegularPullOver) {
    return true;
  }

  // Don't terminate when pull over is triggered when ego is executing
  // routing-source immediate pull over.
  if (pull_over_progress != pb::PullOverProgress::kNotTriggered) {
    return false;
  }

  // If ego has passed the routing destination too far, ego will terminate
  // immediate pull over and go back this route destination by re-route.
  // Terminate when the accumulated distance is greater than the threshold or
  // the distance between ego current position and routing destination point
  // exceeds the threshold.
  const math::geometry::Point2d routing_dest_point(destination.position().x(),
                                                   destination.position().y());
  const math::geometry::Point2d ego_position(
      robot_state.current_state_snapshot().x(),
      robot_state.current_state_snapshot().y());
  return math::geometry::ComparableDistance(ego_position, routing_dest_point) >
             kThresholdDistToRefuseImmediatePullOverInMeter *
                 kThresholdDistToRefuseImmediatePullOverInMeter ||
         accumulated_distance >
             kMaxAccumulatedDistanceToTryRoutingSourceImmediatePullOverInMeter;
}

bool ShouldIgnoreOrTerminateImmediatePullOverWhenEgoNearDestination(
    const RegionalMap& regional_map, bool has_found_immediate_pullover_pdz,
    pb::PullOverProgress pull_over_progress) {
  if (regional_map.regional_path.distance_to_destination_m <=
      kThresholdDistToDestToExecuteRegularPullOver) {
    // Ignore or terminate immediate pull over when pull over is not triggered
    // or has not found immediate pull over pdz.
    return pull_over_progress == pb::PullOverProgress::kNotTriggered ||
           !has_found_immediate_pullover_pdz;
  }

  return false;
}

std::vector<hdmap::StopCellEntity> QueryStopCellEntitiesByPoint(
    const pnc_map::PncMapService& pnc_map_service,
    const math::geometry::Point2d& ego_position) {
  hdmap::SearchPoint query_point;
  auto position = query_point.mutable_position();
  position->set_x(ego_position.x());
  position->set_y(ego_position.y());
  hdmap::StopCellQueryInfo stop_cell_query_info;
  const std::vector<hdmap::StopCellResult> stop_cell_result =
      pnc_map_service.hdmap()->QueryStopCellEntitiesByPoints(
          /*search_points=*/{query_point}, stop_cell_query_info);
  return stop_cell_result.empty()
             ? std::vector<hdmap::StopCellEntity>{}
             : std::vector<hdmap::StopCellEntity>{
                   stop_cell_result.front().stop_cells().begin(),
                   stop_cell_result.front().stop_cells().end()};
}

// Check if ego is almost stopped in a stop cell.
bool IsEgoAlmostStoppedInStopCell(const pnc_map::PncMapService& pnc_map_service,
                                  const math::geometry::Point2d& ego_position,
                                  double ego_speed) {
  if (ego_speed > kEgoSlowMoveSpeedInMps) {
    return false;
  }

  const std::vector<hdmap::StopCellEntity> stop_cells =
      QueryStopCellEntitiesByPoint(pnc_map_service, ego_position);
  return !stop_cells.empty() &&
         std::any_of(
             stop_cells.begin(), stop_cells.end(),
             [&ego_position](const auto& stop_cell) {
               return math::geometry::Within(
                   ego_position,
                   math::geometry::Convert<math::geometry::PolygonWithCache2d>(
                       stop_cell.border()));
             });
}

bool IsEgoInExitZoneRange(const RobotState& robot_state,
                          const PickupDropoffZoneInfo& pdz_info) {
  if (!pdz_info.extended_nominal_path.has_value()) {
    return false;
  }

  // Check if ego is stopping at the range of exit zone.
  const double ego_fb_arc_length =
      pdz_info.extended_nominal_path
          ->GetProximity(
              robot_state.current_state_snapshot().front_bumper_position(),
              math::pb::UseExtensionFlag::kAllow)
          .arc_length;
  const math::Range1d ego_range(ego_fb_arc_length - robot_state.GetLength(),
                                ego_fb_arc_length);
  return std::any_of(
      pdz_info.exit_zone_ranges.begin(), pdz_info.exit_zone_ranges.end(),
      [&ego_range](const math::Range1d& exit_zone_range) {
        return math::AreRangesOverlapping(ego_range, exit_zone_range);
      });
}

bool IsExistingPDZStillValidAfterRequestChange(
    const PickupDropoffZoneInfo& pdz_info,
    const std::vector<hdmap::StopCellEntity::Type>&
        forbidden_stop_types_for_mrc,
    mrc::pb::MrmType current_cycle_mrm_type,
    mrc::pb::MrmType last_cycle_mrm_type,
    pb::PullOverProgress pull_over_progress) {
  // Return true if pull over progress is kLatched or kCompleted.
  if (pull_over_progress == pb::PullOverProgress::kLatched ||
      pull_over_progress == pb::PullOverProgress::kCompleted) {
    return true;
  }

  // When mrm pullright is triggered and planner has found a pdz, then if mrm
  // request jumps to mrm harbor, we need to check whether the pdz meets the
  // requirements of harbor.
  if (last_cycle_mrm_type != mrc::pb::MrmType::MRM_PULLRIGHT ||
      current_cycle_mrm_type != mrc::pb::MrmType::MRM_HARBOR) {
    return true;
  }

  return IsPDZQualifiedForMRC(pdz_info, forbidden_stop_types_for_mrc);
}

bool IsPDZValidInPullOver(const PickupDropoffZoneInfo& pdz_info,
                          const pnc_map::Lane& current_lane,
                          const RobotState& robot_state,
                          const std::vector<hdmap::StopCellEntity::Type>&
                              forbidden_stop_types_for_mrc,
                          mrc::pb::MrmType current_cycle_mrm_type,
                          mrc::pb::MrmType last_cycle_mrm_type,
                          pb::PullOverProgress pull_over_progress) {
  if (!IsExistingPDZStillValidAfterRequestChange(
          pdz_info, forbidden_stop_types_for_mrc, current_cycle_mrm_type,
          last_cycle_mrm_type, pull_over_progress)) {
    return false;
  }

  // Don't update the immediate pull over pdz when pull over has started.
  if (pull_over_progress != pb::PullOverProgress::kNotTriggered) {
    return true;
  }

  const double projected_current_pose_arclength =
      pdz_info.nominal_path
          .GetProximity(
              math::geometry::Point2d(robot_state.current_state_snapshot().x(),
                                      robot_state.current_state_snapshot().y()),
              math::pb::UseExtensionFlag::kAllow)
          .arc_length;

  // When ego is in rightmost vehicle lane, We only update the immediate pull
  // over pdz when the ego crosses the pdz far away.
  const double buffer_distance = current_lane.IsRightmostPullOverLane()
                                     ? kBufferDistanceToUpdatePDZInMeter
                                     : 0.0;
  return projected_current_pose_arclength <=
         pdz_info.nominal_path.GetTotalArcLength() + buffer_distance;
}

// Reject pdz that is not in the regional map, or the lane sequence is not
// feasible when the current lane and pdz are not on the same road:
bool IsPDZReachableInRouting(
    const std::vector<const pnc_map::Section*>& regional_path_sections,
    const pb::LaneSequenceCandidate& last_selected_lane_sequence_candidate,
    const pnc_map::Lane& current_lane, const PickupDropoffZoneInfo& pdz_info,
    int64_t current_timestamp, int64_t proposal_request_timestamp,
    bool is_using_redirecting_mode, pb::PullOverProgress pull_over_progress) {
  if (pull_over_progress != pb::PullOverProgress::kNotTriggered) {
    return true;
  }

  // If the pdz is generated by sampling in lane sequence, it should be checked
  // in every cycle; if the pdz is generated by redirecting mode, it should be
  // checked after a period of time after requesting proposal.
  if (is_using_redirecting_mode &&
      current_timestamp - proposal_request_timestamp <
          kMaxDurationForTryingProposalInMSec) {
    return true;
  }

  // Returns false when the last selected lane sequence candidate is not
  // feasible and the current lane and pdz are not on the same road.
  if (!last_selected_lane_sequence_candidate.is_feasible() &&
      current_lane.section()->road()->id() != pdz_info.road->id()) {
    return false;
  }

  DCHECK(!pdz_info.raw_stop_cells.empty());
  // Local lambda to gets first associate lane id of the input cell.
  auto GetsFirstAssociateLaneIdOfCell =
      [](const hdmap::StopCellEntity& input_stop_cell) {
        const auto& cell_lane_ids = input_stop_cell.associate_lane_ids();
        DCHECK(!cell_lane_ids.empty());
        return *(cell_lane_ids.begin());
      };

  // Return true if any cell section is located in regional map.
  for (const auto& stop_cell : pdz_info.raw_stop_cells) {
    const int64_t cell_first_associate_lane_id =
        GetsFirstAssociateLaneIdOfCell(stop_cell);
    if (std::any_of(
            regional_path_sections.begin(), regional_path_sections.end(),
            [&cell_first_associate_lane_id](const pnc_map::Section* section) {
              return section->IsInSection(cell_first_associate_lane_id);
            })) {
      return true;
    }
  }

  return false;
}

std::string GetStopCellTypeWhichEgoLocated(
    const std::optional<PickupDropoffZoneInfo>& pickup_dropoff_zone_info,
    const voy::Pose& current_pose) {
  if (!pickup_dropoff_zone_info.has_value()) {
    return "EMPTY_PDZ";
  }

  const auto& raw_stop_cells = pickup_dropoff_zone_info->raw_stop_cells;
  for (const auto& raw_stop_cell : raw_stop_cells) {
    if (math::geometry::Within(
            math::geometry::Point2d(current_pose.x(), current_pose.y()),
            math::geometry::Convert<math::geometry::PolygonWithCache2d>(
                raw_stop_cell.border()))) {
      return hdmap::StopCellEntity::Type_Name(raw_stop_cell.type());
    }
  }
  return "NOT_INSIDE_CELL";
}

std::pair</*request_immediate_right_lc=*/bool,
          /*request_restricted_left_lc=*/bool>
GenerateLaneChangeRequest(
    const RegionalMap& regional_map, const RobotState& robot_state,
    const std::vector<const pnc_map::Lane*>& last_lane_follow_lane_sequence,
    const pnc_map::Lane& current_lane, const stage::StageManager& stage_manager,
    const std::optional<PickupDropoffZoneInfo>& pdz_info,
    bool is_immediate_pull_over_triggered, bool is_using_redirecting_mode) {
  bool should_request_immediate_right_lane_change = false;
  bool should_request_restricted_left_lane_change = false;
  if (!is_immediate_pull_over_triggered) {
    return std::make_pair(should_request_immediate_right_lane_change,
                          should_request_restricted_left_lane_change);
  }

  // For redirecting mode, we only trigger immediate right lane change when
  // enter desired direction.
  if (is_using_redirecting_mode) {
    return std::make_pair(
        stage_manager.should_request_immediate_right_lane_change(),
        stage_manager.should_request_restricted_left_lane_change());
  }

  // If ego is about to reach a junction and has not found the PDZ yet, left
  // lane change is allowed to avoid ego being stuck at the end of lane
  // sequence.
  if (regional_map.regional_path_next_info.distance_to_next_junction <
          kThresholdDistanceToJunctionForLaneChangeRequestInMeter &&
      !pdz_info.has_value()) {
    return std::make_pair(should_request_immediate_right_lane_change,
                          should_request_restricted_left_lane_change);
  }

  should_request_restricted_left_lane_change = true;
  // If the lane before the junction in the last lane follow lane sequence,
  // whose right lane is on the route, don't request lane change right.
  if (pdz_info.has_value() && !IsRightLaneBeforeJunctionOnRoute(
                                  robot_state, last_lane_follow_lane_sequence,
                                  regional_map.lane_to_junction_info_map,
                                  current_lane, pdz_info.value())) {
    return std::make_pair(/*should_request_immediate_right_lane_change=*/false,
                          should_request_restricted_left_lane_change);
  }

  // If pdz is near the lane follow lane sequence, don't need request lane
  // change right.
  if (IsPDZNearLaneFollowLaneSequence(last_lane_follow_lane_sequence,
                                      pdz_info)) {
    return std::make_pair(/*should_request_immediate_right_lane_change=*/false,
                          should_request_restricted_left_lane_change);
  }

  // Don't request immediate right lane change if pull over can be triggered.
  should_request_immediate_right_lane_change =
      (robot_state.last_trajectory()
           .extra_planner_signals()
           .behavior_signal() != pb::ExtraPlannerSignals::PULL_OVER);
  return std::make_pair(should_request_immediate_right_lane_change,
                        should_request_restricted_left_lane_change);
}

routing::pb::ImmediatePullOverState ConvertMrcRequestToImmediatePullOverState(
    const std::shared_ptr<const mrc::pb::MrcRequest>& mrc_request) {
  // If mrm type in mrc request is not MRM_HARBOR and MRM_PULLRIGHT, set
  // immediate_pullover_state_ to kNotTriggered.
  if (mrc_request == nullptr ||
      (mrc_request->mrm_type() != mrc::pb::MrmType::MRM_HARBOR &&
       mrc_request->mrm_type() != mrc::pb::MrmType::MRM_PULLRIGHT)) {
    return routing::pb::ImmediatePullOverState::kNotTriggered;
  }

  // If mrm type is MRM_HARBOR or MRM_PULLRIGHT, set immediate_pullover_state_
  // by mrc progress.
  // Note: Even if mrc progress is failed, the planner also needs to try
  // immediate pull over, until mrc is requested to degrade.
  if (mrc_request->mrm_progress() == mrc::pb::MrcProgress::FINISHED) {
    return routing::pb::ImmediatePullOverState::kSuccess;
  }

  if (mrc_request->mrm_progress() == mrc::pb::MrcProgress::ONGOING ||
      mrc_request->mrm_progress() == mrc::pb::MrcProgress::FAILED) {
    return routing::pb::ImmediatePullOverState::kTriggered;
  }
  return routing::pb::ImmediatePullOverState::kNotTriggered;
}

bool IsPDZSuitableForPullOver(
    const PickupDropoffZoneInfo& pdz_info,
    const std::vector<const pnc_map::Lane*>& last_lane_follow_lane_sequence,
    const RobotState& robot_state,
    const std::vector<ConstructionZone>& construction_zones,
    const std::vector<hdmap::StopCellEntity::Type>&
        forbidden_stop_types_for_mrc,
    bool is_triggered_by_mrc) {
  // Filter pdz which is not suitable for mrc when the immediate pull over is
  // triggered by mrc.
  if (is_triggered_by_mrc &&
      !IsPDZQualifiedForMRC(pdz_info, forbidden_stop_types_for_mrc)) {
    return false;
  }

  // Avoid pdz generation on the construction zone.
  if (IsPDZIntersectsWithConstructionZone(pdz_info, construction_zones)) {
    return false;
  }

  // Avoid the left boundary near a median strip.
  if (IsPDZLeftBoundaryNearMedianStrip(pdz_info)) {
    return false;
  }

  // Filter the pdz that are close to ego.
  return IsLongitudinalDistanceBetweenEgoAndPDZSufficient(
      last_lane_follow_lane_sequence, robot_state, pdz_info);
}

}  // namespace planner
