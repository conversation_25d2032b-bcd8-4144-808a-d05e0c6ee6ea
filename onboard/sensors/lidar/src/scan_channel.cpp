#include "lidar/scan_channel.h"

#include <algorithm>
#include <cstddef>
#include <vector>

#include <string.h>

namespace lidar {

////////////////////////////////////////////////////////////////

PointCloudWrapper::PointCloudWrapper() : accessor_func_(nullptr) {}

PointCloudWrapper::PointCloudWrapper(
    pcl::PointCloud<lidar::PointXYZIR>::Ptr point_cloud)
    : point_cloud_(point_cloud) {
  accessor_func_ = &PointCloudWrapper::PclAccessor;
}

PointCloudWrapper::PointCloudWrapper(
    std::shared_ptr<details::PointCloudExtend> extend)
    : extend_(extend) {
  accessor_func_ = &PointCloudWrapper::ExtAccessor;
}

PointCloudWrapper::PointCloudWrapper(const PointCloudWrapper& r) {
  accessor_func_ = r.extend_ ? (&PointCloudWrapper::ExtAccessor)
                             : (&PointCloudWrapper::PclAccessor);
  point_cloud_ = r.point_cloud_;
  extend_ = r.extend_;
}

PointCloudWrapper::PointCloudWrapper(PointCloudWrapper&& r) {
  r.accessor_func_ = nullptr;
  accessor_func_ = r.extend_ ? (&PointCloudWrapper::ExtAccessor)
                             : (&PointCloudWrapper::PclAccessor);
  point_cloud_ = std::move(r.point_cloud_);
  extend_ = std::move(r.extend_);
}

PointCloudWrapper& PointCloudWrapper::operator=(const PointCloudWrapper& r) {
  this->accessor_func_ = r.extend_ ? (&PointCloudWrapper::ExtAccessor)
                                   : (&PointCloudWrapper::PclAccessor);
  this->point_cloud_ = r.point_cloud_;
  this->extend_ = r.extend_;
  return *this;
}

PointCloudWrapper& PointCloudWrapper::operator=(PointCloudWrapper&& r) {
  r.accessor_func_ = nullptr;
  this->accessor_func_ = r.extend_ ? (&PointCloudWrapper::ExtAccessor)
                                   : (&PointCloudWrapper::PclAccessor);
  this->point_cloud_ = std::move(r.point_cloud_);
  this->extend_ = std::move(r.extend_);
  return *this;
}

pcl::PointCloud<lidar::PointXYZIR>::Ptr PointCloudWrapper::GetPointCloud(
    bool disable_check_coping_warning_log) const {
  if (extend_) {
    if (disable_check_coping_warning_log == true) {
      LOG(WARNING) << "triggered extend PointCloudWrapper memcpy";
    }
    auto ptr = const_cast<PointCloudWrapper*>(this);
    return ptr->extend_->GetCopiedPointCloud();
  }
  return point_cloud_;
}

pcl::PointCloud<lidar::PointXYZIR>::Ptr& PointCloudWrapper::GetPointCloud() {
  if (extend_) {
    LOG(FATAL) << "extend PointCloudWrapper is read only";
  }
  // return point_cloud_ to aviod compiling error
  return point_cloud_;
}

const lidar::PointXYZIR* PointCloudWrapper::Data() const {
  if (extend_) {
    return extend_->GetPtsRaw();
  }
  return point_cloud_ ? point_cloud_->points.data() : nullptr;
}

const lidar::PointXYZIR& PointCloudWrapper::operator[](size_t idx) const {
  return (this->*accessor_func_)(static_cast<size_t>(idx));
}

size_t PointCloudWrapper::Size() const {
  if (extend_) {
    return extend_->GetPtsNumber();
  }
  return point_cloud_ ? point_cloud_->size() : 0;
}

bool PointCloudWrapper::IsDense() const {
  if (extend_) {
    return extend_->is_dense_;
  }
  return point_cloud_ ? point_cloud_->is_dense : true;
}

bool PointCloudWrapper::IsContinuous() const {
  return extend_ ? extend_->is_continuous_ : true;
}

void PointCloudWrapper::Reset() {
  point_cloud_.reset();
  extend_.reset();
}

const lidar::PointXYZIR& PointCloudWrapper::PclAccessor(size_t i) const {
  return point_cloud_->points[i];
}

const lidar::PointXYZIR& PointCloudWrapper::ExtAccessor(size_t i) const {
  return extend_->At(i);
}

////////////////////////////////////////////////////////////////

PointCloudMessage::PointCloudMessage()
    : timestamp(0),
      start_timestamp(std::numeric_limits<int64_t>::max()),
      pose(),
      lidar_type(voy::Sensor::UNKNOWN),
      slice_id(0),
      scan_id(0),
      num_slices(0),
      is_complete_scan(false),
      point_cloud_strongest_wrapper_(),
      point_cloud_last_wrapper_(),
      point_cloud_diff_wrapper_() {}

void PointCloudMessage::Reset() {
  // public variables
  timestamp = 0;
  start_timestamp = std::numeric_limits<int64_t>::max();
  pose = voy::Pose();
  lidar_type = voy::Sensor::UNKNOWN;
  slice_id = 0;
  scan_id = 0;
  num_slices = 0;
  is_complete_scan = false;

  // private variables
  point_cloud_strongest_wrapper_.Reset();
  point_cloud_last_wrapper_.Reset();
  point_cloud_diff_wrapper_.Reset();
}

pcl::PointCloud<lidar::PointXYZIR>::Ptr
PointCloudMessage::GetPointCloudStrongest(
    bool disable_check_coping_warning_log) const {
  return point_cloud_strongest_wrapper_.GetPointCloud(
      disable_check_coping_warning_log);
}

pcl::PointCloud<lidar::PointXYZIR>::Ptr PointCloudMessage::GetPointCloudLast(
    bool disable_check_coping_warning_log) const {
  return point_cloud_last_wrapper_.GetPointCloud(
      disable_check_coping_warning_log);
}

pcl::PointCloud<lidar::PointXYZIR>::Ptr PointCloudMessage::GetPointCloudDiff(
    bool disable_check_coping_warning_log) const {
  return point_cloud_diff_wrapper_.GetPointCloud(
      disable_check_coping_warning_log);
}

pcl::PointCloud<lidar::PointXYZIR>::Ptr&
PointCloudMessage::GetPointCloudStrongest() {
  return point_cloud_strongest_wrapper_.GetPointCloud();
}

pcl::PointCloud<lidar::PointXYZIR>::Ptr&
PointCloudMessage::GetPointCloudLast() {
  return point_cloud_last_wrapper_.GetPointCloud();
}

pcl::PointCloud<lidar::PointXYZIR>::Ptr&
PointCloudMessage::GetPointCloudDiff() {
  return point_cloud_diff_wrapper_.GetPointCloud();
}

const PointCloudWrapper PointCloudMessage::GetStrongestPclWrapper() const {
  return point_cloud_strongest_wrapper_;
}

const PointCloudWrapper PointCloudMessage::GetLastPclWrapper() const {
  return point_cloud_last_wrapper_;
}

const PointCloudWrapper PointCloudMessage::GetDiffPclWrapper() const {
  return point_cloud_diff_wrapper_;
}

void PointCloudMessage::ResetPointCloud(const PointCloudWrapper& strongest,
                                        const PointCloudWrapper& last,
                                        const PointCloudWrapper& diff) {
  point_cloud_strongest_wrapper_ = strongest;
  point_cloud_last_wrapper_ = last;
  point_cloud_diff_wrapper_ = diff;
}

void PointCloudMessage::ResetPointCloud(
    pcl::PointCloud<lidar::PointXYZIR>::Ptr strongest,
    pcl::PointCloud<lidar::PointXYZIR>::Ptr last,
    pcl::PointCloud<lidar::PointXYZIR>::Ptr diff) {
  point_cloud_strongest_wrapper_ = PointCloudWrapper(strongest);
  point_cloud_last_wrapper_ = PointCloudWrapper(last);
  point_cloud_diff_wrapper_ = PointCloudWrapper(diff);
}
////////////////////////////////////////////////////////////////

void ScanMessage::Reserve(size_t n) { point_clouds_.reserve(n); }

void ScanMessage::Clear() { point_clouds_.clear(); }

PointCloudMessage ScanMessage::GetPointCloudMessage(
    voy::Sensor::SensorType lidar_type) const {
  TRACE_EVENT_SCOPE(lidar, ScanMessage_GetPointCloudMessage);
  for (unsigned int i = 0; i < point_clouds_.size(); ++i) {
    if (point_clouds_[i].lidar_type == lidar_type) {
      return point_clouds_[i];
    }
  }
  return PointCloudMessage();
}

const lidar::PointCloudWrapper ScanMessage::GetStrongestPclWrapper(
    const voy::Sensor::SensorType& lidar_type) const {
  TRACE_EVENT_SCOPE(lidar, ScanMessage_GetPointCloudStrongest);
  for (unsigned int i = 0; i < point_clouds_.size(); ++i) {
    if (point_clouds_[i].lidar_type == lidar_type) {
      return point_clouds_[i].GetStrongestPclWrapper();
    }
  }
  return lidar::PointCloudWrapper();
}

const lidar::PointCloudWrapper ScanMessage::GetLastPclWrapper(
    const voy::Sensor::SensorType& lidar_type) const {
  TRACE_EVENT_SCOPE(lidar, ScanMessage_GetPointCloudLast);
  for (unsigned int i = 0; i < point_clouds_.size(); ++i) {
    if (point_clouds_[i].lidar_type == lidar_type) {
      return point_clouds_[i].GetLastPclWrapper();
    }
  }
  return lidar::PointCloudWrapper();
}

const lidar::PointCloudWrapper ScanMessage::GetStrongestPclWrapper(
    const std::vector<voy::Sensor::SensorType>& lidar_types) const {
  if (lidar_types.size() == 1) {
    return GetStrongestPclWrapper(lidar_types[0]);
  }

  TRACE_EVENT_SCOPE(lidar, ScanMessage_GetPointCloudStrongest);
  std::vector<lidar::PointCloudWrapper> wrappers;
  wrappers.reserve(lidar_types.size());
  for (const auto& lidar_type : lidar_types) {
    for (const auto& pc : point_clouds_) {
      if (pc.lidar_type == lidar_type) {
        wrappers.emplace_back(pc.GetStrongestPclWrapper());
      }
    }
  }
  return lidar::PointCloudWrapper(
      std::make_shared<MultiPointCloudWrapperExtend>(std::move(wrappers)));
}

const lidar::PointCloudWrapper ScanMessage::GetLastPclWrapper(
    const std::vector<voy::Sensor::SensorType>& lidar_types) const {
  if (lidar_types.size() == 1) {
    return GetLastPclWrapper(lidar_types[0]);
  }

  TRACE_EVENT_SCOPE(lidar, ScanMessage_GetPointCloudLast);
  std::vector<lidar::PointCloudWrapper> wrappers;
  wrappers.reserve(lidar_types.size());
  for (const auto& lidar_type : lidar_types) {
    for (const auto& pc : point_clouds_) {
      if (pc.lidar_type == lidar_type) {
        wrappers.emplace_back(pc.GetLastPclWrapper());
      }
    }
  }
  return lidar::PointCloudWrapper(
      std::make_shared<MultiPointCloudWrapperExtend>(std::move(wrappers)));
}

pcl::PointCloud<lidar::PointXYZIR>::Ptr ScanMessage::GetAllPointCloudStrongest()
    const {
  TRACE_EVENT_SCOPE(lidar, ScanMessage_GetAllPointCloudStrongest);
  pcl::PointCloud<lidar::PointXYZIR>::Ptr point_cloud =
      boost::make_shared<pcl::PointCloud<lidar::PointXYZIR>>();
  size_t total_size = 0;
  const size_t num_point_clouds = point_clouds_.size();
  for (size_t i = 0; i < num_point_clouds; ++i) {
    auto wrapper = point_clouds_[i].GetStrongestPclWrapper();
    total_size += wrapper.Size();
  }
  point_cloud->resize(total_size);

  total_size = 0;
  for (size_t i = 0; i < num_point_clouds; ++i) {
    auto wrapper = point_clouds_[i].GetStrongestPclWrapper();
    if (wrapper.Size() == 0) {
      LOG(ERROR) << "No." << i << " strongest point cloud is null.";
      continue;
    }
    const auto size = wrapper.Size();
    memcpy(&(point_cloud->points[total_size]), wrapper.Data(),
           size * sizeof(lidar::PointXYZIR));
    total_size += size;
  }
  point_cloud->resize(total_size);

  return point_cloud;
}

const std::vector<lidar::PointCloudWrapper>
ScanMessage::GetAllStrongestPclWrappers() const {
  TRACE_EVENT_SCOPE(lidar, ScanMessage_GetAllPointCloudStrongest);
  std::vector<lidar::PointCloudWrapper> pcl_wrappers;
  pcl_wrappers.reserve(point_clouds_.size());
  for (unsigned int i = 0; i < point_clouds_.size(); ++i) {
    auto wrapper = point_clouds_[i].GetStrongestPclWrapper();
    if (wrapper.Size() == 0) {
      LOG(ERROR) << "No." << i << " strongest point cloud is null.";
      continue;
    }
    pcl_wrappers.push_back(wrapper);
  }
  return pcl_wrappers;
}

std::vector<pcl::PointCloud<lidar::PointXYZIR>::ConstPtr>
ScanMessage::GetAllUniquePointPtrs(size_t* total_point_num) const {
  TRACE_EVENT_SCOPE(lidar, ScanMessage_GetAllUniquePoints);
  std::vector<pcl::PointCloud<lidar::PointXYZIR>::ConstPtr> point_clouds;
  size_t all_points_num = 0;
  for (unsigned int i = 0; i < point_clouds_.size(); ++i) {
    if (point_clouds_[i].GetStrongestPclWrapper().Size() > 0) {
      point_clouds.push_back(point_clouds_[i].GetPointCloudStrongest());
      all_points_num += point_clouds.back()->size();
    } else {
      LOG(ERROR) << "No." << i << " strongest point cloud is null.";
    }
    if (point_clouds_[i].GetDiffPclWrapper().Size() > 0) {
      point_clouds.push_back(point_clouds_[i].GetPointCloudDiff());
      all_points_num += point_clouds.back()->size();
    }
  }
  if (total_point_num) {
    *total_point_num = all_points_num;
  }
  return point_clouds;
}

const std::vector<lidar::PointCloudWrapper>
ScanMessage::GetAllUniquePclWrappers(size_t* total_point_num) const {
  TRACE_EVENT_SCOPE(lidar, ScanMessage_GetAllUniquePoints);
  std::vector<lidar::PointCloudWrapper> point_clouds;
  point_clouds.reserve(point_clouds_.size());
  size_t all_points_num = 0;
  for (unsigned int i = 0; i < point_clouds_.size(); ++i) {
    auto strongest_wrapper = point_clouds_[i].GetStrongestPclWrapper();
    auto diff_wrapper = point_clouds_[i].GetDiffPclWrapper();
    if (strongest_wrapper.Size() > 0) {
      all_points_num += strongest_wrapper.Size();
      point_clouds.push_back(strongest_wrapper);
    } else {
      LOG(ERROR) << "No." << i << " strongest point cloud is null.";
    }
    if (diff_wrapper.Size() > 0) {
      all_points_num += diff_wrapper.Size();
      point_clouds.push_back(diff_wrapper);
    }
  }
  if (total_point_num) {
    *total_point_num = all_points_num;
  }
  return point_clouds;
}

pcl::PointCloud<lidar::PointXYZIR>::Ptr ScanMessage::GetAllUniquePoints()
    const {
  TRACE_EVENT_SCOPE(lidar, ScanMessage_GetAllUniquePoints);
  pcl::PointCloud<lidar::PointXYZIR>::Ptr point_cloud =
      boost::make_shared<pcl::PointCloud<lidar::PointXYZIR>>();
  for (unsigned int i = 0; i < point_clouds_.size(); ++i) {
    if (point_clouds_[i].GetPointCloudStrongest() != nullptr) {
      *(point_cloud.get()) +=
          *(point_clouds_[i].GetPointCloudStrongest().get());
    } else {
      LOG(ERROR) << "No." << i << " strongest point cloud is null.";
    }
    if (point_clouds_[i].GetPointCloudDiff() != nullptr) {
      *(point_cloud.get()) += *(point_clouds_[i].GetPointCloudDiff().get());
    }
  }
  return point_cloud;
}

std::vector<voy::Sensor::SensorType> ScanMessage::GetAllUniquePointsLidarType()
    const {
  TRACE_EVENT_SCOPE(lidar, ScanMessage_GetAllUniquePointsLidarType);
  int num_points = 0;
  std::vector<int> start_idx;
  std::vector<voy::Sensor::SensorType> lidar_types;
  for (unsigned int i = 0; i < point_clouds_.size(); ++i) {
    auto strongest_wrapper = point_clouds_[i].GetStrongestPclWrapper();
    if (strongest_wrapper.Size() > 0) {
      num_points += strongest_wrapper.Size();
      start_idx.push_back(num_points);
      lidar_types.push_back(point_clouds_[i].lidar_type);
    } else {
      LOG(ERROR) << "No." << i << " strongest point cloud is null.";
    }
    auto diff_wrapper = point_clouds_[i].GetDiffPclWrapper();
    if (diff_wrapper.Size() > 0) {
      num_points += diff_wrapper.Size();
      start_idx.push_back(num_points);
      lidar_types.push_back(point_clouds_[i].lidar_type);
    }
  }
  if (start_idx.empty()) return {};
  std::vector<voy::Sensor::SensorType> points_lidar_types(num_points);
  std::fill(points_lidar_types.begin(),
            points_lidar_types.begin() + start_idx[0], lidar_types[0]);
  for (size_t i = 1; i < start_idx.size(); ++i) {
    std::fill(points_lidar_types.begin() + start_idx[i - 1],
              points_lidar_types.begin() + start_idx[i], lidar_types[i]);
  }
  return points_lidar_types;
}

const std::vector<lidar::PointCloudWrapper>
ScanMessage::GetNonMergedStrongestPclWrappers() const {
  std::vector<lidar::PointCloudWrapper> non_merged_point_cloud;
  non_merged_point_cloud.reserve(point_clouds_.size());
  for (unsigned int i = 0; i < point_clouds_.size(); ++i) {
    auto strongest_wrapper = point_clouds_[i].GetStrongestPclWrapper();
    if (strongest_wrapper.Size() > 0) {
      non_merged_point_cloud.push_back(strongest_wrapper);
    }
  }
  return non_merged_point_cloud;
}

const std::vector<lidar::PointCloudWrapper>
ScanMessage::GetNonMergedDiffPclWrappers() const {
  std::vector<lidar::PointCloudWrapper> non_merged_point_cloud;
  non_merged_point_cloud.reserve(point_clouds_.size());
  for (unsigned int i = 0; i < point_clouds_.size(); ++i) {
    auto diff_wrapper = point_clouds_[i].GetDiffPclWrapper();
    if (diff_wrapper.Size() > 0) {
      non_merged_point_cloud.push_back(diff_wrapper);
    }
  }
  return non_merged_point_cloud;
}

const voy::Pose& ScanMessage::GetMainLidarPose() const {
  for (unsigned int i = 0; i < point_clouds_.size(); ++i) {
    // NOTE(tongmuchenxuan): We may have LIDAR_128 as main lidar in Gen3.
    // |LIDAR_GEN4_MAIN_1~4| are main lidars for Gen4, which are supposed to
    // have a same pose.
    if (point_clouds_[i].lidar_type == voy::Sensor::LIDAR_64 ||
        point_clouds_[i].lidar_type == voy::Sensor::LIDAR_GEN4_MAIN_1) {
      return point_clouds_[i].pose;
    }
  }
  DCHECK(false) << "No LIDAR_64 and LIDAR_GEN4_MAIN_1 in scan message";
  return voy::Pose::default_instance();
}

int64_t ScanMessage::GetMainLidarTimestamp() const {
  for (unsigned int i = 0; i < point_clouds_.size(); ++i) {
    // NOTE(tongmuchenxuan): We may have LIDAR_128 as main lidar in Gen3.
    // |LIDAR_GEN4_MAIN_1~4| are main lidars for Gen4, which are supposed to
    // have a same timestamp.
    if (point_clouds_[i].lidar_type == voy::Sensor::LIDAR_64 ||
        point_clouds_[i].lidar_type == voy::Sensor::LIDAR_GEN4_MAIN_1 ||
        point_clouds_[i].lidar_type == voy::Sensor::LIDAR_GEN4_MAIN_2 ||
        point_clouds_[i].lidar_type == voy::Sensor::LIDAR_GEN4_MAIN_3 ||
        point_clouds_[i].lidar_type == voy::Sensor::LIDAR_GEN4_MAIN_4) {
      return point_clouds_[i].timestamp;
    }
  }
  DCHECK(false) << "No LIDAR_64 and LIDAR_GEN4_MAIN_1 in scan message";
  return -1;
}

int64_t GetMainLidarTimestamp(const voy::ScanMessage& msg) {
  const size_t point_clouds_size = msg.point_clouds().size();
  for (size_t i = 0; i < point_clouds_size; ++i) {
    // NOTE(tongmuchenxuan): We may have LIDAR_128 as main lidar in Gen3.
    // |LIDAR_GEN4_MAIN_1~4| are main lidars for Gen4, which are supposed to
    // have a same timestamp.
    if (msg.point_clouds(i).lidar_type() == voy::Sensor::LIDAR_64 ||
        msg.point_clouds(i).lidar_type() == voy::Sensor::LIDAR_GEN4_MAIN_1 ||
        msg.point_clouds(i).lidar_type() == voy::Sensor::LIDAR_GEN4_MAIN_2 ||
        msg.point_clouds(i).lidar_type() == voy::Sensor::LIDAR_GEN4_MAIN_3 ||
        msg.point_clouds(i).lidar_type() == voy::Sensor::LIDAR_GEN4_MAIN_4) {
      return msg.point_clouds(i).end_timestamp();
    }
  }
  DCHECK(false) << "No LIDAR_64 and LIDAR_GEN4_MAIN_1 in scan message";
  return -1;
}

unsigned int ScanMessage::NumberOfStrongestPoints() const {
  size_t count = 0;
  for (unsigned int i = 0; i < point_clouds_.size(); ++i) {
    count += point_clouds_[i].GetStrongestPclWrapper().Size();
  }
  return static_cast<unsigned int>(count);
}

bool ScanMessage::IsEmpty() const {
  return point_clouds_.empty() || NumberOfStrongestPoints() == 0;
}

const PointCloudMessage& ScanMessage::PointCloud(int i) const {
  return point_clouds_[i];
}

//////////////////////////// TransformPointCloud ////////////////////////////

namespace {
/** A helper struct to apply an SE3 transform to a 3D point.
 * Supports single and double precision transform matrices. */
template <typename PointT, typename Scalar>
struct Transformer {
  Scalar tf[4][4];
  /** Construct a transformer object.
   * The transform matrix is captured by const reference.
   * Make sure that it does not go out of scope before this object does. */
  explicit Transformer(const Scalar transform[4][4]) {
    memcpy(&(tf[0][0]), &(transform[0][0]), 16 * sizeof(Scalar));
  }

  /** Apply SE3 transform.
   * \param[in] src input 3D point (pointer to 3 floats)
   * \param[out] tgt output 3D point (pointer to 3 floats)
   * The fourth element w will be 1. */
  void se3(const PointT& src, PointT* tgt) const {
    const Scalar p[3] = {src.x, src.y, src.z};  // need this when src == tgt
    tgt->x = static_cast<float>(tf[0][0] * p[0] + tf[1][0] * p[1] +
                                tf[2][0] * p[2] + tf[3][0]);
    tgt->y = static_cast<float>(tf[0][1] * p[0] + tf[1][1] * p[1] +
                                tf[2][1] * p[2] + tf[3][1]);
    tgt->z = static_cast<float>(tf[0][2] * p[0] + tf[1][2] * p[1] +
                                tf[2][2] * p[2] + tf[3][2]);
  }
};

#if defined(__SSE2__)
/** Optimized version for single-precision transforms using SSE2 intrinsics. */
template <typename PointT>
struct Transformer<PointT, float> {
  /// Columns of the transform matrix stored in XMM registers.
  __m128 c[4];

  explicit Transformer(const float transform[4][4]) {
    for (std::size_t i = 0; i < 4; ++i) {
      c[i] = _mm_load_ps(&(transform[i][0]));
    }
  }

  void se3(const PointT& src, PointT* tgt) const {
    __m128 p0 = _mm_mul_ps(_mm_set_ps1(src.x), c[0]);
    __m128 p1 = _mm_mul_ps(_mm_set_ps1(src.y), c[1]);
    __m128 p2 = _mm_mul_ps(_mm_set_ps1(src.z), c[2]);
    float res[4] __attribute__((aligned(32)));
    *reinterpret_cast<__m128*>(res) =
        _mm_add_ps(p0, _mm_add_ps(p1, _mm_add_ps(p2, c[3])));
    tgt->x = res[0];
    tgt->y = res[1];
    tgt->z = res[2];
  }
};

/** Optimized version for double-precision transform using SSE2 intrinsics. */
template <typename PointT>
struct Transformer<PointT, double> {
  /// Columns of the transform matrix stored in XMM registers.
#if !defined(__AVX__)
  __m128d c[4][2];
#else   // !defined(__AVX__)
  __m256d c[4];
#endif  // !defined(__AVX__)

  explicit Transformer(const double transform[4][4]) {
    for (std::size_t i = 0; i < 4; ++i) {
#if !defined(__AVX__)
      c[i][0] = _mm_load_pd(&(transform[i][0]));
      c[i][1] = _mm_load_pd(&(transform[i][2]));
#else   // !defined(__AVX__)
      c[i] = _mm256_load_pd(&(transform[i][0]));
#endif  // !defined(__AVX__)
    }
  }

  void se3(const PointT& src, PointT* tgt) const {
    float res[4] __attribute__((aligned(32)));
#if !defined(__AVX__)
    __m128d vv = _mm_cvtps_pd(_mm_set_ps1((src.x)));
    __m128d p0 = _mm_add_pd(_mm_mul_pd(vv, c[0][0]), c[3][0]);
    __m128d p1 = _mm_add_pd(_mm_mul_pd(vv, c[0][1]), c[3][1]);
    vv = _mm_cvtps_pd(_mm_set_ps1((src.y)));
    p0 = _mm_add_pd(_mm_mul_pd(vv, c[1][0]), p0);
    p1 = _mm_add_pd(_mm_mul_pd(vv, c[1][1]), p1);
    vv = _mm_cvtps_pd(_mm_set_ps1((src.z)));
    p0 = _mm_add_pd(_mm_mul_pd(vv, c[2][0]), p0);
    p1 = _mm_add_pd(_mm_mul_pd(vv, c[2][1]), p1);
    *reinterpret_cast<__m128*>(res) =
        _mm_movelh_ps(_mm_cvtpd_ps(p0), _mm_cvtpd_ps(p1));
#else   // !defined(__AVX__)
    __m256d p0 = _mm256_mul_pd(_mm256_cvtps_pd(_mm_set_ps1(src.x)), c[0]);
    __m256d p1 = _mm256_mul_pd(_mm256_cvtps_pd(_mm_set_ps1(src.y)), c[1]);
    __m256d p2 = _mm256_mul_pd(_mm256_cvtps_pd(_mm_set_ps1(src.z)), c[2]);
    *reinterpret_cast<__m128*>(res) = _mm256_cvtpd_ps(
        _mm256_add_pd(p0, _mm256_add_pd(p1, _mm256_add_pd(p2, c[3]))));
#endif  // !defined(__AVX__)
    tgt->x = res[0];
    tgt->y = res[1];
    tgt->z = res[2];
  }
};
#endif  // defined(__SSE2__)
}  // namespace

template <typename PointTp, typename Scalar>
void TransformPointCloudTp(
    const std::vector<lidar::PointCloudWrapper>& cloud_ins,
    pcl::PointCloud<PointTp>& cloud_out,
    const Eigen::Matrix<Scalar, 4, 4>& transform,
    std::function<bool(const PointTp&)> filter) {
  const Scalar trans[4][4] __attribute__((aligned(16))) = {
      {transform(0, 0), transform(1, 0), transform(2, 0), transform(3, 0)},
      {transform(0, 1), transform(1, 1), transform(2, 1), transform(3, 1)},
      {transform(0, 2), transform(1, 2), transform(2, 2), transform(3, 2)},
      {transform(0, 3), transform(1, 3), transform(2, 3), transform(3, 3)}};

  Transformer<PointTp, Scalar> tf(trans);

  size_t total_size = 0;
  for (const auto& wrapper : cloud_ins) {
    total_size += wrapper.Size();
  }
  cloud_out.resize(total_size);

  size_t output_size = 0;
  for (const auto& wrapper : cloud_ins) {
    const size_t pt_size = wrapper.Size();
    const bool is_dense = wrapper.IsDense();
    if (is_dense) {
      for (size_t i = 0; i < pt_size; ++i) {
        cloud_out[output_size] = wrapper[i];
        tf.se3(cloud_out[output_size], &cloud_out[output_size]);
        output_size += (filter && filter(cloud_out[output_size]) ? 0 : 1);
      }
    } else {
      for (size_t i = 0; i < pt_size; ++i) {
        cloud_out[output_size] = wrapper[i];
        if (!std::isfinite(cloud_out[output_size].x) ||
            !std::isfinite(cloud_out[output_size].y) ||
            !std::isfinite(cloud_out[output_size].z)) {
        } else {
          tf.se3(cloud_out[output_size], &cloud_out[output_size]);
        }
        output_size += (filter && filter(cloud_out[output_size]) ? 0 : 1);
      }
    }
  }
  cloud_out.resize(output_size);
}

void TransformPointCloud(const lidar::PointCloudWrapper& cloud_in,
                         pcl::PointCloud<lidar::PointXYZIR>& cloud_out,
                         const Eigen::Matrix<float, 4, 4>& transform,
                         std::function<bool(const lidar::PointXYZIR&)> filter) {
  TransformPointCloudTp({cloud_in}, cloud_out, transform, filter);
}

void TransformPointCloud(const lidar::PointCloudWrapper& cloud_in,
                         pcl::PointCloud<lidar::PointXYZIR>& cloud_out,
                         const Eigen::Matrix<double, 4, 4>& transform,
                         std::function<bool(const lidar::PointXYZIR&)> filter) {
  TransformPointCloudTp({cloud_in}, cloud_out, transform, filter);
}

void TransformPointCloud(const std::vector<lidar::PointCloudWrapper>& cloud_ins,
                         pcl::PointCloud<lidar::PointXYZIR>& cloud_out,
                         const Eigen::Matrix<float, 4, 4>& transform,
                         std::function<bool(const lidar::PointXYZIR&)> filter) {
  TransformPointCloudTp(cloud_ins, cloud_out, transform, filter);
}

void TransformPointCloud(const std::vector<lidar::PointCloudWrapper>& cloud_ins,
                         pcl::PointCloud<lidar::PointXYZIR>& cloud_out,
                         const Eigen::Matrix<double, 4, 4>& transform,
                         std::function<bool(const lidar::PointXYZIR&)> filter) {
  TransformPointCloudTp(cloud_ins, cloud_out, transform, filter);
}

}  // namespace lidar
