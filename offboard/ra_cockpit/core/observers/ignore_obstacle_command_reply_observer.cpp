#include "ignore_obstacle_command_reply_observer.h"

#include <utility>

#include "av_comm/topics.h"
#include "glog/logging.h"

namespace remote_assist {

IgnoreObstacleCommandReplyObserver::IgnoreObstacleCommandReplyObserver(
    IgnoreObstacleCommandReplyCallback callback)
    : callback_(std::move(callback)) {}

bool IgnoreObstacleCommandReplyObserver::IsInterestedIn(
    const teleassist::pb::TeleassistRequest& message) {
  return (message.message_type() == "IgnoreObstacleCommandReply");
}

std::shared_ptr<google::protobuf::Message>
IgnoreObstacleCommandReplyObserver::OnMessage(
    const teleassist::pb::TeleassistRequest& message,
    DataChannel data_channel) {
  auto reply = std::make_shared<teleassist::pb::IgnoreObstacleCommandReply>();
  if (!reply->ParseFromString(message.message_data())) {
    LOG(ERROR)
        << "Received error IgnoreObstacleCommandReply message format, len = "
        << message.message_data().length();
    return nullptr;
  }

  if (callback_) {
    callback_(reply, data_channel);
  }
  return reply;
}

}  // namespace remote_assist
