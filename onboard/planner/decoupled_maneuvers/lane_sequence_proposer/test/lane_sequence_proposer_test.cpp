#include "planner/decoupled_maneuvers/lane_sequence_proposer/lane_sequence_proposer.h"

#include <memory>
#include <vector>

#include <gtest/gtest.h>

#include "geometry/model/polyline_curve.h"
#include "planner/behavior/util/lane_common/lane_sequence_result_definition.h"
#include "planner/selection/trajectory_meta.h"
#include "planner/speed/reasoning/test/reasoning_test_fixture.h"
#include "planner/utility/seed/planning_seed.h"
#include "planner/utility/seed/planning_seed_token.h"
#include "planner_protos/lane_blockage.pb.h"
#include "planner_protos/path_reasoning_seed.pb.h"
#include "pnc_map_service/map_elements/lane.h"
#include "prediction_protos/agent.pb.h"

namespace planner {

class LaneSequenceProposerTest : public testing::Test,
                                 public speed::ReasoningTestFixture,
                                 public PlanningSeedAccess {
 public:
  // Constructor
  LaneSequenceProposerTest() = default;

  // Initializes the planning segment sequence based on the lane sequence.
  void InitializeLaneFollowPlanningSegmentSequence(
      const std::vector<const pnc_map::Lane*>& lane_sequence,
      pb::PlanningSegmentSequence* planning_segment_sequence) {
    DCHECK_NE(planning_segment_sequence, nullptr);
    planning_segment_sequence->Clear();
    for (const pnc_map::Lane* lane : lane_sequence) {
      auto* lf_segment = planning_segment_sequence->add_planning_segments()
                             ->mutable_lane_follow_segment()
                             ->mutable_lf_segment();
      lf_segment->set_lane_id(lane->id());
      lf_segment->set_start_arclength(0.0);
      lf_segment->set_end_arclength(lane->center_line().GetTotalArcLength());
    }
  }

  void SetRightTurnStuckScenarioForXLaneNudge() {
    SetUpSceneMap(hdmap::test_util::SceneType::kRightStraightMerge);
    SetEgoPose(/*lane_id=*/9025, /*portion=*/0.8);
    agent_list_.Clear();

    // Create lane sequence.
    CreatePathWithLaneSequence(/*lane_ids=*/{9025, 13299, 10997});
    lane_sequence_result_.lane_sequence_type =
        planner::pb::LaneSequenceCandidate::LANE_FOLLOW;
    InitializeLaneFollowPlanningSegmentSequence(
        lane_sequence_result_.lane_sequence,
        &lane_sequence_result_.planning_segment_sequence);

    const auto& lane_sequence = lane_sequence_result_.lane_sequence;

    prediction::pb::Agent& agent_1 = AddAgent(
        /*object_type=*/voy::perception::ObjectType::VEHICLE, /*id=*/1,
        /*lane_id=*/DCHECK_NOTNULL(lane_sequence[0])->id(),
        /*portion=*/0.9, /*length=*/5.0, /*width=*/2.0, /*velocity=*/0.0);
    agent_1.mutable_tracked_object()->add_attributes(
        voy::perception::Attribute::PARKED_CAR);
    AddStationaryPredictedTrajectory(
        DCHECK_NOTNULL(lane_sequence[0])->id(), /*portion=*/0.9,
        /*likelihood=*/1.0, /*traj_id=*/1, agent_1);

    UpdateWorldModel();
  }

  std::shared_ptr<const speed::TrajectoryInfo> MakeTrajectoryInfoCopy() {
    return std::make_shared<const speed::TrajectoryInfo>(
        unextended_path(), path(), path_evaluation_result(), ego_intention(),
        lane_sequence_iterator_opt_.value(),
        planning_segment_sequence_opt_.value(),
        world_model().traffic_light_detection(), physical_boundaries(),
        *world_model().GetLatestJointPncMapService(), previous_iter_seed_,
        maneuver_type_, behavior_type_, pull_over_info_,
        pull_over_dest_meta_ptr(), cross_lane_info_manager_, lane_change_info_,
        lane_change_status_, /*xlane_nudge_meta=*/std::nullopt, motion_mode(),
        reverse_driving_info(), open_space_info(),
        plan_init_ra_arc_length_on_extended_path(),
        world_model_.value().robot_state(), is_pull_out_jump_out_,
        is_for_ra_unstuck_request_, is_lane_sequence_feasible_,
        lane_sequence_cost_factors_, is_waypoint_assist_invoked_,
        is_waypoint_backup_trajectory_used_, is_hard_boundary_relaxed_in_path_,
        planner::pb::TrajectoryType::NOMINAL_TRAJECTORY,
        planner::pb::PathReasoningSeed(),
        mutable_current_trajectory_info_seeds(), /*unique_path_homotopy_id=*/"",
        /*intention_plan_debug_in=*/nullptr);
  }

 protected:
  // Returns the decoupled maneuver seed.
  inline const pb::DecoupledManeuverSeed& seed() const {
    return *Seed::Access<token::DecoupledManeuverSeed>::GetMsg(
        SpeedCurrentFrame());
  }

  // Returns the mutable decoupled maneuver seed.
  inline pb::DecoupledManeuverSeed* mutable_seed() {
    return Seed::Access<token::DecoupledManeuverSeed>::MutableMsg(
               SpeedCurrentFrame())
        .get();
  }
};

TEST_F(LaneSequenceProposerTest, LatchProposalForXLaneNudgeTest) {
  SetRightTurnStuckScenarioForXLaneNudge();
  lane_selection::DecoupledLaneSequenceInfo lane_seq_info(
      planner::pb::ManeuverType::LANE_FOLLOW, lane_sequence_result(), seed());
  BehaviorRelevantObjectsInfo behavior_relevant_objects_info;
  lane_seq_info.Update(world_model(), behavior_relevant_objects_info);
  PathGenerationOption path_option;
  path_option.geometric_constraint.lane_sequence_info = &lane_seq_info;
  path_option.trajectory_type = pb::TrajectoryType::NOMINAL_TRAJECTORY;
  path_option.geometric_constraint.behavior_type = pb::BehaviorType::LANE_KEEP;
  path_option.geometric_constraint.lane_sequence_result =
      &lane_sequence_result();
  path_option.path_reasoning_result.intention_result.set_homotopy(
      pb::IntentionResult::XLANE_PASS_FROM_LEFT);
  pb::XLaneNudgeSeed xlane_nudge_seed;
  xlane_nudge_seed.set_side(pb::StuckRegion::kLeft);
  xlane_nudge_seed.set_overtake_mode(pb::StuckRegion::kNeighborLane);
  xlane_nudge_seed.mutable_target_stuck_region()
      ->mutable_longitudinal_range()
      ->set_start_pos(43.0);
  xlane_nudge_seed.mutable_target_stuck_region()
      ->mutable_longitudinal_range()
      ->set_end_pos(50.0);
  path_option.geometric_constraint.path_reasoning_seed
      .mutable_xlane_nudge_seed()
      ->Swap(&xlane_nudge_seed);

  const std::shared_ptr<const speed::TrajectoryInfo> trajectory_info =
      MakeTrajectoryInfoCopy();
  selection::TrajectoryMetaData selected_trajectory(
      path_option, /*speed_ix_in=*/0, selection::EgoIntentSignals(),
      /*trajectory_info=*/trajectory_info,
      std::make_shared<pb::Path>(pb::Path()),
      /*extended_path_in=*/
      std::make_shared<math::geometry::PolylineCurve2d>(
          math::geometry::PolylineCurve2d()),
      /*proximity_infos=*/
      std::make_shared<std::map<ObjectId, speed::pb::ObjectProximityInfo>>(
          std::map<ObjectId, speed::pb::ObjectProximityInfo>()),
      /*overlap gap=*/
      std::make_shared<
          std::map<ObjectId, std::vector<speed::pb::OverlapRegion>>>(
          std::map<ObjectId, std::vector<speed::pb::OverlapRegion>>()),
      pb::Trajectory(),
      /*speed_result=*/speed::SpeedResult(),
      std::make_shared<pb::SingleHomotopyPathSeed>(
          pb::SingleHomotopyPathSeed()),
      std::make_shared<speed::pb::SpeedSeed>(speed::pb::SpeedSeed()),
      std::make_shared<std::vector<pb::AssistRequest>>(
          std::vector<pb::AssistRequest>()),
      /*speed_discomfort_in=*/0.0,
      /*if_use_trajectory_guider_output_in_path_in=*/false,
      world_model().robot_state().car_model_with_shape().shape_measurement(),
      /*intention_plan_debug_in=*/nullptr);
  const lane_selection::LaneSequenceCandidates lane_sequence_candidates(
      /*waypoint_cost_map_in=*/nullptr);

  std::map<int64_t, LaneBlockage> lane_blockages;
  const LaneCongestionReasoner lane_congestion_reasoner;
  LaneBlockageDetectorSpeedData lane_blockage_speed_data(
      std::move(lane_blockages), lane_congestion_reasoner);
  LaneSequenceProposer proposer(
      *world_model().pnc_map_service(), world_model().regional_map(),
      *world_model().global_route_solution(), lane_sequence_candidates,
      /*current_lanes=*/{}, lane_blockage_speed_data);
  proposer.Propose(selected_trajectory,
                   /*waypoint_proposed_lane_sequence=*/nullptr,
                   /*redirecting_regional_path=*/std::nullopt,
                   world_model().robot_state().plan_init_state_snapshot(),
                   *world_model().GetLatestJointPncMapService(),
                   mutable_seed());

  const auto& proposal = seed().lane_sequence_proposal();
  EXPECT_TRUE(proposal.has_lane_seq_latch_signal());
  EXPECT_TRUE(proposal.lane_seq_latch_signal().is_exclusive());
  EXPECT_EQ(proposal.lane_seq_latch_signal().prefixes().size(), 1);
  EXPECT_TRUE(proposal.lane_seq_latch_signal()
                  .prefixes()[0]
                  .has_planning_segment_sequence());
}
}  // namespace planner
