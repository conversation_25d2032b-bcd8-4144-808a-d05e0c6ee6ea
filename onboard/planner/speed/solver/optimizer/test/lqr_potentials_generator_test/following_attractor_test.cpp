#include "planner/speed/solver/optimizer/lqr_potentials_generator.h"

#include <cmath>
#include <optional>

#include "planner/speed/constraint/constraint.h"
#include "planner/speed/constraint/constraint_creator.h"
#include "planner/speed/constraint/constraint_util.h"
#include "planner/speed/discomforts/discomfort_varying.h"
#include "planner/speed/solver/optimizer/lqr_potentials.h"
#include "planner/speed/solver/optimizer/potentials.h"
#include "planner/speed/solver/optimizer/speed_opt_input.h"
#include "planner/speed/solver/optimizer/test/speed_opt_test_fixture.h"
#include "planner/speed/timemap/time_mapper.h"

namespace planner {
namespace speed {
namespace {
// Add a constraint and verify if the creep attractor is correctly set based on
// the search profile correctly. When the Search Profile is already very high,
// the ref_x position of the creep attractor should be consistent with the
// Search Profile.
TEST(PotentialsGeneratorTest, CreepAttractor) {
  OptInputBuilder input_builder;
  input_builder
      .add_const_speed_constraint(/*t_start=*/0.0, /*x_start=*/5.0,
                                  /*v_const=*/5.0, /*num_states=*/80,
                                  /*decision=*/Decision::YIELD)
      .set_soft_yield()
      .set_creep();
  SpeedOptInput opt_input = input_builder.build();

  LqrPotentialsGenerator potentials_generator(opt_input, nullptr);
  const LqrPotentials& lqr_potentials = potentials_generator.lqr_potentials();

  const int num_steps = opt_input.opt_conf.steps;
  constexpr double kCreepAttractorGain = 500.0;
  constexpr double kCreepAttractorDx = 20.0;
  for (int state_ix = 1; state_ix < num_steps; state_ix++) {
    const std::optional<Attractor>& following_attractor =
        lqr_potentials.state_potentials[state_ix].following_attractor;
    DCHECK(following_attractor.has_value());
    const double ref_x = opt_input.profiles.search_profile.at(state_ix).x;
    const auto [x0, dx, normalization_factor_sqr, gain] =
        PrivateMemberAccessorForUT::GetAttractorMembers(
            following_attractor.value());
    EXPECT_EQ(x0, ref_x);
    EXPECT_EQ(dx, kCreepAttractorDx);
    EXPECT_EQ(gain, kCreepAttractorGain);
    EXPECT_EQ(normalization_factor_sqr, std::pow(1.0 / ref_x, 2.0));
  }
}

// Add a creep constraint and add a low speed search profile. Verify if the
// ref_x is larger than search profile.
TEST(PotentialsGeneratorTest, CreepAttractorWithLowSearchProfile) {
  OptInputBuilder input_builder;
  input_builder
      .add_const_speed_constraint(/*t_start=*/0.0, /*x_start=*/5.0,
                                  /*v_const=*/5.0, /*num_states=*/80,
                                  /*decision=*/Decision::YIELD)
      .set_soft_yield_with_min_speed_accel(/*yield_min_v=*/2.0,
                                           /*yield_min_a=*/-2.0)
      .set_creep();
  SpeedOptInput opt_input =
      input_builder
          .update_ref_profile_with_const_jerk(/*v_in=*/1.5, /*a_in=*/0.0,
                                              /*j_in=*/0.0, /*jerk=*/2.0)
          .update_search_profile_with_const_jerk(/*v_in=*/1.5, /*a_in=*/0.0,
                                                 /*j_in=*/0.0, /*jerk=*/0.0)
          .build();

  LqrPotentialsGenerator potentials_generator(opt_input, nullptr);
  const LqrPotentials& lqr_potentials = potentials_generator.lqr_potentials();

  const int num_steps = opt_input.opt_conf.steps;
  for (int state_ix = 1; state_ix < num_steps; state_ix++) {
    const std::optional<Attractor>& following_attractor =
        lqr_potentials.state_potentials[state_ix].following_attractor;
    DCHECK(following_attractor.has_value()) << state_ix;
    const double search_x = opt_input.profiles.search_profile.at(state_ix).x;
    const auto [x0, dx, normalization_factor_sqr, gain] =
        PrivateMemberAccessorForUT::GetAttractorMembers(
            following_attractor.value());
    EXPECT_GT(x0, search_x);
  }
}

// Verify that if there is no creep constraint, the creep attractor should be
// empty.
TEST(PotentialsGeneratorTest, CreepAttractorWithNoCreepConstraint) {
  OptInputBuilder input_builder;
  input_builder
      .add_const_speed_constraint(/*t_start=*/0.0, /*x_start=*/5.0,
                                  /*v_const=*/5.0, /*num_states=*/80,
                                  /*decision=*/Decision::YIELD)
      .set_soft_yield();
  SpeedOptInput opt_input = input_builder.build();
  VerifyPotentialIsEmpty(opt_input,
                         &LqrPotentials::StatePotentials::following_attractor);
}

// Verify that if we pass a creep constraint, the creep attractor should be
// empty.
TEST(PotentialsGeneratorTest, CreepAttractorWhenPassCreepConstraint) {
  OptInputBuilder input_builder;
  input_builder
      .add_const_speed_constraint(/*t_start=*/0.0, /*x_start=*/5.0,
                                  /*v_const=*/5.0, /*num_states=*/80,
                                  /*decision=*/Decision::PASS)
      .set_soft_yield()
      .set_creep();
  SpeedOptInput opt_input = input_builder.build();
  VerifyPotentialIsEmpty(opt_input,
                         &LqrPotentials::StatePotentials::following_attractor);
}

// Add two speed object, verify that if the constraint attractor is correctly
// set based on the closer speed object when enable fast acceleration.
TEST(PotentialsGeneratorTest, ConstraintAttractor) {
  GflagGuard gflag_guard;
  FLAGS_planning_enable_last_profile_attraction_in_speed_opt = true;
  FLAGS_planning_enable_x_upper_bound_attraction_in_fast_acceleration = false;

  OptInputBuilder input_builder;
  input_builder.add_const_speed_constraint(/*t_start=*/0.0, /*x_start=*/5.0,
                                           /*v_const=*/5.0, /*num_states=*/80,
                                           /*decision=*/Decision::YIELD);
  input_builder.add_const_speed_constraint(/*t_start=*/0.0, /*x_start=*/10.0,
                                           /*v_const=*/5.0, /*num_states=*/80,
                                           /*decision=*/Decision::YIELD);
  SpeedOptInput opt_input = input_builder.enable_fast_accel().build();

  LqrPotentialsGenerator potentials_generator(opt_input, nullptr);
  const LqrPotentials& lqr_potentials = potentials_generator.lqr_potentials();

  const Constraint& closest_speed_constraint = opt_input.constraints.at(0);
  const double discomfort = closest_speed_constraint.selected_discomfort;
  const ConstraintSettings& settings = closest_speed_constraint.settings;
  const double yield_extra_time = settings.yield_extra_time(discomfort);
  const double yield_extra_dist = settings.yield_extra_distance(discomfort);
  const double yield_stop_range = settings.yield_stop_range(discomfort);
  const double yield_offset =
      FLAGS_planning_enable_overlap_v2 ? 0.0 : opt_input.opt_conf.ra_to_fb;

  TimeMapper time_mapper(/*t_immutable=*/0.0, opt_input.opt_conf.steps,
                         opt_input.opt_conf.dt);

  for (const ConstraintState& state : closest_speed_constraint.states) {
    const double state_time = state.end_time + yield_extra_time;
    int state_ix = 0;
    if (!time_mapper.MaybeGetTimeIx(state_time, /*is_pass=*/false, &state_ix)) {
      continue;
    }
    const std::optional<Attractor>& following_attractor =
        lqr_potentials.state_potentials[state_ix].following_attractor;
    DCHECK(following_attractor.has_value());
    const double yield_min_range = GetMinRangeForConstraintState(
        settings, state, opt_input.opt_conf.min_range, discomfort,
        opt_input.opt_conf.min_range_max_required_lat_gap,
        /*is_pass=*/false);
    const double ref_x = state.start_x(discomfort) - yield_offset -
                         yield_min_range - yield_extra_dist - yield_stop_range;
    const auto [x0, dx, normalization_factor_sqr, gain] =
        PrivateMemberAccessorForUT::GetAttractorMembers(
            following_attractor.value());
    EXPECT_EQ(gain, opt_input.opt_conf.conf.potentials_params()
                        .following_attractor_params()
                        .gain());
    EXPECT_EQ(x0, ref_x);
    EXPECT_EQ(normalization_factor_sqr, std::pow(1.0 / ref_x, 2.0));
  }
}

// Add a constraint which is a non-speed object, verify that if the
// constraint attractor is correctly set when enable fast acceleration.
TEST(PotentialsGeneratorTest, ConstraintAttractorNonSpeedObject) {
  GflagGuard gflag_guard;
  FLAGS_planning_enable_last_profile_attraction_in_speed_opt = true;
  FLAGS_planning_enable_x_upper_bound_attraction_in_fast_acceleration = false;

  OptInputBuilder input_builder;
  input_builder.add_avoid_region(/*t_start=*/0.0, /*t_end=*/80.0,
                                 /*x_start=*/20.0, /*x_end=*/50.0,
                                 /*decision=*/Decision::YIELD);
  SpeedOptInput opt_input = input_builder.enable_fast_accel().build();

  LqrPotentialsGenerator potentials_generator(opt_input, nullptr);
  const LqrPotentials& lqr_potentials = potentials_generator.lqr_potentials();

  const Constraint& closest_speed_constraint = opt_input.constraints.at(0);
  const double discomfort = closest_speed_constraint.selected_discomfort;
  const ConstraintSettings& settings = closest_speed_constraint.settings;
  const double yield_extra_dist = settings.yield_extra_distance(discomfort);
  const double yield_stop_range = settings.yield_stop_range(discomfort);
  const double yield_offset =
      FLAGS_planning_enable_overlap_v2 ? 0.0 : opt_input.opt_conf.ra_to_fb;
  const double yield_min_range = GetMinRangeForConstraintState(
      settings, closest_speed_constraint.states[0],
      opt_input.opt_conf.min_range, discomfort,
      opt_input.opt_conf.min_range_max_required_lat_gap,
      /*is_pass=*/false);
  const double ref_x = closest_speed_constraint.states[0].start_x(discomfort) -
                       yield_offset - yield_min_range - yield_extra_dist -
                       yield_stop_range;

  for (int state_ix = 0; state_ix < opt_input.opt_conf.steps; state_ix++) {
    const auto following_attractor =
        lqr_potentials.state_potentials[state_ix].following_attractor;
    DCHECK(following_attractor.has_value()) << state_ix;
    const auto [x0, dx, normalization_factor_sqr, gain] =
        PrivateMemberAccessorForUT::GetAttractorMembers(
            following_attractor.value());
    EXPECT_EQ(gain, opt_input.opt_conf.conf.potentials_params()
                        .following_attractor_params()
                        .gain());
    EXPECT_EQ(x0, ref_x);
    EXPECT_EQ(normalization_factor_sqr, std::pow(1.0 / ref_x, 2.0));
  }
}

// Add speed object, verify that if the constraint attractor is empty when
// disable fast acceleration.
TEST(PotentialsGeneratorTest, ConstraintAttractorWithoutFastAccel) {
  GflagGuard gflag_guard;
  FLAGS_planning_enable_last_profile_attraction_in_speed_opt = true;
  FLAGS_planning_enable_x_upper_bound_attraction_in_fast_acceleration = false;

  OptInputBuilder input_builder;
  input_builder.add_const_speed_constraint(/*t_start=*/0.0, /*x_start=*/5.0,
                                           /*v_const=*/5.0, /*num_states=*/80,
                                           /*decision=*/Decision::YIELD);
  SpeedOptInput opt_input = input_builder.build();
  EXPECT_FALSE(opt_input.settings.enable_fast_acceleration);
  VerifyPotentialIsEmpty(opt_input,
                         &LqrPotentials::StatePotentials::following_attractor);
}

// Add a speed constraint which is too close, verify that if the constraint
// attractor is empty when enable fast acceleration.
TEST(PotentialsGeneratorTest, ConstraintAttractorWithTooCloseConstraint) {
  GflagGuard gflag_guard;
  FLAGS_planning_enable_last_profile_attraction_in_speed_opt = true;
  FLAGS_planning_enable_x_upper_bound_attraction_in_fast_acceleration = false;

  OptInputBuilder input_builder;
  input_builder.add_const_speed_constraint(/*t_start=*/0.0, /*x_start=*/1.0,
                                           /*v_const=*/0.0, /*num_states=*/80,
                                           /*decision=*/Decision::YIELD);
  SpeedOptInput opt_input = input_builder.enable_fast_accel().build();
  VerifyPotentialIsEmpty(opt_input,
                         &LqrPotentials::StatePotentials::following_attractor);
}

// Verify that the following attractor is correctly set after enabling the
// dynamic attractor for a speed object.
TEST(PotentialsGeneratorTest, FollowingAttractor) {
  OptInputBuilder input_builder;
  input_builder
      .add_const_speed_constraint(/*t_start=*/0.0, /*x_start=*/5.0,
                                  /*v_const=*/2.0, /*num_states=*/80,
                                  /*decision=*/Decision::YIELD)
      .set_enable_dynamic_attractor(true)
      .set_yield_headway(DiscomfortVarying(2.0));
  SpeedOptInput opt_input = input_builder.build();

  LqrPotentialsGenerator potentials_generator(opt_input, nullptr);
  const LqrPotentials& lqr_potentials = potentials_generator.lqr_potentials();

  const Constraint& constraint = opt_input.constraints.at(0);
  const double discomfort = constraint.selected_discomfort;
  const double yield_extra_time =
      constraint.settings.yield_extra_time(discomfort);
  const double yield_required_lat_gap =
      constraint.settings.yield_required_lat_gap(discomfort);
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
  // TODO(speed): align this to use min_range_max_req_lat_gap.
  const double barrier_req_lat_gap = opt_input.opt_conf.conf.potentials_params()
                                         .barrier_params()
                                         .max_required_lateral_gap();
#pragma GCC diagnostic pop
  const double yield_dist =
      FLAGS_planning_enable_overlap_v2
          ? constraint.settings.yield_extra_distance(discomfort)
          : opt_input.opt_conf.ra_to_fb +
                constraint.settings.yield_extra_distance(discomfort);
  TimeMapper time_mapper(/*t_immutable=*/0.0, opt_input.opt_conf.steps,
                         opt_input.opt_conf.dt);

  for (const auto& state : constraint.states) {
    if (state.abs_lat_gap > yield_required_lat_gap) {
      continue;
    }
    const double max_attractor_x =
        FLAGS_planning_enable_overlap_v2
            ? state.start_x(discomfort)
            : state.start_x(discomfort) - opt_input.opt_conf.ra_to_fb;
    const std::optional<int> state_ix =
        time_mapper.GetTimeIxWithinHorizon(state.end_time + yield_extra_time,
                                           /*floor_round=*/false);
    if (!state_ix.has_value()) {
      continue;
    }
    const std::optional<Attractor>& following_attractor =
        lqr_potentials.state_potentials[state_ix.value()].following_attractor;
    DCHECK(following_attractor.has_value());
    const auto [x0, dx, normalization_factor_sqr, gain] =
        PrivateMemberAccessorForUT::GetAttractorMembers(
            following_attractor.value());
    EXPECT_EQ(gain, opt_input.opt_conf.conf.potentials_params()
                        .following_attractor_params()
                        .gain());
    EXPECT_EQ(x0,
              state.start_x(discomfort) - yield_dist -
                  GetMinRangeForConstraintState(constraint.settings, state,
                                                opt_input.opt_conf.min_range,
                                                discomfort, barrier_req_lat_gap,
                                                /*is_pass=*/false));
    EXPECT_EQ(dx, opt_input.opt_conf.conf.potentials_params()
                      .following_attractor_params()
                      .dx());
    EXPECT_EQ(normalization_factor_sqr, std::pow(1.0 / max_attractor_x, 2.0));
  }
}

// Verify that the following attractor is empty when decision is pass.
TEST(PotentialsGeneratorTest, FollowingAttractorWithPassDecision) {
  OptInputBuilder input_builder;
  input_builder
      .add_const_speed_constraint(/*t_start=*/0.0, /*x_start=*/5.0,
                                  /*v_const=*/2.0, /*num_states=*/80,
                                  /*decision=*/Decision::PASS)
      .set_enable_dynamic_attractor(true)
      .set_yield_headway(DiscomfortVarying(2.0));
  SpeedOptInput opt_input = input_builder.build();
  VerifyPotentialIsEmpty(opt_input,
                         &LqrPotentials::StatePotentials::following_attractor);
}

// Verify that the following attractor is empty when disabling the dynamic
// attractor for a speed object.
TEST(PotentialsGeneratorTest, FollowingAttractorWithDisableDynamicAttractor) {
  OptInputBuilder input_builder;
  input_builder.add_const_speed_constraint(/*t_start=*/0.0, /*x_start=*/5.0,
                                           /*v_const=*/2.0, /*num_states=*/80,
                                           /*decision=*/Decision::YIELD);
  SpeedOptInput opt_input = input_builder.build();
  EXPECT_FALSE(opt_input.constraints.front().settings.enable_dynamic_attractor);
  VerifyPotentialIsEmpty(opt_input,
                         &LqrPotentials::StatePotentials::following_attractor);
}

// Verify that the following attractor is empty when enabling the dynamic
// attractor for a speed object but set its yield headway to -1.0. Due to the
// hard yield constraint not allowing the yield headway to be -1.0, we have set
// it as a soft yield.
TEST(PotentialsGeneratorTest, FollowingAttractorWithNegHeadway) {
  OptInputBuilder input_builder;
  input_builder
      .add_const_speed_constraint(/*t_start=*/0.0, /*x_start=*/5.0,
                                  /*v_const=*/2.0, /*num_states=*/80,
                                  /*decision=*/Decision::YIELD)
      .set_soft_yield()
      .set_enable_dynamic_attractor(true)
      .set_yield_headway(DiscomfortVarying(-1.0));
  SpeedOptInput opt_input = input_builder.build();
  VerifyPotentialIsEmpty(opt_input,
                         &LqrPotentials::StatePotentials::following_attractor);
}

}  // namespace
}  // namespace speed
}  // namespace planner
