#include "common_math_library/matrix.h"

#include <gtest/gtest.h>

TEST(CML_MatrixCreate, CML_MatrixCreateTest) {
  CML_MatrixCreate(matrix_test, 4, 3);
  ASSERT_EQ(matrix_test->rows, 4);
  ASSERT_EQ(matrix_test->cols, 3);
  ASSERT_EQ(matrix_test->max_size, 4 * 3);
  ASSERT_EQ(matrix_test->data_ptr, matrix_data_matrix_test);
}

TEST(CML_MatrixGetElement, CML_MatrixGetElementTest) {
  CML_MatrixCreate(matrix_test, 4, 3);
  CML_MatrixGetElement(matrix_test, 1, 2) = 1.0F;
  ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_test, 1, 2), 1.0F);
}

TEST(CML_MatrixSetElement, CML_MatrixSetElementTest) {
  CML_MatrixCreate(matrix_test, 4, 3);
  CML_MatrixSetElement(matrix_test, 1, 2, 1.0F);
  ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_test, 1, 2), 1.0F);
}

TEST(CML_MatrixSetSize, CML_MatrixSetSizeTest) {
  CML_MatrixCreate(matrix_test, 5, 5);
  CML_MatrixSetSize(matrix_test, 3, 4);
  ASSERT_EQ(matrix_test->rows, 3);
  ASSERT_EQ(matrix_test->cols, 4);
}

TEST(CML_MatrixIsEmpty, CML_MatrixIsEmptyTest) {
  CML_MatrixCreate(matrix_test, 4, 3);
  ASSERT_FALSE(CML_MatrixIsEmpty(matrix_test));
  CML_MatrixSetSize(matrix_test, 0, 0);
  ASSERT_TRUE(CML_MatrixIsEmpty(matrix_test));
}

TEST(CML_MatrixSetAll, CML_MatrixSetAllTest) {
  CML_MatrixCreate(matrix_test, 4, 3);
  CML_MatrixSetAll(matrix_test, 4, 3, 1.0F);
  for (uint8_t row = 0; row < 4; ++row) {
    for (uint8_t col = 0; col < 3; ++col) {
      ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_test, row, col), 1.0F);
    }
  }
}

TEST(CML_MatrixSetZero, CML_MatrixSetZeroTest) {
  CML_MatrixCreate(matrix_test, 4, 3);
  CML_MatrixSetAll(matrix_test, 4, 3, 1.0F);
  CML_MatrixSetZero(matrix_test);
  for (uint8_t row = 0; row < 4; ++row) {
    for (uint8_t col = 0; col < 3; ++col) {
      ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_test, row, col), 0.0F);
    }
  }
}

TEST(CML_MatrixSetIdentity, CML_MatrixSetIdentityTest) {
  CML_MatrixCreate(matrix_test, 10, 10);
  CML_MatrixSetIdentity(matrix_test, 10);
  for (uint8_t row = 0; row < 10; ++row) {
    for (uint8_t col = 0; col < 10; ++col) {
      if (row == col) {
        ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_test, row, col), 1.0F);
      } else {
        ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_test, row, col), 0.0F);
      }
    }
  }
}

TEST(CML_MatrixAdd, CML_MatrixAddTest) {
  CML_MatrixCreate(matrix_a, 3, 3);
  CML_MatrixCreate(matrix_b, 3, 3);
  CML_MatrixCreate(matrix_res, 3, 3);

  CML_MatrixSetAll(matrix_a, 3, 3, 1.0F);
  CML_MatrixSetAll(matrix_b, 3, 3, 2.0F);
  CML_MatrixAdd(matrix_res, matrix_a, matrix_b);

  for (uint8_t row = 0; row < 3; ++row) {
    for (uint8_t col = 0; col < 3; ++col) {
      ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_res, row, col), 3.0F);
    }
  }
}

TEST(CML_MatrixSub, CML_MatrixSubTest) {
  CML_MatrixCreate(matrix_a, 3, 3);
  CML_MatrixCreate(matrix_b, 3, 3);
  CML_MatrixCreate(matrix_res, 3, 3);

  CML_MatrixSetAll(matrix_a, 3, 3, 1.0F);
  CML_MatrixSetAll(matrix_b, 3, 3, 2.0F);
  CML_MatrixSub(matrix_res, matrix_a, matrix_b);

  for (uint8_t row = 0; row < 3; ++row) {
    for (uint8_t col = 0; col < 3; ++col) {
      ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_res, row, col), -1.0F);
    }
  }
}

TEST(CML_MatrixMul, CML_MatrixMulTest) {
  CML_MatrixCreate(matrix_a, 3, 3);
  CML_MatrixCreate(matrix_b, 3, 3);
  CML_MatrixCreate(matrix_res, 3, 3);

  CML_MatrixSetAll(matrix_a, 3, 3, 1.0F);
  CML_MatrixSetAll(matrix_b, 3, 3, 2.0F);
  CML_MatrixMul(matrix_res, matrix_a, matrix_b);

  for (uint8_t row = 0; row < 3; ++row) {
    for (uint8_t col = 0; col < 3; ++col) {
      ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_res, row, col), 6.0F);
    }
  }
}

TEST(CML_MatrixScale, CML_MatrixScaleTest) {
  CML_MatrixCreate(matrix_a, 3, 3);

  CML_MatrixSetAll(matrix_a, 3, 3, 1.0F);
  CML_MatrixScale(matrix_a, 2.0F);

  for (uint8_t row = 0; row < 3; ++row) {
    for (uint8_t col = 0; col < 3; ++col) {
      ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_a, row, col), 2.0F);
    }
  }
}

TEST(CML_MatrixInv, Matrix2x2InvTest) {
  CML_MatrixCreate(matrix_a, 3, 3);
  CML_MatrixCreate(matrix_res, 3, 3);

  CML_MatrixSetElement(matrix_a, 0, 0, 1.0F);
  CML_MatrixSetElement(matrix_a, 0, 1, 2.0F);
  CML_MatrixSetElement(matrix_a, 0, 2, 3.0F);
  CML_MatrixSetElement(matrix_a, 1, 0, 1.0F);
  CML_MatrixSetElement(matrix_a, 1, 1, 4.0F);
  CML_MatrixSetElement(matrix_a, 1, 2, 5.0F);
  CML_MatrixSetElement(matrix_a, 2, 0, 7.0F);
  CML_MatrixSetElement(matrix_a, 2, 1, 8.0F);
  CML_MatrixSetElement(matrix_a, 2, 2, 9.0F);

  CML_MatrixInv(matrix_res, matrix_a);

  ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_res, 0, 0), 1.0F / 3.0F);
  ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_res, 0, 1), -0.5F);
  ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_res, 0, 2), 1.0F / 6.0F);
  ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_res, 1, 0), -13.0F / 6.0F);
  ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_res, 1, 1), 1.0F);
  ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_res, 1, 2), 1.0F / 6.0F);
  ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_res, 2, 0), 5.0F / 3.0F);
  ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_res, 2, 1), -0.5F);
  ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_res, 2, 2), -1.0F / 6.0F);
}

TEST(CML_MatrixInv, Matrix3x3InvTest) {
  CML_MatrixCreate(matrix_a, 3, 3);
  CML_MatrixCreate(matrix_res, 3, 3);

  CML_MatrixSetElement(matrix_a, 0, 0, 1.0F);
  CML_MatrixSetElement(matrix_a, 0, 1, 2.0F);
  CML_MatrixSetElement(matrix_a, 0, 2, 3.0F);
  CML_MatrixSetElement(matrix_a, 1, 0, 4.0F);
  CML_MatrixSetElement(matrix_a, 1, 1, 5.0F);
  CML_MatrixSetElement(matrix_a, 1, 2, 6.0F);
  CML_MatrixSetElement(matrix_a, 2, 0, 8.0F);
  CML_MatrixSetElement(matrix_a, 2, 1, 8.0F);
  CML_MatrixSetElement(matrix_a, 2, 2, 9.0F);

  CML_MatrixInv(matrix_res, matrix_a);

  ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_res, 0, 0), 1.0F);
  ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_res, 0, 1), -2.0F);
  ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_res, 0, 2), 1.0F);
  ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_res, 1, 0), -4.0F);
  ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_res, 1, 1), 5.0F);
  ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_res, 1, 2), -2.0F);
  ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_res, 2, 0), 8.0F / 3.0F);
  ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_res, 2, 1), -8.0F / 3.0F);
  ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_res, 2, 2), 1.0F);
}

TEST(CML_MatrixInv, Matrix4x4InvTest) {
  // x = [1 2 3 4;
  //      5 6 7 8;
  //      6 6 8 8;
  //     1 1 1 3];
  // inv_x = [-5/8 -3/8 1/2  1/2;
  //          -3/8 11/8  -1 -1/2;
  //           5/8 -5/8 1/2 -1/2;
  //           1/8 -1/8   0  1/2];
  float a_array[16] = {1.0F, 2.0F, 3.0F, 4.0F, 5.0F, 6.0F, 7.0F, 8.0F,
                       6.0F, 6.0F, 8.0F, 8.0F, 1.0F, 1.0F, 1.0F, 3.0F};
  float a_inv_ref_array[16] = {
      -5.0F / 8.0F, -3.0F / 8.0F, 1.0F / 2.0F, 1.0F / 2.0F,
      -3.0F / 8.0F, 11.0F / 8.0F, -1.0F,       -1.0F / 2.0F,
      5.0F / 8.0F,  -5.0F / 8.0F, 1.0F / 2.0F, -1.0F / 2.0F,
      1.0F / 8.0F,  -1.0F / 8.0F, 0.0F,        1.0F / 2.0F};
  CML_MatrixCreate(matrix_a, 4, 4);
  for (int i = 0; i < 16; ++i) {
    matrix_a->data_ptr[i] = a_array[i];
  }

  CML_MatrixCreate(matrix_res, 4, 4);
  CML_MatrixInv(matrix_res, matrix_a);

  for (int i = 0; i < 16; ++i) {
    ASSERT_NEAR(matrix_res->data_ptr[i], a_inv_ref_array[i], 1e-5F);
  }
}

TEST(CML_MatrixInv, CML_MatrixInvIdentityTest) {
  CML_MatrixCreate(matrix_a, 3, 3);
  CML_MatrixCreate(matrix_res, 3, 3);

  CML_MatrixSetIdentity(matrix_a, 3);
  CML_MatrixInv(matrix_res, matrix_a);

  for (uint8_t row = 0; row < 3; ++row) {
    for (uint8_t col = 0; col < 3; ++col) {
      if (row == col) {
        ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_res, row, col), 1.0F);
      } else {
        ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_res, row, col), 0.0F);
      }
    }
  }
}

TEST(CML_MatrixTranspose, CML_MatrixTransposeTest) {
  CML_MatrixCreate(matrix_a_ptr, 3, 2);
  CML_MatrixCreate(matrix_at_ptr, 2, 3);

  CML_MatrixSetElement(matrix_a_ptr, 0, 0, 1.0F);
  CML_MatrixSetElement(matrix_a_ptr, 0, 1, 2.0F);
  CML_MatrixSetElement(matrix_a_ptr, 1, 0, 3.0F);
  CML_MatrixSetElement(matrix_a_ptr, 1, 1, 4.0F);
  CML_MatrixSetElement(matrix_a_ptr, 2, 0, 5.0F);
  CML_MatrixSetElement(matrix_a_ptr, 2, 1, 6.0F);
  CML_MatrixTranspose(matrix_at_ptr, matrix_a_ptr);

  // matrix_at = matrix_a'
  CML_MatrixCreate(matrix_at_ref_ptr, 2, 3);
  CML_MatrixSetElement(matrix_at_ref_ptr, 0, 0, 1.0F);
  CML_MatrixSetElement(matrix_at_ref_ptr, 0, 1, 3.0F);
  CML_MatrixSetElement(matrix_at_ref_ptr, 0, 2, 5.0F);
  CML_MatrixSetElement(matrix_at_ref_ptr, 1, 0, 2.0F);
  CML_MatrixSetElement(matrix_at_ref_ptr, 1, 1, 4.0F);
  CML_MatrixSetElement(matrix_at_ref_ptr, 1, 2, 6.0F);

  for (uint8_t row = 0; row < 2; ++row) {
    for (uint8_t col = 0; col < 3; ++col) {
      ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_at_ptr, row, col),
                      CML_MatrixGetElement(matrix_at_ref_ptr, row, col));
    }
  }
}

TEST(CML_MatrixCopy, CML_MatrixCopyTest) {
  CML_MatrixCreate(matrix_a_ptr, 3, 2);
  CML_MatrixCreate(matrix_b_ptr, 3, 2);
  CML_MatrixSetZero(matrix_b_ptr);

  CML_MatrixSetElement(matrix_a_ptr, 0, 0, 1.0F);
  CML_MatrixSetElement(matrix_a_ptr, 0, 1, 2.0F);
  CML_MatrixSetElement(matrix_a_ptr, 1, 0, 3.0F);
  CML_MatrixSetElement(matrix_a_ptr, 1, 1, 4.0F);
  CML_MatrixSetElement(matrix_a_ptr, 2, 0, 5.0F);
  CML_MatrixSetElement(matrix_a_ptr, 2, 1, 6.0F);
  CML_MatrixCopy(matrix_b_ptr, matrix_a_ptr);

  for (uint8_t row = 0; row < 3; ++row) {
    for (uint8_t col = 0; col < 2; ++col) {
      ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_b_ptr, row, col),
                      CML_MatrixGetElement(matrix_a_ptr, row, col));
    }
  }
}

TEST(CML_MatrixCopy, CML_MatrixCopyDifferentSizeTest) {
  CML_MatrixCreate(matrix_a_ptr, 3, 2);
  CML_MatrixCreate(matrix_b_ptr, 2, 3);
  CML_MatrixSetZero(matrix_b_ptr);

  CML_MatrixSetElement(matrix_a_ptr, 0, 0, 1.0F);
  CML_MatrixSetElement(matrix_a_ptr, 0, 1, 2.0F);
  CML_MatrixSetElement(matrix_a_ptr, 1, 0, 3.0F);
  CML_MatrixSetElement(matrix_a_ptr, 1, 1, 4.0F);
  CML_MatrixSetElement(matrix_a_ptr, 2, 0, 5.0F);
  CML_MatrixSetElement(matrix_a_ptr, 2, 1, 6.0F);
  CML_MatrixCopy(matrix_b_ptr, matrix_a_ptr);

  for (uint8_t row = 0; row < 2; ++row) {
    for (uint8_t col = 0; col < 3; ++col) {
      ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_b_ptr, row, col),
                      CML_MatrixGetElement(matrix_a_ptr, row, col));
    }
  }
}

TEST(CML_MatrixSwap, CML_MatrixSwapTest) {
  CML_MatrixCreate(matrix_a_ptr, 3, 2);
  CML_MatrixCreate(matrix_b_ptr, 3, 2);
  CML_MatrixCreate(matrix_a_ref_ptr, 3, 2);
  CML_MatrixCreate(matrix_b_ref_ptr, 3, 2);

  CML_MatrixSetElement(matrix_a_ptr, 0, 0, 1.0F);
  CML_MatrixSetElement(matrix_a_ptr, 0, 1, 2.0F);
  CML_MatrixSetElement(matrix_a_ptr, 1, 0, 3.0F);
  CML_MatrixSetElement(matrix_a_ptr, 1, 1, 4.0F);
  CML_MatrixSetElement(matrix_a_ptr, 2, 0, 5.0F);
  CML_MatrixSetElement(matrix_a_ptr, 2, 1, 6.0F);
  CML_MatrixCopy(matrix_a_ref_ptr, matrix_a_ptr);

  CML_MatrixSetElement(matrix_b_ptr, 0, 0, 7.0F);
  CML_MatrixSetElement(matrix_b_ptr, 0, 1, 8.0F);
  CML_MatrixSetElement(matrix_b_ptr, 1, 0, 9.0F);
  CML_MatrixSetElement(matrix_b_ptr, 1, 1, 10.0F);
  CML_MatrixSetElement(matrix_b_ptr, 2, 0, 3.0F);
  CML_MatrixSetElement(matrix_b_ptr, 2, 1, 5.0F);
  CML_MatrixCopy(matrix_b_ref_ptr, matrix_b_ptr);

  CML_MatrixSwap(matrix_a_ptr, matrix_b_ptr);

  for (uint8_t row = 0; row < 3; ++row) {
    for (uint8_t col = 0; col < 2; ++col) {
      ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_a_ptr, row, col),
                      CML_MatrixGetElement(matrix_b_ref_ptr, row, col));
      ASSERT_FLOAT_EQ(CML_MatrixGetElement(matrix_b_ptr, row, col),
                      CML_MatrixGetElement(matrix_a_ref_ptr, row, col));
    }
  }
}

TEST(CML_MatrixPrint, CML_MatrixPrintTest) {
  CML_MatrixCreate(matrix_a_ptr, 3, 2);

  CML_MatrixSetElement(matrix_a_ptr, 0, 0, 1.0F);
  CML_MatrixSetElement(matrix_a_ptr, 0, 1, 2.0F);
  CML_MatrixSetElement(matrix_a_ptr, 1, 0, 3.0F);
  CML_MatrixSetElement(matrix_a_ptr, 1, 1, 4.0F);
  CML_MatrixSetElement(matrix_a_ptr, 2, 0, 5.0F);
  CML_MatrixSetElement(matrix_a_ptr, 2, 1, 6.0F);

  testing::internal::CaptureStdout();

  CML_MatrixPrint(matrix_a_ptr);

  std::string expected_output =
      "1.0000 2.0000 \n"
      "3.0000 4.0000 \n"
      "5.0000 6.0000 \n";
  ASSERT_EQ(testing::internal::GetCapturedStdout(), expected_output);
}
