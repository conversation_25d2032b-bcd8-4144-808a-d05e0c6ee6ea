#include "planner/decoupled_maneuvers/xlane_nudge/base_xlane_nudge_strategy.h"

#include <algorithm>
#include <limits>
#include <map>
#include <numeric>
#include <unordered_map>
#include <vector>

#include "geometry/model/point_2d.h"
#include "geometry/model/polyline_curve.h"
#include "hdmap_protos/lane.pb.h"
#include "math/constants.h"
#include "math/math_util.h"
#include "math/range.h"
#include "mrc_protos/mrc_request.pb.h"
#include "planner/constants.h"
#include "planner/decoupled_maneuvers/xlane_nudge/xlane_nudge_param.h"
#include "planner/decoupled_maneuvers/xlane_nudge/xlane_nudge_util.h"
#include "planner/utility/object_id/typed_object_id.h"
#include "planner/utility/unstuck/stuck_region/stuck_region.h"
#include "planner/utility/unstuck/stuck_region/stuck_region_common.h"
#include "planner/world_model/lane_blockage/lane_blockage.h"
#include "planner/world_model/snapshot/robot_state.h"
#include "planner/world_model/snapshot/robot_state_snapshot.h"
#include "planner_protos/agent_intention_config.pb.h"
#include "planner_protos/behavior_reasoners_debug.pb.h"
#include "planner_protos/lane_blockage.pb.h"
#include "planner_protos/path_reasoning_seed.pb.h"
#include "pnc_map_service/map_elements/lane.h"
#include "pnc_map_service/map_elements/lane_marking.h"
#include "voy_protos/math.pb.h"
#include "voy_protos/traffic_light.pb.h"

namespace planner {
namespace xlane_nudge {

namespace {
// The default lateral distance threshold to check if the ego returns to nominal
// path.
constexpr double kDefaultThresholdCloseToNominalPathInMeter = 0.5;
// The minimum exceeding longitudinal distance to check if the ego has fully
// overtaken the stuck region.
constexpr double kMinFullyOvertakenDistanceInMeter = 5.0;
// Default heading difference to compare if the ego's heading aligns with
// tangent direction of the nominal path.
constexpr double kDefaultHeadingToleranceInRadians = 0.2;
// Default longitudinal distance threshold to check if the ego is close to the
// stuck region.
constexpr double kDefaultCloseLongitudinalDistToStuckRegionInMeter = 25.0;
// Default longitudinal distance threshold to check if there is a close u-turn
// to the stuck region.
constexpr double kDefaultCloseLongitudinalDistToUTurnInMeter = 110.0;
// Default distance threshold that indicates stuck region is far away the
// junction, and ego could merge back before entering junction.
constexpr double kDefaultDistanceFarawayJunctionInMeter = 15.0;
constexpr double kMinDistanceFarawayJunctionInMeter = 10.0;
// Default lateral buffer to check if there is no clearance to hard boundary
// for pass. 0.7 = 0.4(min_critical_lat_gap) + 0.3(physical_boundary_lat_gap)
constexpr double kDefaultClearanceBufferToHardBoundaryInMeter = 0.7;
// Default waiting time for xlane nudge triggering for runnable agents when the
// traffic light turns to green.
constexpr int64_t kWaitingTimeForRunnableAgentAfterTLRelaxInMSec = 5000;
// Default max allowable boundary offset to xlane nudge nearby u-turn region.
constexpr double kDefaultMaxBoundaryOffsetNearbyUTurnInMeter = 1.5;
// Default max waiting duration for the temp-parked.
constexpr int64_t kMaxWaitTimeDurationForTempParkedInMSec = 20000;
// Default longitudinal range buffer for stuck region.
constexpr double kStuckRegionLongitudinalBufferInMeter = 5.0;

// Returns true to disable xlane nudge if ego needs to borrow right large
// space to unstuck nearby the u-turn region.
bool ShouldDisableXLaneNudgeNearbyUTurn(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const math::geometry::PolylineCurve2d& reference_curve,
    const StuckRegion& stuck_region, const XLaneSideParams& params) {
  const auto u_turn_iter =
      std::find_if(lane_sequence.begin(), lane_sequence.end(),
                   [](const pnc_map::Lane* lane) {
                     DCHECK(lane != nullptr);
                     return lane->turn() == hdmap::Lane::U_TURN &&
                            lane->type() == hdmap::Lane::VIRTUAL;
                   });
  // Return false if there is no u-turn in lane sequence.
  if (u_turn_iter == lane_sequence.end()) return false;

  const double u_turn_start_pos =
      reference_curve
          .GetProximity(
              DCHECK_NOTNULL(*u_turn_iter)->center_line().GetStartPoint(),
              math::pb::UseExtensionFlag::kForbid)
          .arc_length;
  const double u_turn_end_pos =
      u_turn_start_pos + (*u_turn_iter)->center_line().GetTotalArcLength();
  const math::Range1d u_turn_region_range(
      u_turn_start_pos - kDefaultCloseLongitudinalDistToUTurnInMeter,
      u_turn_end_pos);

  // Check if stuck region is nearby u-turn region, and disable left-side xlane
  // nudge if ego is turning from left-most lane, and disable right side if the
  // lateral movement is too far.
  if (math::AreRangesOverlapping(u_turn_region_range,
                                 stuck_region.longitudinal_range())) {
    // Check if the ego will enter the uturn lane from left-most lane, or ego
    // will turn to left-most lane after uturn when uturn lane is the first lane
    // of the lane sequence. For those scenes, unstuck from left side might be
    // not a good choice at most time.
    const bool is_u_turn_from_left_most_lane =
        params.side == pb::StuckRegion::kLeft &&
        ((u_turn_iter != lane_sequence.begin() &&
          (*std::prev(u_turn_iter))->IsLeftmostDrivableLane()) ||
         (u_turn_iter == lane_sequence.begin() &&
          std::next(u_turn_iter) != lane_sequence.end() &&
          (*std::next(u_turn_iter))->IsLeftmostDrivableLane()));

    // Disable xlane nudge if the ego is turning from left-most lane, or the
    // right side is kNormal overtake policy (for Temp-Parked) and the lateral
    // movement exceeds the threshold.
    return is_u_turn_from_left_most_lane ||
           (params.side == pb::StuckRegion::kRight &&
            params.policy != pb::StuckRegion::kNormal &&
            params.required_offset >
                kDefaultMaxBoundaryOffsetNearbyUTurnInMeter);
  }

  return false;
}

// Returns true if any of the given lanes has associated traffic lights.
inline bool HasTrafficLightAssociatedLane(
    const std::vector<const pnc_map::Lane*>& lanes,
    const pnc_map::JointPncMapService& joint_pnc_map_service) {
  return std::any_of(lanes.begin(), lanes.end(),
                     [&joint_pnc_map_service](const pnc_map::Lane* lane) {
                       return !joint_pnc_map_service
                                   .GetTrafficSignals(*DCHECK_NOTNULL(lane))
                                   .empty();
                     });
}

// Returns true if the unstuck side given by the assist instruction matches with
// the overtake side of the x-lane nudge strategy.
bool IsSameSide(pb::UnstuckSide::Enum unstuck_side,
                pb::StuckRegion::OvertakeSide overtake_side) {
  switch (overtake_side) {
    case pb::StuckRegion::kLeft:
      return unstuck_side == pb::UnstuckSide::kLeft ||
             unstuck_side == pb::UnstuckSide::kBoth ||
             unstuck_side == pb::UnstuckSide::kEither;
    case pb::StuckRegion::kRight:
      return unstuck_side == pb::UnstuckSide::kRight ||
             unstuck_side == pb::UnstuckSide::kBoth ||
             unstuck_side == pb::UnstuckSide::kEither;
    default:
      DCHECK(false) << "Unexpected overtake side: "
                    << pb::StuckRegion::OvertakeSide_Name(overtake_side);
      return false;
  }
}

// Returns lanes in the lane sequence occupied by the stuck region. Note that
// we would prefer to also consider the predecessor lane of the first occupied
// lane so that we can easily handle both stuck before-junction and in-junction
// scenarios.
std::vector<const pnc_map::Lane*> GetInterestedLanesForStuckRegion(
    const StuckRegion& stuck_region,
    const std::vector<const pnc_map::Lane*>& lane_sequence) {
  DCHECK(!stuck_region.occupied_lanes().empty());
  // Find the last occupied lane's iterator on the lane sequence.
  auto iter = std::find_if(
      lane_sequence.begin(), lane_sequence.end(),
      [lane_id = stuck_region.occupied_lanes().front()->id()](
          const pnc_map::Lane* lane) { return lane->id() == lane_id; });
  if (iter == lane_sequence.end()) {
    return {};
  }

  // Move the first occupied lane iterator one step back, so that we can cover
  // the case that stuck region blocked in the junction.
  if (iter != lane_sequence.begin()) {
    --iter;
  }
  return {iter, lane_sequence.end()};
}

// Returns true if the blocking type of blockage is runnable.
bool IsRunnableBlockage(pb::BlockingType blocking_type) {
  return blocking_type != pb::BlockingType::kConstructionZone &&
         blocking_type != pb::BlockingType::kTrafficCone &&
         blocking_type != pb::BlockingType::kBackground &&
         blocking_type != pb::BlockingType::kRaBlockage &&
         blocking_type != pb::BlockingType::kOccupancy &&
         blocking_type != pb::BlockingType::kMapChangeArea;
}

// Returns the waiting duration for triggering xlane nudge when the traffic
// light turns to runnable status.
int64_t GetTLWaitingDurationForXLaneNudgeTriggering(
    const std::unordered_map<TypedObjectId, StuckObjectMeta>& stuck_objects,
    double ego_fb_arclength) {
  // Check if any of blocking objects ahead is runnable. In some scenarios,
  // perception might output fp occupancy or CZs at the rear of the vehicle.
  // TODO(tianxi): refine the runnable check logic by considering a limit
  // distance ahead of the ego
  const bool has_runnable_blockage = std::any_of(
      stuck_objects.begin(), stuck_objects.end(),
      [&ego_fb_arclength](const auto& blocking_meta) {
        return blocking_meta.second.s_range.start_pos > ego_fb_arclength &&
               IsRunnableBlockage(blocking_meta.second.blocking_type);
      });

  // Ego should politely wait for a while if a runnable agent is fully ahead of
  // ego. The main ideal is that the blocking agent ahead maybe not a real
  // blockage, but rather than a static agent waiting for the traffic light. Its
  // reaction to the traffic light change is not as same sensitive as that of
  // ego.
  return has_runnable_blockage ? kWaitingTimeForRunnableAgentAfterTLRelaxInMSec
                               : 0;
}

// Returns true if ego's center point could merge back to original lane sequence
// before the area controlled traffic light.
bool CanEgoFullyOvertakeAndPartialMergeBack(
    const StuckRegion& stuck_region, double tl_controlled_pos, double ego_width,
    double ego_length, bool is_pure_non_agent_static_region, bool is_left) {
  constexpr double kDefaultRequiredLateralGapForStuckRegionInMeter = 0.4;
  const double min_required_dist_to_tl =
      is_pure_non_agent_static_region ? kMinDistanceFarawayJunctionInMeter
                                      : kDefaultDistanceFarawayJunctionInMeter;
  // Return false if stuck region is very close to the stop line.
  if ((tl_controlled_pos - stuck_region.longitudinal_range().start_pos) <
      ego_length) {
    return false;
  }

  // Return true if there are enough space for ego merging back.
  if ((tl_controlled_pos - stuck_region.longitudinal_range().end_pos) >
      min_required_dist_to_tl) {
    return true;
  }

  // Return true if lateral clearance is greater than half of ego width, which
  // means ego can merge most part of body back.
  if ((is_left ? stuck_region.left_inlane_clearance()
               : stuck_region.right_inlane_clearance()) >
      ego_width * 0.5 + kDefaultRequiredLateralGapForStuckRegionInMeter) {
    return true;
  }
  const math::Range1d merge_back_and_wait_range(
      /*start_pos_in=*/tl_controlled_pos - min_required_dist_to_tl,
      /*end_pos_in=*/tl_controlled_pos + min_required_dist_to_tl);
  double min_side_clearance = std::numeric_limits<double>::max();
  for (const auto& [_, meta] : stuck_region.stuck_objects()) {
    if (math::AreRangesOverlapping(meta.s_range, merge_back_and_wait_range)) {
      math::UpdateMin(
          is_left ? meta.left_inlane_clearance : meta.right_inlane_clearance,
          min_side_clearance);
    }
  }

  return min_side_clearance >
         (ego_width * 0.5 + kDefaultRequiredLateralGapForStuckRegionInMeter);
}

// Returns the minimum required boundary lateral offset distance for xlane nudge
// triggering.
double GetMinRequiredLateralBoundaryOffset(
    double lane_marking_violation_tolerance) {
  static double kDefaultObjectComfortLateralGapInMeter = 0.5;
  //
  // ego_width + kMinRequiredLateralGapInMeter - clearance = boundary_offset (a)
  //
  // clearance = ego_width + kMinRequiredLateralGapInMeter - boundary_offset (b)
  //
  // In this function, we want to get the minimum threshold of the
  // 'boundary_offset' to trigger xlane nudge. And this threshold is related to
  // the 'clearance', expressed as:
  //
  //  clearance + violation_tolerance <
  //            ego_width + kDefaultObjectComfortLateralGapInMeter           (c)
  //
  // In other words, if (c) is satisfied, we prefer to trigger xlane nudge.
  // Combined (b) and (c), we can get:
  //
  // (kMinRequiredLateralGapInMeter - kDefaultObjectComfortLateralGapInMeter
  //     + violation_tolerance) < boundary_offset
  // then, the minimum required boundary offset is got.
  return kMinRequiredLateralGapInMeter -
         kDefaultObjectComfortLateralGapInMeter +
         lane_marking_violation_tolerance;
}

// Returns true if the ego is close to the stuck region.
bool IsEgoCloseToStuckRegion(const StuckRegion& stuck_region,
                             double ego_fb_arclength) {
  // TODO(tianxi): consider to buffer the stuck region based on the ego current
  // velocity and the comfort deceleration.
  const math::Range1d buffer_stuck_range(
      stuck_region.longitudinal_range().start_pos -
          kStuckRegionLongitudinalBufferInMeter,
      stuck_region.longitudinal_range().end_pos +
          kStuckRegionLongitudinalBufferInMeter);
  return math::IsInRange(ego_fb_arclength, buffer_stuck_range.start_pos,
                         buffer_stuck_range.end_pos);
}

// Projects blocked lane marking segments to the nominal path and returns those
// longitudinal ranges on the nominal path.
std::vector<math::Range1d> GetSideBlockedLaneMarkingRanges(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const std::map<int64_t, std::vector<LaneMarkingBlockage>>&
        blocking_lane_marking_map,
    const math::geometry::PolylineCurve2d& nominal_path,
    math::pb::Side expected_cross_side) {
  if (blocking_lane_marking_map.empty() || lane_sequence.empty()) {
    return {};
  }

  std::vector<math::Range1d> blocked_lane_marking_ranges;
  blocked_lane_marking_ranges.reserve(std::accumulate(
      blocking_lane_marking_map.begin(), blocking_lane_marking_map.end(), 0,
      [](int acc, const auto& blocking_lane_marking_pair) {
        return acc + blocking_lane_marking_pair.second.size();
      }));
  for (const pnc_map::Lane* lane : lane_sequence) {
    DCHECK_NE(lane, nullptr);
    const pnc_map::LaneMarking* side_lane_marking =
        expected_cross_side == math::pb::kLeft ? lane->left_marking()
                                               : lane->right_marking();
    if (side_lane_marking == nullptr) {
      continue;
    }
    const auto iter = blocking_lane_marking_map.find(side_lane_marking->id());
    if (iter == blocking_lane_marking_map.end()) {
      continue;
    }

    for (const LaneMarkingBlockage& blockage : iter->second) {
      const math::geometry::Point2d start_point =
          lane->center_line().GetInterp(blockage.arc_length_range_m.start_pos,
                                        math::pb::UseExtensionFlag::kForbid);
      const math::geometry::Point2d end_point =
          lane->center_line().GetInterp(blockage.arc_length_range_m.end_pos,
                                        math::pb::UseExtensionFlag::kForbid);
      const double nominal_path_start_pos =
          nominal_path
              .GetProximity(start_point, math::pb::UseExtensionFlag::kForbid)
              .arc_length;
      const double nominal_path_end_pos =
          nominal_path
              .GetProximity(end_point, math::pb::UseExtensionFlag::kForbid)
              .arc_length;
      // Skip invalid range.
      if (nominal_path_start_pos >= nominal_path_end_pos) {
        continue;
      }

      blocked_lane_marking_ranges.emplace_back(nominal_path_start_pos,
                                               nominal_path_end_pos);
    }
  }
  return blocked_lane_marking_ranges;
}
}  // namespace

BaseXLaneNudgeStrategy::BaseXLaneNudgeStrategy(
    const WorldModel& world_model, const StuckRegion& stuck_region,
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const lane_selection::LaneSequenceGeometry& lane_sequence_geometry,
    const pb::XLaneNudgeSeed& last_seed, bool was_ego_stuck,
    bool is_stuck_signal_on_last_lane_sequence,
    const pb::XLaneNudgeDeciderConfig& config)
    : stuck_region_(stuck_region),
      lane_sequence_(lane_sequence),
      lane_blockages_(world_model.lane_blockage_detector().lane_blockages()),
      blocking_lane_marking_map_(
          world_model.lane_blockage_detector().blocked_lane_marking_segments()),
      robot_state_(world_model.robot_state()),
      traffic_lights_(world_model.traffic_light_detection()),
      joint_pnc_map_service_(
          *DCHECK_NOTNULL(world_model.GetLatestJointPncMapService())),
      mrc_request_(world_model.mrc_request_ptr().get()),
      ls_geo_(lane_sequence_geometry),
      last_seed_(last_seed),
      config_(config),
      was_ego_stuck_(was_ego_stuck),
      is_stuck_signal_on_last_lane_sequence_(
          is_stuck_signal_on_last_lane_sequence),
      should_planner_respond_mrc_(world_model.should_planner_respond_mrc()) {
  const auto& optional_unstuck_directives =
      world_model.assist_directive_generator().unstuck_directives();
  assist_instruction_ = optional_unstuck_directives.has_value()
                            ? &(optional_unstuck_directives->assist_instruction)
                            : nullptr;
  ego_fb_arclength_ =
      ls_geo_.nominal_path
          .GetProximity(
              robot_state_.plan_init_state_snapshot().front_bumper_position(),
              math::pb::UseExtensionFlag::kForbid)
          .arc_length;
  ego_rb_arclength_ =
      ls_geo_.nominal_path
          .GetProximity(
              robot_state_.plan_init_state_snapshot().rear_bumper_position(),
              math::pb::UseExtensionFlag::kForbid)
          .arc_length;
}

bool BaseXLaneNudgeStrategy::IsCapableStuckRegionForXLaneNudge() const {
  if (stuck_region_.is_restored_from_last_seed()) {
    return true;
  }
  const bool is_overtaking_stuck_region =
      ego_fb_arclength_ > stuck_region_.longitudinal_range().start_pos;
  return IsPersistentStuckRegion(
      stuck_region_, /*enable_freshness=*/is_overtaking_stuck_region);
}

// Returns true if ego finished overtaking and merge back to nominal path.
bool BaseXLaneNudgeStrategy::IsXLaneNudgeFinished() const {
  const math::geometry::Point2d& ego_ra_point =
      robot_state_.plan_init_state_snapshot().rear_axle_position();
  const double ego_heading = robot_state_.plan_init_state_snapshot().heading();
  const auto nominal_path_proximity = ls_geo_.nominal_path.GetProximity(
      ego_ra_point, math::pb::UseExtensionFlag::kForbid);
  const double ego_ra_arclength = nominal_path_proximity.arc_length;
  const double curve_heading =
      ls_geo_.nominal_path.GetInterpTheta(ego_ra_arclength);
  double lane_sequence_stuck_end_pos = std::numeric_limits<double>::lowest();
  for (const auto& [_, meta] : stuck_region_.stuck_objects()) {
    // Get the stuck end pos on the lane sequence. Here we only consider the
    // static agents occupied the lane sequence. Ideally, if ego merges back to
    // the lane sequence's center line and exceeds all static agents, ego might
    // don't need to do xlane nudge anymore.
    if (meta.speed < constants::kDefaultAgentNearStaticSpeedInMps &&
        !meta.is_post_group_object) {
      math::UpdateMax(meta.s_range.end_pos, lane_sequence_stuck_end_pos);
    }
  }

  // Return true if ego satisfies following conditions:
  // 1). the ego rear axle exceeds stuck range on the lane sequence,
  // 2). ego heading approximates to the nominal path direction,
  // 3). ego is close to the nominal path.
  return ego_ra_arclength > (lane_sequence_stuck_end_pos +
                             kMinFullyOvertakenDistanceInMeter) &&
         math::IsApprox(0.0, math::AngleDiff(curve_heading, ego_heading),
                        /*tolerance=*/kDefaultHeadingToleranceInRadians) &&
         nominal_path_proximity.dist <
             kDefaultThresholdCloseToNominalPathInMeter;
}

bool BaseXLaneNudgeStrategy::ShouldEmployKeepWaitingPolicy(
    const XLaneSideParams& params) const {
  switch (params.policy) {
    case pb::StuckRegion::kOnlyNotify:
      return true;
    case pb::StuckRegion::kNotifyAndOvertake:
      DCHECK_EQ(stuck_region_.GetFirstBlockageType(),
                pb::BlockingType::kTempParked);
      // Currently, in this policy, we expected ego to overtake till it has been
      // stuck for a long time.
      // TODO(tianxi): polish this logic according to road test feedback and
      // recommend from PM.
      return std::min(robot_state_.GetStationaryDurationInMSec(),
                      stuck_region_.GetMaxTempParkedDuration()) <
             kMaxWaitTimeDurationForTempParkedInMSec;
    case pb::StuckRegion::kNormal:
      return false;
    default:
      return false;
  }
}

bool BaseXLaneNudgeStrategy::IsWaitingForTrafficLight(
    const XLaneSideParams& params) const {
  const auto& interested_lanes =
      GetInterestedLanesForStuckRegion(stuck_region_, lane_sequence_);
  const auto lane_before_tl_iter =
      std::find_if(interested_lanes.begin(), interested_lanes.end(),
                   [this](const pnc_map::Lane* lane) {
                     DCHECK(lane != nullptr);
                     return HasTrafficLightAssociatedLane(
                         lane->successors(), joint_pnc_map_service_);
                   });
  // Return false if all lanes occupied by the stuck region are not before the
  // traffic light.
  if (lane_before_tl_iter == interested_lanes.end()) {
    return false;
  }

  const double estimated_tl_controlled_pos =
      ls_geo_.nominal_path
          .GetProximity((*lane_before_tl_iter)->center_line().GetEndPoint(),
                        math::pb::UseExtensionFlag::kForbid)
          .arc_length;
  const bool is_pure_non_agent_static_region = std::all_of(
      stuck_region_.stuck_objects().begin(),
      stuck_region_.stuck_objects().end(), [](const auto& meta) {
        return meta.second.blocking_type == pb::BlockingType::kTrafficCone ||
               meta.second.blocking_type ==
                   pb::BlockingType::kConstructionZone ||
               meta.second.blocking_type == pb::BlockingType::kOccupancy;
      });
  // Return false if ego can fully overtake or the partially merge back.
  if (CanEgoFullyOvertakeAndPartialMergeBack(
          stuck_region_, estimated_tl_controlled_pos, robot_state_.GetWidth(),
          robot_state_.GetLength(), is_pure_non_agent_static_region,
          /*is_left=*/params.side == pb::StuckRegion::kLeft)) {
    return false;
  }

  // Return false if the ego has fully entered the tl-controlled area.
  if (ego_rb_arclength_ > estimated_tl_controlled_pos) {
    return false;
  }

  const int64_t tl_waiting_duration =
      GetTLWaitingDurationForXLaneNudgeTriggering(stuck_region_.stuck_objects(),
                                                  ego_fb_arclength_);
  const auto& successor_lanes = (*lane_before_tl_iter)->successors();
  // Return true if any successor lanes are controlled the traffic light.
  return std::any_of(successor_lanes.begin(), successor_lanes.end(),
                     [&tl_waiting_duration, this](const pnc_map::Lane* lane) {
                       DCHECK(lane != nullptr);
                       return IsLaneControlledByTrafficLight(
                           joint_pnc_map_service_, *lane, traffic_lights_,
                           tl_waiting_duration);
                     });
}

double BaseXLaneNudgeStrategy::GetMaxSpeedLimitForTriggering() const {
  const bool is_stable_recognized_object = std::all_of(
      stuck_region_.stuck_objects().begin(),
      stuck_region_.stuck_objects().end(), [](const auto& blocking_meta) {
        return blocking_meta.second.blocking_type ==
                   pb::BlockingType::kConstructionZone ||
               blocking_meta.second.blocking_type ==
                   pb::BlockingType::kTrafficCone ||
               blocking_meta.second.blocking_type ==
                   pb::BlockingType::kMapChangeArea;
      });
  return is_stable_recognized_object
             ? config_.max_trigger_speed_for_stable_stuck_region_mps()
             : config_.max_trigger_speed_for_normal_stuck_region_mps();
}

bool BaseXLaneNudgeStrategy::IsSideLaneMarkingNotCrossable(
    const XLaneSideParams& current_side_params) const {
  const math::Range1d ego_longitudinal_range = {
      std::min(ego_rb_arclength_, ego_fb_arclength_) -
          math::constants::kEpsilon,
      std::max(ego_rb_arclength_, ego_fb_arclength_) +
          math::constants::kEpsilon};
  if (!math::IsValidRange(ego_longitudinal_range) ||
      !math::IsValidRange(stuck_region_.fully_longitudinal_range())) {
    return true;
  }

  // No need to check if the lane marking is crossable if the ego has not
  // enterred the stuck range.
  if (!math::AreRangesOverlapping(ego_longitudinal_range,
                                  stuck_region_.fully_longitudinal_range())) {
    return false;
  }

  // Return false if ego's rear axle point has crossed the side lane marking.
  const math::pb::Side expected_cross_side =
      current_side_params.side == pb::StuckRegion::kLeft ? math::pb::kLeft
                                                         : math::pb::kRight;
  const math::geometry::PolylineCurve2d& side_lane_marking_boundary =
      expected_cross_side == math::pb::kLeft ? ls_geo_.left_lane_boundary
                                             : ls_geo_.right_lane_boundary;
  if (side_lane_marking_boundary.GetSide(
          robot_state_.plan_init_state_snapshot().rear_axle_position()) ==
      expected_cross_side) {
    return false;
  }

  // Get side lane marking blocking ranges on the nominal path.
  const std::vector<math::Range1d> blocked_lane_marking_ranges =
      GetSideBlockedLaneMarkingRanges(
          lane_sequence_, blocking_lane_marking_map_, ls_geo_.nominal_path,
          expected_cross_side);

  // Return true if the unstuck range is blocked by the uncrossable lane
  // marking.
  return std::any_of(
      blocked_lane_marking_ranges.begin(), blocked_lane_marking_ranges.end(),
      [&ego_longitudinal_range](const math::Range1d& range) {
        return math::AreRangesOverlapping(ego_longitudinal_range, range);
      });
}

bool BaseXLaneNudgeStrategy::IsClearanceWideEnoughToPass(
    const XLaneSideParams& opposite_side_params,
    const XLaneSideParams& current_side_params) const {
  // Lambda function to check if required offset of one side is smaller than the
  // min required boundary offset for xlane nudge trigger.
  const auto is_side_wide_enough = [&config = config_](
                                       pb::StuckRegion::OvertakeMode mode,
                                       double required_offset) -> bool {
    switch (mode) {
      case pb::StuckRegion::kOncomingLane:
        return required_offset <
               GetMinRequiredLateralBoundaryOffset(
                   config.max_lm_violation_to_borrow_oncoming_lane());
      case pb::StuckRegion::kFreeSpace:
        return required_offset <
               GetMinRequiredLateralBoundaryOffset(
                   config.max_lm_violation_to_borrow_free_space());
      case pb::StuckRegion::kNeighborLane:
        return required_offset <
               GetMinRequiredLateralBoundaryOffset(
                   config.max_lm_violation_to_borrow_neighbor_lane());
      default:
        DCHECK(false) << "Unsupported stuck region overtake mode: "
                      << pb::StuckRegion::OvertakeMode_Name(mode);
        return true;
    }
  };

  // Compute ego relative longitudinal ranges on the nominal path.
  const math::Range1d ego_long_range_on_nominal_path = {
      std::min(ego_rb_arclength_, ego_fb_arclength_) -
          math::constants::kEpsilon,
      std::max(ego_rb_arclength_, ego_fb_arclength_) +
          math::constants::kEpsilon};

  // If the ego has no overlap with stuck region in longitudinal direction,
  // check clearances of both sides. Otherwise, only check current clearance of
  // current side.
  if (math::IsValidRange(ego_long_range_on_nominal_path) &&
      !math::AreRangesOverlapping(ego_long_range_on_nominal_path,
                                  stuck_region_.fully_longitudinal_range())) {
    return is_side_wide_enough(opposite_side_params.mode,
                               opposite_side_params.required_offset) ||
           is_side_wide_enough(current_side_params.mode,
                               current_side_params.required_offset);
  }

  return is_side_wide_enough(current_side_params.mode,
                             current_side_params.required_offset);
}

bool BaseXLaneNudgeStrategy::ShouldIgnoreEgoSpeedLimit() const {
  const bool is_ego_in_low_speed =
      robot_state_.plan_init_state_snapshot().speed() <
      GetMaxSpeedLimitForTriggering();
  // Always return true if the speed is under limitation.
  if (is_ego_in_low_speed) {
    return true;
  }

  // Return false if the stuck signal is on the last driving lane sequence, so
  // that we can carefully trigger xlane nudge. Otherwise, check if the ego is
  // potentially on the side of the neighbor lane's stuck region.
  return is_stuck_signal_on_last_lane_sequence_
             ? false
             : IsEgoCloseToStuckRegion(stuck_region_, ego_fb_arclength_);
}

PreCheckResult BaseXLaneNudgeStrategy::PreCheck(
    const XLaneSideParams& opposite_side_params,
    const XLaneSideParams& params) const {
  // The first three functions are used to check whether the ego is ready to
  // trigger the x-lane nudge behavior when it is not already in progress.
  // nudge when it's not triggered. Once ego enters the x-lane nudge behavior,
  // these functions are then used for the abort, finish, and continuous logic.
  // TODO(tianxi): revisit those two checks to give more indicative signals so
  // that ego can run well-designed abort/finish behavior.
  if (!IsCapableStuckRegionForXLaneNudge()) {
    return PreCheckResult(false, pb::XLaneNudgeDebug::kNotPersistentStuck);
  }

  if (IsXLaneNudgeFinished()) {
    return PreCheckResult(false, pb::XLaneNudgeDebug::kFinished);
  }

  // Keep executing x-lane nudge.
  if (IsXLaneNudging(params.side)) {
    return PreCheckResult(true, pb::XLaneNudgeDebug::kSuccess);
  }

  // All following logics are only for initial triggering. Their position can be
  // swapped with each other, except the specialized check function.
  if (!IsRaRequestingXLaneNudge() && !IsStrategyEnabled()) {
    return PreCheckResult(false, pb::XLaneNudgeDebug::kDisabled);
  }

  // Disable xlane nudge when MRC request type is MRM_INLANE or MRM_URGENCY;
  if (should_planner_respond_mrc_ && mrc_request_ != nullptr &&
      (mrc_request_->mrm_type() == mrc::pb::MRM_INLANE ||
       mrc_request_->mrm_type() == mrc::pb::MRM_URGENCY)) {
    return PreCheckResult(false, pb::XLaneNudgeDebug::kDisabled);
  }

  // Disable the mismatched side x-lane nudge for the active trigger by remote
  // assist.
  if (IsRaRequestingXLaneNudge() &&
      !IsSameSide(assist_instruction_->unstuck_side, params.side)) {
    return PreCheckResult(false, pb::XLaneNudgeDebug::kNotSelected);
  }

  // For a stuck region, left CLN may have different overtake mode compared to
  // the right side, and they will have different pre-checking conditions. So
  // the IsXLaneNudging() will not maintain triggering state from left side to
  // right side. Once one-side CLN is selected (which may not be the optimal
  // side), in the next cycle, to make another side CLN have chance to be
  // selected, we only check stuck signal if
  // 1. ego was not executing CLN, or
  // 2. ego was executing CLN, but the region is not in current lane sequence.
  if (!was_ego_stuck_ &&
      (last_seed_.side() == pb::StuckRegion::kNotOvertake ||
       !IsRegionInLaneSequence(last_seed_.target_stuck_region(),
                               lane_sequence_))) {
    return PreCheckResult(false, pb::XLaneNudgeDebug::kNoStuckSignal);
  }

  if (params.dist_to_physical_boundary <
      (kDefaultClearanceBufferToHardBoundaryInMeter +
       robot_state_.GetWidth())) {
    return PreCheckResult(false, pb::XLaneNudgeDebug::kNoClearance);
  }

  if ((stuck_region_.longitudinal_range().start_pos - ego_fb_arclength_) >
      kDefaultCloseLongitudinalDistToStuckRegionInMeter) {
    return PreCheckResult(false, pb::XLaneNudgeDebug::kFaraway);
  }

  // TODO(tianxi): revisit this logic to consider the scenario that is in the
  // non-junction area, and consider the object type in the stuck region.
  // TODO(path/speed): this logic is mainly for reduce FP triggering before
  // junction which might caused by FP perception signal, such kParked or
  // construction zone(cn9397276). Ideally, we need more general logic to reason
  // correct stop line for traffic light in speed reasoner.
  // If the instruction requires to execute x-lane nudge, don't check traffic
  // light anymore.
  if (!IsRaRequestingXLaneNudge() && IsWaitingForTrafficLight(params)) {
    return PreCheckResult(false, pb::XLaneNudgeDebug::kTrafficLight);
  }

  // TODO(tianxi): revisit this logic for x-neighbor-lane nudge.
  if (ShouldDisableXLaneNudgeNearbyUTurn(lane_sequence_, ls_geo_.nominal_path,
                                         stuck_region_, params)) {
    return PreCheckResult(false, pb::XLaneNudgeDebug::kCloseToUTurn);
  }

  // Check if the side is not crossable due to lined traffic cones.
  if (IsSideLaneMarkingNotCrossable(params)) {
    return PreCheckResult(false, pb::XLaneNudgeDebug::kNotCrossable);
  }

  // Clearance is enough to execute normal nudge.
  if (IsClearanceWideEnoughToPass(opposite_side_params, params)) {
    return PreCheckResult(false, pb::XLaneNudgeDebug::kWideEnough);
  }

  return SpecializedScenarioCheck(params);
}

}  // namespace xlane_nudge
}  // namespace planner
