#ifndef ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_LATERAL_BLOCKING_STATE_GENERATOR_SEQUENCE_BLOCKING_STATE_COMPUTER_H_
#define ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_LATERAL_BLOCKING_STATE_GENERATOR_SEQUENCE_BLOCKING_STATE_COMPUTER_H_

#include <utility>
#include <vector>

#include "geometry/model/polygon_with_cache.h"
#include "geometry/model/polyline_curve.h"
#include "planner/behavior/util/agent_state/agent_in_lane_state.h"
#include "planner/path/reasoning/agent_intention/agent_intention_state.h"
#include "planner/path/reasoning/agent_intention/drivable_lane_corridor_generator/drivable_lane_corridor_generator_utility.h"
#include "planner/path/reasoning/agent_intention/object_occupancy_state/object_occupancy_state.h"
#include "planner/path/reasoning/agent_intention/object_occupancy_state/object_stamped_snapshot_info.h"

namespace planner {
namespace path {

// Struct AgentBlockingSequenceInfo stores the blocking sequence type and
// blocking sequences.
struct AgentBlockingSequenceInfo {
  AgentBlockingSequenceInfo() = default;
  explicit AgentBlockingSequenceInfo(
      const LateralBlockingStateMetaData& blocking_state_data)
      : type(blocking_state_data.blocking_sequence_type),
        blocking_sequences(blocking_state_data.blocking_sequences) {}

  // Type of blocking sequence.
  pb::BlockingSequenceType type = pb::BlockingSequenceType::kNonBlocking;
  // BlockingSequence represents a snapshot sequence that share the same
  // blocking state.
  std::vector<pb::BlockingSequence> blocking_sequences;
};

// SequenceBlockingStateComputer is a tool class to compute the blocking states
// of a sequence of agent prediction snapshots in the given drivable space.
class SequenceBlockingStateComputer {
 public:
  // The default required lateral gap in meter for blocking state
  // identification.
  static constexpr double kDefaultRequiredLateralGapInMeter = 0.5;
  // Constructor.
  SequenceBlockingStateComputer() = delete;
  SequenceBlockingStateComputer(
      const DrivableSpaceCorridor& drivable_space_corridor,
      const math::geometry::PolylineCurve2d& nominal_path,
      const math::geometry::PolygonWithCache2d& drivable_space_corridor_contour,
      const EgoInLaneParams& ego_in_lane_params,
      double lateral_clearance_buffer = kDefaultRequiredLateralGapInMeter);

  // Computes the agents effective blocking sequence info for the given agent
  // trajectory represented by 'agent_inlane_state'
  AgentBlockingSequenceInfo Compute(
      const AgentInLaneStates& agent_inlane_state,
      const LateralBlockingStateMetaData& blocking_state_data) const;

  // Computes the agents effective blocking sequence info for the given agent
  // trajectory represented by 'object_occupancy_state'
  AgentBlockingSequenceInfo Compute(
      const ObjectOccupancyState& object_occupancy_state,
      const LateralBlockingStateMetaData& blocking_state_data) const;

  // Gets clearance based blocking state in the drivable space.
  pb::LateralBlockingState::BlockingState
  GetClearanceBasedBlockingStateForSnapshot(
      const math::geometry::PolygonWithCache2d& polygon) const;

 private:
  // SnapshotBlockingState represents a snapshot of the blocking state with its
  // index.
  using SnapshotBlockingState =
      std::pair<int, pb::LateralBlockingState::BlockingState>;

  // Computes and populates the the blocking sequence for the given effective
  // range.
  void ComputeBlockingStateForPosesInRange(
      const AgentTrajectoryInLaneStates& primary_trajectory_state,
      const math::Range<int>& effective_range,
      std::vector<SnapshotBlockingState>* sequence_blocking_states) const;

  // Computes and populates the the blocking sequence for the given effective
  // range (ObjectOccupancyState version).
  void ComputeBlockingStateForPosesInRange(
      const ObjectTrajectoryStates& primary_trajectory_state,
      const math::Range<int>& effective_range,
      std::vector<SnapshotBlockingState>* sequence_blocking_states) const;

  // Converts the given sequence blocking states to the agent blocking sequence
  // info.
  std::vector<pb::BlockingSequence> GetBlockingSequences(
      const std::vector<SnapshotBlockingState>& sequence_blocking_states) const;

  // Reconstructs agent blocking sequence info from the given blocking
  // sequences.
  AgentBlockingSequenceInfo ReconstructBlockingSequenceInfo(
      const std::vector<pb::BlockingSequence>& blocking_sequences) const;

  // Tries to populate cur_sequence into the blocking_sequence with merging with
  // the waiting sequence or the last sequence of the blocking_Sequence. Returns
  // true if the input sequence is populated successfully.
  bool MaybePopulateBlockingSequence(
      const std::vector<pb::BlockingSequence>& raw_blocking_sequences,
      int waiting_seq_idx, const pb::BlockingSequence& cur_sequence,
      std::vector<pb::BlockingSequence>* blocking_sequences) const;

  // Gets blocking sequence type for the given blocking sequences.
  pb::BlockingSequenceType GetBlockingSequenceType(
      const std::vector<pb::BlockingSequence>& blocking_sequences) const;

  //
  // Data members.
  //
  const math::geometry::PolylineCurve2d& left_drivable_space_boundary_;
  const math::geometry::PolylineCurve2d& right_drivable_space_boundary_;
  const math::geometry::PolylineCurve2d& reference_line_;
  const math::geometry::PolygonWithCache2d& drivable_space_polygon_;
  const EgoInLaneParams& ego_in_lane_params_;
  double lateral_clearance_buffer_ = kDefaultRequiredLateralGapInMeter;

  //
  // For Unit Test
  //
  FRIEND_TEST(SequenceBlockingStateComputerTest, TestGetBlockingSequenceInfo);
  FRIEND_TEST(SequenceBlockingStateComputerTest,
              TestGetClearanceBasedBlockingStateForSnapshot);
};

}  // namespace path

}  // namespace planner

#endif  // ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_LATERAL_BLOCKING_STATE_GENERATOR_SEQUENCE_BLOCKING_STATE_COMPUTER_H_
