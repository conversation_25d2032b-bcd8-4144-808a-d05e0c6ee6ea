package(default_visibility = ["//visibility:public"])

cc_library(
    name = "acu_signal_input_main",
    srcs = [
        "acu_signal_input_main.cpp",
    ],
    hdrs = [
        "acu_signal_input_main.h",
    ],
    copts = [
        "-x",
        "c++",
        "-std=c++14",
        "-Wno-missing-field-initializers",
    ],
    include_prefix = "acu_signal_input/",
    deps = [
        "//offboard/fallback_ads/acu_signal_input:acu_signal_input_parameter",
        "//offboard/fallback_ads/acu_signal_input/pose_decode",
        "//offboard/fallback_ads/acu_signal_input/time_sync",
        "//offboard/fallback_ads/acu_signal_input/trajectory_decode",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
    ],
)

cc_library(
    name = "acu_signal_input_parameter",
    srcs = [
        "acu_signal_input_parameter.cpp",
    ],
    hdrs = [
        "acu_signal_input_parameter.h",
    ],
    copts = [
        "-x",
        "c++",
        "-std=c++14",
    ],
    include_prefix = "acu_signal_input/",
)
