/*
 * File: look1_iflf_binlcapw.h
 *
 * Code generated for Simulink model 'LatCtrlFct'.
 *
 * Model version                  : 1.6
 * Simulink Coder version         : 9.5 (R2021a) 14-Nov-2020
 * C/C++ source code generated on : Mon May 30 11:55:40 2022
 */

#ifndef RTW_HEADER_look1_iflf_binlcapw_h_  // NOLINT
#define RTW_HEADER_look1_iflf_binlcapw_h_
#include "common_math_library/rtwtypes.h"

extern float32 look1_iflf_binlcapw(float32 u0, const float32 bp0[],
                                   const float32 table[], uint32 maxIndex);

#endif /* RTW_HEADER_look1_iflf_binlcapw_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
