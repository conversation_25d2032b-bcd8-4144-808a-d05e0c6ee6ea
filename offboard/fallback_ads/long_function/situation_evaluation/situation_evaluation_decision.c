#include "long_function/situation_evaluation/situation_evaluation_decision.h"

#include <math.h>
#include <stdbool.h>
#include <stdint.h>

#include "_out/Appl/GenData/Rte_Type.h"
#include "common_math_library/debounce_method.h"
#include "long_function/long_function_parameter.h"
#include "long_function/long_function_type.h"

bool CheckEmergencyBrake(bool object_position_valid,
                         bool lateral_collision_enable,
                         float collision_probability, float time_to_collision) {
  static bool emergency_brake_enable = false;
  static float emergency_brake_timer = 0.0F;

  bool enable =
      object_position_valid && lateral_collision_enable &&
      (collision_probability > GetSedCollisionProbabilityThreshold()) &&
      (time_to_collision < GetSedTimeToCollisionThreshold());

  CML_TurnOffDelay(enable, GetSedEmergencyBrakeEnableDelayTime(),
                   LGT_CONTROL_TASK_CYCLE_TIME, &emergency_brake_timer,
                   &emergency_brake_enable);

  return emergency_brake_enable;
}

bool CheckObjectFollowingBrake(float time_to_reach_stop_point) {
  static bool object_following_brake_enable = false;
  static float object_following_brake_timer = 0.0F;

  bool enable =
      time_to_reach_stop_point > 1.0F &&
      time_to_reach_stop_point < GetSedTimeToReachStopPointThreshold();
  CML_TurnOffDelay(enable, GetSedObjectFollowingBrakeDelayTime(),
                   LGT_CONTROL_TASK_CYCLE_TIME, &object_following_brake_timer,
                   &object_following_brake_enable);

  return object_following_brake_enable;
}

bool CheckFreeStop(float brake_distance, float ego_velocity,
                   float accel_request_for_free_stop,
                   float accel_request_for_object,
                   float accel_request_for_lane) {
  float brake_distance_threshold =
      CML_Interpolation1D((3.6F * ego_velocity), GetSedFreeStopBrakeDistance(),
                          SED_FREE_STOP_BRAKE_DISTANCE_TABLE_ROWS);

  // distance is enough for free stop
  bool distance_is_enough =
      (brake_distance > brake_distance_threshold) || (ego_velocity < 1.0F);

  // free stop may disable by lane following brake or object following brake
  bool free_stop_not_disable =
      (accel_request_for_free_stop < accel_request_for_object) &&
      (accel_request_for_free_stop < accel_request_for_lane);

  return (distance_is_enough && free_stop_not_disable);
}

bool CheckLaneFollowingBrake(float accel_request) {
  return (accel_request > GetSedAccelThreshold());
}

bool CheckLaneIsTooShort(float ego_velocity, float lane_length,
                         float accel_request) {
  static bool lane_is_too_short = false;
  static float lane_is_too_short_timer = 0.0F;

  float brake_distance_threshold =
      CML_Interpolation1D((3.6 * ego_velocity), GetSedEmergencyBrakeDistance(),
                          SED_EMERGENCY_BRAKE_DISTANCE_TABLE_ROWS);

  // lane length is less than emergency brake distance
  // need excessive deceleration for stop before lane end
  bool enable = (lane_length < brake_distance_threshold) ||
                (accel_request < GetSedAccelThreshold());

  CML_TurnOffDelay(enable, GetSedLaneIsTooShortDelayTime(),
                   LGT_CONTROL_TASK_CYCLE_TIME, &lane_is_too_short_timer,
                   &lane_is_too_short);

  return lane_is_too_short;
}

bool VehicleIsStandstill(float ego_velocity) {
  static bool vehicle_is_standstill = false;
  static float vehicle_is_standstill_timer = 0.0F;

  bool enable = (fabsf(ego_velocity) < 0.1F);
  CML_TurnOnDelay(enable, 0.5F, LGT_CONTROL_TASK_CYCLE_TIME,
                  &vehicle_is_standstill_timer, &vehicle_is_standstill);

  return vehicle_is_standstill;
}

void UpdateStateStandbyExit(bool fallback_active, bool ego_is_standstill,
                            bool collision_enable, bool ego_lane_is_too_short,
                            bool free_stop_enable,
                            bool lane_following_brake_enable,
                            bool object_following_brake_enable,
                            SystemState* system_state_ptr) {
  if (fallback_active) {
    // fallback active state
    if (collision_enable || ego_lane_is_too_short) {
      *system_state_ptr = EMERGENCY_BRAKE;
    } else if (free_stop_enable || ego_is_standstill) {
      *system_state_ptr = FREE_STOP;
    } else {
      // adaptive brake
      if (lane_following_brake_enable && object_following_brake_enable) {
        *system_state_ptr = COMBINE_BRAKE;
      } else if (lane_following_brake_enable) {
        *system_state_ptr = LANE_FOLLOWING_BRAKE;
      } else if (object_following_brake_enable) {
        *system_state_ptr = OBJECT_FOLLOWING_BRAKE;
      } else {
        *system_state_ptr = STANDBY;
      }
    }
  } else {
    *system_state_ptr = STANDBY;
  }
}

void UpdateStateFreeStopExit(bool ego_is_standstill, bool collision_enable,
                             bool ego_lane_is_too_short, bool free_stop_enable,
                             bool lane_following_brake_enable,
                             bool object_following_brake_enable,
                             SystemState* system_state_ptr) {
  // fallback active state
  if (collision_enable || ego_lane_is_too_short) {
    *system_state_ptr = EMERGENCY_BRAKE;
  } else if (free_stop_enable == false && ego_is_standstill == false) {
    if (lane_following_brake_enable && object_following_brake_enable) {
      *system_state_ptr = COMBINE_BRAKE;
    } else if (lane_following_brake_enable) {
      *system_state_ptr = LANE_FOLLOWING_BRAKE;
    } else if (object_following_brake_enable) {
      *system_state_ptr = OBJECT_FOLLOWING_BRAKE;
    } else {
      *system_state_ptr = FREE_STOP;
    }
  } else {
    *system_state_ptr = FREE_STOP;
  }
}

void UpdateStateAdaptiveBrakeExit(bool ego_is_standstill, bool collision_enable,
                                  bool ego_lane_is_too_short,
                                  bool free_stop_enable,
                                  bool lane_following_brake_enable,
                                  bool object_following_brake_enable,
                                  SystemState* system_state_ptr) {
  // fallback active state
  if (collision_enable || ego_lane_is_too_short) {
    *system_state_ptr = EMERGENCY_BRAKE;
  } else if (free_stop_enable || ego_is_standstill) {
    *system_state_ptr = FREE_STOP;
  } else {
    if (lane_following_brake_enable && object_following_brake_enable) {
      *system_state_ptr = COMBINE_BRAKE;
    } else if (lane_following_brake_enable) {
      *system_state_ptr = LANE_FOLLOWING_BRAKE;
    } else if (object_following_brake_enable) {
      *system_state_ptr = OBJECT_FOLLOWING_BRAKE;
    } else {
      // the system status remains unchanged
    }
  }
}

SystemState UpdateSystemState(bool fallback_active, bool ego_is_standstill,
                              bool collision_enable, bool ego_lane_is_too_short,
                              bool object_following_brake_enable,
                              bool lane_following_brake_enable,
                              bool free_stop_enable, float free_stop_accel,
                              float lane_following_brake_accel,
                              float object_following_brake_accel,
                              float* accel_request, float* jerk_request) {
  static SystemState system_state = STANDBY;

  if (system_state == STANDBY) {
    *accel_request = -1.0F;
    *jerk_request = -3.0F;
    // update system state when standby exit
    UpdateStateStandbyExit(fallback_active, ego_is_standstill, collision_enable,
                           ego_lane_is_too_short, free_stop_enable,
                           lane_following_brake_enable,
                           object_following_brake_enable, &system_state);
  } else {
    // fallback active state
    if (system_state == EMERGENCY_BRAKE) {
      *accel_request = -10.0F;
      *jerk_request = -20.0F;
      // check emergency brake exit
      system_state = ego_is_standstill ? FREE_STOP : EMERGENCY_BRAKE;
    } else if (system_state == FREE_STOP) {
      *accel_request = free_stop_accel;
      *jerk_request = -3.0F;
      // check free stop exit
      UpdateStateFreeStopExit(ego_is_standstill, collision_enable,
                              ego_lane_is_too_short, free_stop_enable,
                              lane_following_brake_enable,
                              object_following_brake_enable, &system_state);
    } else {
      // adaptive brake
      if (system_state == COMBINE_BRAKE) {
        *accel_request =
            fminf(object_following_brake_accel, lane_following_brake_accel);
      } else if (system_state == OBJECT_FOLLOWING_BRAKE) {
        *accel_request = object_following_brake_accel;
      } else {
        // lane following brake
        *accel_request = lane_following_brake_accel;
      }
      *jerk_request = -4.0F;
      // check adaptive brake exit
      UpdateStateAdaptiveBrakeExit(
          ego_is_standstill, collision_enable, ego_lane_is_too_short,
          free_stop_enable, lane_following_brake_enable,
          object_following_brake_enable, &system_state);
    }
    // check fallback active exit
    system_state = fallback_active ? system_state : STANDBY;
  }

  return system_state;
}

SystemState SituationEvaluationDecision(
    const VSI_VehicleInfo_Struct* vehicle_info_ptr,
    const CSI_LaneInfo_Struct* lane_info_ptr,
    const VSP_VehicleSignal_Struct* vehicle_signal_ptr,
    const FSC_FreeStopControl_Struct* free_stop_control_ptr,
    const CDS_CollisionDetection_Struct* collision_detection_ptr,
    float* accel_request_ptr, float* jerk_request_ptr) {
  // emergency brake enable by object collision
  bool emergency_brake_enable =
      CheckEmergencyBrake(collision_detection_ptr->CDS_ObjectPositionValid,
                          collision_detection_ptr->CDS_CollisionEnableByDy,
                          collision_detection_ptr->CDS_CollisionProbability,
                          collision_detection_ptr->CDS_TimeToCollision);

  // emergency brake enable when ego lane is too short
  float lane_length =
      fminf(lane_info_ptr->CSI_PosXEndLf, lane_info_ptr->CSI_PosXEndRi);
  bool lane_is_too_short =
      CheckLaneIsTooShort(vehicle_signal_ptr->VSP_LgtVehSpdFild, lane_length,
                          free_stop_control_ptr->FSC_AccRequestByLaneLength);

  // object following enable
  bool object_following_brake_enable = CheckObjectFollowingBrake(
      collision_detection_ptr->CDS_TimeToReachStopPoint);

  // lane following brake enable
  bool lane_following_brake_enable = CheckLaneFollowingBrake(
      free_stop_control_ptr->FSC_AccRequestByLaneLength);

  // free stop enable
  bool free_stop_enable =
      CheckFreeStop(lane_length, vehicle_signal_ptr->VSP_LgtVehSpdFild,
                    free_stop_control_ptr->FSC_FreeStopAccRequest,
                    collision_detection_ptr->CDS_LongNecAcc,
                    free_stop_control_ptr->FSC_AccRequestByLaneLength);

  // ego vehicle is standstill
  bool ego_is_standstill =
      VehicleIsStandstill(vehicle_signal_ptr->VSP_LgtVehSpdFild);

  // update system state
  SystemState system_state = UpdateSystemState(
      vehicle_info_ptr->VSI_FallbackActive, ego_is_standstill,
      emergency_brake_enable, lane_is_too_short, object_following_brake_enable,
      lane_following_brake_enable, free_stop_enable,
      free_stop_control_ptr->FSC_FreeStopAccRequest,
      free_stop_control_ptr->FSC_AccRequestByLaneLength,
      collision_detection_ptr->CDS_LongNecAcc, accel_request_ptr,
      jerk_request_ptr);

  return system_state;
}
