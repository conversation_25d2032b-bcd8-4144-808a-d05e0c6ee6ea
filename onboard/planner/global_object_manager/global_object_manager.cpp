#include "planner/global_object_manager/global_object_manager.h"

#include <memory>
#include <utility>

#include "planner/global_object_manager/objects_evaluation_utility/objects_evaluation_utility.h"
#include "planner/global_object_manager/planner_object_update_utility/planner_object_update_utility.h"
#include "planner/global_object_manager/prediction_trajectory_update_utility/prediction_trajectory_update_utility.h"
#include "planner/global_object_manager/utility/rtree_utility.h"
#include "planner/planning_gflags.h"
#include "rt_event/rt_event.h"
#include "trace/trace.h"
#include "voy_rt_event/rt_event_planner.h"
#include "voy_trace/trace_planner.h"

namespace planner {
namespace {
// The max size of the planner objects history
constexpr int kMaxPlannerObjectsHistorySize = 30;

// Post Off-Road Events
void PostOffRoadEvents(bool has_path_recently_go_offroad,
                       bool has_off_road_objects,
                       const pb::PlannerObjectDebug* planner_object_debug) {
  if (has_path_recently_go_offroad && has_off_road_objects) {
    rt_event::PostRtEvent<rt_event::planner::EgoTrajectoryOffRoad>();
  }
  if (planner_object_debug && !planner_object_debug->off_road_object_debug()
                                   .ignored_offroad_objects()
                                   .empty()) {
    rt_event::PostRtEvent<rt_event::planner::FilterOutOffRoadObjects>();
  }

  if (has_off_road_objects) {
    rt_event::PostRtEvent<rt_event::planner::HasOffRoadObjects>();
  }
}

}  // namespace

GlobalObjectManager::GlobalObjectManager(
    pb::RequiredLateralGapConfig required_lateral_gap_config,
    pb::PlannerObjectConfig planner_object_config)
    : planner_object_config_(std::move(planner_object_config)),
      required_lateral_gap_config_(std::move(required_lateral_gap_config)),
      base_behavior_relevant_objects_info_(
          std::make_unique<BehaviorRelevantObjectsInfo>()),
      planner_object_map_ptr_(std::make_shared<PlannerObjectMap>()),
      planner_objects_history_(kMaxPlannerObjectsHistorySize),
      overtaking_object_evaluator_(
          planner_object_config_.overtaking_evaulator_config()),
      destruction_background_pool_(std::make_unique<av_comm::ThreadPool>(3)) {}

void GlobalObjectManager::Update(
    int64_t snapshot_timestamp, const prediction::pb::AgentList& agent_list,
    const pb::EgoOffroadInfoSeed& ego_off_road_seed,
    const assist::PerceptionFPObstacleTracker& perception_fpobstacle_tracker,
    double distance_to_destination_m, const RobotState& robot_state,
    const pnc_map::PncMapService& pnc_map_service,
    const ObjectTypeTrackingMap& object_type_tracking_map,
    const std::vector<const pnc_map::Lane*>&
        tracked_object_perception_range_lanes,
    const std::optional<UnstuckDirectives>& unstuck_directives,
    const AssistInstruction* assist_instruction,
    pb::PlannerObjectDebug* planner_object_debug) {
  TRACE_EVENT_SCOPE(planner, GlobalObjectManager_Update);

  // step 1: collect irrelevant object ids
  auto objects_evaluation_result = utility::ComputeObjectsEvaluationResult(
      snapshot_timestamp, agent_list, ego_off_road_seed,
      perception_fpobstacle_tracker, distance_to_destination_m,
      planner_object_config_, planner_object_debug);

  // step 2: update planner objects
  UpdatePlannerObjects(snapshot_timestamp, agent_list, robot_state,
                       pnc_map_service, object_type_tracking_map,
                       tracked_object_perception_range_lanes,
                       objects_evaluation_result, unstuck_directives,
                       assist_instruction, planner_object_debug);

  // populate debug data
  if (planner_object_debug) {
    const auto plan_init_timestamp =
        robot_state.plan_init_state_snapshot().timestamp();
    const auto prediction_timestamp = agent_list.timestamp();

    planner_object_debug->set_plan_init_timestamp_ms(plan_init_timestamp);
    planner_object_debug->set_perception_object_timestamp_ms(
        prediction_timestamp);
    planner_object_debug->set_perception_object_plan_init_lag_time_ms(
        plan_init_timestamp - prediction_timestamp);
    planner_object_debug->mutable_off_road_object_debug()
        ->set_last_off_road_path_timestamp(
            ego_off_road_seed.off_road_timestamp());
  }

  PostOffRoadEvents(objects_evaluation_result.has_path_recently_go_offroad,
                    objects_evaluation_result.has_off_road_objects,
                    planner_object_debug);
}

void GlobalObjectManager::UpdatePlannerObjects(
    int64_t snapshot_timestamp, const prediction::pb::AgentList& agent_list,
    const RobotState& robot_state,
    const pnc_map::PncMapService& pnc_map_service,
    const ObjectTypeTrackingMap& object_type_tracking_map,
    const std::vector<const pnc_map::Lane*>&
        tracked_object_perception_range_lanes,
    const ObjectsEvaluationResult& objects_evaluation_result,
    const std::optional<UnstuckDirectives>& unstuck_directives,
    const AssistInstruction* assist_instruction,
    pb::PlannerObjectDebug* planner_object_debug) {
  TRACE_EVENT_SCOPE(planner, GlobalObjectManager_UpdatePlannerObjects);

  utility::UpdatePredictionTrajectoryMap(
      snapshot_timestamp, agent_list,
      objects_evaluation_result.irrelevant_object_ids, &object_prediction_map_,
      &object_conditional_prediction_map_, destruction_background_pool_.get());

  // add planner_object_map to history
  planner_objects_history_.Update(planner_object_map_ptr_,
                                  destruction_background_pool_);
  planner_object_map_ptr_ = std::make_shared<PlannerObjectMap>();

  utility::UpdatePlannerObjectMap(
      agent_list, objects_evaluation_result.stationary_object_ids,
      objects_evaluation_result.irrelevant_object_ids, robot_state,
      pnc_map_service, object_type_tracking_map,
      tracked_object_perception_range_lanes, object_prediction_map_,
      planner_objects_history_, planner_object_map_ptr_.get(),
      planner_object_debug);

  utility::UpdateOvertakingInfo(robot_state, &overtaking_object_evaluator_,
                                planner_object_map_ptr_.get());

  utility::UpdatePlannerObjectsWithAssistInstruction(
      unstuck_directives, assist_instruction, &construction_zones_,
      planner_object_map_ptr_.get());

  is_high_agent_density_ =
      utility::IsHighAgentDensityScenario(*planner_object_map_ptr_);

  is_high_crossing_vru_density_ = utility::IsHighCrossingVruDensityScenario(
      *planner_object_map_ptr_, robot_state);

  // Populate debug.
  if (planner_object_debug != nullptr) {
    planner_object_debug->set_is_high_agent_density(is_high_agent_density_);
    planner_object_debug->set_is_high_crossing_vru_density(
        is_high_crossing_vru_density_);
  }

  if (FLAGS_planning_enable_planner_objects_spatial_indexing) {
    planner_object_spatial_index_ =
        std::make_unique<utility::PlannerObjectGeometryRTree>(
            *planner_object_map_ptr_);
  }
}

void GlobalObjectManager::UpdateIsHighAgentDensityScenario() {
  is_high_agent_density_ =
      utility::IsHighAgentDensityScenario(*planner_object_map_ptr_);
}

void GlobalObjectManager::UpdateBaseBehaviorRelevantObjectsInfo() {
  UpdateRequiredLateralGapInfo();

  DCHECK(base_behavior_relevant_objects_info_ != nullptr)
      << "base_behavior_relevant_objects_info_ is nullptr!";

  auto& relevant_objects =
      base_behavior_relevant_objects_info_->mutable_relevant_objects();

  auto& relevant_construction_zones =
      base_behavior_relevant_objects_info_
          ->mutable_relevant_construction_zones();

  relevant_objects.clear();
  relevant_construction_zones.clear();

  for (const auto& [object_id, planner_object] : *planner_object_map_ptr_) {
    TypedObjectId typed_object_id(object_id,
                                  pb::ObjectSourceType::kTrackedObject);
    relevant_objects.try_emplace(
        typed_object_id, pb::ObjectInformation::FULLY_CONSIDER,
        required_lateral_gap_info_ == nullptr
            ? nullptr
            : &(required_lateral_gap_info_->object_id_to_required_lateral_gaps
                    .at(typed_object_id.id)),
        std::nullopt);
  }
  for (const auto& [cz_id, cz] : construction_zone_map()) {
    TypedObjectId typed_object_id(cz_id,
                                  pb::ObjectSourceType::kConstructionZone);
    relevant_construction_zones.try_emplace(
        typed_object_id, pb::ObjectInformation::FULLY_CONSIDER,
        required_lateral_gap_info_ == nullptr
            ? nullptr
            : &(required_lateral_gap_info_->cz_id_to_required_lateral_gaps.at(
                  typed_object_id.id)),
        std::nullopt);
  }
}

void GlobalObjectManager::UpdateRequiredLateralGapInfo() {
  // Compute required lateral gaps for objects and construction zones
  auto obj_id_to_required_lat_gaps =
      ComputePrincipledRequiredLateralGapForPlannerObjects(
          *planner_object_map_ptr_, required_lateral_gap_config_);
  auto cz_id_to_required_lat_gaps =
      ComputePrincipledRequiredLateralGapForConstructionZones(
          construction_zones_, required_lateral_gap_config_);
  auto hard_boundary_required_lat_gap =
      GetRequiredLateralGapForHardBoundaries(required_lateral_gap_config_);

  // Assign computed maps into RequiredLateralGapInfo
  required_lateral_gap_info_ = std::make_unique<RequiredLateralGapInfo>(
      std::move(obj_id_to_required_lat_gaps),
      std::move(cz_id_to_required_lat_gaps),
      std::move(hard_boundary_required_lat_gap));
}

std::vector<const PlannerObject*>
GlobalObjectManager::GetPlannerObjectsIntersectWithPolyline(
    const math::geometry::PolylineCurve2d& query_line,
    const std::function<bool(const PlannerObject&)>&
        unrelated_objects_filter_fn) const {
  // planner_object_spatial_index_ == nullptr if
  // FLAGS_planning_enable_planner_objects_spatial_indexing is false
  // in this case, use normal BruteForce approach
  return planner_object_spatial_index_ == nullptr
             ? utility::QueryIntersectingObjectsWithLineBruteForce(
                   *planner_object_map_ptr_, query_line,
                   unrelated_objects_filter_fn)
             : utility::QueryIntersectingObjectsWithLine(
                   *planner_object_spatial_index_, query_line,
                   unrelated_objects_filter_fn);
}
}  // namespace planner
