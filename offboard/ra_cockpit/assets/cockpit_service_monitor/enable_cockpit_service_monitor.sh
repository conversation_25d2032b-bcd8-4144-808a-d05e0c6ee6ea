#!/bin/bash

LOGFILE="/opt/RA/log/monitor.log"
PYTHON_PATH=$(which python3)
SCRIPT_PATH="/opt/RA/videos/script/cockpit_service_monitor.py"

cd /opt/RA/videos/script

echo "$(date): Waiting for user login..." >> "$LOGFILE"

while true; do
  USER=$(loginctl list-sessions --no-legend | awk '$3 != "gdm" {print $3; exit}')
  if [ -n "$USER" ]; then
    echo "$(date): Detected logged-in user: $USER" >> "$LOGFILE"
    break
  fi
  sleep 2
done

echo "$(date): Executing cockpit_service_monitor.py" >> "$LOGFILE"
$PYTHON_PATH cockpit_service_monitor.py >> "$LOGFILE" 2>&1
