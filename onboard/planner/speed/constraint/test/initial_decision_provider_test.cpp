#include "planner/speed/constraint/initial_decision_provider.h"

#include <limits>
#include <optional>
#include <utility>
#include <vector>

#include <absl/container/flat_hash_map.h>
#include <gtest/gtest.h>

#include "planner/speed/constraint/constraint.h"
#include "planner/speed/constraint/constraint_creator.h"
#include "planner/speed/constraint/decision.h"
#include "planner/speed/constraint/gap_align_info.h"
#include "planner/speed/discomforts/discomfort_varying.h"
#include "planner/speed/discomforts/discomfort_varying_limits.h"
#include "planner/speed/discomforts/discomforts.h"
#include "planner/speed/solver/searcher/simple_profile_searcher.h"
#include "planner/speed/test_util/test_util.h"
#include "planner/utility/config_center/planner_config_center.h"
#include "planner/world_model/planner_object/planner_object.h"
#include "planner/world_model/traffic_participant/traffic_participant_pose.h"
#include "planner_protos/behavior_reasoner_config.pb.h"
#include "planner_protos/speed_decision.pb.h"

namespace planner {
namespace speed {
namespace {

// Planning horizon params.
constexpr double kDt = 0.1;
constexpr double kRaToFb = 4.0;
constexpr double kRaToRb = 1.0;
constexpr int kSteps = 80;
constexpr double kPoseTime = 1.0;
// Params to build bp and overlap.
constexpr double kSpeed = 10.0;
constexpr double kRefSpeed = 16.67;
constexpr double kPredictedStartTimeInSec = kPoseTime - 0.5;
constexpr double kGapAlignMaxActiveTime = 5.0;
constexpr double kGapAlignMaxDiscomfort = 1.0;
// The max allowed amount of extra time of pass active time more than yield
// active time. It indicates the extra effort we can afford to pass a gap align
// constraint to gain more progress than to yield a gap align constraint. The
// larger the value is, the more progress we will prefer but in the meantime we
// are being aggressive to pass.
constexpr double kMaxAllowedExtraTimeForPassInSecond = 1.0;

TEST(InitialProfileUniqueIdentifierTest, IdentifierUniqueness) {
  Constraint constraint1(ConstraintType::Constraint_Type_SPEED_OBJECT,
                         pb::FenceType::kCrossWalkVRU, "x-reasoner", "c1");
  constraint1.settings.pass_max_a = DiscomfortVarying(2.0);
  constraint1.settings.pass_max_v =
      DiscomfortVarying(std::numeric_limits<double>::infinity());
  constraint1.settings.pass_max_j = DiscomfortVarying(5.0);
  constraint1.settings.yield_min_a = DiscomfortVarying(-1.0);
  constraint1.settings.yield_min_v = DiscomfortVarying(0.0);

  InitialProfileUniqueIdentifier c_1_at_discomfort_0_pass(constraint1.settings,
                                                          /*discomfort_ix=*/0,
                                                          /*for_pass=*/true);
  InitialProfileUniqueIdentifier c_1_at_discomfort_0_yield(constraint1.settings,
                                                           /*discomfort_ix=*/0,
                                                           /*for_pass=*/false);
  InitialProfileUniqueIdentifier c_1_at_discomfort_1_pass(constraint1.settings,
                                                          /*discomfort_ix=*/5,
                                                          /*for_pass=*/true);
  InitialProfileUniqueIdentifier c_1_at_discomfort_0_pass_equal(
      constraint1.settings,
      /*discomfort_ix=*/0, /*for_pass=*/true);
  EXPECT_TRUE(c_1_at_discomfort_0_pass == c_1_at_discomfort_0_pass_equal);
  EXPECT_FALSE(c_1_at_discomfort_0_pass == c_1_at_discomfort_0_yield);
  EXPECT_FALSE(c_1_at_discomfort_0_pass == c_1_at_discomfort_1_pass);

  Constraint constraint2 = constraint1;
  constraint2.settings.yield_min_a =
      DiscomfortVarying(-std::numeric_limits<double>::infinity());
  InitialProfileUniqueIdentifier c_2_at_discomfort_0_pass(constraint2.settings,
                                                          /*discomfort_ix=*/0,
                                                          /*for_pass=*/true);
  InitialProfileUniqueIdentifier c_2_at_discomfort_0_yield(constraint2.settings,
                                                           /*discomfort_ix=*/0,
                                                           /*for_pass=*/false);
  InitialProfileUniqueIdentifier c_2_at_discomfort_1_pass(constraint2.settings,
                                                          /*discomfort_ix=*/5,
                                                          /*for_pass=*/true);
  InitialProfileUniqueIdentifier c_2_at_discomfort_0_pass_equal(
      constraint2.settings,
      /*discomfort_ix=*/0, /*for_pass=*/true);
  EXPECT_TRUE(c_2_at_discomfort_0_pass == c_2_at_discomfort_0_pass_equal);
  EXPECT_FALSE(c_2_at_discomfort_0_pass == c_2_at_discomfort_0_yield);
  EXPECT_FALSE(c_2_at_discomfort_0_pass == c_2_at_discomfort_1_pass);

  // Constraint1 is the same as constraint2 at the pass side, but different at
  // the yield side.
  EXPECT_TRUE(c_1_at_discomfort_0_pass == c_2_at_discomfort_0_pass);
  EXPECT_FALSE(c_1_at_discomfort_0_yield == c_2_at_discomfort_0_yield);
}

class InitialDecisionProviderTest : public ::testing::Test {
 protected:
  InitialDecisionProviderTest()
      : config_(PlannerConfigCenter::GetInstance()
                    .GetDecoupledForwardManeuverConfig()),
        bp_(CreateStraightPredictedTrajectory(
            /*x_start = */ 0.0, /*y_start = */ 0.0, /*heading = */ 0.0,
            /*v = */ kSpeed,
            /*time_in_sec = */ kPredictedStartTimeInSec,
            /*num_step = */ 50)),
        bp_wrapper_(voy::TrackedObject(), bp_),
        min_range_(DiscomfortVaryingMinRange(
            config_.speed_generator_config().for_car_type().min_range())),
        min_range_max_lat_gap_(config_.speed_generator_config()
                                   .for_search()
                                   .min_range_max_required_lat_gap()) {
    all_limits_.emplace(
        config_.speed_generator_config().for_car_type().limits());
    reference_generator_.emplace(CreateReferenceGeneratorWithInitialState(
        ego_speed_, /*init_accel=*/0.0, /*init_jerk=*/0.0,
        /*min_speed=*/kRefSpeed,
        /*max_speed=*/kRefSpeed, kDt, kSteps, kPoseTime,
        config_.speed_generator_config().for_car_type().limits(),
        config_.speed_generator_config().reference()));
    prediction_id_to_trajectory_ptrs_.emplace("obj-1-pred-0", &bp_wrapper_);
    // A overlap region created from constant-speed for test, corresponding
    // to the above test predicted trajectory. Start_time is w.r.t. ego pose
    // time. First_padded_ix is the first predicted pose appearing on ego
    // s-t.
    CreateOverlapRegionFromConstSpeed(
        /*obj_id=*/1, /*traj_id=*/0, /*region_id=*/0,
        /*first_padded_ix=*/1,
        /*last_padded_ix=*/49,
        /*start_time=*/kPredictedStartTimeInSec - kPoseTime,
        /*dt=*/0.1,
        /*start_pos=*/0.0, /*move_along_speed=*/kSpeed,
        /*agent_length=*/3.5, /*lateral_gap=*/0.1, kRaToFb, kRaToRb,
        &overlap_region_);
    overlap_region_.set_padded_boundary_req_lat_gap(2.0);
    overlap_id_to_region_ptrs_.emplace("obj-1-pred-0-region-0",
                                       &overlap_region_);
    auto& tracked_object = *agent_proto_.mutable_tracked_object();
    tracked_object.set_acceleration(0.0);
    object_map_.emplace(
        1, /*dummy_planner_object=*/PlannerObject(
            agent_proto_,
            static_cast<int64_t>(kPredictedStartTimeInSec - kPoseTime),
            TrafficParticipantPose()));
    // Construct a dummy path for overlap.
    agent_reaction_calculator_.emplace(prediction_id_to_trajectory_ptrs_,
                                       overlap_id_to_region_ptrs_, object_map_,
                                       CreateDummyPathForOverlap(), kPoseTime,
                                       /*horizon_in_sec=*/kDt * kSteps);
    initial_decision_provider_.emplace(
        all_limits_.value(), kDt, kSteps,
        reference_generator_.value().immutable_times(), kRaToFb, kRaToRb,
        kGapAlignMaxActiveTime, kGapAlignMaxActiveTime, kGapAlignMaxDiscomfort,
        reference_generator_.value(), agent_reaction_calculator_.value(),
        min_range_, min_range_max_lat_gap_);
  }

  const std::array<double, Discomforts::kLevels>& immutable_times() const {
    DCHECK(reference_generator_.has_value());
    return reference_generator_.value().immutable_times();
  }

  void UpdateIdpReferenceGenerator(const double ego_speed,
                                   const double init_accel,
                                   const double min_speed,
                                   const double max_speed) {
    reference_generator_.emplace(CreateReferenceGeneratorWithInitialState(
        ego_speed, init_accel, /*init_jerk=*/0.0, min_speed, max_speed, kDt,
        kSteps, kPoseTime,
        config_.speed_generator_config().for_car_type().limits(),
        config_.speed_generator_config().reference()));
    initial_decision_provider_.emplace(
        all_limits_.value(), kDt, kSteps,
        reference_generator_.value().immutable_times(), kRaToFb, kRaToRb,
        kGapAlignMaxActiveTime, kGapAlignMaxActiveTime, kGapAlignMaxDiscomfort,
        reference_generator_.value(), agent_reaction_calculator_.value(),
        min_range_, min_range_max_lat_gap_);
  }

  const double dt_ = kDt;
  const double ra_to_fb_ = kRaToFb;
  const double ra_to_rb_ = kRaToRb;
  const int steps_ = kSteps;
  const double ego_speed_ = kSpeed;
  const double gap_align_pass_max_active_time_ = kGapAlignMaxActiveTime;
  const double gap_align_yield_max_active_time_ = kGapAlignMaxActiveTime;
  const double gap_align_max_discomfort_ = kGapAlignMaxDiscomfort;
  planner::pb::DecoupledForwardManeuverConfig config_;
  std::optional<DiscomfortVaryingLimits> all_limits_;
  std::optional<ReferenceGenerator> reference_generator_;
  std::optional<AgentReactionCalculator> agent_reaction_calculator_;
  std::optional<InitialDecisionProvider> initial_decision_provider_;
  prediction::pb::PredictedTrajectory bp_;
  planner::PredictedTrajectoryWrapper bp_wrapper_;
  std::unordered_map<std::string, const planner::PredictedTrajectoryWrapper*>
      prediction_id_to_trajectory_ptrs_;
  pb::OverlapRegion overlap_region_;
  std::unordered_map<std::string, const pb::OverlapRegion*>
      overlap_id_to_region_ptrs_;
  prediction::pb::Agent agent_proto_;
  std::unordered_map<ObjectId, PlannerObject> object_map_;
  absl::flat_hash_map<int, GapAlignInfo> mutable_gap_align_info_map_;
  bool update_initial_decision_success_ = true;
  const DiscomfortVaryingMinRange min_range_;
  const double min_range_max_lat_gap_;
};

TEST_F(InitialDecisionProviderTest, GetInitialDecisionsNa) {
  Constraint avoid_region1(ConstraintType::Constraint_Type_AVOID_REGION,
                           /*fence_type=*/pb::FenceType::kCrossWalkVRU,
                           "reasoner_yield", "c-avoid-region");
  std::vector<Constraint> constraints(1, avoid_region1);
  std::vector<Decision> decisions;
  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions,
      mutable_gap_align_info_map_);
  // By default both pass and yield options are HARD, so initial decision is
  // NOT_DECIDED.
  EXPECT_EQ(decisions.size(), 1);
  EXPECT_EQ(decisions.front(), pb::NOT_DECIDED);
}

TEST_F(InitialDecisionProviderTest, GetInitialDecisionsPassOnly) {
  Constraint avoid_region1(ConstraintType::Constraint_Type_AVOID_REGION,
                           /*fence_type=*/pb::FenceType::kCrossWalkVRU,
                           "reasoner_yield", "c-avoid-region");
  avoid_region1.settings.yield_option =
      pb::ConstraintSettings_PassYieldOption_NA;
  std::vector<Constraint> constraints(1, avoid_region1);
  std::vector<Decision> decisions;
  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions,
      mutable_gap_align_info_map_);
  // Yield is not allowed, so can only PASS.
  EXPECT_EQ(decisions.size(), 1);
  EXPECT_EQ(decisions.front(), pb::PASS);
}

TEST_F(InitialDecisionProviderTest, GetInitialDecisionsYieldOnly) {
  Constraint avoid_region1(ConstraintType::Constraint_Type_AVOID_REGION,
                           /*fence_type=*/pb::FenceType::kCrossWalkVRU,
                           "reasoner_yield", "c-avoid-region");
  avoid_region1.settings.pass_option =
      pb::ConstraintSettings_PassYieldOption_NA;
  std::vector<Constraint> constraints(1, avoid_region1);
  std::vector<Decision> decisions;
  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions,
      mutable_gap_align_info_map_);
  // Pass is not allowed so can only yield.
  EXPECT_EQ(decisions.size(), 1);
  EXPECT_EQ(decisions.front(), pb::YIELD);
}

TEST_F(InitialDecisionProviderTest, GetInitialDecisionsIgnore) {
  Constraint avoid_region1(ConstraintType::Constraint_Type_AVOID_REGION,
                           /*fence_type=*/pb::FenceType::kCrossWalkVRU,
                           "reasoner_yield", "c-avoid-region");
  avoid_region1.settings.pass_option =
      pb::ConstraintSettings_PassYieldOption_NA;
  avoid_region1.settings.yield_option =
      pb::ConstraintSettings_PassYieldOption_NA;
  std::vector<Constraint> constraints(1, avoid_region1);
  std::vector<Decision> decisions;
  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions,
      mutable_gap_align_info_map_);
  // Both pass and yield are not allowed, so can only ignore.
  EXPECT_EQ(decisions.size(), 1);
  EXPECT_EQ(decisions.front(), pb::IGNORE);
}

TEST_F(InitialDecisionProviderTest, NoPassIgnoreGapAlignIgnored) {
  std::vector<Constraint> constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_, &constraints);
  ConstraintMutator constraint_mutator =
      constraint_creator.AddGapAlignSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/-100.0,
          /*longitudinal_speed=*/ego_speed_,
          /*dt=*/0.1, /*num_states=*/80, /*agent_length=*/200.0,
          pb::FenceType::kGapAlign, pb::ReasonerId::GAP_ALIGN,
          /*constraint_id=*/"obj-1-const-speed-gap-align",
          /*obj_id=*/1, /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/true);
  constraint_mutator.set_allow_pass_ignore(false);
  constraint_mutator.set_yield_if_possible_with_min_speed_accel(
      /*yield_min_v=*/5.0, /*yield_min_a=*/-1.0);
  std::vector<Decision> decisions;
  DCHECK(initial_decision_provider_.has_value());
  const bool get_initial_decision_success =
      initial_decision_provider_->GetInitialDecisions(
          /*cur_discomfort=*/0.0, /*elevated_discomfort=*/0.0,
          /*for_gap_align=*/true,
          /*for_rm=*/false,
          /*constraint_indices_to_explore_yield=*/{}, constraints, decisions,
          mutable_gap_align_info_map_);
  // The gap align constraint is extremely large in longitudinal span so it is
  // impossible to pass or yield. We are also not allowed to ignore it.
  // So we fail to get an initial decision.
  EXPECT_TRUE(IsNoPassIgnoreGapAlignConstraint(constraints[0], 0.0));
  EXPECT_FALSE(get_initial_decision_success);
}

TEST_F(InitialDecisionProviderTest, GetInitialDecisionsSoftPass) {
  std::vector<Constraint> mutable_constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_,
                                       &mutable_constraints);
  constexpr double kStartTime = 0.0;
  constexpr double kEndTime = std::numeric_limits<double>::infinity();
  constexpr double kLowStartX = 0.0;
  constexpr double kEndX = 100.0;
  ConstraintMutator constraint_mutator = constraint_creator.AddAvoidRegion(
      kStartTime, kEndTime, kLowStartX, kEndX,
      /*fence_type=*/pb::FenceType::kCrossWalkVRU, pb::ReasonerId::VRU,
      "c-avoid-region");
  EXPECT_FALSE(mutable_constraints.front().is_modified_by_soft);
  constraint_mutator.set_soft_pass_with_max_a(1.0);
  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->ReshapeConstraints(mutable_constraints);
  std::vector<Decision> decisions;
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, mutable_constraints,
      decisions, mutable_gap_align_info_map_);
  EXPECT_EQ(decisions.front(), pb::NOT_DECIDED);
  // Constraint states have been modified.
  EXPECT_LT(mutable_constraints.front().states.front().end_x(Discomforts::kMin),
            kEndX);
  EXPECT_TRUE(mutable_constraints.front().is_modified_by_soft);
}

// The initial decision provider should still work with bad initial
// state, which could be modified based on the discomfort level.
TEST_F(InitialDecisionProviderTest, SoftPassWithHighDiscomfortInitState) {
  std::vector<Constraint> mutable_constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_,
                                       &mutable_constraints);
  constexpr double kStartTime = 0.0;
  constexpr double kEndTime = std::numeric_limits<double>::infinity();
  constexpr double kLowStartX = 0.0;
  constexpr double kEndX = 100.0;
  constraint_creator
      .AddAvoidRegion(kStartTime, kEndTime, kLowStartX, kEndX,
                      /*fence_type=*/pb::FenceType::kCrossWalkVRU,
                      pb::ReasonerId::VRU, "c-avoid-region")
      .set_soft_pass_with_max_a(2.0);

  ReferenceGenerator bad_init_state_ref =
      CreateReferenceGeneratorWithInitialState(
          /*init_speed=*/1.0, /*init_accel=*/-2.7, /*init_jerk=*/9.5,
          /*min_speed=*/10.0,
          /*max_speed=*/12.0, dt_, steps_, /*pose_time=*/10.0,
          config_.speed_generator_config().for_car_type().limits(),
          config_.speed_generator_config().reference(),
          /*update_plan_init_state=*/true);
  DCHECK(all_limits_.has_value());
  DCHECK(agent_reaction_calculator_.has_value());
  InitialDecisionProvider bad_init_state_init_decider(
      all_limits_.value(), dt_, steps_, immutable_times(), ra_to_fb_, ra_to_rb_,
      gap_align_pass_max_active_time_, gap_align_yield_max_active_time_,
      gap_align_max_discomfort_, bad_init_state_ref,
      agent_reaction_calculator_.value(), min_range_, min_range_max_lat_gap_);
  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->ReshapeConstraints(mutable_constraints);
  std::vector<Decision> decisions;
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, mutable_constraints,
      decisions, mutable_gap_align_info_map_);
  EXPECT_EQ(decisions.front(), pb::NOT_DECIDED);
  // Constraint states have been modified.
  EXPECT_LT(mutable_constraints.front().states.front().end_x(Discomforts::kMin),
            kEndX);
}

TEST_F(InitialDecisionProviderTest, GetInitialDecisionsSoftYield) {
  std::vector<Constraint> mutable_constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_,
                                       &mutable_constraints);
  constexpr double kStartTime = 0.0;
  constexpr double kEndTime = std::numeric_limits<double>::infinity();
  constexpr double kLowStartX = 0.0;
  constexpr double kEndX = 0.0;

  ConstraintMutator constraint_mutator = constraint_creator.AddAvoidRegion(
      kStartTime, kEndTime, kLowStartX, kEndX,
      /*fence_type=*/pb::FenceType::kCrossWalkVRU, pb::ReasonerId::VRU,
      "c-avoid-region");
  EXPECT_FALSE(mutable_constraints.front().is_modified_by_soft);
  constraint_mutator.set_soft_yield_with_min_a(-1.0);

  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->ReshapeConstraints(mutable_constraints);
  std::vector<Decision> decisions;
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, mutable_constraints,
      decisions, mutable_gap_align_info_map_);
  EXPECT_EQ(decisions.front(), pb::NOT_DECIDED);
  // Constraint states have been modified.
  EXPECT_GT(
      mutable_constraints.front().states.front().start_x(Discomforts::kMin),
      kLowStartX);
  EXPECT_GT(mutable_constraints.front().states.front().strict_start_x,
            kLowStartX);
  EXPECT_TRUE(mutable_constraints.front().is_modified_by_soft);
}

// Due to the disabling of the initial regulation, the unit test
// VaryingSolverImmutableTime no longer makes sense and is therefore disabled.
TEST_F(InitialDecisionProviderTest, DISABLED_VaryingSolverImmutableTime) {
  UpdateIdpReferenceGenerator(/*ego_speed=*/ego_speed_, /*init_accel=*/-10.0,
                              /*min_speed=*/kRefSpeed,
                              /*max_speed=*/kRefSpeed);
  EXPECT_NE(immutable_times()[/*discomfort_ix=*/1],
            immutable_times()[/*discomfort_ix=*/5]);
  std::vector<Constraint> mutable_constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_,
                                       &mutable_constraints);
  constexpr double kStartX = 0.0;
  constraint_creator
      .AddSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/kStartX,
          /*longitudinal_speed=*/0.0, /*dt=*/0.1,
          /*num_states=*/80, /*agent_length=*/2.0, pb::FenceType::kCross,
          pb::ReasonerId::VRU,
          /*constraint_id=*/"cross-agent-1", /*obj_id=*/1)
      .set_soft_yield_with_min_a(-1.0);
  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->ReshapeConstraints(mutable_constraints);
  std::vector<Decision> decisions;
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, mutable_constraints,
      decisions, mutable_gap_align_info_map_);
  EXPECT_EQ(decisions.front(), pb::NOT_DECIDED);
  // Constraint states are truncated.
  const Constraint& constraint = mutable_constraints.front();
  EXPECT_TRUE(constraint.is_modified_by_soft);
  // The constraint states should not have conflicts with a brake profile at
  // high discomfort.
  Profile brake_profile =
      reference_generator_->GetOrGenerateDiscomfortReferenceProfile(
          Discomforts::kLevels - 1, steps_, dt_);
  GenerateBrakeProfile(/*brake_first_ix=*/0, /*brake_end_ix=*/steps_ - 1,
                       all_limits_->EvasiveLimits(), dt_, &brake_profile);
  const TimeMapper high_discomfort_time_mapper(
      immutable_times()[Discomforts::kLevels - 1], steps_, dt_);
  for (const auto& c_state : constraint.states) {
    int time_ix = -1;
    if (!high_discomfort_time_mapper.MaybeGetTimeIx(
            c_state.end_time, /*is_pass=*/true, &time_ix)) {
      continue;
    }
    EXPECT_LT(brake_profile[time_ix].x,
              c_state.start_x(Discomforts::kLevels - 1) - ra_to_fb_);
  }
}

TEST_F(InitialDecisionProviderTest, GetInitialDecisionsPassAlwaysPossible) {
  std::vector<Constraint> mutable_constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_,
                                       &mutable_constraints);
  constexpr double kStartTime = 0.0;
  constexpr double kEndTime = std::numeric_limits<double>::infinity();
  constexpr double kLowStartX = 0.0;
  constexpr double kEndX = 10.0;
  constraint_creator
      .AddAvoidRegion(kStartTime, kEndTime, kLowStartX, kEndX,
                      /*fence_type=*/pb::FenceType::kCrossWalkVRU,
                      pb::ReasonerId::VRU, "c-avoid-region")
      .set_pass_always_possible();
  EXPECT_FALSE(mutable_constraints.front().is_modified_by_pass_always_possible);
  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->ReshapeConstraints(mutable_constraints);
  std::vector<Decision> decisions;
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, mutable_constraints,
      decisions, mutable_gap_align_info_map_);
  EXPECT_EQ(decisions.front(), pb::NOT_DECIDED);
  // Constraint states have been modified.
  EXPECT_LT(mutable_constraints.front().states.front().end_x(Discomforts::kMin),
            kEndX);
  EXPECT_TRUE(mutable_constraints.front().is_modified_by_pass_always_possible);
}

TEST_F(InitialDecisionProviderTest, GetInitialDecisionsHardYieldWithMinAccel) {
  std::vector<Constraint> mutable_constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_,
                                       &mutable_constraints);
  constexpr double kStartX = 0.0;
  constraint_creator
      .AddSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/kStartX,
          /*longitudinal_speed=*/0.0, /*dt=*/0.1,
          /*num_states=*/80, /*agent_length=*/2.0, pb::FenceType::kCross,
          pb::ReasonerId::VRU,
          /*constraint_id=*/"cross-agent-1", /*obj_id=*/1)
      .set_hard_yield_with_min_accel(/*yield_min_a=*/-2.0);
  EXPECT_FALSE(
      mutable_constraints.front().is_modified_by_hard_yield_with_min_accel);
  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->ReshapeConstraints(mutable_constraints);
  std::vector<Decision> decisions;
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, mutable_constraints,
      decisions, mutable_gap_align_info_map_);
  EXPECT_EQ(decisions.front(), pb::NOT_DECIDED);
  // Constraint states have been modified.
  EXPECT_GT(
      mutable_constraints.front().states.front().start_x(Discomforts::kMin),
      kStartX);
  EXPECT_TRUE(
      mutable_constraints.front().is_modified_by_hard_yield_with_min_accel);
}

TEST_F(InitialDecisionProviderTest,
       NoWigglingForSafetyCriticalHardYieldWithMinAccel) {
  UpdateIdpReferenceGenerator(/*ego_speed=*/0.0, /*init_accel=*/0.0,
                              /*min_speed=*/0.0,
                              /*max_speed=*/0.0);
  DCHECK(reference_generator_.has_value());
  DCHECK(initial_decision_provider_.has_value());
  const double init_state_ra_arc_length =
      reference_generator_->reference_profile().init_state_ra_arc_length();

  std::vector<Constraint> mutable_constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_,
                                       &mutable_constraints);
  constexpr double kSafeStartX = kRaToFb + 4.0;
  constexpr double kRiskStartX = kRaToFb + 1.0;
  constexpr double kDiscomfort = Discomforts::kMid;
  constexpr double kMinRangeMaxRequiredLatGap = 0.2;
  constexpr double kMinAccel = -2.0;
  constexpr double kYieldExtraDistance = 3.0;
  constexpr double kBarrierAdjustmentForOptimizer = 0.25;
  constexpr double kToleranceForSearch = 0.001;
  constraint_creator
      .AddSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/kSafeStartX,
          /*longitudinal_speed=*/0.0, /*dt=*/0.1,
          /*num_states=*/80, /*agent_length=*/0.0, pb::FenceType::kCross,
          pb::ReasonerId::VRU,
          /*constraint_id=*/"cross-agent-1", /*obj_id=*/1)
      .set_hard_yield_with_min_accel(/*yield_min_a=*/kMinAccel)
      .set_yield_extra_distance(DiscomfortVarying(kYieldExtraDistance));
  constraint_creator
      .AddSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/kRiskStartX,
          /*longitudinal_speed=*/0.0, /*dt=*/0.1,
          /*num_states=*/80, /*agent_length=*/0.0, pb::FenceType::kCross,
          pb::ReasonerId::VRU,
          /*constraint_id=*/"cross-agent-2", /*obj_id=*/2)
      .set_hard_yield_with_min_accel(/*yield_min_a=*/kMinAccel)
      .set_yield_extra_distance(DiscomfortVarying(kYieldExtraDistance));
  const double min_range1 = GetMinRangeForConstraintState(
      mutable_constraints.front().settings,
      mutable_constraints.front().states.front(), min_range_, kDiscomfort,
      kMinRangeMaxRequiredLatGap, /*is_pass=*/false);
  const double min_range2 = GetMinRangeForConstraintState(
      mutable_constraints.back().settings,
      mutable_constraints.back().states.back(), min_range_, kDiscomfort,
      kMinRangeMaxRequiredLatGap, /*is_pass=*/false);
  EXPECT_FALSE(
      mutable_constraints.front().is_modified_by_hard_yield_with_min_accel);
  EXPECT_FALSE(
      mutable_constraints.back().is_modified_by_hard_yield_with_min_accel);
  EXPECT_GT(
      mutable_constraints.front().states.front().start_x(Discomforts::kMid) -
          min_range1,
      init_state_ra_arc_length + kBarrierAdjustmentForOptimizer);
  EXPECT_LT(
      mutable_constraints.back().states.front().start_x(Discomforts::kMid) -
          min_range2,
      init_state_ra_arc_length + kBarrierAdjustmentForOptimizer);
  initial_decision_provider_->ReshapeConstraints(mutable_constraints);
  // Non-safety critical constraint retains wiggling room.
  EXPECT_TRUE(
      mutable_constraints.front().is_modified_by_hard_yield_with_min_accel);
  EXPECT_EQ(
      mutable_constraints.front().states.front().start_x(Discomforts::kMid),
      init_state_ra_arc_length + kYieldExtraDistance + min_range1 +
          kBarrierAdjustmentForOptimizer);

  // Safety critical constraint does not retain wiggling room.
  EXPECT_TRUE(
      mutable_constraints.back().is_modified_by_hard_yield_with_min_accel);
  EXPECT_EQ(
      mutable_constraints.back().states.front().start_x(Discomforts::kMid),
      init_state_ra_arc_length + kYieldExtraDistance + min_range2 +
          kToleranceForSearch);
}

TEST_F(InitialDecisionProviderTest, GetInitialDecisionsSoftYieldWithMinV) {
  std::vector<Constraint> mutable_constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_,
                                       &mutable_constraints);
  constexpr double kStartTime = 0.0;
  constexpr double kEndTime = std::numeric_limits<double>::infinity();
  constexpr double kLowStartX = 0.0;
  constexpr double kEndX = 0.0;
  constraint_creator
      .AddAvoidRegion(kStartTime, kEndTime, kLowStartX, kEndX,
                      /*fence_type=*/pb::FenceType::kCrossWalkVRU,
                      pb::ReasonerId::VRU, "c-avoid-region")
      .set_soft_yield_with_min_speed_accel(/*yield_min_v=*/5.0,
                                           /*yield_min_a=*/-1.0);
  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->ReshapeConstraints(mutable_constraints);
  std::vector<Decision> decisions;
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, mutable_constraints,
      decisions, mutable_gap_align_info_map_);
  EXPECT_EQ(decisions.front(), pb::NOT_DECIDED);
  // Constraint states have been modified.
  EXPECT_GT(
      mutable_constraints.front().states.front().start_x(Discomforts::kMin),
      kLowStartX);
}

TEST_F(InitialDecisionProviderTest, TestYieldMinJerkForGapAlign) {
  // Add two gap align constraints, one with default yield min j, and the
  // other with customized yield min j (larger).
  std::vector<Constraint> constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_, &constraints);
  constraint_creator
      .AddGapAlignSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/-5.0,
          /*longitudinal_speed=*/ego_speed_,
          /*dt=*/0.1, /*num_states=*/80, /*agent_length=*/5.0,
          pb::FenceType::kGapAlign, pb::ReasonerId::GAP_ALIGN,
          /*constraint_id=*/"obj-1-const-speed",
          /*obj_id=*/1, /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/true)
      .set_no_pass()
      .set_yield_if_possible_with_min_speed_accel(/*yield_min_v=*/5.0,
                                                  /*yield_min_a=*/-2.0)
      .set_gap_align_yield_max_active_time(7.0);
  constraint_creator
      .AddGapAlignSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/-5.0,
          /*longitudinal_speed=*/ego_speed_,
          /*dt=*/0.1, /*num_states=*/80, /*agent_length=*/5.0,
          pb::FenceType::kGapAlign, pb::ReasonerId::GAP_ALIGN,
          /*constraint_id=*/"obj-2-const-speed",
          /*obj_id=*/2, /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/true)
      .set_no_pass()
      .set_yield_if_possible_with_min_speed_accel_jerk(/*yield_min_v=*/5.0,
                                                       /*yield_min_a=*/-2.0,
                                                       /*yield_min_j=*/-1.0)
      .set_gap_align_yield_max_active_time(7.0);
  std::vector<Decision> decisions;
  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions,
      mutable_gap_align_info_map_);
  const double default_jerk_yield_active_time =
      mutable_gap_align_info_map_[0].yield_active_time;
  const double larger_jerk_yield_active_time =
      mutable_gap_align_info_map_[1].yield_active_time;
  EXPECT_EQ(decisions[0], Decision::YIELD);
  EXPECT_GT(default_jerk_yield_active_time, 0.0);
  EXPECT_EQ(decisions[1], Decision::YIELD);
  EXPECT_GT(larger_jerk_yield_active_time, 0.0);
  // Since the 2nd constraint has larger yield min jerk, its profile should be
  // higher than the default one. Thus it has larger yield active time.
  EXPECT_GT(larger_jerk_yield_active_time, default_jerk_yield_active_time);
}

TEST_F(InitialDecisionProviderTest, GetGapAlignInfoAndDecision) {
  std::vector<Constraint> constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_, &constraints);
  // Add some other random constraints.
  constraint_creator.AddAvoidRegion(
      /*start_time=*/0.0, /*end_time=*/1.0,
      /*ra_start_x=*/100.0, /*ra_end_x=*/100.0,
      /*fence_type=*/pb::FenceType::kCrossWalkVRU, pb::ReasonerId::VRU,
      "c-avoid-region-1");
  constraint_creator.AddAvoidRegion(
      /*start_time=*/0.0, /*end_time=*/1.0,
      /*ra_start_x=*/100.0, /*ra_end_x=*/100.0,
      /*fence_type=*/pb::FenceType::kCrossWalkVRU, pb::ReasonerId::VRU,
      "c-avoid-region-2");
  // The gap align agent is in front of ego and driving in the same speed as
  // ego.
  constraint_creator
      .AddGapAlignSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/5.0,
          /*longitudinal_speed=*/ego_speed_,
          /*dt=*/0.1, /*num_states=*/80, /*agent_length=*/5.0,
          pb::FenceType::kGapAlign, pb::ReasonerId::GAP_ALIGN,
          /*constraint_id=*/"obj-1-const-speed",
          /*obj_id=*/1, /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/true)
      .set_pass_if_possible_with_max_speed_accel_jerk(/*max_v=*/15.5,
                                                      /*max_a=*/2.0,
                                                      /*max_j=*/5.0)
      .set_yield_if_possible_with_min_speed_accel(/*yield_min_v=*/5.0,
                                                  /*yield_min_a=*/-2.0);
  std::vector<Decision> decisions;
  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions,
      mutable_gap_align_info_map_);
  // There is only one GAP_ALIGN constraints so we are supposed
  // to see only one element in the gap align info map.
  EXPECT_EQ(mutable_gap_align_info_map_.size(), 1);
  // The GAP_ALIGN constraint is the third constraint in the
  // constraint manager.
  ASSERT_TRUE(mutable_gap_align_info_map_.contains(2));
  EXPECT_EQ(decisions[2], pb::NOT_DECIDED);
  // We should be able to yield to it at the very beginning and pass
  // it at a later time.
  EXPECT_GT(mutable_gap_align_info_map_[2].pass_active_time, 0.0);
  EXPECT_GT(mutable_gap_align_info_map_[2].pass_active_time_ix, 0);
  EXPECT_LE(mutable_gap_align_info_map_[2].pass_active_time,
            gap_align_pass_max_active_time_);
  EXPECT_LT(mutable_gap_align_info_map_[2].pass_active_time_ix, steps_ - 1);
  EXPECT_EQ(mutable_gap_align_info_map_[2].yield_active_time, 0.0);
  EXPECT_EQ(mutable_gap_align_info_map_[2].yield_active_time_ix, 0);

  DCHECK(initial_decision_provider_.has_value());
  pb::InitialDecisionProviderDebug debug =
      initial_decision_provider_->PopulateDebug(
          constraints, decisions,
          /*discomfort_ix=*/Discomforts::GetDiscomfortIx(0.0),
          /*risk_discomfort_ix=*/Discomforts::GetDiscomfortIx(0.0));
  // The two avoid-region constraints should have debug about the can pass
  // yield info, and no debug about the gap align.
  EXPECT_EQ(debug.initial_decision_debugs().size(), 3);
  EXPECT_EQ(debug.initial_decision_debugs(0).can_pass(), true);
  EXPECT_EQ(debug.initial_decision_debugs(0).can_yield(), true);
  EXPECT_EQ(debug.initial_decision_debugs(0).initial_decision(),
            pb::NOT_DECIDED);
  EXPECT_FALSE(debug.initial_decision_debugs(0).has_pass_profile());
  EXPECT_FALSE(debug.initial_decision_debugs(0).has_yield_profile());
  EXPECT_FALSE(debug.initial_decision_debugs(0).has_gap_align_info());
  EXPECT_EQ(debug.initial_decision_debugs(1).can_pass(), true);
  EXPECT_EQ(debug.initial_decision_debugs(1).can_yield(), true);
  EXPECT_EQ(debug.initial_decision_debugs(1).initial_decision(),
            pb::NOT_DECIDED);
  EXPECT_FALSE(debug.initial_decision_debugs(1).has_pass_profile());
  EXPECT_FALSE(debug.initial_decision_debugs(1).has_yield_profile());
  EXPECT_FALSE(debug.initial_decision_debugs(1).has_gap_align_info());
  // The gap-align constraint should have both the pass yield info debug
  // and the gap align debug.
  EXPECT_EQ(debug.initial_decision_debugs(2).can_pass(), true);
  EXPECT_EQ(debug.initial_decision_debugs(2).can_yield(), true);
  EXPECT_EQ(debug.initial_decision_debugs(2).initial_decision(),
            pb::NOT_DECIDED);
  EXPECT_TRUE(debug.initial_decision_debugs(2).has_pass_profile());
  EXPECT_TRUE(debug.initial_decision_debugs(2).has_yield_profile());
  EXPECT_TRUE(debug.initial_decision_debugs(2).has_gap_align_info());
  EXPECT_EQ(debug.initial_decision_debugs(2).gap_align_pass_max_active_time(),
            gap_align_pass_max_active_time_);
  EXPECT_EQ(debug.initial_decision_debugs(2).gap_align_yield_max_active_time(),
            gap_align_yield_max_active_time_);
}

TEST_F(InitialDecisionProviderTest, TestNewGapAlignParamAndHeuristic) {
  std::vector<Constraint> constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_, &constraints);
  // The gap align agent is in front of ego and driving in the same speed as
  // ego.
  constraint_creator
      .AddGapAlignSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/5.0,
          /*longitudinal_speed=*/ego_speed_,
          /*dt=*/0.1, /*num_states=*/80, /*agent_length=*/5.0,
          pb::FenceType::kGapAlign, pb::ReasonerId::GAP_ALIGN,
          /*constraint_id=*/"obj-1-const-speed",
          /*obj_id=*/1, /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/true)
      .set_pass_if_possible_with_max_speed_accel_jerk(/*max_v=*/15.5,
                                                      /*max_a=*/2.0,
                                                      /*max_j=*/5.0)
      .set_yield_if_possible_with_min_speed_accel(/*yield_min_v=*/5.0,
                                                  /*yield_min_a=*/-2.0)
      .set_max_allowed_extra_time_for_pass(kMaxAllowedExtraTimeForPassInSecond);

  // Add another constraint without prefer yield heuristic.
  constraint_creator
      .AddGapAlignSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/5.0,
          /*longitudinal_speed=*/ego_speed_,
          /*dt=*/0.1, /*num_states=*/80, /*agent_length=*/5.0,
          pb::FenceType::kGapAlign, pb::ReasonerId::GAP_ALIGN,
          /*constraint_id=*/"obj-2-const-speed",
          /*obj_id=*/2, /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/true)
      .set_pass_if_possible_with_max_speed_accel_jerk(/*max_v=*/15.5,
                                                      /*max_a=*/2.0,
                                                      /*max_j=*/5.0)
      .set_yield_if_possible_with_min_speed_accel(/*yield_min_v=*/5.0,
                                                  /*yield_min_a=*/-2.0);

  std::vector<Decision> decisions;
  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions,
      mutable_gap_align_info_map_);
  EXPECT_EQ(mutable_gap_align_info_map_.size(), 2);

  // For the 1st constraint with heuristic.
  // We should be able to yield to it at the very beginning and the pass
  // active time should be at a later time. With the heuristic that we do not
  // allow pass if pass active time is larger than yield active time by
  // kMaxAllowedExtraTimeForPassInSecond, the initial decision should be
  // yield.
  EXPECT_EQ(decisions[0], pb::YIELD);
  EXPECT_LE(mutable_gap_align_info_map_[0].pass_active_time,
            gap_align_pass_max_active_time_);
  EXPECT_LT(mutable_gap_align_info_map_[0].pass_active_time_ix, steps_ - 1);
  EXPECT_EQ(mutable_gap_align_info_map_[0].yield_active_time, 0.0);
  EXPECT_EQ(mutable_gap_align_info_map_[0].yield_active_time_ix, 0);
  EXPECT_GT(mutable_gap_align_info_map_[0].pass_active_time,
            mutable_gap_align_info_map_[0].yield_active_time +
                kMaxAllowedExtraTimeForPassInSecond);

  // For the 2nd constraint without heuristic.
  // The decision is NOT_DECIDED.
  EXPECT_EQ(decisions[1], pb::NOT_DECIDED);
  EXPECT_DOUBLE_EQ(mutable_gap_align_info_map_[1].pass_active_time,
                   mutable_gap_align_info_map_[0].pass_active_time);
  EXPECT_EQ(mutable_gap_align_info_map_[1].pass_active_time_ix,
            mutable_gap_align_info_map_[0].pass_active_time_ix);
  EXPECT_DOUBLE_EQ(mutable_gap_align_info_map_[1].yield_active_time,
                   mutable_gap_align_info_map_[0].yield_active_time);
  EXPECT_EQ(mutable_gap_align_info_map_[1].yield_active_time_ix,
            mutable_gap_align_info_map_[0].yield_active_time_ix);
}

// We cannot find a low-discomfort gap align initial decision, but would find
// one at high-discomfort because we have stronger kinematic capability.
TEST_F(InitialDecisionProviderTest, DiscomfortVaryingGapAlign) {
  std::vector<Constraint> constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_, &constraints);
  // The gap align agent is in front of ego and driving in the same speed as
  // ego. We only explore the pass side of it.
  constraint_creator
      .AddGapAlignSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/8.0,
          /*longitudinal_speed=*/ego_speed_,
          /*dt=*/0.1, /*num_states=*/80, /*agent_length=*/5.0,
          pb::FenceType::kGapAlign, pb::ReasonerId::GAP_ALIGN,
          /*constraint_id=*/"obj-1-const-speed",
          /*obj_id=*/1, /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/true)
      .set_pass_if_possible_with_max_speed_accel_jerk(/*max_v=*/15.5,
                                                      /*max_a=*/2.0,
                                                      /*max_j=*/5.0)
      .set_yield_if_possible_with_min_speed_accel(/*yield_min_v=*/5.0,
                                                  /*yield_min_a=*/-2.0)
      .set_no_yield();
  // Get the initial decision to the gap align at discomfort 0.0.
  std::vector<Decision> decisions1;
  constexpr double kLowDiscomfort = 0.0;
  DCHECK(initial_decision_provider_.has_value());
  bool success = initial_decision_provider_->GetInitialDecisions(
      kLowDiscomfort, kLowDiscomfort, /*for_gap_align=*/true,
      /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions1,
      mutable_gap_align_info_map_);
  // We should fail to find a feasible initial decision for the gap
  // align constraint.
  EXPECT_FALSE(success);
  // There is no feasible pass or yield active time.
  EXPECT_EQ(mutable_gap_align_info_map_.size(), 1);
  EXPECT_GT(mutable_gap_align_info_map_[0].pass_active_time,
            gap_align_pass_max_active_time_);
  EXPECT_TRUE(std::isinf(mutable_gap_align_info_map_[0].yield_active_time));

  // Get the initial decision to the gap align at discomfort 0.5.
  constexpr double kHighDiscomfort = 0.5;
  std::vector<Decision> decisions2;
  DCHECK(initial_decision_provider_.has_value());
  success = initial_decision_provider_->GetInitialDecisions(
      kHighDiscomfort, kHighDiscomfort, /*for_gap_align=*/true,
      /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions2,
      mutable_gap_align_info_map_);
  // We should be able to find a feasible initial pass decision for the gap
  // align constraint.
  EXPECT_TRUE(success);
  EXPECT_EQ(decisions2[0], pb::PASS);
  // The pass active time is smaller than the pass max active time.
  EXPECT_EQ(mutable_gap_align_info_map_.size(), 1);
  EXPECT_GT(mutable_gap_align_info_map_[0].pass_active_time, 0.0);
  EXPECT_LE(mutable_gap_align_info_map_[0].pass_active_time,
            gap_align_pass_max_active_time_);
  EXPECT_TRUE(std::isinf(mutable_gap_align_info_map_[0].yield_active_time));
}

TEST_F(InitialDecisionProviderTest,
       DiscomfortVaryingGapAlignWithAcceleratingYield) {
  std::vector<Constraint> constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_, &constraints);
  // Formulate a scene that ego will still be accelerating even
  // when yielding.
  constexpr double kYieldMinV = 15.0;
  EXPECT_GT(kYieldMinV, ego_speed_);
  constraint_creator
      .AddGapAlignSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/8.0,
          /*longitudinal_speed=*/ego_speed_,
          /*dt=*/0.1, /*num_states=*/80, /*agent_length=*/5.0,
          pb::FenceType::kGapAlign, pb::ReasonerId::GAP_ALIGN,
          /*constraint_id=*/"obj-1-const-speed",
          /*obj_id=*/1, /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/true)
      .set_yield_if_possible_with_min_speed_accel(
          /*yield_min_v=*/DiscomfortVarying(kYieldMinV),
          /*yield_min_a=*/DiscomfortVarying(-2.0, -5.0))
      .set_no_pass();
  // Get the initial decision to the gap align at discomfort 0.0 and 0.5.
  constexpr double kLowDiscomfort = 0.0;
  constexpr double kHighDiscomfort = 0.5;
  DCHECK(initial_decision_provider_.has_value());
  std::vector<Decision> decisions1;
  initial_decision_provider_->GetInitialDecisions(
      kLowDiscomfort, kLowDiscomfort, /*for_gap_align=*/true,
      /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions1,
      mutable_gap_align_info_map_);
  pb::InitialDecisionProviderDebug low_discomfort_debug =
      initial_decision_provider_->PopulateDebug(
          constraints, decisions1,
          /*discomfort_ix=*/Discomforts::GetDiscomfortIx(kLowDiscomfort),
          /*risk_discomfort_ix=*/Discomforts::GetDiscomfortIx(kLowDiscomfort));
  EXPECT_EQ(low_discomfort_debug.initial_decision_debugs().size(), 1);
  std::vector<Decision> decisions2;
  initial_decision_provider_->GetInitialDecisions(
      kHighDiscomfort, kHighDiscomfort, /*for_gap_align=*/true,
      /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions2,
      mutable_gap_align_info_map_);
  pb::InitialDecisionProviderDebug high_discomfort_debug =
      initial_decision_provider_->PopulateDebug(
          constraints, decisions2,
          /*discomfort_ix=*/Discomforts::GetDiscomfortIx(kHighDiscomfort),
          /*risk_discomfort_ix=*/Discomforts::GetDiscomfortIx(kHighDiscomfort));
  EXPECT_EQ(high_discomfort_debug.initial_decision_debugs().size(), 1);
  // Expect the yield profile at high discomfort should be the same as the one
  // at low discomfort.
  const Profile low_discomfort_yield_profile = FromProto(
      low_discomfort_debug.initial_decision_debugs(0).yield_profile());
  const Profile high_discomfort_yield_profile = FromProto(
      high_discomfort_debug.initial_decision_debugs(0).yield_profile());
  EXPECT_EQ(high_discomfort_yield_profile.size(),
            low_discomfort_yield_profile.size());
  for (size_t ix = 0; ix < high_discomfort_yield_profile.size(); ++ix) {
    EXPECT_TRUE(high_discomfort_yield_profile[ix] ==
                low_discomfort_yield_profile[ix]);
  }
}

TEST_F(InitialDecisionProviderTest, DiscomfortVaryingBufferGapAlign) {
  std::vector<Constraint> constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_, &constraints);
  // The gap align agent is beside ego and driving in the same speed as
  // ego. We only explore the pass side of it. It has a large buffer
  // at discomfort 0.0, and zero buffer at 0.5.
  constraint_creator
      .AddGapAlignSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/0.0,
          /*longitudinal_speed=*/ego_speed_,
          /*dt=*/0.1, /*num_states=*/80, /*agent_length=*/5.0,
          pb::FenceType::kGapAlign, pb::ReasonerId::GAP_ALIGN,
          /*constraint_id=*/"obj-1-const-speed",
          /*obj_id=*/1, /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/true)
      .set_pass_if_possible_with_max_speed_accel_jerk(/*max_v=*/15.5,
                                                      /*max_a=*/2.0,
                                                      /*max_j=*/5.0)
      .set_yield_if_possible_with_min_speed_accel(/*yield_min_v=*/5.0,
                                                  /*yield_min_a=*/-2.0)
      .set_no_yield()
      .set_pass_extra_distance(
          DiscomfortVarying({{0.0, 10.0}, {0.5, 0.0}, {1.0, 0.0}}));
  // Get the initial decision to the gap align at discomfort 0.0.
  std::vector<Decision> decisions1;
  constexpr double kLowDiscomfort = 0.0;
  DCHECK(initial_decision_provider_.has_value());
  bool success = initial_decision_provider_->GetInitialDecisions(
      kLowDiscomfort, kLowDiscomfort, /*for_gap_align=*/true,
      /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions1,
      mutable_gap_align_info_map_);
  // We should fail to find a feasible initial decision for the gap
  // align constraint.
  EXPECT_FALSE(success);
  // There is no feasible pass or field active time.
  EXPECT_EQ(mutable_gap_align_info_map_.size(), 1);
  EXPECT_GT(mutable_gap_align_info_map_[0].pass_active_time,
            gap_align_pass_max_active_time_);
  EXPECT_TRUE(std::isinf(mutable_gap_align_info_map_[0].yield_active_time));

  // Get the initial decision to the gap align at discomfort 0.5.
  constexpr double kHighDiscomfort = 0.5;
  std::vector<Decision> decisions2;
  mutable_gap_align_info_map_.clear();
  DCHECK(initial_decision_provider_.has_value());
  success = initial_decision_provider_->GetInitialDecisions(
      kHighDiscomfort, kHighDiscomfort, /*for_gap_align=*/true,
      /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions2,
      mutable_gap_align_info_map_);
  // We should be able to find a feasible initial pass decision for the gap
  // align constraint.
  EXPECT_TRUE(success);
  EXPECT_EQ(decisions2[0], pb::PASS);
  // The pass active time is smaller than the pass max active time.
  EXPECT_EQ(mutable_gap_align_info_map_.size(), 1);
  EXPECT_GT(mutable_gap_align_info_map_[0].pass_active_time, 0.0);
  EXPECT_LE(mutable_gap_align_info_map_[0].pass_active_time,
            gap_align_pass_max_active_time_);
  EXPECT_TRUE(std::isinf(mutable_gap_align_info_map_[0].yield_active_time));
}

TEST_F(InitialDecisionProviderTest, DiscomfortVaryingKinematicForGapAlign) {
  std::vector<Constraint> constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_, &constraints);
  // The gap align agent is beside ego and driving in the same speed as
  // ego. We have a very weak pass and yield capability at 0.0, and much
  // stronger capability at 0.5.
  constraint_creator
      .AddGapAlignSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/0.0,
          /*longitudinal_speed=*/ego_speed_,
          /*dt=*/0.1, /*num_states=*/80, /*agent_length=*/5.0,
          pb::FenceType::kGapAlign, pb::ReasonerId::GAP_ALIGN,
          /*constraint_id=*/"obj-1-const-speed",
          /*obj_id=*/1, /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/true)
      .set_pass_if_possible_with_max_speed_accel_jerk(
          /*max_v=*/DiscomfortVarying(ego_speed_, ego_speed_ * 2.0),
          /*max_a=*/DiscomfortVarying(1.0, 2.0),
          /*max_j=*/DiscomfortVarying(1.0, 5.0))
      .set_yield_if_possible_with_min_speed_accel(
          /*yield_min_v=*/DiscomfortVarying(ego_speed_, 0.0),
          /*yield_min_a=*/DiscomfortVarying(-1.0, -5.0))
      .set_max_allowed_extra_time_for_pass(kMaxAllowedExtraTimeForPassInSecond);
  // Get the initial decision to the gap align at discomfort 0.0.
  std::vector<Decision> decisions1;
  constexpr double kLowDiscomfort = 0.0;
  DCHECK(initial_decision_provider_.has_value());
  bool success = initial_decision_provider_->GetInitialDecisions(
      kLowDiscomfort, kLowDiscomfort, /*for_gap_align=*/true,
      /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions1,
      mutable_gap_align_info_map_);
  // We should fail to find a feasible initial decision for the gap
  // align constraint.
  EXPECT_FALSE(success);
  // There is no feasible pass or field active time.
  EXPECT_EQ(mutable_gap_align_info_map_.size(), 1);
  EXPECT_GT(mutable_gap_align_info_map_[0].pass_active_time,
            gap_align_pass_max_active_time_);
  EXPECT_GT(mutable_gap_align_info_map_[0].yield_active_time,
            gap_align_yield_max_active_time_);

  // Get the initial decision to the gap align at discomfort 0.5.
  constexpr double kHighDiscomfort = 0.5;
  std::vector<Decision> decisions2;
  mutable_gap_align_info_map_.clear();
  DCHECK(initial_decision_provider_.has_value());
  success = initial_decision_provider_->GetInitialDecisions(
      kHighDiscomfort, kHighDiscomfort, /*for_gap_align=*/true,
      /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions2,
      mutable_gap_align_info_map_);
  // We should be able to find a feasible initial decision.
  EXPECT_TRUE(success);
  // Both pass and yield active time is smaller than the respective max
  // allowed active time. But the decision is YIELD due to the pass-yield
  // rebalancing heuristic.
  EXPECT_EQ(mutable_gap_align_info_map_.size(), 1);
  const double pass_active_time =
      mutable_gap_align_info_map_[0].pass_active_time;
  const double yield_active_time =
      mutable_gap_align_info_map_[0].yield_active_time;
  EXPECT_GT(pass_active_time, 0.0);
  EXPECT_LE(pass_active_time, gap_align_pass_max_active_time_);
  EXPECT_GT(yield_active_time, 0.0);
  EXPECT_LE(yield_active_time, gap_align_yield_max_active_time_);
  EXPECT_GT(pass_active_time,
            yield_active_time + kMaxAllowedExtraTimeForPassInSecond);
  EXPECT_EQ(decisions2[0], pb::YIELD);
}

TEST_F(InitialDecisionProviderTest, GapAlignMaxActiveTimeEqualZero) {
  std::vector<Constraint> constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_, &constraints);
  // The gap align agent is in front of ego and driving in the same speed as
  // ego. Set its max pass/yield active time to be zero.
  constraint_creator
      .AddGapAlignSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/10.0,
          /*longitudinal_speed=*/ego_speed_,
          /*dt=*/0.1, /*num_states=*/80, /*agent_length=*/5.0,
          pb::FenceType::kGapAlign, pb::ReasonerId::GAP_ALIGN,
          /*constraint_id=*/"obj-1-const-speed",
          /*obj_id=*/1, /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/true)
      .set_pass_if_possible_with_max_speed_accel_jerk(/*max_v=*/15.5,
                                                      /*max_a=*/2.0,
                                                      /*max_j=*/5.0)
      .set_yield_if_possible_with_min_speed_accel(/*yield_min_v=*/5.0,
                                                  /*yield_min_a=*/-2.0)
      .set_gap_align_yield_max_active_time(0.0)
      .set_gap_align_pass_max_active_time(0.0);
  std::vector<Decision> decisions;
  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions,
      mutable_gap_align_info_map_);
  // There is only one GAP_ALIGN constraints so we are supposed
  // to see only one element in the gap align info map.
  EXPECT_EQ(mutable_gap_align_info_map_.size(), 1);
  ASSERT_TRUE(mutable_gap_align_info_map_.contains(0));
  // We should be able to yield to it and can not pass it at the
  // very beginning.
  EXPECT_GT(mutable_gap_align_info_map_[0].pass_active_time, 0.0);
  EXPECT_GT(mutable_gap_align_info_map_[0].pass_active_time_ix, 0);
  EXPECT_EQ(mutable_gap_align_info_map_[0].yield_active_time, 0.0);
  EXPECT_EQ(mutable_gap_align_info_map_[0].yield_active_time_ix, 0);
  // The initial decision should thus be yield.
  EXPECT_EQ(decisions.front(), pb::YIELD);
}

TEST_F(InitialDecisionProviderTest, ShortGapAlignPassMaxTimeNoPass) {
  std::vector<Constraint> constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_, &constraints);
  // The gap align agent is in front of ego and driving in the same speed as
  // ego.
  constraint_creator
      .AddGapAlignSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/5.0,
          /*longitudinal_speed=*/ego_speed_,
          /*dt=*/0.1, /*num_states=*/80, /*agent_length=*/5.0,
          pb::FenceType::kGapAlign, pb::ReasonerId::GAP_ALIGN,
          /*constraint_id=*/"obj-1-const-speed",
          /*obj_id=*/1, /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/true)
      .set_pass_if_possible_with_max_speed_accel_jerk(/*max_v=*/15.5,
                                                      /*max_a=*/2.0,
                                                      /*max_j=*/5.0)
      .set_yield_if_possible_with_min_speed_accel(/*yield_min_v=*/5.0,
                                                  /*yield_min_a=*/-2.0);

  // Construct two InitialDecisionProviders, one with longer
  // pass_max_active_time and one with shorter pass_max_active_time.
  constexpr double kLongPassMaxActiveTime = 5.0;
  constexpr double kShortPassMaxActiveTime = 0.5;
  DCHECK(all_limits_.has_value());
  DCHECK(agent_reaction_calculator_.has_value());
  DCHECK(reference_generator_.has_value());
  InitialDecisionProvider long_pass_time_decider(
      all_limits_.value(), dt_, steps_, immutable_times(), ra_to_fb_, ra_to_rb_,
      kLongPassMaxActiveTime, gap_align_yield_max_active_time_,
      gap_align_max_discomfort_, reference_generator_.value(),
      agent_reaction_calculator_.value(), min_range_, min_range_max_lat_gap_);
  InitialDecisionProvider short_pass_time_decider(
      all_limits_.value(), dt_, steps_, immutable_times(), ra_to_fb_, ra_to_rb_,
      kShortPassMaxActiveTime, gap_align_yield_max_active_time_,
      gap_align_max_discomfort_, reference_generator_.value(),
      agent_reaction_calculator_.value(), min_range_, min_range_max_lat_gap_);

  // When pass_max_active_time is longer, it is possible to have pass solution
  // so the initial decision is NOT_DECIDED. Note yield is always possible
  // since the gap-align constraint is in front of ego.
  absl::flat_hash_map<int, GapAlignInfo> mutable_gap_align_info_map_1;
  std::vector<Decision> decisions_1;
  long_pass_time_decider.GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions_1,
      mutable_gap_align_info_map_1);
  EXPECT_EQ(decisions_1.front(), pb::NOT_DECIDED);
  EXPECT_GT(mutable_gap_align_info_map_1[0].pass_active_time, 0.0);
  EXPECT_LE(mutable_gap_align_info_map_1[0].pass_active_time,
            kLongPassMaxActiveTime);
  EXPECT_EQ(mutable_gap_align_info_map_1[0].yield_active_time, 0.0);
  EXPECT_EQ(mutable_gap_align_info_map_1[0].yield_active_time_ix, 0);

  // When pass_max_active_time is shorter, we will not have pass solution
  // and the initial decision is YIELD.
  absl::flat_hash_map<int, GapAlignInfo> mutable_gap_align_info_map_2;
  std::vector<Decision> decisions_2;
  short_pass_time_decider.GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions_2,
      mutable_gap_align_info_map_2);
  EXPECT_EQ(decisions_2.front(), pb::YIELD);
  EXPECT_GT(mutable_gap_align_info_map_2[0].pass_active_time, 0.0);
  EXPECT_GT(mutable_gap_align_info_map_2[0].pass_active_time,
            kShortPassMaxActiveTime);
}

TEST_F(InitialDecisionProviderTest, ShortGapAlignYieldMaxTimeNoYield) {
  std::vector<Constraint> constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_, &constraints);
  // The gap align agent is behind ego and driving in the same speed as
  // ego.
  constraint_creator
      .AddGapAlignSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/-10.0,
          /*longitudinal_speed=*/ego_speed_,
          /*dt=*/0.1, /*num_states=*/80, /*agent_length=*/5.0,
          pb::FenceType::kGapAlign, pb::ReasonerId::GAP_ALIGN,
          /*constraint_id=*/"obj-1-const-speed",
          /*obj_id=*/1, /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/true)
      .set_pass_if_possible_with_max_speed_accel_jerk(/*max_v=*/15.0,
                                                      /*max_a=*/2.0,
                                                      /*max_j=*/5.0)
      .set_yield_if_possible_with_min_speed_accel(/*yield_min_v=*/5.0,
                                                  /*yield_min_a=*/-2.0);

  // Construct two InitialDecisionProviders, one with longer
  // yield_max_active_time and one with shorter yield_max_active_time.
  constexpr double kLongYieldMaxActiveTime = 5.0;
  constexpr double kShortYieldMaxActiveTime = 0.5;
  DCHECK(all_limits_.has_value());
  DCHECK(reference_generator_.has_value());
  DCHECK(agent_reaction_calculator_.has_value());
  InitialDecisionProvider long_yield_time_decider(
      all_limits_.value(), dt_, steps_, immutable_times(), ra_to_fb_, ra_to_rb_,
      gap_align_pass_max_active_time_, kLongYieldMaxActiveTime,
      gap_align_max_discomfort_, reference_generator_.value(),
      agent_reaction_calculator_.value(), min_range_, min_range_max_lat_gap_);
  InitialDecisionProvider short_yield_time_decider(
      all_limits_.value(), dt_, steps_, immutable_times(), ra_to_fb_, ra_to_rb_,
      gap_align_pass_max_active_time_, kShortYieldMaxActiveTime,
      gap_align_max_discomfort_, reference_generator_.value(),
      agent_reaction_calculator_.value(), min_range_, min_range_max_lat_gap_);

  // When yield_max_active_time is longer, it is possible to have yield
  // solution so the initial decision is NOT_DECIDED. Note pass is always
  // possible since the gap-align constraint is behind ego.
  absl::flat_hash_map<int, GapAlignInfo> mutable_gap_align_info_map_1;
  std::vector<Decision> decisions_1;
  long_yield_time_decider.GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions_1,
      mutable_gap_align_info_map_1);
  EXPECT_EQ(decisions_1.front(), pb::NOT_DECIDED);
  EXPECT_GT(mutable_gap_align_info_map_1[0].yield_active_time, 0.0);
  EXPECT_LE(mutable_gap_align_info_map_1[0].yield_active_time,
            kLongYieldMaxActiveTime);

  // When yield_max_active_time is shorter, we will not have yield solution
  // and the initial decision is PASS.
  absl::flat_hash_map<int, GapAlignInfo> mutable_gap_align_info_map_2;
  std::vector<Decision> decisions_2;
  short_yield_time_decider.GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions_2,
      mutable_gap_align_info_map_2);
  EXPECT_EQ(decisions_2.front(), pb::PASS);
  EXPECT_GT(mutable_gap_align_info_map_2[0].yield_active_time, 0.0);
  EXPECT_GT(mutable_gap_align_info_map_2[0].yield_active_time,
            kShortYieldMaxActiveTime);
}

TEST_F(InitialDecisionProviderTest, GapAlignCanNotPass) {
  std::vector<Constraint> constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_, &constraints);
  // The agent is in front of ego and driving in a faster speed than ego.
  constraint_creator
      .AddGapAlignSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/10.0,
          /*longitudinal_speed=*/15.0, /*dt=*/0.1,
          /*num_states=*/80, /*agent_length=*/5.0, pb::FenceType::kGapAlign,
          pb::ReasonerId::GAP_ALIGN,
          /*constraint_id=*/"obj-2-const-speed",
          /*obj_id=*/1, /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/true)
      .set_pass_if_possible_with_max_speed_accel_jerk(/*max_v=*/15.0,
                                                      /*max_a=*/2.0,
                                                      /*max_j=*/5.0)
      .set_yield_if_possible_with_min_speed_accel(/*yield_min_v=*/5.0,
                                                  /*yield_min_a=*/-2.0);
  std::vector<Decision> decisions;
  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions,
      mutable_gap_align_info_map_);
  EXPECT_EQ(decisions.front(), pb::YIELD);
  EXPECT_EQ(mutable_gap_align_info_map_.size(), 1);
  ASSERT_TRUE(mutable_gap_align_info_map_.contains(0));
  // We should be able to yield it at the very beginning but can not pass it.
  // In the case we can not pass, pass_active_time is inf and time_ix is -1.
  EXPECT_TRUE(std::isinf(mutable_gap_align_info_map_[0].pass_active_time));
  EXPECT_EQ(mutable_gap_align_info_map_[0].pass_active_time_ix, -1);
  EXPECT_EQ(mutable_gap_align_info_map_[0].yield_active_time, 0.0);
  EXPECT_EQ(mutable_gap_align_info_map_[0].yield_active_time_ix, 0);
}

TEST_F(InitialDecisionProviderTest, GapAlignCanPassWithSoft) {
  std::vector<Constraint> constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_, &constraints);
  // The agent is in front of ego and driving in a faster speed than ego.
  constraint_creator
      .AddGapAlignSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/10.0,
          /*longitudinal_speed=*/15.0, /*dt=*/0.1,
          /*num_states=*/80, /*agent_length=*/5.0, pb::FenceType::kGapAlign,
          pb::ReasonerId::GAP_ALIGN,
          /*constraint_id=*/"obj-2-const-speed",
          /*obj_id=*/1, /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/true)
      .set_soft_pass_with_max_speed_accel_jerk(/*max_v=*/15.0,
                                               /*max_a=*/2.0,
                                               /*max_j=*/5.0)
      .set_yield_if_possible_with_min_speed_accel(/*yield_min_v=*/5.0,
                                                  /*yield_min_a=*/-2.0);
  std::vector<Decision> decisions;
  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->ReshapeConstraints(constraints);
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions,
      mutable_gap_align_info_map_);
  EXPECT_EQ(decisions.front(), pb::NOT_DECIDED);
  EXPECT_EQ(mutable_gap_align_info_map_.size(), 1);
  ASSERT_TRUE(mutable_gap_align_info_map_.contains(0));
  // We should be able to either pass and or yield to it.
  // The pass option is SOFT, and it were not going to physically pass the
  // constraint within planning horizon. So pass active time is 8.0 and active
  // time ix is 80.
  EXPECT_GT(mutable_gap_align_info_map_[0].pass_active_time, 8.0 - 1e-6);
  EXPECT_EQ(mutable_gap_align_info_map_[0].pass_active_time_ix, 80);
  EXPECT_DOUBLE_EQ(mutable_gap_align_info_map_[0].yield_active_time, 0.0);
  EXPECT_EQ(mutable_gap_align_info_map_[0].yield_active_time_ix, 0);
}

TEST_F(InitialDecisionProviderTest, GapAlignCanNotYield) {
  std::vector<Constraint> constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_, &constraints);
  // The agent is behind ego and driving in a slower speed than ego.
  constraint_creator
      .AddGapAlignSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/-10.0,
          /*longitudinal_speed=*/5.0, /*dt=*/0.1,
          /*num_states=*/80, /*agent_length=*/5.0, pb::FenceType::kGapAlign,
          pb::ReasonerId::GAP_ALIGN,
          /*constraint_id=*/"obj-3-const-speed",
          /*obj_id=*/1, /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/true)
      .set_pass_if_possible_with_max_speed_accel_jerk(/*max_v=*/15.0,
                                                      /*max_a=*/2.0,
                                                      /*max_j=*/5.0)
      .set_yield_if_possible_with_min_speed_accel(/*yield_min_v=*/5.0,
                                                  /*yield_min_a=*/-2.0);
  std::vector<Decision> decisions;
  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions,
      mutable_gap_align_info_map_);
  EXPECT_EQ(decisions.front(), pb::PASS);
  EXPECT_EQ(mutable_gap_align_info_map_.size(), 1);
  ASSERT_TRUE(mutable_gap_align_info_map_.contains(0));
  // We should be able to pass it at the very beginning but can not yield to
  // it.
  EXPECT_EQ(mutable_gap_align_info_map_[0].pass_active_time, 0.0);
  EXPECT_EQ(mutable_gap_align_info_map_[0].pass_active_time_ix, 0);
  EXPECT_TRUE(std::isinf(mutable_gap_align_info_map_[0].yield_active_time));
  EXPECT_EQ(mutable_gap_align_info_map_[0].yield_active_time_ix, -1);
}

TEST_F(InitialDecisionProviderTest, GapAlignCanYieldWithSoft) {
  std::vector<Constraint> constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_, &constraints);
  // The agent is behind ego and driving in a slower speed than ego.
  constraint_creator
      .AddGapAlignSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/-10.0,
          /*longitudinal_speed=*/5.0, /*dt=*/0.1,
          /*num_states=*/80, /*agent_length=*/5.0, pb::FenceType::kGapAlign,
          pb::ReasonerId::GAP_ALIGN,
          /*constraint_id=*/"obj-1-const-speed",
          /*obj_id=*/1, /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/true)
      .set_pass_if_possible_with_max_speed_accel_jerk(/*max_v=*/15.0,
                                                      /*max_a=*/2.0,
                                                      /*max_j=*/5.0)
      .set_soft_yield_with_min_speed_accel(/*yield_min_v=*/5.0,
                                           /*yield_min_a=*/-2.0);

  // The agent is faster than ego and near ego. So ego will be able to yield to
  // it.
  constraint_creator
      .AddGapAlignSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/0.0,
          /*longitudinal_speed=*/13.0, /*dt=*/0.1,
          /*num_states=*/80, /*agent_length=*/5.0, pb::FenceType::kGapAlign,
          pb::ReasonerId::GAP_ALIGN,
          /*constraint_id=*/"obj-2-const-speed",
          /*obj_id=*/2, /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/true)
      .set_no_pass()
      .set_soft_yield_with_min_speed_accel(/*yield_min_v=*/5.0,
                                           /*yield_min_a=*/-1.0);

  std::vector<Decision> decisions;
  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->ReshapeConstraints(constraints);
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions,
      mutable_gap_align_info_map_);
  EXPECT_EQ(decisions.front(), pb::NOT_DECIDED);
  EXPECT_EQ(decisions.back(), pb::YIELD);
  EXPECT_EQ(mutable_gap_align_info_map_.size(), 2);
  ASSERT_TRUE(mutable_gap_align_info_map_.contains(0));
  ASSERT_TRUE(mutable_gap_align_info_map_.contains(1));
  // We should be able to either pass and or yield to it.
  // The yield option is SOFT, and it were not going to physically yield to the
  // constraint within planning horizon. So yield active time is 8.0 and active
  // time ix is 80.
  EXPECT_DOUBLE_EQ(mutable_gap_align_info_map_[0].pass_active_time, 0.0);
  EXPECT_EQ(mutable_gap_align_info_map_[0].pass_active_time_ix, 0);
  EXPECT_GT(mutable_gap_align_info_map_[0].yield_active_time, 8.0 - 1e-6);
  EXPECT_EQ(mutable_gap_align_info_map_[0].yield_active_time_ix, 80);

  // We should be able to yield to it, and not able to pass it.
  // The yield option is SOFT, and ego will yield to it within planning horizon.
  // So yield active time is smaller than 8.0 and active time ix smaller
  // than 80.
  EXPECT_TRUE(std::isinf(mutable_gap_align_info_map_[1].pass_active_time));
  EXPECT_EQ(mutable_gap_align_info_map_[1].pass_active_time_ix, -1);
  EXPECT_LT(mutable_gap_align_info_map_[1].yield_active_time, 8.0 - 1e-6);
  EXPECT_LT(mutable_gap_align_info_map_[1].yield_active_time_ix, 80);
  EXPECT_GT(mutable_gap_align_info_map_[1].yield_active_time, 0.0);
  EXPECT_GT(mutable_gap_align_info_map_[1].yield_active_time_ix, 0);
}

TEST_F(InitialDecisionProviderTest,
       GapAlignConstraintsWithShortStatesCanNotPass) {
  std::vector<Constraint> constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_, &constraints);
  // The agent is in front of ego and driving in a faster speed than ego.
  // It also only has 30 states (i.e. we can not see it after 3s).
  constraint_creator
      .AddGapAlignSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/10.0,
          /*longitudinal_speed=*/15.0, /*dt=*/0.1,
          /*num_states=*/30, /*agent_length=*/5.0, pb::FenceType::kGapAlign,
          pb::ReasonerId::GAP_ALIGN,
          /*constraint_id=*/"obj-1-const-speed",
          /*obj_id=*/1, /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/true)
      .set_pass_if_possible_with_max_speed_accel_jerk(/*max_v=*/15.0,
                                                      /*max_a=*/2.0,
                                                      /*max_j=*/5.0)
      .set_yield_if_possible_with_min_speed_accel(/*yield_min_v=*/5.0,
                                                  /*yield_min_a=*/-2.0)
      .set_max_allowed_extra_time_for_pass(kMaxAllowedExtraTimeForPassInSecond);
  std::vector<Decision> decisions;
  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions,
      mutable_gap_align_info_map_);
  // Although physically we cannot pass this agent, this agent is leaving the
  // interest region to add as gap align constraint soon. Also considering the
  // heuristic that we allow pass if the pass active time is not larger than
  // yield active time by more than 1.0s. So we should have a YIELD decision for
  // the constraint.
  EXPECT_EQ(decisions.front(), pb::YIELD);
  EXPECT_EQ(mutable_gap_align_info_map_.size(), 1);
  ASSERT_TRUE(mutable_gap_align_info_map_.contains(0));
  // We should be able to yield it at the very beginning, and can pass it after
  // it disappears in 3s).
  EXPECT_EQ(mutable_gap_align_info_map_[0].pass_active_time_ix, 30);
  EXPECT_TRUE(
      math::NearZero(mutable_gap_align_info_map_[0].pass_active_time - 3.0));
  EXPECT_EQ(mutable_gap_align_info_map_[0].yield_active_time_ix, 0);
  EXPECT_TRUE(math::NearZero(mutable_gap_align_info_map_[0].yield_active_time));
}

// Tests the cases that when a constraint has inf pass-max-v, it is equivalent
// to following the max speed profile at the given discomfort for pass,
// instead of unlimited acceleration.
TEST_F(InitialDecisionProviderTest, GapAlignInfPassMaxV) {
  std::vector<Constraint> constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_, &constraints);
  // The agent is in front of ego and driving in a faster speed than ego.
  constraint_creator
      .AddGapAlignSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/10.0,
          /*longitudinal_speed=*/ego_speed_ + 6.67,
          /*dt=*/0.1,
          /*num_states=*/80, /*agent_length=*/5.0, pb::FenceType::kGapAlign,
          pb::ReasonerId::GAP_ALIGN,
          /*constraint_id=*/"obj-2-const-speed",
          /*obj_id=*/1, /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/true)
      .set_pass_if_possible_with_max_speed_accel_jerk(
          /*max_v=*/std::numeric_limits<double>::infinity(),
          /*max_a=*/5.0,
          /*max_j=*/5.0)
      .set_yield_if_possible_with_min_speed_accel(/*yield_min_v=*/5.0,
                                                  /*yield_min_a=*/-2.0);
  std::vector<Decision> decisions;
  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions,
      mutable_gap_align_info_map_);
  // Ego speed is 10 m/s, and reference generator max speed is 16.67 m/s, so
  // we can not accelerate to over 16.67 m/s to pass the agent.
  EXPECT_EQ(decisions.front(), pb::YIELD);
  EXPECT_EQ(mutable_gap_align_info_map_.size(), 1);
  ASSERT_TRUE(mutable_gap_align_info_map_.contains(0));
  // We should be able to yield it at the very beginning but can not pass it.
  // In the case we can not pass, pass_active_time is inf and time_ix is -1.
  EXPECT_TRUE(std::isinf(mutable_gap_align_info_map_[0].pass_active_time));
  EXPECT_EQ(mutable_gap_align_info_map_[0].pass_active_time_ix, -1);
  EXPECT_EQ(mutable_gap_align_info_map_[0].yield_active_time, 0.0);
  EXPECT_EQ(mutable_gap_align_info_map_[0].yield_active_time_ix, 0);
}

// Tests the cases that a gap align constraint cannot be passed, and
// is grouped with a regular constraint.
TEST_F(InitialDecisionProviderTest, NoPassGapAlignGroupedWithRegular) {
  std::vector<Constraint> constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_, &constraints);
  // The agent is in front of ego and driving in a faster speed than ego.
  constraint_creator
      .AddGapAlignSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/100.0,
          /*longitudinal_speed=*/ego_speed_ + 6.67,
          /*dt=*/0.1,
          /*num_states=*/80, /*agent_length=*/5.0, pb::FenceType::kGapAlign,
          pb::ReasonerId::GAP_ALIGN,
          /*constraint_id=*/"obj-1-const-speed",
          /*obj_id=*/1, /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/true)
      .set_pass_if_possible_with_max_speed_accel_jerk(
          /*max_v=*/std::numeric_limits<double>::infinity(),
          /*max_a=*/5.0,
          /*max_j=*/5.0)
      .set_yield_if_possible_with_min_speed_accel(/*yield_min_v=*/5.0,
                                                  /*yield_min_a=*/-2.0)
      .set_group_id("group-1");

  // Add a regular constraint.
  constraint_creator
      .AddAvoidRegion(/*start_time=*/3.0, /*end_time=*/4.0,
                      /*ra_start_x=*/40.0, /*ra_end_x=*/50.0,
                      /*fence_type=*/pb::FenceType::kCrossWalkVRU,
                      pb::ReasonerId::VRU, "c-avoid-region-1")
      .set_group_id("group-1");

  std::vector<Decision> decisions;
  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions,
      mutable_gap_align_info_map_);
  // Ego speed is 10 m/s, and reference generator max speed is 16.67 m/s, so
  // we can not accelerate to over 16.67 m/s to pass the agent.
  EXPECT_EQ(decisions.front(), pb::YIELD);
  // Since the avoid region is grouped with the gap align constraint, its
  // decision is also updated.
  EXPECT_EQ(decisions.back(), pb::YIELD);
}

// Tests the cases that a gap align constraint cannot be yield, and
// is grouped with a regular constraint.
TEST_F(InitialDecisionProviderTest, NoYieldGapAlignGroupedWithRegular) {
  std::vector<Constraint> constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_, &constraints);
  // The agent is far behind ego and driving at ego speed.
  constraint_creator
      .AddGapAlignSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/-100.0,
          /*longitudinal_speed=*/ego_speed_,
          /*dt=*/0.1,
          /*num_states=*/80, /*agent_length=*/5.0, pb::FenceType::kGapAlign,
          pb::ReasonerId::GAP_ALIGN,
          /*constraint_id=*/"obj-1-const-speed",
          /*obj_id=*/1, /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/true)
      .set_pass_if_possible_with_max_speed_accel_jerk(
          /*max_v=*/std::numeric_limits<double>::infinity(),
          /*max_a=*/5.0,
          /*max_j=*/5.0)
      .set_yield_if_possible_with_min_speed_accel(/*yield_min_v=*/5.0,
                                                  /*yield_min_a=*/-2.0)
      .set_group_id("group-1");

  // Add a regular constraint.
  constraint_creator
      .AddAvoidRegion(/*start_time=*/3.0, /*end_time=*/4.0,
                      /*ra_start_x=*/40.0, /*ra_end_x=*/50.0,
                      /*fence_type=*/pb::FenceType::kCrossWalkVRU,
                      pb::ReasonerId::VRU, "c-avoid-region-1")
      .set_group_id("group-1");

  std::vector<Decision> decisions;
  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions,
      mutable_gap_align_info_map_);
  // For this gap align constraint which is far behind ego, we will have a
  // initial decision of PASS.
  EXPECT_EQ(decisions.front(), pb::PASS);
  // Although the avoid region is grouped with the gap align constraint, we do
  // not set no yield to its grouped constraints.
  EXPECT_EQ(decisions.back(), pb::NOT_DECIDED);
}

// Tests the cases that a gap align constraint cannot be yield, and
// is grouped with a no pass constraint.
TEST_F(InitialDecisionProviderTest, NoYieldGapAlignGroupedWithNoPassRegular) {
  std::vector<Constraint> constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_, &constraints);
  // The agent is far behind ego and driving at ego speed.
  constraint_creator
      .AddGapAlignSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/-100.0,
          /*longitudinal_speed=*/ego_speed_,
          /*dt=*/0.1,
          /*num_states=*/80, /*agent_length=*/5.0, pb::FenceType::kGapAlign,
          pb::ReasonerId::GAP_ALIGN,
          /*constraint_id=*/"obj-1-const-speed",
          /*obj_id=*/1, /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/true)
      .set_pass_if_possible_with_max_speed_accel_jerk(
          /*max_v=*/std::numeric_limits<double>::infinity(),
          /*max_a=*/5.0,
          /*max_j=*/5.0)
      .set_yield_if_possible_with_min_speed_accel(/*yield_min_v=*/5.0,
                                                  /*yield_min_a=*/-2.0)
      .set_group_id("group-1");

  // Add a no pass constraint.
  constraint_creator
      .AddAvoidRegion(/*start_time=*/3.0, /*end_time=*/4.0,
                      /*ra_start_x=*/40.0, /*ra_end_x=*/50.0,
                      /*fence_type=*/pb::FenceType::kCrossWalkVRU,
                      pb::ReasonerId::VRU, "c-avoid-region-1")
      .set_group_id("group-1")
      .set_no_pass();

  std::vector<Decision> decisions;
  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions,
      mutable_gap_align_info_map_);
  // For this gap align constraint which cannot be yielded, but grouped with a
  // constraint with no pass, its decision will be updated to IGNORE. And we
  // will signal an error before we start the gap align search if this gap
  // align can not be pass-ignored.
  EXPECT_EQ(decisions.front(), pb::IGNORE);
  EXPECT_EQ(decisions.back(), pb::YIELD);
}

// Tests the cases that a gap align constraint cannot be passed, and
// is grouped with a no yield constraint.
TEST_F(InitialDecisionProviderTest, NoPassGapAlignGroupedWithNoYieldRegular) {
  std::vector<Constraint> constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_, &constraints);
  // The agent is in front of ego and driving at ego speed.
  constraint_creator
      .AddGapAlignSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/100.0,
          /*longitudinal_speed=*/ego_speed_,
          /*dt=*/0.1,
          /*num_states=*/80, /*agent_length=*/5.0, pb::FenceType::kGapAlign,
          pb::ReasonerId::GAP_ALIGN,
          /*constraint_id=*/"obj-1-const-speed",
          /*obj_id=*/1, /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/true)
      .set_pass_if_possible_with_max_speed_accel_jerk(
          /*max_v=*/std::numeric_limits<double>::infinity(),
          /*max_a=*/5.0,
          /*max_j=*/5.0)
      .set_yield_if_possible_with_min_speed_accel(/*yield_min_v=*/5.0,
                                                  /*yield_min_a=*/-2.0)
      .set_group_id("group-1");

  // Add a no yield constraint.
  constraint_creator
      .AddAvoidRegion(/*start_time=*/3.0, /*end_time=*/4.0,
                      /*ra_start_x=*/40.0, /*ra_end_x=*/50.0,
                      /*fence_type=*/pb::FenceType::kCrossWalkVRU,
                      pb::ReasonerId::VRU, "c-avoid-region-1")
      .set_group_id("group-1")
      .set_no_yield();

  std::vector<Decision> decisions;
  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions,
      mutable_gap_align_info_map_);
  // For this gap align constraint which cannot be passed, but grouped with a
  // constraint with no yield constraint, its decision will be YIELD.
  EXPECT_EQ(decisions.front(), pb::YIELD);
  // The regular constraint will be updated to IGNORE.
  EXPECT_EQ(decisions.back(), pb::IGNORE);
}

// Tests the cases that a gap align constraint cannot be passed, and
// is grouped with a must pass constraint.
TEST_F(InitialDecisionProviderTest, NoPassGapAlignGroupedWithMustPassRegular) {
  std::vector<Constraint> constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_, &constraints);
  // The agent is in front of ego and driving at ego speed.
  constraint_creator
      .AddGapAlignSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/100.0,
          /*longitudinal_speed=*/ego_speed_,
          /*dt=*/0.1,
          /*num_states=*/80, /*agent_length=*/5.0, pb::FenceType::kGapAlign,
          pb::ReasonerId::GAP_ALIGN,
          /*constraint_id=*/"obj-1-const-speed",
          /*obj_id=*/1, /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/true)
      .set_pass_if_possible_with_max_speed_accel_jerk(
          /*max_v=*/std::numeric_limits<double>::infinity(),
          /*max_a=*/5.0,
          /*max_j=*/5.0)
      .set_yield_if_possible_with_min_speed_accel(/*yield_min_v=*/5.0,
                                                  /*yield_min_a=*/-2.0)
      .set_group_id("group-1");

  // Add a regular constraint.
  constraint_creator
      .AddAvoidRegion(/*start_time=*/3.0, /*end_time=*/4.0,
                      /*ra_start_x=*/40.0, /*ra_end_x=*/50.0,
                      /*fence_type=*/pb::FenceType::kCrossWalkVRU,
                      pb::ReasonerId::VRU, "c-avoid-region-1")
      .set_group_id("group-1")
      .set_no_yield()
      .set_allow_pass_ignore(false);

  // A no-pass gap align constraint grouped with a must-pass constraint fails
  // the grouping contract.
  std::vector<Decision> decisions;
  DCHECK(initial_decision_provider_.has_value());
  EXPECT_DEATH(  // NOLINT, FP when checking macro EXPECT_DEATH
      initial_decision_provider_->GetInitialDecisions(
          /*cur_discomfort=*/0.0,
          /*elevated_discomfort=*/0.0, /*for_gap_align=*/true,
          /*for_rm=*/false,
          /*constraint_indices_to_explore_yield=*/{}, constraints, decisions,
          mutable_gap_align_info_map_);
      , "");
}

TEST_F(InitialDecisionProviderTest, GapAlignConstraintsWithAgentReaction) {
  std::vector<Constraint> constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_, &constraints);
  ConstraintMutator constraint_mutator =
      constraint_creator.AddGapAlignSpeedConstraintFromOverlapRegion(
          pb::FenceType::kGapAlign, pb::ReasonerId::LANE_CHANGE_PREPARATION,
          /*constraint_id=*/"obj-1-gap-align", overlap_region_,
          /*obj_id=*/1,
          /*c_traj_id=*/"obj-1-pred-0",
          /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/true);
  constraint_mutator
      .set_pass_if_possible_with_max_speed_accel_jerk(/*max_v=*/12.0,
                                                      /*max_a=*/2.0,
                                                      /*max_j=*/5.0)
      .set_yield_if_possible_with_min_speed_accel(/*yield_min_v=*/5.0,
                                                  /*yield_min_a=*/-2.0);
  absl::flat_hash_map<int, GapAlignInfo> mutable_gap_align_info_map;
  std::vector<Decision> decisions;
  DCHECK(all_limits_.has_value());
  DCHECK(reference_generator_.has_value());
  DCHECK(agent_reaction_calculator_.has_value());
  InitialDecisionProvider idp1(
      all_limits_.value(), kDt, kSteps,
      reference_generator_.value().immutable_times(), kRaToFb, kRaToRb,
      kGapAlignMaxActiveTime, kGapAlignMaxActiveTime, kGapAlignMaxDiscomfort,
      reference_generator_.value(), agent_reaction_calculator_.value(),
      min_range_, min_range_max_lat_gap_);

  idp1.GetInitialDecisions(
      /*cur_discomfort=*/0.5,
      /*elevated_discomfort=*/0.5, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions,
      mutable_gap_align_info_map);
  EXPECT_EQ(decisions.front(), pb::NOT_DECIDED);
  EXPECT_EQ(mutable_gap_align_info_map.size(), 1);
  ASSERT_TRUE(mutable_gap_align_info_map.contains(0));
  EXPECT_TRUE(
      math::NearZero(mutable_gap_align_info_map[0].pass_active_time - 4.5));
  EXPECT_EQ(mutable_gap_align_info_map[0].pass_active_time_ix, 45);
  EXPECT_TRUE(math::NearZero(mutable_gap_align_info_map[0].yield_active_time));
  EXPECT_EQ(mutable_gap_align_info_map[0].yield_active_time_ix, 0);

  constraint_mutator.set_pass_agent_reaction(
      ReactionTimes{DiscomfortVarying(0.0), DiscomfortVarying(0.0)},
      SlowDownParams{{DiscomfortVarying(-2.0), DiscomfortVarying(0.8)},
                     {DiscomfortVarying(-2.0), DiscomfortVarying(0.8)}});
  absl::flat_hash_map<int, GapAlignInfo> mutable_gap_align_info_map2;
  std::vector<Decision> decisions2;
  // Use a new IDP so we are not hitting the same cache.
  DCHECK(initial_decision_provider_.has_value());
  InitialDecisionProvider idp2(
      all_limits_.value(), kDt, kSteps,
      reference_generator_.value().immutable_times(), kRaToFb, kRaToRb,
      kGapAlignMaxActiveTime, kGapAlignMaxActiveTime, kGapAlignMaxDiscomfort,
      reference_generator_.value(), agent_reaction_calculator_.value(),
      min_range_, min_range_max_lat_gap_);
  idp2.GetInitialDecisions(
      /*cur_discomfort=*/0.5,
      /*elevated_discomfort=*/0.5, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions2,
      mutable_gap_align_info_map2);
  // We should be able to yield it at the very beginning and pass it as well
  // after AR.
  EXPECT_EQ(decisions2.front(), pb::NOT_DECIDED);
  EXPECT_EQ(mutable_gap_align_info_map2.size(), 1);
  ASSERT_TRUE(mutable_gap_align_info_map2.contains(0));
  EXPECT_GT(mutable_gap_align_info_map2[0].pass_active_time, 0.0);
  EXPECT_LT(mutable_gap_align_info_map2[0].pass_active_time, 5.0);
  EXPECT_GT(mutable_gap_align_info_map2[0].pass_active_time_ix, 0);
  EXPECT_EQ(mutable_gap_align_info_map2[0].yield_active_time, 0.0);
  EXPECT_EQ(mutable_gap_align_info_map2[0].yield_active_time_ix, 0);
}

// This test is to verify that the initial decision for a pass
// only constraint is PASS and any other constraints in the same
// group have the decisions set to either PASS or IGNORE.
TEST_F(InitialDecisionProviderTest, PassOnlyConstraintDecisions) {
  std::vector<Constraint> mutable_constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_,
                                       &mutable_constraints);
  constexpr double kStartTime = 5.0;
  constexpr double kEndTime = 6.0;
  constexpr double kStartX = 25.0;
  constexpr double kEndX = 50.0;
  const std::string group_id = "g1";
  // Set a pass only constraint.
  constraint_creator
      .AddAvoidRegion(kStartTime, kEndTime, kStartX, kEndX,
                      /*fence_type=*/pb::FenceType::kCrossWalkVRU,
                      pb::ReasonerId::VRU, "xwalk-pass-yield")
      .set_no_yield()
      .set_allow_pass_ignore(false);

  // Set a generic constraint.
  [[maybe_unused]] auto mutator2 = constraint_creator.AddAvoidRegion(
      kStartTime, kEndTime, kStartX, kEndX,
      /*fence_type=*/pb::FenceType::kCrossWalkVRU, pb::ReasonerId::VRU,
      "xwalk-pass-yield-2");

  std::vector<Decision> decisions;
  DCHECK(  // NOLINT, FP when checking macro EXPECT_DEATH
      initial_decision_provider_.has_value());
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, mutable_constraints,
      decisions, mutable_gap_align_info_map_);
  // Verify that the decision for first constraint is PASS
  // and second constraint is NOT_DECIDED.
  EXPECT_EQ(decisions.front(), Decision::PASS);
  EXPECT_EQ(decisions.back(), Decision::NOT_DECIDED);

  // Set the same group id for both constraints.
  mutable_constraints.front().settings.group_id = group_id;
  mutable_constraints.back().settings.group_id = group_id;
  EXPECT_EQ(mutable_constraints.front().settings.group_id,
            mutable_constraints.back().settings.group_id);

  // Calculate the initial decisions again. Expect death because we do not
  // allow must pass constraint to group with non must pass constraint.
  EXPECT_DEATH(  // NOLINT, FP when checking macro EXPECT_DEATH
      initial_decision_provider_->GetInitialDecisions(
          /*cur_discomfort=*/0.0,
          /*elevated_discomfort=*/0.0, /*for_gap_align=*/true,
          /*for_rm=*/false,
          /*constraint_indices_to_explore_yield=*/{}, mutable_constraints,
          decisions, mutable_gap_align_info_map_),
      "");
}

// This test is to verify that the initial decision for a IGNORE-ONLY
// constraint is IGNORE and no yield constraint in the same group have the
// decision set to PASS.
TEST_F(InitialDecisionProviderTest, OneIgnoreOnlyAndOneNoYield) {
  std::vector<Constraint> constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_, &constraints);
  constexpr double kStartTime = 5.0;
  constexpr double kEndTime = 6.0;
  constexpr double kStartX = 25.0;
  constexpr double kEndX = 50.0;
  const std::string group_id = "g1";
  // Set a ignore only constraint.
  constraint_creator
      .AddAvoidRegion(kStartTime, kEndTime, kStartX, kEndX,
                      /*fence_type=*/pb::FenceType::kCrossWalkVRU,
                      pb::ReasonerId::VRU, "xwalk-pass-yield")
      .set_no_pass()
      .set_no_yield()
      .set_group_id("group-1");

  // Set no yield constraint.
  constraint_creator
      .AddAvoidRegion(kStartTime, kEndTime, kStartX, kEndX,
                      /*fence_type=*/pb::FenceType::kCrossWalkVRU,
                      pb::ReasonerId::VRU, "xwalk-pass-yield-2")
      .set_no_yield()
      .set_group_id("group-1");

  std::vector<Decision> decisions;
  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions,
      mutable_gap_align_info_map_);
  // Verify that the decision for first constraint is IGNORE
  // and second constraint is PASS.
  EXPECT_EQ(decisions.front(), Decision::IGNORE);
  EXPECT_EQ(decisions.back(), Decision::PASS);
}

TEST_F(InitialDecisionProviderTest, UpdateInitialDecisionDeath) {
  std::vector<Constraint> constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_, &constraints);
  constexpr double kStartTime = 5.0;
  constexpr double kEndTime = 6.0;
  constexpr double kStartX = 25.0;
  constexpr double kEndX = 50.0;
  const std::string group_id = "group-1";
  // Set a must-pass constraint.
  constraint_creator
      .AddAvoidRegion(kStartTime, kEndTime, kStartX, kEndX,
                      pb::FenceType::kCrossWalkVRU, pb::ReasonerId::VRU,
                      "xwalk-pass-yield-2")
      .set_no_yield()
      .set_allow_pass_ignore(false)
      .set_group_id(group_id);
  // Set a no-pass constraint.
  constraint_creator
      .AddAvoidRegion(kStartTime, kEndTime, kStartX, kEndX,
                      pb::FenceType::kCrossWalkVRU, pb::ReasonerId::VRU,
                      "xwalk-pass-yield")
      .set_no_pass()
      .set_group_id(group_id);
  // By default, a no-pass and a must-pass constraint should not be
  // in the same group. It would cause a DCHECK fail due to contract
  // violation.
  std::vector<Decision> decisions;
  DCHECK(initial_decision_provider_.has_value());
  EXPECT_DEATH(  // NOLINT, FP when checking macro EXPECT_DEATH
      initial_decision_provider_->GetInitialDecisions(
          /*cur_discomfort=*/0.0,
          /*elevated_discomfort=*/0.0, /*for_gap_align=*/true,
          /*for_rm=*/false,
          /*constraint_indices_to_explore_yield=*/{}, constraints, decisions,
          mutable_gap_align_info_map_),
      "");
}

TEST_F(InitialDecisionProviderTest, InitialDecisionsAtDiscomfort) {
  Constraint constraint1(ConstraintType::Constraint_Type_AVOID_REGION,
                         /*fence_type=*/pb::FenceType::kCrossWalkVRU,
                         "reasoner1", "avoid1");
  constraint1.settings.pass_if_possible_below_discomfort = 0.5;
  constraint1.settings.yield_if_possible_below_discomfort = 0.5;
  Constraint constraint2(ConstraintType::Constraint_Type_AVOID_REGION,
                         /*fence_type=*/pb::FenceType::kCrossWalkVRU,
                         "reasoner2", "avoid2");
  const std::vector<Constraint> constraints({constraint1, constraint2});
  // Case: both decisions are not changed at zero discomfort.
  std::vector<Decision> zero_discomfort_decisions;
  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/false,
      /*for_rm=*/false, /*constraint_indices_to_explore_yield=*/{}, constraints,
      zero_discomfort_decisions, mutable_gap_align_info_map_);
  EXPECT_EQ(zero_discomfort_decisions[0], Decision::NOT_DECIDED);
  EXPECT_EQ(zero_discomfort_decisions[1], Decision::NOT_DECIDED);
  // Case: one decision is ignored at high discomfort.
  std::vector<Decision> high_discomfort_decisions;
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.6,
      /*elevated_discomfort=*/0.6, /*for_gap_align=*/false,
      /*for_rm=*/false, /*constraint_indices_to_explore_yield=*/{}, constraints,
      high_discomfort_decisions, mutable_gap_align_info_map_);
  EXPECT_EQ(high_discomfort_decisions[0], Decision::IGNORE);
  EXPECT_EQ(high_discomfort_decisions[1], Decision::NOT_DECIDED);
  // Case: constraint2 still is not ignored at max discomfort.
  std::vector<Decision> max_discomfort_decisions;
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/Discomforts::kMax,
      /*elevated_discomfort=*/Discomforts::kMax, /*for_gap_align=*/false,
      /*for_rm=*/false, /*constraint_indices_to_explore_yield=*/{}, constraints,
      max_discomfort_decisions, mutable_gap_align_info_map_);
  EXPECT_EQ(max_discomfort_decisions[0], Decision::IGNORE);
  EXPECT_EQ(max_discomfort_decisions[1], Decision::NOT_DECIDED);
}

TEST_F(InitialDecisionProviderTest,
       InitialDecisionsWithConstraintToExploreYield) {
  // Create two constraints. The first constraint is a no-yield
  // constraint, the second constraint is a normal constraint.
  Constraint constraint1(ConstraintType::Constraint_Type_AVOID_REGION,
                         /*fence_type=*/pb::FenceType::kCrossWalkVRU,
                         "reasoner1", "avoid1");
  constraint1.settings.allow_pass_ignore_starting_at_discomfort =
      std::numeric_limits<double>::infinity();
  constraint1.settings.yield_option =
      PassYieldOption::ConstraintSettings_PassYieldOption_NA;
  Constraint constraint2(ConstraintType::Constraint_Type_AVOID_REGION,
                         /*fence_type=*/pb::FenceType::kCrossWalkVRU,
                         "reasoner2", "avoid2");
  const std::vector<Constraint> constraints({constraint1, constraint2});
  // Case: both decisions are not changed at zero discomfort.
  std::vector<Decision> yield_diversity_for_normal_constraint;
  std::vector<int> constraint_indices_to_explore_yield;
  constraint_indices_to_explore_yield.push_back(1);
  DCHECK(initial_decision_provider_.has_value());
  bool success = initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/false,
      /*for_rm=*/false, constraint_indices_to_explore_yield, constraints,
      yield_diversity_for_normal_constraint, mutable_gap_align_info_map_);
  EXPECT_TRUE(success);
  EXPECT_EQ(yield_diversity_for_normal_constraint[0], Decision::PASS);
  EXPECT_EQ(yield_diversity_for_normal_constraint[1], Decision::YIELD);

  std::vector<Decision> yield_diversity_for_no_yield_constraint;
  constraint_indices_to_explore_yield[0] = 0;
  success = initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/false,
      /*for_rm=*/false, constraint_indices_to_explore_yield, constraints,
      yield_diversity_for_no_yield_constraint, mutable_gap_align_info_map_);
  // We cannot explore yield for a no-yield constraint which
  // does not allow pass ignore.
  EXPECT_FALSE(success);
}

TEST_F(InitialDecisionProviderTest,
       RiskMitigationConstraintWithYieldIfPossible) {
  // Create a risk mitigation constraint with
  // yield_if_possible_below_discomfort = 0.5.
  Constraint constraint(ConstraintType::Constraint_Type_SPEED_OBJECT,
                        /*fence_type=*/pb::FenceType::kCrossWalkVRU, "reasoner",
                        "speed");
  const double kSwitchTime = 3.0;
  const double kRiskDeltaDiscomfort = 0.75;
  const double kYieldIfPossibleDiscomfort = 0.5;
  constraint.settings.risk_mitigation_params.switch_time = kSwitchTime;
  constraint.settings.risk_mitigation_params.risk_delta_discomfort =
      kRiskDeltaDiscomfort;
  constraint.settings.yield_if_possible_below_discomfort =
      kYieldIfPossibleDiscomfort;
  const std::vector<Constraint> constraints({constraint});
  std::vector<int> constraint_indices_to_explore_yield;
  std::vector<Decision> initial_decisions;
  const double cur_discomfort_1 = 0.25;
  const double elevated_discomfort_1 =
      std::min(Discomforts::kMax, cur_discomfort_1 + kRiskDeltaDiscomfort);
  DCHECK(initial_decision_provider_.has_value());
  const bool success_1 = initial_decision_provider_->GetInitialDecisions(
      cur_discomfort_1, elevated_discomfort_1, /*for_gap_align=*/false,
      /*for_rm=*/true, constraint_indices_to_explore_yield, constraints,
      initial_decisions, mutable_gap_align_info_map_);
  // The risk mitigation's initial decision is not decided because the main
  // discomfort is below kYieldIfPossibleDiscomfort.
  EXPECT_TRUE(success_1);
  EXPECT_EQ(initial_decisions[0], Decision::NOT_DECIDED);

  mutable_gap_align_info_map_.clear();
  initial_decisions.clear();
  const double cur_discomfort_2 = 0.5;
  const double elevated_discomfort_2 =
      std::min(Discomforts::kMax, cur_discomfort_2 + kRiskDeltaDiscomfort);
  const bool success_2 = initial_decision_provider_->GetInitialDecisions(
      cur_discomfort_2, elevated_discomfort_2, /*for_gap_align=*/false,
      /*for_rm=*/true, constraint_indices_to_explore_yield, constraints,
      initial_decisions, mutable_gap_align_info_map_);
  // The risk mitigation's initial decision is pass because the main
  // discomfort is at kYieldIfPossibleDiscomfort so it cannot yield.
  EXPECT_TRUE(success_2);
  EXPECT_EQ(initial_decisions[0], Decision::PASS);
}

// Create a constraint which ego cannot pass before it disappears. Test that we
// should have a pass active time when it disappears.
TEST_F(InitialDecisionProviderTest,
       TestPassActiveTimeWhenEgoCannotPassWithShortConstraint) {
  std::vector<Constraint> constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_, &constraints);
  constraint_creator
      .AddGapAlignSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/100.0,
          /*longitudinal_speed=*/ego_speed_,
          /*dt=*/0.1, /*num_states=*/50, /*agent_length=*/5.0,
          pb::FenceType::kGapAlign, pb::ReasonerId::GAP_ALIGN,
          /*constraint_id=*/"obj-1-const-speed",
          /*obj_id=*/1, /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/true)
      .set_pass_if_possible_with_max_speed_accel_jerk(/*max_v=*/15.5,
                                                      /*max_a=*/2.0,
                                                      /*max_j=*/5.0)
      .set_yield_if_possible_with_min_speed_accel(/*yield_min_v=*/5.0,
                                                  /*yield_min_a=*/-2.0)
      .set_max_allowed_extra_time_for_pass(kMaxAllowedExtraTimeForPassInSecond);
  std::vector<Decision> decisions;
  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions,
      mutable_gap_align_info_map_);
  // Due to pass heuristic, we do not pass.
  EXPECT_EQ(decisions.front(), pb::YIELD);
  EXPECT_TRUE(
      math::NearZero(mutable_gap_align_info_map_[0].pass_active_time, 5.0));
  EXPECT_EQ(mutable_gap_align_info_map_[0].pass_active_time_ix, 50);
  EXPECT_TRUE(math::NearZero(mutable_gap_align_info_map_[0].yield_active_time));
  EXPECT_EQ(mutable_gap_align_info_map_[0].yield_active_time_ix, 0);
}

// Create a constraint which ego cannot yield before it disappears. Test that we
// should have a yield active time when it disappears.
TEST_F(InitialDecisionProviderTest,
       TestYieldActiveTimeWhenEgoCannotYieldWithShortConstraint) {
  std::vector<Constraint> constraints;
  ConstraintCreator constraint_creator(ra_to_fb_, ra_to_rb_, &constraints);
  constraint_creator
      .AddGapAlignSpeedConstraintFromConstSpeed(
          /*start_time=*/0.0, /*ra_start_pos=*/-100.0,
          /*longitudinal_speed=*/ego_speed_,
          /*dt=*/0.1, /*num_states=*/50, /*agent_length=*/5.0,
          pb::FenceType::kGapAlign, pb::ReasonerId::GAP_ALIGN,
          /*constraint_id=*/"obj-1-const-speed",
          /*obj_id=*/1, /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/true)
      .set_pass_if_possible_with_max_speed_accel_jerk(/*max_v=*/15.5,
                                                      /*max_a=*/2.0,
                                                      /*max_j=*/5.0)
      .set_yield_if_possible_with_min_speed_accel(/*yield_min_v=*/5.0,
                                                  /*yield_min_a=*/-2.0);
  std::vector<Decision> decisions;
  DCHECK(initial_decision_provider_.has_value());
  initial_decision_provider_->GetInitialDecisions(
      /*cur_discomfort=*/0.0,
      /*elevated_discomfort=*/0.0, /*for_gap_align=*/true, /*for_rm=*/false,
      /*constraint_indices_to_explore_yield=*/{}, constraints, decisions,
      mutable_gap_align_info_map_);
  // We could yield to it after 5.0s.
  EXPECT_EQ(decisions.front(), pb::NOT_DECIDED);
  EXPECT_TRUE(
      math::NearZero(mutable_gap_align_info_map_[0].yield_active_time, 5.0));
  EXPECT_EQ(mutable_gap_align_info_map_[0].yield_active_time_ix, 50);
  EXPECT_TRUE(math::NearZero(mutable_gap_align_info_map_[0].pass_active_time));
  EXPECT_EQ(mutable_gap_align_info_map_[0].pass_active_time_ix, 0);
}

}  // namespace
}  // namespace speed
}  // namespace planner
