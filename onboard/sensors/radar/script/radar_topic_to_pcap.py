#!/usr/bin/env python3
"""
Script for converting Radar Raw Packet in VBag to PCAP file.

How to use:
    cd voyager
    sudo ./bootstrap -y
    source bazel/scripts/setup.sh
    bazel build --config=local --config=dcheck_always_on protobuf_python:protos_python
    python3.10 radar_topic_to_pcap.py -i xxx.vbag
"""

import argparse
import multiprocessing
import os
import time
import struct
import tqdm
import voy_vbag as vbag
from concurrent.futures import ProcessPoolExecutor

IP2MAC = {
    "************": "A0:A1:A2:A3:A4:01",
    "************": "A0:A1:A2:A3:A4:02",
    "************": "A0:A1:A2:A3:A4:03",
    "************": "A0:A1:A2:A3:A4:04",
    "*************": "A0:A1:A2:A3:A4:05",  # TODO: Change to real Mac
    "***********": "01:00:5e:01:03:65",
    "***********": "01:00:5e:01:03:66",
    "***********": "01:00:5e:01:03:67",
    "***********": "01:00:5e:01:03:68",
}

REQUIRED_TOPICS = [
    "/radar_packet_1",
    "/radar_packet_2",
    "/radar_packet_3",
    "/radar_packet_4",
    "/radar_log_packet",
]

NEED_WRITE_TOPICS = [
    "/radar_packet_1",
    "/radar_packet_2",
    "/radar_packet_3",
    "/radar_packet_4",
    "/radar_log_packet_1",
    "/radar_log_packet_2",
    "/radar_log_packet_3",
    "/radar_log_packet_4",
]


def create_pcap_header(pcap_file):
    """Create the PCAP file header."""
    pcap_file.write(
        struct.pack("<I", 0xA1B2C3D4)
    )  # Magic Number (0xa1b2c3d4 means use nanosec timestamp)
    pcap_file.write(struct.pack("<H", 2))  # Major version (2)
    pcap_file.write(struct.pack("<H", 4))  # Minor version (4)
    pcap_file.write(struct.pack("<I", 0))  # GMT to local correction (0)
    pcap_file.write(struct.pack("<I", 0))  # Accuracy of timestamps (0)
    pcap_file.write(struct.pack("<I", 65535))  # Max length of captured packets (65535)
    pcap_file.write(struct.pack("<I", 1))  # Data link type (1 for Ethernet)


def create_packet_header(timestamp_ns, total_packet_size):
    """Create the packet header."""
    timestamp_sec = int(timestamp_ns / 1_000_000_000)
    timestamp_usec = int((timestamp_ns % 1_000_000_000) / 1000)
    # Set same length to pcap 'cap_len' and 'wire_len'.
    return struct.pack(
        "<IIII", timestamp_sec, timestamp_usec, total_packet_size, total_packet_size
    )


def create_ethernet_header(dst_mac, src_mac):
    """Create the Ethernet header."""
    dst = bytes.fromhex(dst_mac.replace(":", ""))
    src = bytes.fromhex(src_mac.replace(":", ""))
    eth_type = struct.pack("!H", 0x0800)  # IPv4
    return dst + src + eth_type


def create_ip_header(src_ip, dst_ip, total_length, protocol=17):
    """Create the IP header."""
    version_ihl = struct.pack("!B", (4 << 4) | 5)  # IPv4, IHL=5
    tos = struct.pack("!B", 0)
    total_length = struct.pack(
        "!H", 20 + 8 + total_length
    )  # IP header + UDP header + data
    identification = struct.pack("!H", 0)  # TODO: Handle identification
    flags_fragment = struct.pack(
        "!H", 0x4000
    )  # 0x4000 = 0b0100000000000000, # DF=1, MF=0
    ttl = struct.pack("!B", 64)  # TTL=64
    protocol = struct.pack("!B", protocol)  # UDP=17
    checksum = struct.pack("!H", 0)  # Set to 0 for now, will calculate later
    # Convert IP addresses to bytes
    src_ip_bytes = bytes([int(x) for x in src_ip.split(".")])
    dst_ip_bytes = bytes([int(x) for x in dst_ip.split(".")])

    header = (
        version_ihl
        + tos
        + total_length
        + identification
        + flags_fragment
        + ttl
        + protocol
        + checksum
        + src_ip_bytes
        + dst_ip_bytes
    )

    def calculate_checksum(data):
        """Calculate the checksum for the IP header."""
        if len(data) % 2 == 1:
            data += b"\0"
        checksum = 0
        for i in range(0, len(data), 2):
            word = (data[i] << 8) + data[i + 1]
            checksum = (checksum + word) & 0xFFFF
        return ~checksum & 0xFFFF

    checksum_value = calculate_checksum(header)
    checksum = struct.pack("!H", checksum_value)
    # Rebuild the header with the correct checksum
    return (
        version_ihl
        + tos
        + total_length
        + identification
        + flags_fragment
        + ttl
        + protocol
        + checksum
        + src_ip_bytes
        + dst_ip_bytes
    )


def create_udp_header(src_port, dst_port, length):
    """Create the UDP header."""
    length = struct.pack("!H", 8 + length)  # UDP header + data length
    checksum = struct.pack("!H", 0)  # UDP checksum can be 0
    return struct.pack("!HH", src_port, dst_port) + length + checksum


def process_packet_batch(batch_packets, header):
    """Process a batch of packets and create the complete packet."""
    result_packets = []
    src_ip = header["src_ip"]
    dst_ip = header["dst_ip"]
    src_port = header["src_port"]
    dst_port = header["dst_port"]

    for timestamp_ns, udp_payload in batch_packets:
        try:
            udp_header = create_udp_header(src_port, dst_port, len(udp_payload))
            ip_header = create_ip_header(src_ip, dst_ip, len(udp_payload))
            eth_header = create_ethernet_header(IP2MAC[dst_ip], IP2MAC[src_ip])
            complete_packet = eth_header + ip_header + udp_header + udp_payload
            packet_header = create_packet_header(timestamp_ns, len(complete_packet))
            result_packets.append((timestamp_ns, packet_header, complete_packet))
        except Exception as e:
            print(f"Exception in processing packet: {e}")

    return result_packets


def write_packets_to_pcap(
    output_path, packet_slots, headers, batch_size=1000, max_workers=None
):
    """Write packets to PCAP file."""
    start_time = time.time()
    if max_workers is None:
        max_workers = multiprocessing.cpu_count()
    print(f"Batch size: {batch_size}, max workers: {max_workers}")

    all_packets = []
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        futures = []

        print("Processing TX packets...")
        for topic in packet_slots["tx"]:
            packets = packet_slots["tx"][topic]
            total_packets = len(packets)
            print(f"Topic: {topic}, found {total_packets} packets, begin to process...")

            for i in range(0, total_packets, batch_size):
                batch = packets[i : i + batch_size]
                futures.append(
                    executor.submit(process_packet_batch, batch, headers["tx"][topic])
                )

        print("Processing RX packets...")
        for topic in packet_slots["rx"]:
            packets = packet_slots["rx"][topic]
            total_packets = len(packets)
            print(f"Topic: {topic}, found {total_packets} packets, begin to process...")

            for i in range(0, total_packets, batch_size):
                batch = packets[i : i + batch_size]
                futures.append(
                    executor.submit(process_packet_batch, batch, headers["rx"][topic])
                )

        with tqdm.tqdm(total=len(futures), desc="Processing batch", unit="") as pbar:
            for future in futures:
                result = future.result()
                all_packets.extend(result)
                pbar.update(1)

    print("Re-sorting by timestamp...")
    all_packets.sort(key=lambda x: x[0])

    with open(output_path, "wb") as pcap_file:
        create_pcap_header(pcap_file)
        for _, packet_header, complete_packet in all_packets:
            pcap_file.write(packet_header)
            pcap_file.write(complete_packet)

    print(f"Write {len(all_packets)} packets, cost {time.time() - start_time:.2f} s.")


def handle_bag(input_bag_path, output_path, batch_size=1000, max_workers=None):
    """Read the input bag file and write packets to PCAP file."""
    with vbag.Bag(input_bag_path, "r") as input_bag:
        packets_slot = {
            "tx": {topic: [] for topic in NEED_WRITE_TOPICS},
            "rx": {topic: [] for topic in NEED_WRITE_TOPICS},
        }
        headers = {
            "tx": {topic: {} for topic in NEED_WRITE_TOPICS},
            "rx": {topic: {} for topic in NEED_WRITE_TOPICS},
        }
        msg_count = {}
        for topic, msg, timestamp in input_bag.read_messages(
            topics=list(REQUIRED_TOPICS), raw=False
        ):
            msg_count[topic] = msg_count.get(topic, 0) + 1
            src_ip = msg.header.meta["src_ip"]

            if src_ip == "*************":
                headers["tx"][topic].update(
                    {
                        "msg_time": timestamp,
                        "type": msg.header.type,
                        "src_ip": src_ip,
                        "dst_ip": msg.header.meta["dst_ip"],
                        "src_port": int(msg.header.meta["src_port"]),
                        "dst_port": int(msg.header.meta["dst_port"]),
                    }
                )
                packets_slot["tx"][topic].extend(
                    [(p.timestamp_ns, p.eth_packet) for p in msg.packets]
                )
            else:
                if topic == "/radar_log_packet":
                    if src_ip == "************":
                        topic = "/radar_log_packet_1"
                    elif src_ip == "************":
                        topic = "/radar_log_packet_2"
                    elif src_ip == "************":
                        topic = "/radar_log_packet_3"
                    elif src_ip == "************":
                        topic = "/radar_log_packet_4"
                headers["rx"][topic].update(
                    {
                        "msg_time": timestamp,
                        "type": msg.header.type,
                        "src_ip": src_ip,
                        "dst_ip": msg.header.meta["dst_ip"],
                        "src_port": int(msg.header.meta["src_port"]),
                        "dst_port": int(msg.header.meta["dst_port"]),
                    }
                )
                packets_slot["rx"][topic].extend(
                    [(p.timestamp_ns, p.eth_packet) for p in msg.packets]
                )

    tx_packets = sum(len(packets) for packets in packets_slot["tx"].values())
    rx_packets = sum(len(packets) for packets in packets_slot["rx"].values())

    print(f"Read {input_bag_path} successfully:")
    print(f"  - Total packets: {tx_packets + rx_packets}")
    print(f"  - TX packets: {tx_packets}")
    print(f"  - RX packets: {rx_packets}")
    print(f"  - Message count by topic: {msg_count}")
    write_packets_to_pcap(output_path, packets_slot, headers, batch_size, max_workers)
    output_size = os.path.getsize(output_path)
    input_size = os.path.getsize(input_bag_path)
    print(f"PCAP file created at: {output_path}")
    print(
        f"File sizes comparison:\n"
        f"  - PCAP file: {output_size / (1024*1024):.2f} MB\n"
        f"  - VBag file: {input_size / (1024*1024):.2f} MB\n"
        f"  - Ratio: {output_size / input_size:.2f}x"
    )


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Transform radar packets topics in vbag to single pcap file."
    )
    parser.add_argument("-i", "--input_bag", help="Input bag file path", required=True)
    parser.add_argument(
        "-o", "--output", help="Output pcap file path, default is [input_bag].pcap"
    )
    parser.add_argument(
        "-b",
        "--batch_size",
        type=int,
        default=1000,
        help="Batch size for processing packets, default is 1000",
    )
    parser.add_argument(
        "-w", "--workers", type=int, help="Process workers, default is CPU count"
    )
    args = parser.parse_args()

    output_pcap_path = (
        args.output if args.output else args.input_bag.replace(".vbag", ".pcap")
    )
    handle_bag(args.input_bag, output_pcap_path, args.batch_size, args.workers)
