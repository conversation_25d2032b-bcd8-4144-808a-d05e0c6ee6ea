#include "planner/speed/util/precedence/road_precedence_util.h"

#include <algorithm>
#include <memory>
#include <optional>
#include <utility>
#include <vector>

#include <gtest/gtest.h>

#include "planner/planning_gflags.h"
#include "planner/speed/reasoning/test/reasoning_test_fixture.h"
#include "planner/speed/reasoning_input/traffic_rules/traffic_light_traffic_rule.h"
#include "planner/speed/reasoning_input/trajectory_info.h"
#include "planner/world_model/snapshot/snapshot.h"
#include "planner_protos/behavior_common_type.pb.h"
#include "voy_protos/traffic_light.pb.h"

namespace planner {
namespace speed {
namespace util {
class RoadPrecedenceUtilTest : public ::testing::Test,
                               public ReasoningTestFixture {
 public:
  RoadPrecedenceUtilTest() {
    // Currently this test uses default map(Fremont), you can also use other
    // maps by moving this set up map step into each individual test. Now we
    // also support scene maps(See ReasoningTestFeature::SetUpSceneMap. You can
    // check ReasoningTestFixtureTest for some examples).
    SetUpRegionMap(hdmap::test_util::kFremontData);
  }
};

std::vector<OverlapRegionReference> GetOverlapRegionReference(
    const std::vector<pb::OverlapRegion>& overlap_regions) {
  std::vector<OverlapRegionReference> trajectory_overlap_regions;
  std::for_each(
      overlap_regions.begin(), overlap_regions.end(),
      [&trajectory_overlap_regions](const pb::OverlapRegion& overlap_region) {
        trajectory_overlap_regions.push_back(std::cref(overlap_region));
      });
  return trajectory_overlap_regions;
}

std::vector<std::pair<int64_t, math::Range<double>>>
GetLaneSequenceIdRaArclengthRanges(const TrajectoryInfo& trajectory_info) {
  std::vector<std::pair<int64_t, math::Range<double>>>
      lane_sequence_id_ra_arclength_ranges;
  lane_sequence_id_ra_arclength_ranges.reserve(
      trajectory_info.lane_sequence_iterator().size());
  for (const pnc_map::Lane* lane : trajectory_info.lane_sequence_iterator()) {
    const double lane_start_ra_arclength =
        trajectory_info.path().GetProjectionArcLength(
            lane->center_line().GetStartPoint(), math::pb::kForbid) -
        trajectory_info.plan_init_ra_arc_length_on_extended_path();
    const double lane_end_ra_arclength =
        trajectory_info.path().GetProjectionArcLength(
            lane->center_line().GetEndPoint(), math::pb::kForbid) -
        trajectory_info.plan_init_ra_arc_length_on_extended_path();
    lane_sequence_id_ra_arclength_ranges.push_back(std::make_pair(
        lane->id(),
        math::Range<double>(lane_start_ra_arclength, lane_end_ra_arclength)));
  }
  return lane_sequence_id_ra_arclength_ranges;
}

using OverlapRegionReference = std::reference_wrapper<const pb::OverlapRegion>;

TEST_F(RoadPrecedenceUtilTest, NoOverLappedAgentTrajectoryTest) {
  SetEgoPose(/*lane_id=*/104401, /*portion=*/0.0);
  CreatePathWithLaneSequence({104401, 97551, 97549, 16907},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);
  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/97145, /*portion=*/0.5,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/0.0);
  AddStationaryPredictedTrajectory(/*lane_id=*/97145, /*portion=*/0.5,
                                   /*likelihood=*/0.9, /*traj_id=*/1, agent);

  Update();

  EXPECT_EQ(1, object_prediction_map().size());
  EXPECT_FALSE(object_prediction_map().find(object_id) ==
               object_prediction_map().end());
  EXPECT_FALSE(object_prediction_map().at(object_id).empty());

  const planner::pb::DecoupledManeuverSeed previous_iter_seed;
  // Construct LaneSequenceTrafficRules instance.
  traffic_rules::LaneSequenceTrafficRules lane_sequence_traffic_rules(
      path(), lane_sequence_iterator(),
      *world_model().GetLatestJointPncMapService(),
      /*lane_change_instance=*/std::nullopt,
      world_model().traffic_light_detection(), previous_iter_seed,
      /*plan_init_ra_arc_length_on_extended_path=*/0,
      world_model().robot_state().plan_init_state_snapshot().timestamp(),
      world_model().robot_state().region(), motion_mode());

  EXPECT_TRUE(object_proximity_infos().find(object_id) !=
              object_proximity_infos().end());

  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      {}, {}, reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN, std::nullopt,
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);

  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::UNKNOWN);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::NoOverlapWithEgo);
}

TEST_F(RoadPrecedenceUtilTest, EgoMainToSidePrecedenceTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kSideToMainRoad);
  // Ego is on the MAIN_TO_SIDE_ROAD.
  SetEgoPose(/*lane_id=*/188823, /*portion=*/0.5);
  CreateLaneFollowPath();
  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/188801, /*portion=*/0.1,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/6.0);
  // Agent is on the SIDE_ROAD.
  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/188801, /*portion=*/0.1,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/188801, /*portion=*/0.3,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/188801, /*portion=*/0.6,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/188801, /*portion=*/0.9,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};
  AddMultiPointPredictedTrajectory(waypoints, /*likelihood=*/0.9,
                                   /*traj_id=*/1, agent);
  Update();

  EXPECT_EQ(1, object_prediction_map().size());
  EXPECT_FALSE(object_prediction_map().find(object_id) ==
               object_prediction_map().end());
  EXPECT_FALSE(object_prediction_map().at(object_id).empty());

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  // This is the precedence result of the Agent.
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);

  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::HIGHER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::MergingSection);
}

TEST_F(RoadPrecedenceUtilTest, EgoGreenLightStraightThroughTest) {
  SetEgoPose(/*lane_id=*/16867, /*portion=*/0.5, /*speed=*/10.0);
  CreatePathWithLaneSequence({16867, 108357, 16885},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);
  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/108347, /*portion=*/0.8,
               /*length=*/2.5, /*width=*/1.5,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/108347,
                                 /*start_portion=*/0.8,
                                 /*end_lane_id=*/16961, /*end_portion=*/0.0,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);

  // Add traffic light to world model.
  traffic_lights_.clear_traffic_lights();
  AddTrafficLight(/*timestamp=*/timestamp(), /*sign_id=*/6829,
                  /*color=*/voy::TrafficLight_Color_GREEN);
  UpdateWorldModel();

  Update();

  EXPECT_EQ(1, object_prediction_map().size());
  EXPECT_FALSE(object_prediction_map().find(object_id) ==
               object_prediction_map().end());
  EXPECT_FALSE(object_prediction_map().at(object_id).empty());
  EXPECT_FALSE(overlap_regions().empty());

  EXPECT_TRUE(object_proximity_infos().find(object_id) !=
              object_proximity_infos().end());

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);

  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::LOWER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::KnownTrafficLight);
}

TEST_F(RoadPrecedenceUtilTest,
       EgoStraightThroughJunctionWithLargeTiltingAngleTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kNoWatchLineJunction);
  SetEgoPose(/*lane_id=*/63697, /*portion=*/0.99, /*speed=*/10.0);
  CreatePathWithLaneSequence({63697, 67281, 63081},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);
  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/63699, /*portion=*/0.99,
               /*length=*/2.5, /*width=*/1.5,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/63699,
                                 /*start_portion=*/0.99,
                                 /*end_lane_id=*/63081, /*end_portion=*/0.0,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);

  // Add traffic light to world model.
  traffic_lights_.clear_traffic_lights();
  AddTrafficLight(/*timestamp=*/timestamp(), /*sign_id=*/
                  10418,
                  /*color=*/voy::TrafficLight_Color_GREEN);
  UpdateWorldModel();

  Update();

  EXPECT_EQ(1, object_prediction_map().size());
  EXPECT_FALSE(object_prediction_map().find(object_id) ==
               object_prediction_map().end());
  EXPECT_FALSE(object_prediction_map().at(object_id).empty());
  EXPECT_FALSE(overlap_regions().empty());

  EXPECT_TRUE(object_proximity_infos().find(object_id) !=
              object_proximity_infos().end());

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);

  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::HIGHER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::KnownTrafficLight);
  EXPECT_TRUE(result.inferred_precedence_info.inference_source ==
              planner::pb::PrecedenceInferenceSource::StraightMergeInJunction);
}

TEST_F(RoadPrecedenceUtilTest, EgoUnknownLightBeyondNoReturnTest) {
  SetEgoPose(/*lane_id=*/108357, /*portion=*/0.7, /*speed=*/10.0);
  CreatePathWithLaneSequence({108357, 16885},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);
  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/108347, /*portion=*/0.8,
               /*length=*/2.5, /*width=*/1.5,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/108347,
                                 /*start_portion=*/0.8,
                                 /*end_lane_id=*/16961, /*end_portion=*/0.0,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);

  // Add traffic light to world model.
  traffic_lights_.clear_traffic_lights();
  AddTrafficLight(/*timestamp=*/timestamp(), /*sign_id=*/6829,
                  /*color=*/voy::TrafficLight_Color_UNKNOWN_COLOR);
  UpdateWorldModel();

  Update();

  EXPECT_EQ(1, object_prediction_map().size());
  EXPECT_FALSE(object_prediction_map().find(object_id) ==
               object_prediction_map().end());
  EXPECT_FALSE(object_prediction_map().at(object_id).empty());
  EXPECT_FALSE(overlap_regions().empty());

  EXPECT_TRUE(object_proximity_infos().find(object_id) !=
              object_proximity_infos().end());

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);

  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::LOWER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::KnownTrafficLight);
}

TEST_F(RoadPrecedenceUtilTest, EgoUnprotectedLeftTurnTest) {
  SetEgoPose(/*lane_id=*/16863, /*portion=*/0.5, /*speed=*/10.0);
  CreatePathWithLaneSequence({16863, 16965, 16961},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);
  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/16913, /*portion=*/0.0,
               /*length=*/2.5, /*width=*/1.5,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/16913,
                                 /*start_portion=*/0.0,
                                 /*end_lane_id=*/17049, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);

  // Add traffic light to world model.
  traffic_lights_.clear_traffic_lights();
  AddTrafficLight(/*timestamp=*/timestamp(), /*sign_id=*/6829,
                  /*color=*/voy::TrafficLight_Color_GREEN);
  UpdateWorldModel();

  Update();

  EXPECT_EQ(1, object_prediction_map().size());
  EXPECT_FALSE(object_prediction_map().find(object_id) ==
               object_prediction_map().end());
  EXPECT_FALSE(object_prediction_map().at(object_id).empty());
  EXPECT_FALSE(overlap_regions().empty());

  EXPECT_TRUE(object_proximity_infos().find(object_id) !=
              object_proximity_infos().end());

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);

  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::HIGHER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::KnownTrafficLight);
}

TEST_F(RoadPrecedenceUtilTest,
       EgoStraightThroughAgentRightTurnWithoutSignTest) {
  SetEgoPose(/*lane_id=*/97015, /*portion=*/0.5, /*speed=*/10.0);
  CreatePathWithLaneSequence({97015, 16881, 16871},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);
  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/108207, /*portion=*/0.5,
               /*length=*/2.5, /*width=*/1.5,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/108207,
                                 /*start_portion=*/0.5,
                                 /*end_lane_id=*/108213, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);
  Update();

  EXPECT_EQ(1, object_prediction_map().size());
  EXPECT_FALSE(object_prediction_map().find(object_id) ==
               object_prediction_map().end());
  EXPECT_FALSE(object_prediction_map().at(object_id).empty());
  EXPECT_FALSE(overlap_regions().empty());

  EXPECT_TRUE(object_proximity_infos().find(object_id) !=
              object_proximity_infos().end());

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);

  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::LOWER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::NoTrafficSign);
}

TEST_F(RoadPrecedenceUtilTest, ZipMergeInJunctionWithoutSignTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kZipMergeInNoSignalJunction);
  SetEgoPose(/*lane_id=*/111985, /*portion=*/0.9);
  CreatePathWithLaneSequence({111985, 112037, 112011},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);
  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/111989, /*portion=*/0.8,
               /*length=*/2.5, /*width=*/1.5,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/111989,
                                 /*start_portion=*/0.8,
                                 /*end_lane_id=*/112035, /*end_portion=*/0.5,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);
  Update();

  EXPECT_EQ(1, object_prediction_map().size());
  EXPECT_FALSE(object_prediction_map().find(object_id) ==
               object_prediction_map().end());
  EXPECT_FALSE(object_prediction_map().at(object_id).empty());
  EXPECT_FALSE(overlap_regions().empty());

  EXPECT_TRUE(object_proximity_infos().find(object_id) !=
              object_proximity_infos().end());

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);

  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::HIGHER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::NoTrafficSign);
  EXPECT_TRUE(result.inferred_precedence_info.inference_source ==
              planner::pb::PrecedenceInferenceSource::StraightMergeInJunction);
}

TEST_F(RoadPrecedenceUtilTest, ZipMergeLowerTest) {
  SetEgoPose(/*lane_id=*/97149, /*portion=*/0.5, /*speed=*/10.0);
  CreatePathWithLaneSequence({97149, 97153, 97157},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);
  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/97155, /*portion=*/0.5,
               /*length=*/2.5, /*width=*/1.5,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/97155,
                                 /*start_portion=*/0.5,
                                 /*end_lane_id=*/97157, /*end_portion=*/0.0,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);
  Update();

  EXPECT_EQ(1, object_prediction_map().size());
  EXPECT_FALSE(object_prediction_map().find(object_id) ==
               object_prediction_map().end());
  EXPECT_FALSE(object_prediction_map().at(object_id).empty());
  EXPECT_FALSE(overlap_regions().empty());

  EXPECT_TRUE(object_proximity_infos().find(object_id) !=
              object_proximity_infos().end());

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);

  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::LOWER);

  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::MergingSection);
}

TEST_F(RoadPrecedenceUtilTest, ZipMergeHigherTest) {
  SetEgoPose(/*lane_id=*/108313, /*portion=*/0.5, /*speed=*/10.0);
  CreatePathWithLaneSequence({108313, 108319},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);
  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/108315, /*portion=*/0.5,
               /*length=*/2.5, /*width=*/1.5,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/108315,
                                 /*start_portion=*/0.5,
                                 /*end_lane_id=*/108319, /*end_portion=*/0.0,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);
  Update();

  EXPECT_EQ(1, object_prediction_map().size());
  EXPECT_FALSE(object_prediction_map().find(object_id) ==
               object_prediction_map().end());
  EXPECT_FALSE(object_prediction_map().at(object_id).empty());
  EXPECT_FALSE(overlap_regions().empty());

  EXPECT_TRUE(object_proximity_infos().find(object_id) !=
              object_proximity_infos().end());

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);

  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::HIGHER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::MergingSection);
}

TEST_F(RoadPrecedenceUtilTest, ZipMergeByBuslaneHigherTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kMergeByBuslane);
  SetEgoPose(/*lane_id=*/156336, /*portion=*/0.2);
  CreatePathWithLaneSequence({156336, 156496},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/198045, /*portion=*/0.9,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/198045,
                                 /*start_portion=*/0.9,
                                 /*end_lane_id=*/156335, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);
  Update();
  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  EXPECT_EQ(1, object_prediction_map().size());
  EXPECT_FALSE(object_prediction_map().find(object_id) ==
               object_prediction_map().end());
  EXPECT_FALSE(object_prediction_map().at(object_id).empty());
  EXPECT_FALSE(overlap_regions().empty());

  EXPECT_TRUE(object_proximity_infos().find(object_id) !=
              object_proximity_infos().end());

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);

  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::HIGHER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::MergingSection);
}

TEST_F(RoadPrecedenceUtilTest, MergeAfterRightExclusiveTurnTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kMergeAfterExclusiveRightTurn);
  SetEgoPose(/*lane_id=*/178719, /*portion=*/0.1);
  CreatePathWithLaneSequence({178719, 180218, 180214},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);
  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/153608, /*portion=*/0.5,
               /*length=*/2.5, /*width=*/1.5,
               /*velocity=*/10.0);
  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/153608, /*portion=*/0.5,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/1.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/180214, /*portion=*/0.5,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/1.0,
                               /*theta_shift=*/0.0)};
  AddMultiPointPredictedTrajectory(waypoints, /*likelihood=*/0.9,
                                   /*traj_id=*/1, agent);

  Update();

  EXPECT_EQ(1, object_prediction_map().size());
  EXPECT_FALSE(object_prediction_map().find(object_id) ==
               object_prediction_map().end());
  EXPECT_FALSE(object_prediction_map().at(object_id).empty());
  EXPECT_FALSE(overlap_regions().empty());

  EXPECT_TRUE(object_proximity_infos().find(object_id) !=
              object_proximity_infos().end());

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);

  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::HIGHER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::MergingSection);
}

TEST_F(RoadPrecedenceUtilTest, AgentCutInAtRightExclusiveTurnTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kMergeAfterExclusiveRightTurn);
  SetEgoPose(/*lane_id=*/178715, /*portion=*/0.1);
  CreatePathWithLaneSequence({178715, 178719, 180218},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);
  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/178717, /*portion=*/0.5,
               /*length=*/2.5, /*width=*/1.5,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/178717,
                                 /*start_portion=*/0.5,
                                 /*end_lane_id=*/178715, /*end_portion=*/0.9,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);

  Update();

  EXPECT_EQ(1, object_prediction_map().size());
  EXPECT_FALSE(object_prediction_map().find(object_id) ==
               object_prediction_map().end());
  EXPECT_FALSE(object_prediction_map().at(object_id).empty());
  EXPECT_FALSE(overlap_regions().empty());

  EXPECT_TRUE(object_proximity_infos().find(object_id) !=
              object_proximity_infos().end());

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);

  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::LOWER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::AlreadyInLane);
}

TEST_F(RoadPrecedenceUtilTest, EgoThroughBusBulbTest) {
  SetEgoPose(/*lane_id=*/3847, /*portion=*/0.5, /*speed=*/10.0);
  CreatePathWithLaneSequence({3847, 108317, 108321},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);
  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/120685, /*portion=*/0.8,
               /*length=*/2.5, /*width=*/1.5,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/120685,
                                 /*start_portion=*/0.8,
                                 /*end_lane_id=*/108321, /*end_portion=*/0.0,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);
  Update();

  EXPECT_EQ(1, object_prediction_map().size());
  EXPECT_FALSE(object_prediction_map().find(object_id) ==
               object_prediction_map().end());
  EXPECT_FALSE(object_prediction_map().at(object_id).empty());
  EXPECT_FALSE(overlap_regions().empty());

  EXPECT_TRUE(object_proximity_infos().find(object_id) !=
              object_proximity_infos().end());

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);

  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::LOWER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::MergingSection);
}

TEST_F(RoadPrecedenceUtilTest, AgentThroughBusBulbTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kBusBulb);
  SetEgoPose(/*lane_id=*/129495, /*portion=*/0.5,
             /*speed=*/10.0);
  CreatePathWithLaneSequence({129495, 127491},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/129489, /*portion=*/0.5,
               /*length=*/2.5, /*width=*/1.5,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/129489,
                                 /*start_portion=*/0.5,
                                 /*end_lane_id=*/129497, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);
  Update();

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);

  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::LOWER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::MergingSection);
}

TEST_F(RoadPrecedenceUtilTest, AgentPassingBusBulbTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kBusBulb);
  SetEgoPose(/*lane_id=*/129495, /*portion=*/0.5,
             /*speed=*/10.0);
  CreatePathWithLaneSequence({129495, 127491},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/129493, /*portion=*/0.5,
               /*length=*/2.5, /*width=*/1.5,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/129493,
                                 /*start_portion=*/0.5,
                                 /*end_lane_id=*/127491, /*end_portion=*/0.2,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);
  Update();

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);

  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::LOWER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::AlreadyInLane);
}

TEST_F(RoadPrecedenceUtilTest, EgoStraightFlashingYellowLight) {
  SetUpSceneMap(hdmap::test_util::SceneType::kJunction);
  SetEgoPose(/*lane_id=*/9711, /*portion=*/0.5,
             /*speed=*/10.0);
  CreateLaneFollowPath();

  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/14045, /*portion=*/0.1,
               /*length=*/2.5, /*width=*/1.5,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(
      /*start_lane_id=*/14045,
      /*start_portion=*/0.1,
      /*end_lane_id=*/14045, /*end_portion=*/0.9,
      /*likelihood=*/0.9, /*traj_id=*/1, agent);

  // Add traffic light to world model.
  traffic_lights_.clear_traffic_lights();
  AddTrafficLight(/*timestamp=*/timestamp(), /*sign_id=*/3057,
                  /*color=*/voy::TrafficLight_Color_YELLOW,
                  /*type=*/voy::TrafficLight_Type_STRAIGHT,
                  /*countdown_number=*/-1, /*is_flashing=*/true);
  UpdateWorldModel();
  Update();

  EXPECT_EQ(1, object_prediction_map().size());
  EXPECT_FALSE(object_prediction_map().find(object_id) ==
               object_prediction_map().end());
  EXPECT_FALSE(object_prediction_map().at(object_id).empty());
  EXPECT_FALSE(overlap_regions().empty());

  EXPECT_TRUE(object_proximity_infos().find(object_id) !=
              object_proximity_infos().end());

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);

  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::EQUAL);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::NoTrafficSign);
}

TEST_F(RoadPrecedenceUtilTest, EgoRightTurnYellowLight) {
  SetUpSceneMap(hdmap::test_util::SceneType::kRoundabout);
  SetEgoPose(/*lane_id=*/152812, /*portion=*/0.2,
             /*speed=*/10.0);
  CreateLaneFollowPath();

  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/152799, /*portion=*/0.1,
               /*length=*/2.5, /*width=*/1.5,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(
      /*start_lane_id=*/152799,
      /*start_portion=*/0.1,
      /*end_lane_id=*/152799, /*end_portion=*/0.9,
      /*likelihood=*/0.9, /*traj_id=*/1, agent);

  // Add traffic light to world model.
  traffic_lights_.clear_traffic_lights();
  AddTrafficLight(/*timestamp=*/timestamp(), /*sign_id=*/20219,
                  /*color=*/voy::TrafficLight_Color_YELLOW,
                  /*type=*/voy::TrafficLight_Type_RIGHT,
                  /*countdown_number=*/-1, /*is_flashing=*/true);
  UpdateWorldModel();
  Update();

  EXPECT_EQ(1, object_prediction_map().size());
  EXPECT_FALSE(object_prediction_map().find(object_id) ==
               object_prediction_map().end());
  EXPECT_FALSE(object_prediction_map().at(object_id).empty());
  EXPECT_FALSE(overlap_regions().empty());

  EXPECT_TRUE(object_proximity_infos().find(object_id) !=
              object_proximity_infos().end());

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);

  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::HIGHER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::NoTrafficSign);
}

TEST_F(RoadPrecedenceUtilTest, EgoRightTurnWithAgent) {
  SetUpSceneMap(hdmap::test_util::SceneType::kJunction);
  SetEgoPose(/*lane_id=*/13289, /*portion=*/0.3,
             /*speed=*/10.0);
  CreateLaneFollowPath();

  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/13289, /*portion=*/0.2,
               /*length=*/2.5, /*width=*/1.5,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(
      /*start_lane_id=*/13289,
      /*start_portion=*/0.2,
      /*end_lane_id=*/13289, /*end_portion=*/0.9,
      /*likelihood=*/0.9, /*traj_id=*/1, agent);

  // Clear traffic light to world model.
  traffic_lights_.clear_traffic_lights();

  UpdateWorldModel();
  Update();

  EXPECT_EQ(1, object_prediction_map().size());
  EXPECT_FALSE(object_prediction_map().find(object_id) ==
               object_prediction_map().end());
  EXPECT_FALSE(object_prediction_map().at(object_id).empty());
  EXPECT_FALSE(overlap_regions().empty());

  EXPECT_TRUE(object_proximity_infos().find(object_id) !=
              object_proximity_infos().end());

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);

  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::EQUAL);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::NoTrafficSign);
}

TEST_F(RoadPrecedenceUtilTest, EgoPullingOutTest) {
  SetUpSceneMap(hdmap::test_util::kMerge);
  SetEgoPose(/*lane_id=*/145495, /*portion=*/0.7, /*speed=*/10.0);
  CreatePathWithLaneSequence({145495, 145502},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);
  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/145501, /*portion=*/0.2,
               /*length=*/2.5, /*width=*/1.5,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/145501,
                                 /*start_portion=*/0.2,
                                 /*end_lane_id=*/128483, /*end_portion=*/0.0,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);
  Update();
  mutable_trajectory_info().set_is_pull_out_jump_out(true);

  EXPECT_EQ(1, object_prediction_map().size());
  EXPECT_FALSE(object_prediction_map().find(object_id) ==
               object_prediction_map().end());
  EXPECT_FALSE(object_prediction_map().at(object_id).empty());
  EXPECT_FALSE(overlap_regions().empty());

  EXPECT_TRUE(object_proximity_infos().find(object_id) !=
              object_proximity_infos().end());

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);

  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::HIGHER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::PullOverPullOut);
}

TEST_F(RoadPrecedenceUtilTest, EgoPullingOverTest) {
  SetUpSceneMap(hdmap::test_util::kMerge);
  SetEgoPose(/*lane_id=*/145503, /*portion=*/0.7, /*speed=*/10.0);
  CreatePathWithLaneSequence({145503, 145495},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);
  SetBehaviorType(planner::pb::BehaviorType::DECOUPLED_PULL_OVER);
  SetPullOverDestination(/*portion=*/1.0, /*is_pull_over_jump_in=*/true);
  SetPullOverStatus(planner::pull_over::PullOverStatus(
      /*should_trigger_pull_over=*/true,
      /*execution_state=*/planner::pb::PullOverExecutionState::EXECUTE,
      /*pull_over_progress=*/planner::pb::PullOverProgress::kStarted,
      /*is_in_abort_recovery=*/false));
  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/145495, /*portion=*/0.2,
               /*length=*/2.5, /*width=*/1.5,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/145495,
                                 /*start_portion=*/0.2,
                                 /*end_lane_id=*/145495, /*end_portion=*/0.0,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);
  Update();

  EXPECT_EQ(1, object_prediction_map().size());
  EXPECT_FALSE(object_prediction_map().find(object_id) ==
               object_prediction_map().end());
  EXPECT_FALSE(object_prediction_map().at(object_id).empty());
  EXPECT_FALSE(overlap_regions().empty());

  EXPECT_TRUE(object_proximity_infos().find(object_id) !=
              object_proximity_infos().end());

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);

  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::HIGHER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::PullOverPullOut);
}

TEST_F(RoadPrecedenceUtilTest, EgoGoingThroughForkLane) {
  SetEgoPose(/*lane_id=*/16871, /*portion=*/0.8, /*speed=*/10.0);
  CreatePathWithLaneSequence({16871, 16861, 16863},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);
  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/16879, /*portion=*/0.5,
               /*length=*/2.5, /*width=*/1.5,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/16879,
                                 /*start_portion=*/0.5,
                                 /*end_lane_id=*/16861, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);
  Update();

  EXPECT_EQ(1, object_prediction_map().size());
  EXPECT_FALSE(object_prediction_map().find(object_id) ==
               object_prediction_map().end());
  EXPECT_FALSE(object_prediction_map().at(object_id).empty());
  EXPECT_FALSE(overlap_regions().empty());

  EXPECT_TRUE(object_proximity_infos().find(object_id) !=
              object_proximity_infos().end());

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);

  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::LOWER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::LaneChange);
}

TEST_F(RoadPrecedenceUtilTest, AgentLeavingExitZone) {
  SetUpSceneMap(hdmap::test_util::SceneType::kBusBulb);
  SetEgoPose(/*lane_id=*/129403, /*portion=*/0.5,
             /*speed=*/10.0);
  CreatePathWithLaneSequence({129403, 129399},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent = AddAgentInExitZone(
      voy::perception::ObjectType::VEHICLE, object_id,
      /*zone_id=*/47423, /*portion=*/1.0, /*length=*/2.5, /*width=*/1.5,
      /*velocity=*/10.0);
  AddStraightPredictedTrajectory(
      FindPoseInExitZone(/*zone_id=*/47423, /*portion=*/1.0),
      LanePointToPose(/*lane_id=*/129399, /*portion=*/0.3),
      /*likelihood=*/0.9, /*traj_id=*/1, agent);
  Update();

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);

  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::LOWER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::ExitZone);
}

TEST_F(RoadPrecedenceUtilTest, AgentPassingThroughExitZone) {
  SetUpSceneMap(hdmap::test_util::SceneType::kRoundabout);
  SetEgoPose(/*lane_id=*/152700, /*portion=*/0.1,
             /*speed=*/10.0);
  CreatePathWithLaneSequence({152700, 152693},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, object_id,
      /*lane_id=*/152693, /*portion=*/0.5, /*length=*/2.5, /*width=*/1.5,
      /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/152693,
                                 /*start_portion=*/0.5,
                                 /*end_lane_id=*/152695, /*end_portion=*/1.0,
                                 /*likelihood=*/0.5, /*traj_id=*/1, agent);
  agent.mutable_tracked_object()->set_heading(agent.tracked_object().heading() +
                                              M_PI_2);
  Update();

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);

  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::HIGHER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::AlreadyInLane);
}

// Check out the link to view the illustration of the test case.
// https://cooper.didichuxing.com/shares/uw2NSZlYef5P.
TEST_F(RoadPrecedenceUtilTest, EgoAgentEnteringRoundabout) {
  SetUpSceneMap(hdmap::test_util::SceneType::kRoundabout);
  SetEgoPose(/*lane_id=*/168736, /*portion=*/0.1,
             /*speed=*/10.0);
  CreatePathWithLaneSequence({168736, 152743},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, object_id,
      /*lane_id=*/168737, /*portion=*/0.1, /*length=*/2.5, /*width=*/1.5,
      /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/168737,
                                 /*start_portion=*/0.1,
                                 /*end_lane_id=*/152743, /*end_portion=*/1.0,
                                 /*likelihood=*/0.0, /*traj_id=*/1, agent);
  Update();

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);
  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::EQUAL);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::Roundabout);
}

TEST_F(RoadPrecedenceUtilTest, EgoEnteringAgentInRoundabout) {
  SetUpSceneMap(hdmap::test_util::SceneType::kRoundabout);
  SetEgoPose(/*lane_id=*/168736, /*portion=*/0.1,
             /*speed=*/10.0);
  CreatePathWithLaneSequence({168736, 152743},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, object_id,
      /*lane_id=*/152739, /*portion=*/0.9, /*length=*/2.5, /*width=*/1.5,
      /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/152739,
                                 /*start_portion=*/0.9,
                                 /*end_lane_id=*/152794, /*end_portion=*/1.0,
                                 /*likelihood=*/0.0, /*traj_id=*/1, agent);
  Update();

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);
  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::HIGHER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::Roundabout);
}

// Check out the link to view the illustration of the test case.
// https://cooper.didichuxing.com/shares/y9LqjOjGnUPK.
TEST_F(RoadPrecedenceUtilTest, EgoInAgentEnteringRoundabout) {
  SetUpSceneMap(hdmap::test_util::SceneType::kRoundabout);
  SetEgoPose(/*lane_id=*/152739, /*portion=*/0.1,
             /*speed=*/10.0);
  CreatePathWithLaneSequence({152739, 152794},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, object_id,
      /*lane_id=*/168736, /*portion=*/0.1, /*length=*/2.5, /*width=*/1.5,
      /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/168736,
                                 /*start_portion=*/0.1,
                                 /*end_lane_id=*/168736, /*end_portion=*/1.0,
                                 /*likelihood=*/0.0, /*traj_id=*/1, agent);
  Update();

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);
  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::LOWER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::Roundabout);
}

TEST_F(RoadPrecedenceUtilTest, EgoExitingAgentEnteringRoundabout) {
  SetUpSceneMap(hdmap::test_util::SceneType::kRoundabout);
  SetEgoPose(/*lane_id=*/166410, /*portion=*/0.1,
             /*speed=*/10.0);
  CreatePathWithLaneSequence({166410, 168635},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, object_id,
      /*lane_id=*/152815, /*portion=*/0.1, /*length=*/2.5, /*width=*/1.5,
      /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/152815,
                                 /*start_portion=*/0.1,
                                 /*end_lane_id=*/152815, /*end_portion=*/1.0,
                                 /*likelihood=*/0.0, /*traj_id=*/1, agent);
  Update();

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);
  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::LOWER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::Roundabout);
}

TEST_F(RoadPrecedenceUtilTest, EgoExitingAgentInRoundabout) {
  SetUpSceneMap(hdmap::test_util::SceneType::kRoundabout);
  SetEgoPose(/*lane_id=*/166410, /*portion=*/0.1,
             /*speed=*/10.0);
  CreatePathWithLaneSequence({166410, 168635},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, object_id,
      /*lane_id=*/152805, /*portion=*/0.1, /*length=*/2.5, /*width=*/1.5,
      /*velocity=*/10.0);
  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/152805, /*portion=*/0.1,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/152805, /*portion=*/0.3,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/152805, /*portion=*/0.6,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/152805, /*portion=*/0.9,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};
  AddMultiPointPredictedTrajectory(waypoints, /*likelihood=*/0.9,
                                   /*traj_id=*/1, agent);
  Update();

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);
  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::HIGHER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::Roundabout);
}

TEST_F(RoadPrecedenceUtilTest, EgoAgentExitingRoundabout) {
  SetUpSceneMap(hdmap::test_util::SceneType::kRoundabout);
  SetEgoPose(/*lane_id=*/166410, /*portion=*/0.1,
             /*speed=*/10.0);
  CreatePathWithLaneSequence({166410, 168635},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/166409, /*portion=*/0.1,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/2.0);
  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/166409, /*portion=*/0.1,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/166409, /*portion=*/0.5,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/166410, /*portion=*/0.8,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/166410, /*portion=*/1.0,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};

  AddMultiPointPredictedTrajectory(waypoints, /*likelihood=*/0.9,
                                   /*traj_id=*/1, agent);

  Update();

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);
  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::EQUAL);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::Roundabout);
}

TEST_F(RoadPrecedenceUtilTest, AgentHigherPrecedenceInEgoEncroachmentTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kMergeByBuslane);
  SetEgoPose(/*lane_id=*/198199, /*portion=*/0.1,
             /*speed=*/10.0);
  CreateNudgePathWithSineWave(
      /*lane_sequence_id=*/{198199, 198061, 198045},
      /*start_nudging_lane_id=*/198199, /*end_nudging_lane_id=*/198045,
      /*start_nudging_lane_portion=*/0.5, /*end_nudging_lane_portion=*/0.5,
      /*max_lateral_offset_in_meter=*/2.5,
      /*is_nudging_from_right=*/false, /*is_nudging_back=*/true,
      planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, object_id,
      /*lane_id=*/198193, /*portion=*/0.1, /*length=*/2.5, /*width=*/1.5,
      /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/198193,
                                 /*start_portion=*/0.1,
                                 /*end_lane_id=*/198051, /*end_portion=*/1.0,
                                 /*likelihood=*/0.0, /*traj_id=*/1, agent);
  Update();

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);
  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::HIGHER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::EgoEncroachment);
}

TEST_F(RoadPrecedenceUtilTest, AgentLowerPrecedenceInEgoEncroachmentTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kMergeByBuslane);
  SetEgoPose(/*lane_id=*/198199, /*portion=*/0.1,
             /*speed=*/10.0);
  CreateNudgePathWithSineWave(
      /*lane_sequence_id=*/{198199, 198061, 198045},
      /*start_nudging_lane_id=*/198199, /*end_nudging_lane_id=*/198045,
      /*start_nudging_lane_portion=*/0.5, /*end_nudging_lane_portion=*/0.5,
      /*max_lateral_offset_in_meter=*/2.5,
      /*is_nudging_from_right=*/false, /*is_nudging_back=*/true,
      planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, object_id,
      /*lane_id=*/198059, /*portion=*/0.1, /*length=*/2.5, /*width=*/1.5,
      /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/198059,
                                 /*start_portion=*/0.1,
                                 /*end_lane_id=*/198045, /*end_portion=*/0.5,
                                 /*likelihood=*/0.0, /*traj_id=*/1, agent);
  Update();

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);
  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::LOWER);
  EXPECT_FALSE(result.precedence_source ==
               planner::pb::PrecedenceSource::EgoEncroachment);
}

TEST_F(RoadPrecedenceUtilTest, EgoXLaneNudgeAgentOncomingTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kSideToMainRoad);
  SetEgoPose(/*lane_id=*/174313, /*portion=*/0.1,
             /*speed=*/10.0);
  CreateNudgePathWithSineWave(
      /*lane_sequence_id=*/{174313},
      /*start_nudging_lane_id=*/174313, /*end_nudging_lane_id=*/174313,
      /*start_nudging_lane_portion=*/0.2, /*end_nudging_lane_portion=*/0.8,
      /*max_lateral_offset_in_meter=*/5.0,
      /*is_nudging_from_right=*/false, /*is_nudging_back=*/true,
      planner::pb::ManeuverType::DECOUPLED_FORWARD);

  // Initialize fake path reasoning states.
  planner::pb::IntentionResult& ego_intention = mutable_ego_intention();
  ego_intention.set_homotopy(
      planner::pb::IntentionResult::XLANE_PASS_FROM_LEFT);
  planner::pb::PathReasoningSeed* path_reasoning_seed =
      mutable_path_reasoning_seed();
  path_reasoning_seed->mutable_xlane_nudge_seed()->set_side(
      planner::pb::StuckRegion::kLeft);
  path_reasoning_seed->mutable_xlane_nudge_seed()->set_overtake_mode(
      planner::pb::StuckRegion::kOncomingLane);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, object_id,
      /*lane_id=*/174337, /*portion=*/0.1, /*length=*/2.5, /*width=*/1.5,
      /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/174337,
                                 /*start_portion=*/0.1,
                                 /*end_lane_id=*/174337, /*end_portion=*/1.0,
                                 /*likelihood=*/0.0, /*traj_id=*/1, agent);
  Update();

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);
  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::HIGHER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::EgoEncroachment);
}

TEST_F(RoadPrecedenceUtilTest, EgoXLaneNudgeAgentCrossingTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kSideToMainRoad);
  SetEgoPose(/*lane_id=*/174313, /*portion=*/0.1,
             /*speed=*/10.0);
  CreateNudgePathWithSineWave(
      /*lane_sequence_id=*/{174313},
      /*start_nudging_lane_id=*/174313, /*end_nudging_lane_id=*/174313,
      /*start_nudging_lane_portion=*/0.2, /*end_nudging_lane_portion=*/0.8,
      /*max_lateral_offset_in_meter=*/3.0,
      /*is_nudging_from_right=*/false, /*is_nudging_back=*/true,
      planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, object_id,
      /*lane_id=*/174282, /*portion=*/0.9, /*length=*/2.5, /*width=*/1.5,
      /*velocity=*/10.0);
  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/174282, /*portion=*/0.9,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/174336, /*portion=*/0.1,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/174336, /*portion=*/0.9,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};
  AddMultiPointPredictedTrajectory(waypoints, /*likelihood=*/0.9,
                                   /*traj_id=*/1, agent);

  Update();

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);
  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::LOWER);
  EXPECT_FALSE(result.precedence_source ==
               planner::pb::PrecedenceSource::EgoEncroachment);
}

TEST_F(RoadPrecedenceUtilTest, EgoXLaneNudgeOppositeLaneTogetherWithAgentTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kNoWatchLineJunction);
  SetEgoPose(/*lane_id=*/63695, /*portion=*/0.5,
             /*speed=*/10.0);
  CreateNudgePathWithSineWave(
      /*lane_sequence_id=*/{63695},
      /*start_nudging_lane_id=*/63695, /*end_nudging_lane_id=*/63695,
      /*start_nudging_lane_portion=*/0.6, /*end_nudging_lane_portion=*/0.9,
      /*max_lateral_offset_in_meter=*/5.0,
      /*is_nudging_from_right=*/false, /*is_nudging_back=*/true,
      planner::pb::ManeuverType::DECOUPLED_FORWARD);

  // Initialize fake path reasoning states.
  planner::pb::IntentionResult& ego_intention = mutable_ego_intention();
  ego_intention.set_homotopy(
      planner::pb::IntentionResult::XLANE_PASS_FROM_LEFT);
  planner::pb::PathReasoningSeed* path_reasoning_seed =
      mutable_path_reasoning_seed();
  path_reasoning_seed->mutable_xlane_nudge_seed()->set_side(
      planner::pb::StuckRegion::kLeft);
  path_reasoning_seed->mutable_xlane_nudge_seed()->set_overtake_mode(
      planner::pb::StuckRegion::kOncomingLane);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, object_id,
      /*lane_id=*/63695, /*portion=*/0.45, /*length=*/2.5, /*width=*/1.5,
      /*velocity=*/10.0);
  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/63705, /*portion=*/0.5,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/63705, /*portion=*/0.3,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/63705, /*portion=*/0.1,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};
  AddMultiPointPredictedTrajectory(waypoints, /*likelihood=*/0.9,
                                   /*traj_id=*/1, agent);
  Update();

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos[0];
  EXPECT_TRUE(
      agent_trajectory_info.has_overlap_region_in_encroachment_regions());
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);
  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::EQUAL);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::EgoEncroachment);
}

TEST_F(RoadPrecedenceUtilTest, EgoXLaneNudgeOppositeLaneAgentOncomingTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kNoWatchLineJunction);
  SetEgoPose(/*lane_id=*/63695, /*portion=*/0.5,
             /*speed=*/10.0);
  CreateNudgePathWithSineWave(
      /*lane_sequence_id=*/{63695},
      /*start_nudging_lane_id=*/63695, /*end_nudging_lane_id=*/63695,
      /*start_nudging_lane_portion=*/0.6, /*end_nudging_lane_portion=*/0.9,
      /*max_lateral_offset_in_meter=*/5.0,
      /*is_nudging_from_right=*/false, /*is_nudging_back=*/true,
      planner::pb::ManeuverType::DECOUPLED_FORWARD);

  // Initialize fake path reasoning states.
  planner::pb::IntentionResult& ego_intention = mutable_ego_intention();
  ego_intention.set_homotopy(
      planner::pb::IntentionResult::XLANE_PASS_FROM_LEFT);
  planner::pb::PathReasoningSeed* path_reasoning_seed =
      mutable_path_reasoning_seed();
  path_reasoning_seed->mutable_xlane_nudge_seed()->set_side(
      planner::pb::StuckRegion::kLeft);
  path_reasoning_seed->mutable_xlane_nudge_seed()->set_overtake_mode(
      planner::pb::StuckRegion::kOncomingLane);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, object_id,
      /*lane_id=*/63705, /*portion=*/0.0, /*length=*/2.5, /*width=*/1.5,
      /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/63705,
                                 /*start_portion=*/0,
                                 /*end_lane_id=*/63705, /*end_portion=*/0.6,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);
  Update();

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos[0];
  EXPECT_TRUE(
      agent_trajectory_info.has_overlap_region_in_encroachment_regions());
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);
  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::HIGHER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::EgoEncroachment);
}

TEST_F(RoadPrecedenceUtilTest,
       EgoXLaneNudgeNeighbourLaneAgentDrivingThroughTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kForkMergeByBikelane);
  SetEgoPose(/*lane_id=*/163602, /*portion=*/0.2,
             /*speed=*/10.0);
  CreateNudgePathWithSineWave(
      /*lane_sequence_id=*/{163602},
      /*start_nudging_lane_id=*/163602, /*end_nudging_lane_id=*/163602,
      /*start_nudging_lane_portion=*/0.4, /*end_nudging_lane_portion=*/0.9,
      /*max_lateral_offset_in_meter=*/5.0,
      /*is_nudging_from_right=*/true, /*is_nudging_back=*/true,
      planner::pb::ManeuverType::DECOUPLED_FORWARD);

  // Initialize fake path reasoning states.
  planner::pb::IntentionResult& ego_intention = mutable_ego_intention();
  ego_intention.set_homotopy(
      planner::pb::IntentionResult::XLANE_PASS_FROM_RIGHT);
  planner::pb::PathReasoningSeed* path_reasoning_seed =
      mutable_path_reasoning_seed();
  path_reasoning_seed->mutable_xlane_nudge_seed()->set_side(
      planner::pb::StuckRegion::kRight);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, object_id,
      /*lane_id=*/163603, /*portion=*/0.0, /*length=*/2.5, /*width=*/1.5,
      /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/163603,
                                 /*start_portion=*/0,
                                 /*end_lane_id=*/163603, /*end_portion=*/0.6,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);
  Update();
  // Explicitly set the proximity lateral gap to zero.
  auto iter = mutable_object_proximity_infos().find(object_id);
  speed::pb::ObjectProximityInfo& mutable_proximity_info = iter->second;
  mutable_proximity_info.set_signed_lateral_gap(0.0);

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos[0];
  EXPECT_TRUE(
      agent_trajectory_info.has_overlap_region_in_encroachment_regions());
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);
  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::HIGHER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::EgoEncroachment);
}

TEST_F(RoadPrecedenceUtilTest,
       EgoXLaneNudgingToAreaWithoutLabeledLaneWhileAgentDrivingThroughTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kForkMergeByBikelane);
  SetEgoPoseWoYaw(/*x_pos=*/8222.6, /*y_pos=*/9836.6, /*lane_id=*/163601,
                  /*speed=*/10.0);
  const math::geometry::PolylineCurve2d path_curve{{8222.6, 9836.6},
                                                   {8229.9, 9813.1}};
  CreatePathWithPathCurveAndLaneSequence(path_curve, {163601});

  // Initialize fake path reasoning states.
  planner::pb::IntentionResult& ego_intention = mutable_ego_intention();
  ego_intention.set_homotopy(
      planner::pb::IntentionResult::XLANE_PASS_FROM_RIGHT);
  planner::pb::PathReasoningSeed* path_reasoning_seed =
      mutable_path_reasoning_seed();
  path_reasoning_seed->mutable_xlane_nudge_seed()->set_side(
      planner::pb::StuckRegion::kRight);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, object_id,
      /*lane_id=*/163601, /*portion=*/0.0, /*length=*/2.5, /*width=*/1.5,
      /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/163601,
                                 /*start_portion=*/0.2,
                                 /*end_lane_id=*/163607, /*end_portion=*/0.5,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);
  Update();
  // Explicitly set the proximity lateral gap to zero.
  auto iter = mutable_object_proximity_infos().find(object_id);
  speed::pb::ObjectProximityInfo& mutable_proximity_info = iter->second;
  mutable_proximity_info.set_signed_lateral_gap(0.0);

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos[0];
  EXPECT_TRUE(
      agent_trajectory_info.has_overlap_region_in_encroachment_regions());
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);
  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::HIGHER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::EgoEncroachment);
}

TEST_F(RoadPrecedenceUtilTest, EgoSteppingOnLaneMarkingToOvertake) {
  SetUpSceneMap(hdmap::test_util::SceneType::kMergeByBuslane);
  SetEgoPoseWoYaw(/*x_pos=*/55891.4536, /*Y_pos=*/15012.0, /*lane_id=*/198199,
                  /*speed=*/10.0);
  const math::geometry::PolylineCurve2d path_curve{{55891.4536, 15012.0},
                                                   {55920.57, 15006.5}};
  CreatePathWithPathCurveAndLaneSequence(path_curve, {198199, 198061, 198045});

  ObjectId object_id = 1;
  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::CYCLIST, object_id,
      /*pose=*/{55875.0, 15012.1, -0.152864}, /*length=*/1.5, /*width=*/0.3,
      /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_pose=*/{55875.0, 15012.1, -0.152864},
                                 /*end_pose=*/{55909.99, 15008.5, -0.152864},
                                 /*likelihood=*/0.0, /*traj_id=*/1, agent);
  Update();

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  // Fake info accessed from seed.
  mutable_trajectory_info().set_direction_cross_onto_lane_marking(
      math::pb::kRight);
  mutable_trajectory_info().set_out_lane_encroachment_info_ptr(
      &trajectory_info().left_lane_encroachment_info());
  mutable_trajectory_info()
      .set_max_out_lane_encroached_distance_on_lane_marking(0.9);

  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);
  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::EQUAL);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::AlreadyInLane);
}

TEST_F(RoadPrecedenceUtilTest,
       EgoAndAgentBothEncroachingToTheSameLaneFromOppositeSideTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kMergeByBuslane);
  SetEgoPose(/*lane_id=*/198199, /*portion=*/0.3,
             /*speed=*/10.0);
  CreateNudgePathWithSineWave(
      /*lane_sequence_id=*/{198199},
      /*start_nudging_lane_id=*/198199, /*end_nudging_lane_id=*/198199,
      /*start_nudging_lane_portion=*/0.5, /*end_nudging_lane_portion=*/0.9,
      /*max_lateral_offset_in_meter=*/3.0,
      /*is_nudging_from_right=*/false, /*is_nudging_back=*/true,
      planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, object_id,
      /*lane_id=*/198191, /*portion=*/0.1, /*length=*/2.5, /*width=*/1.5,
      /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/198191,
                                 /*start_portion=*/0.1,
                                 /*end_lane_id=*/198193, /*end_portion=*/0.9,
                                 /*likelihood=*/0.0, /*traj_id=*/1, agent);
  Update();
  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;

  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);
  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::EQUAL);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::EgoEncroachment);
}

TEST_F(RoadPrecedenceUtilTest,
       EgoAndAgentBothEncroachingToTheSameLaneFromSameSideTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kMergeByBuslane);
  SetEgoPose(/*lane_id=*/198199, /*portion=*/0.3,
             /*speed=*/10.0);
  CreateNudgePathWithSineWave(
      /*lane_sequence_id=*/{198199},
      /*start_nudging_lane_id=*/198199, /*end_nudging_lane_id=*/198199,
      /*start_nudging_lane_portion=*/0.5, /*end_nudging_lane_portion=*/0.9,
      /*max_lateral_offset_in_meter=*/3.0,
      /*is_nudging_from_right=*/false, /*is_nudging_back=*/true,
      planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, object_id,
      /*lane_id=*/198203, /*portion=*/0.5, /*length=*/2.5, /*width=*/1.5,
      /*velocity=*/10.0);
  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/198203, /*portion=*/0.5,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/198205, /*portion=*/0.8,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/198193, /*portion=*/0.8,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};
  AddMultiPointPredictedTrajectory(waypoints, /*likelihood=*/0.9,
                                   /*traj_id=*/1, agent);
  Update();
  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;

  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);
  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::EQUAL);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::EgoEncroachment);
}

TEST_F(RoadPrecedenceUtilTest, EgoEncroachingToAgentNotFullyAheadTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kMergeByBuslane);
  SetEgoPose(/*lane_id=*/198199, /*portion=*/0.3,
             /*speed=*/10.0);
  CreateNudgePathWithSineWave(
      /*lane_sequence_id=*/{198199},
      /*start_nudging_lane_id=*/198199, /*end_nudging_lane_id=*/198199,
      /*start_nudging_lane_portion=*/0.5, /*end_nudging_lane_portion=*/0.9,
      /*max_lateral_offset_in_meter=*/3.0,
      /*is_nudging_from_right=*/false, /*is_nudging_back=*/true,
      planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, object_id,
      /*lane_id=*/198193, /*portion=*/0.35, /*length=*/5.0, /*width=*/2.0,
      /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/198193,
                                 /*start_portion=*/0.1,
                                 /*end_lane_id=*/198193, /*end_portion=*/0.9,
                                 /*likelihood=*/0.0, /*traj_id=*/1, agent);
  Update();
  EXPECT_TRUE(!reasoning_inputs().empty());
  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;

  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);
  EXPECT_TRUE(
      !reasoning_inputs().front().reasoning_object.IsBehindEgoLeadingBumper());
  EXPECT_TRUE(!reasoning_inputs().front().reasoning_object.IsFullyAhead());
  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::HIGHER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::EgoEncroachment);
}

TEST_F(RoadPrecedenceUtilTest, EgoAndAgentBothLaneChangingToTheSameLaneTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kForkMergeByBikelane);
  SetEgoPose(/*lane_id=*/163592, /*portion=*/0.4,
             /*speed=*/10.0);
  CreateLaneChangePath(/*source_lane_id=*/163592, /*target_lane_id=*/163593);
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::RIGHT_LANE_CHANGE,
      /*lc_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
      /*start_arclength=*/0.45, 163592, 163593,
      /*is_current_homotopy_lane_change=*/true);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, object_id,
      /*lane_id=*/163594, /*portion=*/0.3, /*length=*/2.5, /*width=*/1.5,
      /*velocity=*/10.0);
  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/163594, /*portion=*/0.3,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/163593, /*portion=*/0.35,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/163593, /*portion=*/0.7,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};

  AddMultiPointPredictedTrajectory(waypoints, /*likelihood=*/0.9,
                                   /*traj_id=*/1, agent);
  Update();

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos[0];
  EXPECT_TRUE(
      agent_trajectory_info.has_overlap_region_in_encroachment_regions());
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);
  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::EQUAL);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::EgoEncroachment);
}

TEST_F(RoadPrecedenceUtilTest, EgoXLaneNudgeNeighbourLaneAlongWithAgentTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kForkMergeByBikelane);
  SetEgoPose(/*lane_id=*/163602, /*portion=*/0.3,
             /*speed=*/10.0);
  CreateNudgePathWithSineWave(
      /*lane_sequence_id=*/{163602},
      /*start_nudging_lane_id=*/163602, /*end_nudging_lane_id=*/163602,
      /*start_nudging_lane_portion=*/0.5, /*end_nudging_lane_portion=*/0.9,
      /*max_lateral_offset_in_meter=*/5.0,
      /*is_nudging_from_right=*/true, /*is_nudging_back=*/true,
      planner::pb::ManeuverType::DECOUPLED_FORWARD);

  // Initialize fake path reasoning states.
  planner::pb::IntentionResult& ego_intention = mutable_ego_intention();
  ego_intention.set_homotopy(
      planner::pb::IntentionResult::XLANE_PASS_FROM_RIGHT);
  planner::pb::PathReasoningSeed* path_reasoning_seed =
      mutable_path_reasoning_seed();
  path_reasoning_seed->mutable_xlane_nudge_seed()->set_side(
      planner::pb::StuckRegion::kRight);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, object_id,
      /*lane_id=*/163602, /*portion=*/0.2, /*length=*/2.5, /*width=*/1.5,
      /*velocity=*/10.0);
  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/163602, /*portion=*/0.2,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/163602, /*portion=*/0.3,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/163603, /*portion=*/0.5,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/163603, /*portion=*/0.9,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};

  AddMultiPointPredictedTrajectory(waypoints, /*likelihood=*/0.9,
                                   /*traj_id=*/1, agent);
  Update();
  // Explicitly set the proximity lateral gap to zero.
  auto iter = mutable_object_proximity_infos().find(object_id);
  speed::pb::ObjectProximityInfo& mutable_proximity_info = iter->second;
  mutable_proximity_info.set_signed_lateral_gap(0.0);

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos[0];
  EXPECT_TRUE(
      agent_trajectory_info.has_overlap_region_in_encroachment_regions());
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);
  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::EQUAL);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::AlreadyInLane);
}

TEST_F(RoadPrecedenceUtilTest, EgoLaneChangeWhileBeingFullyOnSourceLaneTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kForkMergeByBikelane);
  SetEgoPoseWoYaw(/*x_pos=*/8234.60, /*y_pos=*/9819.87, /*lane_id=*/163599,
                  /*speed=*/10.0);
  const math::geometry::PolylineCurve2d path_curve{
      {8232.87, 9833.16}, {8233.33, 9828.24}, {8234.93, 9824.30},
      {8234.60, 9819.87}, {8234.35, 9815.30}, {8232.64, 9814.03}};
  CreatePathWithPathCurveAndLaneSequence(path_curve, {163599, 163600});
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::RIGHT_LANE_CHANGE,
      /*lc_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
      /*start_arclength=*/0.0, 163599, 163600,
      /*is_current_homotopy_lane_change=*/true);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/
               163594, /*portion=*/0.99, /*length=*/2.5, /*width=*/1.5,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/163594,
                                 /*start_portion=*/0.99,
                                 /*end_lane_id=*/163600, /*end_portion=*/0.9,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);
  Update();
  // Explicitly set the proximity lateral gap to zero.
  auto iter = mutable_object_proximity_infos().find(object_id);
  speed::pb::ObjectProximityInfo& mutable_proximity_info = iter->second;
  mutable_proximity_info.set_signed_lateral_gap(0.0);

  const std::vector<OverlapRegionReference>& overlap_regions =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_regions();
  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .overlap_region_infos();
  std::string debug_string;
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos[0];
  EXPECT_TRUE(
      agent_trajectory_info.has_overlap_region_in_encroachment_regions());
  RoadPrecedenceResult result = GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
      trajectory_info(), GetLaneSequenceIdRaArclengthRanges(trajectory_info()),
      overlap_regions, overlap_region_infos,
      reasoning_inputs().front().reasoning_object,
      object_prediction_map().at(object_id)[0],
      planner::pb::RoadPriorityType::UNKNOWN,
      reasoning_inputs()
          .front()
          .agent_trajectory_infos.front()
          .associated_route_opt(),
      *world_context().joint_pnc_map_service(),
      world_context().ra_to_leading_bumper(),
      world_context().ra_to_trailing_bumper_shift(), &debug_string);
  EXPECT_TRUE(result.road_precedence == planner::pb::RoadPrecedence::HIGHER);
  EXPECT_TRUE(result.precedence_source ==
              planner::pb::PrecedenceSource::EgoEncroachment);
}

// TODO(minhanli): Add the unit test (similar to the cn10534114 as shown in
// https://cooper.didichuxing.com/shares/8Zh4zMz3t59n) when the tool for
// creating the nudge in progress path (i.e., ego is already on the encroached
// lane) is available.
TEST_F(RoadPrecedenceUtilTest,
       DISABLED_EgoXLaneNudgeOppositeLaneAgentStayingOnSourceLaneTest) {
  EXPECT_TRUE(false);
}

}  // namespace util
}  // namespace speed
}  // namespace planner
