#ifndef ONBOARD_SYSTEM_SUPERVISOR_FAULT_CLASSIFICATION_ACTION_FACTORY_EXCEPTION_EVENT_STATISTICS_H_
#define ONBOARD_SYSTEM_SUPERVISOR_FAULT_CLASSIFICATION_ACTION_FACTORY_EXCEPTION_EVENT_STATISTICS_H_

#include <gflags/gflags.h>
#include <gtest/gtest_prod.h>
#include <memory>
#include <set>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "action_factory_protos/ss_exception_event_statistics.pb.h"
#include "av_comm/car_id.h"
#include "common/param_getter.h"
#include "common/ros_param_getter.h"
#include "fault_detector_protos/fault_config_status.pb.h"
#include "proto_util/proto_io.h"
#include "restart_protos/restart.pb.h"
#include "ros/param.h"
#include "varch/vnode/message_util.h"
#include "varch/vnode/vnode.h"
#include "voy_protos/fault_level_manifest.pb.h"
#include "voy_protos/health.pb.h"
#include "voy_protos/system_control.pb.h"

namespace system_supervisor {
namespace action_factory {
namespace ss_exception_event_statistics {
DECLARE_bool(ss_exception_event_statistics_enable_filter);

constexpr int kMaxFaultGapNum = 20;
constexpr int kPeriodicIntervalMs = 50;
constexpr int kEventDebounceDurationMs = kMaxFaultGapNum * kPeriodicIntervalMs;

struct ActiveFaultInfo {
  ActiveFaultInfo() = default;

  explicit ActiveFaultInfo(time_t curr_timestamp, pb::Fault fault,
                           std::string fault_event_id,
                           fault_detector::pb::FaultExceptionType type)
      : last_detected_timestamp(curr_timestamp),
        first_detected_timestamp(curr_timestamp),
        fault_event_id(std::move(fault_event_id)),
        original_fault_detail(std::move(fault)),
        fault_type(type) {}

  void UpdateLastDetectedTimestamp(time_t curr_timestamp) {
    was_fault_undetectable = false;
    last_detected_timestamp = curr_timestamp;
  }

  void UpdateDisappearedInfo(
      time_t curr_timestamp, const std::string& vehicle_exception_id,
      const std::set<fault_detector::pb::FaultExceptionType>& fault_types) {
    if (was_fault_undetectable) {
      return;
    }
    was_fault_undetectable = true;
    first_disappeared_timestamp = curr_timestamp;
    vehicle_exception_id_at_first_disappearance =
        std::move(vehicle_exception_id);
    fault_types_at_first_disappearance = std::move(fault_types);
  }

  bool IsFaultTrulyDisappeared(time_t curr_timestamp) const {
    return last_detected_timestamp + kEventDebounceDurationMs < curr_timestamp;
  }

  time_t last_detected_timestamp = 0L;
  time_t first_detected_timestamp = 0L;
  std::string fault_event_id;
  pb::Fault original_fault_detail;
  fault_detector::pb::FaultExceptionType fault_type{};

  bool was_fault_undetectable = false;
  // the timestamp of /system_state which has no this fault
  time_t first_disappeared_timestamp = 0L;
  std::string vehicle_exception_id_at_first_disappearance;
  std::set<fault_detector::pb::FaultExceptionType>
      fault_types_at_first_disappearance;
};

using node_state_type =
    ::action_factory::pb::SsExceptionEventStatistics::FaultCause;

struct NodeEventData {
  node_state_type node_state{};
  std::unordered_map<std::string /*fault_unit*/, ActiveFaultInfo> active_faults;

  NodeEventData() = default;
  explicit NodeEventData(node_state_type curr_node_state)
      : node_state(curr_node_state) {}
  void AddFaultInfo(const std::string& fault_unit, int64_t timestamp,
                    const std::string& fault_event_id, const pb::Fault& fault,
                    fault_detector::pb::FaultExceptionType type) {
    active_faults[fault_unit] =
        ActiveFaultInfo(timestamp, fault, fault_event_id, type);
  }
};

class FaultLevelManifest {
 public:
  void GetFaultLevelManifest();
  void CheckFaultClassificationConfigStatus(
      const std::shared_ptr<
          const ::fault_detector::pb::FaultClassificationConfigStatus>&
          fault_classification_config_status);
  bool GetFaultExceptionType(
      const std::string& node_name, const pb::Fault& fault,
      fault_detector::pb::FaultExceptionType& fault_exception_type);

  bool IsLoadedFaultExceptionType() const {
    return is_loaded_fault_exception_type_;
  }
  bool LoadFaultExceptionType(const std::string& json_str);

 private:
  std::string GetFaultEventId(const std::string& node_name,
                              const pb::Fault& fault);
  std::string GetFaultEventId(const fault_detector::pb::FaultUnit& fault);

  // node_name-component-component_instance-code
  std::unordered_map<
      /*key='node_name-component-component_instance-code'*/ std::string,
      fault_detector::pb::FaultExceptionType>
      fault_exception_type_map_;
  bool is_loaded_fault_exception_type_{false};
  fault_detector::pb::FaultClassificationConfigStatus
      fault_classification_config_status_cache_;
};

class ExceptionEventStatistics {
  friend class ExceptionEventStatisticsTestV2;

 public:
  explicit ExceptionEventStatistics(
      std::shared_ptr<FaultLevelManifest>& faul_level_manifest,
      const IParamGetter& param_getter)
      : fault_level_manifest_(faul_level_manifest),
        param_getter_(param_getter) {}

  void Init();

  void CheckSystemState(
      const std::shared_ptr<const ::pb::SystemState>& system_state,
      std::vector<
          std::shared_ptr<::action_factory::pb::SsExceptionEventStatistics>>&
          exception_event_statistics,
      const voy::VehicleMode& vehicle_mode,
      bool is_vehicle_mode_changed_from_manual);
  void UpdateNodeState(
      const std::shared_ptr<const ::service_restore::pb::RestoreRecord>&
          restore_record);
  void UpdateVehicleExceptionId(const std::string& system_level);

  std::shared_ptr<::action_factory::pb::SsExceptionEventStatistics>
  SetSsExceptionEventStatistics(
      const voy::VehicleMode& vehicle_mode, const std::string& system_level,
      const std::string& node_name, const ActiveFaultInfo& fault_info,
      const std::string& vehicle_exception_id, uint64_t af_dispose_time,
      const std::set<fault_detector::pb::FaultExceptionType>& fault_types,
      const ::action_factory::pb::SsExceptionEventStatistics::
          SsExceptionEventType& event_type);

  bool ExceptionEventFilter(const std::string& system_level);

  bool IsTestRuntimeEnv() const { return is_test_env_ || is_gen4_et_env_; }
  void CheckVehicleRuntimeEnv();

  // for unittest
  std::unordered_map<std::string, NodeEventData>& GetExceptionEventData() {
    return exception_event_data_;
  }
  std::shared_ptr<FaultLevelManifest>& GetFaulLevelManifest() const {
    return fault_level_manifest_;
  }
  std::string GetVehicleExceptionId() const {
    return vehicle_exception_id_.second;
  }
  void SetCarId(const std::string& car_id) { car_id_ = car_id; }

 private:
  struct FaultProcessingContext {
    explicit FaultProcessingContext(
        const voy::VehicleMode& vehicle_mode, std::string system_level,
        int64_t af_dispose_time, bool is_vehicle_mode_changed_from_manual,
        const std::set<fault_detector::pb::FaultExceptionType>& fault_types)
        : vehicle_mode(vehicle_mode),
          system_level(std::move(system_level)),
          af_dispose_time(af_dispose_time),
          is_vehicle_mode_changed_from_manual(
              is_vehicle_mode_changed_from_manual),
          fault_types(std::move(fault_types)) {}

    voy::VehicleMode vehicle_mode = {};
    std::string system_level;
    int64_t af_dispose_time = 0L;
    bool is_vehicle_mode_changed_from_manual = false;
    std::set<fault_detector::pb::FaultExceptionType> fault_types;
  };
  using StatsRes = std::vector<
      std::shared_ptr<::action_factory::pb::SsExceptionEventStatistics>>;
  bool ShouldSkipAddFault(const FaultProcessingContext& context);
  void ProcessActiveFault(
      const FaultProcessingContext& context, const std::string& node_name,
      const ::pb::Fault& fault_pb,
      fault_detector::pb::FaultExceptionType fault_exception_type,
      StatsRes& out_exception_event_statistics);
  void ProcessDisappearedFaults(
      const FaultProcessingContext& context,
      const std::unordered_set<std::string>& fault_units,
      StatsRes& out_exception_event_statistics);

  bool GetCarId();
  bool GetAcuVersion();

  void UpdateTimestamp() {
    // TODO(Tianyiliu): find a suitable way to run unittest and remove this.
    if (is_unit_test_env) return;
    exception_event_timestamp_ = base::Now();
  }
  void UpdateSequenceCounter() { ++sequence_counter_; }
  std::string GenerateFaultEventId(const std::string& node_name,
                                   const pb::Fault& fault);
  std::string GenerateFaultUnit(const std::string& node_name,
                                const pb::Fault& fault);
  node_state_type CheckFaultCause(const std::string& node_name) const;
  void UpdateTripID();

  std::string car_id_;
  std::string acu_version_;
  std::string curr_trip_id_;
  std::pair<std::string /*system_level*/,
            std::string /*car_id-timestamp-system_level*/>
      vehicle_exception_id_;
  time_t exception_event_timestamp_{0};
  time_t startup_timestamp_{base::WallClockNowS()};
  uint64_t sequence_counter_{0};
  bool is_test_env_{false};
  bool is_gen4_et_env_{false};
  bool is_unit_test_env{false};

  std::unordered_map<std::string /*node name*/, NodeEventData>
      exception_event_data_;
  std::shared_ptr<FaultLevelManifest>& fault_level_manifest_;
  const IParamGetter& param_getter_;  // not owned

  FRIEND_TEST(ExceptionEventStatisticsTestV2, UpdateVehicleExceptionId_Normal);
  FRIEND_TEST(ExceptionEventStatisticsTestV2,
              CheckSystemState_NewFault_GeneratesAppearedEvent);
  FRIEND_TEST(ExceptionEventStatisticsTestV2,
              CheckSystemState_FaultDisappears_GeneratesDisappearedEvent);
  FRIEND_TEST(ExceptionEventStatisticsTestV2,
              ShouldSkipAddFault_ManualModeAndFilter);
  FRIEND_TEST(ExceptionEventStatisticsTestV2,
              SetSsExceptionEventStatistics_PopulatesFields);
  FRIEND_TEST(ExceptionEventStatisticsTestV2, UpdateTripID_Success);
  FRIEND_TEST(ExceptionEventStatisticsTestV2, UpdateTripID_FailsToGetParam);
};

}  // namespace ss_exception_event_statistics
}  // namespace action_factory
}  // namespace system_supervisor

#endif  // ONBOARD_SYSTEM_SUPERVISOR_FAULT_CLASSIFICATION_ACTION_FACTORY_EXCEPTION_EVENT_STATISTICS_H_
