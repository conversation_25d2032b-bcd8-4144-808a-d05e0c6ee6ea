syntax = "proto3";

package planner.pb;

import "google/protobuf/wrappers.proto";

import "planner_protos/agent_inlane_state.proto";
import "planner_protos/agent_intention.proto";
import "planner_protos/agent_map_element_occupancy_seed.proto";
import "planner_protos/behavior_reasoner.proto";
import "planner_protos/common.proto";
import "planner_protos/cross_lane_info.proto";
import "planner_protos/directive.proto";
import "planner_protos/hazardous_state.proto";
import "planner_protos/lane_blockage.proto";
import "planner_protos/lane_change.proto";
import "planner_protos/lane_sequence_proposal.proto";
import "planner_protos/lane_sequence_candidate.proto";
import "planner_protos/fault_detector_response.proto";
import "planner_protos/path_reasoning_seed.proto";
import "planner_protos/path_seed.proto";
import "planner_protos/planning_lane_sequence.proto";
import "planner_protos/remote_assist.proto";
import "planner_protos/relax_lane_sequence_parameter.proto";
import "planner_protos/route_preview.proto";
import "planner_protos/speed_seed.proto";
import "planner_protos/selection_seed.proto";
import "planner_protos/unstuck_planner_seed.proto";
import "prediction_protos/predicted_trajectory.proto";
import "voy_protos/math.proto";
import "voy_protos/tracked_objects.proto";
import "voy_protos/trajectory.proto";
import "planner_protos/pull_over.proto";

// The seed info defined for stop sign.
// Next Available ID: 3
message StopSignSeed {
  bool has_stopped = 1;
  // The ego's stopped time at this stop sign.
  int64 stopped_timestamp = 2;
}

// The stop sign reasoner's seed info.
// Next Available ID: 2
message StopSignReasonerSeed { map<int64, StopSignSeed> stop_signs = 1; }

// The pull out reasoner's seed info.
// Next Available ID: 6
message PullOutReasonerSeed {
  reserved 1;
  bool is_confirmed = 2;
  int64 confirm_id = 3;
  // Indicates if the ego has finished generating turn signals and ready to go.
  bool is_ready_to_go = 4;
  int64 confirmation_timestamp = 5;
}

// The bus stop reasoner'd seed info.
// Next Available ID: 2
message BusStopReasonerSeed { repeated int64 bus_stop_ids = 1; }

// The destination reasoner's seed info.
// Next Available ID: 3
message DestinationReasonerSeed {
  // The latest received reverse driving response.
  ReverseDrivingResponse reverse_driving_response = 1;
  // The stop line's absolute coordinates on the nominal path, which are
  // set & recorded when the latest reverse driving response is received.
  voy.Point2d stop_line_point = 2;
}

// The yield analyzer's seed info.
// Next Available ID: 2
message YieldAnalyzerSeed { repeated int64 yield_to_agent_ids = 1; }

// The yield reasoner's seed info.
// Next Available ID: 2
message YieldReasonerSeed {
  map<int64, YieldAnalyzerSeed> yield_analyzer_seed_map = 1;
}

// The traffic light reasoner'd seed info.
// Next Available ID: 5
message TrafficLightReasonerSeed {
  int64 first_approaching_traffic_light_lane_id = 1;

  // Traffic light control state.
  TrafficLightControlState state = 2;

  // Distance from stop line to stop fence.
  double stop_fence_distance = 3;

  // Parameters of traffic light reasoner.
  TrafficLightReasonerParam param = 4;
}

// The emergency brake reasoner's seed info.
// Next Available ID: 3
message EmergencyBrakeReasonerSeed {
  // The received fault detector response.
  FaultDetectorResponse fault_detector_response = 1;
  // The absolute coordinate of the stopping line on the nominal path. They are
  // computed when the fault detector response is received for the first time.
  voy.Point2d stopping_line_point = 2;
}

// The engage reasoner's seed info.
// Next Available ID: 5
message EngageReasonerSeed {
  // Whether the stationary steering is latched.
  bool is_stationary_steering_latched = 1;
  // Whether the ego is in autonomous mode last cycle.
  bool is_in_autonomous_mode_last_cycle = 2;
  // The speed recorded when engaging.
  double engaging_speed = 3;
  // The timestamp when steering starts to be latched.
  int64 steering_latched_start_timestamp = 4;
}

// The gear switch reasoner's seed info.
// Next Available ID: 3
message GearSwitchReasonerSeed {
  // The timestamp when this reasoner is activated.
  int64 activation_timestamp = 1;
  // The absolute coordinate of the stop line on the nominal path.
  voy.Point2d stop_line_point = 2;
}

// The common reasoner seeds.
// Next Available ID: 10
message CommonReasonerSeeds {
  // The stop sign seeds.
  StopSignReasonerSeed stop_sign_reasoner_seed = 1;
  // The pull out reasoner seed.
  PullOutReasonerSeed pull_out_confirm_reasoner_seed = 2;
  // The bus stop reasoner seed.
  BusStopReasonerSeed bus_stop_reasoner_seed = 3;
  // The yield reasoner seed.
  YieldReasonerSeed yield_reasoner_seed = 4;
  // The traffic light reasoner seed.
  TrafficLightReasonerSeed traffic_light_reasoner_seed = 5;
  // The emergency brake reasoner seed.
  EmergencyBrakeReasonerSeed emergency_brake_reasoner_seed = 6;
  // The engage reasoner seed.
  EngageReasonerSeed engage_reasoner_seed = 7;
  // The destination reasoner seed.
  DestinationReasonerSeed destination_reasoner_seed = 8;
  // The gear switch reasoner seed.
  GearSwitchReasonerSeed gear_switch_reasoner_seed = 9;
}

// The agent history state watcher seed.
// Next Available ID: 3
message AgentHistoryStateWatcherSeed {
  reserved 2;
  reserved "agent_last_pass_side_map";
  // A map recording the blocking state of the agent in the last cycle (for
  // stationary agent, it's that of tracked state; and for non-stationary agent,
  // it's the most constraining blocking state of its tracked state and all
  // predicted states). Key of the map is agent id.
  map<int64, AgentSnapshotBlockingState> agent_last_blocking_state_map = 1;
}

// The agent in lane state generator seed.
// Next Available ID: 2
message AgentInLaneStateGeneratorSeed {
  // The agent history state watcher seed.
  AgentHistoryStateWatcherSeed agent_history_state_watcher_seed = 1
      [ deprecated = true ];
}

// The result of forawrd simulation in crawl.
// Next Available ID: 5
message CrawlForwardSimulationResult {
  // The leading agent speed profile generated by forward simulation.
  SpeedProfile leading_profile = 1;
  // The trailing agent speed profile generated by forward simulation.
  SpeedProfile trailing_profile = 2;
  // The ego speed profile generated by forward simulation.
  SpeedProfile ego_profile = 3;
  // The error message in forward simulation.
  string error_message = 4;
}

// The information of agent in lane change crawl.
// Next Available ID: 5
message CrawlAgentInfo {
  // The agent id.
  int64 agent_id = 1;
  // The coordination type of the agent.
  AgentCoordinationType coordination_type = 2;
  // The tracked object.
  voy.TrackedObject tracked_object = 3;
  // The coordination trajectories of the agent.
  repeated prediction.pb.PredictedTrajectory coordination_trajectories = 4;
}

// The seed of lane change crawl info.
// Next Available ID: 3
message LaneChangeCrawlInfoSeed {
  // The flag of whether is crawl abort.
  bool is_crawl_abort = 1;
  // The information of agents in crawl.
  repeated CrawlAgentInfo crawl_agents = 2;
}

// The seed of lane sequence info.
// TODO(Xiang): The seed should be hold by something like
// `LaneSequenceInfoGenerator` rather than LaneSequenceInfo itself. Clean this
// up after refactoring the LaneSequenceInfo.
// Next Available ID: 5
message LaneSequenceInfoSeed {
  reserved 1;
  reserved "is_super_nudge_abort";

  // The agent in lane state generator seed.
  AgentInLaneStateGeneratorSeed agent_in_lane_state_generator_seed = 2;
  // The used prior path lane id for lane follow maneuver.
  int64 prior_path_lane_id_for_lane_follow = 3;
  // The seed of lane change crawl info.
  LaneChangeCrawlInfoSeed lane_change_crawl_info_seed = 4;
}

// The seed info of overtaking agent observer.
// Next Available ID: 2
message OvertakingAgentObserverSeed {
  // Info for agents relative states.
  map<int64, OvertakingRelativeStatus> agents_status_map = 1;
}

// The seed info of nudge snapshot hub.
// Next Available ID: 2
message NudgeSnapshotHubSeed {
  OvertakingAgentObserverSeed overtaking_agent_observer_seed = 1;
}

// The seed info of inlane nudge corridor profile recorder.
// Next Available ID: 3
message InLaneCorridorProfileRecorderSeed {
  // The first key dimension is the object id for the profile and the second
  // key dimension is the time step.
  // Next Available ID: 2
  message ObjectIdInLaneCorridorProfileMap {
    // Next Available ID: 2
    message TimestampInLaneCorridorProfileMap {
      map<int64, InLaneCorridorProfile> timestamp_profile_map = 1;
    }
    map<int64, TimestampInLaneCorridorProfileMap> object_id_profile_map = 1;
  }

  // Container for previous InLaneCorridorProfile.
  map<int64, ObjectIdInLaneCorridorProfileMap> profile_map = 1;

  // Container to save the last seen time stamp for each agent. The key is
  // object_id and value is the latest seen time stamp.
  map<int64, int64> last_seen = 2;
}

// The seed info of nudge corridor generator.
// Next Available ID: 2
message NudgeCorridorGeneratorSeed {
  InLaneCorridorProfileRecorderSeed in_lane_nudge_profile_recorder_seed = 1;
}

// The seed info of nudge directive generator.
// Next Available ID: 3
message NudgeDirectiveGeneratorSeed {
  NudgeCorridorGeneratorSeed nudge_corridor_generator_seed = 2;
  NudgeSnapshotHubSeed nudge_snapshot_hub_seed = 1;
}

// The seed info of directive.
// Next Available ID: 3
message DirectiveSeed {
  NudgeDirectiveGeneratorSeed nudge_directive_generator_seed = 1;
  repeated StopLineDirective stop_directive = 2;
}

// The seed info of maneuver common parts.
// Next Available ID: 5
message BaseManeuverSeed {
  LaneSequenceInfoSeed lane_sequence_info_seed = 2;
  CommonReasonerSeeds common_reasoner_seeds = 1;
  DirectiveSeed directive_seed = 3;
  DirectiveSeed backup_directive_seed = 4;
}

// The lane that leads to deadend if do not perform lane change.
// (the last chance for lane change before detour.)
// Next Available ID: 3
message DivergeLaneInfo {
  // The lane that leads to deadend.
  int64 diverge_lane_id = 1;
  // The distance from the last lane start to the start of the
  // last non-lane-change-able lane marking.
  double forward_distance = 2;
}

// The lane change maneuver's request.
// Next Available ID: 9
message LaneChangeManeuverRequest {
  reserved 2, 3, 4;
  reserved "target_speed", "start_arclength", "end_arclength";
  // The flag to decide if the active maneuver should be triggered.
  bool trigger = 1;
  // Turn mode.
  TurnMode turn_mode = 5;
  // The lead car id.
  int64 lead_id = 6;
  // Diverge lane id.
  DivergeLaneInfo diverge_lane_info = 7;
  // The flag to decide if probing should be triggered.
  bool should_probing = 8;
}

// The turn signal indicator's seed info.
// Next Available ID: 4
message TurnSignalIndicatorSeed {
  ManeuverType selected_maneuver_type = 1;
  TurnMode last_turn_signal_mode = 2;
  int64 turn_signal_start_timestamp = 3;
}

// The assist request session.
// Next Available ID: 5
message AssistRequestSession {
  // Session states.
  bool is_opened = 1;
  bool is_updated = 2;

  // Session's request.
  AssistRequest request = 3;

  // Incremental unique ID for updated requests.
  int64 incremental_request_id = 4;
}

// The seed info for assist request scheduler.
// Next Available ID: 2
message AssistRequestSchedulerSeed { AssistRequestSession session = 1; }

// The seed info for pull out request.
// Next Available ID: 2
message PullOutRequestSchedulerSeed { int64 incremental_request_id = 1; }

// The lane follow maneuver's request.
// Next Available ID: 2
message LaneFollowManeuverRequest {
  // The flag to decide if the active maneuver should be triggered.
  bool trigger = 1;
}

// The pull over maneuver's request.
// Next Available ID: 2
message PullOverManeuverRequest {
  // The information of immediate pullover destination.
  ImmediatePullOverDestination immediate_pullover_destination = 1;
}

// The inter-maneuver coordination requests.
// Next Available ID: 6
message InterManeuverCoordinationRequest {
  reserved 1;
  reserved "lane_change_maneuver_request";

  // The maneuver-specific requests.
  oneof maneuver_request {
    // The left lane change returns.
    LaneChangeManeuverRequest lane_change_left_maneuver_request = 4;
    // The right lane change returns.
    LaneChangeManeuverRequest lane_change_right_maneuver_request = 3;
    // The lane follow returns.
    LaneFollowManeuverRequest lane_follow_maneuver_request = 2;
    // The pull over returns.
    PullOverManeuverRequest pull_over_maneuver_request = 5
        [ deprecated = true ];
  }
}

// Enum stands for the reason of exiting reverse driving.
message ReverseDrivingExitType {
  // NEXT Availble ID: 6
  enum Enum {
    // Ego is in reverse driving.
    kNoExit = 0;
    // Ego has reached destination and has been asked for exit.
    kReachDestination = 1;
    // Ego has reached destination and auto exit.
    kReachDestinationAutoExit = 2;
    // Reverse driving is auto triggered and ego has been stucked for a while.
    kTimeOutAutoExit = 3;
    // Ego has been asked for stopping immediately and exit.
    kStopImmediately = 4;
    // Ego has been take-over manually.
    kManual = 5;
  }
}

// Next Available ID: 12
message ReverseDrivingBehaviorSeed {
  // Checks the reverse driving requester.
  ReverseDrivingAsker requester = 1;
  // The state of reverse driving behavior.
  ReverseDrivingState.Enum state = 2;
  // The motion mode of ego for this cycle.
  MotionMode motion_mode = 3;
  // The set reverse driving distance.
  // From current leading(rear) bumper to target leading bumper.
  double reverse_distance = 4;
  // Cached reverse driving destination stop point.
  voy.Point2d stop_point = 5;
  // The duration time of ego stucking.
  double stuck_time = 6;
  // Whether ego need release reasoner boundaries for trying to unstuck.
  bool need_unstuck = 7;
  // Whether ego is in a auto trigger phase.
  bool auto_triggered = 8;
  // The reason of exiting reverse driving.
  ReverseDrivingExitType.Enum exit_type = 9;
  // The guidance curve of the reverse behavior
  optional math.pb.Curve2d guidance_curve = 10;
  // The expected inital steering angle for reverse
  optional double init_steering_angle = 11;
}

// Next Available ID: 5
message AutoTriggerReverseInfo {
  bool need_auto_reverse = 1;
  double reverse_dist = 2;
  optional math.pb.Curve2d reverse_guidance_curve = 3;
  optional double init_steering_angle = 4;
}

// Next Available ID: 42
message DecoupledManeuverSeed {
  // Reserved field ids and corresponding names.
  reserved 2, 9, 10, 18;
  reserved "last_homotopy", "same_alternative_homotopy_win_count",
      "last_dominant_obj_that_reset_flicker_weight",
      "hard_boundary_buffer_status_seed";

  // The seed info of maneuver common parts for legacy planner.
  BaseManeuverSeed base_maneuver_seed = 1;

  // Intention result for last cycle.
  IntentionResult last_intent = 16;

  // The speed seed contains information from previous iterations of the speed
  // pipeline. Each speed generator generates a SpeedSeed, and the SpeedSeed of
  // the selected speed profile is stored here.
  speed.pb.SpeedSeed speed_seed = 3;
  // The lane change gemeotry meta data for path search. It is latched across
  // iteration when LC is in progress.
  LaneChangeGeometryMetaData lane_change_geometry_meta = 21;
  LaneSequenceCandidates lane_sequence_candidates = 22;
  bool should_latch_lane_change_geom = 23;

  // AgentReactionSeeds records the history of agents' reaction to Ego.
  speed.pb.AgentReactionSeeds agent_reaction_seeds = 12;

  // AgentReactionTrackerSeed records the history of agents for agent reaction
  // tracker.
  speed.pb.AgentReactionTrackerSeed agent_reaction_tracker_seed = 30;

  Trajectory selected_trajectory = 4;

  // Fields below should sync to WorldModelSeed if the decoupled trajectory
  // is selected in PlanningNodelet::Plan.
  //
  // The selected path.
  // TODO(Hongda): Stale this filed, if it is not going to be used.
  Path selected_path = 5;

  // Lane ids of the selected lane sequence, used by WorldModel.
  repeated int64 selected_lane_sequence = 6;

  // Selected lane change state prepare/in progress/abort.
  LaneChangeState selected_lane_change_state = 17;

  // Lane sequence plan init state, used by WorldModel.
  LaneSequencePlanInitState lane_sequence_plan_init_state = 7;

  // Best homotopy if flickering cost is not considered.
  IntentionResult.IntentionHomotopy best_homotopy_wo_flicker_cost = 8;

  // The path seed contains the path pipeline seed from the previous-cycle
  // selected trajectory candidate. Each path generator generates a
  // SingleHomotopyPathSeed, and the SingleHomotopyPathSeed of the selected path
  // is stored here.
  SingleHomotopyPathSeed path_seed = 11;

  // The selection seed containts last path history.
  SelectionSeed selection_seed = 13;

  // The continuous cycle count when ego can be considered to be almost static.
  int32 continuous_low_speed_cycle_count = 14;

  // Set to true if current cycle needs a lane sequence with a current lane
  // which is part of the last selected lane sequence.
  bool latch_current_lane = 15;

  // The selected behavior type.
  BehaviorType selected_behavior_type = 19;

  // The extended curve of the planned path with the backward and forward
  // stitch with history path and lane sequence reference curve. This will be
  // synced with WorldModelSeed.last_nominal_path.
  math.pb.Curve2d extended_path = 20;

  // The path reasoning seed contains information that needs to be passed across
  // iterations.
  PathReasoningSeed path_reasoning_seed = 24;

  // The path reasoner seed contains information that needs to be passed across
  // iterations.
  PathReasonerSeed path_reasoner_seed = 37;

  // The seed contains previous iterations of waypoint assist related info if
  // it's selected.
  WaypointAssistSeed selected_waypoint_assist_seed = 25;

  // The reverse driving seed contains the reverse driving state, motion mode,
  // requester and distance info from the previous-cycle.
  ReverseDrivingBehaviorSeed reverse_driving_seed = 26;

  // The seed carries information of what map elements each agent ever occupied
  // in past, which is obtained by the agent predicted trajectory route
  // associatior.
  AgentMapElementOccupancySeeds agent_map_element_occupancy_seeds = 28;

  // Lane sequence proposal is generated by planner, which requires lane
  // sequence candidates for the next cycle.
  LaneSequenceProposal lane_sequence_proposal = 29;

  LaneChangeInfoSeed lane_change_info_seed = 31;

  cross_lane.pb.CrossLaneInfoSeed cross_lane_info_seed = 41;

  PullOverInfoSeed pull_over_info_seed = 36;

  // Whether we need trigger reverse driving and the needed reverse driving
  // distance.
  AutoTriggerReverseInfo auto_trigger_reverse_info = 32;

  // The seed of light assist behaviors.
  LightAssistSeed light_assist_seed = 33;

  // The unstuck planner seed.
  UnstuckPlannerSeed unstuck_planner_seed = 34;

  // The last lane change finishing timestamp in milliseconds.
  int64 last_lane_change_finish_timestamp_msec = 35;

  // The last lane change finishing instance.
  LaneChangeInstance last_lane_change_finish_instance = 38;

  // The lane change sequence proposal metadata.
  LaneChangeSequenceProposalMetadata lane_change_sequence_proposal_metadata =
      39;

  bool is_lc_creep_activated_for_agents_behind = 40;
}

// The lane change info seed.
// Next Available ID: 26
message LaneChangeInfoSeed {
  // Lane change metadata contains the metadata of the ongoing lane change
  // instance.
  LaneChangeMetadata lane_change_metadata = 1;

  double urgency_score = 2;
  double smooth_urgency_score = 3;

  // The lane change in progress info.
  LaneChangeInProgressInfo lane_change_in_progress_info = 4;

  double blocking_traffic_score = 5;

  // Temporarily latch valid lane change end. We only use the urgency type and
  // stop line type, while the urgency dist and urgency score are updated
  // independently.
  LaneChangeUrgencyInfo valid_lane_change_end = 6;

  LaneChangeInstance lane_change_instance = 7;

  LaneSequenceCandidate.LaneSequenceType lane_change_sequence_type = 8;
  repeated int64 lane_change_sequence = 9;

  PlanningSegmentSequence lane_change_planning_segment_sequence = 10;

  // The seed for manual lane change.
  ManualLaneChangeState mlc_state = 11;

  // Preview route info.
  PreviewRouteResult preview_route_result = 12;

  // The key is the object id.
  map<int64, XRegionMotionInfoMap> object_xregion_motion_info_map = 13;

  // Preview route info.
  PreviewRouteResult target_region_preview_route_result = 14;

  map<string, LaneChangeUrgencyInfo> urgency_info_map = 15;

  // The lane change stuck info.
  LaneChangeStuckInfo lane_change_stuck_info = 16;

  // The timestamp of the first frame that try to quit proposing an early lane
  // change.
  optional int64 first_timestamp_try_to_quit_proposing_early_lc = 17;

  // Whether we should set no yield for a slow-moving agent that triggered ELC.
  // This should only be used in or before PopulateLaneChangeEnvInfo; after
  // PopulateLaneChangeEnvInfo, use the value in LaneChangeInfo instead.
  bool should_set_no_yield_for_elc_triggered_object = 18;

  // The slow-moving agent ID that triggered ELC.
  // This should only be used in or before PopulateLaneChangeEnvInfo; after
  // PopulateLaneChangeEnvInfo, use the value in LaneChangeInfo instead.
  int64 elc_triggered_object_id = 19;

  // The distance between ego and source blockage before in-progress.
  optional double source_blockage_start_arclength_before_in_progress = 20;

  LaneChangeIntentionMonitorInfo lane_change_intention_monitor_info = 21;

  // Whether we can trigger crawl for this lane change.
  bool can_trigger_lc_crawl = 22;

  // Whether last lc is crawl.
  bool is_last_lc_crawl = 25;

  // The accumulated cycles without a valid gap.
  int64 consecutive_no_gap_cycles = 23;

  LaneChangeSignalSourceType lane_change_signal_source_type = 24;
}

// The lane change stuck info
// Next Available ID: 4
message LaneChangeStuckInfo {
  // The timestamp when ego starts waiting in lane change state.
  optional int64 start_waiting_timestamp = 1;
  // The timestamp when ego stops waiting in lane change state.
  optional int64 stop_waiting_timestamp = 2;
  // The total waiting time in sec.
  optional double total_waiting_time_in_sec = 3;
}

// The lane change in progress info.
// Next Available ID: 3
message LaneChangeInProgressInfo {
  // The timestamp when ego enters lane change in progress state.
  optional int64 in_progress_timestamp = 1;
  // The turn light duration before ego enters lane change in progress state.
  optional double turn_light_duration_before_in_progress = 2;
  // The speed discomfort during lane change in progress.
  optional double speed_discomfort = 3;
}

// The seed for stuck avoidance.
// Next Available ID: 3
message StuckAvoidanceSeed {
  // The lane IDs in where ego is stuck.
  repeated int64 stuck_lane_ids = 1;
  // The parameter for stuck avoidance.
  StuckAvoidanceParameterSeed parameter = 2;
}

// The info about fork lane ids and merge lane ids for the lane sequence.
// Next Available ID: 4
message ForkAndMergeInfo {
  // The index into the vector who stores multiple lane sequence results.
  int32 lane_sequence_result_index = 1;
  // The fork lane IDs of lane sequence result the index of which is
  // |lane_sequence_result_index|.
  repeated int64 fork_lane_ids = 2;
  // The merge lane IDs of lane sequence result the index of which is
  // |lane_sequence_result_index|.
  repeated int64 merge_lane_ids = 3;
}

// Next Available ID: 7
message LaneSequenceGeneratorSeed {
  // The seed info of last selected lane sequence.
  // NOTE(Tingran): Deprecate this field since the same field was added in world
  // model seed.
  repeated int64 selected_lane_sequence_ids = 1 [ deprecated = true ];
  // Whether lane change sequence exists.
  bool has_lane_change_sequence = 2;
  // The seed for relaxing lane change constraint.
  StuckAvoidanceSeed stuck_avoidance_seed = 3;
  // True if all lane change sequence has been blocked. If the first lane
  // change edge is after a blockage, we say a lane sequence is blocked.
  bool is_all_lane_change_sequence_blocked = 4;
  // The info for fork lanes and merge lanes about all lane sequences.
  repeated ForkAndMergeInfo fork_and_merge_infos = 5;
  // True if all lane change sequence is blocked after first lane change edge.
  bool is_all_lane_change_sequences_blocked_after_first_lc_edge = 6;
}

// The waypoint assist related info.
// Next Available ID: 7
message WaypointAssistSeed {
  optional math.pb.Curve2d reference_curve = 1;
  bool should_latch_waypoint_assist_geom = 2;
  bool prepare_path_fail = 3;
  bool has_responded_lane_sequence_proposal = 4;
  AssistManeuverPhase.Enum waypoint_assist_phase = 5;
  repeated voy.Point2d reference_points = 6;
}

// The light assist related info.
// Next Available ID: 10
message LightAssistSeed {
  reserved 3;
  reserved "light_weight_assist_state";
  DirectionKeyCommand.DirectionType direction_type = 1;
  // The type of unstuck detour.
  UnstuckDetourInfo.DetourType unstuck_detour_type = 2;
  // Indicate detour available state and reason.
  UnstuckDetourAvailableState unstuck_detour_available_state = 4;
  // Indicate whether in light assist.
  bool is_in_light_assist = 5;
  // Represents whether ego forward crawl is available.
  bool is_forward_crawl_available = 6;
  // Represents whether core planner has given cz creep request.
  bool is_core_planner_suggest_creep_around_cz = 7;
  // Represents dominant constraint id when crawl.
  int32 dominant_constraint_id = 8;
  // Represents whether core planner has given creep request.
  bool is_core_planner_suggest_creep = 9;
}
