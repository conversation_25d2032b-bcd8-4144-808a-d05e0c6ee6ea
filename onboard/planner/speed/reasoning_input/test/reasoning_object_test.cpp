#include "planner/speed/reasoning_input/reasoning_object.h"
#include "planner/speed/reasoning/test/reasoning_test_fixture.h"

#include <google/protobuf/text_format.h>
#include <gtest/gtest.h>

#include "planner/speed/overlap/overlap_lib_util.h"
#include "planner_protos/overlap.pb.h"
#include "voy_protos/tracked_objects.pb.h"

namespace planner {
namespace speed {

class ReasoningObjectTest : public ::testing::Test,
                            public ReasoningTestFixture {
 public:
  ReasoningObjectTest() {
    // Currently this test uses default map(Fremont), you can also use other
    // maps by moving this set up map step into each individual test. Now we
    // also support scene maps(See ReasoningTestFeature::SetUpSceneMap. You can
    // check ReasoningTestFixtureTest for some examples).
    SetUpRegionMap(hdmap::test_util::kFremontData);
  }
};

TEST_F(ReasoningObjectTest, BasicTest) {
  // Construct voy::TrackedObject instance.
  prediction::pb::Agent agent_proto;
  auto& tracked_object = *agent_proto.mutable_tracked_object();
  tracked_object.set_object_type(voy::perception::ObjectType::VEHICLE);
  tracked_object.set_id(123);

  // Construct pb::ObjectProximityInfo instance.
  pb::ObjectProximityInfo object_proximity_info;
  object_proximity_info.set_signed_lateral_gap(1.23);
  object_proximity_info.set_signed_lateral_speed(4.56);
  object_proximity_info.set_signed_longitudinal_speed(7.89);
  PrincipledRequiredLateralGap required_lateral_gap{
      math::PiecewiseLinearFunction{1.0},
      math::PiecewiseLinearFunction{1.0},
      math::PiecewiseLinearFunction{0.0},
      1.0,
      0.0,
      0.0,
      0.0};

  const int64_t timestamp = 0;
  // Dummy pose at plan init ts.
  const TrafficParticipantPose pose_at_plan_init_ts(timestamp, tracked_object);
  PlannerObject planner_object(agent_proto, timestamp, pose_at_plan_init_ts);
  planner_object.set_is_in_junction(true);

  ReasoningObject reasoning_object(planner_object, object_proximity_info,
                                   required_lateral_gap,
                                   /*object_occupancy_param_ptr=*/nullptr);

  reasoning_object.set_has_seen_ego_time(-5.0);
  // ReasoningObject test.
  // TODO(waylon): Add more test items.
  EXPECT_EQ(reasoning_object.has_seen_ego_time(), -5.0);
  EXPECT_EQ(reasoning_object.object_type(),
            voy::perception::ObjectType::VEHICLE);
  EXPECT_EQ(reasoning_object.id(), 123);
  EXPECT_EQ(reasoning_object.object_proximity_info().signed_lateral_gap(),
            1.23);
  EXPECT_EQ(reasoning_object.object_proximity_info().signed_lateral_speed(),
            4.56);
  EXPECT_EQ(
      reasoning_object.object_proximity_info().signed_longitudinal_speed(),
      7.89);
  EXPECT_TRUE(reasoning_object.is_in_junction());
}

TEST_F(ReasoningObjectTest, AgentNotInJunctionTest) {
  SetEgoPose(/*lane_id=*/16905, /*portion=*/0.1);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  // Spawn a vehicle agent not in a junction.
  prediction::pb::Agent& agent_not_in_junction =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/16863, /*portion=*/0.5,
               /*length=*/5.0, /*width=*/2.0,
               /*velocity=*/0.0, /*height=*/2.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/16863, /*start_portion=*/0.5,
                                 /*end_lane_id=*/16863, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent_not_in_junction);

  // Validate the agent list.
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  // Verify the first reasoning object is not in junction.
  const ReasoningObject& reasoning_object_not_in_junction =
      reasoning_inputs().front().reasoning_object;
  EXPECT_FALSE(reasoning_object_not_in_junction.is_in_junction());
}

TEST_F(ReasoningObjectTest, AgentInJunctionTest) {
  SetEgoPose(/*lane_id=*/16905, /*portion=*/0.1);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  // Spawn a vehicle agent located in a junction.
  prediction::pb::Agent& agent_in_junction =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/16965, /*portion=*/0.5,
               /*length=*/5.0, /*width=*/2.0,
               /*velocity=*/0.0, /*height=*/2.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/16965, /*start_portion=*/0.5,
                                 /*end_lane_id=*/16965, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent_in_junction);

  // Validate the agent list.
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  // Verify the second reasoning object is in junction.
  const ReasoningObject& reasoning_object_in_junction =
      reasoning_inputs().front().reasoning_object;
  EXPECT_TRUE(reasoning_object_in_junction.is_in_junction());
}

TEST_F(ReasoningObjectTest, LinkedVehicleTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kBusBulb);
  SetEgoPose(/*lane_id=*/129403, /*portion=*/0.5,
             /*speed=*/10.0);
  CreatePathWithLaneSequence({129403},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  const math::Pose2d& tractor_pose{-4506.83, -2579.84, 0.0};
  prediction::pb::Agent& tractor =
      AddAgent(voy::perception::ObjectType::VEHICLE, /*id=*/1, tractor_pose,
               /*length=*/2.0,
               /*width=*/1.9, /*velocity=*/1.5, /*height=*/1.8);
  AddStationaryPredictedTrajectory(tractor_pose, 0.9, 1, tractor);

  const math::Pose2d& trailer_pose{-4510.83, -2579.84, 0.0};
  prediction::pb::Agent& trailer =
      AddAgent(voy::perception::ObjectType::VEHICLE, /*id=*/2, trailer_pose,
               /*length=*/6.0,
               /*width=*/1.9, /*velocity=*/1.5, /*height=*/1.8);
  AddStationaryPredictedTrajectory(trailer_pose, 0.9, 1, trailer);

  tractor.mutable_tracked_object()->set_associated_vehicle_tail_id(2L);
  trailer.mutable_tracked_object()->set_associated_vehicle_head_id(1L);

  // Validate the agent list.
  EXPECT_EQ(2, agent_list().agent_list().size());
  Update();

  // Verify both reasoning objects are linked vehicles.
  EXPECT_EQ(reasoning_inputs().front().reasoning_object.id(), 2);
  EXPECT_TRUE(
      reasoning_inputs().front().reasoning_object.IsLinkedTailVehicle());
  EXPECT_EQ(reasoning_inputs().back().reasoning_object.id(), 1);
  EXPECT_TRUE(reasoning_inputs().back().reasoning_object.IsLinkedHeadVehicle());
}

TEST_F(ReasoningObjectTest, LinkedVehicleAgentWiseSignalTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kBusBulb);
  SetEgoPose(/*lane_id=*/129403, /*portion=*/0.5,
             /*speed=*/10.0);
  CreatePathWithLaneSequence({129403},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  prediction::pb::Agent& tractor =
      AddAgent(voy::perception::ObjectType::VEHICLE, /*id=*/1,
               /*lane_id=*/129401, /*portion=*/0.0,
               /*length=*/2.0,
               /*width=*/1.9, /*velocity=*/1.5, /*height=*/1.8);
  AddStraightPredictedTrajectory(
      /*start_lane_id=*/129401, /*start_portion=*/0.0,
      /*end_lane_id=*/129401, /*end_portion=*/0.0, 0.9, 1, tractor);

  prediction::pb::Agent& trailer =
      AddAgent(voy::perception::ObjectType::VEHICLE, /*id=*/2,
               /*lane_id=*/129377, /*portion=*/1.0,
               /*length=*/6.0,
               /*width=*/1.9, /*velocity=*/1.5, /*height=*/1.8);
  AddStraightPredictedTrajectory(
      /*start_lane_id=*/129377, /*start_portion=*/1.0,
      /*end_lane_id=*/129377, /*end_portion=*/1.0, 0.9, 1, trailer);

  tractor.mutable_tracked_object()->set_associated_vehicle_tail_id(2L);
  trailer.mutable_tracked_object()->set_associated_vehicle_head_id(1L);

  // Validate the agent list.
  EXPECT_EQ(2, agent_list().agent_list().size());
  Update();

  // Verify both reasoning objects are linked vehicles.
  EXPECT_EQ(reasoning_inputs().front().reasoning_object.id(), 2);
  EXPECT_TRUE(
      reasoning_inputs().front().reasoning_object.IsLinkedTailVehicle());
  EXPECT_EQ(reasoning_inputs().back().reasoning_object.id(), 1);
  EXPECT_TRUE(reasoning_inputs().back().reasoning_object.IsLinkedHeadVehicle());

  // Verify the agent wise signal.
  EXPECT_TRUE(reasoning_inputs().front().reasoning_object.IsBehindEgoFrontAxle(
      false, false));
  EXPECT_FALSE(
      reasoning_inputs().front().reasoning_object.IsBehindEgoFrontAxle());
  EXPECT_TRUE(
      reasoning_inputs().front().reasoning_object.IsBehindEgoLeadingBumper(
          false, false));
  EXPECT_FALSE(
      reasoning_inputs().front().reasoning_object.IsBehindEgoLeadingBumper());
  EXPECT_TRUE(
      reasoning_inputs().back().reasoning_object.IsFullyAhead(false, false));
  EXPECT_FALSE(reasoning_inputs().back().reasoning_object.IsFullyAhead());
}

TEST_F(ReasoningObjectTest, IsAgentLaterallyEncroachEgoLaneBoundaryTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kJunction);
  SetEgoPose(/*lane_id=*/57541, /*portion=*/0.1);
  CreatePathWithLaneSequence({57541},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/57543,
      /*portion=*/0.2, /*length=*/3.0, /*width=*/2.0,
      /*velocity=*/10.0, /*height=*/1.0, /*heading_diff_to_lane=*/M_PI / 8);
  // Construct agent's predicted trajectory is encroaching ego lane.
  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/57543, /*portion=*/0.3,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/57543, /*portion=*/0.4,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};
  AddMultiPointPredictedTrajectory(waypoints,
                                   /*likelihood=*/0.9, /*traj_id=*/1, agent);

  Update();

  ASSERT_FALSE(reasoning_inputs().empty());
  EXPECT_TRUE(reasoning_inputs()
                  .front()
                  .reasoning_object.is_laterally_encroach_ego_lane_boundary());
}

TEST_F(ReasoningObjectTest, PlanInitPoseOverlapSlice) {
  SetEgoPose(/*lane_id=*/16913, /*portion=*/0.2, /*speed=*/2.0);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/16913, /*portion=*/0.7,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/16913, /*start_portion=*/0.7,
                                 /*end_lane_id=*/16913, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);

  EXPECT_TRUE(world_context().construction_zones().empty());
  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();
  EXPECT_EQ(1, overlap_regions().size());
  const auto overlap_reg = overlap_regions().front();
  EXPECT_TRUE(overlap_reg.is_primary());
  EXPECT_EQ(reasoning_inputs().size(), 1);
  const auto& reasoning_object = reasoning_inputs().front().reasoning_object;
  const auto* plan_init_pose_overlap_slice_ptr =
      reasoning_object.plan_init_pose_overlap_slice_ptr();
  EXPECT_FALSE(plan_init_pose_overlap_slice_ptr == nullptr);
  const auto& slice = overlap_reg.overlap_slices().at(0);
  EXPECT_EQ(slice.relative_time_in_msec(),
            plan_init_pose_overlap_slice_ptr->relative_time_in_msec());
  EXPECT_EQ(GetPaddedOverlapStart(slice),
            GetPaddedOverlapStart(*plan_init_pose_overlap_slice_ptr));
  EXPECT_EQ(GetPaddedOverlapEnd(slice),
            GetPaddedOverlapEnd(*plan_init_pose_overlap_slice_ptr));
}

}  // namespace speed
}  // namespace planner
