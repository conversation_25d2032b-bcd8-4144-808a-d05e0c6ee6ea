#ifndef ONBOARD_PLANNER_WORLD_MODEL_TRAFFIC_PARTICIPANT_PREDICTED_TRAJECTORY_INTERPOLATOR_H_
#define ONBOARD_PLANNER_WORLD_MODEL_TRAFFIC_PARTICIPANT_PREDICTED_TRAJECTORY_INTERPOLATOR_H_

#include <memory>
#include <vector>

#include "geometry/model/oriented_box.h"
#include "math/interpolation.h"
#include "math/pose_2d.h"
#include "planner/world_model/traffic_participant/predicted_trajectory.h"
#include "prediction_protos/predicted_trajectory.pb.h"

namespace planner {

// Class InterpolatedPoses stores interpolated predicted trajectory, in terms of
// t-x, t-y, t-heading.
class PredictedTrajectoryInterpolator {
 public:
  PredictedTrajectoryInterpolator(
      int64_t object_id, const PredictedTrajectory& predicted_trajectory,
      bool include_ignorable_prediction);

  PredictedTrajectoryInterpolator(
      int64_t object_id, const std::vector<PredictedPose>& predicted_poses);

  explicit PredictedTrajectoryInterpolator(const pb::Trajectory& trajectory,
                                           double width = kDefaultWidthInM,
                                           double length = kDefaultLengthInM)
      : PredictedTrajectoryInterpolator(trajectory.poses(), width, length) {}

  template <typename TrajectoryPoses>
  explicit PredictedTrajectoryInterpolator(
      const TrajectoryPoses& trajectory_poses, double width = kDefaultWidthInM,
      double length = kDefaultLengthInM) {
    const int pose_size = trajectory_poses.size();
    DCHECK_GT(pose_size, 1);
    ts_.reserve(pose_size);
    xs_.reserve(pose_size);
    ys_.reserve(pose_size);
    headings_.reserve(pose_size);
    speeds_.reserve(pose_size);
    accels_.reserve(pose_size);
    odoms_.reserve(pose_size);
    widths_.reserve(pose_size);
    lengths_.reserve(pose_size);

    // Get rounded angle value. It is used for angle interpolation,
    // ensuring the abs diff of two consecutive angles less or equal than pi. To
    // deal with the discontinuity of the angles crossing -pi to pi.
    const auto get_round_angle = [](const double last_angle,
                                    const double current_angle) {
      return last_angle + math::AngleDiff(current_angle, last_angle);
    };

    for (const auto& pose : trajectory_poses) {
      ts_.push_back(static_cast<double>(pose.timestamp()));
      xs_.push_back(pose.x_pos());
      ys_.push_back(pose.y_pos());
      speeds_.push_back(pose.speed());
      accels_.push_back(pose.accel());
      odoms_.push_back(pose.odom());
      steer_wheel_angles_.push_back(pose.steering_wheel_angle());
      if (headings_.empty()) {
        headings_.push_back(pose.heading());
      } else {
        headings_.push_back(get_round_angle(headings_.back(), pose.heading()));
      }
      widths_.push_back(width);
      lengths_.push_back(length);
    }

    t_x_ = std::make_unique<math::Interpolator1d>(
        ts_.data(), xs_.data(), pose_size,
        math::pb::Interpolation1dType::kLinear);
    t_y_ = std::make_unique<math::Interpolator1d>(
        ts_.data(), ys_.data(), pose_size,
        math::pb::Interpolation1dType::kLinear);
    t_heading_ = std::make_unique<math::Interpolator1d>(
        ts_.data(), headings_.data(), pose_size,
        math::pb::Interpolation1dType::kLinear);
    t_speed_ = std::make_unique<math::Interpolator1d>(
        ts_.data(), speeds_.data(), pose_size,
        math::pb::Interpolation1dType::kLinear);
    t_accel_ = std::make_unique<math::Interpolator1d>(
        ts_.data(), accels_.data(), pose_size,
        math::pb::Interpolation1dType::kLinear);
    t_odom_ = std::make_unique<math::Interpolator1d>(
        ts_.data(), odoms_.data(), pose_size,
        math::pb::Interpolation1dType::kLinear);
    t_steer_wheel_angle_ = std::make_unique<math::Interpolator1d>(
        ts_.data(), steer_wheel_angles_.data(), pose_size,
        math::pb::Interpolation1dType::kLinear);
    t_width_ = std::make_unique<math::Interpolator1d>(
        ts_.data(), widths_.data(), pose_size,
        math::pb::Interpolation1dType::kLinear);
    t_length_ = std::make_unique<math::Interpolator1d>(
        ts_.data(), lengths_.data(), pose_size,
        math::pb::Interpolation1dType::kLinear);
    std::tie(start_timestamp_, end_timestamp_) = t_x_->GetRange();
  }

  // Gets the bounding box at the interpolated timestamp. If the query time is
  // not in the range of the trajectory's time span, no bounding box will be
  // returned.
  std::optional<math::geometry::OrientedBox2d> GetInterpolatedBoundingBox(
      double query_timestamp) const;

  // Gets the interpolated pose from the predicted trajectory.
  // TODO(jieruan): Check if it can be optimized when calling this method w/ a
  // sequence of increasing timestamp
  std::optional<pb::TrajectoryPose> GetInterpolatedPose2d(
      int64_t query_timestamp) const;

  // Accessor.
  int64_t object_id() const { return object_id_; }

 private:
  // The id of the object that contains the predicted trajectory.
  int64_t object_id_ = -1;

  double start_timestamp_ = 0;
  double end_timestamp_ = 0;

  // Vectors to store timestamps, x, y, width, length and headings.
  // respectively.
  std::vector<double> ts_;
  std::vector<double> xs_;
  std::vector<double> ys_;
  std::vector<double> widths_;
  std::vector<double> lengths_;
  std::vector<double> headings_;
  std::vector<double> speeds_;
  std::vector<double> accels_;
  std::vector<double> odoms_;
  std::vector<double> steer_wheel_angles_;

  // 1-D interpolators for t-x, t-y and t-heading.
  std::unique_ptr<math::Interpolator1d> t_x_;
  std::unique_ptr<math::Interpolator1d> t_y_;
  std::unique_ptr<math::Interpolator1d> t_heading_;
  std::unique_ptr<math::Interpolator1d> t_width_;
  std::unique_ptr<math::Interpolator1d> t_length_;
  std::unique_ptr<math::Interpolator1d> t_speed_;
  std::unique_ptr<math::Interpolator1d> t_accel_;
  std::unique_ptr<math::Interpolator1d> t_odom_;
  std::unique_ptr<math::Interpolator1d> t_steer_wheel_angle_;

  static constexpr double kDefaultWidthInM = 1.9;
  static constexpr double kDefaultLengthInM = 4.7;
};

}  // namespace planner

#endif  // ONBOARD_PLANNER_WORLD_MODEL_TRAFFIC_PARTICIPANT_PREDICTED_TRAJECTORY_INTERPOLATOR_H_
