#include "trajectory_observer.h"

#include <utility>

#include "av_comm/topics.h"
#include "glog/logging.h"

namespace remote_assist {

TrajectoryObserver::TrajectoryObserver(TrajectoryCallback callback)
    : callback_(std::move(callback)) {}

bool TrajectoryObserver::IsInterestedIn(
    const teleassist::pb::TeleassistRequest& message) {
  return (message.message_type() == kTrajectoryMessage);
}

std::shared_ptr<google::protobuf::Message> TrajectoryObserver::OnMessage(
    const teleassist::pb::TeleassistRequest& message,
    DataChannel data_channel) {
  auto traj_msg = std::make_shared<planner::pb::Trajectory>();
  if (!traj_msg->ParseFromString(message.message_data())) {
    LOG(ERROR) << "Received error Trajectory message format, len = "
               << message.message_data().length();
    return nullptr;
  }

  if (callback_) {
    callback_(traj_msg, data_channel);
  }
  return traj_msg;
}

}  // namespace remote_assist
