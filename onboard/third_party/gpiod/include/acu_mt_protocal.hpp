/**
 * @file acu_mt_protocal.hpp
 * <AUTHOR>
 * @brief
 * @version 0.1
 * @date 2024-08-27
 *
 * @copyright Copyright (c) 2024
 *
 */

#ifndef __ACU_MT_PROTOCAL_HPP__
#define __ACU_MT_PROTOCAL_HPP__

#include <iostream>
#include <limits>

namespace voyager {
namespace hardware {
#pragma pack(1)
struct AcuMtOrinModuleSt {
  int32_t cpu_temp_;  // CPU温度 millidegrees
  int32_t gpu_temp_;  // GPU温度 millidegrees
  int32_t soc0_temp_;
  int32_t soc1_temp_;
  int32_t soc2_temp_;
  int32_t tj_temp_;      // 结温 (Junction Temperature)
  int32_t tboard_temp_;  // 主板温度
  int32_t tdiode_temp_;
  int32_t vdd_gpu_soc_;  // 功率 mW
  int32_t vdd_cpu_cv_;
  int32_t vin_sys_5v0_;
  int32_t vddq_vdd2_1v8ao_;
  int32_t gpu_usage_;
  int32_t cpu0_usage_;
  int32_t cpu1_usage_;
  int32_t cpu2_usage_;
  int32_t cpu3_usage_;
  int32_t cpu4_usage_;
  int32_t cpu5_usage_;
  int32_t cpu6_usage_;
  int32_t cpu7_usage_;
};

struct AcuMtOrinPhySt {
  int32_t q2220_temp_ = INT32_MAX;
  uint8_t q2220_pma_link_ = UINT8_MAX;
  uint8_t q2220_pcs_link_ = UINT8_MAX;
  int32_t q2220_qsi_level_ = INT32_MAX;
  int32_t bcm89890_temp_ = INT32_MAX;
  uint8_t bcm89890_pma_link_ = UINT8_MAX;
  uint8_t bcm89890_pcs_link_ = UINT8_MAX;
  int32_t bcm89890_qsi_level_ = INT32_MAX;
};

struct AcuMtPcieswSt {
  int32_t temp_ = INT32_MIN;
  uint8_t host1_link_ = 0;
  float host1_eg_bw_ = std::numeric_limits<float>::quiet_NaN();
  float host1_in_bw_ = std::numeric_limits<float>::quiet_NaN();
  uint8_t host2_link_ = 0;
  float host2_eg_bw_ = std::numeric_limits<float>::quiet_NaN();
  float host2_in_bw_ = std::numeric_limits<float>::quiet_NaN();
  uint8_t host3_link_ = 0;
  float host3_eg_bw_ = std::numeric_limits<float>::quiet_NaN();
  float host3_in_bw_ = std::numeric_limits<float>::quiet_NaN();
};

struct AcuMtOrinMax20087St {
  int32_t volt1_ = INT32_MAX;  // 12V；0.01V
  int32_t volt2_ = INT32_MAX;
  int32_t volt3_ = INT32_MAX;
  int32_t volt4_ = INT32_MAX;
  int32_t curr1_ = INT32_MAX;  // 100-600mA; 0.001mA
  int32_t curr2_ = INT32_MAX;
  int32_t curr3_ = INT32_MAX;
  int32_t curr4_ = INT32_MAX;
  int32_t vin_ = INT32_MAX;  // 8V; 0.01V
  int32_t vdd_ = INT32_MAX;  // 3.3V; 0.01V
  int32_t viset_ = INT32_MAX;
  int32_t reverse_ = INT32_MAX;
  uint8_t stat1_ = 0;  // bit0:ts; bit1:Oc; bit2:Ov; bit3:Uv
  uint8_t stat2_ = 0;
  uint8_t stat3_ = 0;
  uint8_t stat4_ = 0;
};

struct AcuMtOrinMax96712St {
  uint8_t gmslb_lock_ = UINT8_MAX;
  uint8_t gmslc_lock_ = UINT8_MAX;
  uint8_t gmsld_lock_ = UINT8_MAX;
  uint8_t gmsla_lock_ = UINT8_MAX;
  uint8_t pwr0_;
  uint8_t intr5_;
  uint8_t gmsla_dec_err_;
  uint8_t gmslb_dec_err_;
  uint8_t gmslc_dec_err_;
  uint8_t gmsld_dec_err_;
  uint8_t gmsla_idle_err_;
  uint8_t gmslb_idle_err_;
  uint8_t gmslc_idle_err_;
  uint8_t gmsld_idle_err_;
  uint8_t vid_pxl_crc_err_int_;
  uint8_t line0_crc_err_;
  uint8_t line1_crc_err_;
  uint8_t line2_crc_err_;
  uint8_t line3_crc_err_;
  uint8_t video0_rx8_;
  uint8_t video1_rx8_;
  uint8_t video2_rx8_;
  uint8_t video3_rx8_;
  uint8_t video0_prbs_err_;
  uint8_t video1_prbs_err_;
  uint8_t video2_prbs_err_;
  uint8_t video3_prbs_err_;
  uint8_t video0_lock_;
  uint8_t video1_lock_;
  uint8_t video2_lock_;
  uint8_t video3_lock_;
  uint8_t back_top11_;
  uint8_t back_top25_;
  uint8_t mipi_tx2_phy0_;
  uint8_t mipi_tx2_phy1_;
  uint8_t mipi_tx2_phy2_;
  uint8_t mipi_tx2_phy3_;
  uint8_t mem_ecc0_;
  uint8_t mem_ecc_err1_cnt_;
  uint8_t mem_ecc_err2_cnt_;
  uint8_t mem_ecc_err_dbg_;
  uint8_t mem_ecc_flag_;
  uint8_t gmsla_eye_mom_err_cnt_;
  uint8_t gmslb_eye_mom_err_cnt_;
  uint8_t gmslc_eye_mom_err_cnt_;
  uint8_t gmsld_eye_mom_err_cnt_;
};
#pragma pack()
}  // namespace hardware
}  // namespace voyager
#endif
