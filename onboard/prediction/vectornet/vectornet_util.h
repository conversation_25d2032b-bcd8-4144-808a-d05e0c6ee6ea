#ifndef ONBOARD_PREDICTION_VECTORNET_VECTORNET_UTIL_H_
#define ONBOARD_PREDICTION_VECTORNET_VECTORNET_UTIL_H_

#include <functional>
#include <map>
#include <string>
#include <utility>
#include <vector>

#include "base/base_dir.h"
#include "neural_net/tensor.h"
#include "prediction/common/utility/debug_utility.h"
#include "prediction/concept/trajectory/predicted_trajectory.h"
#include "prediction/scene_snapshot/scene_snapshot.h"
#include "prediction/vectornet/ml_planner_output.h"
#include "prediction/vectornet/vectornet_feature_extractor.h"
#include "prediction/vectornet/vectornet_predictor.h"
#include "prediction_protos/trajectory_generation_type.pb.h"
#include "voy_protos/model_config.pb.h"
#include "voy_protos/trajectory.pb.h"

namespace prediction {
namespace vectornet_util {

// Parses vectornet output tensor vector to a map based on config.
std::map<pb::ModelConfig::OutputTensorType, neural_net::Tensor>
BuildTensorReferenceMap(
    std::vector<neural_net::Tensor>&& tensors,
    const google::protobuf::RepeatedField<int>& tensor_types);

// Parses vectornet_outputs to trajectories of each target_agents.
// NOTE(boyu): the predicted results in |outputs| are assumed to have the same
// order as keys in |target_agents|.
std::map<ObjectId, VectornetOutput> ConvertTensorsToTrajectories(
    const std::map<pb::ModelConfig::OutputTensorType, neural_net::Tensor>&
        output_tensor_map,
    const std::map<int64_t, std::vector<MapObjectKey>>&
        background_map_object_keys,
    const std::vector<const Agent*>& target_agents, const Agent& ego_agent,
    ::pb::ModelType model_type, int num_modal,
    const google::protobuf::RepeatedPtrField<std::string>& modal_names,
    int lane_split_factor, int max_num_map_objects_segment,
    const std::vector<bool>* batch_is_ignoring_ego_mask = nullptr);
// Parses ML Planner model output to MLPlannerOutput.
MLPlannerOutput ConvertTensorsToMLPlannerOutput(
    const std::map<pb::ModelConfig::OutputTensorType, neural_net::Tensor>&
        output_tensor_map,
    const std::vector<RouteFeature>& route_features, const Agent& ego_agent,
    int num_routes, int num_modals, int path_horizon, int traj_horizon,
    ::pb::ModelType model_type);

// Computes and sets timestamap, z, heading (if is_set_heading is true), speed
// and acceleration given x, y.
void ComputeAndSetFields(
    int64_t current_timestamp, const voy::TrackedObject& tracked_object,
    std::vector<planner::pb::TrajectoryPose>* poses, bool is_set_heading = true,
    ::pb::ModelType model_type = ::pb::ModelType::UNKNOWN_MODEL_TYPE);

int64_t GetTensorSize(const neural_net::Tensor& tensor);

// Returns true if the given agent is the leading vehicle in front of the
// tailing agent. There are four conditions to define a leading vehicle. 1) The
// candidate's type must be vehicle. 2) The tailing agent's lateral distance to
// the reference line should not be too large. 3) The candidate must be ahead of
// the tailing agent. 4) The candidate must be not far from the centerline. 5)
// The relative heading between the provided lane sequence and candidate agent
// must be in a mini range. There may be more than one vehicles meet the
// constraints.
bool IsCandidateLeadingVehicleOfAgent(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const voy::TrackedObject& candidate_tracked_object,
    const voy::TrackedObject& tailing_tracked_object,
    const pnc_map::PredictionMapService* prediction_map_service,
    bool candidate_is_stationary);

// TODO(hor): Unify the definition of ForLargeVehcileHead based on
// kLengthThresholdForLargeVechicleHeadand IsLongVehicle based on
// constants::kLongVehicleLengthThreshold. Currently, the former is used to
// determine whether the agent has passed in the large-vehicle head of DL model,
// while the latter is used for tagging samples.
// Returns true if the agent has been fed into large vehicle head for DL model.
bool IsForLargeVehicleHead(const Agent& agent);

// Finds the leading vehicle of the given agent. Returns nullptr if not found.
const Agent* FindLeadingVehicleOfAgent(
    const SceneSnapshot& scene_snapshot,
    const std::vector<const Agent*>& active_agents, const Agent& agent);

FlagMessage ShouldSkipTraditionalPrediction(const SceneSnapshot& scene_snapshot,
                                            const Agent& agent);

// Gets the pointers at the given starting index and the endding index (computed
// by given start_idx + length) in the first dimension of the tensor.
template <typename T>
std::pair<T*, T*> GetTensorBeginAndEnd(neural_net::Tensor* tensor,
                                       int64_t start_idx, int64_t length) {
  const std::vector<int64_t>& tensor_dim_sizes = tensor->dim_sizes();
  DCHECK(!tensor_dim_sizes.empty());
  const int64_t batch_size = tensor->dim_size(0);

  DCHECK_GE(start_idx, 0);
  DCHECK_LE(start_idx + length, batch_size);
  // Note(renhao): The template type of T must be checked to ensure that the
  // size of the T matches the size the tensor's raw data type.
  DCHECK_EQ(sizeof(T), neural_net::DataTypeSize(tensor->data_type()));

  const int64_t single_tensor_sizes =
      std::accumulate(tensor_dim_sizes.begin() + 1, tensor_dim_sizes.end(), 1,
                      std::multiplies<int64_t>());
  T* const begin =
      static_cast<T*>(tensor->data()) + single_tensor_sizes * start_idx;
  T* const end = begin + single_tensor_sizes * length;
  return std::make_pair(begin, end);
}

// Gets the pointers at the begin and end of the given tensor.
template <typename T>
std::pair<T*, T*> GetTensorBeginAndEnd(neural_net::Tensor* tensor) {
  const std::vector<int64_t>& tensor_dim_sizes = tensor->dim_sizes();
  DCHECK(!tensor_dim_sizes.empty());
  return GetTensorBeginAndEnd<T>(tensor, 0, tensor->dim_size(0));
}

// Returns true if vectornet prediction trajectories will be added into agent.
// Otherwise, return false and the reason why vectornet prediction is not added.
FlagMessage ShouldAddVectornetTrajectories(const SceneSnapshot& scene_snapshot,
                                           const Agent& agent);

// Builds stationary intention by vectornet output.
pb::StationaryIntention BuildStationaryIntention(
    const SceneSnapshot& scene_snapshot, const Agent& ego_agent,
    const Agent& agent, const VectornetOutput& vectornet_output);

// Given x, y covariance matrix and agent's heading, sets lateral and
// longitudinal standard deviations.
void SetLatLongStandardDeviation(
    double heading, planner::pb::TrajectoryPoseUncertainty* uncertainty);

// Converts trajectory tensor to PredictedTrajectory.
std::vector<std::vector<PredictedTrajectory>> GetPredictedTrajectories(
    const neural_net::Tensor& trajectory_tensor,
    const neural_net::Tensor& score_tensor,
    const float* covariance_tensor_begin,
    const float* box_headings_tensor_begin,
    const float* box_headings_tensor_end, const float* speeds_tensor_begin,
    const float* speeds_tensor_end, const float* accels_tensor_begin,
    const float* accels_tensor_end, const float* steerings_tensor_begin,
    const float* steerings_tensor_end,
    const int32_t* modals_index_int32_tensor_begin,
    const int32_t* modals_index_int32_tensor_end,
    const std::vector<const Agent*>& target_agents, const Agent& ego_agent,
    ::pb::ModelType model_type,
    const google::protobuf::RepeatedPtrField<std::string>& modal_names,
    int num_modals,
    const std::map<int64_t, std::vector<MapObjectKey>>&
        background_map_object_keys,
    int lane_split_factor, int max_num_map_objects_segment);

// Infers the trajectory motion type based on predicted trajectory poses and
// current box heading
pb::TrajectoryMotionType InferTrajectoryMotionType(
    PredictedTrajectory& trajectory, const Agent& agent);

pb::TrajectoryMotionType InferLargeVehicleTrajectoryMotionType(
    PredictedTrajectory& trajectory, const Agent& agent,
    const Agent& ego_agent);

// Computes signed menger curvatures for the trajectory points.
// Wiki: https://en.wikipedia.org/wiki/Menger_curvature
std::vector<double> ComputeCurvaturesForTrajPoints(
    const AgentState& agent_state,
    const std::vector<planner::pb::TrajectoryPose>& traj_poses);

// Returns true if it is considered Start-To-Move based on given prob, TTM
// (time-to-move) mean and standard deviation.
bool IsStartToMove(double stm_prob, double ttm_mean, double ttm_sd);

// Calculates the ellipse distance from the target agent to the given polyline
// curve.
double CalculateDynamicEllipseDistanceBasedOnSpeed(
    const math::geometry::Point2d& target_agent_position,
    double target_agent_heading, double target_agent_speed,
    const math::geometry::PolylineCurve2d& polyline_curve);

// Converts the lane change predictions in output tensor map to |VectorOutput|.
std::map<ObjectId, VectornetOutput> ConvertTensorToCBPLaneChangeResults(
    const std::vector<const Agent*>& target_agents, const Agent& ego_agent,
    const std::map<pb::ModelConfig::OutputTensorType, neural_net::Tensor>&
        output_tensor_map,
    int max_num_agents);

// Gets the index of ego agent in background agents.
inline int GetEgoIndexInBackgroundAgents(ObjectId target_object_id) {
  return target_object_id == constants::kRobotObjId ? 0 : 1;
}

// Gets the trailer agent id for the agent if it is a tractor.
inline int64_t GetTrailerAgentID(const Agent& agent) {
  return agent.tracked_object().associated_vehicle_head_id() ==
                 constants::kInvalidTractorTrailerId
             ? agent.tracked_object().associated_vehicle_tail_id()
             : constants::kInvalidTractorTrailerId;
}

// Infer and set trajectory pose gear according to box heading.
void SetTrajectoryPoseGear(const Agent& agent, PredictedTrajectory* trajectory);

// Add RT event for gear flicker and gear change.
void AddRtEventForGearFlickerAndChange(const Agent& agent,
                                       const PredictedTrajectory& trajectory);

bool IsMovingUnknownObject(const voy::TrackedObject& tracked_object);

// Gets the attention scores from the tensor if present, otherwise returns an
// empty vector.
const std::vector<double> GetAttentionScoreFromTensor(
    const std::map<pb::ModelConfig::OutputTensorType, neural_net::Tensor>&
        output_tensor_map,
    pb::ModelConfig::OutputTensorType score_name, int num_valid_objects);

const std::vector<int> GetTopKScoresIndices(const std::vector<double>& scores,
                                            int k);

// Gets a sequence of numbers, starting from a given number, and increments by
// 1, and stops before a specified number. (Left closed and right opened)
const std::vector<int> GetRangeIndices(int start, int end);

}  // namespace vectornet_util
}  // namespace prediction

#endif  // ONBOARD_PREDICTION_VECTORNET_VECTORNET_UTIL_H_
