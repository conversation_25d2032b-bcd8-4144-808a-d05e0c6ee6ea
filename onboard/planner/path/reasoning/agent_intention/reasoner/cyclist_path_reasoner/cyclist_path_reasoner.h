#ifndef ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_REASONER_CYCLIST_PATH_REASONER_CYCLIST_PATH_REASONER_H_
#define ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_REASONER_CYCLIST_PATH_REASONER_CYCLIST_PATH_REASONER_H_

#include <memory>
#include <string>
#include <vector>

#include "planner/path/reasoning/agent_intention/reasoner/abstract_reasoner.h"
#include "planner/path/reasoning/agent_intention/reasoner/ego_context_info.h"

namespace planner {
namespace path {

class CyclistPathReasoner : public AbstractReasoner {
 public:
  explicit CyclistPathReasoner(const EgoContextInfo& ego_context_info)
      : AbstractReasoner(ego_context_info, pb::AgentReasonerId::CYCLIST) {}
  virtual ~CyclistPathReasoner() = default;

  bool PreReason(
      const AgentInLaneStates& agent_inlane_state,
      const LateralBlockingStateMetaData& blocking_state_data,
      const AgentSLBoundary& object_sl_boundary,
      TrajectoryIntentionMetaData& trajectory_intention_data) const final;

  bool PreReason(
      const ObjectOccupancyState& object_occupancy_state,
      const LateralBlockingStateMetaData& blocking_state_data,
      const AgentSLBoundary& object_sl_boundary,
      TrajectoryIntentionMetaData& trajectory_intention_data) const final;

  bool ShouldInvokePreReason(
      const LateralBlockingStateMetaData& blocking_state_meta_data,
      const AgentInLaneStates& agent_inlane_state,
      pb::BehaviorType behavior_type, bool is_stationary) const final {
    (void)blocking_state_meta_data;
    (void)behavior_type;
    (void)is_stationary;
    return agent_inlane_state.agent_metadata.agent_type ==
               voy::perception::ObjectType::CYCLIST &&
           !blocking_state_meta_data.reasoning_info
                .is_interested_agent_during_lane_change_or_abort();
  }

  bool ShouldInvokePreReason(
      const LateralBlockingStateMetaData& blocking_state_meta_data,
      const ObjectOccupancyState& object_occupancy_state,
      pb::BehaviorType behavior_type, bool is_stationary) const final {
    (void)blocking_state_meta_data;
    (void)behavior_type;
    (void)is_stationary;
    return object_occupancy_state.object_type() ==
               voy::perception::ObjectType::CYCLIST &&
           !blocking_state_meta_data.reasoning_info
                .is_interested_agent_during_lane_change_or_abort();
  }

  bool ShouldInvokePreRepulsionReason(
      const LateralBlockingStateMetaData& blocking_state_meta_data,
      voy::perception::ObjectType agent_type, pb::BehaviorType behavior_type,
      bool is_stationary, pb::AgentImaginaryActualType imaginary_actual_type,
      std::string& debug_str) const final;

  bool ShouldInvokePostReason(
      const LateralBlockingStateMetaData& blocking_state_meta_data,
      voy::perception::ObjectType agent_type, pb::BehaviorType behavior_type,
      bool is_stationary) const final {
    (void)blocking_state_meta_data;
    (void)behavior_type;
    (void)is_stationary;
    return agent_type == voy::perception::ObjectType::CYCLIST &&
           !blocking_state_meta_data.reasoning_info
                .is_interested_agent_during_lane_change_or_abort();
  }

  bool PostReason(const AgentInLaneStates& agent_inlane_state,
                  const LateralBlockingStateMetaData& blocking_state_data,
                  const TrajectoryIntentionMetaData& trajectory_intention_data,
                  IntentionResultMetaData& intention_result) const final;

  bool PostReason(const ObjectOccupancyState& object_occupancy_state,
                  const LateralBlockingStateMetaData& blocking_state_data,
                  const TrajectoryIntentionMetaData& trajectory_intention_data,
                  IntentionResultMetaData& intention_result) const final;

  std::unique_ptr<RepulsionMetaData> PreRepulsionReason(
      const AgentInLaneStates& agent_inlane_state,
      const LateralBlockingStateMetaData& blocking_state_data,
      const std::optional<pb::Path>& reusable_last_path,
      const RequiredLateralGap& required_lat_gap,
      const std::vector<path::FrameAnalyzerResult>& frame_analyzer_results,
      std::string& debug_str) const;

  std::unique_ptr<RepulsionMetaData> PreRepulsionReason(
      const ObjectOccupancyState& object_occupancy_state,
      const LateralBlockingStateMetaData& blocking_state_data,
      const std::optional<pb::Path>& reusable_last_path,
      const RequiredLateralGap& required_lat_gap,
      const std::vector<path::FrameAnalyzerResult>& frame_analyzer_results,
      std::string& debug_str) const;

 private:
  void AdjustIntentionResultForPassDecision(
      const AgentInLaneStates& agent_inlane_state,
      const pb::SnapshotIntention::PassState& tracked_pass_decision,
      IntentionResultMetaData& intention_result) const;

  void AdjustIntentionResultForPassDecision(
      const ObjectOccupancyState& object_occupancy_state,
      const pb::SnapshotIntention::PassState& tracked_pass_decision,
      IntentionResultMetaData& intention_result) const;

  void AdjustIntentionResultForYieldDecision(
      const AgentSnapshotInLaneParam& inlane_param,
      const pb::TrajectoryReasoningInfo& reasoning_info,
      const pb::BlockingSequenceType& blocking_sequence_type,
      const std::vector<pb::BlockingSequence>& blocking_sequences,
      IntentionResultMetaData& intention_result) const;

  void AdjustIntentionResultForYieldDecision(
      const ObjectOccupancyState& object_occupancy_state,
      const pb::TrajectoryReasoningInfo& reasoning_info,
      const pb::BlockingSequenceType& blocking_sequence_type,
      const std::vector<pb::BlockingSequence>& blocking_sequences,
      IntentionResultMetaData& intention_result) const;
};

}  // namespace path
}  // namespace planner

#endif  // ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_REASONER_CYCLIST_PATH_REASONER_CYCLIST_PATH_REASONER_H_
