#ifndef OFFBOARD_FALLBACK_ADS_ACU_SIGNAL_INPUT_POSE_DECODE_POSE_DECODE_H_
#define OFFBOARD_FALLBACK_ADS_ACU_SIGNAL_INPUT_POSE_DECODE_POSE_DECODE_H_

#include "_out/Appl/GenData/Rte_Type.h"

namespace fallback_adas {
namespace acu_signal_input {

void LocalizationPoseDecode(LocalizationPoseInput* pose_ptr);

void GnssPoseDecode(GnssPoseInput* gnss_pose_ptr);

}  // namespace acu_signal_input
}  // namespace fallback_adas

#endif  // OFFBOARD_FALLBACK_ADS_ACU_SIGNAL_INPUT_POSE_DECODE_POSE_DECODE_H_
