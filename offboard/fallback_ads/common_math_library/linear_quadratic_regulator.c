#include "common_math_library/linear_quadratic_regulator.h"

#include <float.h>
#include <math.h>
#include <stdint.h>

#include "common_math_library/matrix.h"

// solve algebraic Riccati equation (DARE) by iterative method
void SolveRiccatiEquation(const CML_Matrix* A_ptr, const CML_Matrix* B_ptr,
                          const CML_Matrix* Q_ptr, const CML_Matrix* R_ptr,
                          float tolerance, const uint16_t max_num_iteration,
                          CML_Matrix* K_ptr, uint16_t* num_iteration_ptr,
                          float* diff_ptr) {
  // matrix a and matrix b transpose
  CML_MatrixCreate(AT_ptr, 4, 4);
  CML_MatrixTranspose(AT_ptr, A_ptr);
  CML_MatrixCreate(BT_ptr, 1, 4);
  CML_MatrixTranspose(BT_ptr, B_ptr);

  CML_MatrixCreate(P_ptr, 4, 4);
  CML_MatrixCopy(P_ptr, Q_ptr);

  uint32_t num_iteration = 0;
  float diff = FLT_MAX;
  // P_next = AT * P * A - (AT * P * B) * inv(R + BT * P * B) * (BT * P * A) + Q
  // K = inv(R + BT * P * B) * (BT * P * A)
  // P_next = AT * P * A - (AT * P * B) * K + Q
  while (num_iteration < max_num_iteration && diff > tolerance) {
    // P_next part 1: AT * P * A
    CML_MatrixCreate(AT_P_ptr, 4, 4);
    CML_MatrixMul(AT_P_ptr, AT_ptr, P_ptr);
    CML_MatrixCreate(AT_P_A_ptr, 4, 4);
    CML_MatrixMul(AT_P_A_ptr, AT_P_ptr, A_ptr);

    // P_next part 2: AT * P * B
    CML_MatrixCreate(AT_P_B_ptr, 4, 1);
    CML_MatrixMul(AT_P_B_ptr, AT_P_ptr, B_ptr);

    // P_next part 3: K = inv(R + BT * P * B) * (BT * P * A)
    // inv(R + BT * P * B)
    CML_MatrixCreate(BT_P_ptr, 4, 1);
    CML_MatrixMul(BT_P_ptr, BT_ptr, P_ptr);
    CML_MatrixCreate(BT_P_B_ptr, 1, 1);
    CML_MatrixMul(BT_P_B_ptr, BT_P_ptr, B_ptr);

    CML_MatrixCreate(R_Add_BT_P_B_ptr, 1, 1);
    CML_MatrixAdd(R_Add_BT_P_B_ptr, BT_P_B_ptr, R_ptr);

    CML_MatrixCreate(inv_R_Add_BT_P_B_ptr, 1, 1);
    CML_MatrixInv(inv_R_Add_BT_P_B_ptr, R_Add_BT_P_B_ptr);

    // K = inv(R + BT * P * B) * (BT * P * A)
    CML_MatrixCreate(BT_P_A_ptr, 4, 1);
    CML_MatrixMul(BT_P_A_ptr, BT_P_ptr, A_ptr);
    CML_MatrixMul(K_ptr, inv_R_Add_BT_P_B_ptr, BT_P_A_ptr);

    // P_next = AT * P * A - (AT * P * B) * K + Q
    CML_MatrixCreate(AT_P_B_K_ptr, 4, 4);
    CML_MatrixMul(AT_P_B_K_ptr, AT_P_B_ptr, K_ptr);
    CML_MatrixCreate(P_next_ptr, 4, 4);
    CML_MatrixSub(P_next_ptr, AT_P_A_ptr, AT_P_B_K_ptr);
    CML_MatrixAdd(P_next_ptr, P_next_ptr, Q_ptr);

    // calculate diff
    CML_MatrixCreate(P_diff_ptr, 4, 4);
    CML_MatrixSub(P_diff_ptr, P_ptr, P_next_ptr);
    diff = fmaxf(fabsf(CML_MatrixMax(P_diff_ptr)),
                 fabsf(CML_MatrixMin(P_diff_ptr)));
    CML_MatrixCopy(P_ptr, P_next_ptr);

    num_iteration++;
  }

  *num_iteration_ptr = num_iteration;
  *diff_ptr = diff;
}
