#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_route_association/approximate_map_query_util.h"

#include <algorithm>
#include <limits>

#include "geometry/algorithms/arithmetic.h"
#include "math/enum.h"

namespace planner {

namespace route_association {

double ComputeApproxDistanceBetweenPointAndLane(
    const pnc_map::Lane* lane, const math::geometry::Point2d& point) {
  DCHECK(lane != nullptr);
  // Get corner points at exiting and entering boundaries.
  const math::geometry::Point2d& lane_exiting_boundary_leftmost_point =
      lane->left_marking()
          ->lane_marking_segments()
          .back()
          .line_curve.polyline()
          .back();
  const math::geometry::Point2d& lane_exiting_boundary_rightmost_point =
      lane->right_marking()
          ->lane_marking_segments()
          .back()
          .line_curve.polyline()
          .back();
  const math::geometry::Point2d& lane_entering_boundary_leftmost_point =
      lane->left_marking()
          ->lane_marking_segments()
          .front()
          .line_curve.polyline()
          .front();
  const math::geometry::Point2d& lane_entering_boundary_rightmost_point =
      lane->right_marking()
          ->lane_marking_segments()
          .front()
          .line_curve.polyline()
          .front();
  // Form a vector representing exiting boundary from left to right.
  const math::geometry::Point2d exiting_boundary_direction =
      math::geometry::Subtract(lane_exiting_boundary_rightmost_point,
                               lane_exiting_boundary_leftmost_point);
  // Check if the queried point is to the left side of the exiting boundary.
  const bool is_query_point_left_to_exiting =
      math::geometry::CrossProductNorm(
          exiting_boundary_direction,
          math::geometry::Subtract(
              point, lane_exiting_boundary_leftmost_point)) >= 0.0;
  // Form a vector representing enterings boundary from left to right.
  const math::geometry::Point2d entering_boundary_direction =
      math::geometry::Subtract(lane_entering_boundary_rightmost_point,
                               lane_entering_boundary_leftmost_point);
  // Check if the queried point is to the right side of the entering boundary.
  const bool is_query_point_right_to_entering =
      math::geometry::CrossProductNorm(
          entering_boundary_direction,
          math::geometry::Subtract(
              point, lane_entering_boundary_leftmost_point)) <= 0.0;
  // Check if the whole entering boundary is to the right side exiting boundary.
  const bool is_entering_to_right_side_of_exiting =
      math::geometry::CrossProductNorm(
          exiting_boundary_direction,
          math::geometry::Subtract(lane_entering_boundary_leftmost_point,
                                   lane_exiting_boundary_leftmost_point)) <=
          0.0 &&
      math::geometry::CrossProductNorm(
          exiting_boundary_direction,
          math::geometry::Subtract(lane_entering_boundary_rightmost_point,
                                   lane_exiting_boundary_leftmost_point)) <=
          0.0;
  // Check if the whole exiting boundary is to the left side entering boundary.
  const bool is_exiting_to_left_side_of_entering =
      math::geometry::CrossProductNorm(
          entering_boundary_direction,
          math::geometry::Subtract(lane_exiting_boundary_leftmost_point,
                                   lane_entering_boundary_leftmost_point)) >=
          0.0 &&
      math::geometry::CrossProductNorm(
          entering_boundary_direction,
          math::geometry::Subtract(lane_exiting_boundary_rightmost_point,
                                   lane_entering_boundary_leftmost_point)) >=
          0.0;
  // Do cheap computation if not self-intersecting and the queried point falls
  // into regions above exiting boundary or below entering boundary.
  if (is_entering_to_right_side_of_exiting &&
      is_exiting_to_left_side_of_entering) {
    if (is_query_point_left_to_exiting) {
      return std::sqrt(math::geometry::ComparableDistance(
          point, lane_exiting_boundary_leftmost_point,
          lane_exiting_boundary_rightmost_point));
    }
    if (is_query_point_right_to_entering) {
      return std::sqrt(math::geometry::ComparableDistance(
          point, lane_entering_boundary_leftmost_point,
          lane_entering_boundary_rightmost_point));
    }
  }

  // Do expensive computation otherwise.
  const math::ProximityQueryInfo proximity_to_left_marking =
      lane->left_marking()->line().GetProximity(
          point, math::pb::UseExtensionFlag::kForbid);
  // If queried point is not relatively within left marking, distance to the
  // lane should be the min of distance to exiting boundary and distance to
  // entering boundary.
  if (proximity_to_left_marking.relative_position !=
      math::RelativePosition::kWithIn) {
    double dist_to_exiting_boundary = std::numeric_limits<double>::infinity();
    double dist_to_entering_boundary = std::numeric_limits<double>::infinity();
    // In case of that the left boundary is shorter than the right counterpart
    // yet the queried point is indeed within the lane, we need to double check
    // the topological relationship between the queried point and the exiting
    // and entering boundaries.
    if (is_query_point_left_to_exiting) {
      dist_to_exiting_boundary = std::sqrt(math::geometry::ComparableDistance(
          point, lane_exiting_boundary_leftmost_point,
          lane_exiting_boundary_rightmost_point));
    }
    if (is_query_point_right_to_entering) {
      dist_to_entering_boundary = std::sqrt(math::geometry::ComparableDistance(
          point, lane_entering_boundary_leftmost_point,
          lane_entering_boundary_rightmost_point));
    }
    if (dist_to_exiting_boundary != std::numeric_limits<double>::infinity() ||
        dist_to_entering_boundary != std::numeric_limits<double>::infinity()) {
      return std::min(dist_to_exiting_boundary, dist_to_entering_boundary);
    }
  }
  // If queried point is relatively within left marking, using lane width to
  // compute the distance.
  const double lane_width_at_proximal_point =
      lane->right_marking()->line().GetDistance(
          {proximity_to_left_marking.x, proximity_to_left_marking.y},
          math::pb::UseExtensionFlag::kForbid);
  // If the queried point is on left side of left marking, the distance to lane
  // is just the distance to the left marking. Otherwise, if it is on right side
  // of left marking, the approximate distance to lane is computated as the
  // distance to left marking subtracting the lane width at the point's
  // proximity to left marking. For the latter case, we cap the minimum distance
  // to zero, indicating the point is within the lane.
  if (proximity_to_left_marking.side == math::pb::kRight) {
    return proximity_to_left_marking.dist <= lane_width_at_proximal_point
               ? 0.0
               : proximity_to_left_marking.dist - lane_width_at_proximal_point;
  }
  return proximity_to_left_marking.dist;
}

std::vector<QueriedLaneAndApproxDistance> GetLanesWithApproxDistanceInRadius(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const hdmap::Point& point, double radius) {
  std::vector<const hdmap::Lane*> candidate_lanes =
      joint_pnc_map_service.pnc_map_service()->hdmap()->GetCandidateLanes(
          point, radius);

  std::vector<int64_t> candidate_lane_ids;
  candidate_lane_ids.reserve(candidate_lanes.size());
  std::for_each(candidate_lanes.begin(), candidate_lanes.end(),
                [&candidate_lane_ids](const hdmap::Lane* candidate_lane) {
                  candidate_lane_ids.push_back(candidate_lane->id());
                });
  std::vector<const pnc_map::Lane*> pnc_candidate_lanes =
      joint_pnc_map_service.GetLaneSequence(candidate_lane_ids);

  std::vector<QueriedLaneAndApproxDistance> results;
  results.reserve(candidate_lanes.size());
  const math::geometry::Point2d queried_point{point.x(), point.y()};

  for (const pnc_map::Lane* pnc_candidate_lane : pnc_candidate_lanes) {
    if (pnc_candidate_lane == nullptr) {
      continue;
    }
    double approx_distance = ComputeApproxDistanceBetweenPointAndLane(
        pnc_candidate_lane, queried_point);
    if (approx_distance <= radius) {
      results.push_back({pnc_candidate_lane, approx_distance});
    }
  }
  return results;
}

}  // namespace route_association
}  // namespace planner
