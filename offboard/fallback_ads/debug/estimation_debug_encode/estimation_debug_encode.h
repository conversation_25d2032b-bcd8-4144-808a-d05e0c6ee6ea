#ifndef OFFBOARD_FALLBACK_ADS_DEBUG_ESTIMATION_DEBUG_ENCODE_ESTIMATION_DEBUG_ENCODE_H_
#define OFFBOARD_FALLBACK_ADS_DEBUG_ESTIMATION_DEBUG_ENCODE_ESTIMATION_DEBUG_ENCODE_H_

#include "_out/Appl/GenData/Rte_Type.h"

namespace fallback_adas {
namespace debug {

void EstimationDebugEncode(const EstimationDebug& estimation_debug);

}  // namespace debug
}  // namespace fallback_adas

#endif  // OFFBOARD_FALLBACK_ADS_DEBUG_ESTIMATION_DEBUG_ENCODE_ESTIMATION_DEBUG_ENCODE_H_
