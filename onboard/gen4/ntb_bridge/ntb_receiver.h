#ifndef ONBOARD_GEN4_NTB_BRIDGE_NTB_RECEIVER_H_
#define ONBOARD_GEN4_NTB_BRIDGE_NTB_RECEIVER_H_
#include <memory>
#include <thread>  //NOLINT
#include <unordered_map>

#include "av_comm/thread_pool.h"
#include "common.h"
#include "onboard/varch/vnode/message_util.h"

namespace ntb_bridge {

constexpr int kDefaultNumThreads = 16;

class NtbReceiver {
 public:
  using PublisherMapType =
      std::unordered_map<int, varch::vnode::message_util::Publisher>;
  NtbReceiver(ntb_host_id dest_host_id,
              const std::shared_ptr<PublisherMapType>& topic_id_to_publisher,
              int num_threads = kDefaultNumThreads);
  ~NtbReceiver();

  bool Start();
  bool Stop();

 private:
  bool OpenNtbDevice();
  bool CloseNtbDevice();
  bool StartDispatchThread();
  bool FreeNtbPacket(ntb_msg* packet) const;

  std::atomic_bool is_running_;
  ntb_host_id dest_host_id_;
  int ntb_dev_fd_;
  std::shared_ptr<PublisherMapType> topic_id_to_publisher_{nullptr};
  std::thread dispatch_thread_;
  std::unique_ptr<av_comm::ThreadPool> thread_pool_{nullptr};
  int num_threads_;
};
}  // namespace ntb_bridge

#endif  // ONBOARD_GEN4_NTB_BRIDGE_NTB_RECEIVER_H_
