/**
 * @file gc_callback.hpp
 * <AUTHOR>
 * @brief
 * @version 0.1
 * @date 2024-06-24
 *
 * @copyright Copyright (c) 2024
 *
 */

#ifndef __GC_CALLBACK_HPP__
#define __GC_CALLBACK_HPP__

#include <functional>
#include <iostream>
#include <list>
#include <mutex>
#include <string>
#include <unordered_map>
#include <vector>

namespace voyager {
namespace hardware {
class GcCbClass {
  using GcPointer = std::function<void(void*)>;
  using GcElement = std::pair<GcPointer, void*>;
  using GcCntrs = std::unordered_map<std::string, GcElement>;
  using GcCntrsPtr = std::unordered_map<std::string, GcElement>::iterator;

 public:
  static GcCbClass& get_ins() {
    static GcCbClass instance;
    return instance;
  }

  bool cb_add(const std::string name, GcPointer pointer, void* arg) {
    std::lock_guard<std::mutex> lck(opt_mtx);
    if (gc_cntrs.find(name) != gc_cntrs.end()) return false;
    auto it = gc_cntrs.insert({name, {pointer, arg}}).first;
    gc_list.push_back(it);
    return true;
  }

  bool cb_del(const std::string name) {
    std::lock_guard<std::mutex> lck(opt_mtx);
    if (gc_cntrs.find(name) == gc_cntrs.end()) {
      gc_cntrs.erase(name);
      return true;
    }
    return false;
  }

  void cb_run() {
    std::lock_guard<std::mutex> lck(opt_mtx);
    for (auto iter = gc_list.rbegin(); iter != gc_list.rend(); iter++) {
      (*iter)->second.first((*iter)->second.second);
    }
    gc_list.clear();
    gc_cntrs.clear();
  }

 private:
  GcCbClass() = default;
  GcCbClass(const GcCbClass&) = delete;
  GcCbClass& operator=(const GcCbClass&) = delete;

 private:
  std::mutex opt_mtx;
  GcCntrs gc_cntrs;
  std::list<GcCntrs::iterator> gc_list;
};
}  // namespace hardware
}  // namespace voyager

#if 0
typedef  ssize_t(GcCallbackFunc)(void*);
extern "C" void GcCallbackAddPointC(const char* name, GcCallbackFunc func_point, void* arg) {
  voyager::v2x::GcCbClass::GetInstance().CallbackAdd(name, func_point, arg);
}

extern "C" void GcCallbackDelPointC(const char* name) {
  voyager::v2x::GcCbClass::GetInstance().CallbackDel(name);
}

extern "C" void GcCallbackRunPointC() {
  voyager::v2x::GcCbClass::GetInstance().CallbackRun();
}
#endif

#endif
