#include "route_status_observer.h"

#include <utility>

#include "av_comm/topics.h"
#include "glog/logging.h"

namespace remote_assist {

RouteStatusObserver::RouteStatusObserver(RouteStatusCallback callback)
    : callback_(std::move(callback)) {}

bool RouteStatusObserver::IsInterestedIn(
    const teleassist::pb::TeleassistRequest& message) {
  return (message.message_type() == kRoutePath);
}

std::shared_ptr<google::protobuf::Message> RouteStatusObserver::OnMessage(
    const teleassist::pb::TeleassistRequest& message,
    DataChannel data_channel) {
  auto route_status_msg = std::make_shared<teleassist::pb::RoutePath>();
  if (!route_status_msg->ParseFromString(message.message_data())) {
    LOG(ERROR) << "Received error RouteStatus message format, len = "
               << message.message_data().length();
    return nullptr;
  }
  if (callback_) {
    callback_(route_status_msg, data_channel);
  }
  return route_status_msg;
}

}  // namespace remote_assist
