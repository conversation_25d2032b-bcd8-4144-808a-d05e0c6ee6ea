#ifndef ONBOARD_PLANNER_OPS_WARNING_ST_AGENT_TEST_ST_AGENT_TEST_UTIL_H_
#define ONBOARD_PLANNER_OPS_WARNING_ST_AGENT_TEST_ST_AGENT_TEST_UTIL_H_

#include "voy_protos/trajectory.pb.h"

namespace planner {
// Construct the trajectory by const speed.
pb::Trajectory ConstructTrajectory(double v, double x_offset = 0.0,
                                   double y_offset = 0.0, double heading = 0.0);

}  // namespace planner

#endif  // ONBOARD_PLANNER_OPS_WARNING_ST_AGENT_TEST_ST_AGENT_TEST_UTIL_H_
