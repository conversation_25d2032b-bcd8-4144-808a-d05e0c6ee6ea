{"bags": [{"bagName": "camera_box2", "compressionType": "UNCOMPRESSED", "maxDurationS": 120, "bagType": "NORMAL", "topicGroups": [{"priority": 1, "topics": ["/camera_gen3_video_frame_short_1", "/camera_gen3_video_frame_short_2", "/camera_gen3_video_frame_short_3", "/camera_gen3_video_frame_short_4", "/camera_video_frame_104", "/camera_video_frame_105", "/camera_video_frame_106", "/raw_camera_infrared_1"], "maxSizeMb": 5}]}, {"bagName": "default_box2", "compressionType": "ZSTD", "maxDurationS": 120, "bagType": "NORMAL", "topicGroups": [{"priority": 1, "topics": ["/fault_detector/fault_classification_cloud_config", "/radar_conti_430_1", "/radar_conti_430_2", "/radar_conti_430_3", "/radar_conti_430_4", "/planning/fault_detector_response", "/planning/customer_monitor_visual", "/planning/mrc_immediate_pullover", "/health/can_inspector_node", "/health/cpubox2_custom_recorder_node", "/health/cpubox2_system_control", "/health/cpubox2_recorder_node", "/health/conti430_radar_node", "/health/planner_node", "/health/routing_node", "/health/prediction_node", "/health/cpubox2_scan_conversion_node", "/health/xavier4_aurix_driver", "/health/xavier4_custom_recorder_node", "/health/xavier3_system_control", "/health/xavier4_system_control", "/health/gmsl_camera_xavier3", "/health/gmsl_camera_xavier4", "/health/cpubox2_ntb_bridge", "/health/cpubox2_gpu_monitor", "/health/inference_server_node", "/health/fallback_can_decode_node", "/health/fallback_level1_control_node", "/health/fallback_planning", "/health/fallback_level1", "/health/fallback_miira", "/health/restore_network_detector", "/health/teleassist", "/health/mrc_node", "/health/exception_handler_node", "/health/cpubox2_smart_bridge_node", "/health/cpubox2_hdmap_node", "/fallback_level1", "/fallback_level1_control", "/fallback_level1_control/control_debug", "/fallback_level1_control/seed", "/fallback_level1_fault_injection", "/fallback_level2", "/fallback_level2/chassis", "/fallback_level2/camera", "/fallback_level2/radar", "/fallback_anomaly_detection", "/health/realtime_map_node", "/teleassist/phmi_audio_control", "/teleassist/traffic_light", "/teleassist/status", "/teleassist/zone_list", "/teleassist/net_info", "/teleassist/delay_info", "/teleassist/degradation_info", "/teleassist/teleassist_regulator_info", "/fallback_planning_trajectory", "/fallback_planning_trajectory_result", "/cpubox2_external_disk_free_space", "/camera/h264_encode_signal", "/converted_tnu_gnss", "/converted_tnu_gnss_secondary", "/converted_tnu_imu", "/tnu_imu"], "maxSizeMb": 5}]}, {"bagName": "autonomy_box2", "compressionType": "ZSTD", "maxDurationS": 120, "bagType": "NORMAL", "topicGroups": [{"priority": 1, "topics": ["/bag_message_metadata", "/prediction/predicted_objects", "/planning/trajectory", "/planning/planning_lane_sequence", "/planning/ops_warning", "/planning/pullout_request", "/planning/cloud_cell_request", "/planning/stuck_detection_recall_signal", "/planning/remote_warning_signal", "/planning/assist_request", "/planning/assist_response", "/planning/remote_speed_limit", "/planning/seed", "/planner/state", "/planning/snapshot_seed", "/ml_planner/ml_trajectories", "/trajectory_guider/guide_trajectory", "/planning/ttc_detect_result", "/exception_handler/debug", "/exception_handler/debug_for_perception", "/exception_handler/global_init_state", "/exception_handler/global_selection_state", "/exception_handler/global_selection_seed", "/exception_handler/trajectory", "/planning/message_metadata", "/planning/message_metadata_input", "/planning/ml_planner_close_loop_assist_trajectory", "/mrc/mrc_debug", "/mrc/mrc_request", "/mrc/mrc_seed", "/planning/planning_empirical_raw_data", "/planning/trajectory_result"], "maxSizeMb": 5}]}, {"bagName": "debug_box2", "compressionType": "ZSTD", "maxDurationS": 120, "bagType": "NORMAL", "topicGroups": [{"priority": 1, "topics": ["/planning/planning_debug", "/routing/routing_debug", "/fallback_planning_debug", "/trajectory_guider/debug"], "maxSizeMb": 5}]}], "numberCompressionThread": 2, "maxBufferDurationMs": 5000, "maxBufferSizeMb": 4096}