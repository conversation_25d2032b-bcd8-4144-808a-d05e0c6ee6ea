package(default_visibility = ["//visibility:public"])

cc_library(
    name = "boundary_data_processing",
    srcs = [
        "boundary_data_processing.c",
    ],
    hdrs = [
        "boundary_data_processing.h",
    ],
    copts = [
        "-x",
        "c",
        "-std=c11",
    ],
    include_prefix = "lateral_function/boundary_data_processing/",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_lateral_function",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_lateral_function_type",
        "//offboard/fallback_ads/common_math_library:common_method",
        "//offboard/fallback_ads/lateral_function:lateral_function_parameter",
    ],
)

cc_library(
    name = "check_lane_property",
    srcs = [
        "check_lane_property.c",
    ],
    hdrs = [
        "check_lane_property.h",
    ],
    copts = [
        "-x",
        "c",
        "-std=c11",
    ],
    include_prefix = "lateral_function/boundary_data_processing/",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_lateral_function",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_lateral_function_type",
        "//offboard/fallback_ads/common_math_library:common_method",
        "//offboard/fallback_ads/common_math_library:debounce_method",
        "//offboard/fallback_ads/lateral_function:lateral_function_parameter",
    ],
)
