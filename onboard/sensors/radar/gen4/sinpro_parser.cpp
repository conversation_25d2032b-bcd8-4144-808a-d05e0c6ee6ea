#include "sinpro_parser.h"

#include <glog/logging.h>

#include <algorithm>
#include <cmath>
#include <string>
#include <unordered_map>
#include <utility>

#include "base/now.h"
#include "math/math_util.h"
#include "voy_protos/canbus.pb.h"
#include "voy_protos/radar.pb.h"

namespace radar {

namespace {

using onboard_monitor::AddHardwareMonitorData;

constexpr uint8_t kDefaultChirpSyncMode = 0x00;
constexpr uint32_t kDefaultSyncStepUs = 100000;  // Allowed max: 100000us

constexpr uint16_t kMotionStatus = 0x0002;   // 1 - Static     2 - Motion
constexpr uint16_t kLongRangeMode = 0x0002;  // 1 - ShortRange 2 - LongRange

constexpr int kMaxPointCloudNumInPacket = 32;

static RadarTxSystem::VehicleDimensionInfo GetLincolnVehicleDimensionInfo() {
  return {
      .model_info = 1,
      .vehicle_length_m = 4.925,
      .vehicle_width_m = 1.864,
      .vehicle_width_wide_m = 2.0,
      .wheel_base_m = 2.85,
      .vehicle_height_m = 1.477,
      .front_overhang_m = 1.1,
      .min_ground_clearance_m = 0.28,
      .mirror_height_m = 1.0,
      .wheel_diameter_m = 0.668,
  };
}

static RadarTxSystem::Mount GetLincolnMountInfo(const std::string& direction) {
  static const std::unordered_map<std::string, RadarTxSystem::Mount>
      mount_info = {{"front", {2.375, 0.250, 0.540, 0.0, 0.0, 0.0}},
                    {"right", {1.795, -1.010, 0.975, -M_PI / 2, 0.0, 0.0}},
                    {"rear", {-2.495, -0.230, 0.420, M_PI, 0.0, 0.0}},
                    {"left", {1.795, 1.010, 0.975, M_PI / 2, 0.0, 0.0}}};
  auto it = mount_info.find(direction);
  CHECK(it != mount_info.end()) << "Invalid direction: " << direction;
  return it->second;
}

static RadarTxSystem::VehicleDimensionInfo GetGacA2tVehicleDimensionInfo() {
  return {
      .model_info = 10,  // TODO(caochunpeng): temporary value
      .vehicle_length_m = 4.56,
      .vehicle_width_m = 1.854,
      .vehicle_width_wide_m = 2.084,
      .wheel_base_m = 2.75,
      .vehicle_height_m = 1.652,
      .front_overhang_m = 0.858,
      .min_ground_clearance_m = 0.145,
      .mirror_height_m = 1.096,
      .wheel_diameter_m = 0.650,
  };
}

static RadarTxSystem::Mount GetGacA2tMountInfo(const std::string& direction) {
  static const std::unordered_map<std::string, RadarTxSystem::Mount>
      mount_info = {{"front", {3.625, 0.181, 0.317, 0.0, 0.0, 0.0}},
                    {"right", {2.872, -0.885, 0.885, -M_PI / 2, 0.0, 0.0}},
                    {"rear", {-0.868, -0.192, 0.184, M_PI, 0.0, 0.0}},
                    {"left", {2.867, 0.885, 0.885, M_PI / 2, 0.0, 0.0}}};
  auto it = mount_info.find(direction);
  CHECK(it != mount_info.end()) << "Invalid direction: " << direction;
  return it->second;
}

}  // namespace

SinproParser::SinproParser(const std::string& facing_direction)
    : RadarParser(facing_direction) {
  InitExtrinsics();
  LOG(INFO) << facing_ip_ << "Create SinproParser with protocol version ["
            << kRadarProtocolVersion << "], facing: " << facing_direction;
}

void SinproParser::ProcessReceive(const PacketPtr& pkt_buf_ptr) {
  if (pkt_buf_ptr->size() < sizeof(RadarTxHeader)) {
    LOG(ERROR) << facing_ip_
               << "Received packet is too short: " << pkt_buf_ptr->size();
    return;
  }
  auto it = pkt_buf_ptr->cbegin();
  auto header = ExtractField<RadarTxHeader>(pkt_buf_ptr, it);

  // Check header
  if (!abnormal_checker_->SinproCheckProtocol(header)) return;
  RecordRawPacket(pkt_buf_ptr, base::NowNs(), PacketType::RECV);
  abnormal_checker_->SinproHeaderCheck(header, pkt_buf_ptr->data(),
                                       pkt_buf_ptr->size());

  // TODO(caochunpeng): Implement specific functions
  abnormal_checker_->SinproPubTimeStampCheck(
      static_cast<int64_t>(header->frame_time_ns / 1e6));

  if (NewFrame(header->frame_cnt)) {
    // Initialize radar_scan.
    if (!is_this_frame_pub_) {
      OnScanCycleComplete();
    }
    UdpPacketPublish();
    radar_scan_ = std::make_shared<pb::RadarScan>();
    radar_scan_->set_timestamp(
        static_cast<int64_t>(header->frame_time_ns / 1e6));
    radar_scan_->set_device_type(pb::RadarType::SINPRO_SFR2K);
    is_this_frame_pub_ = false;
    pointcloud_num_ = -1;
    scan_frame_cnt_ = header->frame_cnt;
  }

  switch (static_cast<RadarTxType>(header->type)) {
    case RadarTxType::POINTCLOUD:
      ProcessPointCloud(pkt_buf_ptr, it, header->seq);
      break;
    case RadarTxType::SYSTEM:
      ProcessSystem(pkt_buf_ptr, it);
      break;
    default:
      // Donot handle other types(TARGET, OBSTACLE) because they are not used.
      LOG_EVERY_N(ERROR, 100)
          << facing_ip_ << "Received packet with wrong type: " << header->type;
      break;
  }
}

bool SinproParser::NewFrame(const uint64_t frame_cnt) {
  if (scan_frame_cnt_ == frame_cnt) {
    return false;
  }
  return true;
}

void SinproParser::OnScanCycleComplete() {
  // TODO(caochunpeng): Add trace here.
  if (radar_scan_ == nullptr) {
    return;
  }
  int frame_sync_offset = static_cast<int>(radar_scan_->timestamp() % 100);
  if ((frame_sync_offset < 50 && std::abs(frame_sync_offset) > 10) ||
      (frame_sync_offset >= 50 && std::abs(frame_sync_offset - 100) > 10)) {
    LOG_EVERY_N(ERROR, 1000)
        << facing_ip_ << "frame sync offset abnormal: " << std::fixed
        << std::setprecision(3) << (radar_scan_->timestamp() / 1000.0);
  }
  LOG(INFO) << facing_ip_ << "Received point cloud with "
            << radar_scan_->targets_size() << " targets "
            << " pointcloud_num: " << pointcloud_num_
            << " frame_cnt: " << scan_frame_cnt_ << " " << std::fixed
            << std::setprecision(3) << (radar_scan_->timestamp() / 1000.0)
            << "ms";
  is_this_frame_pub_ = true;
  radar_scan_pub_.Publish(std::move(radar_scan_));
}

void SinproParser::ProcessPointCloud(const PacketPtr& pkt_buf_ptr,
                                     std::vector<uint8_t>::const_iterator& it,
                                     const uint16_t seq) {
  // Skip invalid part of this frame.
  if (seq > 0 && pointcloud_num_ == radar_scan_->targets_size()) {
    return;
  }
  // Parse point cloud
  auto point_cloud = ExtractField<RadarTxPointCloud>(pkt_buf_ptr, it);
  if (pointcloud_num_ == -1) pointcloud_num_ = point_cloud->pointcloud_num;
  // Check InterferenceMitigation & ChannelPower
  abnormal_checker_->SinproPointCloudCheck(point_cloud);
  int remaining_points =
      std::min(static_cast<int>(pointcloud_num_ - radar_scan_->targets_size()),
               kMaxPointCloudNumInPacket);

  for (int i = 0; i < remaining_points; i++) {
    auto& pc_unit = point_cloud->pointcloud_unit[i];
    // Parse point cloud unit
    auto target = radar_scan_->add_targets();
    target->set_range(pc_unit.rng_m);
    target->set_azimuth(pc_unit.azi_rad);
    target->set_elevation(pc_unit.ele_rad);
    target->set_velocity(pc_unit.dpl_unambi_prim_mps);
    target->set_doppler_raw_velocity(pc_unit.dpl_raw_mps);
    target->set_rcs(pc_unit.rcs_dbsm);
    target->set_snr(pc_unit.snr_db);
    // target->set_peakinfo_idx(pc_unit.peakinfo_idx);
    // target->set_delta_velocity(pc_unit.delta_velocity);
    target->set_is_motion(pc_unit.flags.motion_status == kMotionStatus);
    target->set_is_long_range(pc_unit.flags.range_mode == kLongRangeMode);
    target->set_peak_type(pc_unit.flags.peak_type);
    target->set_is_ghost(pc_unit.flags.is_ghost);
  }
  // TODO(lyrezhang): Confirm the condition
  if (pointcloud_num_ == radar_scan_->targets_size()) {
    // TODO(lyrezhang): Add trace here.
    OnScanCycleComplete();
  }
}

void SinproParser::HardwareMonitorInfo(const RadarTxSystem* system) {
  std::string plugin_name = facing_direction_ + "_radar";

  std::lock_guard<std::mutex> lock(mtx);
  monitor_data_single_.Clear();
  AddHardwareMonitorData(monitor_data_single_, plugin_name, "state",
                         static_cast<int>(system->state));
  AddHardwareMonitorData(monitor_data_single_, plugin_name, "rolling_cnt",
                         static_cast<int>(system->active_err.rolling_cnt));
  AddHardwareMonitorData(monitor_data_single_, plugin_name, "gptp_sync_status",
                         static_cast<int>(system->gptp_info.gptp_sync_status));
  AddHardwareMonitorData(monitor_data_single_, plugin_name, "vol_input",
                         static_cast<int>(system->key_vol_info.vol_input));
  AddHardwareMonitorData(monitor_data_single_, plugin_name, "temp_soc",
                         static_cast<int>(system->key_temp_info.temp_soc) - 40);
  AddHardwareMonitorData(
      monitor_data_single_, plugin_name, "cpu_load_core",
      static_cast<int>(system->key_resource_info.cpu_load_core[0]));
  AddHardwareMonitorData(
      monitor_data_single_, plugin_name, "stack_used_max",
      static_cast<int>(system->key_resource_info.stack_used_max));
  AddHardwareMonitorData(monitor_data_single_, plugin_name, "blindness_prob",
                         static_cast<int>(system->blindness_prob));
}
void SinproParser::ProcessSystem(const PacketPtr& pkt_buf_ptr,
                                 std::vector<uint8_t>::const_iterator& it) {
  auto system = ExtractField<RadarTxSystem>(pkt_buf_ptr, it);
  HardwareMonitorInfo(system);
  is_radar_state_working_ = true;
  bool is_frame_sync_ = true;
  bool is_vehicle_info_valid_ = true;
  abnormal_checker_->SinproSystemStatusCheck(
      system, extrinsics_, is_radar_state_working_, is_frame_sync_,
      is_vehicle_info_valid_);

  if (!is_frame_sync_) {
    EnqueueSend(MakeFrameSync(0));
  }

  if (!is_vehicle_info_valid_) {
    EnqueueSend(MakeExtrinsics());
  }
}

PacketPtr SinproParser::Canbus2VehicleInfo(const voy::Canbus& canbus) {
  RadarRxVehicleInfo vehicle_info;
  // Initialize vehicle_info, 0x00 for invalid values.
  std::memset(&vehicle_info, 0x00, sizeof(vehicle_info));

  auto gear = canbus.gear();
  int move_direction = (voy::Canbus_Transmission_GEAR_REVERSE == gear) ? -1 : 1;

  vehicle_info.chassis_info.vehicle_info_ts_ns =
      static_cast<uint64_t>(canbus.timestamp() * 1e3);
  vehicle_info.chassis_info.veh_spd_mps = static_cast<float>(
      math::KmphToMps(canbus.vehicle_speed() * move_direction));
  vehicle_info.chassis_info.veh_yawrate_radps =
      static_cast<float>(math::Degree2Radian(canbus.yaw_rate()));
  vehicle_info.chassis_info.veh_actgear_pos = static_cast<uint8_t>(gear);
  // TODO(lyrezhang): Complete the following fields
  vehicle_info.chassis_info.veh_steer_wheel_angle_rad =
      static_cast<float>(math::Degree2Radian(canbus.steering_angle()));
  // No steering angle speed output, calculate from steering angle.
  vehicle_info.chassis_info.veh_steer_wheel_angle_spd_radps =
      static_cast<float>(math::Degree2Radian(CalculateSteeringAngleSpeed(
          canbus.steering_angle(), canbus.timestamp())));

  // Wheel speed
  vehicle_info.chassis_info.veh_whl_spd_pls_fl =
      static_cast<uint8_t>(canbus.vehicle_detail_info().whl_rot_cntr_fl());
  vehicle_info.chassis_info.veh_whl_spd_pls_fr =
      static_cast<uint8_t>(canbus.vehicle_detail_info().whl_rot_cntr_fr());
  vehicle_info.chassis_info.veh_whl_spd_pls_rl =
      static_cast<uint8_t>(canbus.vehicle_detail_info().whl_rot_cntr_rl());
  vehicle_info.chassis_info.veh_whl_spd_pls_rr =
      static_cast<uint8_t>(canbus.vehicle_detail_info().whl_rot_cntr_rr());

  // WheelRotatedDirection
  // for sinpro 0:Standstill 1:Forward 2:Backward 3:Invalid
  // for canbus is_stationary:Standstill 0:Forward 1:Backward 2:Invalid
  auto GetSinproWheelRotatedDirection = [&](int canbus_direction,
                                            bool is_stationary) {
    if (is_stationary) {
      return 0;
    }
    switch (canbus_direction) {
      case 0:
        return 1;
      case 1:
        return 2;
      default:
        LOG_EVERY_N(INFO, 100)
            << facing_ip_
            << "Invalid wheel rotated direction: " << canbus_direction;
        return 3;
    }
  };
  vehicle_info.chassis_info.veh_whl_spd_dir_fl = GetSinproWheelRotatedDirection(
      canbus.driving_info().wheel_rotated_direction_fl(),
      canbus.is_stationary());
  vehicle_info.chassis_info.veh_whl_spd_dir_fr = GetSinproWheelRotatedDirection(
      canbus.driving_info().wheel_rotated_direction_fr(),
      canbus.is_stationary());
  vehicle_info.chassis_info.veh_whl_spd_dir_rl = GetSinproWheelRotatedDirection(
      canbus.driving_info().wheel_rotated_direction_rl(),
      canbus.is_stationary());
  vehicle_info.chassis_info.veh_whl_spd_dir_rr = GetSinproWheelRotatedDirection(
      canbus.driving_info().wheel_rotated_direction_rr(),
      canbus.is_stationary());

  // TODO(caochunpeng): veh_fwiper_status not clear yet.
  // Use default value for now.
  vehicle_info.chassis_info.veh_fwiper_status = 0;
  vehicle_info.chassis_info.veh_long_acc_mps2 =
      static_cast<float>(canbus.acceleration_forward());
  // TODO(caochunpeng): veh_brake_pdl_state not clear yet.
  // Use default value for now.
  vehicle_info.chassis_info.veh_mileage = 0;

  // TODO(caochunpeng): brake info not clear yet. Use default value for now.
  vehicle_info.brake_info.veh_acceleration_pdl_pos = 0;
  vehicle_info.brake_info.veh_brake_pdl_state = 0;
  vehicle_info.brake_info.veh_brake_torque_nm = 0;
  vehicle_info.brake_info.veh_brake_pressure_bar = 0;
  vehicle_info.brake_info.veh_brake_sys_state = 1;

  vehicle_info.brake_info.veh_slope_estimation = 0xFF;

  vehicle_info.counter = canbus_counter_++;

  AddRadarRxHeader(&vehicle_info, RadarRxType::VEHICLE_INFO);
  return ObjectToByteArray(vehicle_info);
}

void SinproParser::InitExtrinsics() {
  // Initialize extrinsics_ with all zeros.
  std::memset(&extrinsics_, 0x00, sizeof(extrinsics_));

  switch (car_type_) {
    case av_comm::CarType::kLincoln:
      extrinsics_.vehicle_dimension_info = GetLincolnVehicleDimensionInfo();
      extrinsics_.mount_info = GetLincolnMountInfo(facing_direction_);
      break;
    case av_comm::CarType::kGacA2t:
      extrinsics_.vehicle_dimension_info = GetGacA2tVehicleDimensionInfo();
      extrinsics_.mount_info = GetGacA2tMountInfo(facing_direction_);
      break;
    default:
      LOG(ERROR) << facing_ip_
                 << "Unknown car type: " << static_cast<int>(car_type_);
      break;
  }
}

PacketPtr SinproParser::MakeExtrinsics() {
  AddRadarRxHeader(&extrinsics_, RadarRxType::EXTRINSICS);
  return ObjectToByteArray(extrinsics_);
}

PacketPtr SinproParser::MakeFrameSync(int target_time_us) {
  RadarRxFrameSync frame_sync;
  frame_sync.target_time_us = target_time_us;
  frame_sync.sync_step_us = kDefaultSyncStepUs;
  frame_sync.chirp_sync_mode = kDefaultChirpSyncMode;
  AddRadarRxHeader(&frame_sync, RadarRxType::FRAME_SYNC);
  return ObjectToByteArray(frame_sync);
}

PacketPtr SinproParser::MakeReboot() {
  RadarRxReboot reboot;
  AddRadarRxHeader(&reboot, RadarRxType::REBOOT);
  return ObjectToByteArray(reboot);
}

}  // namespace radar
