
#ifndef OFFBOARD_FALLBACK_ADS_LONG_FUNCTION_SITUATION_EVALUATION_SITUATION_EVALUATION_DECISION_H_
#define OFFBOARD_FALLBACK_ADS_LONG_FUNCTION_SITUATION_EVALUATION_SITUATION_EVALUATION_DECISION_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <stdbool.h>
#include <stdint.h>

#include "_out/Appl/GenData/Rte_Type.h"
#include "long_function/long_function_type.h"

extern bool CheckEmergencyBrake(bool object_position_valid,
                                bool lateral_collision_enable,
                                float collision_probability,
                                float time_to_collision);

extern bool CheckObjectFollowingBrake(float time_to_reach_stop_point);

extern bool CheckFreeStop(float brake_distance, float ego_velocity,
                          float accel_request_for_free_stop,
                          float accel_request_for_object,
                          float accel_request_for_lane);

extern bool CheckLaneFollowingBrake(float accel_request);

extern bool CheckLaneIsTooShort(float ego_velocity, float lane_length,
                                float accel_request);

extern bool VehicleIsStandstill(float ego_velocity);

extern void UpdateStateStandbyExit(bool fallback_active, bool ego_is_standstill,
                                   bool collision_enable,
                                   bool ego_lane_is_too_short,
                                   bool free_stop_enable,
                                   bool lane_following_brake_enable,
                                   bool object_following_brake_enable,
                                   SystemState* system_state_ptr);
extern void UpdateStateFreeStopExit(
    bool ego_is_standstill, bool collision_enable, bool ego_lane_is_too_short,
    bool free_stop_enable, bool lane_following_brake_enable,
    bool object_following_brake_enable, SystemState* system_state_ptr);

extern void UpdateStateAdaptiveBrakeExit(
    bool ego_is_standstill, bool collision_enable, bool ego_lane_is_too_short,
    bool free_stop_enable, bool lane_following_brake_enable,
    bool object_following_brake_enable, SystemState* system_state_ptr);

extern SystemState UpdateSystemState(
    bool fallback_active, bool ego_is_standstill, bool collision_enable,
    bool ego_lane_is_too_short, bool object_following_brake_enable,
    bool lane_following_brake_enable, bool free_stop_enable,
    float free_stop_accel, float lane_following_brake_accel,
    float object_following_brake_accel, float* accel_request,
    float* jerk_request);

extern SystemState SituationEvaluationDecision(
    const VSI_VehicleInfo_Struct* vehicle_info_ptr,
    const CSI_LaneInfo_Struct* lane_info_ptr,
    const VSP_VehicleSignal_Struct* vehicle_signal_ptr,
    const FSC_FreeStopControl_Struct* free_stop_control_ptr,
    const CDS_CollisionDetection_Struct* collision_detection_ptr,
    float* accel_request_ptr, float* jerk_request_ptr);

#ifdef __cplusplus
}
#endif

#endif  // OFFBOARD_FALLBACK_ADS_LONG_FUNCTION_SITUATION_EVALUATION_SITUATION_EVALUATION_DECISION_H_
