#include <gtest/gtest.h>

#include <cstdint>

#include "common_math_library/common_method.h"

TEST(CMLSign, PositiveValueTest) {
  // Setup
  float value = 1.0F;

  // Execute
  float sign_value = CML_Sign(value);

  // Verify
  EXPECT_FLOAT_EQ(sign_value, 1.0F);
}

TEST(CMLSign, NegativeValueTest) {
  // Setup
  float value = -1.0F;

  // Execute
  float sign_value = CML_Sign(value);

  // Verify
  EXPECT_FLOAT_EQ(sign_value, -1.0F);
}

TEST(CMLSignTest, ZeroValueTest) {
  // Setup
  float value = 0.0F;

  // Execute
  float sign_value = CML_Sign(value);

  // Verify
  EXPECT_FLOAT_EQ(sign_value, 1.0F);
}

TEST(CMLIsNonZero, NonZeroValueTest) {
  // Setup
  float value = 1.0F;

  // Execute
  bool is_non_zero = CML_IsNonZero(value);

  // Verify
  EXPECT_TRUE(is_non_zero);
}

TEST(CMLIsNonZero, ZeroValueTest) {
  // Setup
  float value = 0.0F;

  // Execute
  bool is_non_zero = CML_IsNonZero(value);

  // Verify
  EXPECT_FALSE(is_non_zero);
}

TEST(CMLIsNonZero, PositiveNonZeroValueTest) {
  // Setup
  float value = 1.1e-6F;

  // Execute
  bool is_non_zero = CML_IsNonZero(value);

  // Verify
  EXPECT_TRUE(is_non_zero);
}

TEST(CMLIsNonZero, PositiveNearZeroValueTest) {
  // Setup
  float value = 1e-7F;

  // Execute
  bool is_non_zero = CML_IsNonZero(value);

  // Verify
  EXPECT_FALSE(is_non_zero);
}

TEST(CMLIsNonZero, NegativeNonZeroValueTest) {
  // Setup
  float value = -1.1e-6F;

  // Execute
  bool is_non_zero = CML_IsNonZero(value);

  // Verify
  EXPECT_TRUE(is_non_zero);
}

TEST(CMLIsNonZero, NegativeNearZeroValueTest) {
  // Setup
  float value = -1e-7F;

  // Execute
  bool is_non_zero = CML_IsNonZero(value);

  // Verify
  EXPECT_FALSE(is_non_zero);
}

TEST(CMLSetBit, U8TypeTest) {
  uint8_t num = 0;
  CML_SetBit(num, 1);
  EXPECT_EQ(num, 0b00000010);
}

TEST(CMLSetBit, U16TypeTest) {
  uint16_t num = 0;
  CML_SetBit(num, 1);
  EXPECT_EQ(num, 0b0000000000000010);
}

TEST(CMLSetBit, U32TypeTest) {
  uint32_t num = 0;
  CML_SetBit(num, 1);
  EXPECT_EQ(num, 0b00000000000000000000000000000010);
}

TEST(CMLSetBit, SetMultipleBitsTest) {
  uint8_t num = 0;
  CML_SetBit(num, 1);
  CML_SetBit(num, 3);
  EXPECT_EQ(num, 0b00001010);
}

TEST(CMLSetBit, SetAlreadySetBitTest) {
  uint8_t num = 0b00000010;
  CML_SetBit(num, 1);
  EXPECT_EQ(num, 0b00000010);
}

TEST(CMLSetBit, SetBitOutOfRangeTest) {
  uint8_t num = 0;
  CML_SetBit(num, 8);
  EXPECT_EQ(num, 0);
}

TEST(CMLClearBit, U8TypeTest) {
  uint8_t num = 0b00000010;
  CML_ClearBit(num, 1);
  EXPECT_EQ(num, 0b00000000);
}

TEST(CMLClearBit, U16TypeTest) {
  uint16_t num = 0b0000000000000010;
  CML_ClearBit(num, 1);
  EXPECT_EQ(num, 0b0000000000000000);
}

TEST(CMLClearBit, U32TypeTest) {
  uint32_t num = 0b00000000000000000000000000000010;
  CML_ClearBit(num, 1);
  EXPECT_EQ(num, 0b00000000000000000000000000000000);
}

TEST(CMLClearBit, ClearMultipleBitsTest) {
  uint8_t num = 0b00001010;
  CML_ClearBit(num, 1);
  CML_ClearBit(num, 3);
  EXPECT_EQ(num, 0b00000000);
}

TEST(CMLClearBit, ClearAlreadyClearedBitTest) {
  uint8_t num = 0b00000000;
  CML_ClearBit(num, 1);
  EXPECT_EQ(num, 0b00000000);
}

TEST(CMLClearBit, ClearBitOutOfRangeTest) {
  uint8_t num = 0b00000010;
  CML_ClearBit(num, 8);
  EXPECT_EQ(num, 0b00000010);
}

TEST(CMLSafeDivision, DivisionByZeroTest) {
  // Setup
  float dividend = 1.0F;
  float divisor = 0.0F;

  // Execute
  float result = CML_SafeDivision(dividend, divisor);

  // Verify
  EXPECT_FLOAT_EQ(result, 1e6F);
}

TEST(CMLLimit, NormalLimitTest) {
  // Setup
  float input = 1.0F;
  float lower_limit = -1.0F;
  float upper_limit = 1.0F;

  // Execute
  float result = CML_Limit(input, lower_limit, upper_limit);

  // Verify
  EXPECT_FLOAT_EQ(result, 1.0F);
}

TEST(CMLLimit, LowerLimitGreaterThanUpperLimitTest) {
  // Setup
  float input = 1.0F;
  float lower_limit = 1.0F;
  float upper_limit = -1.0F;

  // Execute
  float result = CML_Limit(input, lower_limit, upper_limit);

  // Verify
  EXPECT_FLOAT_EQ(result, 1.0F);
}

TEST(CMLLimit, InputGreaterThanUpperLimitTest) {
  // Setup
  float input = 2.0F;
  float lower_limit = -1.0F;
  float upper_limit = 1.0F;

  // Execute
  float result = CML_Limit(input, lower_limit, upper_limit);

  // Verify
  EXPECT_FLOAT_EQ(result, 1.0F);
}

TEST(CMLLimit, InputLessThanLowerLimitTest) {
  // Setup
  float input = -2.0F;
  float lower_limit = -1.0F;
  float upper_limit = 1.0F;

  // Execute
  float result = CML_Limit(input, lower_limit, upper_limit);

  // Verify
  EXPECT_FLOAT_EQ(result, -1.0F);
}

TEST(CMLRateLimit, NormalRateLimitTest) {
  // Setup
  float input = 1.0F;
  float lower_rate_limit = -1.0F;
  float upper_rate_limit = 1.0F;
  float task_cycle_time = 0.01F;
  float last_cycle_output = 0.0F;

  // Execute and verify
  for (int i = 0; i < 1000; ++i) {
    float ref_output = i < 100 ? 0.01F * (i + 1) : 1.0F;
    CML_RateLimit(input, lower_rate_limit, upper_rate_limit, task_cycle_time,
                  &last_cycle_output);
    EXPECT_NEAR(last_cycle_output, ref_output, 1e-3F);
  }
}

TEST(CMLRateLimit, LowerLimitGreaterThanUpperLimitTest) {
  // Setup
  float input = 1.0F;
  float lower_rate_limit = 1.0F;
  float upper_rate_limit = -1.0F;
  float task_cycle_time = 0.01F;
  float last_cycle_output = 0.0F;

  // Execute and verify
  for (int i = 0; i < 1000; ++i) {
    float ref_output = i < 100 ? 0.01F * (i + 1) : 1.0F;
    CML_RateLimit(input, lower_rate_limit, upper_rate_limit, task_cycle_time,
                  &last_cycle_output);
    EXPECT_NEAR(last_cycle_output, ref_output, 1e-3F);
  }
}

TEST(CMLLowPassFilter, NormalLowPassFilterTest) {
  // Setup
  float input = 1.0F;
  float lower_rate_limit = 1.0F;
  float upper_rate_limit = -1.0F;
  float task_cycle_time = 0.01F;
  float last_cycle_output = 0.0F;

  // Execute and verify
  for (int i = 0; i < 1000; ++i) {
    float ref_output = i < 100 ? 0.01F * (i + 1) : 1.0F;
    CML_RateLimit(input, lower_rate_limit, upper_rate_limit, task_cycle_time,
                  &last_cycle_output);
    EXPECT_NEAR(last_cycle_output, ref_output, 1e-3F);
  }
}
