#ifndef ONBOARD_PLANNER_SPEED_REASONING_REASONING_TEST_FIXTURE_H_
#define ONBOARD_PLANNER_SPEED_REASONING_REASONING_TEST_FIXTURE_H_

#include <tbb/concurrent_unordered_map.h>

#include <map>
#include <memory>
#include <optional>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "adv_geom/path2d_with_juke.h"
#include "geometry/model/point_2d.h"
#include "geometry/model/polygon_with_cache.h"
#include "geometry/model/polyline_curve.h"
#include "hdmap/lib/test/test_util.h"
#include "math/pose_2d.h"
#include "planner/decoupled_maneuvers/cross_lane/cross_lane_info_manager.h"
#include "planner/decoupled_maneuvers/lane_change/lane_change_info.h"
#include "planner/decoupled_maneuvers/pull_over/destination_meta_data.h"
#include "planner/decoupled_maneuvers/pull_over/pull_over_info.h"
#include "planner/speed/constraint/constraint.h"
#include "planner/speed/constraint/constraint_creator.h"
#include "planner/speed/overlap/ego_lateral_clearance.h"
#include "planner/speed/reasoning/prediction_decision_maker.h"
#include "planner/speed/reasoning/reasoning_pipeline.h"
#include "planner/speed/reasoning/reasoning_policy/reasoning_policies.h"
#include "planner/speed/reasoning_input/trajectory_info.h"
#include "planner/speed/reasoning_input/urgency_info.h"
#include "planner/speed/reasoning_input/world_context.h"
#include "planner/speed/reference/reference_generator.h"
#include "planner/utility/config_center/planner_config_center.h"
#include "planner_protos/overlap.pb.h"
#include "planner_protos/path_reasoning_seed.pb.h"
#include "planner_protos/planning_debug.pb.h"
#include "planner_protos/planning_lane_sequence.pb.h"
#include "planner_protos/planning_seed.pb.h"
#include "planner_protos/speed_reasoning_debug.pb.h"
#include "pnc_map_service/test/map_test_util.h"
#include "prediction_protos/agent.pb.h"
#include "voy_protos/construction_zones.pb.h"
#include "voy_protos/math.pb.h"
#include "voy_protos/tracked_objects.pb.h"
#include "voy_protos/traffic_light.pb.h"

namespace planner {
namespace speed {

// Prediction pubishes a 9s trajectory with 0.1s interval. Note that this does
// not include the current tracking pose so the there are 91 poses in total.
constexpr int kPredictedTrajectoryStep = 90;
constexpr int64_t kPredictedTrajectoryTimestampIntervalInMSec = 100;  // ms
constexpr size_t kMinimumDensifiedPolylineCurvePointNumber = 20UL;

class ReasoningTestFixture {
 public:
  ReasoningTestFixture();

  // Always need to set up map when a map is needed.
  // Setting up the map should always be the first step.
  // Currently this API only supports kFremontData, for other types, you can set
  // up by the second one which needs coordinates as input
  void SetUpRegionMap(hdmap::test_util::TestData test_map_area);
  void SetUpRegionMap(double init_map_x, double init_map_y,
                      const std::string& route_name,
                      hdmap::test_util::TestData test_map_area);
  // For scene maps, the init x, init y and route name are scene-dependent. A
  // complete list of scenes can be found in
  // https://cooper.didichuxing.com/docs/document/2199982455755
  void SetUpSceneMap(hdmap::test_util::SceneType scene_type);

  // Sets ego lane id.
  void SetEgoLane(int64_t current_lane_id) { ego_lane_id_ = current_lane_id; }

  // Sets ego canbus state.
  void SetEgoCanbus(bool is_auto);

  // Sets ego pose in at `portion` of the lane center line. `portion` ranges
  // from 0.0 to 1.0.
  void SetEgoPose(int64_t lane_id, double portion, double speed = 0.0,
                  double accel = 0.0);

  // Sets ego pose by absolutely pos, yaw(rad), speed(m/s) and accel(m/s^2).
  void SetEgoPose(double x_pos, double y_pos, double yaw, double speed,
                  double accel);

  // Sets ego pose without yaw by pos, lane and speed.
  void SetEgoPoseWoYaw(double x_pos, double y_pos, int64_t lane_id,
                       double speed = 0.0);

  // Sets ego trajectory.
  void SetEgoTrajectory(const planner::pb::Trajectory& trajectory);

  // Sets the order start time info.
  void SetOrderStartTimeInfo(int hour, int min, int sec);

  // Sets lane change status for trajectory_info_.
  void SetLaneChangeStatus(planner::pb::LaneChangeMode direction,
                           planner::pb::LaneChangeState lane_change_state,
                           double start_arclength, int64_t source_lane_id,
                           int64_t target_lane_id,
                           bool is_current_homotopy_lane_change,
                           int64_t leading_object_id = 0,
                           int64_t tailing_object_id = 0);

  // Set pull over status for trajectory_info_.
  void SetPullOverStatus(pull_over::PullOverStatus&& pull_over_status);

  // Set pull over destination for trajectory_info_.
  void SetPullOverDestination(double portion, bool is_pull_over_jump_in);

  // Set the object that behind the pull over gap.
  void SetPullOverRearObject(int64_t target_lane_id, int64_t rear_object_id);

  // Set the flag indicates if this trajectory is generated for pull out jump
  // out.
  void SetIsPullOutJumpOut(bool is_pull_out_jump_out);

  // Set the planner's GLOBAL flag to indicate if pull out is triggered.
  void SetIsPullOutTriggered(bool is_pull_out_triggered);

  // Set the flag indicates if the lane sequence is feasible.
  void SetIsLaneSequenceFeasible(bool is_lane_sequence_feasible);

  // Set the flag indicates whether the trajectory is generated for waypoint
  // assist mode.
  void SetIsWaypointAssistInvoked(bool is_waypoint_assist_invoked);

  // Set the flag indicates whether the backup trajectory is generated
  // for waypoint assist mode.
  void SetIsWaypointBackupTrajectoryUsed(
      bool is_waypoint_backup_trajectory_used);

  // Set the flag indicates whether any hard boundary constraint is relaxed for
  // the path.
  void SetIsHardBoundaryConstraintRelaxedForPath(
      bool is_hard_boundary_relaxed_in_path);

  // Set the flag indicates whether trajectory is for
  // light assist detour-jump out.
  void SetIsForRAUnstuckRequest(bool is_for_ra_unstuck_request);

  // Set motion mode for trajectory_info_.
  void SetMotionMode(planner::pb::MotionMode motion_mode);

  // Set behavior type of the trajectory info.
  void SetBehaviorType(planner::pb::BehaviorType behavior_type);

  // Set reverse driving info for destination reasoning.
  void SetReverseDrivingInfo(
      planner::pb::ReverseDrivingState_Enum reverse_driving_state,
      double portion);

  // Set open space info for unstuck reasoner and destination reasoner.
  void SetOpenSpaceInfo(const open_space::OpenSpaceInfo& open_space_info);

  // Set should planner respond mrc.
  void SetShouldPlannerRespondMRC(bool should_planner_respond_mrc);

  // Sets path_ to follow the current lane of ego_pose_. If extend_backward is
  // true, add the first predecessor to lane sequence as well.
  void CreateLaneFollowPath(bool extend_backward = true);

  // Sets path_ based on the predefined lane change instance. Users should make
  // sure ego_pose_ is inside the source lane and the target lane is aligned
  // with the lane change mode.
  void CreateLaneChangePath(int64_t source_lane_id, int64_t target_lane_id);

  // Sets path_ to follow a given lane sequence. Users should make sure
  // ego_pose_ is inside the lane sequence and the lane sequence is aligned
  // with the maneuver type.
  void CreatePathWithLaneSequence(
      const std::vector<int64_t>& lane_sequence_id,
      planner::pb::ManeuverType maneuver_type =
          planner::pb::ManeuverType::DECOUPLED_FORWARD,
      bool update_trajectory_info = true);

  // Sets path_ to follow a given lane sequence with a part of nudging segment
  // generated by sine wave. The nudging segment starts at the
  // |start_nudging_lane_portion| in |start_nudging_lane_id| and ends at
  // |end_nudging_lane_portion| in |end_nudging_lane_id|. The nudge segment is
  // of sine wave shape, whose magnitude is set by |max_lateral_offset_in_meter|
  // and side is defined by |is_nudging_from_right|. Users are able to control
  // the closeness of nudging, i.e., whether the path comes back to reference
  // line at the end. Users should make sure ego_pose_ is inside the lane
  // sequence and the lane sequence is aligned with the maneuver type.

  // NOTE: An open nudging is of a quarter period sine wave shape, whereas a
  // closed nudging is of a half period sine wave shape. To create an open
  // nudging path, |end_nudging_lane_id| must be the last lane in sequence and
  // |end_nudging_lane_portion| must be 1.0, DCHECK has been enforced for these
  // requirements.
  void CreateNudgePathWithSineWave(
      const std::vector<int64_t>& lane_sequence_id,
      int64_t start_nudging_lane_id, int64_t end_nudging_lane_id,
      double start_nudging_lane_portion, double end_nudging_lane_portion,
      double max_lateral_offset_in_meter, bool is_nudging_from_right,
      bool is_nudging_back,
      planner::pb::ManeuverType maneuver_type =
          planner::pb::ManeuverType::DECOUPLED_FORWARD,
      bool update_trajectory_info = true);

  // Sets path_ to follow a given lane sequence. An untrimmed lane sequence is
  // also given in case it is different from the (trimmed) lane sequence. Users
  // should make sure ego_pose_ is inside the lane sequence and the lane
  // sequence is aligned with the maneuver type.
  void CreatePathWithLaneSequenceAndUntrimmedLaneSequence(
      const std::vector<int64_t>& lane_sequence_id,
      const std::vector<int64_t>& untrimmed_lane_sequence_id,
      planner::pb::ManeuverType maneuver_type,
      bool update_trajectory_info = true);

  // Sets path_ based on the given path curve. Users should make sure ego_pose_
  // is inside the lane sequence and the lane sequence is aligned with the
  // maneuver type.
  void CreatePathWithPathCurveAndLaneSequence(
      const math::geometry::PolylineCurve2d& path_curve,
      const std::vector<int64_t>& lane_sequence_id,
      planner::pb::ManeuverType maneuver_type =
          planner::pb::ManeuverType::DECOUPLED_FORWARD,
      bool update_trajectory_info = true);

  // Adds a construction zone to construction_zones_ and returns the reference
  // to the added construction zone.
  voy::ConstructionZone& AddConstructionZone(
      int64_t id,
      const std::vector<math::geometry::Point2d>& contour_points = {},
      voy::ZoneSource source = voy::ZoneSource::REAL_TIME_MAP_ONLY,
      voy::SpaceType space_type = voy::SpaceType::CONSTRUCTION_ZONE);

  // Adds an agent to agent_list_ and returns the reference to the added
  // agent.
  prediction::pb::Agent& AddAgent(voy::perception::ObjectType object_type,
                                  int id, const math::Pose2d& pose,
                                  double length, double width, double velocity,
                                  double height = 1.5);
  prediction::pb::Agent& AddAgent(voy::perception::ObjectType object_type,
                                  int id, int64_t lane_id, double portion,
                                  double length, double width, double velocity,
                                  double height = 1.5,
                                  double heading_diff_to_lane = 0.0);

  // Adds an agent staying in an exit zone to agent_list_ and returns the
  // reference to the added agent. The agent pose is picked from the exit
  // zone centerline, according to the portion.
  prediction::pb::Agent& AddAgentInExitZone(
      voy::perception::ObjectType object_type, int id, int64_t zone_id,
      double portion, double length, double width, double velocity,
      double height = 1.5);

  // Adds a predicted trajectory to the agent and returns the reference to the
  // added predicted trajectory.
  prediction::pb::PredictedTrajectory& AddPredictedTrajectory(
      prediction::pb::Agent& agent);

  // Adds a stationary predicted trajectory whose all the predicted poses
  // are the same and set agent stationary intention type to
  // STATIONARY_NOT_TO_MOVE.
  prediction::pb::PredictedTrajectory& AddStationaryPredictedTrajectory(
      const math::Pose2d& pose, double likelihood, int traj_id,
      prediction::pb::Agent& agent) const;

  // Same as above but pose is defined by (lane_id, portion).
  prediction::pb::PredictedTrajectory& AddStationaryPredictedTrajectory(
      int64_t lane_id, double portion, double likelihood, int traj_id,
      prediction::pb::Agent& agent,
      double bbox_heading_diff_to_lane = 0.0) const;

  // Adds a predicted trajectory to the agent and returns the reference to the
  // added predicted trajectory. The predicted trajectory is a straight line
  // segment from start position to end position with given speed and
  // likelihood. Same as current prediction, it's a 9s trajectory with 0.1s
  // interval. The pose speed is calculated based on the total travel
  // distance and travel time.
  prediction::pb::PredictedTrajectory& AddStraightPredictedTrajectory(
      const math::Pose2d& start_pose, const math::Pose2d& end_pose,
      double likelihood, int traj_id, prediction::pb::Agent& agent);

  // Same as above but start position is defined by (start_lane_id,
  // start_portion) and end position is defined by (end_lane_id, end_portion).
  prediction::pb::PredictedTrajectory& AddStraightPredictedTrajectory(
      int64_t start_lane_id, double start_portion, int64_t end_lane_id,
      double end_portion, double likelihood, int traj_id,
      prediction::pb::Agent& agent, double bbox_heading_diff_to_lane = 0.0);

  // Same as above but the poses in the predicted trajectory will contain
  // constant lateral and longitudinal uncertainties.
  prediction::pb::PredictedTrajectory&
  AddStraightPredictedTrajectoryWithConstantUncertainty(
      int64_t start_lane_id, double start_portion, int64_t end_lane_id,
      double end_portion, double likelihood, int traj_id,
      prediction::pb::Agent& agent, double lat_sd, double long_sd,
      double bbox_heading_diff_to_lane = 0.0);

  // Same as above but the speed and acceleration of the poses in the predicted
  // trajectory will be updated by a constant accel speed profile.
  prediction::pb::PredictedTrajectory&
  AddStraightPredictedTrajectoryWithConstantAccel(
      int64_t start_lane_id, double start_portion, int64_t end_lane_id,
      double end_portion, double likelihood, int traj_id,
      prediction::pb::Agent& agent, double accel = 0.0);

  // Adds a moving predicted trajectory for the start-to-move agent and returns
  // the reference to the added predicted trajectory. The predicted trajectory's
  // is_multi_output_trajectory is set false.
  prediction::pb::PredictedTrajectory& AddStartToMovePredictedTrajectory(
      int64_t start_lane_id, double start_portion, int64_t end_lane_id,
      double end_portion, double likelihood, int traj_id,
      prediction::pb::Agent& agent, int start_to_move_idx = 0,
      double bbox_heading_diff_to_lane = 0.0);

  // Adds a predicted trajectory to the agent and returns the reference to the
  // added predicted trajectory. The predicted trajectory is a piece-wise
  // straight line segment between given waypoints and added poses are
  // interpolated between waypoints. Same as current prediction, it's a 9s
  // trajectory with 0.1s interval and has 91 points in total including all the
  // waypoints. Waypoints will be guaranteed to be at the waypoint indices
  // UNLESS it's the same value as the previous value. This is allowed to
  // support the function below and the user should never input the same index
  // for different waypoints. The first index of waypoint_indices must be 0 and
  // the last one must be kPredictedTrajectoryStep.
  prediction::pb::PredictedTrajectory& AddMultiPointPredictedTrajectory(
      const std::vector<math::Pose2d>& waypoints,
      const std::vector<int>& waypoint_indices, double likelihood, int traj_id,
      prediction::pb::Agent& agent);

  // Similar as above but this function will infer the waypoint indices assuming
  // the agent is approximately CONST SPEED all the time. Waypoints except the
  // start and end waypoints are NOT guaranteed to be included if they are too
  // close.
  prediction::pb::PredictedTrajectory& AddMultiPointPredictedTrajectory(
      const std::vector<math::Pose2d>& waypoints, double likelihood,
      int traj_id, prediction::pb::Agent& agent);

  // Adds a traffic light to the traffic_lights_ and returns the reference to
  // the traffic light. Please make sure that this function is called before
  // UpdateWorldModel() so as to access expected traffic light info from the
  // world model.
  voy::TrafficLight& AddTrafficLight(
      int64_t timestamp, int64_t sign_id, voy::TrafficLight::Color color,
      voy::TrafficLight::Type type = voy::TrafficLight::NORMAL,
      int32_t countdown_number = -1, bool is_flashing = false);

  // Returns a mutable reference to the planner object. Note that it could only
  // be called after Update*() function, so it might not fully update all the
  // values as expected.
  PlannerObject& MutablePlannerObject(int64_t id) {
    auto it = planner_object_map_.find(id);
    DCHECK(it != planner_object_map_.end());
    return it->second;
  }

  // Updates the |world_context_| after the WorldModel has been updated.
  void UpdateWorldContext();

  // Updates all upstream data for running lane change speed reasoning pipeline.
  void UpdateForLaneChange(double last_lane_change_point_distance = 0.0);

  // Updates all related data in cross lane info manager.
  void UpdateCrossLaneInfoManager();

  // Updates all related data in pull over info.
  void UpdatePullOverInfo();

  // Updates all related data in urgency info.
  void UpdateUrgencyInfo();

  // Updates all upstream datas for running the reasoning pipeline, including
  // - Update WorldModel based on ego_pose_ and path_;
  // - Update world_context_ and trajectory_info_;
  // - Call UpdateOverlapRegions() to update object_overlap_regions_;
  // - TODO: Update tracked_objects_ and predicted_trajectories_;
  void Update();

  // Computes and updates object_overlap_regions_ object_proximity_infos between
  // current path_ and current agent_list_.
  void UpdateOverlapRegionsAndProximityInfos();

  // Updates wrapped_prediction_decisions_ by calling PredictionDecisionMaker
  // on current agent_list_ and object_overlap_regions_.
  void UpdateWrappedPredictionDecisions();

  // Populate the input overlap_computer_ptr with updated infos.
  void PopulateOverlapComputerByUpdatedInfo(
      speed::OverlapComputer* overlap_computer_ptr) const;

  // Create ReferenceGenerator for path_, which could provide
  // CautiousDrivingLimiter for reasoners.
  speed::ReferenceGenerator CreateReferenceGenerator() const;

  // Updates agent yield type and yield probability.
  void AddAgentYieldIntention(
      prediction::pb::Agent& agent,
      ::prediction::pb::YieldIntentionType yield_intention_type,
      double yield_probability);

  // Set waypoint_assist_phase to given phase.
  void SetWaypointAssistPhase(::planner::pb::AssistManeuverPhase::Enum phase);

  // Set waypoint_assist_phase end point.
  void SetWaypointAssistEndPoint(const voy::Point2d& point);

  // Updates waypoint assist tracker with given assist command.
  void AddWaypointAssistCommand(
      const planner::pb::WaypointBasedAssistCommand& assist_command,
      const std::vector<int64_t>& lane_ids);

  void AddPhysicalBoundary(const math::geometry::Polyline2d& boundary_line,
                           math::pb::Side side);

  const WorldModel& world_model() const {
    DCHECK(world_model_.has_value());
    return world_model_.value();
  }

  WorldModel& mutable_world_model() {
    DCHECK(world_model_.has_value());
    return world_model_.value();
  }

  const SpeedWorldModel& speed_world_model() const {
    return *speed_world_model_;
  }

  WorldContext& world_context() {
    DCHECK(world_context_.has_value());
    return world_context_.value();
  }

  const UrgencyInfo& urgency_info() const {
    DCHECK(urgency_info_.has_value());
    return urgency_info_.value();
  }

  double rear_axle_to_front_bumper() {
    DCHECK(world_context_.has_value());
    return world_context().ra_to_leading_bumper();
  }

  double rear_axle_to_rear_bumper() {
    DCHECK(world_context_.has_value());
    // ra_to_trailing_bumper_shift is a negative value.
    return -world_context().ra_to_trailing_bumper_shift();
  }

  const TrajectoryInfo& trajectory_info() const {
    DCHECK(trajectory_info_.has_value());
    return trajectory_info_.value();
  }

  TrajectoryInfo& mutable_trajectory_info() {
    DCHECK(trajectory_info_.has_value());
    return trajectory_info_.value();
  }

  const LaneChangeStatusForSpeed& lane_change_status() const {
    return lane_change_status_;
  }

  const LaneChangeInfo& lane_change_info() const { return lane_change_info_; }

  planner::pb::PullOverProgress pull_over_progress() const {
    return pull_over_status_.pull_over_progress();
  }

  planner::pb::MotionMode motion_mode() const { return motion_mode_; }

  planner::pb::ReverseDrivingInfo reverse_driving_info() const {
    return reverse_driving_info_;
  }

  const open_space::OpenSpaceInfo& open_space_info() const {
    return open_space_info_;
  }

  const voy::Pose& ego_pose() const { return ego_pose_; }

  const adv_geom::Path2dWithJuke& path() const {
    DCHECK(extended_path_.has_value());
    return extended_path_.value();
  }

  const adv_geom::Path2dWithJuke& unextended_path() const {
    DCHECK(unextended_path_.has_value());
    return unextended_path_.value();
  }

  const speed::PathForOverlap& path_for_overlap() const {
    DCHECK(trajectory_info_.has_value());
    return trajectory_info_->unextended_path_for_overlap();
  }

  const path::PathEvaluationResult& path_evaluation_result() const {
    DCHECK(path_evaluation_result_.has_value());
    return path_evaluation_result_.value();
  }

  const LaneSequenceResult& lane_sequence_result() const {
    return lane_sequence_result_;
  }

  const prediction::pb::AgentList& agent_list() const { return agent_list_; }

  const std::unordered_map<ObjectId, planner::PlannerObject>&
  planner_object_map() const {
    return planner_object_map_;
  }

  std::map<ObjectId, speed::pb::OverlapComputationPolicy::Type>&
  mutable_overlap_computation_policy_map() {
    return overlap_computation_policy_map_;
  }

  const std::map<ObjectId, speed::pb::OverlapComputationPolicy::Type>&
  overlap_computation_policy_map() const {
    return overlap_computation_policy_map_;
  }

  const std::unordered_map<ConstructionZoneId, const ConstructionZone*>&
  cz_ptr_map() const {
    DCHECK(world_model_.has_value());
    return world_model_->construction_zone_ptr_map();
  }

  const tbb::concurrent_unordered_map<
      ObjectId, std::vector<planner::PredictedTrajectoryWrapper>>&
  object_prediction_map() const {
    DCHECK(world_model_.has_value());
    return world_model_->global_object_manager_ptr()->object_prediction_map();
  }

  const std::map<ObjectId, std::vector<speed::pb::OverlapRegion>>&
  object_overlap_regions() const {
    return object_overlap_regions_;
  }

  std::map<ObjectId, std::vector<speed::pb::OverlapRegion>>&
  mutable_object_overlap_regions() {
    return object_overlap_regions_;
  }

  const std::map<ConstructionZoneId, std::vector<speed::pb::OverlapRegion>>&
  cz_overlap_regions() const {
    return cz_overlap_regions_;
  }

  std::map<ConstructionZoneId, std::vector<speed::pb::OverlapRegion>>&
  mutable_cz_overlap_regions() {
    return cz_overlap_regions_;
  }

  const std::map<ConstructionZoneId, speed::pb::ObjectProximityInfo>&
  cz_proximity_infos() const {
    return cz_proximity_infos_;
  }

  std::map<ConstructionZoneId, speed::pb::ObjectProximityInfo>&
  mutable_cz_proximity_infos() {
    return cz_proximity_infos_;
  }

  std::vector<speed::pb::OverlapRegion> overlap_regions() const {
    std::vector<speed::pb::OverlapRegion> overlap_regions;
    for (const auto& [object_id, regions] : object_overlap_regions_) {
      overlap_regions.insert(overlap_regions.end(), regions.begin(),
                             regions.end());
    }
    return overlap_regions;
  }

  const std::map<ObjectId, speed::pb::ObjectProximityInfo>&
  object_proximity_infos() const {
    return object_proximity_infos_;
  }

  std::map<ObjectId, speed::pb::ObjectProximityInfo>&
  mutable_object_proximity_infos() {
    return object_proximity_infos_;
  }

  std::unordered_map<ObjectId, PrincipledRequiredLateralGap>&
  mutable_obj_id_to_required_lat_gaps() {
    return obj_id_to_required_lat_gaps_;
  }

  const std::unordered_map<ObjectId, PrincipledRequiredLateralGap>&
  obj_id_to_required_lat_gaps() const {
    return obj_id_to_required_lat_gaps_;
  }

  speed::pb::EgoLateralClearanceInfo& mutable_ego_lateral_clearance_info() {
    return ego_lateral_clearance_info_;
  }

  const speed::pb::EgoLateralClearanceInfo& ego_lateral_clearance_info() const {
    return ego_lateral_clearance_info_;
  }

  PinchRegionsMapper& mutable_pinch_regions_mapper() {
    return pinch_regions_mapper_;
  }

  const PinchRegionsMapper& pinch_regions_mapper() const {
    return pinch_regions_mapper_;
  }

  const std::vector<PredictionDecisionMakerInput>& reasoning_inputs() const {
    return reasoning_inputs_;
  }

  const std::vector<PredictionDecisionMakerOutput>&
  wrapped_prediction_decisions() const {
    return wrapped_prediction_decisions_;
  }

  std::vector<PredictionDecisionMakerOutput>&
  mutable_wrapped_prediction_decisions() {
    return wrapped_prediction_decisions_;
  }

  // Finds the prediction decision for the given object trajectory id.
  // This function checks all of the prediction decisions from the prediction
  // decision maker outputs.
  const PredictionDecision& prediction_decision(
      const ObjectTrajectoryId& id) const;

  const LeftRightBoundaryPair& physical_boundaries() const {
    return physical_boundaries_;
  }

  // Converts a lane point defined by land_id and portion to pose in global
  // frame and return the pose. lane_id should be valid inside the
  // pnc_map_service and portion should be within [0.0, 1.0].
  math::Pose2d LanePointToPose(int64_t lane_id, double portion,
                               double heading_diff_to_lane = 0.0) const;

  // Similar as above, but the returned pose will be shifted by 1.) shift along
  // the lane heading at `portion` by arclength_shift, then shift laterally by
  // lateral_shift (positive for left) and finally rotated by theta_shift
  // (positive for anti-clock).
  math::Pose2d LanePointToPoseWithShift(int64_t lane_id, double portion,
                                        double arclength_shift,
                                        double lateral_shift,
                                        double theta_shift) const;

  // NOTE: Construct trajectory_info before calling the function as it depends
  // on the crosswalk in lane sequence. The result pose will be located at the
  // center line of the crosswalk given a crosswalk id and the portion.
  math::Pose2d FindPoseInCrosswalk(int64_t crosswalk_id, double portion) const;

  // NOTE: Construct trajectory_info before calling the function as it depends
  // on the zone in lane sequence. The result pose will be located at the center
  // line of the exit zone given the zone id and the portion.
  math::Pose2d FindPoseInExitZone(int64_t zone_id, double portion) const;

  // Current timestamp.
  int64_t timestamp() const { return timestamp_; }

  double plan_init_ra_arc_length_on_extended_path() const {
    return path().GetProjectionArcLength(
        {world_model().robot_state().plan_init_state_snapshot().x(),
         world_model().robot_state().plan_init_state_snapshot().y()},
        math::pb::UseExtensionFlag::kForbid);
  }

  void IncreaseTime(int64_t duration) { timestamp_ += duration; }

  planner::pb::DecoupledManeuverSeed& mutable_previous_iter_seed() {
    return previous_iter_seed_;
  }

  const planner::pb::DecoupledManeuverSeed& previous_iter_seed() const {
    return previous_iter_seed_;
  }

  const planner::pb::PathReasoningSeed& path_reasoning_seed() const {
    return path_reasoning_seed_;
  }

  planner::pb::LaneSequenceCostFactors* mutable_lane_sequence_cost_factors() {
    return &lane_sequence_cost_factors_;
  }

  const planner::pb::LaneSequenceCostFactors& lane_sequence_cost_factors() {
    return lane_sequence_cost_factors_;
  }

  planner::pb::PathReasoningSeed* mutable_path_reasoning_seed() {
    return &path_reasoning_seed_;
  }

  speed::pb::SpeedSeed* mutable_current_iter_seed() {
    return &mutable_current_iter_seed_;
  }

  speed::pb::TrajectoryInfoSeeds* mutable_current_trajectory_info_seeds() {
    return mutable_current_iter_seed_.mutable_trajectory_info_seeds();
  }

  const planner::pb::IntentionResult& ego_intention() const {
    return ego_intention_;
  }

  const TurnSignalState& current_turn_signal_state() const {
    return current_turn_signal_state_;
  }

  planner::pb::IntentionResult& mutable_ego_intention() {
    return ego_intention_;
  }

  const planner::pb::PlanningDebug& planning_debug() const {
    return planning_debug_;
  }

  planner::pb::PlanningDebug& mutable_planning_debug() {
    return planning_debug_;
  }

  const pnc_map::MapTestUtil& map_test_util() const { return map_test_util_; }

  const std::vector<Constraint>& constraints() const { return constraints_; }

  const LaneSequenceIterator& lane_sequence_iterator() const {
    DCHECK(lane_sequence_iterator_opt_.has_value());
    return lane_sequence_iterator_opt_.value();
  }

  const planner::pb::PlanningSegmentSequence& planning_segment_sequence()
      const {
    DCHECK(planning_segment_sequence_opt_.has_value());
    return planning_segment_sequence_opt_.value();
  }

  const speed::OverlapComputer& overlap_computer() const {
    DCHECK(overlap_computer_opt_.has_value());
    return overlap_computer_opt_.value();
  }

  const speed::pb::SpeedGeneratorConfig& GetSpeedGeneratorConfig() const;

  const planner::pb::PlannerObjectConfig& GetPlannerObjectConfig() const;

  const speed::pb::ReasoningConfig& GetReasoningConfig() const;
  const speed::pb::VruReasonerConfig& GetVruReasonerConfig() const;

  const speed::pb::ReasoningInputConfig& GetReasoningInputConfig() const;

  // Returns the pointer to speed::pb::ReasoningDebug in planning_debug. If it's
  // not created yet, creates and returns the pointer. Note that for reasoning
  // unit test, there should be only one decoupled base manuever.
  speed::pb::ReasoningDebug* GetOrCreateReasoningDebug();

  // Generates Ego's Trajectory from path_ with given constant speed. This is
  // only used by WriteToRosBag for visualizing path_;
  planner::pb::Trajectory GenerateTrajectoryFromPath(double speed = 10.0) const;

  // Gets lane sequence with the given lane ids.
  std::vector<const pnc_map::Lane*> GetLaneSequenceFromId(
      const std::vector<int64_t>& lane_sequence_id) const;

  // Gets lane with the given lane id.
  const pnc_map::Lane* GetLaneFromId(int64_t lane_id) const;

  // Sets the distance to destination.
  void SetDistanceToDestination(double distance_to_destination_m) {
    mutable_world_model()
        .regional_map_.regional_path.distance_to_destination_m =
        distance_to_destination_m;
  }

  // Returns the PredictionDecision by object id and trajectory id. If no such
  // prediction decision is found, DCHECK will fail given this is only used in
  // test and caller should make sure there is corresponding prediction
  // decisions.
  const PredictionDecision& GetPredictionDecision(int64_t object_id,
                                                  int trajectory_id) const;

  // Adds the timestamp as first see ego time and last see ego time for the
  // object.
  void AddAgentSeenEgoTimeToSeed(ObjectId object_id, int64_t timestamp);

  VehicleParamsForOverlap GetVehicleParamsForOverlap() const;

  // This function will write the test scenario into a ros bag and store it
  // in the filepath defined by bag_name. This function is only used for
  // visualization and should never be called onboard. These topics will be
  // written:
  // "/prediction/predicted_objects" <- ::prediction::pb::AgentList
  // "/planning/planning_debug" <- ::planner::pb::PlanningDebug
  // "/planning/trajectory" <- ::planner::pb::Trajectory
  void WriteToRosBag(const std::string& bag_name = "/tmp/test.bag") const;

  // This function will only update world_model and world_context.
  void UpdateWorldModel(
      const std::optional<mrc::pb::MrcRequest>& mrc_request = std::nullopt,
      const std::optional<const std::vector<planner::pb::AssistResponse>>&
          assist_response = std::nullopt);

  void ExtractToSpeedWorldModel();

  void UpdatePreviousSeed();

  // After this function is called, the state of ReasoningTestFixture is set to
  // the state after SetUpRegionMap is called, i.e. world_model_ and
  // world_context have been set, while others are not. The seeds are not
  // cleared. This function is only used to add test which crosses multiple
  // cycles.
  void Reset();

  ConstraintCreator GenerateConstraintCreator();

  void AddLightAssistResponse(
      const planner::pb::AssistResponse& assist_response);

  ReasoningPolicies& mutable_reasoning_policies() {
    return reasoning_policies_;
  }

  const ReasoningPolicies& reasoning_policies() const {
    return reasoning_policies_;
  }

 protected:
  // A helper function to add linear interpolated poses into predicted
  // trajectory. If including_end_pose is false, there are (num_of_poses - 1)
  // interpolated poses and else there are (num_of_poses - 2) interpolated poses
  // so that there are always num_of_poses poses added.
  prediction::pb::PredictedTrajectory&
  AddLinearInterpolatedPosesToPredictedTrajectory(
      const math::Pose2d& start_pose, const math::Pose2d& end_pose,
      int64_t start_timestamp, double start_odom, int num_of_poses,
      bool including_end_pose,
      prediction::pb::PredictedTrajectory& predicted_trajectory);

  // Construct unextended_path by truncating the points of extended_path before
  // ego_pose.
  void GenerateUnextendedPath();

  // Returns the destination meta data ptr for pull over trajectory. For
  // non-pull-over behavior type, return nullptr.
  const pull_over::DestinationMetaData* pull_over_dest_meta_ptr() const {
    return behavior_type_ == planner::pb::BehaviorType::DECOUPLED_PULL_OVER
               ? &destination_
               : nullptr;
  }

  pnc_map::MapTestUtil map_test_util_;

  // Used to update world model.
  voy::Canbus canbus_;

  std::optional<WorldModel> world_model_;

  std::unique_ptr<SpeedWorldModel> speed_world_model_;

  std::optional<WorldContext> world_context_;

  std::optional<TrajectoryInfo> trajectory_info_;

  std::optional<UrgencyInfo> urgency_info_;

  CrossLaneInfoManager cross_lane_info_manager_;

  LaneChangeStatusForSpeed lane_change_status_;

  LaneChangeInfo lane_change_info_;

  // Pose of Ego geometry center in global frames. Note that planner uses Ego
  // rear axle as origin.
  voy::Pose ego_pose_;

  int64_t ego_lane_id_ = -1;

  planner::pb::ManeuverType maneuver_type_ =
      planner::pb::ManeuverType::DECOUPLED_FORWARD;

  planner::pb::BehaviorType behavior_type_ =
      planner::pb::BehaviorType::LANE_KEEP;

  std::optional<adv_geom::Path2dWithJuke> extended_path_;

  std::optional<adv_geom::Path2dWithJuke> unextended_path_;

  std::optional<path::PathEvaluationResult> path_evaluation_result_;

  LaneSequenceResult lane_sequence_result_;

  // This is needed besides lane_sequence_result_ since it's contained by
  // trajectory_info_ and has to outlive it.
  std::optional<LaneSequenceIterator> lane_sequence_iterator_opt_;

  std::optional<planner::pb::PlanningSegmentSequence>
      planning_segment_sequence_opt_;

  std::optional<speed::OverlapComputer> overlap_computer_opt_;

  prediction::pb::AgentList agent_list_;

  std::vector<voy::ConstructionZone> construction_zones_;

  voy::TrafficLights traffic_lights_;

  std::unordered_map<ObjectId, planner::PlannerObject> planner_object_map_;

  std::map<ObjectId, std::vector<speed::pb::OverlapRegion>>
      object_overlap_regions_;

  std::map<ConstructionZoneId, std::vector<speed::pb::OverlapRegion>>
      cz_overlap_regions_;

  std::map<ObjectId, speed::pb::ObjectProximityInfo> object_proximity_infos_;

  std::map<ConstructionZoneId, speed::pb::ObjectProximityInfo>
      cz_proximity_infos_;

  std::unordered_map<ObjectId, PrincipledRequiredLateralGap>
      obj_id_to_required_lat_gaps_;

  speed::pb::EgoLateralClearanceInfo ego_lateral_clearance_info_;

  PinchRegionsMapper pinch_regions_mapper_;

  pull_over::PullOverInfo pull_over_info_;
  pull_over::PullOverStatus pull_over_status_;
  // TODO(Zixuan, Liwen): Fit-in multi-dest in the future.
  pull_over::DestinationMetaData destination_;
  // TODO(Zixuan, Liwen): Add more required fields to support pull over related
  // UTs.

  bool is_pull_out_jump_out_ = false;

  bool is_for_ra_unstuck_request_ = false;

  bool is_lane_sequence_feasible_ = true;

  planner::pb::LaneSequenceCostFactors lane_sequence_cost_factors_;

  bool is_waypoint_assist_invoked_ = false;

  bool is_waypoint_backup_trajectory_used_ = false;

  bool is_hard_boundary_relaxed_in_path_ = false;

  planner::pb::MotionMode motion_mode_ = planner::pb::MotionMode::FORWARD;

  planner::pb::ReverseDrivingInfo reverse_driving_info_;

  open_space::OpenSpaceInfo open_space_info_;

  // This is needed because the ReasoningObject and AgentTrajectoryInfo should
  // outlive reasoners.
  std::vector<PredictionDecisionMakerInput> reasoning_inputs_;

  ReasoningPolicies reasoning_policies_;

  std::vector<PredictionDecisionMakerOutput> wrapped_prediction_decisions_;

  LeftRightBoundaryPair physical_boundaries_;

  std::vector<Constraint> constraints_;

  GlobalSpeedSolverSettings solver_global_strategy_;

  planner::pb::DecoupledManeuverSeed& previous_iter_seed_;

  planner::pb::PathReasoningSeed path_reasoning_seed_;

  speed::pb::SpeedSeed mutable_current_iter_seed_;

  planner::pb::IntentionResult ego_intention_;

  TurnSignalState current_turn_signal_state_;

  planner::pb::PlanningDebug planning_debug_;

  // This is w.r.t linux epoch in unit ms
  int64_t timestamp_ = 1656008770000;

  std::map<ObjectId, speed::pb::OverlapComputationPolicy::Type>
      overlap_computation_policy_map_;
};

}  // namespace speed
}  // namespace planner

#endif  // ONBOARD_PLANNER_SPEED_REASONING_REASONING_TEST_FIXTURE_H_
