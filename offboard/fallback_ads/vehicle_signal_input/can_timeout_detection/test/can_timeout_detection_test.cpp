#include "vehicle_signal_input/can_timeout_detection/can_timeout_detection.h"

#include <gtest/gtest.h>

#include "_out/Appl/GenData/Components/Rte_VehSigInput_Type.h"
#include "_out/Appl/GenData/Rte_Type.h"
#include "vehicle_signal_input/vehicle_signal_input_parameter.h"
#include "vehicle_signal_input/vehicle_signal_input_type.h"

TEST(CanCounterMapping, CanCounterMappingMixedValuesTest) {
  VehicleAndAcuCanSignal can_signal = {0};

  can_signal.PrimVehSpdGroupSafeCntr = 1U;
  can_signal.PrimALgtDataRawSafeCntr = 2U;
  can_signal.AgDataRawSafeCntr = 3U;
  can_signal.BrkTqMinReqBrkTqMinReqCntr = 4U;
  can_signal.AdPrimWhlAgEstimdGroupSafeCntr = 5U;
  can_signal.PrimWhlAgSpdFrntSafeCntr = 6U;
  can_signal.BrkDegradedRdntCntr = 7U;
  can_signal.AdSecSteerStsSafeGroupCntr = 8U;
  can_signal.AdNomALgtReqGroupSafeCntr_A = 9U;
  can_signal.AdPrimALgtLimReqGroupSafeCntr_A = 10U;
  can_signal.AdPrimPoseCntr_A = 11U;
  can_signal.AdPrimWhlAgReqGroupSafeCntr_A = 12U;
  can_signal.AdSecALgtLimReqGroupSafeCntr_A = 13U;
  can_signal.AdSecWhlAgReqGroupSafeCntr_A = 14U;
  can_signal.AcuFbRollingCounter = 15U;

  uint8 mcu_can_counter[15] = {0};
  CanCounterMapping(&can_signal, mcu_can_counter, VSI_CAN_TIME_OUT_ARRAY_SIZE);

  int expected_value = 1;
  for (const auto& counter : mcu_can_counter) {
    EXPECT_EQ(counter, expected_value);
    expected_value = expected_value + 1;
  }
}

TEST(CanCounterMapping, CanCounterMappingZeroTest) {
  VehicleAndAcuCanSignal can_signal = {0};

  uint8 mcu_can_counter[15] = {0};
  CanCounterMapping(&can_signal, mcu_can_counter, VSI_CAN_TIME_OUT_ARRAY_SIZE);

  for (const auto& counter : mcu_can_counter) {
    EXPECT_EQ(counter, 0U);
  }
}

TEST(CanCounterMapping, CanCounterMappingMaxValueTest) {
  VehicleAndAcuCanSignal can_signal = {0xFF};
  can_signal.PrimVehSpdGroupSafeCntr = 0xFF;
  can_signal.PrimALgtDataRawSafeCntr = 0xFF;
  can_signal.AgDataRawSafeCntr = 0xFF;
  can_signal.BrkTqMinReqBrkTqMinReqCntr = 0xFF;
  can_signal.AdPrimWhlAgEstimdGroupSafeCntr = 0xFF;
  can_signal.PrimWhlAgSpdFrntSafeCntr = 0xFF;
  can_signal.BrkDegradedRdntCntr = 0xFF;
  can_signal.AdSecSteerStsSafeGroupCntr = 0xFF;
  can_signal.AdNomALgtReqGroupSafeCntr_A = 0xFF;
  can_signal.AdPrimALgtLimReqGroupSafeCntr_A = 0xFF;
  can_signal.AdPrimPoseCntr_A = 0xFF;
  can_signal.AdPrimWhlAgReqGroupSafeCntr_A = 0xFF;
  can_signal.AdSecALgtLimReqGroupSafeCntr_A = 0xFF;
  can_signal.AdSecWhlAgReqGroupSafeCntr_A = 0xFF;
  can_signal.AcuFbRollingCounter = 0xFF;

  uint8 mcu_can_counter[15] = {0};
  CanCounterMapping(&can_signal, mcu_can_counter, VSI_CAN_TIME_OUT_ARRAY_SIZE);
  for (const auto& counter : mcu_can_counter) {
    EXPECT_EQ(counter, 0xFF);
  }
}

TEST(GetMcuCanTimeoutResult, GetMcuCanTimeoutResultTest) {
  float can_timeout_timer_array[VSI_CAN_TIME_OUT_ARRAY_SIZE] = {0};
  bool can_timeout_enable_array[VSI_CAN_TIME_OUT_ARRAY_SIZE] = {false};

  for (int i = 0; i < VSI_CAN_TIME_OUT_ARRAY_SIZE; ++i) {
    can_timeout_timer_array[i] = i * 100.0F / 1000.0F;
    can_timeout_enable_array[i] = (i % 2 == 0);
  }

  VSI_McuCanTimeout_Struct mcu_can_timeout;
  GetMcuCanTimeoutResult(can_timeout_timer_array, can_timeout_enable_array,
                         VSI_CAN_TIME_OUT_ARRAY_SIZE, &mcu_can_timeout);

  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_VehMid3SsmCounter0Timer, 0.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_VehMid3SsmCounter1Timer, 100.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_VehMid3VcuCounter0Timer, 200.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_VehMid3VcuCounter1Timer, 300.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_VehMid5SsmCounter0Timer, 400.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_VehMid5SsmCounter1Timer, 500.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_VehMid6SsmCounter0Timer, 600.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_VehMid6SsmCounter1Timer, 700.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_AcuMid3SsmCounter0Timer, 800.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_AcuMid3SsmCounter1Timer, 900.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_AcuMid5SsmCounter0Timer, 1000.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_AcuMid5SsmCounter1Timer, 1100.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_AcuMid6SsmCounter0Timer, 1200.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_AcuMid6SsmCounter1Timer, 1300.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_AcuFbCanTimer, 1400.0F);

  EXPECT_TRUE(mcu_can_timeout.VSI_VehMid3SsmCounter0Timeout);
  EXPECT_FALSE(mcu_can_timeout.VSI_VehMid3SsmCounter1Timeout);
  EXPECT_TRUE(mcu_can_timeout.VSI_VehMid3VcuCounter0Timeout);
  EXPECT_FALSE(mcu_can_timeout.VSI_VehMid3VcuCounter1Timeout);
  EXPECT_TRUE(mcu_can_timeout.VSI_VehMid5SsmCounter0Timeout);
  EXPECT_FALSE(mcu_can_timeout.VSI_VehMid5SsmCounter1Timeout);
  EXPECT_TRUE(mcu_can_timeout.VSI_VehMid6SsmCounter0Timeout);
  EXPECT_FALSE(mcu_can_timeout.VSI_VehMid6SsmCounter1Timeout);
  EXPECT_TRUE(mcu_can_timeout.VSI_AcuMid3SsmCounter0Timeout);
  EXPECT_FALSE(mcu_can_timeout.VSI_AcuMid3SsmCounter1Timeout);
  EXPECT_TRUE(mcu_can_timeout.VSI_AcuMid5SsmCounter0Timeout);
  EXPECT_FALSE(mcu_can_timeout.VSI_AcuMid5SsmCounter1Timeout);
  EXPECT_TRUE(mcu_can_timeout.VSI_AcuMid6SsmCounter0Timeout);
  EXPECT_FALSE(mcu_can_timeout.VSI_AcuMid6SsmCounter1Timeout);
  EXPECT_TRUE(mcu_can_timeout.VSI_AcuFbCanTimeout);
}

TEST(GetMcuCanTimeoutResult, GetMcuCanTimeoutResultAllEnabledTest) {
  float can_timeout_timer_array[VSI_CAN_TIME_OUT_ARRAY_SIZE] = {0};
  bool can_timeout_enable_array[VSI_CAN_TIME_OUT_ARRAY_SIZE] = {true};

  for (int i = 0; i < VSI_CAN_TIME_OUT_ARRAY_SIZE; ++i) {
    can_timeout_timer_array[i] = i * 100.0F / 1000.0F;
    can_timeout_enable_array[i] = true;
  }

  VSI_McuCanTimeout_Struct mcu_can_timeout;
  GetMcuCanTimeoutResult(can_timeout_timer_array, can_timeout_enable_array,
                         VSI_CAN_TIME_OUT_ARRAY_SIZE, &mcu_can_timeout);

  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_VehMid3SsmCounter0Timer, 0.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_VehMid3SsmCounter1Timer, 100.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_VehMid3VcuCounter0Timer, 200.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_VehMid3VcuCounter1Timer, 300.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_VehMid5SsmCounter0Timer, 400.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_VehMid5SsmCounter1Timer, 500.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_VehMid6SsmCounter0Timer, 600.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_VehMid6SsmCounter1Timer, 700.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_AcuMid3SsmCounter0Timer, 800.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_AcuMid3SsmCounter1Timer, 900.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_AcuMid5SsmCounter0Timer, 1000.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_AcuMid5SsmCounter1Timer, 1100.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_AcuMid6SsmCounter0Timer, 1200.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_AcuMid6SsmCounter1Timer, 1300.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_AcuFbCanTimer, 1400.0F);

  EXPECT_TRUE(mcu_can_timeout.VSI_VehMid3SsmCounter0Timeout);
  EXPECT_TRUE(mcu_can_timeout.VSI_VehMid3SsmCounter1Timeout);
  EXPECT_TRUE(mcu_can_timeout.VSI_VehMid3VcuCounter0Timeout);
  EXPECT_TRUE(mcu_can_timeout.VSI_VehMid3VcuCounter1Timeout);
  EXPECT_TRUE(mcu_can_timeout.VSI_VehMid5SsmCounter0Timeout);
  EXPECT_TRUE(mcu_can_timeout.VSI_VehMid5SsmCounter1Timeout);
  EXPECT_TRUE(mcu_can_timeout.VSI_VehMid6SsmCounter0Timeout);
  EXPECT_TRUE(mcu_can_timeout.VSI_VehMid6SsmCounter1Timeout);
  EXPECT_TRUE(mcu_can_timeout.VSI_AcuMid3SsmCounter0Timeout);
  EXPECT_TRUE(mcu_can_timeout.VSI_AcuMid3SsmCounter1Timeout);
  EXPECT_TRUE(mcu_can_timeout.VSI_AcuMid5SsmCounter0Timeout);
  EXPECT_TRUE(mcu_can_timeout.VSI_AcuMid5SsmCounter1Timeout);
  EXPECT_TRUE(mcu_can_timeout.VSI_AcuMid6SsmCounter0Timeout);
  EXPECT_TRUE(mcu_can_timeout.VSI_AcuMid6SsmCounter1Timeout);
  EXPECT_TRUE(mcu_can_timeout.VSI_AcuFbCanTimeout);
}

TEST(GetMcuCanTimeoutResult, GetMcuCanTimeoutResultAllDisabledTest) {
  float can_timeout_timer_array[VSI_CAN_TIME_OUT_ARRAY_SIZE] = {0};
  bool can_timeout_enable_array[VSI_CAN_TIME_OUT_ARRAY_SIZE] = {false};

  for (int i = 0; i < VSI_CAN_TIME_OUT_ARRAY_SIZE; ++i) {
    can_timeout_timer_array[i] = i * 100.0F / 1000.0F;
  }

  VSI_McuCanTimeout_Struct mcu_can_timeout;
  GetMcuCanTimeoutResult(can_timeout_timer_array, can_timeout_enable_array,
                         VSI_CAN_TIME_OUT_ARRAY_SIZE, &mcu_can_timeout);

  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_VehMid3SsmCounter0Timer, 0.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_VehMid3SsmCounter1Timer, 100.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_VehMid3VcuCounter0Timer, 200.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_VehMid3VcuCounter1Timer, 300.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_VehMid5SsmCounter0Timer, 400.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_VehMid5SsmCounter1Timer, 500.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_VehMid6SsmCounter0Timer, 600.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_VehMid6SsmCounter1Timer, 700.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_AcuMid3SsmCounter0Timer, 800.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_AcuMid3SsmCounter1Timer, 900.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_AcuMid5SsmCounter0Timer, 1000.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_AcuMid5SsmCounter1Timer, 1100.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_AcuMid6SsmCounter0Timer, 1200.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_AcuMid6SsmCounter1Timer, 1300.0F);
  EXPECT_FLOAT_EQ(mcu_can_timeout.VSI_AcuFbCanTimer, 1400.0F);

  EXPECT_FALSE(mcu_can_timeout.VSI_VehMid3SsmCounter0Timeout);
  EXPECT_FALSE(mcu_can_timeout.VSI_VehMid3SsmCounter1Timeout);
  EXPECT_FALSE(mcu_can_timeout.VSI_VehMid3VcuCounter0Timeout);
  EXPECT_FALSE(mcu_can_timeout.VSI_VehMid3VcuCounter1Timeout);
  EXPECT_FALSE(mcu_can_timeout.VSI_VehMid5SsmCounter0Timeout);
  EXPECT_FALSE(mcu_can_timeout.VSI_VehMid5SsmCounter1Timeout);
  EXPECT_FALSE(mcu_can_timeout.VSI_VehMid6SsmCounter0Timeout);
  EXPECT_FALSE(mcu_can_timeout.VSI_VehMid6SsmCounter1Timeout);
  EXPECT_FALSE(mcu_can_timeout.VSI_AcuMid3SsmCounter0Timeout);
  EXPECT_FALSE(mcu_can_timeout.VSI_AcuMid3SsmCounter1Timeout);
  EXPECT_FALSE(mcu_can_timeout.VSI_AcuMid5SsmCounter0Timeout);
  EXPECT_FALSE(mcu_can_timeout.VSI_AcuMid5SsmCounter1Timeout);
  EXPECT_FALSE(mcu_can_timeout.VSI_AcuMid6SsmCounter0Timeout);
  EXPECT_FALSE(mcu_can_timeout.VSI_AcuMid6SsmCounter1Timeout);
  EXPECT_FALSE(mcu_can_timeout.VSI_AcuFbCanTimeout);
}

TEST(CheckMcuCanTimeout, CheckMcuCanTimeoutTest) {
  VehicleAndAcuCanSignal can_signal = {0};
  VSI_McuCanTimeout_Struct mcu_can_timeout = {0};
  float timer = 5.0F * 1000.0F;

  for (int i = 0; i < 500; ++i) {
    CheckMcuCanTimeout(&can_signal, &mcu_can_timeout);
  }

  ASSERT_NEAR(mcu_can_timeout.VSI_VehMid3SsmCounter0Timer, timer, 10.0F);
  ASSERT_NEAR(mcu_can_timeout.VSI_VehMid3SsmCounter1Timer, timer, 10.0F);
  ASSERT_NEAR(mcu_can_timeout.VSI_VehMid3VcuCounter0Timer, timer, 10.0F);
  ASSERT_NEAR(mcu_can_timeout.VSI_VehMid3VcuCounter1Timer, timer, 10.0F);
  ASSERT_NEAR(mcu_can_timeout.VSI_VehMid5SsmCounter0Timer, timer, 10.0F);
  ASSERT_NEAR(mcu_can_timeout.VSI_VehMid5SsmCounter1Timer, timer, 10.0F);
  ASSERT_NEAR(mcu_can_timeout.VSI_VehMid6SsmCounter0Timer, timer, 10.0F);
  ASSERT_NEAR(mcu_can_timeout.VSI_VehMid6SsmCounter1Timer, timer, 10.0F);
  ASSERT_NEAR(mcu_can_timeout.VSI_AcuMid3SsmCounter0Timer, timer, 10.0F);
  ASSERT_NEAR(mcu_can_timeout.VSI_AcuMid3SsmCounter1Timer, timer, 10.0F);
  ASSERT_NEAR(mcu_can_timeout.VSI_AcuMid5SsmCounter0Timer, timer, 10.0F);
  ASSERT_NEAR(mcu_can_timeout.VSI_AcuMid5SsmCounter1Timer, timer, 10.0F);
  ASSERT_NEAR(mcu_can_timeout.VSI_AcuMid6SsmCounter0Timer, timer, 10.0F);
  ASSERT_NEAR(mcu_can_timeout.VSI_AcuMid6SsmCounter1Timer, timer, 10.0F);
  ASSERT_NEAR(mcu_can_timeout.VSI_AcuFbCanTimer, timer, 10.0F);

  ASSERT_TRUE(mcu_can_timeout.VSI_VehMid3SsmCounter0Timeout);
  ASSERT_TRUE(mcu_can_timeout.VSI_VehMid3SsmCounter1Timeout);
  ASSERT_TRUE(mcu_can_timeout.VSI_VehMid3VcuCounter0Timeout);
  ASSERT_TRUE(mcu_can_timeout.VSI_VehMid3VcuCounter1Timeout);
  ASSERT_TRUE(mcu_can_timeout.VSI_VehMid5SsmCounter0Timeout);
  ASSERT_TRUE(mcu_can_timeout.VSI_VehMid5SsmCounter1Timeout);
  ASSERT_TRUE(mcu_can_timeout.VSI_VehMid6SsmCounter0Timeout);
  ASSERT_TRUE(mcu_can_timeout.VSI_VehMid6SsmCounter1Timeout);
  ASSERT_TRUE(mcu_can_timeout.VSI_AcuMid3SsmCounter0Timeout);
  ASSERT_TRUE(mcu_can_timeout.VSI_AcuMid3SsmCounter1Timeout);
  ASSERT_TRUE(mcu_can_timeout.VSI_AcuMid5SsmCounter0Timeout);
  ASSERT_TRUE(mcu_can_timeout.VSI_AcuMid5SsmCounter1Timeout);
  ASSERT_TRUE(mcu_can_timeout.VSI_AcuMid6SsmCounter0Timeout);
  ASSERT_TRUE(mcu_can_timeout.VSI_AcuMid6SsmCounter1Timeout);
  ASSERT_TRUE(mcu_can_timeout.VSI_AcuFbCanTimeout);
}

TEST(CheckMainCanTimeout, CheckMainCanTimeoutTest) {
  CheckMainCanTimeout(1);
  for (int i = 0; i < 1000; ++i) {
    ASSERT_FALSE(CheckMainCanTimeout(i % 256));
  }

  for (int i = 0; i < 51; ++i) {
    ASSERT_FALSE(CheckMainCanTimeout(0));
  }
  ASSERT_TRUE(CheckMainCanTimeout(0));
  ASSERT_FALSE(CheckMainCanTimeout(1));
}

TEST(CheckRedundantCanTimeout, CheckRedundantCanTimeoutTest) {
  CheckRedundantCanTimeout(1);
  for (int i = 0; i < 1000; ++i) {
    ASSERT_FALSE(CheckRedundantCanTimeout(i % 256));
  }
  for (int i = 0; i < 51; ++i) {
    ASSERT_FALSE(CheckRedundantCanTimeout(0));
  }
  ASSERT_TRUE(CheckRedundantCanTimeout(0));
  ASSERT_FALSE(CheckRedundantCanTimeout(1));
}
