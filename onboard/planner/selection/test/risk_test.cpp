#include "planner/selection/risk/agent_reaction_fusion.h"
#include "planner/selection/risk/risk_reasoner.h"
#include "planner/selection/risk/risk_solver.h"
#include "planner/selection/risk/risk_utils.h"
#include "planner/speed/constraint/constraint_mutator.h"
#include "pnc_map_service/test/mock_factory.h"

namespace planner {
namespace {

voy::TrackedObject MockTrackedObject(int64_t id, double length, double width,
                                     double height,
                                     voy::perception::ObjectType type) {
  voy::TrackedObject object;
  object.set_id(id);
  object.set_length(length);
  object.set_width(width);
  object.set_height(height);
  object.set_object_type(type);
  return object;
}

vehicle_model::pb::AxleRectangularMeasurement MockEgoShape() {
  vehicle_model::pb::AxleRectangularMeasurement ego_shape;
  ego_shape.set_front_bumper_to_front_axle(0.8826);
  ego_shape.set_rear_bumper_to_rear_axle(1.0867);
  ego_shape.set_width(1.958);
  ego_shape.set_length(4.953);
  ego_shape.set_geometry_center_to_rear_axle(1.3902);
  return ego_shape;
}

TrafficParticipantPose MockTrafficParticipantPose(
    const voy::TrackedObject& agent_object, double center_x, double center_y,
    double heading, double speed) {
  planner::pb::TrajectoryPose pb_pose;
  pb_pose.set_x_pos(center_x);
  pb_pose.set_y_pos(center_y);
  pb_pose.set_heading(heading);
  pb_pose.set_speed(speed);
  return {agent_object, pb_pose};
}

google::protobuf::RepeatedPtrField<planner::pb::TrajectoryPose>
MockTrajectoryPoses(int num_poses, double start_x, double start_y,
                    double start_heading, double start_speed,
                    double heading_rate, double accel) {
  const double lat_sd_increment_rate = 0.1;
  const double long_sd_increment_rate = 0.4;
  google::protobuf::RepeatedPtrField<planner::pb::TrajectoryPose> traj_poses;
  traj_poses.Reserve(num_poses);
  for (int i = 0; i < num_poses; ++i) {
    planner::pb::TrajectoryPose& new_pose = *traj_poses.Add();
    new_pose.set_timestamp(i * constants::kPlanningCycleTimeInMSec);
    new_pose.set_x_pos(start_x);
    new_pose.set_y_pos(start_y);
    if (i > 0) {
      new_pose.set_odom(
          traj_poses.at(i - 1).odom() +
          std::sqrt(math::Sqr(start_x - traj_poses.at(i - 1).x_pos()) +
                    math::Sqr(start_y - traj_poses.at(i - 1).y_pos())));
    }
    new_pose.set_heading(start_heading);
    new_pose.set_speed(start_speed);
    new_pose.mutable_uncertainty()->set_lat_sd(
        lat_sd_increment_rate * constants::kTrajectoryIntervalInSec * i);
    new_pose.mutable_uncertainty()->set_long_sd(
        long_sd_increment_rate * constants::kTrajectoryIntervalInSec * i);
    start_x += start_speed * std::cos(start_heading) *
               constants::kTrajectoryIntervalInSec;
    start_y += start_speed * std::sin(start_heading) *
               constants::kTrajectoryIntervalInSec;
    start_heading += heading_rate * constants::kTrajectoryIntervalInSec;
    start_speed += accel * constants::kTrajectoryIntervalInSec;
  }
  return traj_poses;
}

prediction::pb::PredictedTrajectory MockPredictedTrajectory(
    planner::PredictionId bp_id, const math::geometry::Point2d& start,
    const math::geometry::Point2d& speed_direction, double speed) {
  prediction::pb::PredictedTrajectory prediction{};
  prediction.set_id(bp_id);
  prediction.mutable_traj_poses()->CopyFrom(MockTrajectoryPoses(
      80, start.x(), start.y(),
      std::atan2(speed_direction.y(), speed_direction.x()), speed, 0.0, 0.0));
  return prediction;
}

hdmap::Lane MockPbLane(int64_t id, hdmap::Lane::LaneType type,
                       const math::geometry::Polyline2d& polyline) {
  hdmap::Lane lane;
  lane.set_id(id);
  lane.set_type(type);
  for (const math::geometry::Point2d pt : polyline) {
    hdmap::Point* point =
        lane.mutable_assistant_line()->mutable_points()->Add();
    point->set_x(pt.x());
    point->set_y(pt.y());
  }
  return lane;
}

hdmap::LaneMarking MockPbLaneMarking(
    const math::geometry::Polyline2d& polyline,
    hdmap::LaneMarking::Attribute::Type marking_type =
        hdmap::LaneMarking::Attribute::UNKNOWN_TYPE) {
  hdmap::LaneMarking lane_marking{};
  hdmap::LaneMarking::Attribute* attribute =
      lane_marking.mutable_attrs()->Add();
  attribute->set_type(marking_type);
  for (const math::geometry::Point2d pt : polyline) {
    hdmap::Point* point = lane_marking.mutable_line()->mutable_points()->Add();
    point->set_x(pt.x());
    point->set_y(pt.y());
  }
  return lane_marking;
}

class RiskLibraryTestFixture : public testing::Test {
 protected:
  RiskLibraryTestFixture() : nominal_path_{{0.0, 0.0}, {0.0, 1.0}, {0.0, 2.0}} {
    const int64_t agent_id = 17985;
    const int32_t trajectory_id = 79;
    agent_proto_.mutable_tracked_object()->CopyFrom(MockTrackedObject(
        agent_id, 5.0, 2.0, 1.8, voy::perception::ObjectType::VEHICLE));
    agent_pb_bp_.set_id(trajectory_id);
    agent_bp_ = std::make_unique<PredictedTrajectoryWrapper>(
        agent_proto_.tracked_object(), agent_pb_bp_);
    *trajectory_.mutable_poses() =
        MockTrajectoryPoses(80, 0.0, 0.0, 0.0, 1.0, 0.0, 1.0);
    geo_centered_traj_ =
        std::make_shared<PredictedTrajectoryInterpolator>(trajectory_);
    ego_shape_ = MockEgoShape();
    google::protobuf::RepeatedPtrField<speed::pb::ReasoningObjectDebug>*
        reasoning_objects_debug =
            intention_debug_.mutable_speed_generator_debug()
                ->mutable_speed_reasoning_debug()
                ->mutable_input_debug()
                ->mutable_reasoning_objects();
    speed::pb::ReasoningObjectDebug* object_debug =
        reasoning_objects_debug->Add();
    object_debug->set_object_id(agent_id);
    speed::pb::AgentTrajectoryInfoDebug* traj_info_debug =
        object_debug->mutable_trajectory_infos()->Add();
    traj_info_debug->set_trajectory_id(trajectory_id);
    traj_info_debug->set_road_precedence(planner::pb::RoadPrecedence::HIGHER);
  }

  planner::pb::DecoupledManeuverSeed seed_{};
  prediction::pb::Agent agent_proto_;
  prediction::pb::PredictedTrajectory agent_pb_bp_{};
  std::unique_ptr<PredictedTrajectoryWrapper> agent_bp_{};
  planner::pb::Trajectory trajectory_{};
  std::shared_ptr<PredictedTrajectoryInterpolator> geo_centered_traj_;
  std::vector<speed::Constraint> constraints_{};
  std::vector<speed::Decision> decisions_{};
  vehicle_model::pb::AxleRectangularMeasurement ego_shape_{};
  math::geometry::PolylineCurve2d nominal_path_{};
  planner::pb::IntentionPlanDebug intention_debug_{};
  std::string debug_{};
};

}  // namespace

namespace selection {

TEST(RiskAggregationPolicyTest, RequiredYieldBrakeForKeeperEgo) {
  EXPECT_DOUBLE_EQ(ComputeRequiredAccelForOneFrame(
                       /*yield_dist=*/30., /*yield_time=*/2.,
                       /*start_speed=*/10., /*end_speed=*/10.),
                   0.0);
  EXPECT_DOUBLE_EQ(ComputeRequiredAccelForOneFrame(15., 2., 10., 10.), -2.5);
  EXPECT_DOUBLE_EQ(ComputeRequiredAccelForOneFrame(10., 2., 10., 10.), -5.0);
}

TEST_F(RiskLibraryTestFixture, IdentifyAgentsForRiskConstraint) {
  DCHECK(agent_bp_);
  RiskEvalTarget candidate(/*unique_id_in=*/"", trajectory_, geo_centered_traj_,
                           constraints_, decisions_, ego_shape_, nominal_path_,
                           1.0, planner::pb::BehaviorType::CROSS_LANE, nullptr,
                           &intention_debug_, false, nullptr, nullptr, nullptr,
                           planner::pb::SelectionScenarioType::SCENARIO_NORMAL);
  const std::unordered_map<ObjectId, PlannerObject> planner_object_map{};
  const absl::flat_hash_map<ObjectId,
                            absl::flat_hash_map<std::string, PredictionInfo>>
      considered_agents_bps{};
  std::string debug{};
  absl::flat_hash_map<ObjectId, AgentInfo> attention_agent_set =
      IdentifyAgentsForRiskConstraint(planner_object_map, considered_agents_bps,
                                      {candidate}, debug);
  EXPECT_TRUE(attention_agent_set.empty());
}

TEST(ComputeAgentRoadPrecedence, FindFirstConflictAgentPose) {
  const int64_t plan_init = 3700;
  const int64_t agent1_id = 13967;
  const int64_t agent2_id = 13989;
  const int64_t agent3_id = 14011;
  voy::TrackedObject agent1_object = MockTrackedObject(
      agent1_id, 5.0, 2.0, 1.8, voy::perception::ObjectType::VEHICLE);
  voy::TrackedObject agent2_object = MockTrackedObject(
      agent2_id, 5.0, 2.0, 1.8, voy::perception::ObjectType::VEHICLE);
  voy::TrackedObject agent3_object = MockTrackedObject(
      agent3_id, 5.0, 2.0, 1.8, voy::perception::ObjectType::VEHICLE);
  PredictionId bp_id = 1;
  prediction::pb::PredictedTrajectory dummy_pb_bp{};
  prediction::pb::PredictedTrajectory valid_pb_bp{};
  dummy_pb_bp.set_id(bp_id);
  valid_pb_bp.set_id(bp_id);
  valid_pb_bp.mutable_traj_poses()->Reserve(
      planner::constants::kNumPlanningCycles);
  for (int i = 0; i < planner::constants::kNumPlanningCycles; ++i) {
    planner::pb::TrajectoryPose* new_pose =
        valid_pb_bp.mutable_traj_poses()->Add();
    new_pose->set_timestamp(plan_init +
                            i * planner::constants::kPlanningCycleTimeInMSec);
  }
  PredictedTrajectoryWrapper agent1_bp{agent1_object, dummy_pb_bp};
  PredictedTrajectoryWrapper agent2_bp{agent2_object, dummy_pb_bp};
  PredictedTrajectoryWrapper agent3_bp{agent3_object, valid_pb_bp};

  std::map<ObjectId, std::vector<speed::pb::OverlapRegion>>
      object_overlap_region_map;
  object_overlap_region_map[agent1_id].resize(1);
  object_overlap_region_map[agent1_id].front().set_trajectory_id(bp_id);
  google::protobuf::RepeatedPtrField<speed::pb::OverlapSlice>*
      mutable_overlap_slices =
          object_overlap_region_map[agent1_id].front().mutable_overlap_slices();
  mutable_overlap_slices->Reserve(planner::constants::kNumPlanningCycles);
  for (int i = 0; i < planner::constants::kNumPlanningCycles; ++i) {
    speed::pb::OverlapSlice* new_slice = mutable_overlap_slices->Add();
    new_slice->set_relative_time_in_msec(
        i * planner::constants::kPlanningCycleTimeInMSec);
    new_slice->set_signed_lateral_gap(
        std::abs(i - planner::constants::kNumPlanningCycles / 2));
  }
  object_overlap_region_map[agent3_id] = object_overlap_region_map[agent1_id];
  std::string debug{};

  // Empty prediction poses.
  std::optional<TrafficParticipantPose> agent1_conflict_pose =
      FindFirstConflictAgentPose(object_overlap_region_map, plan_init,
                                 agent1_bp, &debug);
  EXPECT_FALSE(agent1_conflict_pose.has_value());

  // Agent not in object_overlap_region_map.
  std::optional<TrafficParticipantPose> agent2_conflict_pose =
      FindFirstConflictAgentPose(object_overlap_region_map, plan_init,
                                 agent2_bp, &debug);
  EXPECT_FALSE(agent2_conflict_pose.has_value());

  // Agent in map w/ valid conflict pose.
  std::optional<TrafficParticipantPose> agent3_conflict_pose =
      FindFirstConflictAgentPose(object_overlap_region_map, plan_init,
                                 agent3_bp, &debug);
  EXPECT_TRUE(agent3_conflict_pose.has_value());
  EXPECT_TRUE(agent3_conflict_pose->timestamp() ==
              plan_init + (planner::constants::kNumPlanningCycles / 2) *
                              planner::constants::kPlanningCycleTimeInMSec);
}

TEST(ComputeAgentRoadPrecedence, FindEgoAgentConflictPoint) {
  adv_geom::Pose2dWithJuke ego_pose1{
      0.0, math::geometry::Point2d{0.0, 0.0}, M_PI_2, 0.0, 0.0, 0.0};
  voy::TrackedObject agent_object =
      MockTrackedObject(0, 5.0, 2.0, 1.8, voy::perception::ObjectType::VEHICLE);
  std::string debug{};

  TrafficParticipantPose agent_pose1 =
      MockTrafficParticipantPose(agent_object, 2.0, 0.0, M_PI_2, 10);
  std::optional<math::geometry::Point2d> conflict_pt1 =
      FindEgoAgentConflictPoint(ego_pose1, agent_pose1, &debug);
  EXPECT_FALSE(conflict_pt1.has_value());

  TrafficParticipantPose agent_pose2 =
      MockTrafficParticipantPose(agent_object, 2.0, 0.0, M_PI_4, 10);
  std::optional<math::geometry::Point2d> conflict_pt2 =
      FindEgoAgentConflictPoint(ego_pose1, agent_pose2, &debug);
  EXPECT_FALSE(conflict_pt2.has_value());

  TrafficParticipantPose agent_pose3 =
      MockTrafficParticipantPose(agent_object, -2.0, 0.0, M_PI_4, 10);
  std::optional<math::geometry::Point2d> conflict_pt3 =
      FindEgoAgentConflictPoint(ego_pose1, agent_pose3, &debug);
  EXPECT_TRUE(conflict_pt3.has_value());
  EXPECT_TRUE(math::IsApprox(conflict_pt3->x(), 0.0) &&
              math::IsApprox(conflict_pt3->y(), 2.0));

  TrafficParticipantPose agent_pose4 =
      MockTrafficParticipantPose(agent_object, -2.0, 0.0, M_PI / 3, 10);
  std::optional<math::geometry::Point2d> conflict_pt4 =
      FindEgoAgentConflictPoint(ego_pose1, agent_pose4, &debug);
  EXPECT_TRUE(conflict_pt4.has_value());
  EXPECT_TRUE(math::IsApprox(conflict_pt4->x(), 0.0) &&
              math::IsApprox(conflict_pt4->y(), 2 * std::tan(M_PI / 3)));

  adv_geom::Pose2dWithJuke ego_pose2{
      0.0, math::geometry::Point2d{1.0, 1.0}, M_PI * 0.75, 0.0, 0.0, 0.0};
  TrafficParticipantPose agent_pose5 =
      MockTrafficParticipantPose(agent_object, -2.0, 2.0, M_PI_4, 10);
  std::optional<math::geometry::Point2d> conflict_pt5 =
      FindEgoAgentConflictPoint(ego_pose2, agent_pose5, &debug);
  EXPECT_TRUE(conflict_pt5.has_value());
  EXPECT_TRUE(math::IsApprox(conflict_pt5->x(), -1.0) &&
              math::IsApprox(conflict_pt5->y(), 3.0));
}

TEST(ComputeAgentRoadPrecedence, FindCompetingLaneInfo) {
  hdmap::Lane pb_lane0 = MockPbLane(
      0, hdmap::Lane::REGULAR,
      math::geometry::Polyline2d{{0.0, -2.0}, {0.0, -1.0}, {0.0, 0.0}});
  hdmap::Lane pb_lane1 = MockPbLane(
      1, hdmap::Lane::VIRTUAL,
      math::geometry::Polyline2d{{0.0, 0.0}, {0.0, 1.0}, {0.0, 2.0}});
  hdmap::Lane pb_lane2 = MockPbLane(
      2, hdmap::Lane::REGULAR,
      math::geometry::Polyline2d{{0.0, 2.0}, {0.0, 3.0}, {0.0, 4.0}});
  hdmap::Lane pb_lane3 = MockPbLane(
      3, hdmap::Lane::REGULAR,
      math::geometry::Polyline2d{{0.0, 4.0}, {-1.0, 5.0}, {-2.0, 6.0}});
  hdmap::Lane pb_lane4 = MockPbLane(
      4, hdmap::Lane::REGULAR,
      math::geometry::Polyline2d{{0.0, 4.0}, {1.0, 5.0}, {2.0, 6.0}});
  hdmap::LaneMarking pb_lane0_left_marking = MockPbLaneMarking(
      math::geometry::Polyline2d{{-1.0, -2.0}, {-1.0, -1.0}, {-1.0, 0.0}});
  hdmap::LaneMarking pb_lane0_right_marking = MockPbLaneMarking(
      math::geometry::Polyline2d{{1.0, -2.0}, {1.0, -1.0}, {1.0, 0.0}});
  hdmap::LaneMarking pb_lane1_left_marking = MockPbLaneMarking(
      math::geometry::Polyline2d{{-1.0, 0.0}, {-1.0, 1.0}, {-1.0, 2.0}});
  hdmap::LaneMarking pb_lane1_right_marking = MockPbLaneMarking(
      math::geometry::Polyline2d{{1.0, 0.0}, {1.0, 1.0}, {1.0, 2.0}});
  hdmap::LaneMarking pb_lane2_left_marking = MockPbLaneMarking(
      math::geometry::Polyline2d{{-1.0, 2.0}, {-1.0, 3.0}, {-1.0, 4.0}});
  hdmap::LaneMarking pb_lane2_right_marking = MockPbLaneMarking(
      math::geometry::Polyline2d{{1.0, 2.0}, {1.0, 3.0}, {1.0, 4.0}});
  hdmap::LaneMarking pb_lane3_left_marking = MockPbLaneMarking(
      math::geometry::Polyline2d{{-1.0, 4.0}, {-2.0, 5.0}, {-3.0, 6.0}});
  hdmap::LaneMarking pb_lane3_right_marking = MockPbLaneMarking(
      math::geometry::Polyline2d{{1.0, 4.0}, {0.0, 5.0}, {-1.0, 6.0}});
  hdmap::LaneMarking pb_lane4_left_marking = MockPbLaneMarking(
      math::geometry::Polyline2d{{-1.0, 4.0}, {0.0, 5.0}, {1.0, 6.0}});
  hdmap::LaneMarking pb_lane4_right_marking = MockPbLaneMarking(
      math::geometry::Polyline2d{{1.0, 4.0}, {2.0, 5.0}, {3.0, 6.0}});

  pnc_map::LaneMarking lane0_left_marking(&pb_lane0_left_marking);
  pnc_map::LaneMarking lane0_right_marking(&pb_lane0_right_marking);
  pnc_map::LaneMarking lane1_left_marking(&pb_lane1_left_marking);
  pnc_map::LaneMarking lane1_right_marking(&pb_lane1_right_marking);
  pnc_map::LaneMarking lane2_left_marking(&pb_lane2_left_marking);
  pnc_map::LaneMarking lane2_right_marking(&pb_lane2_right_marking);
  pnc_map::LaneMarking lane3_left_marking(&pb_lane3_left_marking);
  pnc_map::LaneMarking lane3_right_marking(&pb_lane3_right_marking);
  pnc_map::LaneMarking lane4_left_marking(&pb_lane4_left_marking);
  pnc_map::LaneMarking lane4_right_marking(&pb_lane4_right_marking);
  pnc_map::Lane lane0(&pb_lane0, &lane0_left_marking, &lane0_right_marking);
  pnc_map::Lane lane1(&pb_lane1, &lane1_left_marking, &lane1_right_marking);
  pnc_map::Lane lane2(&pb_lane2, &lane2_left_marking, &lane2_right_marking);
  pnc_map::Lane lane3(&pb_lane3, &lane3_left_marking, &lane3_right_marking);
  pnc_map::Lane lane4(&pb_lane4, &lane4_left_marking, &lane4_right_marking);
  lane0.set_successors(std::vector<const pnc_map::Lane*>{&lane1});
  lane1.set_successors(std::vector<const pnc_map::Lane*>{&lane2});
  lane2.set_successors(std::vector<const pnc_map::Lane*>{&lane3, &lane4});

  std::string debug{};
  std::vector<const pnc_map::Lane*> ego_lane_sequence{&lane0, &lane1, &lane2};
  math::geometry::Point2d conflict_pt1{0.0, -3.0};
  CompetingLaneInfo competing_lane1 =
      FindCompetingLaneInfo(ego_lane_sequence, conflict_pt1, &debug);
  EXPECT_TRUE(competing_lane1.competing_lane == nullptr);

  math::geometry::Point2d conflict_pt2{0.0, -1.0};
  CompetingLaneInfo competing_lane2 =
      FindCompetingLaneInfo(ego_lane_sequence, conflict_pt2, &debug);
  EXPECT_TRUE(competing_lane2.competing_lane != nullptr);
  EXPECT_EQ(competing_lane2.competing_lane->id(), 0);

  math::geometry::Point2d conflict_pt3{0.0, 1.0};
  CompetingLaneInfo competing_lane3 =
      FindCompetingLaneInfo(ego_lane_sequence, conflict_pt3, &debug);
  EXPECT_TRUE(competing_lane3.competing_lane != nullptr);
  EXPECT_EQ(competing_lane3.competing_lane->id(), 2);
}

TEST_F(RiskLibraryTestFixture, ComputeCalibratedAgentRoadPrecedence) {
  hdmap::Lane pb_current_lane = MockPbLane(
      0, hdmap::Lane::REGULAR,
      math::geometry::Polyline2d{{-80.0, 0.0}, {0.0, 0.0}, {80.0, 0.0}});
  hdmap::LaneMarking pb_current_lane_left_marking = MockPbLaneMarking(
      math::geometry::Polyline2d{{-80.0, 2.0}, {0.0, 2.0}, {80.0, 2.0}});
  hdmap::LaneMarking pb_current_lane_right_marking = MockPbLaneMarking(
      math::geometry::Polyline2d{{-80.0, -2.0}, {0.0, -2.0}, {80.0, -2.0}});
  pnc_map::LaneMarking current_lane_left_marking(&pb_current_lane_left_marking);
  pnc_map::LaneMarking current_lane_right_marking(
      &pb_current_lane_right_marking);
  pnc_map::Lane current_lane(&pb_current_lane, &current_lane_left_marking,
                             &current_lane_right_marking);
  LaneSequenceResult lane_seq_res;
  lane_seq_res.current_lane = &current_lane;

  prediction::pb::PredictedTrajectory agent_pb_bp{};
  agent_pb_bp.set_id(1);
  const double agent_speed = 6.0;  // m/s
  *agent_pb_bp.mutable_traj_poses() = MockTrajectoryPoses(
      80,
      0.25 * (agent_proto_.tracked_object().width() -
              std::sqrt(3) * agent_proto_.tracked_object().length()),
      -4.0 - 0.5 * agent_proto_.tracked_object().width() / std::sqrt(3) -
          0.25 * agent_proto_.tracked_object().length(),
      M_PI / 6.0, 6.0, 0.0, 0.0);
  PredictedTrajectoryWrapper agent_bp(agent_proto_.tracked_object(),
                                      agent_pb_bp);
  std::map<ObjectId, std::vector<speed::pb::OverlapRegion>>
      object_overlap_region_map;
  object_overlap_region_map[agent_bp.object_id()] =
      std::vector<speed::pb::OverlapRegion>(1);
  object_overlap_region_map[agent_bp.object_id()][0].set_trajectory_id(
      agent_bp.id());
  for (int i = 0; i < agent_bp.size(); ++i) {
    speed::pb::OverlapSlice* new_slice =
        object_overlap_region_map[agent_bp.object_id()][0].add_overlap_slices();
    new_slice->set_relative_time_in_msec(i *
                                         constants::kPlanningCycleTimeInMSec);
    const double travel_dist_along_dir =
        i * constants::kTrajectoryIntervalInSec * agent_speed;
    if (travel_dist_along_dir <= 6.0) {
      new_slice->set_signed_lateral_gap(travel_dist_along_dir * 0.5 - 3.0);
    } else if (travel_dist_along_dir > 15.0 + 2 * std::sqrt(3.0)) {
      new_slice->set_signed_lateral_gap(travel_dist_along_dir * 0.5 - 7.5 -
                                        std::sqrt(3.0));
    } else {
      new_slice->set_signed_lateral_gap(0.0);
    }
  }

  math::geometry::PolylineCurve2d path{{0.0, 0.0}, {80.0, 0.0}};
  planner::pb::Path pb_path;
  for (const math::geometry::Point2d& pt : path) {
    planner::pb::PathPose* pose = pb_path.add_poses();
    pose->set_x_pos(pt.x());
    pose->set_y_pos(pt.y());
  }
  RiskEvalTarget candidate(
      /*unique_id_in=*/"", trajectory_, geo_centered_traj_, constraints_,
      decisions_, ego_shape_, nominal_path_, 1.0,
      planner::pb::BehaviorType::CROSS_LANE, &lane_seq_res, &intention_debug_,
      /*consider_speed_change_in=*/false,
      std::make_shared<planner::pb::Path>(pb_path),
      /*trajectory_info_in=*/nullptr,
      std::make_shared<
          std::map<ObjectId, std::vector<speed::pb::OverlapRegion>>>(
          object_overlap_region_map),
      planner::pb::SelectionScenarioType::SCENARIO_NORMAL);

  std::string debug{};
  // Agent cutin ego in ego's current regular lane. Expect to skip calibration.
  EXPECT_EQ(ComputeCalibratedAgentRoadPrecedence(candidate, agent_bp, &debug),
            std::nullopt);
}

TEST_F(RiskLibraryTestFixture, GetAgentRoadPrecedenceForLcNonTail) {
  DCHECK(agent_bp_);
  RiskEvalTarget candidate(/*unique_id_in=*/"", trajectory_, geo_centered_traj_,
                           constraints_, decisions_, ego_shape_, nominal_path_,
                           1.0, planner::pb::BehaviorType::CROSS_LANE, nullptr,
                           &intention_debug_, false, nullptr, nullptr, nullptr,
                           planner::pb::SelectionScenarioType::SCENARIO_NORMAL);
  std::optional<planner::pb::RoadPrecedence::Enum> agent_road_precedence =
      GetAgentRoadPrecedenceForLcNonTail(seed_, candidate, *agent_bp_);
  EXPECT_EQ(agent_road_precedence, std::nullopt);
}

TEST_F(RiskLibraryTestFixture, ComputeAgentRoadPrecedence) {
  DCHECK(agent_bp_);
  hdmap::Lane pb_current_lane = MockPbLane(
      0, hdmap::Lane::REGULAR,
      math::geometry::Polyline2d{{-80.0, 0.0}, {0.0, 0.0}, {80.0, 0.0}});
  hdmap::LaneMarking pb_current_lane_left_marking = MockPbLaneMarking(
      math::geometry::Polyline2d{{-80.0, -2.0}, {0.0, -2.0}, {80.0, -2.0}});
  hdmap::LaneMarking pb_current_lane_right_marking = MockPbLaneMarking(
      math::geometry::Polyline2d{{-80.0, 2.0}, {0.0, 2.0}, {80.0, 2.0}});
  pnc_map::LaneMarking current_lane_left_marking(&pb_current_lane_left_marking);
  pnc_map::LaneMarking current_lane_right_marking(
      &pb_current_lane_right_marking);
  pnc_map::Lane current_lane(&pb_current_lane, &current_lane_left_marking,
                             &current_lane_right_marking);
  LaneSequenceResult lane_seq_res;
  lane_seq_res.current_lane = &current_lane;
  RiskEvalTarget candidate(/*unique_id_in=*/"", trajectory_, geo_centered_traj_,
                           constraints_, decisions_, ego_shape_, nominal_path_,
                           1.0, planner::pb::BehaviorType::LANE_KEEP,
                           &lane_seq_res, &intention_debug_, false, nullptr,
                           nullptr, nullptr,
                           planner::pb::SelectionScenarioType::SCENARIO_NORMAL);
  planner::pb::RoadPrecedence::Enum speed_agent_road_precedence =
      GetAgentRoadPrecedenceInSpeed(candidate, agent_bp_->object_id(),
                                    agent_bp_->id());
  planner::pb::RoadPrecedence::Enum agent_road_precedence =
      ComputeAgentRoadPrecedence(seed_, candidate, *agent_bp_,
                                 speed_agent_road_precedence, &debug_);
  EXPECT_EQ(agent_road_precedence, planner::pb::RoadPrecedence::HIGHER);
}

TEST(ComputeCollisionRiskOnAgentBp, ComputeDistanceTotalErrorSd) {
  const double lat_error = 0.1;
  const double long_error = 0.2;
  const double ego_init_heading = M_PI_2;
  const double ego_curr_heading = ego_init_heading;
  const double agent_init_heading = ego_init_heading;
  const PositionErrorInfo ego_localization_error(ego_init_heading, lat_error,
                                                 long_error);
  const PositionErrorInfo ego_control_error(ego_curr_heading, lat_error,
                                            long_error);
  const PositionErrorInfo agent_tracking_error(agent_init_heading, lat_error,
                                               long_error);

  voy::TrackedObject ego_object =
      MockTrackedObject(0, 5.0, 2.0, 1.8, voy::perception::ObjectType::VEHICLE);
  voy::TrackedObject agent_object =
      MockTrackedObject(1, 5.0, 2.0, 1.8, voy::perception::ObjectType::VEHICLE);
  TrafficParticipantPose ego_init_pose =
      MockTrafficParticipantPose(ego_object, 0.0, 0.0, ego_init_heading, 6.0);
  TrafficParticipantPose agent_init_pose = MockTrafficParticipantPose(
      agent_object, 3.0, 0.0, agent_init_heading, 6.0);
  TrafficParticipantPose ego_curr_pose =
      MockTrafficParticipantPose(ego_object, 0.0, 12.0, ego_curr_heading, 6.0);

  // Ego and agent positions are parallel for both initial and current.
  const double agent_curr_heading1 = M_PI_2;
  const PositionErrorInfo agent_prediction_error1(agent_curr_heading1,
                                                  lat_error, long_error);
  TrafficParticipantPose agent_curr_pose1 = MockTrafficParticipantPose(
      agent_object, 3.0, 12.0, agent_curr_heading1, 6.0);
  PositionErrorInfo total_dist_error1 = ComputeDistanceTotalErrorSd(
      agent_curr_pose1.heading(), ego_localization_error, ego_control_error,
      agent_tracking_error, agent_prediction_error1);
  EXPECT_DOUBLE_EQ(total_dist_error1.lateral_error_sd(), 2 * lat_error);
  EXPECT_DOUBLE_EQ(total_dist_error1.longitudinal_error_sd(), 2 * long_error);

  // Ego and agent positions are parallel initially and orthogonal current.
  const double agent_curr_heading2 = 0.0;
  const PositionErrorInfo agent_prediction_error2(agent_curr_heading2,
                                                  lat_error, long_error);
  TrafficParticipantPose agent_curr_pose2 = MockTrafficParticipantPose(
      agent_object, 3.0, 12.0, agent_curr_heading2, 6.0);
  PositionErrorInfo total_dist_error2 = ComputeDistanceTotalErrorSd(
      agent_curr_pose2.heading(), ego_localization_error, ego_control_error,
      agent_tracking_error, agent_prediction_error2);
  EXPECT_DOUBLE_EQ(total_dist_error2.lateral_error_sd(),
                   std::sqrt(math::Sqr(lat_error) + 3 * math::Sqr(long_error)));
  EXPECT_DOUBLE_EQ(total_dist_error2.longitudinal_error_sd(),
                   std::sqrt(3 * math::Sqr(lat_error) + math::Sqr(long_error)));

  // Ego and agent positions are parallel initially, and heading difference
  // becomes 45 deg current.
  const double agent_curr_heading3 = M_PI_4;
  const PositionErrorInfo agent_prediction_error3(agent_curr_heading3,
                                                  lat_error, long_error);
  TrafficParticipantPose agent_curr_pose3 = MockTrafficParticipantPose(
      agent_object, 3.0, 12.0, agent_curr_heading3, 6.0);
  PositionErrorInfo total_dist_error3 = ComputeDistanceTotalErrorSd(
      agent_curr_pose3.heading(), ego_localization_error, ego_control_error,
      agent_tracking_error, agent_prediction_error3);
  EXPECT_DOUBLE_EQ(
      total_dist_error3.lateral_error_sd(),
      std::sqrt(2.5 * math::Sqr(lat_error) + 1.5 * math::Sqr(long_error)));
  EXPECT_DOUBLE_EQ(
      total_dist_error3.longitudinal_error_sd(),
      std::sqrt(1.5 * math::Sqr(lat_error) + 2.5 * math::Sqr(long_error)));

  // Ego and agent positions are orthogonal initially and current.
  const double agent_init_heading4 = 0.0;
  const double agent_curr_heading4 = 0.0;
  const PositionErrorInfo agent_tracking_error4(agent_init_heading4, lat_error,
                                                long_error);
  const PositionErrorInfo agent_prediction_error4(agent_curr_heading4,
                                                  lat_error, long_error);
  TrafficParticipantPose agent_init_pose1 = MockTrafficParticipantPose(
      agent_object, 5.0, 0.0, agent_init_heading4, 6.0);
  TrafficParticipantPose agent_curr_pose4 = MockTrafficParticipantPose(
      agent_object, 17.0, 0.0, agent_curr_heading4, 6.0);
  PositionErrorInfo total_dist_error4 = ComputeDistanceTotalErrorSd(
      agent_curr_pose4.heading(), ego_localization_error, ego_control_error,
      agent_tracking_error4, agent_prediction_error4);
  EXPECT_DOUBLE_EQ(
      total_dist_error4.lateral_error_sd(),
      std::sqrt(2 * math::Sqr(lat_error) + 2 * math::Sqr(long_error)));
  EXPECT_DOUBLE_EQ(
      total_dist_error4.longitudinal_error_sd(),
      std::sqrt(2 * math::Sqr(lat_error) + 2 * math::Sqr(long_error)));
}

TEST(GeneralizedUkfPolicy, GetPoseRiskWithMaxLikelihoodEstOfTtc) {
  // All invalid poses.
  std::vector<PoseRiskInfo> poses_risk1(planner::constants::kNumPlanningCycles);
  EXPECT_EQ(GetPoseRiskWithMaxLikelihoodEstOfTtc(poses_risk1), nullptr);

  // All valid but non-risky poses.
  std::vector<PoseRiskInfo> poses_risk2(planner::constants::kNumPlanningCycles);
  for (size_t i = 0; i < poses_risk2.size(); ++i) {
    poses_risk2[i].time_after_plan_init_in_sec =
        i * planner::constants::kTrajectoryIntervalInSec;
  }
  EXPECT_EQ(GetPoseRiskWithMaxLikelihoodEstOfTtc(poses_risk2), nullptr);

  // Partially valid w/ constant added collision probability.
  const double const_added_col_prob = 0.01;
  const int start_valid_cycle = 10;
  std::vector<PoseRiskInfo> poses_risk3(planner::constants::kNumPlanningCycles);
  for (size_t i = start_valid_cycle; i < poses_risk3.size(); ++i) {
    poses_risk3[i].time_after_plan_init_in_sec =
        i * planner::constants::kTrajectoryIntervalInSec;
    poses_risk3[i].added_collision_prob = const_added_col_prob;
  }
  const PoseRiskInfo* risk3 = GetPoseRiskWithMaxLikelihoodEstOfTtc(poses_risk3);
  ASSERT_NE(risk3, nullptr);
  EXPECT_DOUBLE_EQ(
      risk3->time_after_plan_init_in_sec,
      start_valid_cycle * planner::constants::kTrajectoryIntervalInSec);

  // All valid w/ varying added collision probability.
  std::vector<PoseRiskInfo> poses_risk4(planner::constants::kNumPlanningCycles);
  for (size_t i = 0; i < poses_risk4.size(); ++i) {
    poses_risk4[i].time_after_plan_init_in_sec =
        i * planner::constants::kTrajectoryIntervalInSec;
    poses_risk4[i].added_collision_prob =
        std::min(i, static_cast<int>(poses_risk4.size()) - i) * 0.001;
  }
  const PoseRiskInfo* risk4 = GetPoseRiskWithMaxLikelihoodEstOfTtc(poses_risk4);
  ASSERT_NE(risk4, nullptr);
  EXPECT_DOUBLE_EQ(risk4->time_after_plan_init_in_sec,
                   static_cast<double>(poses_risk4.size()) / 2 *
                       planner::constants::kTrajectoryIntervalInSec);
}

TEST(GeneralizedUkfPolicy, GetArBpWithUncertainty) {
  planner::pb::DecoupledManeuverSeed decoupled_maneuver_seed{};
  std::unordered_map<ObjectId, PrincipledRequiredLateralGap>
      required_lateral_gap_map{};
  const voy::TrackedObject agent_object =
      MockTrackedObject(0, 5.0, 2.0, 1.8, voy::perception::ObjectType::VEHICLE);
  const math::geometry::Point2d agent_start(0.0, 0.0);
  const math::geometry::Point2d agent_speed_direction(1.0, 0.0);
  const double agent_speed = 5.0;  // m/s
  const planner::PredictionId agent_bp_id = 1.0;
  const prediction::pb::PredictedTrajectory raw_bp_pb = MockPredictedTrajectory(
      agent_bp_id, agent_start, agent_speed_direction, agent_speed);
  const PredictedTrajectoryWrapper raw_bp(agent_object, raw_bp_pb);

  std::unique_ptr<prediction::pb::PredictedTrajectory> ar_bp1 =
      GetArBpWithUncertainty(/*is_ar_bp=*/false, nullptr, raw_bp);
  EXPECT_FALSE(ar_bp1);

  std::unique_ptr<prediction::pb::PredictedTrajectory> ar_bp2 =
      GetArBpWithUncertainty(/*is_ar_bp=*/true, nullptr, raw_bp);
  EXPECT_FALSE(ar_bp2);

  const prediction::pb::PredictedTrajectory& ar_bp_pb3 = raw_bp_pb;
  std::unique_ptr<prediction::pb::PredictedTrajectory> ar_bp3 =
      GetArBpWithUncertainty(/*is_ar_bp=*/false, &ar_bp_pb3, raw_bp);
  EXPECT_FALSE(ar_bp3);

  const prediction::pb::PredictedTrajectory& ar_bp_pb4 = raw_bp_pb;
  std::unique_ptr<prediction::pb::PredictedTrajectory> ar_bp4 =
      GetArBpWithUncertainty(/*is_ar_bp=*/true, &ar_bp_pb4, raw_bp);
  EXPECT_TRUE(ar_bp4);
  EXPECT_EQ(ar_bp4->id(), raw_bp.id());
  EXPECT_EQ(ar_bp4->traj_poses_size(), raw_bp.size());
  for (int ix = 0; ix < raw_bp.size(); ++ix) {
    EXPECT_DOUBLE_EQ(ar_bp4->traj_poses(ix).uncertainty().lat_sd(),
                     raw_bp.pose(ix).uncertainty().lat_sd());
    EXPECT_DOUBLE_EQ(ar_bp4->traj_poses(ix).uncertainty().long_sd(),
                     raw_bp.pose(ix).uncertainty().long_sd());
  }

  const double slow_down_ratio = 0.6;
  const planner::PredictionId agent_ar_bp_id = 3;
  const prediction::pb::PredictedTrajectory ar_bp_pb5 = MockPredictedTrajectory(
      agent_ar_bp_id, agent_start, agent_speed_direction,
      slow_down_ratio * agent_speed);
  std::unique_ptr<prediction::pb::PredictedTrajectory> ar_bp5 =
      GetArBpWithUncertainty(/*is_ar_bp=*/true, &ar_bp_pb5, raw_bp);
  EXPECT_TRUE(ar_bp5);
  EXPECT_EQ(ar_bp5->id(), raw_bp.id());
  EXPECT_EQ(ar_bp5->traj_poses_size(), raw_bp.size());
  for (int ix = 0; ix < raw_bp.size(); ++ix) {
    EXPECT_TRUE(math::IsApprox(
        ar_bp5->traj_poses(ix).uncertainty().lat_sd(),
        raw_bp.pose(ix).uncertainty().lat_sd() * slow_down_ratio));
    EXPECT_TRUE(math::IsApprox(
        ar_bp5->traj_poses(ix).uncertainty().lat_sd(),
        raw_bp.pose(ix).uncertainty().lat_sd() * slow_down_ratio));
  }
}

TEST(GeneralizedUkfPolicy, ComputeAgentCornersToEgoFrontSideDistVector) {
  voy::TrackedObject ego_object =
      MockTrackedObject(0, 5.0, 2.0, 1.8, voy::perception::ObjectType::VEHICLE);
  voy::TrackedObject agent_object =
      MockTrackedObject(1, 5.0, 2.0, 1.8, voy::perception::ObjectType::VEHICLE);
  TrafficParticipantPose ego_pose =
      MockTrafficParticipantPose(ego_object, 0.0, 0.0, M_PI_2, 6.0);
  // At ego's upper right.
  TrafficParticipantPose agent_pose1 =
      MockTrafficParticipantPose(agent_object, 3.0, 5.1, M_PI_2, 6.0);
  EXPECT_FALSE(
      ComputeAgentCornersToEgoFrontSideDistVector(agent_pose1, ego_pose));

  // At ego's right.
  TrafficParticipantPose agent_pose2 =
      MockTrafficParticipantPose(agent_object, 3.0, -2.5, M_PI_2, 6.0);
  std::optional<math::geometry::Segment2d> dist_vec2 =
      ComputeAgentCornersToEgoFrontSideDistVector(agent_pose2, ego_pose);
  EXPECT_TRUE(dist_vec2);
  EXPECT_TRUE(math::geometry::TolerantEquals(
      dist_vec2->start(), math::geometry::Point2d(2.0, 0.0)));
  EXPECT_TRUE(math::geometry::TolerantEquals(
      dist_vec2->end(), math::geometry::Point2d(1.0, 0.0)));

  // At ego's rear.
  TrafficParticipantPose agent_pose3 =
      MockTrafficParticipantPose(agent_object, 0.8, -5, M_PI_4, 6.0);
  EXPECT_FALSE(math::geometry::Intersects(GetPoseBBox(agent_pose3),
                                          GetPoseBBox(ego_pose)));
  EXPECT_FALSE(
      ComputeAgentCornersToEgoFrontSideDistVector(agent_pose3, ego_pose));

  // At ego's left.
  TrafficParticipantPose agent_pose4 =
      MockTrafficParticipantPose(agent_object, -3.5, 0.0, M_PI / 3, 6.0);
  std::optional<math::geometry::Segment2d> dist_vec4 =
      ComputeAgentCornersToEgoFrontSideDistVector(agent_pose4, ego_pose);
  EXPECT_TRUE(dist_vec4);
  EXPECT_TRUE(math::geometry::TolerantEquals(
      dist_vec4->start(), math::geometry::Point2d(-2.25 + std::sqrt(3) / 2,
                                                  2.5 * sqrt(3) / 2 - 0.5)));
  EXPECT_TRUE(math::geometry::TolerantEquals(
      dist_vec4->end(),
      math::geometry::Point2d(-1.0, 2.5 * sqrt(3) / 2 - 0.5)));

  // At ego's front.
  TrafficParticipantPose agent_pose5 = MockTrafficParticipantPose(
      agent_object, 2.27, 3.77 * std::sqrt(3), M_PI * 4 / 3, 6.0);
  std::optional<math::geometry::Segment2d> dist_vec5 =
      ComputeAgentCornersToEgoFrontSideDistVector(agent_pose5, ego_pose);
  EXPECT_TRUE(dist_vec5);
  EXPECT_TRUE(math::geometry::TolerantEquals(
      dist_vec5->start(),
      math::geometry::Point2d(1.02 - std::sqrt(3) / 2,
                              3.77 * std::sqrt(3) - 2.5 * sqrt(3) / 2 + 0.5)));
  EXPECT_TRUE(math::geometry::TolerantEquals(
      dist_vec5->end(), math::geometry::Point2d(1.02 - std::sqrt(3) / 2, 2.5)));
}

TEST(GeneralizedUkfPolicy, ComputeAgentCornerToEgoFrontSideDistanceInfo) {
  voy::TrackedObject ego_object =
      MockTrackedObject(0, 5.0, 2.0, 1.8, voy::perception::ObjectType::VEHICLE);
  voy::TrackedObject agent_object =
      MockTrackedObject(1, 5.0, 2.0, 1.8, voy::perception::ObjectType::VEHICLE);
  TrafficParticipantPose ego_pose =
      MockTrafficParticipantPose(ego_object, 0.0, 0.0, M_PI_2, 6.0);
  constexpr double kLateralErrorSd = 0.25;
  constexpr double kLongitudinalErrorSd = 0.3;
  constexpr double kErrorSdUpperBound = 0.4;
  // At ego's upper left.
  TrafficParticipantPose agent_pose1 =
      MockTrafficParticipantPose(agent_object, -3.5, 5.05, M_PI_2, 6.0);
  PositionErrorInfo error_info1(agent_pose1.heading(), kLateralErrorSd,
                                kLongitudinalErrorSd);
  PotentialCollisionDistanceInfo dist_info1 =
      ComputeAgentCornerToEgoFrontSideDistanceInfo(
          agent_pose1, ego_pose, error_info1, kErrorSdUpperBound);
  EXPECT_TRUE(std::isinf(dist_info1.Dist()));
  EXPECT_TRUE(std::isinf(dist_info1.DistOverSd()));

  // At ego's left.
  TrafficParticipantPose agent_pose2 =
      MockTrafficParticipantPose(agent_object, -3.5, -2.0, M_PI_2, 6.0);
  PositionErrorInfo error_info2(agent_pose2.heading(), kLateralErrorSd,
                                kLongitudinalErrorSd);
  PotentialCollisionDistanceInfo dist_info2 =
      ComputeAgentCornerToEgoFrontSideDistanceInfo(
          agent_pose2, ego_pose, error_info2, kErrorSdUpperBound);
  EXPECT_DOUBLE_EQ(dist_info2.Dist(), 1.5);
  EXPECT_TRUE(math::NearZero(dist_info2.ObjectToEgoAngle()));
  EXPECT_DOUBLE_EQ(dist_info2.Sd(), kLateralErrorSd);
  EXPECT_DOUBLE_EQ(dist_info2.DistOverSd(), 1.5 / kLateralErrorSd);

  // At ego's rear. Not considered
  TrafficParticipantPose agent_pose3 =
      MockTrafficParticipantPose(agent_object, 0.8, -5, M_PI_4, 6.0);
  PositionErrorInfo error_info3(agent_pose3.heading(), kLateralErrorSd,
                                kLongitudinalErrorSd);
  PotentialCollisionDistanceInfo dist_info3 =
      ComputeAgentCornerToEgoFrontSideDistanceInfo(
          agent_pose3, ego_pose, error_info3, kErrorSdUpperBound);
  EXPECT_TRUE(std::isinf(dist_info3.Dist()));
  EXPECT_TRUE(std::isinf(dist_info3.DistOverSd()));

  // At ego's right. Already hit.
  TrafficParticipantPose agent_pose4 = MockTrafficParticipantPose(
      agent_object, 1.5 + std::sqrt(3) * 1.25, -1.0, M_PI * 5 / 6, 6.0);
  PositionErrorInfo error_info4(agent_pose4.heading(), kLateralErrorSd,
                                kLongitudinalErrorSd);
  PotentialCollisionDistanceInfo dist_info4 =
      ComputeAgentCornerToEgoFrontSideDistanceInfo(
          agent_pose4, ego_pose, error_info4, kErrorSdUpperBound);
  // when two bbox intersects and distance is 0, min_direction_distance is not
  // set as inf.
  EXPECT_TRUE(std::isinf(dist_info4.Dist()));
  EXPECT_TRUE(std::isinf(dist_info4.DistOverSd()));

  // At ego's front.
  TrafficParticipantPose agent_pose5 =
      MockTrafficParticipantPose(agent_object, -2.25 + std::sqrt(3) / 2,
                                 5 + 1.25 * std::sqrt(3), -M_PI / 3, 6.0);
  PositionErrorInfo error_info5(agent_pose5.heading(), kLateralErrorSd,
                                kLongitudinalErrorSd);
  PotentialCollisionDistanceInfo dist_info5 =
      ComputeAgentCornerToEgoFrontSideDistanceInfo(
          agent_pose5, ego_pose, error_info5, kErrorSdUpperBound);
  const double expected_error_sd5 =
      std::sqrt(math::Sqr(kLateralErrorSd * std::sin(M_PI / 6)) +
                math::Sqr(kLongitudinalErrorSd * std::cos(M_PI / 6)));
  EXPECT_DOUBLE_EQ(dist_info5.Dist(), 2.0);
  EXPECT_DOUBLE_EQ(dist_info5.ObjectToEgoAngle(), M_PI + M_PI_2);
  EXPECT_DOUBLE_EQ(dist_info5.Sd(), expected_error_sd5);
  EXPECT_DOUBLE_EQ(dist_info5.DistOverSd(), 2.0 / expected_error_sd5);
}

TEST(RiskArFusionModel, ComputeAttentionFovInfoForLaneEncroach) {
  hdmap::Lane pb_current_lane = MockPbLane(
      0, hdmap::Lane::REGULAR,
      math::geometry::Polyline2d{{2.0, 0.0}, {2.0, 5.0}, {2.0, 10.0}});
  hdmap::LaneMarking pb_current_lane_left_marking = MockPbLaneMarking(
      math::geometry::Polyline2d{{0.0, 0.0}, {0.0, 5.0}, {0.0, 10.0}});
  hdmap::LaneMarking pb_current_lane_right_marking = MockPbLaneMarking(
      math::geometry::Polyline2d{{4.0, 0.0}, {4.0, 5.0}, {4.0, 10.0}});
  pnc_map::LaneMarking current_lane_left_marking(&pb_current_lane_left_marking);
  pnc_map::LaneMarking current_lane_right_marking(
      &pb_current_lane_right_marking);
  pnc_map::Lane current_lane(&pb_current_lane, &current_lane_left_marking,
                             &current_lane_right_marking);
  voy::TrackedObject ego_object =
      MockTrackedObject(0, 5.0, 2.0, 1.8, voy::perception::ObjectType::VEHICLE);
  const TrafficParticipantPose ego_init_pose =
      MockTrafficParticipantPose(ego_object, 2.0, 0.0, M_PI_2, 10);
  const TrafficParticipantPose ego_cross_pose =
      MockTrafficParticipantPose(ego_object, 0.5, 4.0, M_PI * 0.75, 10);
  TrajectoryLaneEncroachingInfo lane_encroach_info{
      .crossed_marking = current_lane.left_marking(),
      .first_cross_bbox = std::make_optional<math::geometry::OrientedBox2d>(
          GetPoseBBox(ego_cross_pose))};
  RiskArFusionModel::AttentionFovInfo attention_fov_info =
      RiskArFusionModel::ComputeAttentionFovInfoForLaneEncroach(
          ego_init_pose, lane_encroach_info);
  EXPECT_DOUBLE_EQ(attention_fov_info.left_mild_attention_angle, 165.0);
  EXPECT_DOUBLE_EQ(attention_fov_info.left_full_attention_angle, 165.0 * 0.9);
  EXPECT_DOUBLE_EQ(attention_fov_info.right_full_attention_angle, -90.0);
  EXPECT_DOUBLE_EQ(attention_fov_info.right_mild_attention_angle, -125.0);
}

TEST(GeneralizedUkfPolicy, NormalCdf) {
  EXPECT_LT(std::abs(NormalCdf(3.9) - 0.99995), 1e-5);
  EXPECT_LT(std::abs(NormalCdf(3.5) - 0.99977), 1e-5);
  EXPECT_LT(std::abs(NormalCdf(3.0) - 0.99865), 1e-5);
  EXPECT_LT(std::abs(NormalCdf(2.5) - 0.99379), 1e-5);
  EXPECT_LT(std::abs(NormalCdf(2.0) - 0.97725), 1e-5);
  EXPECT_LT(std::abs(NormalCdf(1.5) - 0.93319), 1e-5);
  EXPECT_LT(std::abs(NormalCdf(1.0) - 0.84134), 1e-5);
  EXPECT_LT(std::abs(NormalCdf(0.5) - 0.69146), 1e-5);
  EXPECT_LT(std::abs(NormalCdf(0.0) - 0.5), 1e-5);
  EXPECT_LT(std::abs(NormalCdf(0.5) + NormalCdf(-0.5) - 1.0), 1e-5);
  EXPECT_LT(std::abs(NormalCdf(1.0) + NormalCdf(-1.0) - 1.0), 1e-5);
  EXPECT_LT(std::abs(NormalCdf(1.5) + NormalCdf(-1.5) - 1.0), 1e-5);
  EXPECT_LT(std::abs(NormalCdf(2.0) + NormalCdf(-2.0) - 1.0), 1e-5);
  EXPECT_LT(std::abs(NormalCdf(2.5) + NormalCdf(-2.5) - 1.0), 1e-5);
  EXPECT_LT(std::abs(NormalCdf(3.0) + NormalCdf(-3.0) - 1.0), 1e-5);
  EXPECT_LT(std::abs(NormalCdf(3.5) + NormalCdf(-3.5) - 1.0), 1e-5);
  EXPECT_LT(std::abs(NormalCdf(3.9) + NormalCdf(-3.9) - 1.0), 1e-5);
  EXPECT_DOUBLE_EQ(NormalCdf(std::numeric_limits<double>::infinity()), 1.0);
}

TEST(GeneralizedUkfPolicy, ComputeSignedLateralDistance) {
  constexpr double kDefaultSearchRange = 100.0;
  const voy::TrackedObject ego_object =
      MockTrackedObject(0, 5.0, 2.0, 1.8, voy::perception::ObjectType::VEHICLE);
  const voy::TrackedObject agent_object =
      MockTrackedObject(0, 5.0, 2.0, 1.8, voy::perception::ObjectType::VEHICLE);

  const TrafficParticipantPose ego_pose1 =
      MockTrafficParticipantPose(ego_object, 0.0, 0.0, M_PI_2, 0.0);
  const TrafficParticipantPose agent_pose1 =
      MockTrafficParticipantPose(agent_object, 5.0, 0.0, M_PI_2, 0.0);
  const double lateral_dist1 =
      ComputeSignedLateralDistance(kDefaultSearchRange, agent_pose1, ego_pose1);
  const double lateral_dist1_range =
      ComputeSignedLateralDistance(1.0, agent_pose1, ego_pose1);
  EXPECT_DOUBLE_EQ(lateral_dist1, 3.0);
  EXPECT_EQ(lateral_dist1_range, std::numeric_limits<double>::infinity());

  const TrafficParticipantPose agent_pose2 = MockTrafficParticipantPose(
      agent_object, 2.0, -5.0, std::atan2(2.5, -1.0), 0.0);
  const double lateral_dist2 =
      ComputeSignedLateralDistance(kDefaultSearchRange, agent_pose2, ego_pose1);
  EXPECT_EQ(lateral_dist2, std::numeric_limits<double>::infinity());

  const TrafficParticipantPose agent_pose3 =
      MockTrafficParticipantPose(agent_object, 4.0, 5.5, M_PI * 0.75, 0.0);
  const double lateral_dist3 =
      ComputeSignedLateralDistance(kDefaultSearchRange, agent_pose3, ego_pose1);
  const double lateral_dist3_range =
      ComputeSignedLateralDistance(2.0, agent_pose3, ego_pose1);
  EXPECT_DOUBLE_EQ(lateral_dist3, 3.0 * std::sqrt(2) - 1);
  EXPECT_EQ(lateral_dist3_range, std::numeric_limits<double>::infinity());

  const TrafficParticipantPose ego_pose2 =
      MockTrafficParticipantPose(ego_object, 2.0, 2.0, M_PI_2, 0.0);
  const TrafficParticipantPose agent_pose4 = MockTrafficParticipantPose(
      ego_object, 1.0 - 2.0 * std::sqrt(3), 6.5, M_PI / 3, 0.0);
  const double lateral_dist4 =
      ComputeSignedLateralDistance(kDefaultSearchRange, agent_pose4, ego_pose2);
  const double lateral_dist4_range =
      ComputeSignedLateralDistance(2.0, agent_pose4, ego_pose2);
  EXPECT_DOUBLE_EQ(lateral_dist4, -3.0);
  EXPECT_EQ(lateral_dist4_range, std::numeric_limits<double>::infinity());
}

TEST(GeneralizedUkfPolicy, ComputeSignedLongitudinalDistance) {
  constexpr double kDefaultSearchRange = 100.0;
  const voy::TrackedObject ego_object =
      MockTrackedObject(0, 5.0, 2.0, 1.8, voy::perception::ObjectType::VEHICLE);
  const voy::TrackedObject agent_object =
      MockTrackedObject(0, 5.0, 2.0, 1.8, voy::perception::ObjectType::VEHICLE);

  const TrafficParticipantPose ego_pose1 =
      MockTrafficParticipantPose(ego_object, 0.0, 0.0, M_PI_2, 0.0);
  const TrafficParticipantPose agent_pose1 =
      MockTrafficParticipantPose(agent_object, 0.0, 7.0, M_PI_2, 0.0);
  const double longitudinal_dist1 = ComputeSignedLongitudinalDistance(
      kDefaultSearchRange, agent_pose1, ego_pose1);
  const double longitudinal_dist1_range =
      ComputeSignedLongitudinalDistance(1.0, agent_pose1, ego_pose1);
  EXPECT_DOUBLE_EQ(longitudinal_dist1, -2.0);
  EXPECT_EQ(longitudinal_dist1_range, std::numeric_limits<double>::infinity());

  const TrafficParticipantPose agent_pose2 =
      MockTrafficParticipantPose(agent_object, -3.0, 0.0, M_PI_2, 0.0);
  const double longitudinal_dist2 = ComputeSignedLongitudinalDistance(
      kDefaultSearchRange, agent_pose2, ego_pose1);
  EXPECT_EQ(longitudinal_dist2, std::numeric_limits<double>::infinity());

  const TrafficParticipantPose agent_pose3 =
      MockTrafficParticipantPose(agent_object, -4.0, 5.5, M_PI * 0.75, 0.0);
  const double longitudinal_dist3 = ComputeSignedLongitudinalDistance(
      kDefaultSearchRange, agent_pose3, ego_pose1);
  const double longitudinal_dist3_range =
      ComputeSignedLongitudinalDistance(.5, agent_pose3, ego_pose1);
  EXPECT_DOUBLE_EQ(longitudinal_dist3, 2.5 - 3.0 * std::sqrt(2));
  EXPECT_EQ(longitudinal_dist3_range, std::numeric_limits<double>::infinity());

  const TrafficParticipantPose ego_pose2 =
      MockTrafficParticipantPose(ego_object, 2.0, 2.0, M_PI_2, 0.0);
  const TrafficParticipantPose agent_pose4 = MockTrafficParticipantPose(
      ego_object, 3.0 + 2.0 * std::sqrt(3), 6.5, M_PI / 6, 0.0);
  const double longitudinal_dist4 = ComputeSignedLongitudinalDistance(
      kDefaultSearchRange, agent_pose4, ego_pose2);
  const double longitudinal_dist4_range =
      ComputeSignedLongitudinalDistance(1.0, agent_pose4, ego_pose2);
  EXPECT_DOUBLE_EQ(longitudinal_dist4, -1.5);
  EXPECT_EQ(longitudinal_dist4_range, std::numeric_limits<double>::infinity());
}

TEST(GeneralizedUkfPolicy, GetBndPosToVehicleFrameSignal) {
  const voy::TrackedObject ego_object =
      MockTrackedObject(0, 5.0, 2.0, 1.8, voy::perception::ObjectType::VEHICLE);
  const voy::TrackedObject bnd_object = MockTrackedObject(
      0, 0.2, 0.2, 0.15, voy::perception::ObjectType::UNKNOWN);

  // under-chassis-over.
  const TrafficParticipantPose ego_pose1 =
      MockTrafficParticipantPose(ego_object, 0.0, 0.0, 0.0, 0.0);
  const TrafficParticipantPose bnd_pose1 =
      MockTrafficParticipantPose(bnd_object, 0.0, 0.0, 0.0, 0.0);
  const BndRelativeLocation bnd_pos_to_ego_frame_signal1 =
      GetBndPosToVehicleFrameSignal(ego_pose1, bnd_pose1);
  EXPECT_EQ(bnd_pos_to_ego_frame_signal1, BndRelativeLocation::UNDER_CHASSIS);

  // non-overlap.
  const TrafficParticipantPose ego_pose2 =
      MockTrafficParticipantPose(ego_object, 0.0, 0.0, 0.0, 0.0);
  const TrafficParticipantPose bnd_pose2 =
      MockTrafficParticipantPose(bnd_object, 3.0, 0.0, 0.0, 0.0);
  const BndRelativeLocation bnd_pos_to_ego_frame_signal2 =
      GetBndPosToVehicleFrameSignal(ego_pose2, bnd_pose2);
  EXPECT_EQ(bnd_pos_to_ego_frame_signal2,
            BndRelativeLocation::OUTSIDE_EGO_BBOX);

  // tire-over.
  const TrafficParticipantPose ego_pose3 =
      MockTrafficParticipantPose(ego_object, 0.0, 0.0, 0.0, 0.0);
  const TrafficParticipantPose bnd_pose3 =
      MockTrafficParticipantPose(bnd_object, 0.0, 1.0, 0.0, 0.0);
  const BndRelativeLocation bnd_pos_to_ego_frame_signal3 =
      GetBndPosToVehicleFrameSignal(ego_pose3, bnd_pose3);
  EXPECT_EQ(bnd_pos_to_ego_frame_signal3,
            BndRelativeLocation::OVERLAP_TIRE_REGION);
}

TEST_F(RiskLibraryTestFixture, ComputeTrajectoryLaneMarkingCrossInfo) {
  hdmap::Lane pb_current_lane = MockPbLane(
      0, hdmap::Lane::REGULAR,
      math::geometry::Polyline2d{{0.0, -80.0}, {0.0, 0.0}, {0.0, 80.0}});
  hdmap::LaneMarking pb_current_lane_left_marking = MockPbLaneMarking(
      math::geometry::Polyline2d{{-2.0, -80.0}, {-2.0, 0.0}, {-2.0, 80.0}},
      hdmap::LaneMarking::Attribute::BROKEN);
  hdmap::LaneMarking pb_current_lane_right_marking = MockPbLaneMarking(
      math::geometry::Polyline2d{{2.0, -80.0}, {2.0, 0.0}, {2.0, 80.0}},
      hdmap::LaneMarking::Attribute::BROKEN);
  pnc_map::LaneMarking current_lane_left_marking(&pb_current_lane_left_marking);
  pnc_map::LaneMarking current_lane_right_marking(
      &pb_current_lane_right_marking);
  pnc_map::Lane current_lane(&pb_current_lane, &current_lane_left_marking,
                             &current_lane_right_marking);
  vehicle_model::pb::AxleRectangularMeasurement ego_shape;
  ego_shape.set_width(2.0);
  ego_shape.set_length(5.0);

  const google::protobuf::RepeatedPtrField<planner::pb::TrajectoryPose>
      traj_poses1 = MockTrajectoryPoses(80, 0.0, -70.0, M_PI_2, 0.0, 0.0, 1.0);
  const TrajectoryLaneEncroachingInfo encroach_info1 =
      ComputeTrajectoryLaneMarkingCrossInfo(current_lane, ego_shape,
                                            traj_poses1);
  EXPECT_TRUE(encroach_info1.crossed_marking == nullptr);
  EXPECT_TRUE(encroach_info1.target_lane == nullptr);
  EXPECT_FALSE(encroach_info1.first_cross_bbox.has_value());
  EXPECT_FALSE(encroach_info1.first_reverse_cross_bbox.has_value());

  const google::protobuf::RepeatedPtrField<planner::pb::TrajectoryPose>
      traj_poses2 =
          MockTrajectoryPoses(80, 0.0, -70.0, M_PI * 100 / 180, 0.0, 0.0, 1.0);
  const TrajectoryLaneEncroachingInfo encroach_info2 =
      ComputeTrajectoryLaneMarkingCrossInfo(current_lane, ego_shape,
                                            traj_poses2);
  EXPECT_EQ(encroach_info2.crossed_marking, &current_lane_left_marking);
  EXPECT_EQ(encroach_info2.target_lane, nullptr);
  EXPECT_FALSE(encroach_info2.is_first_cross_current);
  EXPECT_FALSE(encroach_info2.is_crossed_marking_virtual);
  EXPECT_TRUE(encroach_info2.first_cross_bbox.has_value());
  EXPECT_FALSE(encroach_info2.first_reverse_cross_bbox.has_value());

  const google::protobuf::RepeatedPtrField<planner::pb::TrajectoryPose>
      traj_poses3 =
          MockTrajectoryPoses(80, 0.0, -70.0, M_PI * 85 / 180, 0.0, 0.0, 1.0);
  const TrajectoryLaneEncroachingInfo encroach_info3 =
      ComputeTrajectoryLaneMarkingCrossInfo(current_lane, ego_shape,
                                            traj_poses3);
  EXPECT_EQ(encroach_info3.crossed_marking, &current_lane_right_marking);
  EXPECT_EQ(encroach_info3.target_lane, nullptr);
  EXPECT_FALSE(encroach_info3.is_first_cross_current);
  EXPECT_FALSE(encroach_info3.is_crossed_marking_virtual);
  EXPECT_TRUE(encroach_info3.first_cross_bbox.has_value());
  EXPECT_FALSE(encroach_info3.first_reverse_cross_bbox.has_value());

  google::protobuf::RepeatedPtrField<planner::pb::TrajectoryPose> traj_poses4 =
      MockTrajectoryPoses(40, 0.0, -70.0, M_PI * 85 / 180, 3.0, 0.0, 0.0);
  google::protobuf::RepeatedPtrField<planner::pb::TrajectoryPose>
      traj_poses4_2nd_half = MockTrajectoryPoses(
          40, 1.0458, -58.0456, M_PI * 95 / 180, 3.0, 0.0, 0.0);
  traj_poses4.MergeFrom(traj_poses4_2nd_half);
  const TrajectoryLaneEncroachingInfo encroach_info4 =
      ComputeTrajectoryLaneMarkingCrossInfo(current_lane, ego_shape,
                                            traj_poses4);
  EXPECT_EQ(encroach_info4.crossed_marking, &current_lane_right_marking);
  EXPECT_EQ(encroach_info4.target_lane, nullptr);
  EXPECT_FALSE(encroach_info4.is_first_cross_current);
  EXPECT_FALSE(encroach_info4.is_crossed_marking_virtual);
  EXPECT_TRUE(encroach_info4.first_cross_bbox.has_value());
  EXPECT_TRUE(encroach_info4.first_reverse_cross_bbox.has_value());
}

TEST(ComputeCollisionLiability, ComputeTrajectoryLaneEncroachingInfo) {
  hdmap::Lane pb_current_lane = MockPbLane(
      0, hdmap::Lane::REGULAR,
      math::geometry::Polyline2d{{0.0, -80.0}, {0.0, 0.0}, {0.0, 80.0}});
  hdmap::LaneMarking pb_current_lane_left_marking = MockPbLaneMarking(
      math::geometry::Polyline2d{{-2.0, -80.0}, {-2.0, 0.0}, {-2.0, 80.0}});
  hdmap::LaneMarking pb_current_lane_right_marking = MockPbLaneMarking(
      math::geometry::Polyline2d{{2.0, -80.0}, {2.0, 0.0}, {2.0, 80.0}});
  pnc_map::LaneMarking current_lane_left_marking(&pb_current_lane_left_marking);
  pnc_map::LaneMarking current_lane_right_marking(
      &pb_current_lane_right_marking);
  pnc_map::Lane current_lane(&pb_current_lane, &current_lane_left_marking,
                             &current_lane_right_marking);
  vehicle_model::pb::AxleRectangularMeasurement ego_shape;
  ego_shape.set_width(2.0);
  ego_shape.set_length(5.0);

  const google::protobuf::RepeatedPtrField<planner::pb::TrajectoryPose>
      traj_poses1 = MockTrajectoryPoses(80, 0.0, -70.0, M_PI_2, 0.0, 0.0, 1.0);
  const TrajectoryLaneEncroachingInfo encroach_info1 =
      ComputeTrajectoryLaneEncroachingInfo(current_lane, ego_shape,
                                           traj_poses1);
  EXPECT_TRUE(encroach_info1.crossed_marking == nullptr);
  EXPECT_TRUE(encroach_info1.target_lane == nullptr);

  const google::protobuf::RepeatedPtrField<planner::pb::TrajectoryPose>
      traj_poses2 =
          MockTrajectoryPoses(80, 0.0, -70.0, M_PI * 100 / 180, 0.0, 0.0, 1.0);
  const TrajectoryLaneEncroachingInfo encroach_info2 =
      ComputeTrajectoryLaneEncroachingInfo(current_lane, ego_shape,
                                           traj_poses2);
  EXPECT_EQ(encroach_info2.crossed_marking, &current_lane_left_marking);
  EXPECT_EQ(encroach_info2.target_lane, nullptr);

  const google::protobuf::RepeatedPtrField<planner::pb::TrajectoryPose>
      traj_poses3 =
          MockTrajectoryPoses(80, 0.0, -70.0, M_PI * 85 / 180, 0.0, 0.0, 1.0);
  const TrajectoryLaneEncroachingInfo encroach_info3 =
      ComputeTrajectoryLaneEncroachingInfo(current_lane, ego_shape,
                                           traj_poses3);
  EXPECT_EQ(encroach_info3.crossed_marking, &current_lane_right_marking);
  EXPECT_EQ(encroach_info3.target_lane, nullptr);

  hdmap::Lane pb_adjacent_right_lane = MockPbLane(
      0, hdmap::Lane::REGULAR,
      math::geometry::Polyline2d{{4.0, -80.0}, {4.0, 0.0}, {4.0, 80.0}});
  hdmap::LaneMarking pb_right_lane_left_marking = MockPbLaneMarking(
      math::geometry::Polyline2d{{2.0, -80.0}, {2.0, 0.0}, {2.0, 80.0}});
  hdmap::LaneMarking pb_right_lane_right_marking = MockPbLaneMarking(
      math::geometry::Polyline2d{{6.0, -80.0}, {6.0, 0.0}, {6.0, 80.0}});
  pnc_map::LaneMarking right_lane_left_marking(&pb_right_lane_left_marking);
  pnc_map::LaneMarking right_lane_right_marking(&pb_right_lane_right_marking);
  pnc_map::Lane adjacent_right_lane(&pb_adjacent_right_lane,
                                    &right_lane_left_marking,
                                    &right_lane_right_marking);
  current_lane.set_adjacent_right_lane(&adjacent_right_lane);

  const google::protobuf::RepeatedPtrField<planner::pb::TrajectoryPose>
      traj_poses4 =
          MockTrajectoryPoses(80, 0.0, -70.0, M_PI * 85 / 180, 0.0, 0.0, 1.0);
  const TrajectoryLaneEncroachingInfo encroach_info4 =
      ComputeTrajectoryLaneEncroachingInfo(current_lane, ego_shape,
                                           traj_poses4);
  EXPECT_EQ(encroach_info4.crossed_marking, &current_lane_right_marking);
  EXPECT_EQ(encroach_info4.target_lane, &adjacent_right_lane);
  EXPECT_EQ(encroach_info4.target_lane_to_cross_marking, math::pb::kRight);
  EXPECT_TRUE(encroach_info4.target_lane_has_marking);
  EXPECT_FALSE(encroach_info4.is_target_lane_oncoming);
  EXPECT_EQ(encroach_info4.entering_side_of_target_lane, math::pb::kLeft);
}

TEST(ComputeCollisionLiability, GetBBoxEnteringLanePercentage) {
  const math::geometry::PolylineCurve2d marking{
      {0.0, -100.0}, {0.0, 0.0}, {0.0, 100.0}};

  const math::geometry::OrientedBox2d pose_bbox1(2.0, 0.0, 5.0, 2.0, M_PI_2);
  const double pose_bbox1_enter_lane_right_percent =
      GetBBoxEnteringLanePercentage(marking, pose_bbox1, math::pb::kRight);
  const double pose_bbox1_enter_lane_left_percent =
      GetBBoxEnteringLanePercentage(marking, pose_bbox1, math::pb::kLeft);
  EXPECT_TRUE(math::IsApprox(pose_bbox1_enter_lane_right_percent, 1.0));
  EXPECT_TRUE(math::IsApprox(pose_bbox1_enter_lane_left_percent, 0.0));

  const math::geometry::OrientedBox2d pose_bbox2(0.0, 0.0, 5.0, 2.0, M_PI_2);
  const double pose_bbox2_enter_lane_right_percent =
      GetBBoxEnteringLanePercentage(marking, pose_bbox2, math::pb::kRight);
  const double pose_bbox2_enter_lane_left_percent =
      GetBBoxEnteringLanePercentage(marking, pose_bbox2, math::pb::kLeft);
  EXPECT_TRUE(math::IsApprox(pose_bbox2_enter_lane_right_percent, 0.5));
  EXPECT_TRUE(math::IsApprox(pose_bbox2_enter_lane_left_percent, 0.5));

  const math::geometry::OrientedBox2d pose_bbox3(-0.5, 0.0, 5.0, 2.0, M_PI_4);
  const double pose_bbox3_enter_lane_right_percent =
      GetBBoxEnteringLanePercentage(marking, pose_bbox3, math::pb::kRight);
  const double pose_bbox3_enter_lane_left_percent =
      GetBBoxEnteringLanePercentage(marking, pose_bbox3, math::pb::kLeft);
  EXPECT_TRUE(math::IsApprox(pose_bbox3_enter_lane_right_percent,
                             (-0.5 + 3.5 / std::sqrt(2)) / (7 / std::sqrt(2))));
  EXPECT_TRUE(math::IsApprox(pose_bbox3_enter_lane_left_percent,
                             (0.5 + 3.5 / std::sqrt(2)) / (7 / std::sqrt(2))));
}

TEST_F(RiskLibraryTestFixture, ComputeCollisionLiability) {
  hdmap::Lane pb_current_lane = MockPbLane(
      0, hdmap::Lane::REGULAR,
      math::geometry::Polyline2d{{-80.0, 0.0}, {0.0, 0.0}, {80.0, 0.0}});
  hdmap::LaneMarking pb_current_lane_left_marking = MockPbLaneMarking(
      math::geometry::Polyline2d{{-80.0, -2.0}, {0.0, -2.0}, {80.0, -2.0}});
  hdmap::LaneMarking pb_current_lane_right_marking = MockPbLaneMarking(
      math::geometry::Polyline2d{{-80.0, 2.0}, {0.0, 2.0}, {80.0, 2.0}});
  pnc_map::LaneMarking current_lane_left_marking(&pb_current_lane_left_marking);
  pnc_map::LaneMarking current_lane_right_marking(
      &pb_current_lane_right_marking);
  pnc_map::Lane current_lane(&pb_current_lane, &current_lane_left_marking,
                             &current_lane_right_marking);
  LaneSequenceResult lane_seq_res;
  lane_seq_res.current_lane = &current_lane;
  RiskEvalTarget candidate(/*unique_id_in=*/"", trajectory_, geo_centered_traj_,
                           constraints_, decisions_, ego_shape_, nominal_path_,
                           1.0, planner::pb::BehaviorType::CROSS_LANE,
                           &lane_seq_res, &intention_debug_, false, nullptr,
                           nullptr, nullptr,
                           planner::pb::SelectionScenarioType::SCENARIO_NORMAL);
  CollisionLiabilityInfo collision_liability_info = ComputeCollisionLiability(
      /*is_in_lane_change_scenario=*/false, candidate, *agent_bp_);
  EXPECT_TRUE(
      math::IsApprox(collision_liability_info.risk_scaler_by_liability, 1.0));
}

TEST_F(RiskLibraryTestFixture,
       ComputeCrossLaneMergeCalibratedAgentPrecedenceByRedundantCheck) {
  hdmap::Lane pb_current_lane = MockPbLane(
      0, hdmap::Lane::REGULAR,
      math::geometry::Polyline2d{{0.0, -80.0}, {0.0, 0.0}, {0.0, 80.0}});
  hdmap::LaneMarking pb_current_lane_left_marking = MockPbLaneMarking(
      math::geometry::Polyline2d{{-2.0, -80.0}, {-2.0, 0.0}, {-2.0, 80.0}});
  hdmap::LaneMarking pb_current_lane_right_marking = MockPbLaneMarking(
      math::geometry::Polyline2d{{2.0, -80.0}, {2.0, 0.0}, {2.0, 80.0}});
  pnc_map::LaneMarking current_lane_left_marking(&pb_current_lane_left_marking);
  pnc_map::LaneMarking current_lane_right_marking(
      &pb_current_lane_right_marking);
  pnc_map::Lane current_lane(&pb_current_lane, &current_lane_left_marking,
                             &current_lane_right_marking);

  const vehicle_model::pb::AxleRectangularMeasurement ego_shape =
      MockEgoShape();
  planner::pb::Trajectory ego_trajectory;
  *ego_trajectory.mutable_poses() = MockTrajectoryPoses(
      80, 1.2, 0.0, math::Degree2Radian(105.0), 0.5, 0.0, 0.0);
  const TrajectoryLaneEncroachingInfo ego_lane_encroach_info =
      ComputeTrajectoryLaneEncroachingInfo(current_lane, ego_shape,
                                           ego_trajectory.poses());
  const pnc_map::Lane& target_lane =
      *DCHECK_NOTNULL(ego_lane_encroach_info.target_lane);
  voy::perception::ObjectType agent_type = voy::perception::ObjectType::VEHICLE;
  const math::geometry::OrientedBox2d& ego_init_bbox =
      GetPoseBBox(ego_shape, ego_trajectory.poses(0));
  const double ego_in_target_lane_percentage = GetBBoxEnteringLanePercentage(
      (ego_lane_encroach_info.entering_side_of_target_lane == math::pb::kLeft)
          ? target_lane.left_marking()->line()
          : target_lane.right_marking()->line(),
      ego_init_bbox, ego_lane_encroach_info.target_lane_to_cross_marking);

  // Ego leads slightly in longitudinal and sufficiently in inlane percentage.
  const math::geometry::OrientedBox2d agent_init_bbox1(
      -3.4, -0.2, 5.0, 2.0, math::Degree2Radian(75.0));
  const double agent_in_target_lane_percentage1 = std::min(
      GetBBoxEnteringLanePercentage(target_lane.left_marking()->line(),
                                    agent_init_bbox1, math::pb::kRight),
      GetBBoxEnteringLanePercentage(target_lane.right_marking()->line(),
                                    agent_init_bbox1, math::pb::kLeft));
  planner::pb::RoadPrecedence::Enum agent_road_precedence1 =
      ComputeCrossLaneMergeCalibratedAgentPrecedenceByRedundantCheck(
          ego_shape, ego_trajectory, ego_lane_encroach_info, agent_type,
          agent_init_bbox1, ego_init_bbox, agent_in_target_lane_percentage1,
          ego_in_target_lane_percentage);
  EXPECT_EQ(agent_road_precedence1, planner::pb::RoadPrecedence::LOWER);

  // Ego trails slightly in longitudinal.
  const math::geometry::OrientedBox2d agent_init_bbox2(
      -3.4, 0.2, 5.0, 2.0, math::Degree2Radian(75.0));
  const double agent_in_target_lane_percentage2 = std::min(
      GetBBoxEnteringLanePercentage(target_lane.left_marking()->line(),
                                    agent_init_bbox2, math::pb::kRight),
      GetBBoxEnteringLanePercentage(target_lane.right_marking()->line(),
                                    agent_init_bbox2, math::pb::kLeft));
  planner::pb::RoadPrecedence::Enum agent_road_precedence2 =
      ComputeCrossLaneMergeCalibratedAgentPrecedenceByRedundantCheck(
          ego_shape, ego_trajectory, ego_lane_encroach_info, agent_type,
          agent_init_bbox2, ego_init_bbox, agent_in_target_lane_percentage2,
          ego_in_target_lane_percentage);
  EXPECT_EQ(agent_road_precedence2, planner::pb::RoadPrecedence::UNKNOWN);

  // Agent is half inside target lane.
  const math::geometry::OrientedBox2d agent_init_bbox3(
      -2.0, -1.0, 5.0, 2.0, math::Degree2Radian(75.0));
  const double agent_in_target_lane_percentage3 = std::min(
      GetBBoxEnteringLanePercentage(target_lane.left_marking()->line(),
                                    agent_init_bbox3, math::pb::kRight),
      GetBBoxEnteringLanePercentage(target_lane.right_marking()->line(),
                                    agent_init_bbox3, math::pb::kLeft));
  planner::pb::RoadPrecedence::Enum agent_road_precedence3 =
      ComputeCrossLaneMergeCalibratedAgentPrecedenceByRedundantCheck(
          ego_shape, ego_trajectory, ego_lane_encroach_info, agent_type,
          agent_init_bbox3, ego_init_bbox, agent_in_target_lane_percentage3,
          ego_in_target_lane_percentage);
  EXPECT_TRUE(math::NearZero(agent_in_target_lane_percentage3 - 0.5, 0.001));
  EXPECT_EQ(agent_road_precedence3, planner::pb::RoadPrecedence::UNKNOWN);
}

TEST_F(RiskLibraryTestFixture, IsNearCrossLaneTailAgentByRedundantCheck) {
  hdmap::Lane pb_current_lane = MockPbLane(
      0, hdmap::Lane::REGULAR,
      math::geometry::Polyline2d{{0.0, -80.0}, {0.0, 0.0}, {0.0, 80.0}});
  hdmap::LaneMarking pb_current_lane_left_marking = MockPbLaneMarking(
      math::geometry::Polyline2d{{-2.0, -80.0}, {-2.0, 0.0}, {-2.0, 80.0}});
  hdmap::LaneMarking pb_current_lane_right_marking = MockPbLaneMarking(
      math::geometry::Polyline2d{{2.0, -80.0}, {2.0, 0.0}, {2.0, 80.0}});
  pnc_map::LaneMarking current_lane_left_marking(&pb_current_lane_left_marking);
  pnc_map::LaneMarking current_lane_right_marking(
      &pb_current_lane_right_marking);
  pnc_map::Lane current_lane(&pb_current_lane, &current_lane_left_marking,
                             &current_lane_right_marking);
  LaneSequenceResult lane_seq_res;
  lane_seq_res.current_lane = &current_lane;
  *trajectory_.mutable_poses() =
      MockTrajectoryPoses(80, -4.0, 0.0, M_PI_4, 1.0, 0.0, 0.0);
  geo_centered_traj_ =
      std::make_shared<PredictedTrajectoryInterpolator>(trajectory_);
  RiskEvalTarget candidate(/*unique_id_in=*/"", trajectory_, geo_centered_traj_,
                           constraints_, decisions_, ego_shape_, nominal_path_,
                           1.0, planner::pb::BehaviorType::LANE_KEEP,
                           &lane_seq_res, &intention_debug_);
  candidate.lane_encroach_info = ComputeTrajectoryLaneEncroachingInfo(
      current_lane, candidate.ego_shape, candidate.trajectory.poses());
  std::string debug{};

  // Ego encroach agent fully inlane.
  PlannerObject agent1(agent_proto_, /*object_timestamp=*/0,
                       MockTrafficParticipantPose(agent_proto_.tracked_object(),
                                                  0.0, -2.5, M_PI_2, 10));
  EXPECT_TRUE(
      IsNearCrossLaneTailAgentByRedundantCheck(agent1, candidate, debug));

  // Ego encroach agent fully out of lane.
  PlannerObject agent2(agent_proto_, /*object_timestamp=*/0,
                       MockTrafficParticipantPose(agent_proto_.tracked_object(),
                                                  2.5, 0.0, M_PI_2, 10));
  EXPECT_FALSE(
      IsNearCrossLaneTailAgentByRedundantCheck(agent2, candidate, debug));
}

TEST_F(RiskLibraryTestFixture, IsCyclistInSmallFovByRedundantCheck) {
  const int64_t agent_id = 17985;
  prediction::pb::Agent cyc_agent;
  cyc_agent.mutable_tracked_object()->CopyFrom(MockTrackedObject(
      agent_id, 5.0, 2.0, 1.8, voy::perception::ObjectType::CYCLIST));
  RiskEvalTarget candidate(
      /*unique_id_in=*/"", trajectory_, geo_centered_traj_, constraints_,
      decisions_, ego_shape_, nominal_path_, 1.0,
      planner::pb::BehaviorType::LANE_KEEP, nullptr, &intention_debug_);
  std::string debug{};

  // Cyc outside of small FoV.
  PlannerObject agent1(cyc_agent, /*object_timestamp=*/0,
                       MockTrafficParticipantPose(cyc_agent.tracked_object(),
                                                  0.0, -2.5, M_PI_2, 10));
  EXPECT_FALSE(IsCyclistInSmallFovByRedundantCheck(agent1, candidate, debug));

  // Cyc inside a small FoV.
  PlannerObject agent2(cyc_agent, /*object_timestamp=*/0,
                       MockTrafficParticipantPose(cyc_agent.tracked_object(),
                                                  2.5, 0.0, M_PI_2, 10));
  EXPECT_TRUE(IsCyclistInSmallFovByRedundantCheck(agent2, candidate, debug));
}

TEST_F(RiskLibraryTestFixture,
       ComputeRiskCheckHorizonForSpeedPartiallyIgnoredBp) {
  RiskEvalTarget candidate(/*unique_id_in=*/"", trajectory_, geo_centered_traj_,
                           constraints_, decisions_, ego_shape_, nominal_path_,
                           1.0, planner::pb::BehaviorType::CROSS_LANE, nullptr,
                           &intention_debug_, false, nullptr, nullptr, nullptr,
                           planner::pb::SelectionScenarioType::SCENARIO_NORMAL);
  RiskCheckHorizonInfo risk_check_horizon_info =
      ComputeRiskCheckHorizonForSpeedPartiallyIgnoredBp(
          *agent_bp_, std::nullopt, candidate);
  EXPECT_TRUE(std::isinf(risk_check_horizon_info.risk_check_horizon));
  EXPECT_TRUE(std::isinf(risk_check_horizon_info.speed_check_horizon));
  EXPECT_TRUE(std::isinf(risk_check_horizon_info.agent_to_lane_dist));
}

TEST(WorstCaseRiskConstraintTest, GenerateConstAccelAndOmegaPrediction) {}

}  // namespace selection
}  // namespace planner
