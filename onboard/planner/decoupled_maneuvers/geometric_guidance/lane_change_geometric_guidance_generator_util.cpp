#include "planner/decoupled_maneuvers/geometric_guidance/lane_change_geometric_guidance_generator_util.h"

#include <algorithm>
#include <cmath>
#include <cstdint>
#include <glog/logging.h>
#include <limits>
#include <optional>
#include <string>
#include <utility>
#include <vector>

#include "log_utils/log_macros.h"
#include "math/constants.h"
#include "math/interpolation.h"
#include "math/math_util.h"
#include "math/range.h"
#include "planner/behavior/util/lane_common/lane_change_instance.h"
#include "planner/decoupled_maneuvers/lane_change/lane_change_common.h"
#include "planner/decoupled_maneuvers/lane_change/lane_change_object_info.h"
#include "planner/planning_gflags.h"
#include "planner/speed/overlap/overlap_lib_util.h"
#include "planner/speed/profile/profile.h"
#include "planner/speed/profile/profile_util.h"
#include "planner/speed/solver/searcher/simple_profile_searcher.h"
#include "planner_protos/behavior_reasoners_debug.pb.h"
#include "planner_protos/speed_seed.pb.h"
#include "voy_rt_event/rt_event_planner.h"

namespace planner {

DrivingCorridor GenerateCorridorForLaneChange(
    const LaneChangeGeometryMetaData& lane_change_geom_meta) {
  const math::geometry::Point2d& cl_start =
      lane_change_geom_meta.cross_lane_start_pt_on_extended_source_lane_seq;

  // Pick the farthest point as the lc end.
  // TODO(lane change): Revisit the logic if we should always use actual cross
  // lane end pt to construct boundary.
  const math::geometry::Point2d& latest_cl_end =
      lane_change_geom_meta
                  .actual_cross_lane_end_arc_length_on_target_lane_seq >
              lane_change_geom_meta
                  .latest_cross_lane_end_arc_length_on_source_lane_seq
          ? lane_change_geom_meta.actual_cross_lane_end_pt_on_target_lane_seq
          : lane_change_geom_meta.latest_cross_lane_end_pt_on_source_lane_seq;
  const math::geometry::PolylineCurve2d& source_left_marking =
      lane_change_geom_meta.base_meta_data.extended_source_curve_left_boundary;
  const math::geometry::PolylineCurve2d& source_right_marking =
      lane_change_geom_meta.base_meta_data.extended_source_curve_right_boundary;
  const math::geometry::PolylineCurve2d& target_left_marking =
      lane_change_geom_meta.base_meta_data.target_curve_left_boundary;
  const math::geometry::PolylineCurve2d& target_right_marking =
      lane_change_geom_meta.base_meta_data.target_curve_right_boundary;

  const bool is_left_cross_lane =
      lane_change_geom_meta.base_meta_data.cross_lane_direction ==
      pb::LaneChangeMode::LEFT_LANE_CHANGE;
  DrivingCorridor ret;
  ret.left_boundary =
      StitchCurvesAtDivisionPt(source_left_marking, target_left_marking,
                               is_left_cross_lane ? cl_start : latest_cl_end);
  ret.right_boundary =
      StitchCurvesAtDivisionPt(source_right_marking, target_right_marking,
                               is_left_cross_lane ? latest_cl_end : cl_start);

  return ret;
}

adv_geom::PathCurve2d GenerateReferencePathFromGapResult(
    const LaneChangeGeometryMetaData& lane_change_geom_meta,
    std::ostringstream& debug_oss) {
  const int extended_source_curve_size =
      lane_change_geom_meta.base_meta_data.extended_source_curve.size();
  const int target_curve_size =
      lane_change_geom_meta.base_meta_data.target_curve.size();
  if (extended_source_curve_size == 0 || target_curve_size == 0) {
    debug_oss << __func__
              << " Generate reference failed because of empty source curve or "
                 "target curve. "
              << DUMP_TO_STREAM(extended_source_curve_size, target_curve_size)
              << "\n";
    return adv_geom::PathCurve2d();
  }
  const math::geometry::Point2d& cl_start =
      lane_change_geom_meta.cross_lane_start_pt_on_extended_source_lane_seq;
  const math::geometry::Point2d& cl_end =
      lane_change_geom_meta.actual_cross_lane_end_pt_on_target_lane_seq;

  std::vector<math::geometry::Point2d> reference_pts;
  reference_pts.reserve(extended_source_curve_size + target_curve_size);

  const math::ProximityQueryInfo& cl_start_proximity =
      lane_change_geom_meta.base_meta_data.extended_source_curve.GetProximity(
          cl_start, math::pb::UseExtensionFlag::kForbid);
  const math::ProximityQueryInfo& cl_end_proximity =
      lane_change_geom_meta.base_meta_data.target_curve.GetProximity(
          cl_end, math::pb::UseExtensionFlag::kForbid);
  // Get the part of extended source lane center before lane change start point.
  DivideCurveWithQueryArcLength(
      lane_change_geom_meta.base_meta_data.extended_source_curve,
      cl_start_proximity.arc_length,
      /*keep_front=*/true, &reference_pts);
  const double cl_start_extended_source_curve_arc_length =
      cl_start_proximity.arc_length;
  const double cl_end_extended_source_curve_arc_length =
      lane_change_geom_meta.base_meta_data.extended_source_curve
          .GetProximity(cl_end, math::pb::UseExtensionFlag::kAllow)
          .arc_length;
  if (cl_start_extended_source_curve_arc_length >
      cl_end_extended_source_curve_arc_length) {
    debug_oss << __func__ << " Generate reference failed. "
              << DUMP_TO_STREAM(cl_start_extended_source_curve_arc_length,
                                cl_end_extended_source_curve_arc_length)
              << "\n";
    return adv_geom::PathCurve2d();
  }
  // If lane change longitude span is too short,we use straight lines to connect
  // source curve with target curve.
  constexpr double kGapBufferForConnectSourceWithTargetInMeter = 1.0;
  const double cl_end_arc_length_on_extended_source_curve =
      lane_change_geom_meta.base_meta_data.extended_source_curve
          .GetProximity(cl_end, math::pb::UseExtensionFlag::kAllow)
          .arc_length;
  const double cl_start_arc_length_on_target_curve =
      lane_change_geom_meta.base_meta_data.target_curve
          .GetProximity(cl_start, math::pb::UseExtensionFlag::kAllow)
          .arc_length;
  std::vector<math::geometry::Point2d> connecting_curve = {};
  if (!FLAGS_planning_use_comfort_priority_lane_change_option &&
      cl_end_arc_length_on_extended_source_curve -
              cl_start_proximity.arc_length >
          (kMinLongitudinalSpanToDoLaneChangeInMeter +
           kGapBufferForConnectSourceWithTargetInMeter)) {
    connecting_curve = GenerateSpiralCurveForLaneChange(
        lane_change_geom_meta.base_meta_data.extended_source_curve,
        lane_change_geom_meta.base_meta_data.target_curve, cl_start_proximity,
        cl_end_proximity);
  }
  constexpr int kConnectingPointsNumber = 10;
  if (connecting_curve.empty()) {
    debug_oss << __func__ << " Generate spiral curve for LaneChange failed.\n";
    connecting_curve = GenerateConnectingPointsForLaneChange(
        lane_change_geom_meta.base_meta_data.extended_source_curve,
        lane_change_geom_meta.base_meta_data.target_curve,
        cl_end_arc_length_on_extended_source_curve, cl_end_proximity.arc_length,
        cl_start_proximity.arc_length, cl_start_arc_length_on_target_curve,
        kConnectingPointsNumber);
  }
  if (!connecting_curve.empty()) {
    reference_pts.insert(reference_pts.end(), connecting_curve.begin(),
                         connecting_curve.end());
  }
  // Append the part of target lane center after lane change end point.
  DivideCurveWithQueryArcLength(
      lane_change_geom_meta.base_meta_data.target_curve,
      cl_end_proximity.arc_length,
      /*keep_front=*/false, &reference_pts);
  // When there are too few reference points, don't build the reference path.
  if (reference_pts.size() < 2) {
    debug_oss << __func__ << " reference points < 2.\n";
    return adv_geom::PathCurve2d();
  }
  return adv_geom::PathCurve2d(
      math::geometry::PolylineCurve2d(std::move(reference_pts)));
}

lane_selection::LaneSequenceGeometry GetLaneSequenceGeometry(
    const lane_selection::LaneSequenceGeometry& lane_sequence_geometry_in,
    const math::geometry::PolylineCurve2d& nominal_path,
    const math::geometry::PolylineCurve2d& left_lane_boundary,
    const math::geometry::PolylineCurve2d& right_lane_boundary) {
  lane_selection::LaneSequenceGeometry lane_sequence_geometry =
      lane_sequence_geometry_in;
  lane_sequence_geometry.nominal_path =
      lane_selection::NominalPath(nominal_path);
  lane_sequence_geometry.left_lane_boundary = left_lane_boundary;
  lane_sequence_geometry.right_lane_boundary = right_lane_boundary;
  return lane_sequence_geometry;
}

double GetSplitPointArclengthOnLaneMarkingCurve(
    const LaneChangeGeometryMetaData& lane_change_geom_meta,
    bool is_source_lane, bool is_left_side) {
  const math::geometry::Point2d& cl_start =
      lane_change_geom_meta.cross_lane_start_pt_on_extended_source_lane_seq;
  const math::geometry::Point2d& latest_cl_end =
      lane_change_geom_meta
                  .actual_cross_lane_end_arc_length_on_target_lane_seq >
              lane_change_geom_meta
                  .latest_cross_lane_end_arc_length_on_source_lane_seq
          ? lane_change_geom_meta.actual_cross_lane_end_pt_on_target_lane_seq
          : lane_change_geom_meta.latest_cross_lane_end_pt_on_source_lane_seq;

  const math::geometry::PolylineCurve2d& left_lane_marking =
      is_source_lane
          ? lane_change_geom_meta.base_meta_data
                .extended_source_curve_left_boundary
          : lane_change_geom_meta.base_meta_data.target_curve_left_boundary;
  const math::geometry::PolylineCurve2d& right_lane_marking =
      is_source_lane
          ? lane_change_geom_meta.base_meta_data
                .extended_source_curve_right_boundary
          : lane_change_geom_meta.base_meta_data.target_curve_right_boundary;

  const bool is_left_cross_lane =
      lane_change_geom_meta.base_meta_data.cross_lane_direction ==
      pb::LaneChangeMode::LEFT_LANE_CHANGE;
  // Get the Proximity of start/end cl point on soure/target lane marking curve.
  return is_left_side
             ? left_lane_marking
                   .GetProximity(is_left_cross_lane ? cl_start : latest_cl_end,
                                 math::pb::UseExtensionFlag::kForbid)
                   .arc_length
             : right_lane_marking
                   .GetProximity(is_left_cross_lane ? latest_cl_end : cl_start,
                                 math::pb::UseExtensionFlag::kForbid)
                   .arc_length;
}

// Given range, return the valid range and the invalid range of this range.
std::pair<std::optional<math::Range1d>, std::optional<math::Range1d>>
GetSoftAndHardBoundaryRange(const bool is_front_range,
                            const math::Range1d& range,
                            const std::vector<math::Range1d>& valid_ranges,
                            const bool need_solid_line_relax,
                            const double relaxed_dist) {
  if (!math::IsValidRange(range)) {
    return std::make_pair(std::nullopt, std::nullopt);
  }

  const std::vector<math::Range1d> intersect_range =
      math::IntersectRanges(valid_ranges, {range});

  std::optional<math::Range1d> soft_range;
  constexpr double kToleranceDistanceInMeter = 0.2;
  // We only want to treat the valid range as soft range if the valid range is
  // near the gap. For the front range, we choose the last intersect_range. For
  // the back range, we choose the first intersect_range.
  if (is_front_range) {
    soft_range =
        !intersect_range.empty() &&
                math::IsApprox(intersect_range.back().end_pos, range.end_pos,
                               kToleranceDistanceInMeter)
            ? std::make_optional(math::Range1d(intersect_range.back().start_pos,
                                               range.end_pos))
            : std::nullopt;

  } else {
    soft_range =
        !intersect_range.empty() &&
                math::IsApprox(intersect_range[0].start_pos, range.start_pos,
                               kToleranceDistanceInMeter)
            ? std::make_optional(
                  math::Range1d(range.start_pos, intersect_range[0].end_pos))
            : std::nullopt;
  }

  if (need_solid_line_relax && !is_front_range) {
    soft_range =
        soft_range.has_value()
            ? math::Range1d(
                  soft_range->start_pos,
                  std::min(soft_range->end_pos + relaxed_dist, range.end_pos))
            : math::Range1d(
                  range.start_pos,
                  std::min(range.start_pos + relaxed_dist, range.end_pos));
  } else if (need_solid_line_relax && is_front_range) {
    soft_range =
        soft_range.has_value()
            ? math::Range1d(std::max(range.start_pos,
                                     soft_range->start_pos - relaxed_dist),
                            soft_range->end_pos)
            : math::Range1d(
                  std::max(range.start_pos, range.end_pos - relaxed_dist),
                  range.end_pos);
  }
  // For a back range, if we have more than one intersect_range within range
  // span, the hard range should end before second feasible range start.
  std::optional<math::Range1d> hard_range =
      soft_range.has_value()
          ? (is_front_range
                 ? math::Range1d(range.start_pos, soft_range->start_pos)
                 : math::Range1d(soft_range->end_pos,
                                 (intersect_range.size() > 1)
                                     ? intersect_range[1].start_pos
                                     : range.end_pos))
          : range;
  return std::make_pair(soft_range, hard_range);
}

bool IsSafeGapForBackupOption(const double ego_speed,
                              const EstimateGapInfoAtSwitchTime& gap_info,
                              const double yield_min_v) {
  // Check if the ego can safely yield before the estimated lead object by its
  // yield_min_v.
  constexpr double kSwitchTimeInSec = 3.0;
  constexpr double kSafelyYieldAccelInMpss = 1.5;
  constexpr double kYieldHorizonInSec = 6.0;

  const double decel_time =
      math::Clamp((ego_speed - yield_min_v) / kSafelyYieldAccelInMpss, 0.0,
                  kYieldHorizonInSec);
  const double const_speed = ego_speed - kSafelyYieldAccelInMpss * decel_time;
  const double const_speed_time = kYieldHorizonInSec - decel_time;
  const double ego_displacement = const_speed * const_speed_time +
                                  0.5 * (ego_speed + const_speed) * decel_time;
  const bool can_safely_yield =
      gap_info.lead_obj_ego_dist +
          gap_info.lead_obj_speed * kYieldHorizonInSec - ego_displacement >
      0.0;

  // Check if this gap is large enough to enter.
  constexpr double kLeadTimeBufferInSec = 0.5;
  constexpr double kTailTimeBufferInSec = 1.0;
  constexpr double kMinLengthBufferInMeter = 5.0;
  constexpr double kTtcBufferInSec = 4.0;
  const double min_lead_dist =
      std::max(kLeadTimeBufferInSec * ego_speed, kMinLengthBufferInMeter);
  const double min_tail_dist = std::max(
      kTailTimeBufferInSec * gap_info.tail_obj_speed, kMinLengthBufferInMeter);
  const bool is_safe_to_lead =
      gap_info.lead_obj_ego_dist -
              (ego_speed - gap_info.lead_obj_speed) * kSwitchTimeInSec >
          min_lead_dist &&
      gap_info.lead_obj_ego_ttc > kTtcBufferInSec;
  const bool is_safe_to_tail =
      -gap_info.tail_obj_ego_dist -
              (gap_info.tail_obj_speed - ego_speed) * kSwitchTimeInSec >
          min_tail_dist &&
      gap_info.tail_obj_ego_ttc > kTtcBufferInSec;

  return can_safely_yield && is_safe_to_lead && is_safe_to_tail;
}

math::Range1d EstimateLcOptionWithRemainingLcDuration(
    const double ego_arc_length, const double remaining_lc_duration,
    const double single_lc_lat_distance,
    const double ego_source_curve_lat_distance, const bool is_left_lane_change,
    const double ego_speed) {
  // Compute remaining lane change range.
  DCHECK_GT(remaining_lc_duration, math::constants::kEpsilon);
  constexpr double kMinLaneChangeSpeedInMps = 3.0;
  constexpr double kMaxTotalLcDurationInSec = 8.0;
  constexpr double kMaxRemainingLcDurationInSec = 5.0;
  const double remaining_lc_range =
      std::max(ego_speed, kMinLaneChangeSpeedInMps) *
      std::min(remaining_lc_duration, kMaxRemainingLcDurationInSec);

  // Estimate the total lane change range based on remaining range and ego's
  // current lateral position.
  const double normal_ego_source_curve_lat_distance =
      is_left_lane_change ? ego_source_curve_lat_distance
                          : -ego_source_curve_lat_distance;
  DCHECK_GT(single_lc_lat_distance, math::constants::kEpsilon);
  const double lc_completion_rate =
      normal_ego_source_curve_lat_distance / single_lc_lat_distance;
  const double total_lc_range =
      remaining_lc_range /
      std::max(1 - lc_completion_rate, math::constants::kEpsilon);

  // It's possible that we get a very large total_lc_range when ego is very
  // close to the target curve. In this case, we cap the total range.
  const double max_lc_option_length =
      std::max(ego_speed, kMinLaneChangeSpeedInMps) * kMaxTotalLcDurationInSec;
  double final_total_lc_range = total_lc_range;
  if (final_total_lc_range > max_lc_option_length) {
    final_total_lc_range = max_lc_option_length;
  }

  return math::Range1d(
      ego_arc_length + remaining_lc_range - final_total_lc_range,
      ego_arc_length + remaining_lc_range);
}

std::optional<math::Range1d> FindFeasibleRangeBestMatch(
    const std::vector<math::Range1d>& feasible_ranges,
    const math::Range1d& lc_option, std::ostringstream& debug_oss) {
  // Try to find a match with the middle 1/2 of the lane change option, assuming
  // that this is the range that ego is crossing lane and subject to the
  // feasible range check.
  constexpr double kLcOptionEffectiveRatio = 0.5;
  const double lc_option_length = lc_option.end_pos - lc_option.start_pos;
  const double lc_option_effective_length =
      lc_option_length * kLcOptionEffectiveRatio;
  const double lc_option_single_side_shift =
      (lc_option_length - lc_option_effective_length) * 0.5;
  const double lc_option_start =
      lc_option.start_pos + lc_option_single_side_shift;
  const double lc_option_end = lc_option.end_pos - lc_option_single_side_shift;
  for (const auto& feasible_range : feasible_ranges) {
    debug_oss << __func__ << ": Checking range (" << feasible_range.start_pos
              << ", " << feasible_range.end_pos << "): ";
    // We add a small tolerance to the feasible range check.
    constexpr double kToleranceBufferInMeter = 5.0;
    const bool option_matches =
        lc_option_start > feasible_range.start_pos - kToleranceBufferInMeter &&
        lc_option_end < feasible_range.end_pos + kToleranceBufferInMeter;
    if (option_matches) {
      debug_oss << "Match!\n";
      return std::make_optional<math::Range1d>(feasible_range);
    }

    debug_oss << "No Match.\n";
  }

  return std::nullopt;
}

speed::pb::SpeedSolverLcGuideSeed ComputeConstSpeedLcGuideSeed(
    const RobotStateSnapshot& current_state_snapshot,
    const math::geometry::PolylineCurve2d& extended_source_curve) {
  constexpr int kProfileSteps = 80;

  speed::pb::SpeedSolverLcGuideSeed pseudo_cross_lane_guide;
  const int64_t cur_t = current_state_snapshot.timestamp();
  const double cur_x = current_state_snapshot.x();
  const double cur_v = current_state_snapshot.speed();

  // Generate a constant speed profile.
  ::planner::speed::pb::Profile speed_profile;
  for (int64_t ix = 0; ix < kProfileSteps; ++ix) {
    ::planner::speed::pb::State state;
    const double dt = math::Ms2Sec(ix * constants::kPlanningCycleTimeInMSec);
    state.set_t(math::Ms2Sec(cur_t) + dt);
    state.set_x(cur_x + cur_v * dt);
    state.set_v(cur_v);
    state.set_a(0.0);
    *(speed_profile.add_states()) = state;
  }

  // Generate pseudo_cross_lane_guide.
  const double lc_start_arclength =
      extended_source_curve
          .GetProximity(current_state_snapshot.rear_axle_position(),
                        math::pb::UseExtensionFlag::kForbid)
          .arc_length;
  ::voy::Point2d lc_start_position;
  lc_start_position.set_x(
      extended_source_curve.GetInterp(lc_start_arclength).x());
  lc_start_position.set_y(
      extended_source_curve.GetInterp(lc_start_arclength).y());
  *pseudo_cross_lane_guide.mutable_lc_start_position() = lc_start_position;

  const double lc_end_arclength = lc_start_arclength +
                                  speed_profile.states().rbegin()->x() -
                                  speed_profile.states().begin()->x();
  ::voy::Point2d lc_end_position;
  lc_end_position.set_x(extended_source_curve.GetInterp(lc_end_arclength).x());
  lc_end_position.set_y(extended_source_curve.GetInterp(lc_end_arclength).y());
  *pseudo_cross_lane_guide.mutable_lc_end_position() = lc_end_position;
  *pseudo_cross_lane_guide.mutable_lc_ref_end_position() = lc_end_position;

  *pseudo_cross_lane_guide.mutable_speed_profile() = speed_profile;
  *pseudo_cross_lane_guide.mutable_tree_profile() = speed_profile;
  *pseudo_cross_lane_guide.mutable_max_speed_profile() = speed_profile;
  pseudo_cross_lane_guide.set_discomfort_ix(speed::Discomforts::kLevels - 1);
  pseudo_cross_lane_guide.set_tail_obj_id(-1);

  return pseudo_cross_lane_guide;
}

speed::pb::SpeedSolverLcGuideSeed ComputeConstDecelLcGuideSeed(
    const RobotStateSnapshot& current_state_snapshot,
    const math::geometry::PolylineCurve2d& extended_source_curve,
    const double dist_to_blockage) {
  constexpr int kProfileSteps = 80;
  constexpr double kMinAccelInMpss = -2.0;

  speed::pb::SpeedSolverLcGuideSeed pseudo_cross_lane_guide;
  const int64_t cur_t = current_state_snapshot.timestamp();
  const double cur_x = current_state_snapshot.x();
  const double cur_v = current_state_snapshot.speed();
  const double ttc_of_blockage =
      std::isinf(dist_to_blockage) ? 0.0 : dist_to_blockage / cur_v;

  // Generate speed profile.
  // Use switch_ix to separate the deceleration & constant speed phases within
  // profile's max step number.
  ::planner::speed::pb::Profile speed_profile;
  const int64_t switch_ix = std::min(
      static_cast<int>(ttc_of_blockage /
                       math::Ms2Sec(constants::kPlanningCycleTimeInMSec)),
      kProfileSteps);
  for (int64_t ix = 0; ix < kProfileSteps; ++ix) {
    ::planner::speed::pb::State state;
    const double dt = math::Ms2Sec(ix * constants::kPlanningCycleTimeInMSec);
    // Upper bounded by the switch time when ego reaches obstacle pos with
    // constant target speed.
    const double time_duration_before_reaching_target_speed = std::min(
        dt, math::Ms2Sec(switch_ix * constants::kPlanningCycleTimeInMSec));
    // Lower bounded by 0 if ego is still decelerating.
    const double time_duration_after_reaching_target_speed =
        dt - time_duration_before_reaching_target_speed;
    const double local_speed =
        cur_v + kMinAccelInMpss * time_duration_before_reaching_target_speed;

    state.set_t(math::Ms2Sec(cur_t) + dt);
    state.set_x(cur_x + cur_v * time_duration_before_reaching_target_speed +
                0.5 * kMinAccelInMpss *
                    time_duration_before_reaching_target_speed *
                    time_duration_before_reaching_target_speed +
                local_speed * time_duration_after_reaching_target_speed);
    state.set_v(local_speed);
    state.set_a(math::NearZero(time_duration_after_reaching_target_speed)
                    ? kMinAccelInMpss
                    : 0.0);
    *(speed_profile.add_states()) = state;
  }

  // Generate pseudo_cross_lane_guide.
  const double lc_start_arclength =
      extended_source_curve
          .GetProximity(current_state_snapshot.rear_axle_position(),
                        math::pb::UseExtensionFlag::kForbid)
          .arc_length;
  ::voy::Point2d lc_start_position;
  lc_start_position.set_x(
      extended_source_curve.GetInterp(lc_start_arclength).x());
  lc_start_position.set_y(
      extended_source_curve.GetInterp(lc_start_arclength).y());
  *pseudo_cross_lane_guide.mutable_lc_start_position() = lc_start_position;

  const double lc_end_arclength = lc_start_arclength +
                                  speed_profile.states().rbegin()->x() -
                                  speed_profile.states().begin()->x();
  ::voy::Point2d lc_end_position;
  lc_end_position.set_x(extended_source_curve.GetInterp(lc_end_arclength).x());
  lc_end_position.set_y(extended_source_curve.GetInterp(lc_end_arclength).y());
  *pseudo_cross_lane_guide.mutable_lc_end_position() = lc_end_position;
  *pseudo_cross_lane_guide.mutable_lc_ref_end_position() = lc_end_position;

  *pseudo_cross_lane_guide.mutable_speed_profile() = speed_profile;
  *pseudo_cross_lane_guide.mutable_tree_profile() = speed_profile;
  *pseudo_cross_lane_guide.mutable_max_speed_profile() = speed_profile;
  pseudo_cross_lane_guide.set_discomfort_ix(speed::Discomforts::kLevels - 1);

  return pseudo_cross_lane_guide;
}

std::vector<math::Range1d> GetIntersectedRanges(
    const double ego_ra_arc_length_on_extended_source_region,
    const double speed_start_arc_length_on_extended_source_region,
    const double dist_to_blockage, const double ego_speed,
    const LaneChangeInstance& lane_change_instance,
    const LaneChangeObjectInfoList* target_region_object_info_list,
    const int64_t tail_obj_id, const bool ignore_routing_start_arclength,
    std::ostringstream& debug_oss) {
  const double source_region_extended_length =
      lane_change_instance.lane_sequence_length_before_lane_change_region();
  // Initialize a vector of ranges candidates to take intersection.
  std::vector<std::vector<math::Range1d>> ranges_candidates;
  constexpr int kMaxRangesSourceTypeCount = 4;
  ranges_candidates.reserve(kMaxRangesSourceTypeCount);
  // Get range from speed gap align.
  // If tail obj is parked car, decrease start arc length.
  const double source_region_start_arc_length =
      speed_start_arc_length_on_extended_source_region -
      source_region_extended_length;
  const double source_region_end_arc_length =
      source_region_start_arc_length + dist_to_blockage;
  const bool is_tail_obj_parked_car =
      target_region_object_info_list != nullptr &&
      target_region_object_info_list->FindNonLeaving(tail_obj_id) != nullptr &&
      target_region_object_info_list->FindNonLeaving(tail_obj_id)
          ->is_parked_car;
  math::Range1d speed_lane_change_range(
      is_tail_obj_parked_car
          ? source_region_start_arc_length -
                target_region_object_info_list->FindNonLeaving(tail_obj_id)
                    ->length
          : source_region_start_arc_length,
      source_region_end_arc_length);
  ranges_candidates.push_back({speed_lane_change_range});
  debug_oss << __func__ << ": \ngap align range:  "
            << speed_lane_change_range.start_pos + source_region_extended_length
            << " ~ "
            << speed_lane_change_range.end_pos + source_region_extended_length;

  // Initialize an overlapped ranges.
  std::vector<math::Range1d> overlapped_ranges = {
      std::move(speed_lane_change_range)};
  // Get valid lane marking ranges.
  // NOTE(Judychen): This range is derived from lane marking
  // instead of lane center line, which is an approximation for simplicity.
  const math::geometry::PolylineCurve2d& lane_marking_curve =
      lane_change_instance.direction() == planner::pb::LEFT_LANE_CHANGE
          ? lane_change_instance.target_region().right_boundary()
          : lane_change_instance.target_region().left_boundary();
  const std::vector<math::Range1d>& valid_lane_marking_ranges =
      lane_change_instance.target_region_marking_sequence()
          .GetValidLaneChangeRanges(
              ego_ra_arc_length_on_extended_source_region -
                  source_region_extended_length,
              lane_marking_curve);
  ranges_candidates.push_back(valid_lane_marking_ranges);
  debug_oss << "\nvalid lane marking ranges:  ";
  for (const math::Range1d& valid_lane_marking_range :
       valid_lane_marking_ranges) {
    debug_oss << valid_lane_marking_range.start_pos +
                     source_region_extended_length
              << " ~ "
              << valid_lane_marking_range.end_pos +
                     source_region_extended_length
              << ",  ";
  }

  // When lane change instance is NOT in junction, it should finish before
  // junction. So get valid ranges which are NOT in junction.
  if (!lane_change_instance.target_lane().IsInJunction()) {
    const std::vector<math::Range1d>& junction_ranges =
        GetNonJunctionCrossingLaneMarkingRanges(
            lane_change_instance.direction(),
            lane_change_instance.target_lane_sequence());
    ranges_candidates.push_back(junction_ranges);
    debug_oss << "\nno junction ranges:  ";
    for (const math::Range1d& junction_range : junction_ranges) {
      debug_oss << junction_range.start_pos + source_region_extended_length
                << " ~ "
                << junction_range.end_pos + source_region_extended_length
                << ",  ";
    }
  }
  // Get valid routing range.
  // - Use the routing start arc length as start with a small backward buffer.
  // - Use lane change region length as the max because sometimes target region
  // can exceed the lane change region.
  double routing_start_arclength_on_extended_source_region_with_buf =
      -lane_change_instance.ComputeLaneChangeStartArcLengthBufferBeforeRouting(
          ego_speed);
  if (!ignore_routing_start_arclength) {
    math::UpdateMax(
        lane_change_instance.start_arclength_on_source_lane() +
            lane_change_instance.source_region_length_before_source_lane() -
            lane_change_instance
                .ComputeLaneChangeStartArcLengthBufferBeforeRouting(ego_speed),
        routing_start_arclength_on_extended_source_region_with_buf);
  }
  const math::Range1d& routing_range = math::Range1d(
      routing_start_arclength_on_extended_source_region_with_buf,
      lane_change_instance.target_region().nominal_path().GetTotalArcLength() -
          lane_change_instance
              .target_region_length_beyond_lane_change_region());
  ranges_candidates.push_back({routing_range});
  debug_oss << "\nrouting range:  "
            << routing_range.start_pos + source_region_extended_length << " ~ "
            << routing_range.end_pos + source_region_extended_length << "\n";

  for (std::vector<math::Range1d>& ranges : ranges_candidates) {
    // Get the intersection of each set of ranges.
    for (size_t i = 0; i < ranges.size(); i++) {
      ranges[i].start_pos += source_region_extended_length;
      ranges[i].end_pos += source_region_extended_length;
    }
    overlapped_ranges = math::IntersectRanges(overlapped_ranges, ranges);
  }
  return overlapped_ranges;
}

std::vector<math::Range1d> GetNonJunctionCrossingLaneMarkingRanges(
    const pb::LaneChangeMode& lane_change_direction,
    const std::vector<const pnc_map::Lane*>& target_lane_sequence) {
  std::vector<math::Range1d> non_junction_ranges;
  non_junction_ranges.reserve(target_lane_sequence.size());
  double curr_non_junction_arclength = 0.0;
  double next_lane_start_arclength = 0.0;
  for (const pnc_map::Lane* lane : target_lane_sequence) {
    const pnc_map::LaneMarking* cross_marking =
        lane_change_direction == pb::LaneChangeMode::RIGHT_LANE_CHANGE
            ? lane->left_marking()
            : lane->right_marking();
    if (!lane->IsInJunction()) {
      // This lane is not in junction.
      next_lane_start_arclength += cross_marking->line().GetTotalArcLength();
      continue;
    }
    // This lane is in junction.
    if (next_lane_start_arclength >
        curr_non_junction_arclength + math::constants::kEpsilon) {
      // This is a valid longest non junction range, Add it.
      non_junction_ranges.emplace_back(curr_non_junction_arclength,
                                       next_lane_start_arclength);
    }
    // Update both arclengths.
    next_lane_start_arclength += cross_marking->line().GetTotalArcLength();
    curr_non_junction_arclength = next_lane_start_arclength;
  }

  // Update once more if there is a valid range.
  if (next_lane_start_arclength >
      curr_non_junction_arclength + math::constants::kEpsilon) {
    non_junction_ranges.emplace_back(curr_non_junction_arclength,
                                     next_lane_start_arclength);
  }
  return non_junction_ranges;
}

LaneChangeSelectedGapInfo GetSelectedGapInfo(
    const RobotState& ego_state, const LaneChangeInstance& lane_change_instance,
    const LaneChangeInfo& lane_change_info,
    const pb::ElectiveLaneChangeDecision& elc_decision,
    const pb::StuckSignal& stuck_signal, const bool need_backup_lc,
    const double extended_source_region_start_to_ego_ra,
    const std::vector<math::Range1d>&
        feasible_lc_ranges_on_extended_source_region,
    const speed::SpeedBoundGenerator& speed_bound_generator,
    const speed::pb::Profile& speed_seed_speed_profile,
    const speed::pb::Profile& speed_seed_tree_profile,
    const speed::pb::Profile& speed_seed_resampled_ref_profile,
    const int discomfort_ix, const int64_t tail_obj_id,
    std::ostringstream& debug_oss) {
  LaneChangeSelectedGapInfo selected_gap_info;
  const LaneChangeObjectInfoList* target_region_object_info_list =
      GetTargetRegionObjectInfoListPtr(lane_change_info);
  if (target_region_object_info_list == nullptr) {
    debug_oss << __func__
              << ": No feasible options because of wrong target region info.\n";
    return selected_gap_info;
  }
  const pb::LaneChangeMetadata& lane_change_metadata =
      lane_change_info.lane_change_metadata;
  const std::vector<LaneChangeUrgencyInfo>& urgency_infos =
      lane_change_info.urgency_infos();
  const LaneSequenceResult* lane_sequence_wo_lane_change =
      lane_change_info.lane_sequence_wo_lane_change;
  const LaneChangeRegionInfo* source_region_info =
      GetLaneChangeRegionInfoPtrByType(
          /*region_type=*/pb::LaneChangeRegionType::SOURCE,
          lane_change_info.region_infos());
  const LaneChangeRegionInfo* target_region_info =
      GetLaneChangeRegionInfoPtrByType(
          /*region_type=*/pb::LaneChangeRegionType::TARGET,
          lane_change_info.region_infos());

  const speed::Profile speed_profile =
      speed::FromProto(speed_seed_speed_profile);
  const speed::Profile tree_profile = speed::FromProto(speed_seed_tree_profile);
  const speed::Profile resampled_ref_profile =
      speed::FromProto(speed_seed_resampled_ref_profile);
  DCHECK(!tree_profile.empty());
  [[maybe_unused]] const double ego_speed = tree_profile[0].v;
  const bool has_queue_fully_in_front_of_ego =
      target_region_info != nullptr && target_region_info->HasQueue() &&
      !target_region_info->HasQueueObjectBehindEgo();
  std::vector<pb::LaneChangeLongitudinalSpanOptionsDebug::LcOptionType>
      lc_option_type_candidates;
  // NOTE(Tingran): Put the lc option types in order of priority. The type with
  // lower index has higher priority to select if feasible.
  // De-prioritize the start delayed option when a queue is found.
  if (has_queue_fully_in_front_of_ego) {
    lc_option_type_candidates = {
        pb::LaneChangeLongitudinalSpanOptionsDebug::kEarliest,
        pb::LaneChangeLongitudinalSpanOptionsDebug::kDefault};
  } else {
    lc_option_type_candidates = {
        pb::LaneChangeLongitudinalSpanOptionsDebug::kStartDelayed,
        pb::LaneChangeLongitudinalSpanOptionsDebug::kEarliest,
        pb::LaneChangeLongitudinalSpanOptionsDebug::kDefault};
  }
  if (FLAGS_planning_use_comfort_priority_lane_change_option) {
    lc_option_type_candidates = {
        pb::LaneChangeLongitudinalSpanOptionsDebug::kComfortPriority};
  }
  if (need_backup_lc) {
    lc_option_type_candidates.emplace_back(
        pb::LaneChangeLongitudinalSpanOptionsDebug::kBackup);
  }
  for (size_t ix = 0; ix < feasible_lc_ranges_on_extended_source_region.size();
       ++ix) {
    const math::Range1d feasible_lc_range_on_extended_source_region =
        feasible_lc_ranges_on_extended_source_region[ix];
    debug_oss
        << __func__ << ":\nfeasible range: "
        << feasible_lc_range_on_extended_source_region.start_pos << "~"
        << feasible_lc_range_on_extended_source_region.end_pos
        << ", extended length: "
        << lane_change_instance.lane_sequence_length_before_lane_change_region()
        << ", ego ra arclength: " << extended_source_region_start_to_ego_ra
        << "\n";
    const double earliest_lc_start_ra_arclength =
        std::max(0.0, feasible_lc_range_on_extended_source_region.start_pos -
                          extended_source_region_start_to_ego_ra);

    // If this range starts far away, do not generate LC option.
    constexpr int64_t kIndexOfStateToCheck = 40;
    constexpr double kMinTimeToGenerateLcOptionInSec = 2.0;
    constexpr double kMinDistToGenerateLcOptionInMeter = 5.0;
    DCHECK(tree_profile.size() > kIndexOfStateToCheck + 1);
    if (earliest_lc_start_ra_arclength >
        std::max({tree_profile[kIndexOfStateToCheck].x,
                  tree_profile[0].v * kMinTimeToGenerateLcOptionInSec,
                  kMinDistToGenerateLcOptionInMeter})) {
      debug_oss << "Range starts far away, path blocked. \n";
      continue;
    }

    // New lane change option model.
    if (FLAGS_planning_use_comfort_priority_lane_change_option) {
      constexpr double kTimeHorizonForQueueObjInSec = 2.0;
      selected_gap_info.feasible_lc_ranges_and_options.emplace_back(
          NewLaneChangeDurationLongitudinalSpan(
              ego_state, tail_obj_id, target_region_object_info_list,
              feasible_lc_range_on_extended_source_region, lane_change_metadata,
              lane_change_instance.extended_source_region(),
              lane_change_instance.target_region(),
              lane_change_instance.direction(), tree_profile,
              resampled_ref_profile, elc_decision, stuck_signal,
              speed_bound_generator, need_backup_lc,
              earliest_lc_start_ra_arclength,
              extended_source_region_start_to_ego_ra,
              std::min(source_region_info->DistToNearestObject(),
                       -target_region_info->DistToNearestObjectBehindEgo()),
              target_region_info->DistToNearestQueueObjectInFrontOfEgoInFuture(
                  kTimeHorizonForQueueObjInSec),
              discomfort_ix, debug_oss));
    } else {
      // Append feasible lc range for lane change.
      selected_gap_info.feasible_lc_ranges_and_options.emplace_back(
          LaneChangeDurationLongitudinalSpan(
              ego_state, tail_obj_id, target_region_object_info_list,
              feasible_lc_range_on_extended_source_region, lane_change_metadata,
              lane_change_instance.extended_source_region(),
              lane_change_instance.target_region(),
              lane_change_instance.direction(), speed_profile, tree_profile,
              resampled_ref_profile, elc_decision, stuck_signal, urgency_infos,
              speed_bound_generator, earliest_lc_start_ra_arclength,
              extended_source_region_start_to_ego_ra, discomfort_ix,
              debug_oss));
    }
    // Loop through all lc options to find the first available gap.
    for (const pb::LaneChangeLongitudinalSpanOptionsDebug::LcOptionType
             lc_option_type : lc_option_type_candidates) {
      LaneChangeLongitudinalSpanOptions& lane_change_span_options =
          selected_gap_info.feasible_lc_ranges_and_options.back();
      auto iter = lane_change_span_options.lc_options_map.find(lc_option_type);
      if (iter == lane_change_span_options.lc_options_map.end()) {
        continue;
      }
      auto crossing_pos_iter =
          lane_change_span_options.lc_options_crossing_pos_map.find(
              lc_option_type);
      DCHECK(crossing_pos_iter !=
             lane_change_span_options.lc_options_crossing_pos_map.end());
      auto& lc_option_range = iter->second;
      const double lc_crossing_pos = crossing_pos_iter->second;

      // Avoid using start delayed option for short consecutive LC.
      const bool is_short_consecutive_lc =
          lane_change_metadata.consecutive_lane_change_count() > 1 &&
          lane_change_metadata.max_longitudinal_span_for_current_lc() >
              math::constants::kEpsilon;
      const bool option_end_after_max_longitudinal_span =
          is_short_consecutive_lc &&
          lc_option_range.end_pos >
              lane_change_metadata.max_longitudinal_span_for_current_lc() +
                  extended_source_region_start_to_ego_ra +
                  math::constants::kEpsilon;
      if (option_end_after_max_longitudinal_span &&
          lc_option_type ==
              pb::LaneChangeLongitudinalSpanOptionsDebug::kStartDelayed) {
        continue;
      }

      if (CanLCOptionSatisfyFeasibleRange(
              lane_sequence_wo_lane_change,
              feasible_lc_range_on_extended_source_region, lc_option_range,
              lc_crossing_pos, lc_option_type, debug_oss)) {
        selected_gap_info.selected_lc_range_and_option_ix = ix;
        lane_change_span_options.selected_lc_option_type = lc_option_type;
        if (FLAGS_planning_use_comfort_priority_lane_change_option) {
          debug_oss << "Feasible comfort priority option found!\n";
          return selected_gap_info;
        }
        // Clamp the lc option end pos to the feasible lc range end pos. This is
        // to avoid running solid lane marking.
        const double unclamped_end_pos = lc_option_range.end_pos;
        const bool end_clamped =
            math::UpdateMin(feasible_lc_range_on_extended_source_region.end_pos,
                            lc_option_range.end_pos);
        if (end_clamped) {
          debug_oss << __func__
                    << ": lc_option_range.end_pos is clamped. Before: "
                    << unclamped_end_pos
                    << ", after: " << lc_option_range.end_pos << "\n";
        }

        debug_oss << __func__ << ": Feasible option found!\n";
        return selected_gap_info;
      }
    }
  }
  debug_oss << __func__ << ": No feasible options.\n";
  return selected_gap_info;
}

LaneChangeLongitudinalSpanOptions NewLaneChangeDurationLongitudinalSpan(
    const RobotState& ego_state, const int64_t tail_obj_id,
    const LaneChangeObjectInfoList* target_region_object_info_list,
    const math::Range1d& feasible_lc_range_on_extended_source_region,
    const pb::LaneChangeMetadata& lane_change_metadata,
    const LaneFollowSequenceCurve& extended_source_region_curve,
    const LaneFollowSequenceCurve& target_region_curve,
    const pb::LaneChangeMode& lc_direction, const speed::Profile& tree_profile,
    const speed::Profile& resampled_ref_profile,
    const pb::ElectiveLaneChangeDecision& elc_decision,
    const pb::StuckSignal& stuck_signal,
    const speed::SpeedBoundGenerator& speed_bound_generator,
    const bool need_backup_lc, const double earliest_lc_start_ra_arclength,
    const double extended_source_region_start_to_ego_ra,
    const double dist_to_nearest_interactive_object,
    const double dist_to_target_region_queue_ahead_of_ego_in_future,
    const int discomfort_ix, std::ostringstream& debug_oss) {
  DCHECK_GT(earliest_lc_start_ra_arclength, -math::constants::kEpsilon);
  DCHECK(!tree_profile.empty());
  const vehicle_model::pb::AxleRectangularMeasurement& ego_shape =
      ego_state.car_model_with_shape().shape_measurement();
  const double rb_to_ra_dist = ego_shape.rear_bumper_to_rear_axle();
  const double ego_speed = tree_profile[0].v;
  constexpr double kMaxStartDelayedTimeInSec = 0.5;

  LaneChangeLongitudinalSpanOptions lane_change_longitudinal_span_options;
  // If need_backup_lc, early return w/o comfort_priority_option.
  if (need_backup_lc) {
    double backup_option_crossing_pos = 0.0;
    const std::optional<math::Range1d>& backup_option =
        GetBackupLongitudinalSpan(
            lane_change_metadata, ego_state, lc_direction,
            extended_source_region_curve, target_region_curve,
            feasible_lc_range_on_extended_source_region,
            extended_source_region_start_to_ego_ra, kMaxStartDelayedTimeInSec,
            backup_option_crossing_pos, debug_oss);
    if (backup_option.has_value()) {
      lane_change_longitudinal_span_options.feasible_lc_range =
          feasible_lc_range_on_extended_source_region;
      lane_change_longitudinal_span_options.lc_options_map.emplace(
          pb::LaneChangeLongitudinalSpanOptionsDebug::kBackup,
          std::move(backup_option.value()));
      lane_change_longitudinal_span_options.lc_options_crossing_pos_map.emplace(
          pb::LaneChangeLongitudinalSpanOptionsDebug::kBackup,
          backup_option_crossing_pos);
      rt_event::PostRtEvent<rt_event::planner::PlannerLaneChangeBackupOption>();
    }
    return lane_change_longitudinal_span_options;
  }

  const double lc_duration_reduction = ComputeLCDurationReduction(
      tail_obj_id, target_region_object_info_list, ego_speed, rb_to_ra_dist,
      lane_change_metadata.consecutive_lane_change_count(), debug_oss);
  debug_oss << __func__ << ": lc_duration_reduction: " << lc_duration_reduction;

  // Generate a comfort priority option.
  constexpr double kLowInterationLateralJerk = 0.5;
  constexpr double kHighInterationLateralJerk = 0.8;
  double comfort_option_crossing_pos = 0.0;
  // If exists a near object, ego need a higher comfort lateral jerk w.r.t
  // higher interaction intensity.
  // TODO(ryanliyiyang): separate the comfort jerk of agent from source front,
  // source behind, and target behind.
  constexpr double kMinDistToIgnoreObjectInMeter = 8.0;
  constexpr double kMinTimeToIgnoreObjectInSec = 5.0;
  const bool has_near_object =
      dist_to_nearest_interactive_object <
      std::max(kMinDistToIgnoreObjectInMeter,
               ego_speed * kMinTimeToIgnoreObjectInSec);
  const double comfort_lateral_jerk =
      has_near_object ? kHighInterationLateralJerk : kLowInterationLateralJerk;
  debug_oss << ", comfort jerk: " << comfort_lateral_jerk << "\n";

  const double dist_to_last_cl_pt =
      (lane_change_metadata.has_last_lane_change_chance_point()
           ? lane_change_metadata.last_lane_change_chance_point()
                 .dist_from_ego()
           : lane_change_metadata
                 .remaining_distance_for_consecutive_lane_change()) /
      lane_change_metadata.consecutive_lane_change_count();

  const std::optional<math::Range1d>& comfort_priority_option =
      GetLongitudinalSpan(
          lc_duration_reduction, ego_state, lane_change_metadata,
          comfort_lateral_jerk, tree_profile, resampled_ref_profile,
          dist_to_last_cl_pt, earliest_lc_start_ra_arclength,
          dist_to_target_region_queue_ahead_of_ego_in_future,
          kMaxStartDelayedTimeInSec, extended_source_region_start_to_ego_ra,
          feasible_lc_range_on_extended_source_region,
          extended_source_region_curve, target_region_curve,
          speed_bound_generator, lc_direction, elc_decision, stuck_signal,
          discomfort_ix, comfort_option_crossing_pos, debug_oss);

  lane_change_longitudinal_span_options.feasible_lc_range =
      feasible_lc_range_on_extended_source_region;
  if (comfort_priority_option.has_value()) {
    lane_change_longitudinal_span_options.lc_options_map.emplace(
        pb::LaneChangeLongitudinalSpanOptionsDebug::kComfortPriority,
        std::move(comfort_priority_option.value()));
    lane_change_longitudinal_span_options.lc_options_crossing_pos_map.emplace(
        pb::LaneChangeLongitudinalSpanOptionsDebug::kComfortPriority,
        comfort_option_crossing_pos);
  } else {
    rt_event::PostRtEvent<rt_event::planner::PlannerLaneChangeNoOption>();
  }

  return lane_change_longitudinal_span_options;
}

std::optional<math::Range1d> GetBackupLongitudinalSpan(
    const pb::LaneChangeMetadata& lane_change_metadata,
    const RobotState& ego_state, const pb::LaneChangeMode& lc_direction,
    const LaneFollowSequenceCurve& extended_source_region_curve,
    const LaneFollowSequenceCurve& target_region_curve,
    const math::Range1d& backup_lc_range_on_extended_source_region,
    const double extended_source_region_start_to_ego_ra,
    const double max_start_delayed_time, double& lc_crossing_pos,
    std::ostringstream& debug_oss) {
  // Trajectory may turn slower than the reference path.
  constexpr double kMinStartDelayedDistInMeter = 0.5;
  constexpr double kMinSolidLineBufferInMeter = 2.0;
  constexpr double kCrossingLaneTimeBufferInSec = 0.8;

  // Prepare distance for later use.
  const double dist_to_lc_start = std::max(
      {kMinStartDelayedDistInMeter,
       max_start_delayed_time * ego_state.plan_init_state_snapshot().speed(),
       backup_lc_range_on_extended_source_region.start_pos -
           extended_source_region_start_to_ego_ra});
  const bool is_short_consecutive_lc =
      lane_change_metadata.consecutive_lane_change_count() > 1 &&
      lane_change_metadata.max_longitudinal_span_for_current_lc() > 0;
  const double dist_to_latest_lc_crossing_pt =
      std::min(backup_lc_range_on_extended_source_region.end_pos -
                   extended_source_region_start_to_ego_ra -
                   std::max(kMinSolidLineBufferInMeter,
                            ego_state.plan_init_state_snapshot().speed() *
                                kCrossingLaneTimeBufferInSec),
               is_short_consecutive_lc
                   ? lane_change_metadata.max_longitudinal_span_for_current_lc()
                   : std::numeric_limits<double>::infinity());

  // Generate a feasible backup lc option.
  return GenerateBackupLcOption(
      ego_state, extended_source_region_start_to_ego_ra, dist_to_lc_start,
      dist_to_latest_lc_crossing_pt, extended_source_region_curve,
      target_region_curve, lc_direction, lc_crossing_pos, debug_oss);
}

std::optional<math::Range1d> GenerateBackupLcOption(
    const RobotState& ego_state,
    const double extended_source_region_start_to_ego_ra,
    double dist_to_lc_start_pt, const double dist_to_latest_lc_crossing_pt,
    const LaneFollowSequenceCurve& extended_source_region_curve,
    const LaneFollowSequenceCurve& target_region_curve,
    const pb::LaneChangeMode& lc_direction, double& lc_crossing_pos,
    std::ostringstream& debug_oss) {
  // Get LC duration time with lateral jerk.
  constexpr double kBackupLcLateralJerkInMpsss = 0.5;
  constexpr double kMinBackupLcSpeedInMps = 3.0;
  const double ego_speed = std::max(
      ego_state.plan_init_state_snapshot().speed(), kMinBackupLcSpeedInMps);
  const std::optional<double>& lc_duration = ComputeLcDuration(
      /*lc_duration_reduction=*/0.0, ego_state, ego_speed,
      extended_source_region_start_to_ego_ra, lc_direction,
      extended_source_region_curve, target_region_curve, debug_oss,
      kBackupLcLateralJerkInMpsss);
  if (!lc_duration.has_value()) {
    debug_oss << __func__ << ": LC_BACKUP duration has no value."
              << "\n";
    return std::nullopt;
  }
  debug_oss << __func__ << ": LC_BACKUP duration = " << lc_duration.value();

  const double dist_to_lc_end_pt =
      dist_to_lc_start_pt + ego_speed * lc_duration.value();
  const double dist_to_lc_crossing_pt =
      dist_to_lc_start_pt + ego_speed * 0.5 * lc_duration.value();
  if (dist_to_lc_crossing_pt > dist_to_latest_lc_crossing_pt) {
    debug_oss << ", option cross lane after " << dist_to_lc_crossing_pt
              << "m, only have " << dist_to_latest_lc_crossing_pt
              << "m remaining. Not enough!"
              << "\n";
    return std::nullopt;
  }
  lc_crossing_pos =
      extended_source_region_start_to_ego_ra + dist_to_lc_crossing_pt;
  debug_oss << ", option available."
            << "\n";
  return math::Range1d(
      extended_source_region_start_to_ego_ra + dist_to_lc_start_pt,
      extended_source_region_start_to_ego_ra + dist_to_lc_end_pt);
}

std::optional<math::Range1d> GetLongitudinalSpan(
    const double lc_duration_reduction, const RobotState& ego_state,
    const pb::LaneChangeMetadata& lane_change_metadata,
    const double comfort_lateral_jerk, const speed::Profile& speed_profile,
    const speed::Profile& resampled_ref_profile,
    const double dist_to_last_cl_pt, const double dist_to_lc_start,
    const double dist_to_target_region_queue_ahead_of_ego_in_future,
    const double max_start_delayed_time,
    const double extended_source_region_start_to_ego_ra,
    const math::Range1d& feasible_lc_range_on_extended_source_region,
    const LaneFollowSequenceCurve& extended_source_region_curve,
    const LaneFollowSequenceCurve& target_region_curve,
    const speed::SpeedBoundGenerator& speed_bound_generator,
    const pb::LaneChangeMode& lc_direction,
    const pb::ElectiveLaneChangeDecision& elc_decision,
    const pb::StuckSignal& stuck_signal, const int discomfort_ix,
    double& lc_crossing_pos, std::ostringstream& debug_oss) {
  DCHECK(!speed_profile.empty());
  DCHECK_GE(dist_to_lc_start, 0.0);
  // Trajectory may turn slower than the reference path.
  constexpr double kMinSolidLineBufferInMeter = 2.0;
  constexpr double kQueueFollowBufferInMeter = 2.0;
  constexpr double kCrossingLaneTimeBufferInSec = 0.8;
  // Step 0: prepare lc_start_state & dist_to_latest_lc_crossing_pt
  // for later use.
  const std::optional<speed::State>& lc_start_state = GetStateAtArcLength(
      speed_profile, dist_to_lc_start, /*allow_extension=*/true);
  if (!lc_start_state.has_value()) {
    debug_oss << __func__ << " lc_start_state has no value."
              << "\n";
    return std::nullopt;
  }
  const bool is_short_consecutive_lc =
      lane_change_metadata.consecutive_lane_change_count() > 1 &&
      lane_change_metadata.max_longitudinal_span_for_current_lc() > 0;
  const double dist_to_static_latest_lc_crossing_pt =
      std::min(feasible_lc_range_on_extended_source_region.end_pos -
                   extended_source_region_start_to_ego_ra -
                   std::max(kMinSolidLineBufferInMeter,
                            lc_start_state->v * kCrossingLaneTimeBufferInSec),
               is_short_consecutive_lc
                   ? lane_change_metadata.max_longitudinal_span_for_current_lc()
                   : std::numeric_limits<double>::infinity());
  const double dist_to_dynamic_latest_lc_end_pt =
      dist_to_target_region_queue_ahead_of_ego_in_future -
      kQueueFollowBufferInMeter;
  const double dist_to_dynamic_latest_lc_crossing_pt =
      0.5 * (dist_to_lc_start + dist_to_dynamic_latest_lc_end_pt);
  const double dist_to_latest_lc_crossing_pt =
      std::min(dist_to_static_latest_lc_crossing_pt,
               dist_to_dynamic_latest_lc_crossing_pt);
  if (dist_to_dynamic_latest_lc_crossing_pt <
      dist_to_static_latest_lc_crossing_pt) {
    rt_event::PostRtEvent<rt_event::planner::PlannerLaneChangeFasterForQueue>(
        absl::StrFormat("should end before queue within: %.2fm",
                        dist_to_dynamic_latest_lc_end_pt));
  }

  // Step 1: return comfort lc option if feasible.
  const std::optional<math::Range1d>& comfort_option = GenerateLcOptionByJerk(
      lc_duration_reduction, ego_state, pb::LaneChangeOptionType::LC_COMFORT,
      comfort_lateral_jerk, speed_profile, resampled_ref_profile,
      lc_start_state.value(), dist_to_lc_start,
      dist_to_static_latest_lc_crossing_pt, max_start_delayed_time,
      /*mutable_solid_line_length=*/0.0, extended_source_region_start_to_ego_ra,
      extended_source_region_curve, target_region_curve, speed_bound_generator,
      lc_direction, elc_decision, stuck_signal, discomfort_ix, lc_crossing_pos,
      debug_oss);
  if (comfort_option.has_value() &&
      comfort_option.value().end_pos < extended_source_region_start_to_ego_ra +
                                           dist_to_dynamic_latest_lc_end_pt) {
    return comfort_option.value();
  }

  // Step 2: calculate discomfort_lateral_jerk & mutable_solid_line_length
  // to help deciding final option.
  // Use distance to last cl pt to decide discomfort_lateral_jerk.
  constexpr double kMinUrgentLateralJerk = 1.23;
  constexpr double kMaxUrgentLateralJerk = 2.4;
  constexpr double kMinDistanceOfUrgentLC = 300.0;
  const double urgency_score = math::GetInterpolationRatio(
      kMinDistanceOfUrgentLC, 0.0, dist_to_last_cl_pt,
      /*allow_extension =*/false);
  const double discomfort_lateral_jerk = math::LinearInterpolate(
      kMinUrgentLateralJerk, kMaxUrgentLateralJerk, urgency_score);
  // Set at most 3 meters of solid line to be mutable.
  constexpr double kMinUrgencyToExpandSolidLine = 0.8;
  constexpr double kMaxMutableDistInMeter = 3.0;
  const double mutable_solid_line_length =
      math::GetLinearInterpolatedY(kMinUrgencyToExpandSolidLine, 1.0, 0.0,
                                   kMaxMutableDistInMeter, urgency_score);

  // Step 3: if discomfort lc option is not feasible or ends a little after
  // latest_lc_crossing_pt, return it.
  const std::optional<math::Range1d>& discomfort_lc_option =
      GenerateLcOptionByJerk(
          lc_duration_reduction, ego_state,
          pb::LaneChangeOptionType::LC_DISCOMFORT, discomfort_lateral_jerk,
          speed_profile, resampled_ref_profile, lc_start_state.value(),
          dist_to_lc_start, dist_to_static_latest_lc_crossing_pt,
          max_start_delayed_time, mutable_solid_line_length,
          extended_source_region_start_to_ego_ra, extended_source_region_curve,
          target_region_curve, speed_bound_generator, lc_direction,
          elc_decision, stuck_signal, discomfort_ix, lc_crossing_pos,
          debug_oss);
  if (!discomfort_lc_option.has_value() ||
      lc_crossing_pos > extended_source_region_start_to_ego_ra +
                            dist_to_latest_lc_crossing_pt) {
    if (discomfort_lc_option.has_value()) {
      rt_event::PostRtEvent<
          rt_event::planner::PlannerLaneChangeDiscomfortOption>(absl::StrFormat(
          "discomfort_lateral_jerk: %.2f", discomfort_lateral_jerk));
    }
    return discomfort_lc_option;
  }

  // Step 4: if discomfort lc option ends before latest_lc_crossing_pt,
  // return greedy lc option.
  return GenerateLcOptionByJerk(
      lc_duration_reduction, ego_state, pb::LaneChangeOptionType::LC_GREEDY,
      /*lateral_jerk=*/0.0, speed_profile, resampled_ref_profile,
      lc_start_state.value(), dist_to_lc_start, dist_to_latest_lc_crossing_pt,
      max_start_delayed_time, mutable_solid_line_length,
      extended_source_region_start_to_ego_ra, extended_source_region_curve,
      target_region_curve, speed_bound_generator, lc_direction, elc_decision,
      stuck_signal, discomfort_ix, lc_crossing_pos, debug_oss);
}

std::optional<math::Range1d> GenerateLcOptionByJerk(
    const double lc_duration_reduction, const RobotState& ego_state,
    const pb::LaneChangeOptionType& option_type, const double lateral_jerk,
    const speed::Profile& speed_profile,
    const speed::Profile& resampled_ref_profile,
    const speed::State& lc_start_state, const double dist_to_lc_start,
    const double dist_to_latest_lc_crossing_pt,
    const double max_start_delayed_time, const double mutable_solid_line_length,
    const double extended_source_region_start_to_ego_ra,
    const LaneFollowSequenceCurve& extended_source_region_curve,
    const LaneFollowSequenceCurve& target_region_curve,
    const speed::SpeedBoundGenerator& speed_bound_generator,
    const pb::LaneChangeMode& lc_direction,
    const pb::ElectiveLaneChangeDecision& elc_decision,
    const pb::StuckSignal& stuck_signal, const int discomfort_ix,
    double& lc_crossing_pos, std::ostringstream& debug_oss) {
  // Check latest lane change crossing state for greedy option.
  const std::optional<speed::State>& latest_lc_crossing_state =
      option_type == pb::LaneChangeOptionType::LC_GREEDY
          ? GetStateAtArcLength(speed_profile, dist_to_latest_lc_crossing_pt,
                                /*allow_extension=*/true)
          : std::nullopt;

  // Get LC duration time with lateral jerk.
  const std::optional<double>& lc_duration =
      latest_lc_crossing_state.has_value()
          ? 2 * (latest_lc_crossing_state->t - lc_start_state.t)
          : ComputeLcDuration(
                lc_duration_reduction, ego_state, lc_start_state.v,
                dist_to_lc_start + extended_source_region_start_to_ego_ra,
                lc_direction, extended_source_region_curve, target_region_curve,
                debug_oss, lateral_jerk);
  if (!lc_duration.has_value() || std::isinf(lc_duration.value())) {
    debug_oss << __func__ << ": " << LaneChangeOptionType_Name(option_type)
              << " duration has no value."
              << "\n";
    return std::nullopt;
  }
  debug_oss << __func__ << ": " << LaneChangeOptionType_Name(option_type)
            << " duration = " << lc_duration.value();

  // Calculate longitudinal span using duration time and speed profile.
  speed::State lc_end_state;
  speed::State lc_crossing_state;
  if (!GenerateLCStateByTime(lc_start_state, lc_duration.value(), speed_profile,
                             resampled_ref_profile, speed_bound_generator,
                             elc_decision, stuck_signal, discomfort_ix,
                             lc_end_state, lc_crossing_state, debug_oss)) {
    debug_oss << ", option out of profile range!"
              << "\n";
    return std::nullopt;
  }

  // TODO(ryanliyiyang): check if we still need this clamp for jerk model.
  const double dist_to_lc_end_pt =
      dist_to_lc_start + std::clamp(lc_end_state.x - dist_to_lc_start,
                                    kMinLongitudinalSpanToDoLaneChangeInMeter,
                                    kMaxLongitudinalSpanToDoLaneChangeInMeter);
  const double dist_to_lc_crossing_pt =
      dist_to_lc_start + std::clamp(lc_crossing_state.x - dist_to_lc_start,
                                    kMinLongitudinalSpanToDoLaneChangeInMeter,
                                    kMaxLongitudinalSpanToDoLaneChangeInMeter);
  constexpr double kDistTolerenceInMeter = 1e-3;
  const bool can_lc_satisfy_feasible_range =
      dist_to_lc_crossing_pt < dist_to_latest_lc_crossing_pt +
                                   mutable_solid_line_length +
                                   kDistTolerenceInMeter;
  const double start_delayed_dist =
      option_type == pb::LaneChangeOptionType::LC_COMFORT
          ? CalculateStartDelayedDist(
                lc_start_state, dist_to_lc_start, dist_to_lc_crossing_pt,
                dist_to_latest_lc_crossing_pt, max_start_delayed_time)
          : 0.0;
  if (!can_lc_satisfy_feasible_range) {
    debug_oss << ", option cross lane after " << dist_to_lc_crossing_pt
              << "m, only have "
              << dist_to_latest_lc_crossing_pt + mutable_solid_line_length
              << "m remaining. Not enough!"
              << "\n";
    return std::nullopt;
  }
  lc_crossing_pos = dist_to_lc_crossing_pt +
                    extended_source_region_start_to_ego_ra + start_delayed_dist;
  debug_oss << ", option available. "
            << "\n";
  return math::Range1d(
      dist_to_lc_start + extended_source_region_start_to_ego_ra +
          start_delayed_dist,
      dist_to_lc_end_pt + extended_source_region_start_to_ego_ra +
          start_delayed_dist);
}

std::optional<double> ComputeLcDuration(
    const double lc_duration_reduction, const RobotState& ego_state,
    const double lc_start_ego_speed,
    const double extended_source_region_start_to_ego_ra,
    const pb::LaneChangeMode& lc_direction,
    const LaneFollowSequenceCurve& extended_source_region_curve,
    const LaneFollowSequenceCurve& target_region_curve,
    std::ostringstream& debug_oss,
    const std::optional<double>& max_lateral_jerk) {
  // Consider the sign of curvature of the extended source region and
  // lc_direction. When turning left, the curvature is positive. When turning
  // right, the curvature is negative.
  DCHECK(lc_direction != pb::LaneChangeMode::NONE_LANE_CHANGE);
  const double sign =
      lc_direction == pb::LaneChangeMode::LEFT_LANE_CHANGE ? 1.0 : -1.0;
  const math::Curve2d center_line(
      extended_source_region_curve.nominal_path().points(),
      math::pb::Interpolation1dType::kCSpline);
  const double centripetal_accel = center_line.GetInterpSignedCurvature(
                                       extended_source_region_start_to_ego_ra) *
                                   std::pow(lc_start_ego_speed, 2.0);
  constexpr double kDesirableMaxLateralAccelInMpss = 3.0;  // m/s^2
  const double max_lateral_accel =
      kDesirableMaxLateralAccelInMpss - sign * centripetal_accel;
  if (max_lateral_accel < 0.0) {
    // The road is too curvy to make a comfortable lane change.
    // For example, u-turn.
    // TODO(lane-change): see whether we want to handle LC on curvy road like
    // u-turns.
    debug_oss << __func__ << " ComputeLcDuration failed ! "
              << " max_lateral_accel " << max_lateral_accel;
    return std::nullopt;
  }

  // Prepare initial states to calculate duration.
  const math::geometry::Point2d& ego_ra_point =
      ego_state.plan_init_state_snapshot().rear_axle_position();
  const double signed_dist_to_source_lane_center =
      extended_source_region_curve.nominal_path()
          .GetProximity(ego_ra_point, math::pb::UseExtensionFlag::kForbid)
          .signed_dist;
  const double lateral_dist =
      0.5 * (extended_source_region_curve.GetLaneWidthAtPoint(ego_ra_point) +
             target_region_curve.GetLaneWidthAtPoint(ego_ra_point)) -
      sign * signed_dist_to_source_lane_center;
  if (lateral_dist < math::constants::kEpsilon) {
    debug_oss << __func__ << "Ego already beyond target lane center.";
    return 0.0;
  }

  const double current_wheel_angle =
      ego_state.plan_init_state_snapshot().heading() +
      ego_state.plan_init_state_snapshot().steering();
  const double current_lane_angle =
      extended_source_region_curve.nominal_path().GetInterpTheta(
          extended_source_region_start_to_ego_ra);
  const double init_lateral_speed =
      sign * lc_start_ego_speed *
      std::sin(math::AngleDiff(current_wheel_angle, current_lane_angle));

  // Calculate from jerk to time.
  if (max_lateral_jerk.has_value()) {
    return GetLaneChangeDurationTime(lateral_dist, init_lateral_speed,
                                     max_lateral_accel,
                                     max_lateral_jerk.value()) -
           lc_duration_reduction;
  }

  // Original function
  return GetLaneChangeComfortTimeWindow(lateral_dist, max_lateral_accel) -
         lc_duration_reduction;
}

double CalculateStartDelayedDist(const speed::State& lc_start_state,
                                 const double dist_to_lc_start,
                                 const double dist_to_lc_crossing_pt,
                                 const double dist_to_latest_lc_crossing_pt,
                                 const double max_start_delayed_time) {
  // Use remaining distance to delay cl_start for better comfort.
  const double remaining_dist =
      dist_to_latest_lc_crossing_pt - dist_to_lc_crossing_pt;
  constexpr double kMinStartDelayedDistInMeter = 0.5;
  return std::clamp(std::max(max_start_delayed_time * lc_start_state.v,
                             kMinStartDelayedDistInMeter) -
                        dist_to_lc_start,
                    0.0, remaining_dist);
}

std::vector<int64_t> ComputeAgentsToIgnoreNudgeDuringLaneChange(
    const LaneChangeInstance& lane_change_instance,
    const bool can_trigger_crawl, const bool should_creep,
    const LaneChangeObjectInfoList* source_region_object_info_list,
    const LaneChangeObjectInfoList* target_region_object_info_list,
    const RobotState& robot_state,
    const double actual_cross_lane_end_arc_length_on_target_lane_seq,
    const double ego_ra_arc_length_on_target_region,
    const speed::pb::SpeedSolverLcGuideSeed& cross_lane_guide_from_speed,
    std::ostringstream& debug_oss) {
  if (target_region_object_info_list == nullptr) {
    return {};
  }
  std::vector<int64_t> source_region_ids;
  if (source_region_object_info_list != nullptr) {
    source_region_ids = source_region_object_info_list->ids();
  }
  std::vector<int64_t> objects_to_ignore;
  objects_to_ignore.resize(target_region_object_info_list->size());
  const vehicle_model::pb::AxleRectangularMeasurement& ego_shape =
      robot_state.car_model_with_shape().shape_measurement();
  const RobotStateSnapshot& current_state_snapshot =
      robot_state.current_state_snapshot();
  const double ra_to_fb_dist =
      ego_shape.length() - ego_shape.rear_bumper_to_rear_axle();

  for (const int64_t target_agent_id : target_region_object_info_list->ids()) {
    const LaneChangeObjectInfo* target_agent =
        target_region_object_info_list->Find(target_agent_id);
    DCHECK(target_agent != nullptr);
    if ((target_agent->is_parked_car ||
         target_agent->is_likely_parked_car_from_score ||
         !(target_agent->is_vehicle || target_agent->is_cyclist))) {
      continue;
    }
    if (CanIgnoreAgentLaterally(
            can_trigger_crawl, should_creep, source_region_ids, *target_agent,
            current_state_snapshot, cross_lane_guide_from_speed.tail_obj_id(),
            cross_lane_guide_from_speed.lead_obj_id(),
            ego_shape.rear_bumper_to_rear_axle(), ra_to_fb_dist,
            ego_ra_arc_length_on_target_region,
            actual_cross_lane_end_arc_length_on_target_lane_seq,
            lane_change_instance, debug_oss)) {
      objects_to_ignore.emplace_back(target_agent->id);
      rt_event::PostRtEvent<
          rt_event::planner::PlannerLaneChangeIgnoreNudgeAgent>(
          std::to_string(target_agent->id));
    }
  }
  return objects_to_ignore;
}

bool CanIgnoreAgentLaterally(
    const bool can_trigger_crawl, const bool should_creep,
    const std::vector<int64_t>& source_region_ids,
    const LaneChangeObjectInfo& target_agent,
    const RobotStateSnapshot& current_state_snapshot, const int64 tail_obj_id,
    const int64 lead_obj_id, const double ego_rb_to_ra,
    const double ego_ra_to_fb, const double ego_ra_arc_length_on_target_region,
    const double ego_cross_lane_end_arc_length_on_target_region,
    const LaneChangeInstance& lane_change_instance,
    std::ostringstream& debug_oss) {
  const math::geometry::PolylineCurve2d& extended_source_region_nominal_path =
      lane_change_instance.extended_source_region().nominal_path();
  const math::geometry::PolylineCurve2d& target_region_nominal_path =
      lane_change_instance.target_region().nominal_path();

  if (target_agent.overlap_regions.empty()) {
    debug_oss << __func__ << ": Ignore id " << target_agent.id
              << " with no overlap. "
              << "\n";
    return true;
  }
  const double ego_speed = current_state_snapshot.speed();

  const math::geometry::PolylineCurve2d& cross_lane_curve =
      lane_change_instance.GetCrossLaneCurve();
  const int sign =
      lane_change_instance.direction() == pb::LaneChangeMode::LEFT_LANE_CHANGE
          ? -1
          : 1;
  // Check if both ego vehicle and target agent are nearly stationary.
  // If both speeds are below a predefined threshold, ignore the target agent.
  // This is to avoid unnecessary nudge for nearly stationary object.
  constexpr double kNearStaticObjectSpeedThreshold = 1.0;
  constexpr double kStaticObjectSpeedThreshold = 0.5;
  const double center_point_signed_dist =
      sign * cross_lane_curve
                 .GetProximity(target_agent.center_2d,
                               math::pb::UseExtensionFlag::kForbid)
                 .signed_dist;
  if (target_agent.speed < kNearStaticObjectSpeedThreshold &&
      std::find(source_region_ids.begin(), source_region_ids.end(),
                target_agent.id) != source_region_ids.end() &&
      center_point_signed_dist > 0.0) {
    debug_oss << __func__ << ": Don't Ignore id " << target_agent.id
              << " center dist " << center_point_signed_dist
              << " static agent in the target lane and source lane "
              << "\n";
    return false;
  }
  if ((ego_speed < kNearStaticObjectSpeedThreshold &&
       target_agent.speed < kNearStaticObjectSpeedThreshold) ||
      ego_speed < kStaticObjectSpeedThreshold) {
    debug_oss << __func__ << ": Ignore id " << target_agent.id
              << " ego & agent speed low, or ego is static "
              << "\n";
    return true;
  }
  const speed::pb::OverlapRegion& first_gap_align_overlap_region =
      target_agent.overlap_regions[0];
  const double start_padded_relative_time_in_sec =
      first_gap_align_overlap_region.start_padded_relative_time_in_sec();

  const double ego_nominal_path_theta =
      target_region_nominal_path.GetInterpTheta(
          ego_ra_arc_length_on_target_region);
  const double ego_delta_theta =
      math::AngleDiff(ego_nominal_path_theta, current_state_snapshot.heading());
  const double vel_x_ego =
      current_state_snapshot.speed() * std::sin(ego_delta_theta);
  const double vel_y_ego =
      current_state_snapshot.speed() * std::cos(ego_delta_theta);

  // When the agent is outside of the target region, it should be behind the
  // target region, so use extended source region for computation.
  const math::ProximityQueryInfo& agent_proximity_on_target_region =
      target_region_nominal_path.GetProximity(
          target_agent.center_2d, math::pb::UseExtensionFlag::kForbid);
  const math::ProximityQueryInfo& agent_proximity_on_extended_source_region =
      extended_source_region_nominal_path.GetProximity(
          target_agent.center_2d, math::pb::UseExtensionFlag::kForbid);
  const bool use_target_region_for_agent_proximity =
      agent_proximity_on_target_region.relative_position ==
      math::RelativePosition::kWithIn;
  const math::geometry::PolylineCurve2d& nominal_path_for_agent_proximity =
      use_target_region_for_agent_proximity
          ? target_region_nominal_path
          : extended_source_region_nominal_path;
  const math::ProximityQueryInfo& agent_proximity =
      use_target_region_for_agent_proximity
          ? agent_proximity_on_target_region
          : agent_proximity_on_extended_source_region;

  // Compute agent information with proximity info.
  const double agent_nominal_path_theta =
      nominal_path_for_agent_proximity.GetInterpTheta(
          agent_proximity.arc_length);
  const double agent_delta_theta =
      math::AngleDiff(agent_nominal_path_theta, target_agent.heading);
  const double vel_x_agent = target_agent.speed * std::sin(agent_delta_theta);
  const double vel_y_agent = target_agent.speed * std::cos(agent_delta_theta);

  const double agent_length = target_agent.length;
  const double agent_center_to_ego_ra = target_agent.object_ego_dist;
  //  ego_rb_to_ra: >0,  ego_ra_to_fb: >0
  const double agent_fb_to_ego_rb =
      agent_center_to_ego_ra + 0.5 * agent_length + ego_rb_to_ra;
  const double agent_rb_to_ego_fb =
      agent_center_to_ego_ra - 0.5 * agent_length - ego_ra_to_fb;
  const double agent_center_arc_length_on_target_region =
      target_agent.arc_length_on_region;

  debug_oss << __func__ << ": id " << target_agent.id << " "
            << DUMP_TO_STREAM(start_padded_relative_time_in_sec,
                              agent_center_arc_length_on_target_region,
                              ego_rb_to_ra, ego_ra_to_fb)
            << "\n";
  // When the tail agent is behind ego's rear bumper, we ignore it.
  if (tail_obj_id != 0 &&
      (target_agent.id == tail_obj_id && agent_fb_to_ego_rb < 0.0)) {
    debug_oss << __func__ << ": Ignore id " << target_agent.id
              << " the target tail agent that is behind ego's rear bumper : "
              << "\n";
    return true;
  }
  // When the lead agent is before ego's front bumper, we ignore it.
  if ((lead_obj_id != 0 && !target_agent.is_primary_stationary) &&
      (target_agent.id == lead_obj_id && agent_rb_to_ego_fb > 0.0)) {
    debug_oss << __func__ << ": Ignore id " << target_agent.id
              << " the target lead agent that is before ego's front bumper : "
              << "\n";
    return true;
  }
  // Check if agent should be ignored laterally for normal lane change.
  if (!can_trigger_crawl && !should_creep) {
    return CanIgnoreAgentLaterallyForNormalLc(
        target_agent.id, current_state_snapshot, lane_change_instance,
        vel_y_ego, vel_y_agent, ego_ra_arc_length_on_target_region,
        ego_cross_lane_end_arc_length_on_target_region,
        agent_center_arc_length_on_target_region, agent_fb_to_ego_rb,
        agent_rb_to_ego_fb, debug_oss);
  }

  // Check if agent should be ignored laterally for high reward lc.
  constexpr double kLatestOverlapAppearanceTimeInSecs = 1.0;
  if (start_padded_relative_time_in_sec > kLatestOverlapAppearanceTimeInSecs) {
    debug_oss << __func__ << ": Don't ignore id " << target_agent.id
              << " appears after 1s : "
              << "\n";
    return false;
  }
  constexpr double
      kMinSafetyLongitudinalBufferBetweenEgoAndAgentDuringHighRewardLCInMeters =
          1.0;
  // When the agent is parallel with the ego currently, don't ignore.
  if (!should_creep &&
      agent_fb_to_ego_rb >
          -kMinSafetyLongitudinalBufferBetweenEgoAndAgentDuringHighRewardLCInMeters &&
      agent_rb_to_ego_fb <
          kMinSafetyLongitudinalBufferBetweenEgoAndAgentDuringHighRewardLCInMeters) {
    debug_oss << __func__ << ": Don't ignore id " << target_agent.id
              << " is almost parallel with the ego : "
              << DUMP_TO_STREAM(agent_fb_to_ego_rb, agent_rb_to_ego_fb) << "\n";
    return false;
  }

  // When the agent and ego can keep lateral safe distance with moderate brake,
  // we ignore it.
  constexpr double kMinAccelDuringHighRewardLCInMpss = -2.0;
  const double time_to_brake =
      current_state_snapshot.speed() / kMinAccelDuringHighRewardLCInMpss;
  constexpr double
      kMinSafetyLateralBufferBetweenEgoAndAgentDuringHighRewardLCInMeters = 0.2;
  double lateral_buffer_during_high_reward_lc =
      kMinSafetyLateralBufferBetweenEgoAndAgentDuringHighRewardLCInMeters;
  // If the ego is nearly stationary, we only consider the min safety lateral
  // buffer.
  if (ego_speed > kStaticObjectSpeedThreshold) {
    constexpr double kSpeedLimitForLateralBufferDuringHighRewardLCInMps = 5.0;
    constexpr double kTimeBufferForLateralBufferDuringHighRewardLCInSec = 0.04;
    const double not_see_ego_lateral_buffer =
        agent_rb_to_ego_fb > -0.5 * agent_length
            ? kMinSafetyLateralBufferBetweenEgoAndAgentDuringHighRewardLCInMeters
            : 0.0;
    const double ego_speed_lateral_buffer =
        std::min(ego_speed,
                 kSpeedLimitForLateralBufferDuringHighRewardLCInMps) *
        kTimeBufferForLateralBufferDuringHighRewardLCInSec;
    const double agent_speed_lateral_buffer =
        std::min(target_agent.speed,
                 kSpeedLimitForLateralBufferDuringHighRewardLCInMps) *
        kTimeBufferForLateralBufferDuringHighRewardLCInSec;
    constexpr double kTimeBufferForLateralSpeedDiffDuringHighRewardLcInSec =
        1.0;

    const double lateral_speed_diff_buffer =
        std::max(-sign * (vel_x_ego - vel_x_agent), 0.0) *
        kTimeBufferForLateralSpeedDiffDuringHighRewardLcInSec;
    constexpr double
        kMaxSafetyLateralBufferBetweenEgoAndAgentDuringHighRewardLCInMeters =
            0.7;
    lateral_buffer_during_high_reward_lc = std::clamp(
        lateral_buffer_during_high_reward_lc +
            std::max(ego_speed_lateral_buffer, agent_speed_lateral_buffer) +
            lateral_speed_diff_buffer + not_see_ego_lateral_buffer,
        kMinSafetyLateralBufferBetweenEgoAndAgentDuringHighRewardLCInMeters,
        kMaxSafetyLateralBufferBetweenEgoAndAgentDuringHighRewardLCInMeters);
  }

  double agent_min_lateral_distance = -std::numeric_limits<double>::infinity();

  for (const math::geometry::Point2d& current_point :
       target_agent.contour.points()) {
    const double signed_dist =
        sign *
        cross_lane_curve
            .GetProximity(current_point, math::pb::UseExtensionFlag::kForbid)
            .signed_dist;
    agent_min_lateral_distance =
        std::max(agent_min_lateral_distance, signed_dist);
  }
  const double ego_distance_cross_curve =
      lane_change_instance.CalculateEgoFrontBumperCornerDistToCrossLaneCurve(
          current_state_snapshot);

  const double agent_ego_lateral_distance =
      (agent_min_lateral_distance - ego_distance_cross_curve) * sign;

  const double delta_x =
      vel_x_agent * time_to_brake - vel_x_ego * time_to_brake * 0.5;
  if (std::abs(delta_x + agent_ego_lateral_distance) >
      lateral_buffer_during_high_reward_lc) {
    debug_oss << __func__ << ": Ignore id " << target_agent.id
              << " during HighRewardLC "
              << DUMP_TO_STREAM(delta_x, agent_fb_to_ego_rb, vel_x_ego,
                                vel_x_agent, agent_ego_lateral_distance,
                                lateral_buffer_during_high_reward_lc)
              << "\n";
    return true;
  }
  // When the agent and ego can keep longitudinal safe distance with moderate
  // brake, we ignore it.
  const double delta_y =
      vel_y_agent * time_to_brake - vel_y_ego * time_to_brake * 0.5;
  const bool is_not_close_to_agent_behind =
      agent_fb_to_ego_rb + delta_y +
              vel_y_ego * vel_y_ego * 0.5 / kMinAccelDuringHighRewardLCInMpss <
          -kMinSafetyLongitudinalBufferBetweenEgoAndAgentDuringHighRewardLCInMeters &&
      agent_fb_to_ego_rb < 0.0;
  const bool is_not_close_to_agent_front =
      agent_rb_to_ego_fb + delta_y >
      kMinSafetyLongitudinalBufferBetweenEgoAndAgentDuringHighRewardLCInMeters;
  if (is_not_close_to_agent_behind || is_not_close_to_agent_front) {
    debug_oss << __func__ << ": Ignore id " << target_agent.id
              << " during HighRewardLC "
              << DUMP_TO_STREAM(agent_fb_to_ego_rb, agent_rb_to_ego_fb, delta_y)
              << "\n";
    return true;
  }

  debug_oss << __func__ << ": Don't ignore id " << target_agent.id
            << " during HighRewardLC "
            << DUMP_TO_STREAM(delta_x, delta_y, agent_fb_to_ego_rb, vel_y_ego,
                              vel_y_agent, vel_x_ego, vel_x_agent,
                              agent_ego_lateral_distance)
            << "\n";
  return false;
}

bool CanIgnoreAgentLaterallyForNormalLc(
    const int64 target_agent_id,
    const RobotStateSnapshot& current_state_snapshot,
    const LaneChangeInstance& lane_change_instance, const double vel_y_ego,
    const double vel_y_agent, const double ego_ra_arc_length_on_target_region,
    const double ego_cross_lane_end_arc_length_on_target_region,
    const double agent_center_arc_length_on_target_region,
    const double agent_fb_to_ego_rb, const double agent_rb_to_ego_fb,
    std::ostringstream& debug_oss) {
  const double ego_encroached_distance = -std::min(
      0.0,
      lane_change_instance.CalculateEgoFrontBumperCornerDistToCrossLaneCurve(
          current_state_snapshot));
  const double ego_agent_delta_vel_y = vel_y_ego - vel_y_agent;
  constexpr double kMinTTCForAgentsToIgnoreInSec = 3.0;
  constexpr double
      kMinEgoEncroachedDistanceToConsiderIgnoringNearbyAgentInMeter = 0.8;

  // We use TTC to check whether an agent ahead can be ignored when:
  // 1. The agent can reach actual cross lane end arc length at the time when
  // ego arrives there, or
  // 2. Ego has encroached the target lane for 0.8m.
  const double ego_cross_lane_end_time =
      std::max(0.0, (ego_cross_lane_end_arc_length_on_target_region -
                     ego_ra_arc_length_on_target_region) /
                        std::max(vel_y_ego, math::constants::kEpsilon));
  const double agent_center_arc_length_at_ego_cross_lane_end_time =
      agent_center_arc_length_on_target_region +
      vel_y_agent * ego_cross_lane_end_time;
  if (agent_rb_to_ego_fb > 0.0 &&
      (agent_center_arc_length_at_ego_cross_lane_end_time >
           ego_cross_lane_end_arc_length_on_target_region ||
       ego_encroached_distance >
           kMinEgoEncroachedDistanceToConsiderIgnoringNearbyAgentInMeter)) {
    const double time_to_collision_with_agent_ahead =
        ego_agent_delta_vel_y > math::constants::kEpsilon
            ? agent_rb_to_ego_fb / ego_agent_delta_vel_y
            : std::numeric_limits<double>::infinity();
    const bool should_ignore_agent =
        time_to_collision_with_agent_ahead > kMinTTCForAgentsToIgnoreInSec;
    if (should_ignore_agent) {
      debug_oss << __func__ << ": Ignore id " << target_agent_id;
    } else {
      debug_oss << __func__ << ": Don't ignore id " << target_agent_id;
    }
    debug_oss << ", "
              << DUMP_TO_STREAM(
                     agent_center_arc_length_at_ego_cross_lane_end_time,
                     ego_cross_lane_end_arc_length_on_target_region,
                     agent_rb_to_ego_fb, ego_encroached_distance,
                     ego_agent_delta_vel_y, time_to_collision_with_agent_ahead)
              << "\n";
    return should_ignore_agent;
  }

  // We use TTC to check whether an agent behind can be ignored when:
  // 1. The agent is well behind ego (>10m), or
  // 2. Ego has encroached the target lane.
  constexpr double kMaxObjectEgoDistanceForAgentsBehindToIgnoreInMeter = -10.0;
  if (agent_fb_to_ego_rb < 0.0 &&
      (agent_fb_to_ego_rb <
           kMaxObjectEgoDistanceForAgentsBehindToIgnoreInMeter ||
       ego_encroached_distance >
           kMinEgoEncroachedDistanceToConsiderIgnoringNearbyAgentInMeter)) {
    const double time_to_collision_with_agent_behind =
        ego_agent_delta_vel_y < -math::constants::kEpsilon
            ? agent_fb_to_ego_rb / ego_agent_delta_vel_y
            : std::numeric_limits<double>::infinity();
    const bool should_ignore_agent =
        time_to_collision_with_agent_behind > kMinTTCForAgentsToIgnoreInSec;
    if (should_ignore_agent) {
      debug_oss << __func__ << ": Ignore id " << target_agent_id;
    } else {
      debug_oss << __func__ << ": Don't ignore id " << target_agent_id;
    }
    debug_oss << ", "
              << DUMP_TO_STREAM(agent_fb_to_ego_rb, ego_encroached_distance,
                                ego_agent_delta_vel_y,
                                time_to_collision_with_agent_behind)
              << "\n";
    return should_ignore_agent;
  }

  debug_oss << __func__ << ": Don't ignore id " << target_agent_id
            << DUMP_TO_STREAM(
                   agent_rb_to_ego_fb, agent_fb_to_ego_rb,
                   ego_cross_lane_end_arc_length_on_target_region,
                   agent_center_arc_length_at_ego_cross_lane_end_time,
                   ego_agent_delta_vel_y, ego_encroached_distance)
            << "\n";
  return false;
}

bool CanLCOptionSatisfyFeasibleRange(
    const LaneSequenceResult* lane_sequence_wo_lane_change,
    const math::Range1d& feasible_lc_range_on_extended_source_region,
    const math::Range1d& lc_option_range_on_extended_source_region,
    const double lc_option_crossing_arc_length_on_extended_source_region,
    const pb::LaneChangeLongitudinalSpanOptionsDebug::LcOptionType&
        lc_option_type,
    std::ostringstream& debug_oss) {
  debug_oss << "\t"
            << pb::LaneChangeLongitudinalSpanOptionsDebug::LcOptionType_Name(
                   lc_option_type)
            << ": " << lc_option_range_on_extended_source_region.start_pos
            << "~" << lc_option_range_on_extended_source_region.end_pos << ": ";
  //<< ", lc crossing pos: " << lc_crossing_pos << "\n";
  // Invalid feasible range.
  if (feasible_lc_range_on_extended_source_region.start_pos >
      feasible_lc_range_on_extended_source_region.end_pos) {
    debug_oss << "invalid feasible range.\n";
    return false;
  }

  // LC option's start is behind feasible range.
  if (feasible_lc_range_on_extended_source_region.start_pos >
      lc_option_range_on_extended_source_region.start_pos +
          math::constants::kEpsilon) {
    debug_oss << "start pos smaller than feasible range.\n";
    return false;
  }

  // Generally, we use LC crossing arc length to decide whether the LC option
  // can satisfy the feasible range's end. However, when the lane change is not
  // urgent, we will use the LC option's end for default/earliest type option to
  // prevent unnecessary lateral discomfort.
  bool is_lane_change_urgent = false;
  constexpr double kUrgentLCDistToLastLCPointInMeter = 200.0;
  if (lane_sequence_wo_lane_change != nullptr) {
    const pb::LaneSequenceCostFactors& cost_factors =
        lane_sequence_wo_lane_change->cost_factors;
    if (cost_factors.has_last_lane_change_chance_point() &&
        cost_factors.last_lane_change_chance_point().dist_from_ego() <
            kUrgentLCDistToLastLCPointInMeter) {
      is_lane_change_urgent = true;
    }
  }

  const bool use_lc_option_end_for_comparison =
      !is_lane_change_urgent &&
      lc_option_type !=
          (FLAGS_planning_use_comfort_priority_lane_change_option
               ? pb::LaneChangeLongitudinalSpanOptionsDebug::kComfortPriority
               : pb::LaneChangeLongitudinalSpanOptionsDebug::kStartDelayed);
  const double end_for_comparison =
      use_lc_option_end_for_comparison
          ? lc_option_range_on_extended_source_region.end_pos
          : lc_option_crossing_arc_length_on_extended_source_region;
  debug_oss << (use_lc_option_end_for_comparison ? "end pos" : "crossing pos")
            << " (" << end_for_comparison << ") ";

  constexpr double kMutableSolidLineInMeter = 1.0;
  const bool result =
      feasible_lc_range_on_extended_source_region.end_pos >
      end_for_comparison - math::constants::kEpsilon -
          (FLAGS_planning_use_comfort_priority_lane_change_option
               ? kMutableSolidLineInMeter
               : 0.0);
  debug_oss << (result ? "ok" : "too large") << "\n";
  return result;
}

double ComputeLCDurationReduction(
    const int64_t tail_obj_id,
    const LaneChangeObjectInfoList* target_region_object_info_list,
    const double ego_speed, const double rb_to_ra_dist,
    const int consecutive_lane_change_count, std::ostringstream& debug_oss) {
  constexpr double kLCDurationReductionForConsecutiveLaneChangeInSec = 1.0;
  const double lc_duration_reduction_for_consecutive_lc =
      consecutive_lane_change_count > 1
          ? kLCDurationReductionForConsecutiveLaneChangeInSec
          : 0.0;
  if (tail_obj_id == 0 || target_region_object_info_list == nullptr) {
    return lc_duration_reduction_for_consecutive_lc;
  }
  const LaneChangeObjectInfo* tail_obj_ptr =
      target_region_object_info_list->FindNonLeaving(tail_obj_id);
  if (tail_obj_ptr == nullptr || tail_obj_ptr->object_ego_dist > 0.0) {
    return lc_duration_reduction_for_consecutive_lc;
  }
  const double tail_speed = tail_obj_ptr->speed;
  const double tail_length = tail_obj_ptr->length;
  const double tail_ego_dis =
      tail_obj_ptr->object_ego_dist + 0.5 * tail_length + rb_to_ra_dist;
  const std::optional<double>& ttc_of_agent_behind_ego =
      ComputeTTCOfAgentBehindEgo(tail_obj_id, *target_region_object_info_list,
                                 -rb_to_ra_dist, ego_speed);
  debug_oss << __func__ << ": lc_duration_reduction calculation "
            << " tail_speed " << tail_speed << " tail_speed " << tail_speed
            << " tail_ego_dis " << tail_ego_dis
            << " lc_duration_reduction_for_consecutive_lc "
            << lc_duration_reduction_for_consecutive_lc << "\n";
  constexpr double kTailAgentTimeBufferInSec = 2.5;
  constexpr double kIdleTailAgentEgoAgenTimeToCollisionInSec = 3.7;
  constexpr double kMaxLCDurationReductionInSec = 1.5;
  constexpr double kLCDurationReductionForCloseDistInSec = 1.0;
  const double lc_duration_reduction_for_ttc =
      ttc_of_agent_behind_ego.has_value()
          ? math::Clamp(kIdleTailAgentEgoAgenTimeToCollisionInSec -
                            ttc_of_agent_behind_ego.value(),
                        0.0, kMaxLCDurationReductionInSec)
          : 0.0;
  const double lc_duration_reduction_for_close_dist =
      (-tail_ego_dis) / std::max(tail_speed, math::constants::kEpsilon) >
              kTailAgentTimeBufferInSec
          ? 0.0
          : kLCDurationReductionForCloseDistInSec;
  return math::Clamp(std::max(lc_duration_reduction_for_consecutive_lc,
                              lc_duration_reduction_for_close_dist +
                                  lc_duration_reduction_for_ttc),
                     0.0, kMaxLCDurationReductionInSec);
}

bool GenerateLCStateByTime(
    const speed::State& lc_start_state, const double lc_duration_in_sec,
    const speed::Profile& speed_profile,
    const speed::Profile& resampled_ref_profile,
    const speed::SpeedBoundGenerator& speed_bound_generator,
    const pb::ElectiveLaneChangeDecision& elc_decision,
    const pb::StuckSignal& stuck_signal, const int discomfort_ix,
    speed::State& lc_end_state, speed::State& lc_crossing_state,
    std::ostringstream& debug_oss) {
  // we use half of the time to LC to be the LC crossing lane marking time.
  const double lc_crossing_time = lc_start_state.t + lc_duration_in_sec * 0.5;
  const double lc_end_time = lc_start_state.t + lc_duration_in_sec;
  int unused_time_ix = -1;
  if (!IsStuckOrSlowMovingAgent(elc_decision, stuck_signal) ||
      FLAGS_planning_enable_lane_change_tree_search) {
    // Get lc end state from lane follow speed profile.
    if (!GetStateAtTime(speed_profile, lc_end_time, &lc_end_state,
                        unused_time_ix, true)) {
      debug_oss << __func__ << " lc end state has no value "
                << "\n";
      return false;
    }
    if (!GetStateAtTime(speed_profile, lc_crossing_time, &lc_crossing_state,
                        unused_time_ix, true)) {
      debug_oss << __func__ << " lc crossing state has no value "
                << "\n";
      return false;
    }
    return true;
  }
  // Ego is having stuck scene. The speed profile will be limited by stuck
  // object thus causing short LC corridor. We have a heuristic to increase it
  // for better LC success rate.
  if (!GetLcStatesOnBranchingProfile(lc_start_state.t, lc_duration_in_sec,
                                     speed_profile, resampled_ref_profile,
                                     speed_bound_generator, discomfort_ix,
                                     lc_end_state, lc_crossing_state)) {
    debug_oss << __func__ << " lc branch profile has no value "
              << "\n";
    return false;
  }
  return true;
}

LaneChangeLongitudinalSpanOptions LaneChangeDurationLongitudinalSpan(
    const RobotState& ego_state, const int64_t tail_obj_id,
    const LaneChangeObjectInfoList* target_region_object_info_list,
    const math::Range1d& feasible_lc_range_on_extended_source_region,
    const pb::LaneChangeMetadata& lane_change_metadata,
    const LaneFollowSequenceCurve& extended_source_region_curve,
    const LaneFollowSequenceCurve& target_region_curve,
    const pb::LaneChangeMode& lc_direction, const speed::Profile& speed_profile,
    const speed::Profile& tree_profile,
    const speed::Profile& resampled_ref_profile,
    const pb::ElectiveLaneChangeDecision& elc_decision,
    const pb::StuckSignal& stuck_signal,
    const std::vector<LaneChangeUrgencyInfo>& urgency_infos,
    const speed::SpeedBoundGenerator& speed_bound_generator,
    const double earliest_lc_start_ra_arclength,
    const double extended_source_region_start_to_ego_ra,
    const int discomfort_ix, std::ostringstream& debug_oss) {
  DCHECK_GT(earliest_lc_start_ra_arclength, -math::constants::kEpsilon);
  DCHECK(!tree_profile.empty());
  const vehicle_model::pb::AxleRectangularMeasurement& ego_shape =
      ego_state.car_model_with_shape().shape_measurement();
  const double rb_to_ra_dist = ego_shape.rear_bumper_to_rear_axle();
  const double ego_speed = tree_profile[0].v;
  const double lc_duration_reduction = ComputeLCDurationReduction(
      tail_obj_id, target_region_object_info_list, ego_speed, rb_to_ra_dist,
      lane_change_metadata.consecutive_lane_change_count(), debug_oss);
  debug_oss << __func__ << ": lc_duration_reduction " << lc_duration_reduction
            << "\n";
  // Firstly, estimate ego speed during lane change, Right now we use first
  // state in LC guide speed profile to estimate a default longitudinal span.
  // See
  // https://docs.google.com/document/d/11ORr7uzbfV9PAS-t4GfH7FwifI6uBsgi-VfdW37IAbM/edit#bookmark=id.m91nkvuiugei
  double default_option_crossing_pos = 0.0;
  std::optional<math::Range1d> default_option =
      CanTriggerDefaultOption(urgency_infos)
          ? GetDefaultLongitudinalSpan(
                ego_state,
                /*lc_start_ra_arclength=*/earliest_lc_start_ra_arclength,
                extended_source_region_start_to_ego_ra,
                extended_source_region_curve, target_region_curve, lc_direction,
                default_option_crossing_pos, debug_oss)
          : std::nullopt;

  // Secondly, Take earliest lc start position into consideration,
  // and calculate a more accurate lane change longitudinal span.
  double earliest_option_crossing_pos = 0.0;
  const std::optional<math::Range1d>& earliest_option =
      GetLongitudinalSpanByLcStartPos(
          lc_duration_reduction, ego_state, tree_profile, resampled_ref_profile,
          /*lc_start_ra_arclength=*/earliest_lc_start_ra_arclength,
          earliest_lc_start_ra_arclength,
          extended_source_region_start_to_ego_ra,
          lane_change_metadata.max_longitudinal_span_for_current_lc(),
          extended_source_region_curve, target_region_curve,
          speed_bound_generator, lc_direction, elc_decision, stuck_signal,
          discomfort_ix, earliest_option_crossing_pos, debug_oss);

  // Thirdly, extend the lc start position by ego speed to get a more
  // comfortable option. Use speed profile to calculate the start offset to
  // capture the blockage on extended source lane and start lane change sooner.
  double start_delayed_option_crossing_pos = 0.0;
  const std::optional<math::Range1d>& start_delayed_option =
      GetLongitudinalSpanByLcStartPos(
          lc_duration_reduction, ego_state, tree_profile, resampled_ref_profile,
          /*lc_start_ra_arclength=*/earliest_lc_start_ra_arclength +
              CalculateLaneChangeStartDelayedOptionStartOffset(
                  speed_profile,
                  IsStuckOrSlowMovingAgent(elc_decision, stuck_signal)),
          earliest_lc_start_ra_arclength,
          extended_source_region_start_to_ego_ra,
          lane_change_metadata.max_longitudinal_span_for_current_lc(),
          extended_source_region_curve, target_region_curve,
          speed_bound_generator, lc_direction, elc_decision, stuck_signal,
          discomfort_ix, start_delayed_option_crossing_pos, debug_oss);
  LaneChangeLongitudinalSpanOptions lane_change_longitudinal_span_options;
  lane_change_longitudinal_span_options.feasible_lc_range =
      feasible_lc_range_on_extended_source_region;
  if (default_option.has_value()) {
    lane_change_longitudinal_span_options.lc_options_map.emplace(
        pb::LaneChangeLongitudinalSpanOptionsDebug::kDefault,
        std::move(default_option.value()));
    lane_change_longitudinal_span_options.lc_options_crossing_pos_map.emplace(
        pb::LaneChangeLongitudinalSpanOptionsDebug::kDefault,
        default_option_crossing_pos);
  }
  if (earliest_option.has_value()) {
    lane_change_longitudinal_span_options.lc_options_map.emplace(
        pb::LaneChangeLongitudinalSpanOptionsDebug::kEarliest,
        std::move(earliest_option.value()));
    lane_change_longitudinal_span_options.lc_options_crossing_pos_map.emplace(
        pb::LaneChangeLongitudinalSpanOptionsDebug::kEarliest,
        earliest_option_crossing_pos);
  }
  if (start_delayed_option.has_value()) {
    lane_change_longitudinal_span_options.lc_options_map.emplace(
        pb::LaneChangeLongitudinalSpanOptionsDebug::kStartDelayed,
        std::move(start_delayed_option.value()));
    lane_change_longitudinal_span_options.lc_options_crossing_pos_map.emplace(
        pb::LaneChangeLongitudinalSpanOptionsDebug::kStartDelayed,
        start_delayed_option_crossing_pos);
  }
  return lane_change_longitudinal_span_options;
}

bool CanTriggerDefaultOption(
    const std::vector<LaneChangeUrgencyInfo>& urgency_infos) {
  constexpr double kHardStopLineUrgencyDistanceToTriggerDefaultOptionInMeter =
      40.0;
  for (const LaneChangeUrgencyInfo& urgency_info : urgency_infos) {
    if (urgency_info.stop_line_type() ==
            planner::pb::LaneChangeStopLineType::HARD_STOP_LINE &&
        urgency_info.urgency_dist() <
            kHardStopLineUrgencyDistanceToTriggerDefaultOptionInMeter) {
      return true;
    }
  }
  return false;
}

std::optional<math::Range1d> GetDefaultLongitudinalSpan(
    const RobotState& ego_state, const double lc_start_ra_arclength,
    const double extended_source_region_start_to_ego_ra,
    const LaneFollowSequenceCurve& extended_source_region_curve,
    const LaneFollowSequenceCurve& target_region_curve,
    const pb::LaneChangeMode& lc_direction, double& default_option_crossing_pos,
    std::ostringstream& debug_oss) {
  const double ego_speed = ego_state.current_state_snapshot().speed();
  const double ego_speed_during_lc =
      std::max(kMinSpeedDuringLaneChangeInMps, ego_speed);
  constexpr double kLCDurationReductionForDefaultLongitudinalSpanInSec = 0.0;
  const std::optional<double>& default_lc_duration_in_sec = ComputeLcDuration(
      kLCDurationReductionForDefaultLongitudinalSpanInSec, ego_state,
      ego_speed_during_lc, extended_source_region_start_to_ego_ra, lc_direction,
      extended_source_region_curve, target_region_curve, debug_oss);
  if (!default_lc_duration_in_sec.has_value()) {
    return std::nullopt;
  }
  // Obtain default LC longitudinal span.
  const double default_lc_start_pos =
      lc_start_ra_arclength + extended_source_region_start_to_ego_ra;
  const double lc_duration_in_sec = default_lc_duration_in_sec.value();
  constexpr double kDefaultOptionEgoAccelInMpss = -1.5;
  const double ego_accel =
      std::max(kDefaultOptionEgoAccelInMpss,
               (kMinSpeedDuringLaneChangeInMps - ego_speed_during_lc) /
                   lc_duration_in_sec);
  const double default_lc_end_pos =
      default_lc_start_pos +
      std::clamp(ego_speed_during_lc * lc_duration_in_sec +
                     0.5 * lc_duration_in_sec * lc_duration_in_sec * ego_accel,
                 kMinLongitudinalSpanToDoLaneChangeInMeter,
                 kMaxLongitudinalSpanToDoLaneChangeInMeter);
  default_option_crossing_pos =
      (default_lc_start_pos + default_lc_end_pos) * 0.5;
  return math::Range1d(default_lc_start_pos, default_lc_end_pos);
}

std::optional<math::Range1d> GetLongitudinalSpanByLcStartPos(
    const double lc_duration_reduction, const RobotState& ego_state,
    const speed::Profile& speed_profile,
    const speed::Profile& resampled_ref_profile,
    const double lc_start_ra_arclength,
    const double earliest_lc_start_ra_arclength,
    const double extended_source_region_start_to_ego_ra,
    const double max_longitudinal_span,
    const LaneFollowSequenceCurve& extended_source_region_curve,
    const LaneFollowSequenceCurve& target_region_curve,
    const speed::SpeedBoundGenerator& speed_bound_generator,
    const pb::LaneChangeMode& lc_direction,
    const pb::ElectiveLaneChangeDecision& elc_decision,
    const pb::StuckSignal& stuck_signal, const int discomfort_ix,
    double& lc_crossing_pos, std::ostringstream& debug_oss) {
  DCHECK(!speed_profile.empty());
  DCHECK_GE(lc_start_ra_arclength, 0.0);
  // Interpolate lc_start_state from speed profile.
  const std::optional<speed::State> lc_start_state =
      GetStateAtArcLength(speed_profile, lc_start_ra_arclength);
  if (!lc_start_state.has_value()) {
    debug_oss << __func__ << " lc_start_state has no value ";
    return std::nullopt;
  }

  // Calculate lc time duration.
  const std::optional<double> lc_duration_in_sec = ComputeLcDuration(
      lc_duration_reduction, ego_state, lc_start_state->v,
      lc_start_ra_arclength + extended_source_region_start_to_ego_ra,
      lc_direction, extended_source_region_curve, target_region_curve,
      debug_oss);
  if (!lc_duration_in_sec.has_value()) {
    debug_oss << __func__ << " lc duration has no value ";
    return std::nullopt;
  }

  // Get the LC longitudinal range using the lc_duration_in_sec and the
  // speed profile.
  const double lc_end_time =
      lc_start_state.value().t + lc_duration_in_sec.value();
  // we use half of the time to LC to be the LC crossing lane marking time.
  const double lc_crossing_time =
      lc_start_state.value().t + lc_duration_in_sec.value() * 0.5;
  speed::State lc_end_state;
  speed::State lc_crossing_state;
  int unused_time_ix = -1;
  if (!IsStuckOrSlowMovingAgent(elc_decision, stuck_signal) ||
      FLAGS_planning_enable_lane_change_tree_search) {
    // Get lc end state from lane follow speed profile.
    if (!GetStateAtTime(speed_profile, lc_end_time, &lc_end_state,
                        unused_time_ix)) {
      debug_oss << __func__ << " lc end state has no value ";
      return std::nullopt;
    }
    if (!GetStateAtTime(speed_profile, lc_crossing_time, &lc_crossing_state,
                        unused_time_ix)) {
      debug_oss << __func__ << " lc cross state has no value ";
      return std::nullopt;
    }
  } else {
    // Ego is having stuck scene. The speed profile will be limited by stuck
    // object thus causing short LC corridor. We have a heuristic to increase it
    // for better LC success rate.
    if (!GetLcStatesOnBranchingProfile(
            lc_start_state.value().t, lc_duration_in_sec.value(), speed_profile,
            resampled_ref_profile, speed_bound_generator, discomfort_ix,
            lc_end_state, lc_crossing_state)) {
      debug_oss << __func__ << " lc branch profile has no value ";
      return std::nullopt;
    }
  }

  // Use max_longitudinal_span to limit the span.
  // A bit background:
  // |max_longitudinal_span| is the recommended maximum span of the current lane
  // change when there are consecutive lane changes ahead. It's calculated by
  // distributing the total remaining distance over all future LCs with a
  // decelerating model.
  // ClampLongitudinalSpan clamps the LC option if the end exceeds the
  // |max_longitudinal_span|. It does so by first clamping the "start
  // delayed" part (ego current position to lc_option start), and then the LC
  // option itself (lc option start to lc option end).
  const double lc_end_ra_arclength =
      lc_start_ra_arclength +
      std::clamp(lc_end_state.x - lc_start_ra_arclength,
                 kMinLongitudinalSpanToDoLaneChangeInMeter,
                 kMaxLongitudinalSpanToDoLaneChangeInMeter);
  const math::Range1d& clamped_span = ClampLongitudinalSpan(
      lc_start_ra_arclength, lc_end_ra_arclength,
      earliest_lc_start_ra_arclength, max_longitudinal_span);

  // Get LC crossing pos. Currently we reuse the exact logic to get the
  // |lc_end_ra_arclength|.
  // TODO(lane change): Improve the lc crossing arclength clamp logic as the
  // original logic and buffer is designed for lc end.
  const double lc_crossing_ra_arclength =
      lc_start_ra_arclength +
      std::clamp(lc_crossing_state.x - lc_start_ra_arclength,
                 kMinLongitudinalSpanToDoLaneChangeInMeter,
                 kMaxLongitudinalSpanToDoLaneChangeInMeter);
  const math::Range1d& lc_crossing_clamped_span = ClampLongitudinalSpan(
      lc_start_ra_arclength, lc_crossing_ra_arclength,
      earliest_lc_start_ra_arclength, max_longitudinal_span);
  lc_crossing_pos =
      lc_crossing_clamped_span.end_pos + extended_source_region_start_to_ego_ra;

  return math::Range1d(
      clamped_span.start_pos + extended_source_region_start_to_ego_ra,
      clamped_span.end_pos + extended_source_region_start_to_ego_ra);
}

bool IsStuckOrSlowMovingAgent(
    const pb::ElectiveLaneChangeDecision& elc_decision,
    const pb::StuckSignal& stuck_signal) {
  return !math::NearZero(stuck_signal.stuck_score()) ||
         elc_decision.is_triggered();
}

bool GetLcStatesOnBranchingProfile(
    const double lc_start_time, const double lc_duration_time,
    const speed::Profile& speed_profile,
    const speed::Profile& resampled_ref_profile,
    const speed::SpeedBoundGenerator& speed_bound_generator,
    const int discomfort_ix, speed::State& lc_end_state,
    speed::State& lc_crossing_state) {
  const double lc_switch_time = lc_start_time + lc_duration_time / 2.0;
  int lc_switch_time_ix = -1;
  if (!GetStateAtTime(speed_profile, lc_switch_time, &lc_crossing_state,
                      lc_switch_time_ix)) {
    return false;
  }

  // Accelerate after ego enters target lane.
  speed::Profile lc_speed_profile = speed_profile;
  speed::AccelerateTowardsReferenceProfile(
      resampled_ref_profile,
      speed_bound_generator.discomfort_limits().LimitsAtDiscomfortIx(
          discomfort_ix),
      lc_switch_time_ix, speed_bound_generator.dt(), /*n_horizon=*/80,
      /*max_v=*/std::nullopt, &lc_speed_profile);
  int lc_end_time_ix = -1;
  // The lc_end_state is on the accelerated profile after LC switch time.
  const double lc_end_time = lc_start_time + lc_duration_time;
  return GetStateAtTime(lc_speed_profile, lc_end_time, &lc_end_state,
                        lc_end_time_ix);
}

math::Range1d ClampLongitudinalSpan(const double lc_start_ra_arclength,
                                    const double lc_end_ra_arclength,
                                    const double earliest_lc_start_ra_arclength,
                                    const double max_longitudinal_span) {
  if (max_longitudinal_span < math::constants::kEpsilon ||
      max_longitudinal_span > lc_end_ra_arclength) {
    // No max span or current span already within max span.
    return {lc_start_ra_arclength, lc_end_ra_arclength};
  }

  const double clamp_needed = lc_end_ra_arclength - max_longitudinal_span;

  // Prefer to reduce lc_start_ra_arclength to earliest_lc_start_ra_arclength.
  const double max_reduction_by_lc_start =
      lc_start_ra_arclength - earliest_lc_start_ra_arclength;
  if (max_reduction_by_lc_start > clamp_needed) {
    return {lc_start_ra_arclength - clamp_needed,
            lc_end_ra_arclength - clamp_needed};
  }

  // If not enough, reduce the LC duration span until
  // kMinLongitudinalSpanToDoLaneChangeInMeter.
  const double clamp_needed_after_lc_start_reduction =
      clamp_needed - max_reduction_by_lc_start;
  const double max_reduction_by_lc_duration =
      std::max(0.0, lc_end_ra_arclength - lc_start_ra_arclength -
                        kMinLongitudinalSpanToDoLaneChangeInMeter);
  if (max_reduction_by_lc_duration > clamp_needed_after_lc_start_reduction) {
    return {lc_start_ra_arclength - max_reduction_by_lc_start,
            lc_end_ra_arclength - max_reduction_by_lc_start -
                clamp_needed_after_lc_start_reduction};
  }

  return {earliest_lc_start_ra_arclength,
          earliest_lc_start_ra_arclength +
              kMinLongitudinalSpanToDoLaneChangeInMeter};
}

}  // namespace planner
