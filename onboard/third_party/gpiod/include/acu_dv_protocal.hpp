/**
 * @file acu_dv_protocal.hpp
 * <AUTHOR>
 * @brief
 * @version 0.1
 * @date 2024-08-27
 *
 * @copyright Copyright (c) 2024
 *
 */

#ifndef __ACU_DV_PROTOCAL_HPP__
#define __ACU_DV_PROTOCAL_HPP__

#include <cstring>
#include <iostream>

namespace voyager {
namespace hardware {
enum kAcuDvOrinCanIdEnum {
  kAcuDvTestControl = 0xC1,
  kAcuDvOrinaBaseInfo = 0x502,
  kAcuDvOrinbBaseInfo = 0x503,
  kAcuDvOrinaPhyInfo = 0x50D,
  kAcuDvOrinbPhyInfo = 0x50E,
  kAcuDvOrinaCamerInfo = 0x50F,
  kAcuDvOrinbCamerInfo = 0x510,
  kAcuDvOrinaMax20087Info = 0x511,
  kAcuDvOrinbMax20087Info = 0x512,
  kAcuDvOrinaMax96712D1G1Info = 0x513,
  kAcuDvOrinaMax96712D1G2Info = 0x514,
  kAcuDvOrinaMax96712D2G1Info = 0x515,
  kAcuDvOrinaMax96712D2G2Info = 0x516,
  kAcuDvOrinbMax96712D1G1Info = 0x517,
  kAcuDvOrinbMax96712D1G2Info = 0x518,
  kAcuDvOrinbMax96712D2G1Info = 0x519,
  kAcuDvOrinbMax96712D2G2Info = 0x51A,
  kAcuDvOrinaCanTest = 0x51C,
  kAcuDvOrinbCanTest = 0x51D,
};
#pragma pack(1)
// orinA->602; orinB->603
struct AcuDvOrinBaseInfoSt {
  int16_t tj_temp_;     // unit 1°C
  int16_t cpu_temp_;    // unit 1°C
  uint8_t cpu0_load_;   // unit 1%
  uint8_t cpu1_load_;   // unit 1%
  uint8_t cpu2_load_;   // unit 1%
  uint8_t cpu3_load_;   // unit 1%
  uint8_t cpu4_load_;   // unit 1%
  uint8_t cpu5_load_;   // unit 1%
  uint8_t cpu6_load_;   // unit 1%
  uint8_t cpu7_load_;   // unit 1%
  uint16_t cpu0_freq_;  // unit 1MHz
  uint16_t cpu4_freq_;  // unit 1MHz
  int16_t gpu_temp_;    // unit 1°C
  uint8_t gpu_load_;    // unit 1%
  uint8_t pciesw_host1_link_;
  uint16_t pciesw_host1_eg_bw_;  // MB/s
  uint16_t pciesw_host1_in_bw_;  // MB/s
  uint8_t pciesw_host2_link_;
  uint16_t pciesw_host2_eg_bw_;  // MB/s
  uint16_t pciesw_host2_in_bw_;  // MB/s
  uint8_t pciesw_host3_link_;
  uint16_t pciesw_host3_eg_bw_;  // MB/s
  uint16_t pciesw_host3_in_bw_;  // MB/s
  uint16_t usb_disk_rd_rate_;    // MB/s
  uint16_t usb_disk_wr_rate_;    // MB/s
  int16_t pciesw_temp_;          // unit 1°C

  void to_big(AcuDvOrinBaseInfoSt& info) {
    memmove(&info, this, sizeof(AcuDvOrinBaseInfoSt));
    info.tj_temp_ = htobe16(this->tj_temp_);
    info.cpu_temp_ = htobe16(this->cpu_temp_);
    info.cpu0_freq_ = htobe16(this->cpu0_freq_);
    info.cpu4_freq_ = htobe16(this->cpu4_freq_);
    info.gpu_temp_ = htobe16(this->gpu_temp_);
    info.pciesw_host1_eg_bw_ = htobe16(this->pciesw_host1_eg_bw_);
    info.pciesw_host1_in_bw_ = htobe16(this->pciesw_host1_in_bw_);
    info.pciesw_host2_eg_bw_ = htobe16(this->pciesw_host2_eg_bw_);
    info.pciesw_host2_in_bw_ = htobe16(this->pciesw_host2_in_bw_);
    info.pciesw_host3_eg_bw_ = htobe16(this->pciesw_host3_eg_bw_);
    info.pciesw_host3_in_bw_ = htobe16(this->pciesw_host3_in_bw_);
    info.usb_disk_rd_rate_ = htobe16(this->usb_disk_rd_rate_);
    info.usb_disk_wr_rate_ = htobe16(this->usb_disk_wr_rate_);
    info.pciesw_temp_ = htobe16(this->pciesw_temp_);
  }
};
union AcuDvOrinBaseInfoUn {
  AcuDvOrinBaseInfoSt msg_;
  uint8_t var1[sizeof(AcuDvOrinBaseInfoSt)];
};
// orinA->60D; orinB->60E
struct AcuDvOrinPhyRegSt {
  uint8_t q2220_temp_;  // unit 1°C, offset -75
  uint8_t q2220_pma_link_;
  uint16_t q2220_pma_reg_;
  uint16_t q2220_qsi_level_reg_;
  uint16_t q2220_qsi_value_reg_;
  uint16_t q2220_pcs_st_reg2_;
  uint16_t q2220_base_t1_pcs_st_reg2_;
  uint16_t q2220_pcs_base_t1_st_reg2_;
  uint16_t q2220_pcs_base_t1_st_reg1_;
  uint8_t q2220_pcs_link_;
  // uint16_t q2220_packet_chr_counter_reg_;
  // uint16_t q2220_link_drop_sensor_reg_;
  uint8_t bcm89890_temp_;  // unit 1°C
  uint16_t bcm89890_linkdown_counter_reg_;
  uint8_t bcm89890_cooper_link_;
  uint16_t bcm89890_cooper_link_reg_;
  uint16_t bcm89890_sqi_value_reg_;
  uint16_t bcm89890_avdd_0p8v_reg_;   // unit 0.01V
  uint16_t bcm89890_avdd_1p0v_reg_;   // unit 0.01V
  uint16_t bcm89890_dvdd_0p8v0_reg_;  // unit 0.01V
  uint16_t bcm89890_dvdd_0p8v1_reg_;  // unit 0.01V
  uint16_t bcm89890_dvdd_1p8v1_reg_;  // unit 0.01V
  uint16_t bcm89890_dvdd_3p3v_reg_;   // unit 0.01V
  void to_big(AcuDvOrinPhyRegSt& info) {
    memmove(&info, this, sizeof(AcuDvOrinPhyRegSt));
    info.q2220_pma_reg_ = htobe16(this->q2220_pma_reg_);
    info.q2220_qsi_level_reg_ = htobe16(this->q2220_qsi_level_reg_);
    info.q2220_qsi_value_reg_ = htobe16(this->q2220_qsi_value_reg_);
    info.q2220_pcs_st_reg2_ = htobe16(this->q2220_pcs_st_reg2_);
    info.q2220_base_t1_pcs_st_reg2_ = htobe16(this->q2220_base_t1_pcs_st_reg2_);
    info.q2220_pcs_base_t1_st_reg2_ = htobe16(this->q2220_pcs_base_t1_st_reg2_);
    info.q2220_pcs_base_t1_st_reg1_ = htobe16(this->q2220_pcs_base_t1_st_reg1_);

    info.bcm89890_linkdown_counter_reg_ =
        htobe16(this->bcm89890_linkdown_counter_reg_);
    info.bcm89890_cooper_link_reg_ = htobe16(this->bcm89890_cooper_link_reg_);
    info.bcm89890_sqi_value_reg_ = htobe16(this->bcm89890_sqi_value_reg_);
    info.bcm89890_avdd_0p8v_reg_ = htobe16(this->bcm89890_avdd_0p8v_reg_);
    info.bcm89890_avdd_1p0v_reg_ = htobe16(this->bcm89890_avdd_1p0v_reg_);
    info.bcm89890_dvdd_0p8v0_reg_ = htobe16(this->bcm89890_dvdd_0p8v0_reg_);
    info.bcm89890_dvdd_0p8v1_reg_ = htobe16(this->bcm89890_dvdd_0p8v1_reg_);
    info.bcm89890_dvdd_3p3v_reg_ = htobe16(this->bcm89890_dvdd_3p3v_reg_);
  }
};
union AcuDvOrinPhyRegUn {
  AcuDvOrinPhyRegSt msg_;
  uint8_t var1[sizeof(AcuDvOrinPhyRegSt)];
};
// orinA->611; orinB->612
struct AcuDvOrinMax20087InfoSt {
  uint16_t broad_vdd_;  // unit 0.01V
  uint16_t volt1_;      // unit 0.01V
  uint16_t curr1_;      // unit 0.001A
  uint8_t stat1_;
  uint16_t volt2_;  // unit 0.01V
  uint16_t curr2_;  // unit 0.001A
  uint8_t stat2_;
  uint16_t volt3_;  // unit 0.01V
  uint16_t curr3_;  // unit 0.001A
  uint8_t stat3_;
  uint16_t volt4_;  // unit 0.01V
  uint16_t curr4_;  // unit 0.001A
  uint8_t stat4_;
};
struct AcuDvOrinMax20087sInfoSt {
  AcuDvOrinMax20087InfoSt m1_;
  AcuDvOrinMax20087InfoSt m2_;
  void to_big(AcuDvOrinMax20087sInfoSt& info) {
    memmove(&info, this, sizeof(AcuDvOrinMax20087sInfoSt));
    info.m1_.broad_vdd_ = htobe16(this->m1_.broad_vdd_);
    info.m1_.volt1_ = htobe16(this->m1_.volt1_);
    info.m1_.curr1_ = htobe16(this->m1_.curr1_);
    info.m1_.volt2_ = htobe16(this->m1_.volt2_);
    info.m1_.curr2_ = htobe16(this->m1_.curr2_);
    info.m1_.volt3_ = htobe16(this->m1_.volt3_);
    info.m1_.curr3_ = htobe16(this->m1_.curr3_);
    info.m1_.volt4_ = htobe16(this->m1_.volt4_);
    info.m1_.curr4_ = htobe16(this->m1_.curr4_);
    info.m1_.stat1_ = this->m1_.stat1_ << 4;
    info.m1_.stat2_ = this->m1_.stat2_ << 4;
    info.m1_.stat3_ = this->m1_.stat3_ << 4;
    info.m1_.stat4_ = this->m1_.stat4_ << 4;

    info.m2_.broad_vdd_ = htobe16(this->m2_.broad_vdd_);
    info.m2_.volt1_ = htobe16(this->m2_.volt1_);
    info.m2_.curr1_ = htobe16(this->m2_.curr1_);
    info.m2_.volt2_ = htobe16(this->m2_.volt2_);
    info.m2_.curr2_ = htobe16(this->m2_.curr2_);
    info.m2_.volt3_ = htobe16(this->m2_.volt3_);
    info.m2_.curr3_ = htobe16(this->m2_.curr3_);
    info.m2_.volt4_ = htobe16(this->m2_.volt4_);
    info.m2_.curr4_ = htobe16(this->m2_.curr4_);
    info.m2_.stat1_ = this->m2_.stat1_ << 4;
    info.m2_.stat2_ = this->m2_.stat2_ << 4;
    info.m2_.stat3_ = this->m2_.stat3_ << 4;
    info.m2_.stat4_ = this->m2_.stat4_ << 4;
  }
};
union AcuDvOrinMax20087InfoUn {
  AcuDvOrinMax20087sInfoSt msg_;
  uint8_t var1[sizeof(AcuDvOrinMax20087sInfoSt)];
};
// orinA.m1.g1->613;orinA.m2.g1->615;orinB.m1->617;orinB.m2->619
struct AcuDvOrinMax96712G1InfoSt {
  uint8_t gmslb_lock_;
  uint8_t gmslc_lock_;
  uint8_t gmsld_lock_;
  uint8_t gmsla_lock_;
  uint8_t pwr0_bit_core0_status_;
  uint8_t intr5_bit_gmsld_eye_open_;
  uint8_t intr5_bit_gmslc_eye_open_;
  uint8_t intr5_bit_gmslb_eye_open_;
  uint8_t intr5_bit_gmsla_eye_open_;
  uint8_t intr5_bit_crc_error_;
  uint8_t intr5_bit_water_mark1;
  uint8_t intr5_bit_water_mark0;
  uint32_t gmsla_dec_err_counter;
  uint32_t gmslb_dec_err_counter;
  uint32_t gmslc_dec_err_counter;
  uint32_t gmsld_dec_err_counter;
  uint32_t gmsla_idle_err_counter;
  uint32_t gmslb_idle_err_counter;
  uint32_t gmslc_idle_err_counter;
  uint32_t gmsld_idle_err_counter;
  uint8_t vid_pxl_bit_dec_ecc_2b_err;
  uint8_t vid_pxl_bit_dec_ecc_1b_err;
  uint8_t vid_pxl_bit_gmsld_crc_err;
  uint8_t vid_pxl_bit_gmslc_crc_err;
  uint8_t vid_pxl_bit_gmslb_crc_err;
  uint8_t vid_pxl_bit_gmsla_crc_err;
  uint8_t vid_line0_crc_err;
  uint8_t vid_line1_crc_err;
  uint8_t vid_line2_crc_err;
  uint8_t vid_line3_crc_err;
  uint8_t vid0_rx8_bit_blk_len_err;
  uint8_t vid0_rx8_bit_pipeline_lock_;
  uint8_t vid0_rx8_bit_pkt_det_;
  uint8_t vid0_rx8_bit_seq_err_;
  uint8_t vid1_rx8_bit_blk_len_err;
  uint8_t vid1_rx8_bit_pipeline_lock_;
  uint8_t vid1_rx8_bit_pkt_det_;
  uint8_t vid1_rx8_bit_seq_err_;
  uint8_t vid2_rx8_bit_blk_len_err;
  uint8_t vid2_rx8_bit_pipeline_lock_;
  void to_big(AcuDvOrinMax96712G1InfoSt& info) {
    memmove(&info, this, sizeof(AcuDvOrinMax96712G1InfoSt));
    info.gmsla_dec_err_counter = htobe32(this->gmsla_dec_err_counter);
    info.gmslb_dec_err_counter = htobe32(this->gmslb_dec_err_counter);
    info.gmslc_dec_err_counter = htobe32(this->gmslc_dec_err_counter);
    info.gmsld_dec_err_counter = htobe32(this->gmsld_dec_err_counter);
    info.gmsla_idle_err_counter = htobe32(this->gmsla_idle_err_counter);
    info.gmslb_idle_err_counter = htobe32(this->gmslb_idle_err_counter);
    info.gmslc_idle_err_counter = htobe32(this->gmslc_idle_err_counter);
    info.gmsld_idle_err_counter = htobe32(this->gmsld_idle_err_counter);
  }
};
union AcuDvOrinMax96712G1InfoUn {
  AcuDvOrinMax96712G1InfoSt msg_;
  uint8_t var1[sizeof(AcuDvOrinMax96712G1InfoSt)];
};
// orinA.m1.g2->614;orinA.m2.g2->616;orinA.m1->618;orinB.m2->61A
struct AcuDvOrinMax96712G2InfoSt {
  uint8_t vid2_rx8_bit_pkt_det_;
  uint8_t vid2_rx8_bit_seq_err_;
  uint8_t vid3_rx8_bit_blk_len_err;
  uint8_t vid3_rx8_bit_pipeline_lock_;
  uint8_t vid3_rx8_bit_pkt_det_;
  uint8_t vid3_rx8_bit_seq_err_;
  uint8_t backtop25_bit_pipe3_mem_err_;
  uint8_t backtop25_bit_pipe2_mem_err_;
  uint8_t backtop25_bit_pipe1_mem_err_;
  uint8_t backtop25_bit_pipe0_mem_err_;
  uint8_t mipi_tx2_phy0_bit_sync_mode_enable_;
  uint8_t mipi_tx2_phy0_bit_video_in_sync_;
  uint8_t mipi_tx2_phy0_bit_loss_video_sync_;
  uint8_t mipi_tx2_phy1_bit_sync_mode_enable_;
  uint8_t mipi_tx2_phy1_bit_video_in_sync_;
  uint8_t mipi_tx2_phy1_bit_loss_video_sync_;
  uint8_t mipi_tx2_phy2_bit_sync_mode_enable_;
  uint8_t mipi_tx2_phy2_bit_video_in_sync_;
  uint8_t mipi_tx2_phy2_bit_loss_video_sync_;
  uint8_t mipi_tx2_phy3_bit_sync_mode_enable_;
  uint8_t mipi_tx2_phy3_bit_video_in_sync_;
  uint8_t mipi_tx2_phy3_bit_loss_video_sync_;
  uint32_t mem_ecc_err1_counter_;
  uint32_t mem_ecc_err2_counter_;
  uint32_t gmsla_eye_mom_err_counter_;
  uint32_t gmslb_eye_mom_err_counter_;
  uint32_t gmslc_eye_mom_err_counter_;
  uint32_t gmsld_eye_mom_err_counter_;
  void to_big(AcuDvOrinMax96712G2InfoSt& info) {
    memmove(&info, this, sizeof(AcuDvOrinMax96712G2InfoSt));
    info.mem_ecc_err1_counter_ = htobe32(this->mem_ecc_err1_counter_);
    info.mem_ecc_err2_counter_ = htobe32(this->mem_ecc_err2_counter_);
    info.gmsla_eye_mom_err_counter_ = htobe32(this->gmsla_eye_mom_err_counter_);
    info.gmslb_eye_mom_err_counter_ = htobe32(this->gmslb_eye_mom_err_counter_);
    info.gmslc_eye_mom_err_counter_ = htobe32(this->gmslc_eye_mom_err_counter_);
    info.gmsld_eye_mom_err_counter_ = htobe32(this->gmsld_eye_mom_err_counter_);
  }
};
union AcuDvOrinMax96712G2InfoUn {
  AcuDvOrinMax96712G2InfoSt msg_;
  uint8_t var1[sizeof(AcuDvOrinMax96712G2InfoSt)];
};
// c1
struct AcuDvOrinTestCtlSt {
  uint8_t monitor_start_;
  uint8_t eth_test_start_;
  uint8_t can_test_start_;
  uint8_t orina_cam1_start_;
  uint8_t orina_cam2_start_;
  uint8_t orina_cam3_start_;
  uint8_t orina_cam4_start_;
  uint8_t orina_cam5_start_;
  uint8_t orina_cam6_start_;
  uint8_t orina_cam7_start_;
  uint8_t orina_cam8_start_;
  uint8_t orinb_cam1_start_;
  uint8_t orinb_cam2_start_;
  uint8_t orinb_cam3_start_;
  uint8_t orinb_cam4_start_;
  uint8_t orinb_cam5_start_;
  uint8_t orinb_cam6_start_;
  uint8_t orinb_cam7_start_;
  uint8_t orinb_cam8_start_;
};
union AcuDvOrinTestCtlUn {
  AcuDvOrinTestCtlSt msg_;
  uint8_t var1[sizeof(AcuDvOrinTestCtlSt)];
};
// orinA->612; orinB->613
struct AcuDvOrinCanTestSt {
  int64_t timestamp_;
  int64_t counter_;
  void to_big(AcuDvOrinCanTestSt& info) {
    memmove(&info, this, sizeof(AcuDvOrinCanTestSt));
    info.timestamp_ = htobe64(this->timestamp_);
    info.counter_ = htobe64(this->counter_);
  }
};
union AcuDvOrinCanTestUn {
  AcuDvOrinCanTestSt msg_;
  uint8_t var1[sizeof(AcuDvOrinCanTestSt)];
};
#pragma pack()
}  // namespace hardware
}  // namespace voyager

#endif
