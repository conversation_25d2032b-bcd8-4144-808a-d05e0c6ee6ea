#include "planner/path/reasoning/agent_intention/object_occupancy_state/object_occupancy_util.h"

#include <gtest/gtest.h>
#include "planner/path/reasoning/agent_intention/object_occupancy_state/object_occupancy_state.h"

namespace planner {
namespace {
class ObjectOccupancyUtilTest : public ::testing::Test {
 protected:
  void SetUp() override {
    auto& tracked_object = *agent_proto_.mutable_tracked_object();
    tracked_object.set_object_type(voy::perception::VEHICLE);
    tracked_object.set_id(18845);
    tracked_object.set_center_x(0.8);
    tracked_object.set_center_y(10.0);
    tracked_object.set_center_z(0.0);
    tracked_object.set_length(2.0);
    tracked_object.set_width(2.0);
    tracked_object.set_height(1.0);
    tracked_object.set_heading(M_PI / 2.0);
    tracked_object.set_velocity(1.0);
    tracked_object.add_attributes(voy::perception::PARKED_CAR);
    {
      auto* point = tracked_object.add_contour();
      point->set_x(-0.20);
      point->set_y(9.0);
    }
    {
      auto* point = tracked_object.add_contour();
      point->set_x(1.8);
      point->set_y(9.0);
    }
    {
      auto* point = tracked_object.add_contour();
      point->set_x(-0.20);
      point->set_y(11.0);
    }
    {
      auto* point = tracked_object.add_contour();
      point->set_x(1.8);
      point->set_y(11.0);
    }
    const int64_t timestamp = 0;
    // Dummy pose at plan init ts.
    const TrafficParticipantPose pose_at_plan_init_ts_(
        timestamp, agent_proto_.tracked_object());
  }

  prediction::pb::Agent agent_proto_;
  // Dummy pose at plan init ts.
  const TrafficParticipantPose pose_at_plan_init_ts_;
  RequiredLateralGap required_lateral_gap_ =
      RequiredLateralGap(/*critical_required_lateral_gap=*/0.4,
                         /*comfort_required_lateral_gap=*/0.8);
  const double ego_width_ = 2.0;
  const int64_t timestamp = 0;
};
}  // namespace

TEST_F(ObjectOccupancyUtilTest, ObjectHardBlocking) {
  // Unit test scenario 1, with tracked object width being 2.0 m and left-right
  // corridor gap being 4.0 m:
  //
  //  (0, 20)   |       |     (4, 20)
  //            |       |
  //            |       |
  //            |___    |
  //  left      |   |   |    right
  //  boundary  |   |   |    boundary
  //            |   |   |
  //            |___|   |
  //            |       |
  //            |       |
  //            |       |
  //  (0, 0)    |       |    (4, 0)

  // Object HARD_BLOCKING on the left side of ego vehicle.
  PlannerObject planner_object =
      PlannerObject(agent_proto_, timestamp, pose_at_plan_init_ts_);
  const math::geometry::Polyline2d left_linestring_1 = {{0.0, 0.0},
                                                        {0.0, 20.0}};
  const math::geometry::Polyline2d right_linestring_1 = {{4.0, 0.0},
                                                         {4.0, 20.0}};
  const math::geometry::PolylineCurve2d left_boundary_segment_1(
      left_linestring_1);
  const math::geometry::PolylineCurve2d right_boundary_segment_1(
      right_linestring_1);
  pb::AgentSnapshotBlockingState blocking_state1 = GenerateBlockingInformation(
      left_boundary_segment_1, right_boundary_segment_1, required_lateral_gap_,
      planner_object, ego_width_);
  EXPECT_EQ(blocking_state1, pb::AgentSnapshotBlockingState::HARD_BLOCKING);
}

TEST_F(ObjectOccupancyUtilTest, ObjectSoftBlocking) {
  // Unit test scenario 2, with tracked object width being 2.0 m and left-right
  // corridor gap being 8.0 m:
  //
  //  (-6, 20)  |           |     (2, 20)
  //            |           |
  //            |           |
  //            |        ___|
  //  left      |       |   |    right
  //  boundary  |       |   |    boundary
  //            |       |   |
  //            |       |___|
  //            |           |
  //            |           |
  //            |           |
  //  (-6, 0)   |           |    (2, 0)

  // Object SOFT_BLOCKING on the right side of ego vehicle.
  PlannerObject planner_object =
      PlannerObject(agent_proto_, timestamp, pose_at_plan_init_ts_);
  const math::geometry::Polyline2d left_linestring_2 = {{-6.0, 0.0},
                                                        {-6.0, 20.0}};
  const math::geometry::Polyline2d right_linestring_2 = {{2.0, 0.0},
                                                         {2.0, 20.0}};
  const math::geometry::PolylineCurve2d left_boundary_segment_2(
      left_linestring_2);
  const math::geometry::PolylineCurve2d right_boundary_segment_2(
      right_linestring_2);
  pb::AgentSnapshotBlockingState blocking_state_2 = GenerateBlockingInformation(
      left_boundary_segment_2, right_boundary_segment_2, required_lateral_gap_,
      planner_object, ego_width_);
  EXPECT_EQ(blocking_state_2, pb::AgentSnapshotBlockingState::SOFT_BLOCKING);
}

TEST_F(ObjectOccupancyUtilTest, ObjectNotBlockingOnLeftSide) {
  // Unit test scenario 3, with tracked object width being 2.0 m and left-right
  // corridor gap being 5.0 m:
  //
  //  (3, 20)    |               |    (8, 20)
  //             |               |
  //             |               |
  //        ___  |               |
  //       |   | | left          |    right
  //       |   | | boundary      |    boundary
  //       |   | |               |
  //       |___| |               |
  //             |               |
  //             |               |
  //             |               |
  //  (3, 0)     |               |   (8, 0)

  // Object NO_BLOCKING on the left side of left boundary.
  PlannerObject planner_object =
      PlannerObject(agent_proto_, timestamp, pose_at_plan_init_ts_);
  const math::geometry::Polyline2d left_linestring_3 = {{3.0, 0.0},
                                                        {3.0, 20.0}};
  const math::geometry::Polyline2d right_linestring_3 = {{8.0, 0.0},
                                                         {8.0, 20.0}};
  const math::geometry::PolylineCurve2d left_boundary_segment_3(
      left_linestring_3);
  const math::geometry::PolylineCurve2d right_boundary_segment_3(
      right_linestring_3);
  pb::AgentSnapshotBlockingState blocking_state_3 = GenerateBlockingInformation(
      left_boundary_segment_3, right_boundary_segment_3, required_lateral_gap_,
      planner_object, ego_width_);
  EXPECT_EQ(blocking_state_3, pb::AgentSnapshotBlockingState::NOT_BLOCKING);
}

TEST_F(ObjectOccupancyUtilTest, ObjectNotBlockingOnRightSide) {
  // Unit test scenario 3, with tracked object width being 2.0 m and left-right
  // corridor gap being 5.0 m:
  //
  //  (-6, 20)   |             |    (-1, 20)
  //             |             |
  //             |             |
  //   left      |             |      ___
  //   boundary  |        right|     |   |
  //             |     boundary|     |   |
  //             |             |     |   |
  //             |             |     |___|
  //             |             |
  //             |             |
  //             |             |
  //  (-6, 0)    |             |   (-1, 0)

  // Object NO_BLOCKING on the right side of right boundary.
  PlannerObject planner_object =
      PlannerObject(agent_proto_, timestamp, pose_at_plan_init_ts_);
  const math::geometry::Polyline2d left_linestring_4 = {{-6.0, 0.0},
                                                        {-6.0, 20.0}};
  const math::geometry::Polyline2d right_linestring_4 = {{-1.0, 0.0},
                                                         {-1.0, 20.0}};
  const math::geometry::PolylineCurve2d left_boundary_segment_4(
      left_linestring_4);
  const math::geometry::PolylineCurve2d right_boundary_segment_4(
      right_linestring_4);
  pb::AgentSnapshotBlockingState blocking_state_4 = GenerateBlockingInformation(
      left_boundary_segment_4, right_boundary_segment_4, required_lateral_gap_,
      planner_object, ego_width_);
  EXPECT_EQ(blocking_state_4, pb::AgentSnapshotBlockingState::NOT_BLOCKING);
}

}  // namespace planner
