syntax = "proto3";

// Voyager package.
package planner.pb;

import public "voy_protos/math.proto";
import public "voy_protos/perception_collision.proto";
import public "voy_protos/perception_object_type.proto";
import "voy_protos/canbus.proto";
import "voy_protos/pose.proto";
import "voy_protos/trajectory.proto";
import "prediction_protos/stationary_intention.proto";
import "prediction_protos/prediction_anomaly.proto";

// Type of ttc calculation for debug.
// Next field 15.
enum TimeToCollisionType {
  // NO collision at all in furture.
  SAFETY = 0;
  // It is risky right now, but ego can hard brake to avoid.
  RISKY_BUT_CAN_AVOID_BY_HB = 1;
  // It is dangerous right now, but ego can emergency brake to avoid.
  DANGEROUS_BUT_CAN_AVOID_BY_EB = 2;
  // It is dangerous and can bot to avoid by emergency brake.
  DANGEROUS_EVEN_IF_EB = 3;
  // It is dangerous right now, but ego can emergency brake and swerve left to
  // avoid.
  DANGEROUS_BUT_CAN_AVOID_BY_EB_SWERVE_LEFT = 4;
  // It is dangerous right now, but ego can emergency brake and swerve right to
  // avoid.
  DANGEROUS_BUT_CAN_AVOID_BY_EB_SWERVE_RIGHT = 5;
  // EB trajectory is dangerous and one taligator behind, ego should not invoke
  // eb.
  DANGEROUS_TALIGATOR = 6 [ deprecated = true ];
  // It is dangerous and exception handler can not deal with it.
  DANGEROUS_AND_UNABLE = 7;
  // It is dangerous right now, but ego can emergency brake by last trajectory
  // to avoid.
  DANGEROUS_BUT_CAN_AVOID_BY_LAST_EB = 8;
  // It is critical exception, ego need emergency brake.
  CRITICAL_EXCEPTION_NEED_EB = 9;
  // It is dangerous for early publish.
  DANGEROUS_FOR_EARLY_PUBLISH = 10;
  // It is dangerous right now, but ego can use emergency lane keep to return
  // to safer lane.
  DANGEROUS_BUT_CAN_AVOID_BY_ELK_LEFT = 11;
  DANGEROUS_BUT_CAN_AVOID_BY_ELK_RIGHT = 12;

  // Original trajectory is dangerous and one taligator behind, ego should use
  // soft brake.
  DANGEROUS_TALIGATOR_ORIGINAL_TRAJECTORY = 13;
  // EB trajectory is dangerous and one taligator behind, ego should not invoke
  // eb.
  DANGEROUS_TALIGATOR_EB_TRAJECTORY = 14;
};

// Type of siganl for other module.
enum TimeToCollisionSignal {
  NOOP = 0;
  WARNING = 1;
  ERROR = 2;
  FATAL = 3;
};

// Which rollout algorithm used in collision check.
// Next field 7.
enum CollisionRollOutAlgorithom {
  // Collision with primary predicted trajectory;
  PRIMARY_PREDICTION = 0;
  // Collision with non-primary predicted trajectory;
  NON_PRMARY_PREDICTION = 1;
  // Collision with const model by tracked object;
  CONST_MODEL = 2;
  // Collision with reactive const model by tracked object;
  REACTIVE_CONST_MODEL = 3;
  // Collision with adaptive reactive const model by tracked object;
  // adaptive means the reaction time is calculated by rollout instead
  // of calculated by initial pose.
  ADAPTIVE_REACTIVE_CONST_MODEL = 6;
  // Collision with reactive model by predicted trajectory;
  REACTIVE_PREDICTION_MODEL = 4;
  // Collision with adaptive reactive model by predicted trajectory;
  // adaptive means the reaction time is calculated by rollout instead
  // of calculated by initial pose.
  ADAPTIVE_REACTIVE_PREDICTION_MODEL = 5;
};

// Type of trajectory.
// Next field 13.
enum EgoTrajectoryType {
  // Original trajectory.
  ORIGINAL = 0;
  // Trajectory of ego with soft brake.
  SOFT_BRAKE = 8;
  // Trajectory of ego with hard brake.
  HARD_BRAKE = 1;
  // Trajectory of ego with hard brake.
  HARD_BRAKE_CURRENT_CYCLE = 12;
  // Trajectory of ego with emergency brake.
  EMERGENCY_BRAKES = 2;
  // EMERGENCY BRAKE AND SWERVE;
  SWERVE_LEFT = 3;
  SWERVE_RIGHT = 4;
  // Trajectory of ego with emergency brake.
  LAST_EMERGENCY_BRAKES = 5;
  // HARD BRAKE AND SWERVE;
  HARD_BRAKE_SWERVE_LEFT = 6;
  HARD_BRAKE_SWERVE_RIGHT = 7;
  // Emergency lane keep trajectory.
  EMERGENCY_LANE_KEEP_LEFT = 9;
  EMERGENCY_LANE_KEEP_RIGHT = 10;
  // Trajectory for avoid tail collision.
  SOFT_BRAKE_AVOID_TAIL_COLLISION = 11;
};

// Reason of EH trigger.
// Next field 11.
enum EHTriggerReason {
  // NONE.
  NO_TRIGGER_REASON = 0;
  // Front collision.
  FRONT_COLLISION = 1;
  // Taligator collision.
  TALIGATOR_COLLISION = 2;
  // XTaligator collision..
  XTALIGATOR_COLLISION = 3;
  // Obstacle collision.
  OBSTACLES_COLLISION = 4;
  // Camera collision.
  CAMERA_COLLISION = 5;
  // Early publish object collision.
  EARLY_PUBLISH_OBJECTS = 6;
  // Unknown object collision.
  UNKNOWN_OBJECTS = 7;
  // Emergency object collision.
  EMERGENCY_OBJECTS = 8;
  // Anomaly detection collision.
  ANOMALY_DETECTION = 9;
  // Tailgater and side collision both exist, will trigger TP Swerve(ES).
  TALIGATOR_SIDE_COLLISION = 10;
};

// Type of EH trigger.
// Next field 7.
enum EHTriggerType {
  // None.
  NO_TRIGGER_TYPE = 0;
  // Emergency brake.
  EB = 1;
  // Hard brake.
  HB = 2;
  // Soft brake.
  SB = 3;
  // Emergency brake and swerve.
  EBS = 4;
  // Hard brake and swerve.
  HBS = 5;
  // Emergency steering.
  ES = 6;
};

// Type of const model for different scenarios.
enum ConstModelType {
  // Do not use const model.
  NO_CONST_MODEL = 0;
  // Use const model in lateral danger with higher belief.
  LATERAL_DANGER = 1;
  // Use const model in cut-in with higher belief.
  CUT_IN_AHEAD = 3;
  // Use const model for fresh agent.
  FRESH = 4;
  // Use const model in other scenarios with lower belief.
  OTHER_SCENARIO = 2;
  // Use const model for VRU.
  CYC_CONST_MODEL = 5;
  // Use const model for long-distance agents with no collision risks,
  // to reduce FP in abnormal trajectory prediction.
  NO_SAFETY_RISK = 6;
  // Use const model for front dangerous Veh.
  FRONT_DANGER = 7;
};

// Type of const model for different scenarios.
enum PredictionModelType {
  // Do not use prediction model.
  NO_PREDICTION_MODEL = 0;
  // Use primary trajectory from prediction.
  PRIMARY_MODEL = 1;
  // Use primary ignore ego trajectory from prediction.
  PRIMARY_IGNORE_EGO_MODEL = 3;
  // Use non-primary trajectory from prediction.
  MULTIPLE_MODEL = 4;
};

// Next field 3.
enum AgentReactionType {
  NO_AR = 0;
  AR_BY_NOW = 1;
  // adaptive means the reaction time is calculated by rollout instead
  // of calculated by initial pose.
  ADAPTIVE_AR_IN_FUTURE = 2;
};

// Degradation handler type for different node.
enum DegradationHandlerType {
  // Degradation handler for localization drift.
  LOCALIZATION_DRIFT = 0;
  // Degradation handler for control drift.
  CONTROL_DRIFT = 1;
  // Degradation handler for remote speed limit.
  REMOTE_SPEED_LIMIT = 2;
  // Degradation handler for sensor abnormal.
  SENSOR_ABNORMAL = 3;
  // Degradation handler for exception handler warning.
  EH_WARNING = 4;
};

// Type of hard boundary.
enum GeometryCollisionType {
  // no collision.
  NO_COLLISION = 0;
  // hard boundary.
  HARD_BOUNDARY = 1;
  // construction zones.
  CONSTRUCTION_ZONE = 2;
  // curb.
  CURB = 3;
};

// Type of tailgator trigger reason
// Next field 23.
enum TailgatorTriggerReason {
  // no tailgator.
  NO_TAILGATOR = 0;
  // tailgator trigger for destination.
  DESTINATION = 1;
  // tailgator trigger for trafficlight.
  TRAFFIC_LIGHT = 2;
  // tailgator trigger for vehicle.
  VEHICLE = 3;
  // tailgator not trigger for VRU.
  NOT_TRIGGER_VRU = 4;
  // tailgator trigger for fp_unknown.
  FP_UNKNOWN = 5 [ deprecated = true ];
  // tailgator not trigger because eh trajectory is shorter than planner
  NOT_TRIGGER_SHORT_TRAJECTORY = 6;
  // not trigger for dangerous vehicle
  NOT_TRIGGER_DANGEROUS_VEHICLE = 7;
  // not trigger because planner brake for tailgator
  NOT_TRIGGER_DOMINANT_OBJECT = 8;
  // tailgator trigger for no risk vru
  NO_RISK_VRU = 9;
  // tailgator trigger for no risk barrier/cone
  NO_RISK_BARRIER_CONE = 10;
  // tailgator trigger for fp unknown/barrier/traffic_cone
  FP_OBJECT = 11;
  // tailgator trigger for fp object that not in tracked_object_list
  FP_OBJECT_NOT_IN_TRACKING = 12;
  // tailgator trigger for dominant vehicle
  DOMINANT_VEHICLE = 13;
  // tailgator trigger for dominant vru
  DOMINANT_VRU = 14;
  // tailgator trigger for fp construction zone
  FP_CONSTRUCTION_ZONE = 15;
  // not trigger when ego is in low speed
  NOT_TRIGGER_EGO_LOW_SPEED = 16;
  // Do not trigger when the distance between the ego and the tailgator
  // is more than 10m farther than the distance between the ego anddominant
  // vehicle.
  NOT_TRIGGER_DANGEROUS_DOMINANT_VEHICLE = 17;
  // Do not trigger for dangerous cut in vehicle.
  NOT_TRIGGER_DANGEROUS_CUTIN_DOMINANT_VEHICLE = 18;
  // Do not trigger for dangerous crossing vehicle.
  NOT_TRIGGER_DANGEROUS_CROSSING_DOMINANT_VEHICLE = 19;
  // Do not trigger for close vehicle, rel_distance less than 0.5m.
  NOT_TRIGGER_DANGEROUS_CLOSE_DOMINANT_VEHICLE = 20;
  // Do not trigger for close ahead vehicle.
  NOT_TRIGGER_DANGEROUS_CLOSE_AHEAD_VEHICLE = 21;
  // Do not trigger for close parallel ahead vehicle.
  NOT_TRIGGER_DANGEROUS_CLOSE_PARALLEL_AND_AHEAD_VEHICLE = 22;
  // Do not trigger if collision position is not tail.
  NOT_TRIGGER_COLLISION_POSITION_IS_NOT_TAIL = 23;
  // Hard brake for no reason.
  HARD_BRAKE_FOR_NO_REASON = 24;
}

// if agent will yeild to ego and how much decal will be.
// Next field is 8.
message AgentSeeEgoParam {
  bool agent_see_ego = 1;
  // The angle range of agent see ego contour.
  // signed angle: left is + / right is -.
  math.pb.Range agent_see_ego_angle_range = 2;
  // The angle range of agent's FOV, which is adjusted by agent speed
  // and omega(delta_heading).
  math.pb.Range agent_fov_range = 3;
  // The delta angle of agent_see_ego_angle.
  // If it is higher, the reaction time is shorter.
  // If use_visual_loom is false, reaction time is read from const config.
  bool use_visual_loom = 4;
  double delta_visual_loom_of_agent = 5;
  // the deviation of center of visual loom to center of fov.
  double visual_loom_deviation = 6;
  bool is_in_left_rear_view_mirror = 7;
};

// if agent will yeild to ego and how much decel will be.
message AgentReactionDecelParam {
  double reactive_decel = 2;  // should LT 0
  double reactive_time = 3;   // in sec, should GT 0
  double reactive_jerk = 4;   // should LT 0
  // IF this timestamp is zero, please use initial timestatmp + reactive_time.
  int64 reactive_timestamp = 5;  // in ms
  // The yield belief is calculated by speed/decel of agent's hisotry, and is
  // not suitable if agent yield to ego by lateral nudge, in which case agent
  // will not decelerate.
  double agent_yield_belief_of_this_frame = 6;
  double final_agent_yield_belief = 7;

  // If ego is in fov of agent, will_agent_yield_to_ego_fov is true;
  bool will_agent_yield_to_ego_fov = 8;
  // If the yield intention is too low, even will_agent_yield_to_ego_fov was set
  // true, the final will_agent_yield_to_ego will be set to false;
  bool will_agent_yield_to_ego = 1;
};

// Collision calculation configuration.
// Next field: 3
message CollisionCalculationConfig {
  // Trajectory horizon.
  double time_horizon = 1;
  // Trajectory points interval.
  int64 trajectory_step = 2;
};


// Control error compensation value.
message ControlErrorCompValue {
  int64 timestamp = 1;
  double odom = 2;
  double longi_comp = 3;
  double lat_comp = 4;
  double heading_comp = 5;
}

// Control error state for compensation.
message ControlErrorState {
  int64 timestamp = 1;
  voy.Pose ego_pose = 2;
  TrajectoryPose ref_pose = 3;
  bool is_valid = 4;
  double longitudinal_error = 5;
  double lateral_error = 6;
  double heading_error_deg = 7;
  double longitudinal_comp = 8;
  double lateral_comp = 9;
  double heading_comp_deg = 10;
  repeated ControlErrorCompValue comp_values = 11;
}

// Trajectory planning info.
message TrajectoryPlanningInfo {
  bool is_ebs_exist = 1;
}

// Collision calc config for each trajectory.
// Next field: 10
message TrajectoryWiseCalculationConfig {
  // Ego trajectory type.
  EgoTrajectoryType ego_trajectory_type = 1;

  // Type of planner trajectory.
  enum PlannerTrajectoryType {
    NORMAL = 0;
    FS = 1;
    EFS = 2;
    EB = 3;
    MS = 4;
    ES = 5;
  }
  // Planner trajectory type.
  PlannerTrajectoryType planner_trajectory_type = 2;
  // Ego Trajectory.
  Trajectory trajectory = 3;
  // Final Trajectory.
  Trajectory trajectory_comp = 7;

  // Longitudinal safety buffer.
  double longi_safety_buffer = 4;
  double longi_safety_buffer_end = 5;

  // Trajectory planning info.
  TrajectoryPlanningInfo trajectory_plan_info = 8;

  // Control error state for compensation.
  ControlErrorState control_error_state = 6;

  // EB trajectory profile compensation.
  ControlErrorState profile_comp_state = 9;
}

// model should triggered in collision calculation.
// Next field: 10
message CollisionCalculateModel {
  ConstModelType const_model_type = 1;
  bool use_reactive_model = 2;
  bool use_adaptive_model = 3;
  bool use_adaptive_reactive_const_model = 4;
  bool use_predicition_perturb = 5;
  bool use_multi_predicition = 6;
  bool use_ignore_ego_predicition = 9;
  bool use_eb_swerve = 8;
  enum ShadowMode {
    NO_SHADOW = 0;
    CONST_CYCLIST = 1;
  }
  // Is in shadow mode for const model?
  // In shadow mode, we will only record rt_event and do not
  // invode EB.
  // There are many const model FP in road test, we need
  // to run shadow mode first.
  ShadowMode const_shadow_mode = 7;
};

// extra lateral and longitudinal buffer.
message ExtraBuffer {
  enum ExtraBufferType {
    FINAL = 0;
    CONTROL_DRIFT = 1;
    EB_SWERVE = 2;
    EMERGENCY_LANE_KEEP = 3;
    EXPECT_CONTROL_DRIFT = 4;
    FS_CONTROL_DRIFT_VRU = 5;
    FS_CONTROL_DRIFT_VEH = 6;
  }

  double lateral_buffer = 1;
  double longitudinal_buffer = 2;
  ExtraBufferType extra_buffer_type = 3;
};

// Relative pose info of object according to ego.
// Next field 56.
message RelativePoseInfo {
  reserved 8, 9, 10, 11, 12, 15, 39;
  reserved "ego_is_in_agent_view", "max_ego_to_agent_angle_diff";
  reserved "min_ego_to_agent_angle_diff", "will_agent_yield_to_ego";
  reserved "reactive_decel", "delta_visual_loom_of_agent";
  int64 pose_timestamp = 13;
  int64 object_timestamp = 14;
  // After time align and object state filter.
  voy.TrackedObject time_align_object = 44;
  // Roll out trajectory of const model.
  // Empty trajectory if const model is disabled.
  Trajectory const_model_trajectory = 45;
  // The relative pose type of object according to ego at initial time.
  enum RelativePoseType {
    // fail to compute RelativePoseType.
    FAIL = 0;
    // all points of contour are behind.
    FULLY_BEHIND = 1;
    // some points of contour are parallel and some are behind.
    PARALLEL_AND_BEHIND = 2;
    // all points of contour are parallel.
    FULLY_PARALLEL = 3;
    // some points of contour are parallel and some are ahead.
    PARALLEL_AND_AHEAD = 4;
    // all points of contour are ahead.
    FULLY_AHEAD = 5;
  };
  RelativePoseType pose_type = 1;

  // Type of agent moving according to ego.
  enum AgentMovingType {
    ON_COMING = 0;
    SAME_DIRECTION = 1;
    CROSSING = 2;
  };
  AgentMovingType agent_moving_type = 27;
  // The turn mode is calculated by heading diff of trajectory poses.
  TurnMode ego_turn_mode = 28;
  TurnMode primary_predicition_turn_mode = 29;

  // relative heading is signed.
  // ego.heading - object.heading
  double relative_heading = 2;
  double relative_speed = 3;
  // bbox relative distance is signed.
  double relative_distance = 4;
  // relative center direction = angle of (object.center to ego.center)
  double relative_center_direction = 5;
  // project object center to ego axle.
  // + for ahead rear bumper / - for behind rear bumper
  double longitudinal_distance_to_ego_axle = 6;
  // + for left of axle / - for right of axle
  double lateral_distance_to_ego_axle = 7;
  // relative distance from heading intersection position to ego
  // front bumper.
  double relative_distance_from_heading_intersection = 50;
  // Agent lateral speed relative to ego heading.
  double lateral_speed = 18;
  // Agent longitudinal speed relative to ego heading.
  double longitudinal_speed = 21;
  // Agent lateral speed relative to ego heading.
  double lateral_acceleration = 31;
  // Agent longitudinal acceleration relative to ego heading.
  double longitudinal_acceleration = 32;

  // Params of agent see ego, which is used in AgentReactionDecelParam.
  AgentSeeEgoParam agent_see_ego_param = 16;
  // Will agent react to ego and how much decel will be.
  AgentReactionDecelParam agent_reaction_param = 17;

  // extra lateral and longitudinal buffer.
  double extra_distance = 19 [ deprecated = true ];
  ExtraBuffer final_extra_buffer = 37;
  repeated ExtraBuffer extra_buffers = 38;

  // agent stationary_to_move signal from prediction.
  optional prediction.pb.StationaryIntentionType agent_stationary_type = 20;

  // Prediction Error.
  prediction.pb.AgentBehaviorAnomaly agent_behavior_anomaly = 22;
  // If agent_behavior_anomaly was detected, stores the timestamp.
  int64 last_behavior_anomaly_timestamp = 23;
  bool has_anomaly = 24;

  // Agent heading relative to lane direction.
  double relative_heading_to_lane = 25;
  // Center direction relative to ego heading.
  double relative_center_direction_to_ego_heading = 26;

  // if track age is short, it is dangerous and appear suddenly.
  bool track_age_is_short = 30;

  // Aligned tracked object info.
  double aligned_relative_distance = 33 [ deprecated = true ];
  double aligned_lateral_distance_to_ego_axle = 34 [ deprecated = true ];
  double aligned_lateral_ttc = 35;
  double aligned_longitudinal_ttc = 36;
  double aligned_is_fully_ahead = 40 [ deprecated = true ];

  bool agent_cross_left_lane_marking = 41;
  bool agent_cross_right_lane_marking = 42;

  bool is_acc_turning_with_parallel_vehicle = 43 [ deprecated = true ];
  bool is_key_object = 46;
  bool is_taligator = 47;
  bool is_agent_xlane_taligator = 48;
  bool is_agent_xlane_taligator_extend = 52;
  bool is_acc_with_parallel_vehicle = 49;
  bool is_static = 51;
  bool is_large_vehicle = 53;
  bool is_less_num_obs = 54;
  bool is_move_away_agent = 55;
};

// Calculate uncertainty of collision.
// Next id: 11.
message CollisionUncertainty {
  // Input feature.
  double predicition_pose_uncertainty = 1;
  double agent_rollout_uncertainty = 2;
  double ttc_uncertainty = 5;

  // Output of this frame.
  double agent_yield_belief_of_this_frame = 6;
  double final_agent_yield_belief = 7;

  double uncertainty_of_this_frame = 3;
  // output of this frame and previous beliefs.
  double final_collision_belief = 4;
  double previous_collision_belief = 8;
  double severity = 9;
  double expected_risk = 10;
};

// Pose perturbation direction.
enum PerturbDir {
  PERTURB_CENTER = 0;
  PERTURB_FRONT = 1;
  PERTURB_BACK = 2;
  PERTURB_LEFT = 3;
  PERTURB_RIGHT = 4;
};

// Next field 6.
message NearmissPoseInfo {
  int64 timestamp = 1;
  TrajectoryPose ego_pose = 2;
  TrajectoryPose agent_pose = 3;
  // the relative distance and speed between ego bbox and object bbox/contour.
  double relative_distance = 4;
  double relative_speed = 5;
}

// Collsiion Pose information from collision check.
// Next field 28.
message CollisionPoseInfo {
  // IF it is false, this struct is empty and unvalid.
  bool is_valid = 11;
  bool has_collision = 1;
  double ttc = 2;
  // The orientation of object in collision.
  voy.perception.CollisionDetection.ObjectPositionType object_position_type = 3;
  // the relative angle between ego_to_object and ego heading.
  double ego_see_object_angle = 4;
  // The orientation of polygon intersection in collision.
  voy.perception.CollisionDetection.CollisionPositionType
      collision_position_type = 5;
  // the relative angle between ego_to_collision_certer and ego heading.
  double ego_see_collision_angle = 6;
  // The orientation of polygon intersection in collision considered
  //  vehicle geometry shape.
  voy.perception.CollisionDetection.CollisionPositionType
      collision_position_type_geo = 22;
  // The relative angle between ego_to_collision_certer and ego heading in
  // degree.
  double ego_see_collision_angle_deg = 23;
  // The relative angle between collision point to rear corner in degree.
  double side_collision_angle_deg = 24;
  // Pose perturbation direction.
  PerturbDir perturb_dir = 25;

  // type of agent rollout algorithm, rollout_type is deprecated, see
  // features (const_model_type, prediction_model_type, agent_reaction_type)
  CollisionRollOutAlgorithom rollout_type = 7;
  ConstModelType const_model_type = 12;
  PredictionModelType prediction_model_type = 13;
  AgentReactionType agent_reaction_type = 14;

  // agent reaction param.
  AgentReactionDecelParam agent_react_param = 8;
  CollisionUncertainty collision_uncertainty = 9;
  // should handle collision.
  bool should_handle_collision = 10;
  // Pose of collision time.
  TrajectoryPose ego_collision_pose = 15;
  TrajectoryPose agent_collision_pose = 16;
  // should handle collision for tailgator.
  bool should_handle_collision_for_tailgator = 17;
  // the relative distance and speed between ego bbox and object bbox/contour.
  // Use NearmissPoseInfo now.
  repeated double relative_distance = 18;
  repeated double relative_speed = 19;
  repeated NearmissPoseInfo nearmiss_pose_infos = 26;

  // If the agent yields ego for AR, how much is the speed loss and odom loss.
  double agent_speed_loss_if_reaction = 20;
  double agent_odom_loss_if_reaction = 21;

  // Collision calc config for each trajectory.
  TrajectoryWiseCalculationConfig trajectory_calc_config = 27;
};

enum BehaviorTypeForEH {
  UNDEFINED_4EH = 0;
  // Lane keep without any cross lane behavior.
  LANE_KEEP_4EH = 1;
  LANE_CHANGE_4EH = 2;
  PULL_OVER_4EH = 3;
  JUMP_OUT_4EH = 4;
  CROSS_LANE_NUDGE_4EH = 5;
  REVERSE_4EH = 6;
  WAYPOINT_ASSIST_4EH = 7;
  LEFT_MERGE_4EH = 8;
  RIGHT_MERGE_4EH = 9;
};

// Ego behavior features from lane sequences and planning decisions.
// Next id: 11.
message EgoBehaviorFeatures {
  // Behavior type of selected trajectory.
  BehaviorTypeForEH behavior_type = 7;
  bool is_ego_in_junction = 1;
  bool is_ego_in_roundabout = 2;
  bool is_ego_in_fork_lane = 10;
  // turn mode from map.
  TurnMode ego_turn_mode_from_map = 3;
  LaneChangeMode lane_change = 4;

  // is current pose of ego crossing lane marking?
  bool is_pose_cross_left_lane_marking = 5;
  bool is_pose_cross_right_lane_marking = 6;

  bool is_ego_motion_lateral_to_left = 8;
  bool is_ego_motion_lateral_to_right = 9;
};

// Next Available ID: 32
// Result of TTC of one object.
message ObjectTimeToCollision {
  // obj_id for hard_boundary is 0 and critical_exception is -1.
  int64 obj_id = 1;
  // collision boundary type
  GeometryCollisionType boundary_type = 21;
  // If ego will collide with boundary, which side of ego is the boundary.
  math.pb.Side collision_boundary_side = 24;
  // ttc of original ego trajectory.
  double ttc = 2;
  // timestamp of tracked object
  int64 obj_timestamp = 3;
  // ttc if ego hard brake.
  double hb_ttc = 4;
  // ttc if ego emergence brake.
  double eb_ttc = 5;
  // ttc if ego soft brake.
  double sb_ttc = 30;
  // should trigger emergence brake?
  bool trigger_eb = 6;
  string debug_info = 7;
  // type of reuslt.
  TimeToCollisionType ttc_type = 8;

  CollisionPoseInfo original_collision_info = 9;
  CollisionPoseInfo soft_brake_collision_info = 31;
  CollisionPoseInfo hard_brake_collision_info = 10;
  CollisionPoseInfo emergency_brake_collision_info = 11;
  CollisionPoseInfo emergency_brake_and_swerve_left_collision_info = 12;
  bool is_ebs_left_safe = 22;
  CollisionPoseInfo emergency_brake_and_swerve_right_collision_info = 13;
  bool is_ebs_right_safe = 23;
  CollisionPoseInfo emergency_brake_collision_info_for_last_path = 20;

  // If the object is taligator and we should not invoke rear-end collision if
  // triggering EB.
  CollisionPoseInfo emergency_brake_collision_info_for_taligator = 15;

  // If the object is taligator and we should not invoke swerve collision if
  // triggering EB swerve.
  CollisionPoseInfo emergency_brake_swerve_collision_info_for_taligator = 17;

  // Relative pose at the initial time.
  RelativePoseInfo relative_pose_at_initial = 14;

  // ttc if ego emergence brake and taligator object collides with ego.
  bool is_taligator_collision = 16;

  // Model triggered in collision calculation.
  CollisionCalculateModel collision_calculate_model = 18;

  bool is_tailgator_risk = 25;
  TailgatorTriggerReason tailgator_trigger_reason = 26;
  voy.perception.ObjectType hard_brake_obj_type = 27;
  bool is_soft_brake_safe = 28;
  // EB should not be invoked even if there is a TTC margin.
  bool is_eb_dangerous_with_ttc_margin = 29;
};

// Emergency take over trajectory type.
enum TakeOverTrajectoryType {
  TYPE_UNDEFINED = 0;
  TYPE_GENERAL_TAKE_OVER = 1;
  TYPE_EMERGENCY_LANE_KEEP = 2;
  TYPE_EVASIVE_STEER = 3;
  TYPE_TAILGATER_PROTECTION = 4;
  TYPE_EMGERGENCY_BRAKE_SWERVE_LEFT = 5;
  TYPE_EMGERGENCY_BRAKE_SWERVE_RIGHT = 6;
  TYPE_EMGERGENCY_BRAKE_SWERVE = 7;
};

// Take over steering swerve direction.
enum TakeOverSteerDir {
  STEER_DIR_INVALID = 0;
  STEER_DIR_LEFT = 1;
  STEER_DIR_RIGHT = 2;
  STEER_DIR_NONE = 3;
};

// ES trajectory generation config.
// Next Available ID: 9
message ESTrajectoryGenerationConfig {
  TakeOverTrajectoryType traj_type = 1;
  string config_id = 2;
  // Longitudinal limit.
  double accel_limit_min = 3;
  double accel_limit_max = 4;
  double jerk_limit_min = 5;
  double jerk_limit_max = 6;
  // Lateral limit.
  double lat_accel_limit = 7;
  double lat_jerk_limit = 8;
  double lateral_offset = 9;
};

enum MsgFrom {
  UNKNOWN_FROM = 0;
  NODE = 1;
  NODELET = 2;
};

// Trajectory map planned by EH.
message TrajectoryMapItem {
  EgoTrajectoryType trajectory_type = 1;
  Trajectory trajectory = 2;
};

// Environment feature extraction. Only for debug.
message EnvironmentFeatures {
  int64 pose_timestamp = 1;
  Trajectory ego_trajectory = 2;
  voy.Pose pose = 3;
  voy.Canbus canbus = 4;
  string manual_type = 5;
};

// State machine type.
// Next Available ID: 11
enum EhState {
  STATE_IDLE = 0;
  STATE_ACTIVE = 1;
  STATE_KEEP_EB = 2;
  STATE_KEEP_EBS = 3;
  STATE_EB_SWITCH_TO_EBS = 4;
  STATE_EBS_SWITCH_TO_EB = 5;
  STATE_DEACTIVE = 6;
  STATE_EXTRA_EB_FOR_GS = 7;
  STATE_EXTRA_EBS_FOR_GS = 9;
  STATE_DEACTIVE_EB_FOR_TAIL_RISK = 8;
  STATE_DEACTIVE_FOR_INVALID_TRAJECTORY = 10;
};

// Result of TTC of object list.
// Only include critical ttc.
// Next Available ID: 18
message TtcDetectResult {
  // The sequence number identifying the message of the exception handler cycle.
  int64 sequence_number = 10;
  // Using specific timestamp below.
  int64 timestamp = 1 [ deprecated = true ];
  repeated ObjectTimeToCollision ttc_list = 2;

  // Timing is critical in collision mitigation.
  int64 pose_timestamp = 3;
  // lidar message timestamp of each node.
  int64 tracking_timestamp = 4;
  int64 prediction_timestamp = 5;
  int64 planning_timestamp = 6;
  // the first timestamp to plan.
  int64 plan_init_timestamp = 7;

  // taligator state.
  bool is_taligator_collision = 11;

  // type of signal.
  TimeToCollisionSignal ttc_signal = 8;

  // Collision mitigation trajectory.
  Trajectory trajectory = 9;

  // Trajectory map planned by EH.
  repeated TrajectoryMapItem trajectory_map = 16;

  // message type
  MsgFrom eh_msg_from = 12;

  // EH trigger type.
  EHTriggerType type = 13;

  // EH trigger reason.
  EHTriggerReason reason = 14;

  // Ego behavior features from lane sequences and planning decisions.
  EgoBehaviorFeatures ego_behavior_features = 15;

  // Environment feature extraction. Only for debug.
  EnvironmentFeatures environment_features = 17;
}