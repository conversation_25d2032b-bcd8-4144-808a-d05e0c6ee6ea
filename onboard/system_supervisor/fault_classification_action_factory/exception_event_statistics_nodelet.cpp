#include "exception_event_statistics_nodelet.h"

#include <glog/logging.h>
#include <google/protobuf/util/json_util.h>
#include <memory>
#include <string>
#include <tuple>
#include <utility>

#include "av_comm/car_id.h"
#include "av_comm/topics.h"
#include "common/ros_param_getter.h"
#include "platform_interface/platform_interface.h"

namespace system_supervisor {
namespace action_factory {
namespace ss_exception_event_statistics {

namespace {
[[maybe_unused]] const char kExceptionEventStatisticsKey[] =
    "ss_exception_event_statistics";
[[maybe_unused]] const char kExceptionEventStatisticsKeyTest[] =
    "ss_exception_event_statistics_test";  // hil env
[[maybe_unused]] const char kSystemEventPubName[] = "system_event_pub";
}  // namespace

ExceptionEventStatisticsVNode::ExceptionEventStatisticsVNode() {
  faul_level_manifest_ = std::make_shared<FaultLevelManifest>();
  exception_event_statistics_ = std::make_shared<ExceptionEventStatistics>(
      faul_level_manifest_, ros_param_getter_);
}

bool ExceptionEventStatisticsVNode::Init() {
  exception_event_statistics_->Init();
  faul_level_manifest_->GetFaultLevelManifest();
  return true;
}

void ExceptionEventStatisticsVNode::Callback(
    std::list<std::shared_ptr<const ::pb::SystemState>>& system_state_messages,
    std::list<std::shared_ptr<const ::service_restore::pb::RestoreRecord>>&
        restore_record_messages,
    std::list<std::shared_ptr<const ::voy::Canbus>>& canbus_messages,
    std::list<std::shared_ptr<
        const ::fault_detector::pb::FaultClassificationConfigStatus>>&
        fault_classification_config_status) {
  ProcessRestoreRecord(restore_record_messages);
  ProcessCanbusMessage(canbus_messages);

  ProcessFaultClassificationConfigStatus(fault_classification_config_status);
  if (!faul_level_manifest_->IsLoadedFaultExceptionType()) {
    LOG_EVERY_N(WARNING, 200) << "Not yet obtain FaultExceptionType.";
  }

  std::vector<std::shared_ptr<::action_factory::pb::SsExceptionEventStatistics>>
      exception_event_statistics;
  ProcessExceptionEventStatistics(system_state_messages,
                                  exception_event_statistics);

  PublishExceptionEventStatistics(exception_event_statistics);
}

void ExceptionEventStatisticsVNode::PublishExceptionEventStatistics(
    const std::vector<
        std::shared_ptr<::action_factory::pb::SsExceptionEventStatistics>>&
        exception_event_statistics) {
  const int64_t timestamp{base::WallClockNow()};
  for (const auto& event_stats : exception_event_statistics) {
    ::pb::SystemEvent event = ::pb::SystemEvent::default_instance();
    static std::string key = PlatformInterface::GetInstance().IsHILEnv()
                                 ? kExceptionEventStatisticsKeyTest
                                 : kExceptionEventStatisticsKey;
    event.set_eventkey(key);
    std::string json_val;
    {
      google::protobuf::util::JsonPrintOptions options;
      options.add_whitespace = false;
      options.always_print_primitive_fields = true;
      google::protobuf::util::MessageToJsonString(*event_stats, &json_val,
                                                  options);
    }
    event.set_eventval(json_val);
    event.set_timestamp(timestamp);
    event.set_acutype("cpubox1");
    GetPublisherByName<pb::SystemEvent>(kSystemEventPubName)->Publish(event);

    LOG_EVERY_N(INFO, 1) << "Send SystemEvent:\n" << event.DebugString();
  }
}

void ExceptionEventStatisticsVNode::ProcessFaultClassificationConfigStatus(
    const std::list<std::shared_ptr<
        const ::fault_detector::pb::FaultClassificationConfigStatus>>&
        fault_classification_config_status) {
  for (const auto& config_status : fault_classification_config_status) {
    faul_level_manifest_->CheckFaultClassificationConfigStatus(config_status);
  }
}

void ExceptionEventStatisticsVNode::ProcessExceptionEventStatistics(
    const std::list<std::shared_ptr<const ::pb::SystemState>>&
        system_state_messages,
    std::vector<
        std::shared_ptr<::action_factory::pb::SsExceptionEventStatistics>>&
        exception_event_statistics) {
  for (auto& system_state : system_state_messages) {
    exception_event_statistics_->CheckSystemState(
        system_state, exception_event_statistics, vehicle_mode_,
        is_vehicle_mode_changed_from_manual_);
  }
}

void ExceptionEventStatisticsVNode::ProcessCanbusMessage(
    const std::list<std::shared_ptr<const ::voy::Canbus>>& canbus_messages) {
  for (const auto& canbus : canbus_messages) {
    if (vehicle_mode_ == ::voy::VehicleMode::MANUAL &&
        canbus->mode() != vehicle_mode_) {
      is_vehicle_mode_changed_from_manual_ = true;
    } else {
      is_vehicle_mode_changed_from_manual_ = false;
    }
    vehicle_mode_ = canbus->mode();
  }
}

void ExceptionEventStatisticsVNode::ProcessRestoreRecord(
    const std::list<
        std::shared_ptr<const ::service_restore::pb::RestoreRecord>>&
        restore_record_messages) {
  for (auto& restore_record : restore_record_messages) {
    exception_event_statistics_->UpdateNodeState(restore_record);
  }
}

REGISTER_VNODE(ExceptionEventStatisticsVNode);

}  // namespace ss_exception_event_statistics
}  // namespace action_factory
}  // namespace system_supervisor
