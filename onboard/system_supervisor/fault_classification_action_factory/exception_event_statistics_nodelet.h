#ifndef ONBOARD_SYSTEM_SUPERVISOR_FAULT_CLASSIFICATION_ACTION_FACTORY_EXCEPTION_EVENT_STATISTICS_NODELET_H_
#define ONBOARD_SYSTEM_SUPERVISOR_FAULT_CLASSIFICATION_ACTION_FACTORY_EXCEPTION_EVENT_STATISTICS_NODELET_H_

#include <list>
#include <memory>
#include <set>
#include <string>
#include <unordered_map>
#include <vector>

#include "action_factory_protos/ss_exception_event_statistics.pb.h"
#include "av_comm/car_id.h"
#include "common/ros_param_getter.h"
#include "exception_event_statistics.h"
#include "fault_detector_protos/fault_config_status.pb.h"
#include "proto_util/proto_io.h"
#include "restart_protos/restart.pb.h"
#include "ros/param.h"
#include "varch/vnode/vnode.h"
#include "voy_protos/fault_level_manifest.pb.h"
#include "voy_protos/health.pb.h"
#include "voy_protos/system_control.pb.h"
#include "voy_protos/system_event.pb.h"

namespace system_supervisor {
namespace action_factory {
namespace ss_exception_event_statistics {

namespace vnode = varch::vnode;
using ExceptionEventStatisticsVNodeBase = vnode::VNode<
    vnode::PubMessages<::pb::SystemEvent>,
    vnode::SubMessages<::pb::SystemState, ::service_restore::pb::RestoreRecord,
                       ::voy::Canbus,
                       ::fault_detector::pb::FaultClassificationConfigStatus>>;

class ExceptionEventStatisticsVNode : public ExceptionEventStatisticsVNodeBase {
 public:
  ExceptionEventStatisticsVNode();

  bool Init() override;
  void Callback(
      std::list<std::shared_ptr<const ::pb::SystemState>>&
          system_state_messages,
      std::list<std::shared_ptr<const ::service_restore::pb::RestoreRecord>>&
          restore_record_messages,
      std::list<std::shared_ptr<const ::voy::Canbus>>& canbus_messages,
      std::list<std::shared_ptr<
          const ::fault_detector::pb::FaultClassificationConfigStatus>>&
          fault_classification_config_status) override;

  void PublishExceptionEventStatistics(
      const std::vector<
          std::shared_ptr<::action_factory::pb::SsExceptionEventStatistics>>&
          exception_event_statistics);

  void ProcessFaultClassificationConfigStatus(
      const std::list<std::shared_ptr<
          const ::fault_detector::pb::FaultClassificationConfigStatus>>&
          fault_classification_config_status);
  void ProcessExceptionEventStatistics(
      const std::list<std::shared_ptr<const ::pb::SystemState>>&
          system_state_messages,
      std::vector<
          std::shared_ptr<::action_factory::pb::SsExceptionEventStatistics>>&
          exception_event_statistics);
  void ProcessCanbusMessage(
      const std::list<std::shared_ptr<const ::voy::Canbus>>& canbus_messages);
  void ProcessRestoreRecord(
      const std::list<
          std::shared_ptr<const ::service_restore::pb::RestoreRecord>>&
          restore_record_messages);

  bool GetFaultExceptionType(
      const std::string& node_name, const pb::Fault& fault,
      fault_detector::pb::FaultExceptionType& fault_exception_type) {
    return faul_level_manifest_->GetFaultExceptionType(node_name, fault,
                                                       fault_exception_type);
  }

  // for unittest
  voy::VehicleMode GetVehicleMode() { return vehicle_mode_; }
  bool GetVehicleModeChangedFromManual() {
    return is_vehicle_mode_changed_from_manual_;
  }
  std::shared_ptr<ExceptionEventStatistics> GetExceptionEventStatistics() {
    return exception_event_statistics_;
  }

 private:
  std::shared_ptr<ExceptionEventStatistics> exception_event_statistics_;
  voy::VehicleMode vehicle_mode_{::voy::VehicleMode::MANUAL};
  bool is_vehicle_mode_changed_from_manual_{false};
  std::shared_ptr<FaultLevelManifest> faul_level_manifest_;
  RosParamGetter ros_param_getter_;
};

}  // namespace ss_exception_event_statistics
}  // namespace action_factory
}  // namespace system_supervisor

#endif  // ONBOARD_SYSTEM_SUPERVISOR_FAULT_CLASSIFICATION_ACTION_FACTORY_EXCEPTION_EVENT_STATISTICS_NODELET_H_
