#include "planner/path/reasoning/agent_intention/semantic_context/dynamic_overtake/dynamic_overtake_scenario_analyzer.h"

#include <vector>

#include <gtest/gtest.h>

#include "planner/behavior/util/agent_state/agent_in_lane_param_generation.h"
#include "planner/behavior/util/agent_state/agent_in_lane_state.h"
#include "planner/behavior/util/lane_sequence_geometry/lane_sequence_geometry_helper.h"
#include "planner/constants.h"
#include "planner/speed/profile/profile.h"
#include "planner/speed/reasoning/test/reasoning_test_fixture.h"
#include "planner/speed/test_util/test_util.h"
#include "pnc_map_service/map_elements/lane.h"

namespace planner {
namespace path {
namespace {
constexpr int64_t kInvalidTimestamp = -1;
}  // namespace

class DynamicOvertakeScenarioAnalyzerTest : public ::testing::Test,
                                            public speed::ReasoningTestFixture {
 public:
  void SetUp() override {
    timestamp_ = 1763805065401;
    SetUpSceneMap(hdmap::test_util::SceneType::kForklane);
    SetEgoPose(/*lane_id=*/128229, /*portion=*/0.5, /*speed=*/7.0);
    lane_sequence_ = GetLaneSequenceFromId(
        /*lane_sequence_id=*/{128229, 128253, 128237});
    current_lane_ = lane_sequence_[0];
    ls_geo_ = lane_selection::GetLaneSequenceGeometryForLaneKeep(
        *DCHECK_NOTNULL(world_model().GetLatestJointPncMapService().get()),
        lane_sequence_, world_model().robot_state().plan_init_state_snapshot());

    // Init ego in lane param.
    ego_param_ = lane_selection::GetEgoInLaneParam(world_model().robot_state(),
                                                   ls_geo_.nominal_path);

    // Init speed upper bound.
    speed_upper_bound_ = speed::CreateConstantSpeedProfileFromTime(
        /*t0=*/0.0, /*x0=*/0.0, /*v0=*/10.0,
        /*n_sample=*/constants::kTrajectoryHorizonInMSec,
        /*dt=*/constants::kTrajectoryIntervalInSec);

    // Load config.
    config_ = planner::PlannerConfigCenter::GetInstance()
                  .GetAgentIntentionConfig(behavior_type_)
                  .path_reasoning_config()
                  .dynamic_overtake_analyzer_config();
  }

  pb::BehaviorType behavior_type_ = pb::LANE_KEEP;
  EgoInLaneParams ego_param_;
  speed::Profile speed_upper_bound_;

  const pnc_map::Lane* current_lane_ = nullptr;
  std::vector<const pnc_map::Lane*> lane_sequence_;
  lane_selection::LaneSequenceGeometry ls_geo_;
  lane_selection::LaneSequenceCandidates ls_candidates_ =
      lane_selection::LaneSequenceCandidates(
          /*waypoint_cost_map_in=*/nullptr);

  pb::DynamicOvertakeAnalyzerConfig config_;
};

TEST_F(DynamicOvertakeScenarioAnalyzerTest, TestUpdateAbortRecords) {
  pb::DecoupledManeuverSeed prev_iter_seed;
  pb::DynamicOvertakeScenarioSeed* last_seed =
      prev_iter_seed.mutable_path_reasoning_seed()
          ->mutable_dynamic_overtake_scenario_seed();
  // Construct the last abort timestamp map.
  (*last_seed->mutable_abort_timestamp_map())[1] =
      ego_param_.plan_start_timestamp_ms - 1000;
  (*last_seed->mutable_abort_timestamp_map())[2] =
      ego_param_.plan_start_timestamp_ms - 2000;
  (*last_seed->mutable_abort_timestamp_map())[3] =
      ego_param_.plan_start_timestamp_ms - 6000;
  // Create dynamic overtake scenario analyzer.
  DynamicOvertakeScenarioAnalyzer analyzer(
      behavior_type_, world_model(), ls_candidates_, prev_iter_seed, ego_param_,
      speed_upper_bound_, *current_lane_, lane_sequence_, config_);
  pb::DynamicOvertakeScenarioSeed current_seed;
  analyzer.UpdateAbortRecords(&current_seed);
  EXPECT_EQ(current_seed.abort_timestamp_map().size(), 2);
  EXPECT_EQ(current_seed.abort_timestamp_map().at(1),
            ego_param_.plan_start_timestamp_ms - 1000);
  EXPECT_EQ(current_seed.abort_timestamp_map().at(2),
            ego_param_.plan_start_timestamp_ms - 2000);
}

TEST_F(DynamicOvertakeScenarioAnalyzerTest,
       TestGetLowEfficiencySpeedThreshold) {
  pb::DecoupledManeuverSeed prev_iter_seed;
  // Create dynamic overtake scenario analyzer.
  DynamicOvertakeScenarioAnalyzer analyzer(
      behavior_type_, world_model(), ls_candidates_, prev_iter_seed, ego_param_,
      speed_upper_bound_, *current_lane_, lane_sequence_, config_);
  const double low_efficiency_speed_threshold =
      analyzer.GetLowEfficiencySpeedThreshold(ls_geo_);
  const double expected_threshold_mps =
      60.0 / 3.6 * config_.slow_moving_speed_ratio();
  EXPECT_NEAR(low_efficiency_speed_threshold, expected_threshold_mps, 1e-3);

  // Test case that ego drives in the curvy lane.
  const std::vector<const pnc_map::Lane*> curvy_lanes = GetLaneSequenceFromId(
      /*lane_sequence_id=*/{145244, 128233});
  SetEgoPose(/*lane_id=*/145244, /*portion=*/0.5, /*speed=*/4.0);
  const lane_selection::LaneSequenceGeometry curvy_ls_geo =
      lane_selection::GetLaneSequenceGeometryForLaneKeep(
          *DCHECK_NOTNULL(world_model().GetLatestJointPncMapService().get()),
          curvy_lanes, world_model().robot_state().plan_init_state_snapshot());
  // Init ego in lane param.
  EgoInLaneParams ego_param_in_curvy = lane_selection::GetEgoInLaneParam(
      world_model().robot_state(), curvy_ls_geo.nominal_path);
  DynamicOvertakeScenarioAnalyzer curvy_lane_analyzer(
      behavior_type_, world_model(), ls_candidates_, prev_iter_seed,
      ego_param_in_curvy, speed_upper_bound_, *curvy_lanes[0], curvy_lanes,
      config_);
  const double low_efficiency_speed_threshold_in_curvy =
      curvy_lane_analyzer.GetLowEfficiencySpeedThreshold(curvy_ls_geo);
  const double expected_speed_threshold_in_curvy =
      std::sqrt(
          2.0 /
          ego_param_in_curvy.nearby_curvature_info.avg_absolute_curvature) *
      config_.slow_moving_speed_ratio();
  EXPECT_NEAR(low_efficiency_speed_threshold_in_curvy,
              expected_speed_threshold_in_curvy, 1e-3);
}

TEST_F(DynamicOvertakeScenarioAnalyzerTest,
       TestUpdateEgoDrivingStateAndPatience) {
  SetEgoPose(/*lane_id=*/128229, /*portion=*/0.5, /*speed=*/6.0);
  ego_param_ = lane_selection::GetEgoInLaneParam(world_model().robot_state(),
                                                 ls_geo_.nominal_path);
  // Set the last slowing driving states.
  pb::DecoupledManeuverSeed prev_iter_seed;
  pb::DynamicOvertakeScenarioSeed* last_seed =
      prev_iter_seed.mutable_path_reasoning_seed()
          ->mutable_dynamic_overtake_scenario_seed();
  last_seed->set_timestamp(ego_param_.plan_start_timestamp_ms - 100);
  last_seed->set_slow_driving_duration(4000);
  last_seed->set_slow_driving_odom(20.0);
  last_seed->set_start_normal_driving_timestamp(kInvalidTimestamp);

  DynamicOvertakeScenarioAnalyzer analyzer(
      behavior_type_, world_model(), ls_candidates_, prev_iter_seed, ego_param_,
      speed_upper_bound_, *current_lane_, lane_sequence_, config_);
  pb::DynamicOvertakeScenarioSeed current_seed;
  analyzer.UpdateEgoDrivingStateAndPatience(ls_geo_, &current_seed,
                                            /*debug=*/nullptr);

  EXPECT_EQ(current_seed.timestamp(), ego_param_.plan_start_timestamp_ms);
  EXPECT_EQ(current_seed.slow_driving_duration(), 4100);
  EXPECT_EQ(current_seed.slow_driving_odom(), 20.6);
  EXPECT_EQ(current_seed.start_normal_driving_timestamp(), kInvalidTimestamp);
  EXPECT_EQ(current_seed.normal_driving_duration(), 0.0);
  EXPECT_EQ(current_seed.normal_driving_odom(), 0.0);

  // Test the case ego exits the slow moving state.
  SetEgoPose(/*lane_id=*/128229, /*portion=*/0.5, /*speed=*/13.0);
  ego_param_ = lane_selection::GetEgoInLaneParam(world_model().robot_state(),
                                                 ls_geo_.nominal_path);
  // Set the last slowing driving states.
  last_seed->set_timestamp(ego_param_.plan_start_timestamp_ms - 100);
  last_seed->set_slow_driving_duration(4000);
  last_seed->set_slow_driving_odom(20.0);
  // Has normally drive for 10s.
  last_seed->set_start_normal_driving_timestamp(
      ego_param_.plan_start_timestamp_ms - 10000);
  last_seed->set_normal_driving_duration(10000);
  last_seed->set_normal_driving_odom(45.0);
  DynamicOvertakeScenarioAnalyzer analyzer_2(
      behavior_type_, world_model(), ls_candidates_, prev_iter_seed, ego_param_,
      speed_upper_bound_, *current_lane_, lane_sequence_, config_);
  analyzer_2.UpdateEgoDrivingStateAndPatience(ls_geo_, &current_seed,
                                              /*debug=*/nullptr);
  EXPECT_EQ(current_seed.timestamp(), ego_param_.plan_start_timestamp_ms);
  EXPECT_EQ(current_seed.slow_driving_duration(), 0);
  EXPECT_EQ(current_seed.slow_driving_odom(), 0);
  EXPECT_EQ(current_seed.start_normal_driving_timestamp(),
            ego_param_.plan_start_timestamp_ms - 10000);
  EXPECT_EQ(current_seed.normal_driving_duration(), 10100);
  EXPECT_EQ(current_seed.normal_driving_odom(), 46.3);

  // Test the case ego drives quickly but not long enough to exit slow moving
  // status. In this situation, slom driving state keeps as before.
  SetEgoPose(/*lane_id=*/128229, /*portion=*/0.5, /*speed=*/13.0);
  ego_param_ = lane_selection::GetEgoInLaneParam(world_model().robot_state(),
                                                 ls_geo_.nominal_path);
  // Set the last slowing driving states.
  last_seed->set_timestamp(ego_param_.plan_start_timestamp_ms - 100);
  last_seed->set_slow_driving_duration(4000);
  last_seed->set_slow_driving_odom(20.0);
  // Has normally drive for 10s.
  last_seed->set_start_normal_driving_timestamp(
      ego_param_.plan_start_timestamp_ms - 10000);
  last_seed->set_normal_driving_duration(9900);
  last_seed->set_normal_driving_odom(45.0);
  DynamicOvertakeScenarioAnalyzer analyzer_3(
      behavior_type_, world_model(), ls_candidates_, prev_iter_seed, ego_param_,
      speed_upper_bound_, *current_lane_, lane_sequence_, config_);
  analyzer_3.UpdateEgoDrivingStateAndPatience(ls_geo_, &current_seed,
                                              /*debug=*/nullptr);
  EXPECT_EQ(current_seed.timestamp(), ego_param_.plan_start_timestamp_ms);
  EXPECT_EQ(current_seed.slow_driving_duration(), 4000);
  EXPECT_EQ(current_seed.slow_driving_odom(), 20.0);
  EXPECT_EQ(current_seed.start_normal_driving_timestamp(),
            ego_param_.plan_start_timestamp_ms - 10000);
  EXPECT_EQ(current_seed.normal_driving_duration(), 10000);
  EXPECT_EQ(current_seed.normal_driving_odom(), 46.3);

  // Test the case ego drives slowly again, clear all normal driving states.
  SetEgoPose(/*lane_id=*/128229, /*portion=*/0.5, /*speed=*/5.0);
  ego_param_ = lane_selection::GetEgoInLaneParam(world_model().robot_state(),
                                                 ls_geo_.nominal_path);
  // Set the last slowing driving states.
  last_seed->set_timestamp(ego_param_.plan_start_timestamp_ms - 100);
  last_seed->set_slow_driving_duration(4000);
  last_seed->set_slow_driving_odom(20.0);
  // Has normally drive for 10s.
  last_seed->set_start_normal_driving_timestamp(
      ego_param_.plan_start_timestamp_ms - 10000);
  last_seed->set_normal_driving_duration(10000);
  last_seed->set_normal_driving_odom(45.0);
  DynamicOvertakeScenarioAnalyzer analyzer_4(
      behavior_type_, world_model(), ls_candidates_, prev_iter_seed, ego_param_,
      speed_upper_bound_, *current_lane_, lane_sequence_, config_);
  analyzer_4.UpdateEgoDrivingStateAndPatience(ls_geo_, &current_seed,
                                              /*debug=*/nullptr);
  EXPECT_EQ(current_seed.timestamp(), ego_param_.plan_start_timestamp_ms);
  EXPECT_EQ(current_seed.slow_driving_duration(), 14100);
  EXPECT_EQ(current_seed.slow_driving_odom(), 65.5);
  EXPECT_EQ(current_seed.start_normal_driving_timestamp(), kInvalidTimestamp);
  EXPECT_EQ(current_seed.normal_driving_duration(), 0);
  EXPECT_EQ(current_seed.normal_driving_odom(), 0);
}

TEST_F(DynamicOvertakeScenarioAnalyzerTest, TestWasOvertakenInLastCycle) {
  pb::DecoupledManeuverSeed prev_iter_seed;
  pb::IntentionResult* last_intention = prev_iter_seed.mutable_last_intent();
  pb::EgoIntention cyc_1_intention;
  cyc_1_intention.set_object_id(1);
  pb::SnapshotIntention* nudge_intenion =
      cyc_1_intention.add_snapshot_intentions();
  nudge_intenion->set_pass_state(pb::SnapshotIntention::PASS_LEFT);
  (*last_intention->mutable_object_intentions())[1] = cyc_1_intention;
  pb::DynamicOvertakeScenarioSeed* last_seed =
      prev_iter_seed.mutable_path_reasoning_seed()
          ->mutable_dynamic_overtake_scenario_seed();
  last_seed->add_dynamic_nudge_agent_ids(1);
  last_seed->add_dynamic_nudge_agent_ids(2);
  DynamicOvertakeScenarioAnalyzer analyzer(
      behavior_type_, world_model(), ls_candidates_, prev_iter_seed, ego_param_,
      speed_upper_bound_, *current_lane_, lane_sequence_, config_);

  EXPECT_TRUE(analyzer.WasOvertakenInLastCycle(1));
  EXPECT_FALSE(analyzer.WasOvertakenInLastCycle(2));
}

TEST_F(DynamicOvertakeScenarioAnalyzerTest, TestGetEgoCurrentPatienceDuration) {
  pb::DecoupledManeuverSeed prev_iter_seed;
  DynamicOvertakeScenarioAnalyzer analyzer(
      behavior_type_, world_model(), ls_candidates_, prev_iter_seed, ego_param_,
      speed_upper_bound_, *current_lane_, lane_sequence_, config_);
  pb::DynamicOvertakeScenarioSeed current_seed;
  // 0.7 patience.
  current_seed.set_slow_driving_duration(2000);
  current_seed.set_slow_driving_odom(30.0);
  const double encroach_time_tolerance_with_patience_0_7 =
      analyzer.GetEgoCurrentPatienceDuration(current_seed);
  EXPECT_EQ(encroach_time_tolerance_with_patience_0_7, 5.8);

  // 0.5 patience.
  current_seed.set_slow_driving_duration(10000);
  current_seed.set_slow_driving_odom(30.0);
  const double encroach_time_tolerance_with_patience_0_5 =
      analyzer.GetEgoCurrentPatienceDuration(current_seed);
  EXPECT_EQ(encroach_time_tolerance_with_patience_0_5, 5.0);

  // Zero patience.
  current_seed.set_slow_driving_duration(12000);
  current_seed.set_slow_driving_odom(120.0);
  const double encroach_time_tolerance_with_patience_0_0 =
      analyzer.GetEgoCurrentPatienceDuration(current_seed);
  EXPECT_EQ(encroach_time_tolerance_with_patience_0_0, 3.0);
}
}  // namespace path
}  // namespace planner
