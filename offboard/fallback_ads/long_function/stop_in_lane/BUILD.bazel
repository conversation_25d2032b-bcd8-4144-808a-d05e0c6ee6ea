package(default_visibility = ["//visibility:public"])

cc_library(
    name = "avoidance_deceleration",
    srcs = [
        "avoidance_deceleration.c",
    ],
    hdrs = [
        "avoidance_deceleration.h",
    ],
    copts = [
        "-x",
        "c",
        "-std=c11",
    ],
    include_prefix = "long_function/stop_in_lane/",
    deps = [
        "//offboard/fallback_ads/common_math_library:common_method",
    ],
)

cc_library(
    name = "free_stop_control",
    srcs = [
        "free_stop_control.c",
    ],
    hdrs = [
        "free_stop_control.h",
    ],
    copts = [
        "-x",
        "c",
        "-std=c11",
    ],
    include_prefix = "long_function/stop_in_lane/",
    deps = [
        "//offboard/fallback_ads/common_math_library:common_method",
        "//offboard/fallback_ads/common_math_library:vehicle_parameters",
        "//offboard/fallback_ads/long_function:long_function_parameter",
        "//offboard/fallback_ads/long_function:long_function_type",
    ],
)

cc_library(
    name = "long_threat_assessor",
    srcs = [
        "long_threat_assessor.c",
    ],
    hdrs = [
        "long_threat_assessor.h",
    ],
    copts = [
        "-x",
        "c",
        "-std=c11",
        "-Wno-missing-field-initializers",
    ],
    include_prefix = "long_function/stop_in_lane/",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
        "//offboard/fallback_ads/common_math_library:common_method",
        "//offboard/fallback_ads/common_math_library:interpolation",
        "//offboard/fallback_ads/common_math_library:vehicle_parameters",
        "//offboard/fallback_ads/long_function:long_function_parameter",
        "//offboard/fallback_ads/long_function:long_function_type",
        "//offboard/fallback_ads/long_function/stop_in_lane:avoidance_deceleration",
        "//offboard/fallback_ads/long_function/stop_in_lane:free_stop_control",
        "//offboard/fallback_ads/long_function/stop_in_lane:time_to_collision",
    ],
)

cc_library(
    name = "time_to_collision",
    srcs = [
        "time_to_collision.c",
    ],
    hdrs = [
        "time_to_collision.h",
    ],
    copts = [
        "-x",
        "c",
        "-std=c11",
    ],
    include_prefix = "long_function/stop_in_lane/",
    deps = [
        "//offboard/fallback_ads/common_math_library:common_method",
        "//offboard/fallback_ads/common_math_library:poly_solve",
        "//offboard/fallback_ads/long_function:long_function_parameter",
    ],
)
