#include "planner/decoupled_maneuvers/required_lateral_gap/requried_lateral_gap.h"

#include <gtest/gtest.h>

#include "planner/planning_gflags.h"
#include "planner/utility/config_center/planner_config_center.h"
#include "planner/world_model/planner_object/planner_object.h"

namespace planner {

class PrincipledRequiredLateralGapTest {
 public:
  double GetPerceptionError(PrincipledRequiredLateralGap* origin_class) const {
    return origin_class->perception_error_;
  }

  double GetLateralControlErrorSmallCurvature(
      PrincipledRequiredLateralGap* origin_class) const {
    return origin_class->lateral_control_error_small_curvature_;
  }

  double GetLateralControlErrorLargeCurvature(
      PrincipledRequiredLateralGap* origin_class) const {
    return origin_class->lateral_control_error_large_curvature_;
  }

  math::PiecewiseLinearFunction GetCustomExtraCriticalLateralGapAtEgoSpeed(
      PrincipledRequiredLateralGap* origin_class) const {
    return origin_class->custom_extra_critical_lateral_gap_at_ego_speed_;
  }

  math::PiecewiseLinearFunction GetComfortLateralGapAtEgoSpeedWithZeroCurvature(
      PrincipledRequiredLateralGap* origin_class) const {
    return origin_class->comfort_lateral_gap_at_ego_speed_with_zero_curvature_;
  }

  math::PiecewiseLinearFunction GetCustomExtraComfortLateralGapAtCurvature(
      PrincipledRequiredLateralGap* origin_class) const {
    return origin_class->custom_extra_comfort_lateral_gap_at_curvature_;
  }
};

TEST(RequiredLateralGapTest, ComputeRequiredLateralGapForObjects) {
  const planner::pb::RequiredLateralGapConfig& config =
      PlannerConfigCenter::GetInstance()
          .GetDecoupledForwardManeuverConfig()
          .required_lateral_gap_config();

  prediction::pb::Agent agent_proto;
  auto& tracked_object = *agent_proto.mutable_tracked_object();
  tracked_object.set_object_type(voy::perception::ObjectType::VEHICLE);
  const int64_t timestamp = 0;
  // Dummy pose at plan init ts.
  const TrafficParticipantPose pose_at_plan_init_ts(timestamp, tracked_object);
  const PlannerObject planner_object(agent_proto, timestamp,
                                     pose_at_plan_init_ts);

  RequiredLateralGap required_lateral_gap =
      ComputeRequiredLateralGap(planner_object, config);

  EXPECT_LE(required_lateral_gap.critical_required_lateral_gap,
            required_lateral_gap.comfort_required_lateral_gap);
}

TEST(RequiredLateralGapTest, ComputePrincipledRequiredLateralGapForMapCz) {
  voy::ConstructionZone cz_proto;
  cz_proto.set_space_type(voy::SpaceType::MAP_CHANGE_AREA);
  cz_proto.set_zone_source(voy::ZoneSource::REAL_TIME_MAP_ONLY);
  ConstructionZone cz(/*timestamp=*/1000, cz_proto,
                      /*perception_range_lanes=*/{});
  const auto cz_lat_gap =
      ComputePrincipledRequiredLateralGap(cz, pb::RequiredLateralGapConfig());
  EXPECT_LE(cz_lat_gap.GetCriticalRequiredLateralGapGoingStraightAtZeroSpeed(),
            0.2);
}

TEST(RequiredLateralGapTest, ComputePrincipledRequiredLateralGapForVehicle) {
  const planner::pb::RequiredLateralGapConfig& config =
      PlannerConfigCenter::GetInstance()
          .GetDecoupledForwardManeuverConfig()
          .required_lateral_gap_config();

  prediction::pb::Agent agent_proto;
  auto& tracked_object = *agent_proto.mutable_tracked_object();
  tracked_object.set_object_type(voy::perception::ObjectType::VEHICLE);
  tracked_object.set_width(2.0);
  tracked_object.set_length(4.0);
  const int64_t timestamp = 0;
  // Dummy pose at plan init ts.
  const TrafficParticipantPose pose_at_plan_init_ts(timestamp, tracked_object);
  PlannerObject planner_object(agent_proto, timestamp, pose_at_plan_init_ts);

  PrincipledRequiredLateralGap required_lateral_gap =
      ComputePrincipledRequiredLateralGap(planner_object, config);

  EXPECT_NEAR(required_lateral_gap
                  .GetCriticalRequiredLateralGapGoingStraightAtZeroSpeed(),
              0.216, 1e-3);
  EXPECT_NEAR(
      required_lateral_gap.GetCriticalRequiredLateralGapMakingTurnAtZeroSpeed(),
      0.332, 1e-3);
  EXPECT_NEAR(required_lateral_gap
                  .GetComfortRequiredLateralGapGoingStraightAtZeroSpeed(),
              0.8, 1e-3);
  EXPECT_NEAR(
      required_lateral_gap.GetComfortRequiredLateralGapMakingTurnAtZeroSpeed(),
      0.8, 1e-3);

  EXPECT_NEAR(required_lateral_gap
                  .GetCriticalSpeedLimit(/*curvature=*/0.0, /*lateral_gap=*/0.0)
                  .value(),
              0.0, 1e-3);

  EXPECT_NEAR(required_lateral_gap
                  .GetCriticalSpeedLimit(/*curvature=*/0.0, /*lateral_gap=*/0.3)
                  .value(),
              1.255, 1e-3);

  EXPECT_NEAR(required_lateral_gap
                  .GetCriticalSpeedLimit(/*curvature=*/0.0, /*lateral_gap=*/0.4)
                  .value(),
              2.755, 1e-3);

  EXPECT_NEAR(
      required_lateral_gap
          .GetCriticalSpeedLimit(/*curvature=*/0.0, /*lateral_gap=*/0.416)
          .value(),
      3.0, 1e-2);

  // If beyond critical required laterl gap, value does not exist.
  EXPECT_FALSE(
      required_lateral_gap
          .GetCriticalSpeedLimit(/*curvature=*/0.0, /*lateral_gap=*/1.0)
          .has_value());
}

TEST(RequiredLateralGapTest, ComputePrincipledRequiredLateralGapForCyclist) {
  const planner::pb::RequiredLateralGapConfig& config =
      PlannerConfigCenter::GetInstance()
          .GetDecoupledForwardManeuverConfig()
          .required_lateral_gap_config();

  prediction::pb::Agent agent_proto;
  auto& tracked_object = *agent_proto.mutable_tracked_object();
  tracked_object.set_object_type(voy::perception::ObjectType::CYCLIST);
  tracked_object.set_width(1.0);
  tracked_object.set_length(2.0);
  const int64_t timestamp = 0;
  // Dummy pose at plan init ts.
  const TrafficParticipantPose pose_at_plan_init_ts(timestamp, tracked_object);
  PlannerObject planner_object(agent_proto, timestamp, pose_at_plan_init_ts);

  PrincipledRequiredLateralGap required_lateral_gap =
      ComputePrincipledRequiredLateralGap(planner_object, config);

  EXPECT_NEAR(required_lateral_gap
                  .GetCriticalRequiredLateralGapGoingStraightAtZeroSpeed(),
              0.262, 1e-3);
  EXPECT_NEAR(
      required_lateral_gap.GetCriticalRequiredLateralGapMakingTurnAtZeroSpeed(),
      0.363, 1e-3);
  EXPECT_NEAR(required_lateral_gap
                  .GetComfortRequiredLateralGapGoingStraightAtZeroSpeed(),
              1.0, 1e-3);
  EXPECT_NEAR(
      required_lateral_gap.GetComfortRequiredLateralGapMakingTurnAtZeroSpeed(),
      1.0, 1e-3);
}

TEST(RequiredLateralGapTest, ComputePrincipledRequiredLateralGapForPED) {
  const planner::pb::RequiredLateralGapConfig& config =
      PlannerConfigCenter::GetInstance()
          .GetDecoupledForwardManeuverConfig()
          .required_lateral_gap_config();

  prediction::pb::Agent agent_proto;
  auto& tracked_object = *agent_proto.mutable_tracked_object();
  tracked_object.set_object_type(voy::perception::ObjectType::PED);
  tracked_object.set_width(0.5);
  tracked_object.set_length(0.5);
  const int64_t timestamp = 0;
  // Dummy pose at plan init ts.
  const TrafficParticipantPose pose_at_plan_init_ts(timestamp, tracked_object);
  PlannerObject planner_object(agent_proto, timestamp, pose_at_plan_init_ts);

  PrincipledRequiredLateralGap required_lateral_gap =
      ComputePrincipledRequiredLateralGap(planner_object, config);

  EXPECT_NEAR(required_lateral_gap
                  .GetCriticalRequiredLateralGapGoingStraightAtZeroSpeed(),
              0.241, 1e-3);
  EXPECT_NEAR(
      required_lateral_gap.GetCriticalRequiredLateralGapMakingTurnAtZeroSpeed(),
      0.349, 1e-3);
  EXPECT_NEAR(required_lateral_gap
                  .GetComfortRequiredLateralGapGoingStraightAtZeroSpeed(),
              1.0, 1e-3);
  EXPECT_NEAR(
      required_lateral_gap.GetComfortRequiredLateralGapMakingTurnAtZeroSpeed(),
      1.0, 1e-3);
}

TEST(RequiredLateralGapTest, ComputeRequiredLateralGapForStationaryUnknown) {
  const planner::pb::RequiredLateralGapConfig& config =
      PlannerConfigCenter::GetInstance()
          .GetDecoupledForwardManeuverConfig()
          .required_lateral_gap_config();

  prediction::pb::Agent agent_proto;
  auto& tracked_object = *agent_proto.mutable_tracked_object();
  tracked_object.set_object_type(voy::perception::ObjectType::UNKNOWN);
  tracked_object.set_width(0.3);
  const int64_t timestamp = 0;
  // Dummy pose at plan init ts.
  const TrafficParticipantPose pose_at_plan_init_ts(timestamp, tracked_object);
  PlannerObject planner_object(agent_proto, timestamp, pose_at_plan_init_ts);
  // The unknown object is stationary.
  planner_object.set_is_primary_stationary(true);

  RequiredLateralGap required_lateral_gap =
      ComputeRequiredLateralGap(planner_object, config);

  EXPECT_LE(required_lateral_gap.critical_required_lateral_gap,
            required_lateral_gap.comfort_required_lateral_gap);
  // NOTE: We treat the stationary unknown object with limited width as road
  // barrier.
  EXPECT_DOUBLE_EQ(required_lateral_gap.critical_required_lateral_gap,
                   config.barrier().critical_required_lateral_gap());
}

TEST(RequiredLateralGapTest, ComputeRequiredLateralGapForBicycleWithoutPerson) {
  const planner::pb::RequiredLateralGapConfig& config =
      PlannerConfigCenter::GetInstance()
          .GetDecoupledForwardManeuverConfig()
          .required_lateral_gap_config();

  prediction::pb::Agent agent_proto;
  auto& tracked_object = *agent_proto.mutable_tracked_object();
  tracked_object.set_object_type(voy::perception::ObjectType::UNKNOWN);
  tracked_object.set_width(0.6);
  const int64_t timestamp = 0;
  // Dummy pose at plan init ts.
  const TrafficParticipantPose pose_at_plan_init_ts(timestamp, tracked_object);
  PlannerObject planner_object(agent_proto, timestamp, pose_at_plan_init_ts);
  // The unknown object is bicycle without person.
  planner_object.set_is_cyc_no_person(true);

  RequiredLateralGap required_lateral_gap =
      ComputeRequiredLateralGap(planner_object, config);

  EXPECT_LE(required_lateral_gap.critical_required_lateral_gap,
            required_lateral_gap.comfort_required_lateral_gap);
}

TEST(RequiredLateralGapTest, ComputeRequiredLateralGapForVegetation) {
  const planner::pb::RequiredLateralGapConfig& config =
      PlannerConfigCenter::GetInstance()
          .GetDecoupledForwardManeuverConfig()
          .required_lateral_gap_config();

  prediction::pb::Agent agent_proto;
  auto& tracked_object = *agent_proto.mutable_tracked_object();
  tracked_object.set_object_type(voy::perception::ObjectType::UNKNOWN);
  tracked_object.set_width(0.6);
  const int64_t timestamp = 0;
  // Dummy pose at plan init ts.
  const TrafficParticipantPose pose_at_plan_init_ts(timestamp, tracked_object);
  PlannerObject planner_object(agent_proto, timestamp, pose_at_plan_init_ts);
  // The unknown object is bicycle without person.
  planner_object.set_is_vegetation(true);

  RequiredLateralGap required_lateral_gap =
      ComputeRequiredLateralGap(planner_object, config);

  EXPECT_LE(required_lateral_gap.critical_required_lateral_gap,
            required_lateral_gap.comfort_required_lateral_gap);
}

TEST(RequiredLateralGapTest, ComputeRequiredLateralGapForOccupancy) {
  const planner::pb::RequiredLateralGapConfig& config =
      PlannerConfigCenter::GetInstance()
          .GetDecoupledForwardManeuverConfig()
          .required_lateral_gap_config();

  prediction::pb::Agent agent_proto;
  auto& tracked_object = *agent_proto.mutable_tracked_object();
  tracked_object.set_object_type(voy::perception::ObjectType::UNKNOWN);
  tracked_object.add_attributes(voy::perception::CLUSTER_ONLY_TRACK);
  tracked_object.set_width(0.6);
  const int64_t timestamp = 0;
  // Dummy pose at plan init ts.
  const TrafficParticipantPose pose_at_plan_init_ts(timestamp, tracked_object);
  PlannerObject planner_object(agent_proto, timestamp, pose_at_plan_init_ts);
  planner_object.set_is_primary_stationary(true);
  RequiredLateralGap required_lateral_gap =
      ComputeRequiredLateralGap(planner_object, config);

  EXPECT_LE(required_lateral_gap.critical_required_lateral_gap,
            required_lateral_gap.comfort_required_lateral_gap);
  EXPECT_NEAR(required_lateral_gap.critical_required_lateral_gap, 0.2, 1e-3);
  EXPECT_NEAR(required_lateral_gap.comfort_required_lateral_gap, 0.8, 1e-3);
  // Set the object_width to be greater than the barrier threshold.
  tracked_object.set_width(0.3);
  // Dummy pose at plan init ts.
  const TrafficParticipantPose pose_at_plan_init_ts_2(timestamp,
                                                      tracked_object);
  PlannerObject planner_object_2(agent_proto, timestamp, pose_at_plan_init_ts);
  planner_object_2.set_is_primary_stationary(true);
  required_lateral_gap = ComputeRequiredLateralGap(planner_object, config);

  EXPECT_LE(required_lateral_gap.critical_required_lateral_gap,
            required_lateral_gap.comfort_required_lateral_gap);
  EXPECT_NEAR(required_lateral_gap.critical_required_lateral_gap, 0.2, 1e-3);
  EXPECT_NEAR(required_lateral_gap.comfort_required_lateral_gap, 0.8, 1e-3);
}

TEST(RequiredLateralGapTest, ComputePrincipledReqLatGapForRaUnstuckObject) {
  const planner::pb::RequiredLateralGapConfig& config =
      PlannerConfigCenter::GetInstance()
          .GetDecoupledForwardManeuverConfig()
          .required_lateral_gap_config();

  prediction::pb::Agent agent_proto;
  auto& tracked_object = *agent_proto.mutable_tracked_object();
  tracked_object.set_object_type(voy::perception::ObjectType::VEHICLE);
  tracked_object.set_width(3.5);
  tracked_object.set_length(7.0);
  tracked_object.set_height(3.0);
  const int64_t timestamp = 0;
  // Dummy pose at plan init ts.
  const TrafficParticipantPose pose_at_plan_init_ts(timestamp, tracked_object);
  PlannerObject planner_object(agent_proto, timestamp, pose_at_plan_init_ts);
  planner_object.set_should_reduce_critical_req_lat_gap_by_ra_confirmation(
      true);

  PrincipledRequiredLateralGap required_lateral_gap =
      ComputePrincipledRequiredLateralGap(planner_object, config);

  EXPECT_NEAR(required_lateral_gap
                  .GetCriticalRequiredLateralGapGoingStraightAtZeroSpeed(),
              0.18, 1e-3);
  EXPECT_NEAR(
      required_lateral_gap.GetCriticalRequiredLateralGapMakingTurnAtZeroSpeed(),
      0.31, 1e-3);
}

TEST(RequiredLateralGapTest, ComputePrincipledReqLatGapForPhysicalBoundary) {
  PrincipledRequiredLateralGap required_lateral_gap =
      ComputePrincipledRequiredLateralGapForPhysicalBoundary();

  EXPECT_DOUBLE_EQ(required_lateral_gap
                       .GetCriticalRequiredLateralGapGoingStraightAtZeroSpeed(),
                   0.18);
  EXPECT_DOUBLE_EQ(
      required_lateral_gap.GetCriticalRequiredLateralGapMakingTurnAtZeroSpeed(),
      0.31);
  EXPECT_DOUBLE_EQ(required_lateral_gap.GetCriticalRequiredLateralGap(
                       /*speed_mps=*/0.0, /*curvature=*/0.0),
                   0.18);
  EXPECT_DOUBLE_EQ(required_lateral_gap.GetCriticalRequiredLateralGap(
                       /*speed_mps=*/0.0, /*curvature=*/0.04),
                   0.18);
  EXPECT_DOUBLE_EQ(required_lateral_gap.GetCriticalRequiredLateralGap(
                       /*speed_mps=*/0.0, /*curvature=*/0.05),
                   0.245);
  EXPECT_DOUBLE_EQ(required_lateral_gap.GetCriticalRequiredLateralGap(
                       /*speed_mps=*/0.0, /*curvature=*/0.06),
                   0.31);
  EXPECT_NEAR(required_lateral_gap.GetCriticalRequiredLateralGap(
                  /*speed_mps=*/2.5, /*curvature=*/0.05),
              0.352, 0.001);
}

// NOTE: Large vehicles have not been integrated into PLG. This is a placeholder
// unit test for guarding against any unexpected changes to their legacy RLG
// settings. Update the unit test after the PLG integration get completed.
TEST(RequiredLateralGapTest, ComputePrincipledReqLatGapForLargeVehicle) {
  const planner::pb::RequiredLateralGapConfig& config =
      PlannerConfigCenter::GetInstance()
          .GetDecoupledForwardManeuverConfig()
          .required_lateral_gap_config();

  prediction::pb::Agent agent_proto;
  auto& tracked_object = *agent_proto.mutable_tracked_object();
  tracked_object.set_object_type(voy::perception::ObjectType::VEHICLE);
  tracked_object.set_width(3.0);
  tracked_object.set_height(4.0);
  tracked_object.set_length(20.0);
  const int64_t timestamp = 0;
  // Dummy pose at plan init ts.
  const TrafficParticipantPose pose_at_plan_init_ts(timestamp, tracked_object);
  PlannerObject planner_object(agent_proto, timestamp, pose_at_plan_init_ts);
  planner_object.set_is_primary_stationary(true);
  EXPECT_TRUE(planner_object.is_large_vehicle());

  PrincipledRequiredLateralGap required_lateral_gap =
      ComputePrincipledRequiredLateralGap(planner_object, config);

  // Large vehicles PLGs are expected to be the legacy RLGs.
  EXPECT_DOUBLE_EQ(0.4, config.vehicle().critical_required_lateral_gap());
  EXPECT_DOUBLE_EQ(required_lateral_gap
                       .GetCriticalRequiredLateralGapGoingStraightAtZeroSpeed(),
                   config.vehicle().critical_required_lateral_gap());
  EXPECT_DOUBLE_EQ(
      required_lateral_gap.GetCriticalRequiredLateralGapMakingTurnAtZeroSpeed(),
      config.vehicle().critical_required_lateral_gap());
  EXPECT_DOUBLE_EQ(
      required_lateral_gap.ComputeCriticalRequiredLateralGap(20.0, 0.0),
      required_lateral_gap
          .GetCriticalRequiredLateralGapGoingStraightAtZeroSpeed());

  EXPECT_DOUBLE_EQ(0.8, config.vehicle().comfort_required_lateral_gap());
  EXPECT_DOUBLE_EQ(required_lateral_gap
                       .GetComfortRequiredLateralGapGoingStraightAtZeroSpeed(),
                   config.vehicle().comfort_required_lateral_gap());
  EXPECT_DOUBLE_EQ(
      required_lateral_gap.GetComfortRequiredLateralGapMakingTurnAtZeroSpeed(),
      config.vehicle().comfort_required_lateral_gap());
  EXPECT_DOUBLE_EQ(
      required_lateral_gap.ComputeComfortRequiredLateralGap(20.0, 0.0),
      required_lateral_gap
          .GetComfortRequiredLateralGapGoingStraightAtZeroSpeed());
}

TEST(RequiredLateralGapTest, ProtoConversion) {
  // Config is same as ComputePrincipledRequiredLateralGapForVehicle.
  const planner::pb::RequiredLateralGapConfig& config =
      PlannerConfigCenter::GetInstance()
          .GetDecoupledForwardManeuverConfig()
          .required_lateral_gap_config();

  prediction::pb::Agent agent_proto;
  auto& tracked_object = *agent_proto.mutable_tracked_object();
  tracked_object.set_object_type(voy::perception::ObjectType::VEHICLE);
  tracked_object.set_width(2.0);
  tracked_object.set_length(4.0);
  const int64_t timestamp = 0;
  // Dummy pose at plan init ts.
  const TrafficParticipantPose pose_at_plan_init_ts(timestamp, tracked_object);
  PlannerObject planner_object(agent_proto, timestamp, pose_at_plan_init_ts);

  PrincipledRequiredLateralGap required_lateral_gap1 =
      ComputePrincipledRequiredLateralGap(planner_object, config);

  PrincipledRequiredLateralGapTest friend_class;

  // Extract internal states.
  double legacy_critical_required_lateral_gap_in_meter1 =
      required_lateral_gap1.GetLegacyCriticalRequiredLateralGapInMeter();
  double perception_error1 =
      friend_class.GetPerceptionError(&required_lateral_gap1);
  double lateral_control_error_small_curvature1 =
      friend_class.GetLateralControlErrorSmallCurvature(&required_lateral_gap1);
  double lateral_control_error_large_curvature1 =
      friend_class.GetLateralControlErrorLargeCurvature(&required_lateral_gap1);
  // Extract common used results for timing.
  double small_curvature_zero_speed_critical_lateral_gap1 =
      required_lateral_gap1
          .GetCriticalRequiredLateralGapGoingStraightAtZeroSpeed();
  double large_curvature_zero_speed_critical_lateral_gap1 =
      required_lateral_gap1
          .GetCriticalRequiredLateralGapMakingTurnAtZeroSpeed();
  double small_curvature_zero_speed_comfort_lateral_gap1 =
      required_lateral_gap1
          .GetComfortRequiredLateralGapGoingStraightAtZeroSpeed();
  double large_curvature_zero_speed_comfort_lateral_gap1 =
      required_lateral_gap1.GetComfortRequiredLateralGapMakingTurnAtZeroSpeed();

  // Extract PiecewiseLinearFunction states.
  const auto& custom_extra_critical_lateral_gap_at_ego_speed1 =
      friend_class.GetCustomExtraCriticalLateralGapAtEgoSpeed(
          &required_lateral_gap1);
  const auto& comfort_lateral_gap_at_ego_speed_with_zero_curvature1 =
      friend_class.GetComfortLateralGapAtEgoSpeedWithZeroCurvature(
          &required_lateral_gap1);
  const auto& custom_extra_comfort_lateral_gap_at_curvature1 =
      friend_class.GetCustomExtraComfortLateralGapAtCurvature(
          &required_lateral_gap1);

  // Test ToProto().
  pb::PrincipledRequiredLateralGap required_lateral_gap_proto =
      required_lateral_gap1.ToProto();
  // Validate PiecewiseLinearFunction fields in proto.
  EXPECT_DOUBLE_EQ(required_lateral_gap_proto
                       .custom_extra_critical_lateral_gap_at_ego_speed()
                       .xs(0),
                   custom_extra_critical_lateral_gap_at_ego_speed1.start_x());
  EXPECT_DOUBLE_EQ(
      required_lateral_gap_proto
          .comfort_lateral_gap_at_ego_speed_with_zero_curvature()
          .xs(0),
      comfort_lateral_gap_at_ego_speed_with_zero_curvature1.start_x());
  EXPECT_DOUBLE_EQ(
      required_lateral_gap_proto.custom_extra_comfort_lateral_gap_at_curvature()
          .xs(0),
      custom_extra_comfort_lateral_gap_at_curvature1.start_x());

  // Validate other fields in proto.
  EXPECT_DOUBLE_EQ(legacy_critical_required_lateral_gap_in_meter1,
                   required_lateral_gap_proto
                       .legacy_critical_required_lateral_gap_in_meter());
  EXPECT_DOUBLE_EQ(perception_error1,
                   required_lateral_gap_proto.perception_error());
  EXPECT_DOUBLE_EQ(
      lateral_control_error_small_curvature1,
      required_lateral_gap_proto.lateral_control_error_small_curvature());
  EXPECT_DOUBLE_EQ(
      lateral_control_error_large_curvature1,
      required_lateral_gap_proto.lateral_control_error_large_curvature());

  // Test constructor with FromProto().
  PrincipledRequiredLateralGap required_lateral_gap2(
      required_lateral_gap_proto);
  double legacy_critical_required_lateral_gap_in_meter2 =
      required_lateral_gap2.GetLegacyCriticalRequiredLateralGapInMeter();
  double perception_error2 =
      friend_class.GetPerceptionError(&required_lateral_gap2);
  double lateral_control_error_small_curvature2 =
      friend_class.GetLateralControlErrorSmallCurvature(&required_lateral_gap2);
  double lateral_control_error_large_curvature2 =
      friend_class.GetLateralControlErrorLargeCurvature(&required_lateral_gap2);
  // Validate internal states.
  EXPECT_DOUBLE_EQ(legacy_critical_required_lateral_gap_in_meter1,
                   legacy_critical_required_lateral_gap_in_meter2);
  EXPECT_DOUBLE_EQ(perception_error1, perception_error2);
  EXPECT_DOUBLE_EQ(lateral_control_error_small_curvature1,
                   lateral_control_error_small_curvature2);
  EXPECT_DOUBLE_EQ(lateral_control_error_large_curvature1,
                   lateral_control_error_large_curvature2);
  // Validate PiecewiseLinearFunction fields in required_lateral_gap2.
  const auto& custom_extra_critical_lateral_gap_at_ego_speed2 =
      friend_class.GetCustomExtraCriticalLateralGapAtEgoSpeed(
          &required_lateral_gap2);
  const auto& comfort_lateral_gap_at_ego_speed_with_zero_curvature2 =
      friend_class.GetComfortLateralGapAtEgoSpeedWithZeroCurvature(
          &required_lateral_gap2);
  const auto& custom_extra_comfort_lateral_gap_at_curvature2 =
      friend_class.GetCustomExtraComfortLateralGapAtCurvature(
          &required_lateral_gap2);

  EXPECT_DOUBLE_EQ(custom_extra_critical_lateral_gap_at_ego_speed1.start_x(),
                   custom_extra_critical_lateral_gap_at_ego_speed2.start_x());
  EXPECT_DOUBLE_EQ(
      comfort_lateral_gap_at_ego_speed_with_zero_curvature1.start_x(),
      comfort_lateral_gap_at_ego_speed_with_zero_curvature2.start_x());
  EXPECT_DOUBLE_EQ(custom_extra_comfort_lateral_gap_at_curvature1.start_x(),
                   custom_extra_comfort_lateral_gap_at_curvature2.start_x());

  // Validate common used results for timing.
  double small_curvature_zero_speed_critical_lateral_gap2 =
      required_lateral_gap2
          .GetCriticalRequiredLateralGapGoingStraightAtZeroSpeed();
  double large_curvature_zero_speed_critical_lateral_gap2 =
      required_lateral_gap2
          .GetCriticalRequiredLateralGapMakingTurnAtZeroSpeed();
  double small_curvature_zero_speed_comfort_lateral_gap2 =
      required_lateral_gap2
          .GetComfortRequiredLateralGapGoingStraightAtZeroSpeed();
  double large_curvature_zero_speed_comfort_lateral_gap2 =
      required_lateral_gap2.GetComfortRequiredLateralGapMakingTurnAtZeroSpeed();
  EXPECT_DOUBLE_EQ(small_curvature_zero_speed_critical_lateral_gap1,
                   small_curvature_zero_speed_critical_lateral_gap2);
  EXPECT_DOUBLE_EQ(large_curvature_zero_speed_critical_lateral_gap1,
                   large_curvature_zero_speed_critical_lateral_gap2);
  EXPECT_DOUBLE_EQ(small_curvature_zero_speed_comfort_lateral_gap1,
                   small_curvature_zero_speed_comfort_lateral_gap2);
  EXPECT_DOUBLE_EQ(large_curvature_zero_speed_comfort_lateral_gap1,
                   large_curvature_zero_speed_comfort_lateral_gap2);
}

}  // namespace planner
