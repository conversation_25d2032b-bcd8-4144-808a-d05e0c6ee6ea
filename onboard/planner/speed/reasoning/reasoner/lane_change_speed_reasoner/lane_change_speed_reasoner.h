#ifndef ONBOARD_PLANNER_SPEED_REASONING_REASONER_LANE_CHANGE_SPEED_REASONER_LANE_CHANGE_SPEED_REASONER_H_
#define ONBOARD_PLANNER_SPEED_REASONING_REASONER_LANE_CHANGE_SPEED_REASONER_LANE_CHANGE_SPEED_REASONER_H_

#include <map>
#include <string>
#include <unordered_map>
#include <vector>

#include <tbb/concurrent_unordered_map.h>
#include "planner/decoupled_maneuvers/lane_change/gap_quality_evaluator.h"
#include "planner/decoupled_maneuvers/lane_change/lane_change_env_analyzer.h"
#include "planner/speed/reasoning/agent_policy.h"
#include "planner/speed/reasoning/agent_trajectory_info.h"
#include "planner/speed/reasoning/prediction_decision.h"
#include "planner/speed/reasoning/prediction_decision_maker.h"
#include "planner/speed/reasoning_input/trajectory_info.h"
#include "planner/speed/reasoning_input/world_context.h"
#include "planner/speed/reference/cautious_driving_limiter.h"
#include "planner_protos/behavior_reasoners_debug.pb.h"
#include "planner_protos/lane_change.pb.h"
#include "prediction_protos/predicted_trajectory.pb.h"
#include "voy_protos/tracked_objects.pb.h"

namespace planner {
namespace speed {
namespace reasoner {

class LaneChangeSpeedReasoner {
 public:
  // Reasons about the environment and interested agents on the target region.
  // Adds corresponding constraints. Returns true if any gap align constraint is
  // added.
  bool Reason(const WorldContext& world_context,
              const std::vector<PredictionDecisionMakerInput>& reasoning_inputs,
              const TrajectoryInfo& trajectory_info,
              const std::unordered_map<ObjectId, planner::PlannerObject>&
                  planner_object_map,
              const planner::pb::DecoupledManeuverSeed& previous_iter_seed,
              const pb::NominalModelConfig& nominal_model_config,
              pb::LaneChangeSpeedReasonerSeed* mutable_current_iter_seed,
              ConstraintCreator* constraint_creator_ptr,
              GlobalStrategySetter* global_strategy_setter,
              CautiousDrivingLimiter* reference_limiter_ptr,
              GapQualityEvaluator* gap_quality_evaluator_ptr,
              pb::ReasoningDebug* debug_proto_ptr);

  [[nodiscard]] pb::ReasonerId id() const {
    return pb::ReasonerId::LANE_CHANGE_PREPARATION;
  }

 private:
  // Adds pass headway.
  void AddPassHeadway(
      const PlannerObject& planner_object,
      const LaneChangeObjectInfo& object_info,
      const planner::pb::SelectedLaneChangeGapInfo& last_selected_gap_info,
      const std::optional<double>& dist_to_last_lc_chance_point,
      double ego_speed, ConstraintMutator& mutator,
      std::ostringstream& debug_oss);

  // Adds agent reaction for the gap align overlap regions.
  void AddAgentReactionForOverlapRegion(
      double ego_speed, const PlannerObject& planner_object,
      const LaneChangeObjectInfo& object_info,
      const pb::NominalModelConfig& nominal_model_config,
      double generic_urgency_score, bool is_in_collision_info_map,
      bool has_static_queue, ConstraintMutator& mutator) const;

  // Adds constraint from the primary predicted trajectory of agent if the
  // prediction is nominal.
  bool AddConstraintFromPrimaryTrajectory(
      const pb::SpeedSeed& last_speed_seed,
      const planner::pb::SelectedLaneChangeGapInfo& last_selected_gap_info,
      const LaneChangeObjectInfo* last_selected_lead_object_info,
      const LaneChangeObjectInfo* last_selected_tail_object_info,
      const PlannerObject& planner_object,
      const LaneChangeObjectInfo& object_info,
      const std::optional<double>& dist_to_last_lc_chance_point,
      const std::optional<int64_t>& soft_pass_id,
      const std::optional<int64_t>& soft_yield_id, double generic_urgency_score,
      double ego_speed, double safe_yield_acceleration,
      double safe_pass_max_speed, bool should_set_no_yield,
      bool is_in_collision_info_map, bool has_enough_room_for_speed_up,
      bool is_ego_overtaking_nearby_static_vehicle, bool has_static_queue,
      const pb::NominalModelConfig& nominal_model_config,
      ConstraintCreator* constraint_creator_ptr,
      pb::LaneChangeSpeedReasonerDebug* lc_speed_debug,
      pb::LaneChangeSpeedReasonerSeed* mutable_current_iter_seed,
      std::ostringstream& debug_oss);

  // Adds a stop constraint for an urgency info if it should be added.
  void AddStopConstraintForUrgencyInfo(
      const std::optional<LaneChangeUrgencyInfo>& urgency_info,
      const RobotStateSnapshot& snapshot, bool can_trigger_crawl,
      bool is_inexecutable_lc_signal, double safe_yield_acceleration,
      ConstraintCreator* constraint_creator_ptr) const;

  // Adds speed limit by consecutive lane change info to avoid ego from driving
  // too fast and left little span for subsequent lane changes.
  void AddSpeedLimitByConsecutiveLaneChangeInfo(
      const planner::pb::LaneChangeMetadata& lane_change_metadata,
      CautiousDrivingLimiter& reference_limiter_ptr,
      std::ostringstream& debug_oss) const;

  void AddSpeedLimitByLaneChangeEnd(
      bool is_lane_change_to_fork,
      const std::optional<LaneChangeUrgencyInfo>& lane_change_end_urgency_info,
      const RobotStateSnapshot& snapshot, double safe_yield_acc,
      CautiousDrivingLimiter& reference_limiter,
      std::ostringstream& debug_oss) const;

  // Adds the proximity speed for the objects on the target neighbor region.
  void AddProximitySpeedConstraintForObjectsOnTargetNeighborRegion(
      const LaneChangeInfo& lane_change_info,
      ConstraintCreator* constraint_creator_ptr) const;

  // Sets pass max a/v/j for a constraint through |mutator|.
  void SetPassKinematicParams(
      const LaneChangeObjectInfo* last_selected_tail_object_info,
      const LaneChangeObjectInfo& object_info, bool set_soft_constraint,
      double generic_urgency_score, double ego_speed,
      double safe_pass_max_speed, ConstraintMutator& mutator,
      std::ostringstream& debug_oss);

  // Sets yield min a/v/j for a constraint through |mutator|.
  void SetYieldKinematicParams(
      const LaneChangeObjectInfo* last_selected_lead_object_info,
      const PlannerObject& planner_object,
      const LaneChangeObjectInfo& object_info,
      const std::optional<double>& dist_to_last_lc_chance_point,
      bool set_soft_constraint, double generic_urgency_score, double ego_speed,
      double safe_yield_acceleration, bool has_enough_room_for_speed_up,
      bool is_ego_overtaking_nearby_static_vehicle, double yield_extra_distance,
      double yield_extra_time,
      bool& should_decrease_yield_params_for_agent_in_front_of_ego,
      pb::LaneChangeSpeedReasonerSeed* mutable_current_iter_seed,
      ConstraintMutator& mutator, std::ostringstream& debug_oss) const;

  enum class SpeedLimitType : int {
    kConsecutiveLaneChange = 0,
    kLaneChangeEnd = 1
  };

  static constexpr const char* kLogHeader = "[lane change][speed reasoner] ";
};

}  // namespace reasoner
}  // namespace speed
}  // namespace planner

#endif  // ONBOARD_PLANNER_SPEED_REASONING_REASONER_LANE_CHANGE_SPEED_REASONER_LANE_CHANGE_SPEED_REASONER_H_
