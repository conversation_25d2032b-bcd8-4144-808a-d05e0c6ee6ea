#include "av_comm/node_names.h"
#include "node/breakpad_exception_handler.h"
#include "node/log_context.h"
#include "node/node_runner.h"
#include "onboard_monitor.h"

DEFINE_string(config_file, "", "Path to config file");

int main(int argc, char* argv[]) {
  google::ParseCommandLineFlags(&argc, &argv, true /* remove_flags */);
  std::string node_name = av_comm::node_name::kOnboardMonitorNode;
  if (av_comm::IsGen4Orin1()) {
    node_name = av_comm::node_name::kOnboardMonitorNodeOrin1;
  } else if (av_comm::IsGen4Orin2()) {
    node_name = av_comm::node_name::kOnboardMonitorNodeOrin2;
  }
  ros::init(argc, argv, node_name, ros::init_options::NoSigintHandler);
  node::LogContext log_context(ros::this_node::getName().c_str());
  node::BreakpadExceptionHandler eh(ros::this_node::getName());
  node::NodeRunner node_runner(1, true, false);

  // Create and initialize monitor
  onboard_monitor::OnboardMonitor monitor;
  monitor.Init();
  monitor.Run(node_runner.io_service());

  // Construct a node_runner and run
  return node_runner.Run().value();
}
