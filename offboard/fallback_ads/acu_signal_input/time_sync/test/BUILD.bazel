load("//bazel:defs.bzl", "voy_cc_test")

package(default_visibility = ["//visibility:public"])

voy_cc_test(
    name = "time_sync_test",
    srcs = ["time_sync_test.cpp"],
    copts = [
        "-x",
        "c++",
        "-std=c++14",
    ],
    deps = [
        "//offboard/fallback_ads:fallback_l2_simulation",
        "//offboard/fallback_ads/acu_signal_input/time_sync",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_acu_sig_input_type",
        "//offboard/fallback_ads/common_math_library:can_util",
        "@voy-sdk//:gtest",
    ],
)
