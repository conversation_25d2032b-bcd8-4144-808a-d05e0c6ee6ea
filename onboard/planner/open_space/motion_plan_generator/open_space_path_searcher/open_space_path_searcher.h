#ifndef ONBOARD_PLANNER_OPEN_SPACE_MOTION_PLAN_GENERATOR_OPEN_SPACE_PATH_SEARCHER_OPEN_SPACE_PATH_SEARCHER_H_
#define ONBOARD_PLANNER_OPEN_SPACE_MOTION_PLAN_GENERATOR_OPEN_SPACE_PATH_SEARCHER_OPEN_SPACE_PATH_SEARCHER_H_

#include "planner/open_space/motion_plan_generator/open_space_path_searcher/grid.h"
#include "planner/open_space/motion_plan_generator/open_space_path_searcher/hybrid_a_star_search.h"
#include "planner/open_space/motion_plan_generator/open_space_path_searcher/open_space_path_search_result.h"
#include "planner_protos/nominal_path_planner_config.pb.h"

namespace planner {

namespace open_space {

// The class performs the main open space search pipeline, generates a
// collision free and non-holonomic path contains multiple motion sequence from
// a start pose and end pose.
class OpenSpacePathSearcher {
 public:
  explicit OpenSpacePathSearcher(const pb::OpenSpaceSearchConfig& config)
      : config_(config),
        grid_(config.grid_config()),
        hybrid_a_star_search_(config, &grid_) {}

  // Returns the open space path search result with the prepared motion plan
  // problem, executes the main open space search process, including open space
  // grid construction and hybrid A* search two steps.
  std::optional<OpenSpacePathSearchResult> Search(
      const MotionPlanProblem& motion_plan_problem);

 private:
  const pb::OpenSpaceSearchConfig& config_;
  open_space::Grid grid_;
  open_space::HybridAStarSearch hybrid_a_star_search_;

  friend class OpenSpacePathSearcherBenchmark_OpenSpaceGrid_Benchmark;
  friend class OpenSpacePathSearcherBenchmark_HybridAStarSearch_Benchmark;
};

}  // namespace open_space

}  // namespace planner

#endif  // ONBOARD_PLANNER_OPEN_SPACE_MOTION_PLAN_GENERATOR_OPEN_SPACE_PATH_SEARCHER_OPEN_SPACE_PATH_SEARCHER_H_
