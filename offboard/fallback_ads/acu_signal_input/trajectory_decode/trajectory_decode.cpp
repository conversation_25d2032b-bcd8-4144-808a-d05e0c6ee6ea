#include "acu_signal_input/trajectory_decode/trajectory_decode.h"

#include <cstddef>
#include <cstdint>

#include "_out/Appl/GenData/Components/Rte_AcuSigInput.h"
#include "_out/Appl/GenData/Components/Rte_AcuSigInput_Type.h"
#include "_out/Appl/GenData/Rte_Type.h"
#include "common_math_library/assert_cfg.h"
#include "common_math_library/can_util.h"
#include "common_math_library/common_method.h"

namespace fallback_adas {
namespace acu_signal_input {

void AcuTrajectoryPose00To03Encode(TrajectoryPose* TrajectoryPose_ptr) {
  // trajectory pose 0 ~ 3
  TrajectoryPoseDecode(TrajectoryPose_ptr, 0, AcuTrajectoryPose00To03,
                       TrajectoryPose00);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 1, AcuTrajectoryPose00To03,
                       TrajectoryPose01);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 2, AcuTrajectoryPose00To03,
                       TrajectoryPose02);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 3, AcuTrajectoryPose00To03,
                       TrajectoryPose03);
}

void AcuTrajectoryPose04To07Encode(TrajectoryPose* TrajectoryPose_ptr) {
  // trajectory pose 4 ~ 7
  TrajectoryPoseDecode(TrajectoryPose_ptr, 4, AcuTrajectoryPose04To07,
                       TrajectoryPose04);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 5, AcuTrajectoryPose04To07,
                       TrajectoryPose05);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 6, AcuTrajectoryPose04To07,
                       TrajectoryPose06);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 7, AcuTrajectoryPose04To07,
                       TrajectoryPose07);
}

void AcuTrajectoryPose08To11Encode(TrajectoryPose* TrajectoryPose_ptr) {
  // trajectory pose 8 ~ 11
  TrajectoryPoseDecode(TrajectoryPose_ptr, 8, AcuTrajectoryPose08To11,
                       TrajectoryPose08);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 9, AcuTrajectoryPose08To11,
                       TrajectoryPose09);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 10, AcuTrajectoryPose08To11,
                       TrajectoryPose10);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 11, AcuTrajectoryPose08To11,
                       TrajectoryPose11);
}

void AcuTrajectoryPose12To15Encode(TrajectoryPose* TrajectoryPose_ptr) {
  // trajectory pose 12 ~ 15
  TrajectoryPoseDecode(TrajectoryPose_ptr, 12, AcuTrajectoryPose12To15,
                       TrajectoryPose12);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 13, AcuTrajectoryPose12To15,
                       TrajectoryPose13);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 14, AcuTrajectoryPose12To15,
                       TrajectoryPose14);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 15, AcuTrajectoryPose12To15,
                       TrajectoryPose15);
}

void AcuTrajectoryPose16To19Encode(TrajectoryPose* TrajectoryPose_ptr) {
  // trajectory pose 16 ~ 19
  TrajectoryPoseDecode(TrajectoryPose_ptr, 16, AcuTrajectoryPose16To19,
                       TrajectoryPose16);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 17, AcuTrajectoryPose16To19,
                       TrajectoryPose17);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 18, AcuTrajectoryPose16To19,
                       TrajectoryPose18);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 19, AcuTrajectoryPose16To19,
                       TrajectoryPose19);
}

void AcuTrajectoryPose20To23Encode(TrajectoryPose* TrajectoryPose_ptr) {
  // trajectory pose 20 ~ 23
  TrajectoryPoseDecode(TrajectoryPose_ptr, 20, AcuTrajectoryPose20To23,
                       TrajectoryPose20);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 21, AcuTrajectoryPose20To23,
                       TrajectoryPose21);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 22, AcuTrajectoryPose20To23,
                       TrajectoryPose22);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 23, AcuTrajectoryPose20To23,
                       TrajectoryPose23);
}

void AcuTrajectoryPose24To27Encode(TrajectoryPose* TrajectoryPose_ptr) {
  // trajectory pose 24 ~ 27
  TrajectoryPoseDecode(TrajectoryPose_ptr, 24, AcuTrajectoryPose24To27,
                       TrajectoryPose24);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 25, AcuTrajectoryPose24To27,
                       TrajectoryPose25);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 26, AcuTrajectoryPose24To27,
                       TrajectoryPose26);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 27, AcuTrajectoryPose24To27,
                       TrajectoryPose27);
}

void AcuTrajectoryPose28To31Encode(TrajectoryPose* TrajectoryPose_ptr) {
  // trajectory pose 28 ~ 31
  TrajectoryPoseDecode(TrajectoryPose_ptr, 28, AcuTrajectoryPose28To31,
                       TrajectoryPose28);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 29, AcuTrajectoryPose28To31,
                       TrajectoryPose29);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 30, AcuTrajectoryPose28To31,
                       TrajectoryPose30);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 31, AcuTrajectoryPose28To31,
                       TrajectoryPose31);
}

void AcuTrajectoryPose32To35Encode(TrajectoryPose* TrajectoryPose_ptr) {
  // trajectory pose 32 ~ 35
  TrajectoryPoseDecode(TrajectoryPose_ptr, 32, AcuTrajectoryPose32To35,
                       TrajectoryPose32);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 33, AcuTrajectoryPose32To35,
                       TrajectoryPose33);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 34, AcuTrajectoryPose32To35,
                       TrajectoryPose34);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 35, AcuTrajectoryPose32To35,
                       TrajectoryPose35);
}

void AcuTrajectoryPose36To39Encode(TrajectoryPose* TrajectoryPose_ptr) {
  // trajectory pose 36 ~ 39
  TrajectoryPoseDecode(TrajectoryPose_ptr, 36, AcuTrajectoryPose36To39,
                       TrajectoryPose36);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 37, AcuTrajectoryPose36To39,
                       TrajectoryPose37);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 38, AcuTrajectoryPose36To39,
                       TrajectoryPose38);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 39, AcuTrajectoryPose36To39,
                       TrajectoryPose39);
}

void AcuTrajectoryPose40To43Encode(TrajectoryPose* TrajectoryPose_ptr) {
  // trajectory pose 40 ~ 43
  TrajectoryPoseDecode(TrajectoryPose_ptr, 40, AcuTrajectoryPose40To43,
                       TrajectoryPose40);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 41, AcuTrajectoryPose40To43,
                       TrajectoryPose41);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 42, AcuTrajectoryPose40To43,
                       TrajectoryPose42);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 43, AcuTrajectoryPose40To43,
                       TrajectoryPose43);
}

void AcuTrajectoryPose44To47Encode(TrajectoryPose* TrajectoryPose_ptr) {
  // trajectory pose 44 ~ 47
  TrajectoryPoseDecode(TrajectoryPose_ptr, 44, AcuTrajectoryPose44To47,
                       TrajectoryPose44);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 45, AcuTrajectoryPose44To47,
                       TrajectoryPose45);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 46, AcuTrajectoryPose44To47,
                       TrajectoryPose46);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 47, AcuTrajectoryPose44To47,
                       TrajectoryPose47);
}

void AcuTrajectoryPose48To51Encode(TrajectoryPose* TrajectoryPose_ptr) {
  // trajectory pose 48 ~ 51
  TrajectoryPoseDecode(TrajectoryPose_ptr, 48, AcuTrajectoryPose48To51,
                       TrajectoryPose48);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 49, AcuTrajectoryPose48To51,
                       TrajectoryPose49);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 50, AcuTrajectoryPose48To51,
                       TrajectoryPose50);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 51, AcuTrajectoryPose48To51,
                       TrajectoryPose51);
}

void AcuTrajectoryPose52To55Encode(TrajectoryPose* TrajectoryPose_ptr) {
  // trajectory pose 52 ~ 55
  TrajectoryPoseDecode(TrajectoryPose_ptr, 52, AcuTrajectoryPose52To55,
                       TrajectoryPose52);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 53, AcuTrajectoryPose52To55,
                       TrajectoryPose53);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 54, AcuTrajectoryPose52To55,
                       TrajectoryPose54);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 55, AcuTrajectoryPose52To55,
                       TrajectoryPose55);
}

void AcuTrajectoryPose56To59Encode(TrajectoryPose* TrajectoryPose_ptr) {
  // trajectory pose 56 ~ 59
  TrajectoryPoseDecode(TrajectoryPose_ptr, 56, AcuTrajectoryPose56To59,
                       TrajectoryPose56);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 57, AcuTrajectoryPose56To59,
                       TrajectoryPose57);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 58, AcuTrajectoryPose56To59,
                       TrajectoryPose58);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 59, AcuTrajectoryPose56To59,
                       TrajectoryPose59);
}

void AcuTrajectoryPose60To63Encode(TrajectoryPose* TrajectoryPose_ptr) {
  // trajectory pose 60 ~ 63
  TrajectoryPoseDecode(TrajectoryPose_ptr, 60, AcuTrajectoryPose60To63,
                       TrajectoryPose60);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 61, AcuTrajectoryPose60To63,
                       TrajectoryPose61);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 62, AcuTrajectoryPose60To63,
                       TrajectoryPose62);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 63, AcuTrajectoryPose60To63,
                       TrajectoryPose63);
}

void AcuTrajectoryPose64To67Encode(TrajectoryPose* TrajectoryPose_ptr) {
  // trajectory pose 64 ~ 67
  TrajectoryPoseDecode(TrajectoryPose_ptr, 64, AcuTrajectoryPose64To67,
                       TrajectoryPose64);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 65, AcuTrajectoryPose64To67,
                       TrajectoryPose65);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 66, AcuTrajectoryPose64To67,
                       TrajectoryPose66);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 67, AcuTrajectoryPose64To67,
                       TrajectoryPose67);
}

void AcuTrajectoryPose68To71Encode(TrajectoryPose* TrajectoryPose_ptr) {
  // trajectory pose 68 ~ 71
  TrajectoryPoseDecode(TrajectoryPose_ptr, 68, AcuTrajectoryPose68To71,
                       TrajectoryPose68);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 69, AcuTrajectoryPose68To71,
                       TrajectoryPose69);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 70, AcuTrajectoryPose68To71,
                       TrajectoryPose70);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 71, AcuTrajectoryPose68To71,
                       TrajectoryPose71);
}
void AcuTrajectoryPose72To75Encode(TrajectoryPose* TrajectoryPose_ptr) {
  // trajectory pose 72 ~ 75
  TrajectoryPoseDecode(TrajectoryPose_ptr, 72, AcuTrajectoryPose72To75,
                       TrajectoryPose72);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 73, AcuTrajectoryPose72To75,
                       TrajectoryPose73);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 74, AcuTrajectoryPose72To75,
                       TrajectoryPose74);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 75, AcuTrajectoryPose72To75,
                       TrajectoryPose75);
}

void AcuTrajectoryPose76To79Encode(TrajectoryPose* TrajectoryPose_ptr) {
  // trajectory pose 76 ~ 79
  TrajectoryPoseDecode(TrajectoryPose_ptr, 76, AcuTrajectoryPose76To79,
                       TrajectoryPose76);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 77, AcuTrajectoryPose76To79,
                       TrajectoryPose77);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 78, AcuTrajectoryPose76To79,
                       TrajectoryPose78);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 79, AcuTrajectoryPose76To79,
                       TrajectoryPose79);
}

void AcuTrajectoryPose80To83Encode(TrajectoryPose* TrajectoryPose_ptr) {
  // trajectory pose 80 ~ 83
  TrajectoryPoseDecode(TrajectoryPose_ptr, 80, AcuTrajectoryPose80To83,
                       TrajectoryPose80);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 81, AcuTrajectoryPose80To83,
                       TrajectoryPose81);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 82, AcuTrajectoryPose80To83,
                       TrajectoryPose82);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 83, AcuTrajectoryPose80To83,
                       TrajectoryPose83);
}

void AcuTrajectoryPose84To87Encode(TrajectoryPose* TrajectoryPose_ptr) {
  // trajectory pose 84 ~ 87
  TrajectoryPoseDecode(TrajectoryPose_ptr, 84, AcuTrajectoryPose84To87,
                       TrajectoryPose84);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 85, AcuTrajectoryPose84To87,
                       TrajectoryPose85);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 86, AcuTrajectoryPose84To87,
                       TrajectoryPose86);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 87, AcuTrajectoryPose84To87,
                       TrajectoryPose87);
}

void AcuTrajectoryPose88To91Encode(TrajectoryPose* TrajectoryPose_ptr) {
  // trajectory pose 88 ~ 91
  TrajectoryPoseDecode(TrajectoryPose_ptr, 88, AcuTrajectoryPose88To91,
                       TrajectoryPose88);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 89, AcuTrajectoryPose88To91,
                       TrajectoryPose89);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 90, AcuTrajectoryPose88To91,
                       TrajectoryPose90);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 91, AcuTrajectoryPose88To91,
                       TrajectoryPose91);
}

void AcuTrajectoryPose92To95Encode(TrajectoryPose* TrajectoryPose_ptr) {
  // trajectory pose 92 ~ 95
  TrajectoryPoseDecode(TrajectoryPose_ptr, 92, AcuTrajectoryPose92To95,
                       TrajectoryPose92);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 93, AcuTrajectoryPose92To95,
                       TrajectoryPose93);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 94, AcuTrajectoryPose92To95,
                       TrajectoryPose94);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 95, AcuTrajectoryPose92To95,
                       TrajectoryPose95);
}

void AcuTrajectoryPose96To99Encode(TrajectoryPose* TrajectoryPose_ptr) {
  // trajectory pose 96 ~ 99
  TrajectoryPoseDecode(TrajectoryPose_ptr, 96, AcuTrajectoryPose96To99,
                       TrajectoryPose96);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 97, AcuTrajectoryPose96To99,
                       TrajectoryPose97);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 98, AcuTrajectoryPose96To99,
                       TrajectoryPose98);
  TrajectoryPoseDecode(TrajectoryPose_ptr, 99, AcuTrajectoryPose96To99,
                       TrajectoryPose99);
}

void TrajectoryPoseArrayDecode(TrajectoryPose* TrajectoryPose_ptr,
                               std::size_t size) {
  // prevent array overflow
  CML_ASSERT(size == 100);

  // message AcuTrajectoryPose00To03
  AcuTrajectoryPose00To03Encode(TrajectoryPose_ptr);

  // message AcuTrajectoryPose04To07
  AcuTrajectoryPose04To07Encode(TrajectoryPose_ptr);

  // message AcuTrajectoryPose08To11
  AcuTrajectoryPose08To11Encode(TrajectoryPose_ptr);

  // message AcuTrajectoryPose12To15
  AcuTrajectoryPose12To15Encode(TrajectoryPose_ptr);

  // message AcuTrajectoryPose16To19
  AcuTrajectoryPose16To19Encode(TrajectoryPose_ptr);

  // message AcuTrajectoryPose20To23
  AcuTrajectoryPose20To23Encode(TrajectoryPose_ptr);

  // message AcuTrajectoryPose24To27
  AcuTrajectoryPose24To27Encode(TrajectoryPose_ptr);

  // message AcuTrajectoryPose28To31
  AcuTrajectoryPose28To31Encode(TrajectoryPose_ptr);

  // message AcuTrajectoryPose32To35
  AcuTrajectoryPose32To35Encode(TrajectoryPose_ptr);

  // message AcuTrajectoryPose36To39
  AcuTrajectoryPose36To39Encode(TrajectoryPose_ptr);

  // message AcuTrajectoryPose40To43
  AcuTrajectoryPose40To43Encode(TrajectoryPose_ptr);

  // message AcuTrajectoryPose44To47
  AcuTrajectoryPose44To47Encode(TrajectoryPose_ptr);

  // message AcuTrajectoryPose48To51
  AcuTrajectoryPose48To51Encode(TrajectoryPose_ptr);

  // message AcuTrajectoryPose52To55
  AcuTrajectoryPose52To55Encode(TrajectoryPose_ptr);

  // message AcuTrajectoryPose56To59
  AcuTrajectoryPose56To59Encode(TrajectoryPose_ptr);

  // message AcuTrajectoryPose60To63
  AcuTrajectoryPose60To63Encode(TrajectoryPose_ptr);

  // message AcuTrajectoryPose64To67
  AcuTrajectoryPose64To67Encode(TrajectoryPose_ptr);

  // message AcuTrajectoryPose68To71
  AcuTrajectoryPose68To71Encode(TrajectoryPose_ptr);

  // message AcuTrajectoryPose72To75
  AcuTrajectoryPose72To75Encode(TrajectoryPose_ptr);

  // message AcuTrajectoryPose76To79
  AcuTrajectoryPose76To79Encode(TrajectoryPose_ptr);

  // message AcuTrajectoryPose80To83
  AcuTrajectoryPose80To83Encode(TrajectoryPose_ptr);

  // message AcuTrajectoryPose84To87
  AcuTrajectoryPose84To87Encode(TrajectoryPose_ptr);

  // message AcuTrajectoryPose88To91
  AcuTrajectoryPose88To91Encode(TrajectoryPose_ptr);

  // message AcuTrajectoryPose92To95
  AcuTrajectoryPose92To95Encode(TrajectoryPose_ptr);

  // message AcuTrajectoryPose96To99
  AcuTrajectoryPose96To99Encode(TrajectoryPose_ptr);
}

void TrajectoryIntentionDecode(Intention* intention) {
  intention->motion =
      Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_TrajectoryInfoMotion();
  intention->lane_change =
      Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_TrajectoryInfoLaneChange();
  intention->horn =
      Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_TrajectoryInfoHorn();
  intention->hazard_light =
      Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_TrajectoryInfoHazardLight();
}

// The timestamp of the trajectory pose was not directly sent because the CAN
// load capacity is limited. Generate based on the initial timestamp and replan
// start index of the trajectory.
// Ref doc: https://cooper.didichuxing.com/docs2/document/2204571940943.
void GenerateTrajectoryPoseTimestamp(uint32_t init_timestamp_ms,
                                     uint16_t replan_start_idx,
                                     TrajectoryPose* TrajectoryPose_ptr,
                                     std::size_t size) {
  init_timestamp_ms = init_timestamp_ms < (replan_start_idx * 100)
                          ? 0
                          : init_timestamp_ms - (replan_start_idx * 100);

  for (size_t i = 0; i < size; ++i) {
    TrajectoryPose_ptr[i].timestamp_ms = init_timestamp_ms;
    init_timestamp_ms = init_timestamp_ms + 100;
  }
}

void TrajectoryDecode(TrajectoryInput* trajectory_ptr) {
  // Timestamp decode, ms.
  uint8_t* timestamp_bytes_ptr =
      Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_TrajectoryInfoInitTimeStamp();

  uint64_t timestamp_ms = 0;
  common::BytesToUint64(timestamp_bytes_ptr, 8, &timestamp_ms, true);
  trajectory_ptr->plan_init_timestamp_ms =
      static_cast<uint32_t>(timestamp_ms % 1000000000L);

  // Trajectory pose decode.
  TrajectoryPoseArrayDecode(
      trajectory_ptr->trajectory_pose,
      sizeof(trajectory_ptr->trajectory_pose) / sizeof(TrajectoryPose));

  // Interntion decode.
  TrajectoryIntentionDecode(&trajectory_ptr->intention);

  // Generate trajectory pose timestamp.
  uint16_t replan_start_idx =
      Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_TrajectoryInfoReserve0();

  GenerateTrajectoryPoseTimestamp(
      trajectory_ptr->plan_init_timestamp_ms, replan_start_idx,
      trajectory_ptr->trajectory_pose,
      sizeof(trajectory_ptr->trajectory_pose) / sizeof(TrajectoryPose));
}

void AcuControlDecode(AcuControl* acu_control_ptr) {
  // timestamp, ms.
  uint8_t* timestamp_bytes_ptr =
      Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_ControlTimestamp();

  uint64_t timestamp_ms = 0;
  common::BytesToUint64(timestamp_bytes_ptr, 8, &timestamp_ms, true);
  acu_control_ptr->timestamp_ms =
      static_cast<uint32_t>(timestamp_ms % 1000000000L);

  // wheel angle offset, rad.
  acu_control_ptr->calibrated_vehicle_params.wheel_angle_offset =
      Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_ControlWheelAngleOffset() *
      1e-6F;
}

}  // namespace acu_signal_input
}  // namespace fallback_adas
