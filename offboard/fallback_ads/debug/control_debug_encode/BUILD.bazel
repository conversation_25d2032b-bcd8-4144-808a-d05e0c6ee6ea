package(default_visibility = ["//visibility:public"])

cc_library(
    name = "control_debug_encode",
    srcs = [
        "control_debug_encode.cpp",
    ],
    hdrs = [
        "control_debug_encode.h",
    ],
    copts = [
        "-x",
        "c++",
        "-std=c++14",
    ],
    include_prefix = "debug/control_debug_encode/",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_debug",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_debug_type",
        "//offboard/fallback_ads/common_math_library:can_util",
        "//offboard/fallback_ads/common_math_library:common_method",
    ],
)
