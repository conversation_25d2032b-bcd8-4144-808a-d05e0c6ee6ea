#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_route_association/predicted_trajectory_route_association.h"

#include <gtest/gtest.h>

#include "hdmap/lib/test/test_util.h"
#include "math/math_util.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_wrapper.h"
#include "planner/speed/reasoning/test/reasoning_test_fixture.h"
#include "planner/world_model/traffic_participant/predicted_trajectory.h"
#include "planner_protos/agent_map_element_occupancy_seed.pb.h"

namespace planner {

namespace route_association {

class PredictedTrajectoryRouteAssociationTest
    : public ::testing::Test,
      public speed::ReasoningTestFixture {
 public:
  PredictedTrajectoryRouteAssociationTest() : ReasoningTestFixture() {
    SetUpSceneMap(hdmap::test_util::SceneType::kJunction);
  }
};

TEST_F(PredictedTrajectoryRouteAssociationTest, DummyTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kJunction);
  SetEgoPose(/*lane_id=*/9713, /*portion=*/0.0,
             /*speed=*/5.0);
  CreatePathWithLaneSequence({9713, 57543},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/10539, /*portion=*/0.4,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/2.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/10539,
                                 /*start_portion=*/0.1,
                                 /*end_lane_id=*/10539, /*end_portion=*/0.5,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);
  Update();
  EXPECT_EQ(
      planning_debug().behavior_reasoner_debug().decoupled_maneuvers().size(),
      1UL);

  pnc_map::JointPncMapService joint_pnc_map_service(
      map_test_util().pnc_map_service());
  const PredictedTrajectoryRouteAssociator association_result{
      object_prediction_map().at(object_id)[0], joint_pnc_map_service,
      pb::AgentMapElementOccupancySeeds{}, nullptr};
  EXPECT_EQ(association_result.object_id(), 1);
  EXPECT_EQ(association_result.trajectory_id(), 1);
}

TEST_F(PredictedTrajectoryRouteAssociationTest, LeftTurnTest) {
  DCHECK(!FLAGS_planning_enable_agent_predicted_trajectory_route_association);
  FLAGS_planning_enable_agent_predicted_trajectory_route_association = true;
  SetUpSceneMap(hdmap::test_util::SceneType::kJunction);
  SetEgoPose(/*lane_id=*/9911, /*portion=*/0.0,
             /*speed=*/5.0);
  CreatePathWithLaneSequence({9911, 9111},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/10539, /*portion=*/0.4,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/2.0);
  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/10539, /*portion=*/0.4,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/10539, /*portion=*/0.6,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/3.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/10539, /*portion=*/0.8,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/3.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/9141, /*portion=*/0.01,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/9141, /*portion=*/0.05,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};

  AddMultiPointPredictedTrajectory(waypoints, /*likelihood=*/0.9, /*traj_id=*/1,
                                   agent);
  Update();
  pnc_map::JointPncMapService joint_pnc_map_service(
      map_test_util().pnc_map_service());
  const PredictedTrajectoryRouteAssociator association_result{
      object_prediction_map().at(object_id)[0], joint_pnc_map_service,
      pb::AgentMapElementOccupancySeeds{}, nullptr};

  const std::vector<MapElementAndPoseInfo> ground_truth_route{
      MapElementAndPoseInfo{pb::MapElementType::LANE, 10539, true},
      MapElementAndPoseInfo{pb::MapElementType::LANE, 9141, true},
  };

  EXPECT_EQ(association_result.most_likely_route().size(), 2UL);
  for (size_t idx = 0UL; idx < association_result.most_likely_route().size();
       ++idx) {
    EXPECT_EQ(association_result.most_likely_route().at(idx).type,
              ground_truth_route.at(idx).type);
    EXPECT_EQ(association_result.most_likely_route().at(idx).element_id,
              ground_truth_route.at(idx).element_id);
  }
  ASSERT_FALSE(reasoning_inputs().empty());
  ASSERT_EQ(reasoning_inputs().front().agent_trajectory_infos.size(), 1);
  [[maybe_unused]] const speed::AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos.front();
  FLAGS_planning_enable_agent_predicted_trajectory_route_association = false;
}

TEST_F(PredictedTrajectoryRouteAssociationTest, SideMergeTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kMerge);
  SetEgoPose(/*lane_id=*/128377, /*portion=*/0.0,
             /*speed=*/5.0);
  CreatePathWithLaneSequence({128377, 128433},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/128379, /*portion=*/0.4,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/2.0);
  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/128379, /*portion=*/0.4,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/128379, /*portion=*/0.9,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/145496, /*portion=*/0.1,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/145496, /*portion=*/0.6,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};

  AddMultiPointPredictedTrajectory(waypoints, /*likelihood=*/0.9, /*traj_id=*/1,
                                   agent);
  Update();
  pnc_map::JointPncMapService joint_pnc_map_service(
      map_test_util().pnc_map_service());
  const PredictedTrajectoryRouteAssociator association_result{
      object_prediction_map().at(object_id)[0], joint_pnc_map_service,
      pb::AgentMapElementOccupancySeeds{}, nullptr};

  const std::vector<MapElementAndPoseInfo> ground_truth_route{
      MapElementAndPoseInfo{pb::MapElementType::LANE, 128379, true},
      MapElementAndPoseInfo{pb::MapElementType::LANE, 145496, true},
  };

  EXPECT_EQ(association_result.most_likely_route().size(), 2UL);
  for (size_t idx = 0UL; idx < association_result.most_likely_route().size();
       ++idx) {
    EXPECT_EQ(association_result.most_likely_route().at(idx).type,
              ground_truth_route.at(idx).type);
    EXPECT_EQ(association_result.most_likely_route().at(idx).element_id,
              ground_truth_route.at(idx).element_id);
  }
}

TEST_F(PredictedTrajectoryRouteAssociationTest, ExitingRoundaboutTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kRoundabout);
  SetEgoPose(/*lane_id=*/152724, /*portion=*/0.0,
             /*speed=*/5.0);
  CreatePathWithLaneSequence({152724, 152787},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/152727, /*portion=*/0.8,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/2.0);
  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/152727, /*portion=*/0.8,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/166411, /*portion=*/0.4,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/168634, /*portion=*/0.1,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};

  AddMultiPointPredictedTrajectory(waypoints, /*likelihood=*/0.9, /*traj_id=*/1,
                                   agent);
  Update();
  pnc_map::JointPncMapService joint_pnc_map_service(
      map_test_util().pnc_map_service());
  const PredictedTrajectoryRouteAssociator association_result{
      object_prediction_map().at(object_id)[0], joint_pnc_map_service,
      pb::AgentMapElementOccupancySeeds{}, nullptr};

  const std::vector<MapElementAndPoseInfo> ground_truth_route{
      MapElementAndPoseInfo{pb::MapElementType::LANE, 152727, true},
      MapElementAndPoseInfo{pb::MapElementType::LANE, 166411, true},
      MapElementAndPoseInfo{pb::MapElementType::LANE, 168634, true},
  };

  EXPECT_EQ(association_result.most_likely_route().size(), 3UL);
  for (size_t idx = 0UL; idx < association_result.most_likely_route().size();
       ++idx) {
    EXPECT_EQ(association_result.most_likely_route().at(idx).type,
              ground_truth_route.at(idx).type);
    EXPECT_EQ(association_result.most_likely_route().at(idx).element_id,
              ground_truth_route.at(idx).element_id);
  }
}

TEST_F(PredictedTrajectoryRouteAssociationTest,
       StraightLaneChangeInJunctionTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kForkMergeByBikelane);
  SetEgoPose(/*lane_id=*/163735, /*portion=*/0.9,
             /*speed=*/5.0);
  CreatePathWithLaneSequence({163735, 164363},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/164364, /*portion=*/0.1,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/2.0);
  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/164364, /*portion=*/0.1,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/164364, /*portion=*/0.4,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/164363, /*portion=*/0.7,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/163579, /*portion=*/0.2,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};

  AddMultiPointPredictedTrajectory(waypoints, /*likelihood=*/0.9, /*traj_id=*/1,
                                   agent);
  Update();
  pnc_map::JointPncMapService joint_pnc_map_service(
      map_test_util().pnc_map_service());
  const PredictedTrajectoryRouteAssociator association_result{
      object_prediction_map().at(object_id)[0], joint_pnc_map_service,
      pb::AgentMapElementOccupancySeeds{}, nullptr};

  const std::vector<MapElementAndPoseInfo> ground_truth_route{
      MapElementAndPoseInfo{pb::MapElementType::LANE, 164364, true},
      MapElementAndPoseInfo{pb::MapElementType::LANE, 164363, true},
      MapElementAndPoseInfo{pb::MapElementType::LANE, 163579, true},
  };

  EXPECT_EQ(association_result.most_likely_route().size(), 3UL);
  for (size_t idx = 0UL; idx < association_result.most_likely_route().size();
       ++idx) {
    EXPECT_EQ(association_result.most_likely_route().at(idx).type,
              ground_truth_route.at(idx).type);
    EXPECT_EQ(association_result.most_likely_route().at(idx).element_id,
              ground_truth_route.at(idx).element_id);
  }
}

TEST_F(PredictedTrajectoryRouteAssociationTest, ContentedGapFillingTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kForkMergeByBikelane);
  SetEgoPose(/*lane_id=*/163578, /*portion=*/0.0,
             /*speed=*/5.0);
  CreatePathWithLaneSequence({163578, 163799},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/163602, /*portion=*/0.1,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/2.0);
  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/163602, /*portion=*/0.1,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/163602, /*portion=*/0.9,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/163806, /*portion=*/0.5,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/163799, /*portion=*/0.1,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};
  AddMultiPointPredictedTrajectory(waypoints, /*likelihood=*/0.9, /*traj_id=*/1,
                                   agent);
  Update();
  pnc_map::JointPncMapService joint_pnc_map_service(
      map_test_util().pnc_map_service());
  const PredictedTrajectoryRouteAssociator association_result{
      object_prediction_map().at(object_id)[0], joint_pnc_map_service,
      pb::AgentMapElementOccupancySeeds{}, nullptr};

  const std::vector<MapElementAndPoseInfo> ground_truth_route{
      MapElementAndPoseInfo{pb::MapElementType::LANE, 163602, true},
      MapElementAndPoseInfo{pb::MapElementType::LANE, 163806, true},
      MapElementAndPoseInfo{pb::MapElementType::LANE, 163799, true}};

  EXPECT_EQ(association_result.most_likely_route().size(), 3UL);
  for (size_t idx = 0UL; idx < association_result.most_likely_route().size();
       ++idx) {
    EXPECT_EQ(association_result.most_likely_route().at(idx).type,
              ground_truth_route.at(idx).type);
    EXPECT_EQ(association_result.most_likely_route().at(idx).element_id,
              ground_truth_route.at(idx).element_id);
  }
  // The lane 163806 is inferred by gap filling between contented elements.
  EXPECT_TRUE(
      association_result.most_likely_route().at(1UL).is_inferred_element);
}

TEST_F(PredictedTrajectoryRouteAssociationTest, NeighborhoodGapFillingTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kForkMergeByBikelane);
  SetEgoPose(/*lane_id=*/163704, /*portion=*/0.0,
             /*speed=*/5.0);
  CreatePathWithLaneSequence({163704, 163676},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/163697, /*portion=*/0.1,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/2.0);
  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/163697, /*portion=*/0.1,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/163697, /*portion=*/0.9,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/163706, /*portion=*/0.1,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/163704, /*portion=*/0.15,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};

  AddMultiPointPredictedTrajectory(waypoints, /*likelihood=*/0.9, /*traj_id=*/1,
                                   agent);
  Update();
  pnc_map::JointPncMapService joint_pnc_map_service(
      map_test_util().pnc_map_service());
  const PredictedTrajectoryRouteAssociator association_result{
      object_prediction_map().at(object_id)[0], joint_pnc_map_service,
      pb::AgentMapElementOccupancySeeds{}, nullptr};

  const std::vector<MapElementAndPoseInfo> ground_truth_route{
      MapElementAndPoseInfo{pb::MapElementType::LANE, 163697, true},
      MapElementAndPoseInfo{pb::MapElementType::LANE, 163706, true},
      MapElementAndPoseInfo{pb::MapElementType::LANE, 163705, true},
      MapElementAndPoseInfo{pb::MapElementType::LANE, 163704, true},
  };

  EXPECT_EQ(association_result.most_likely_route().size(), 4UL);
  for (size_t idx = 0UL; idx < association_result.most_likely_route().size();
       ++idx) {
    EXPECT_EQ(association_result.most_likely_route().at(idx).type,
              ground_truth_route.at(idx).type);
    EXPECT_EQ(association_result.most_likely_route().at(idx).element_id,
              ground_truth_route.at(idx).element_id);
  }
  // The lane 163705 is inferred by gap filling between neighboring elements.
  EXPECT_TRUE(
      association_result.most_likely_route().at(2UL).is_inferred_element);
}

TEST_F(PredictedTrajectoryRouteAssociationTest,
       SuperNeighborhoodGapFillingTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kForkMergeByBikelane);
  SetEgoPose(/*lane_id=*/163704, /*portion=*/0.0,
             /*speed=*/5.0);
  CreatePathWithLaneSequence({163704, 163676},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/163586, /*portion=*/0.1,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/2.0);
  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/163586, /*portion=*/0.1,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/163586, /*portion=*/0.5,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/163583, /*portion=*/0.6,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/163588, /*portion=*/0.0,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/163588, /*portion=*/0.25,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/163588, /*portion=*/0.5,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};

  AddMultiPointPredictedTrajectory(waypoints, /*likelihood=*/0.9, /*traj_id=*/1,
                                   agent);
  Update();
  pnc_map::JointPncMapService joint_pnc_map_service(
      map_test_util().pnc_map_service());
  const PredictedTrajectoryRouteAssociator association_result{
      object_prediction_map().at(object_id)[0], joint_pnc_map_service,
      pb::AgentMapElementOccupancySeeds{}, nullptr};

  const std::vector<MapElementAndPoseInfo> ground_truth_route{
      MapElementAndPoseInfo{pb::MapElementType::LANE, 163586, true},
      MapElementAndPoseInfo{pb::MapElementType::LANE, 163585, true},
      MapElementAndPoseInfo{pb::MapElementType::LANE, 163584, true},
      MapElementAndPoseInfo{pb::MapElementType::LANE, 163583, true},
      MapElementAndPoseInfo{pb::MapElementType::LANE, 163588, true},
  };

  EXPECT_EQ(association_result.most_likely_route().size(), 5UL);
  for (size_t idx = 0UL; idx < association_result.most_likely_route().size();
       ++idx) {
    EXPECT_EQ(association_result.most_likely_route().at(idx).type,
              ground_truth_route.at(idx).type);
    EXPECT_EQ(association_result.most_likely_route().at(idx).element_id,
              ground_truth_route.at(idx).element_id);
  }
  // The lanes 163585, 163584 and 163583 are inferred by gap filling between
  // super-neighboring elements.
  EXPECT_TRUE(
      association_result.most_likely_route().at(1UL).is_inferred_element);
  EXPECT_TRUE(
      association_result.most_likely_route().at(2UL).is_inferred_element);
  EXPECT_TRUE(
      association_result.most_likely_route().at(3UL).is_inferred_element);
}

TEST_F(PredictedTrajectoryRouteAssociationTest, HeadingDiffTieBrakingTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kRightStraightMerge);
  SetEgoPose(/*lane_id=*/9713, /*portion=*/0.1,
             /*speed=*/5.0);
  CreatePathWithLaneSequence({9713, 57543},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/13345, /*portion=*/0.8,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/2.0);
  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/13345, /*portion=*/0.8,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/13345, /*portion=*/0.9,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/57541, /*portion=*/0.1,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};

  AddMultiPointPredictedTrajectory(waypoints, /*likelihood=*/0.9, /*traj_id=*/1,
                                   agent);
  Update();
  pnc_map::JointPncMapService joint_pnc_map_service(
      map_test_util().pnc_map_service());
  const PredictedTrajectoryRouteAssociator association_result{
      object_prediction_map().at(object_id)[0], joint_pnc_map_service,
      pb::AgentMapElementOccupancySeeds{}, nullptr};

  const std::vector<MapElementAndPoseInfo> ground_truth_route{
      MapElementAndPoseInfo{pb::MapElementType::LANE, 13345, true},
      MapElementAndPoseInfo{pb::MapElementType::LANE, 57541, true},
  };

  EXPECT_EQ(association_result.most_likely_route().size(), 2UL);
  for (size_t idx = 0UL; idx < association_result.most_likely_route().size();
       ++idx) {
    EXPECT_EQ(association_result.most_likely_route().at(idx).type,
              ground_truth_route.at(idx).type);
    EXPECT_EQ(association_result.most_likely_route().at(idx).element_id,
              ground_truth_route.at(idx).element_id);
  }
}

TEST_F(PredictedTrajectoryRouteAssociationTest, LatestIsolatedLaneSeedTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kRightStraightMerge);
  SetEgoPose(/*lane_id=*/9713, /*portion=*/0.1,
             /*speed=*/5.0);
  CreatePathWithLaneSequence({9713, 57543},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  const double agent_in_lane_portion = 0.9;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/13345, /*portion=*/agent_in_lane_portion,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/2.0);

  const pnc_map::Lane* straight_lane = GetLaneFromId(9711);
  const double heading_on_straight_lane =
      straight_lane->center_line().GetInterpTheta(
          agent_in_lane_portion *
          straight_lane->center_line().GetTotalArcLength());
  const pnc_map::Lane* right_turn_lane = GetLaneFromId(13345);
  const double heading_on_right_turn_lane =
      right_turn_lane->center_line().GetInterpTheta(
          agent_in_lane_portion *
          right_turn_lane->center_line().GetTotalArcLength());

  const double theta_shift =
      math::AngleDiff(heading_on_straight_lane, heading_on_right_turn_lane);

  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/13345, agent_in_lane_portion,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0, theta_shift),
      LanePointToPoseWithShift(/*lane_id=*/57541, /*portion=*/0.1,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};

  AddMultiPointPredictedTrajectory(waypoints, /*likelihood=*/0.9, /*traj_id=*/1,
                                   agent);

  Update();

  pb::AgentOccupiedMapElementInfos info;
  info.set_timestamp(0);
  info.mutable_latest_occupied_isolated_lane()->set_element_type(
      pb::MapElementType::LANE);
  info.mutable_latest_occupied_isolated_lane()->set_element_id(32465);
  pb::AgentMapElementOccupancySeeds seed;
  seed.mutable_agent_element_occupancy_map()->insert({object_id, info});

  pnc_map::JointPncMapService joint_pnc_map_service(
      map_test_util().pnc_map_service());
  const PredictedTrajectoryRouteAssociator association_result{
      object_prediction_map().at(object_id)[0], joint_pnc_map_service, seed,
      nullptr};

  const std::vector<MapElementAndPoseInfo> ground_truth_route{
      MapElementAndPoseInfo{pb::MapElementType::LANE, 13345, true},
      MapElementAndPoseInfo{pb::MapElementType::LANE, 57541, true},
  };

  EXPECT_EQ(association_result.most_likely_route().size(), 2UL);
  for (size_t idx = 0UL; idx < association_result.most_likely_route().size();
       ++idx) {
    EXPECT_EQ(association_result.most_likely_route().at(idx).type,
              ground_truth_route.at(idx).type);
    EXPECT_EQ(association_result.most_likely_route().at(idx).element_id,
              ground_truth_route.at(idx).element_id);
  }
}

TEST_F(PredictedTrajectoryRouteAssociationTest, SoftNeighborhoodTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kCrosswalk);
  SetEgoPose(/*lane_id=*/11315, /*portion=*/0.9,
             /*speed=*/5.0);
  CreatePathWithLaneSequence({11315, 13317},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/32465, /*portion=*/0.99,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/2.0);

  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/32465, /*portion=*/0.99,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0, /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/14051, /*portion=*/0.1,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/14045, /*portion=*/0.3,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};

  AddMultiPointPredictedTrajectory(waypoints, /*likelihood=*/0.9, /*traj_id=*/1,
                                   agent);

  Update();
  pnc_map::JointPncMapService joint_pnc_map_service(
      map_test_util().pnc_map_service());
  const PredictedTrajectoryRouteAssociator association_result{
      object_prediction_map().at(object_id)[0], joint_pnc_map_service,
      pb::AgentMapElementOccupancySeeds{}, nullptr};

  const std::vector<MapElementAndPoseInfo> ground_truth_route{
      MapElementAndPoseInfo{pb::MapElementType::LANE, 32465, true},
      MapElementAndPoseInfo{pb::MapElementType::LANE, 14051, true},
      MapElementAndPoseInfo{pb::MapElementType::LANE, 14045, true},
  };

  EXPECT_EQ(association_result.most_likely_route().size(), 3UL);
  for (size_t idx = 0UL; idx < association_result.most_likely_route().size();
       ++idx) {
    EXPECT_EQ(association_result.most_likely_route().at(idx).type,
              ground_truth_route.at(idx).type);
    EXPECT_EQ(association_result.most_likely_route().at(idx).element_id,
              ground_truth_route.at(idx).element_id);
  }
}

TEST_F(PredictedTrajectoryRouteAssociationTest, LeavingExitZoneTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kMerge);
  SetEgoPose(/*lane_id=*/145631, /*portion=*/0.9,
             /*speed=*/5.0);
  CreatePathWithLaneSequence({145631, 128527},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/128527, /*portion=*/0.7,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/2.0);

  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/128527, /*portion=*/0.7,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/-5.0, /*theta_shift=*/M_PI_4),
      LanePointToPoseWithShift(/*lane_id=*/128527, /*portion=*/0.7,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0, /*theta_shift=*/M_PI_4),
      LanePointToPoseWithShift(/*lane_id=*/128527, /*portion=*/0.9,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/128545, /*portion=*/0.1,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};

  AddMultiPointPredictedTrajectory(waypoints, /*likelihood=*/0.9, /*traj_id=*/1,
                                   agent);

  Update();
  pnc_map::JointPncMapService joint_pnc_map_service(
      map_test_util().pnc_map_service());
  const PredictedTrajectoryRouteAssociator association_result{
      object_prediction_map().at(object_id)[0], joint_pnc_map_service,
      pb::AgentMapElementOccupancySeeds{}, nullptr};

  const std::vector<MapElementAndPoseInfo> ground_truth_route{
      MapElementAndPoseInfo{pb::MapElementType::ZONE, 46655, true},
      MapElementAndPoseInfo{pb::MapElementType::LANE, 128527, true},
      MapElementAndPoseInfo{pb::MapElementType::LANE, 128545, true},
  };

  EXPECT_EQ(association_result.most_likely_route().size(), 3UL);
  for (size_t idx = 0UL; idx < association_result.most_likely_route().size();
       ++idx) {
    EXPECT_EQ(association_result.most_likely_route().at(idx).type,
              ground_truth_route.at(idx).type);
    EXPECT_EQ(association_result.most_likely_route().at(idx).element_id,
              ground_truth_route.at(idx).element_id);
  }
}

TEST_F(PredictedTrajectoryRouteAssociationTest, JustLeftExitZoneTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kMerge);
  SetEgoPose(/*lane_id=*/145631, /*portion=*/0.9,
             /*speed=*/5.0);
  CreatePathWithLaneSequence({145631, 128527},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/128527, /*portion=*/0.7,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/2.0);

  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/128527, /*portion=*/0.7,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0, /*theta_shift=*/M_PI_4),
      LanePointToPoseWithShift(/*lane_id=*/128527, /*portion=*/0.9,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/128545, /*portion=*/0.1,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};

  AddMultiPointPredictedTrajectory(waypoints, /*likelihood=*/0.9, /*traj_id=*/1,
                                   agent);

  Update();

  pb::AgentOccupiedMapElementInfos info;
  info.set_timestamp(0);
  info.mutable_latest_occupied_isolated_zone()->set_element_type(
      pb::MapElementType::ZONE);
  info.mutable_latest_occupied_isolated_zone()->set_element_id(46655);
  pb::AgentMapElementOccupancySeeds seed;
  seed.mutable_agent_element_occupancy_map()->insert({object_id, info});

  pnc_map::JointPncMapService joint_pnc_map_service(
      map_test_util().pnc_map_service());
  const PredictedTrajectoryRouteAssociator association_result{
      object_prediction_map().at(object_id)[0], joint_pnc_map_service, seed,
      nullptr};

  const std::vector<MapElementAndPoseInfo> ground_truth_route{
      MapElementAndPoseInfo{pb::MapElementType::LANE, 128527, true},
      MapElementAndPoseInfo{pb::MapElementType::LANE, 128545, true},
  };

  EXPECT_EQ(association_result.most_likely_route().size(), 2UL);
  for (size_t idx = 0UL; idx < association_result.most_likely_route().size();
       ++idx) {
    EXPECT_EQ(association_result.most_likely_route().at(idx).type,
              ground_truth_route.at(idx).type);
    EXPECT_EQ(association_result.most_likely_route().at(idx).element_id,
              ground_truth_route.at(idx).element_id);
  }

  EXPECT_TRUE(
      association_result.latest_exit_zone_info_in_seed_opt().has_value());
  EXPECT_EQ(association_result.latest_exit_zone_info_in_seed_opt()->element_id,
            46655);
}

}  // namespace route_association
}  // namespace planner
