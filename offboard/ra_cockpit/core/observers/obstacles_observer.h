#ifndef OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_OBSTACLES_OBSERVER_H_
#define OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_OBSTACLES_OBSERVER_H_

#include <functional>
#include <memory>

#include "../data_observer.h"
#include "teleassist_protos/teleassist.pb.h"
#include "voy_protos/obstacle.pb.h"

namespace remote_assist {

class ObstaclesObserver : public IDataObserver {
 public:
  using ObstaclesCallback =
      std::function<void(std::shared_ptr<voy::Obstacles>, DataChannel)>;

  explicit ObstaclesObserver(ObstaclesCallback callback);

  bool IsInterestedIn(
      const teleassist::pb::TeleassistRequest& message) override;

  std::shared_ptr<google::protobuf::Message> OnMessage(
      const teleassist::pb::TeleassistRequest& message,
      DataChannel data_channel) override;

 private:
  ObstaclesCallback callback_;
};

}  // namespace remote_assist

#endif  // OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_OBSTACLES_OBSERVER_H_
