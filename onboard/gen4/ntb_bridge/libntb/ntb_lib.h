#ifndef _NTB_LIB_H
#define _NTB_LIB_H

#include <linux/types.h>

typedef uint64_t u64;
typedef int64_t s64;
typedef uint32_t u32;
typedef int32_t s32;
typedef uint16_t u16;
typedef int16_t s16;
typedef uint8_t u8;
typedef int8_t s8;

enum ntb_host_id { NTB_ORIN1, NTB_ORIN2, NTB_X86, NTB_NR };

enum {
  NTBE_ERR = -1,        /* fatal error */
  NTBE_AGAIN = -EAGAIN, /* try again */
};

struct ntb_msg {
  char *buf;
  s64 index;
  /* max length of buf */
  u32 size;
  /* buf length, which is set by user */
  u32 len;
};

/**
 * Open ntb device.
 *
 * @dst_id: peer ntb id.
 * @mode:   operation mode, could be any one of the following mode:
 *		O_RDONLY for recv
 *		O_WRONLY for send
 *		O_RDWR for recv and send
 *
 * Returns: the device id or negative value indicating an error.
 */
int ntb_open(enum ntb_host_id dst_id, mode_t mode);

/**
 * get a msg buffer for send.
 *
 * @dev_id: ntb device id.
 *
 * User should fill msg->buf with tx data, and set data length to msg->len,
 * which can not exceed msg->size.
 *
 * Returns: A pointer on msg or NULL on failure.
 */
struct ntb_msg *ntb_tx_mem_alloc(int dev_id);

/**
 * Send ntb msg.
 *
 * @dev_id: 	ntb device id.
 * @msg: 	pointer on msg to be sent.
 *
 * Returns: 0 on success, otherwise a negative error number.
 */
int ntb_send(int dev_id, struct ntb_msg *const msg);

/**
 * Receive a msg, the caller will wait here if no data available.
 *
 * @dev_id: 	ntb device id.
 *
 * User can fetch rx data from msg->buf, msg->len means rx data length.
 *
 * Returns: a pointer on msg or NULL on failure.
 */
struct ntb_msg *ntb_recv(int dev_id);

/**
 * Free ntb recv msg.
 *
 * @dev_id: 	ntb device id.
 * @msg:	pointer on msg to be freed.
 *
 * Return: 0 on success, otherwise a negative error number.
 */
int ntb_rx_mem_free(int dev_id, struct ntb_msg *const msg);

/**
 * Close ntb device.
 *
 * @dev_id: 	ntb device id.
 */
void ntb_close(int dev_id);

#endif