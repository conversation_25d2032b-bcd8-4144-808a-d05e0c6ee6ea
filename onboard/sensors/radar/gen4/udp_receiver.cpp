
#include <algorithm>
#include <iostream>
#include <memory>
#include <string>
#include <utility>
#include <vector>

#include <boost/asio.hpp>
#include <boost/asio/ip/multicast.hpp>
#include <glog/logging.h>

#include "abnormal_checker.h"
#include "node/timer.h"
#include "udp_receiver.h"

namespace radar {

namespace {
using varch::vnode::message_util::CreatePublisher;

constexpr int kMaxLen = 2048;  // maximum size of packet to be received
constexpr int kReceiveTimeoutMs = 300;
constexpr int kHardWarePubPeriodS = 10;
constexpr int kMaxLogPktCount = 10;

std::shared_ptr<udp::socket> CreateSocket(
    boost::asio::io_service* io_service,
    const boost::asio::ip::address_v4& local_ip, const int local_port) {
  boost::system::error_code ec;
  std::shared_ptr<udp::socket> socket =
      std::make_shared<udp::socket>(*io_service);
  socket->open(udp::v4(), ec);
  socket->set_option(udp::socket::reuse_address(true), ec);
  socket->bind(udp::endpoint(local_ip, local_port), ec);
  if (ec) {
    std::string err_msg = "Failed binding socket to [" + local_ip.to_string() +
                          ":" + std::to_string(local_port) +
                          "] ERROR: " + ec.message();
    CHECK(false) << err_msg;
  }
  return socket;
}

void SetupMulticast(std::shared_ptr<udp::socket> socket,
                    const boost::asio::ip::address_v4& multicast_ip,
                    const boost::asio::ip::address_v4& local_ip) {
  boost::system::error_code ec;
  socket->set_option(
      boost::asio::ip::multicast::join_group(multicast_ip, local_ip), ec);
  socket->set_option(boost::asio::ip::multicast::outbound_interface(local_ip),
                     ec);
  if (ec) {
    LOG(ERROR) << "Failed to set multicast options: " << ec.message();
  }
}

}  // namespace

UdpReceiver::UdpReceiver(boost::asio::io_service* io_service)
    : io_service_(io_service) {}

// Create socket & Bind socket to endpoint
void UdpReceiver::InitUnicast1vN() {
  socket_ = CreateSocket(io_service_,
                         boost::asio::ip::address_v4::from_string(kLocalIp),
                         FLAGS_local_port);
  socket_debug_ = CreateSocket(
      io_service_, boost::asio::ip::address_v4::from_string(kLocalIp),
      FLAGS_local_port_log);
}

void UdpReceiver::InitMulticast1v1(const std::string& facing) {
  std::string radar_ip = GetRadarConfig<RadarConfigItem::IP_ADDRESS>(facing);
  std::string multicast_ip =
      GetRadarConfig<RadarConfigItem::MULTICAST_ADDRESS>(facing);
  auto local_ip = boost::asio::ip::address_v4::from_string(kLocalIp);
  auto multicast_ip_addr =
      boost::asio::ip::address_v4::from_string(multicast_ip);

  // Create socket.
  socket_ = CreateSocket(io_service_, multicast_ip_addr, FLAGS_local_port);
  SetupMulticast(socket_, multicast_ip_addr, local_ip);
  socket_debug_ =
      CreateSocket(io_service_, multicast_ip_addr, FLAGS_local_port_log);
  SetupMulticast(socket_debug_, multicast_ip_addr, local_ip);

  // Setup publishers for raw and log packets.
  radar_log_pkt_pub_ =
      CreatePublisher<voy::Capture>(av_comm::topic::kRadarLogPacket);

  // Setup publisher for hardware monitor data.
  radar_hardware_monitor_pub_ = CreatePublisher<monitor::HardwareMonitorData>(
      av_comm::topic::kHardwareMonitor);

  // Setup and initialize capture proto.
  radar_log_pkt_ = InitializeCapture(radar_ip, FLAGS_radar_port_log,
                                     multicast_ip, FLAGS_local_port_log);
}

void UdpReceiver::ReceiveSpawn() {
  LOG(INFO) << "Start receiving data packets";
  auto timer = std::make_unique<node::Timer>(*io_service_);
  boost::asio::spawn(*io_service_, [this, timer = std::move(timer)](
                                       boost::asio::yield_context yield) {
    boost::system::error_code ec;
    while (true) {
      // Add timeout to figure out the packets lost issue.
      timer->expires_from_now(std::chrono::milliseconds(kReceiveTimeoutMs), ec);
      timer->async_wait([this](const boost::system::error_code& ec) {
        if (ec != boost::asio::error::operation_aborted) ReportAllTimeouts();
      });
      udp::endpoint sender_endpoint;
      auto pkt_buf_ptr = std::make_shared<std::vector<uint8_t>>(kMaxLen);
      size_t bytes_recvd = socket_->async_receive_from(
          boost::asio::buffer(*pkt_buf_ptr), sender_endpoint, yield[ec]);
      if (ec) {
        LOG(ERROR) << "Failed in Receive packet. Error: " << ec.message();
        break;
      }
      if (bytes_recvd <= 0) continue;
      auto it = parser_map_.find(sender_endpoint);
      if (it != parser_map_.end()) {
        it->second->ReportTimeout(false);
        pkt_buf_ptr->resize(bytes_recvd);
        it->second->EnqueueReceive(std::move(pkt_buf_ptr));
      }
    }
  });

  for (auto& [_, parser] : parser_map_) {
    parser->ReceiveProcessSpawn(io_service_);
  }

  boost::asio::spawn(*io_service_, [this](boost::asio::yield_context yield) {
    auto timer = std::make_unique<node::Timer>(*io_service_);
    boost::system::error_code ec;
    while (!ec) {
      timer->expires_from_now(std::chrono::seconds(kHardWarePubPeriodS), ec);
      for (auto& [_, parser] : parser_map_) {
        auto hardware_monitor_data = parser->GetHardwareMonitorData();
        radar_hardware_monitor_pub_.Publish(hardware_monitor_data);
      }
      timer->async_wait(yield[ec]);
    }
  });
}

void UdpReceiver::ReceiveLogSpawn() {
  LOG(INFO) << "Start receiving log packets";
  boost::asio::spawn(*io_service_, [this](boost::asio::yield_context yield) {
    while (true) {
      boost::system::error_code ec;
      udp::endpoint sender_endpoint;
      auto pkt_buf_ptr = std::make_shared<std::vector<uint8_t>>(kMaxLen);
      size_t bytes_recvd = socket_debug_->async_receive_from(
          boost::asio::buffer(*pkt_buf_ptr), sender_endpoint, yield[ec]);
      int64_t pkt_time_ns = base::NowNs();
      if (ec) {
        LOG(ERROR) << "Failed in Receive log. Error: " << ec.message();
        break;
      }
      if (bytes_recvd <= 0) continue;
      if (sender_endpoint.port() != FLAGS_radar_port_log) continue;
      pkt_buf_ptr->resize(bytes_recvd);
      RecordLogRawPacket(pkt_buf_ptr, pkt_time_ns);
    }
  });
}

void UdpReceiver::SendSpawn() {
  LOG(INFO) << "Start sending data packets";
  for (auto& [radar, parser] : parser_map_) {
    parser->SendProcessSpawn(
        io_service_,
        std::make_shared<std::function<void(PacketPtr, const udp::endpoint&)>>(
            [this](PacketPtr pkt_buf_ptr, const udp::endpoint& radar_ep) {
              socket_->async_send_to(
                  boost::asio::buffer(*pkt_buf_ptr), radar_ep,
                  [radar_ep](const boost::system::error_code& ec, auto) {
                    if (ec) {
                      LOG(ERROR) << "Failed to send to "
                                 << radar_ep.address().to_string() << ":"
                                 << radar_ep.port() << ". Error: ["
                                 << ec.value() << "]" << ec.message();
                    }
                  });
            }));
  }
}

void UdpReceiver::RecordLogRawPacket(const PacketPtr& pkt_buf_ptr,
                                     int64_t pkt_time_ns) {
  // Support raw packet from multicast version.
  if (!FLAGS_enable_raw_pkt || !FLAGS_is_multicast) return;

  auto* pkt = radar_log_pkt_->add_packets();
  pkt->set_timestamp_ns(pkt_time_ns);
  pkt->set_eth_packet(pkt_buf_ptr->data(), pkt_buf_ptr->size());
  if (radar_log_pkt_->packets_size() > kMaxLogPktCount) {
    radar_log_pkt_->set_timestamp(base::Now());
    radar_log_pkt_pub_.Publish(radar_log_pkt_);
    radar_log_pkt_->mutable_packets()->Clear();
  }
}

}  // namespace radar
