#ifndef OFFBOARD_FALLBACK_ADS_COMMON_MATH_LIBRARY_SORT_H_
#define OFFBOARD_FALLBACK_ADS_COMMON_MATH_LIBRARY_SORT_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>

/**
 * @brief Sorts an array of floats in ascending or descending order.
 *
 * @param array Pointer to the array of floats to be sorted.
 * @param num_of_elements Number of elements in the array.
 * @param direction Sorting direction: "ascend" for ascending order, "descend"
 * for descending order.
 */
extern void CML_Sort(float *array, uint8_t num_of_elements,
                     const char *direction);

#ifdef __cplusplus
}
#endif

#endif  // OFFBOARD_FALLBACK_ADS_COMMON_MATH_LIBRARY_SORT_H_
