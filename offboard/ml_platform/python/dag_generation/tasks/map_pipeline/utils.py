from experiment_metadata.utils.luban_utils import get_username_uuid

from perception_training import luban_settings
from orion_cloud.flower.airflow.ops.luban_offline_task_op import (
    LubanSubmitOfflineTaskConf,
)
from orion_cloud.flower.airflow.ops.luban_distributed_task_op import (
    LubanApiClient,
)
from orion_cloud.flower.airflow.xcom_utils import get_key_value

from dataloop.utils.luban_utils import get_script


IMAGE_UUID = "fedac1bd334d43359dd23570f961b7cf"
_LUBAN_URL = "https://ml.intra.xiaojukeji.com/axe#/pro/jobs?appId={app_id}"
_LUBAN_DISTRIBUTE_URL = (
    "https://ml.intra.xiaojukeji.com/axe#/jobs/offlinemgr"
    + "/detail-new?appId={app_id}"
)
_JENKINS_BASE_URL = "http://jenkinsvoyager.intra.xiaojukeji.com/job/{job_name}/{job_id}"
_JENKINS_JOB_NAME = "voyager_ifxruntime_trt_cached_engines_generator_ov23_trt10_dev"


def gen_luban_task_conf(
    user_name, script_cmd, job_name, resource_uuid, branch, retry=2
):
    user_uuid = get_username_uuid(user_name)
    script_param = " ".join(map(str, script_cmd))

    task_conf = LubanSubmitOfflineTaskConf(
        project_uuid=luban_settings.LUBAN_PROJECT_UUID,
        project_token=luban_settings.LUBAN_PROJECT_TOKEN,
        user_uuid=user_uuid,
        image_uuid=IMAGE_UUID,
        script_path=get_script("av", "map/map-learning", branch),
        script_param=script_param,
        region_name="nmg03",
        resource_uuid=resource_uuid,
        level="PRO",
        job_name=job_name,
        script_source_type="git",
        cuda_driver_version="470.57.02",
        team_id=288,  # map_T3/map
        priority=1,
        retry=retry,
        mount_all_dataset=False,
    )
    return task_conf


def on_success_callback(context, scene, is_distribute=False):
    luban_app_id = get_key_value(context=context, key="task_app_id")
    if luban_app_id is None:
        return
    if is_distribute:
        luban_job_url = _LUBAN_DISTRIBUTE_URL.format(app_id=luban_app_id)
    else:
        luban_job_url = _LUBAN_URL.format(app_id=luban_app_id)
    context["ti"].xcom_push(key="luban_job_url", value=luban_job_url)

    output = {f"{scene}_luban_url": luban_job_url}
    context["ti"].xcom_push(key="output", value=output)


def on_failure_callback(context, scene):
    error_message = str(context["exception"])[-512:]
    err_info = f"{scene} luban fail: {error_message}"
    context["ti"].xcom_push(key="failures", value=err_info)


def gen_distribute_request(user_name, script_cmd, job_name, resource_uuid, branch):
    # user_uuid = get_username_uuid(user_name)
    script_param = " ".join(map(str, script_cmd))
    script_path = get_script("av", "map/map-learning", branch)

    TaskSpecParam = LubanApiClient.TaskSpecParam
    SubmitPytorchTaskRequest = LubanApiClient.SubmitPytorchTaskRequest

    master_task_conf = TaskSpecParam(
        args=script_param,
        image_uuid=IMAGE_UUID,
        port=luban_settings.LUBAN_DISTRIBUTED_TASK_PORT,
        replicas=1,
        script=script_path,
        tag_uuid=resource_uuid,
        script_source_type="git",
        cuda_driver_version="470.57.02",
    )

    worker_task_conf = TaskSpecParam(
        args=script_param,
        image_uuid=IMAGE_UUID,
        port=luban_settings.LUBAN_DISTRIBUTED_TASK_PORT,
        replicas=3,
        script=script_path,
        tag_uuid=resource_uuid,
        script_source_type="git",
        cuda_driver_version="470.57.02",
    )

    request = SubmitPytorchTaskRequest(
        region_name="nmg03",
        user_name=user_name,
        name=job_name,
        priority=1,
        team_id=288,
        level="PRO",
        back_off_limit=2,
        master_spec_param=master_task_conf,
        worker_spec_param=worker_task_conf,
    )
    return request


def on_jenkis_success(context, task_id, scene=""):
    jenkis_job_id = context["ti"].xcom_pull(
        task_ids=task_id, key="JENKINS_JOB_BUILD_NUM"
    )
    jenkis_job_url = _JENKINS_BASE_URL.format(
        job_name=_JENKINS_JOB_NAME, job_id=jenkis_job_id
    )
    output = {f"jenkis_job_url_{scene}": jenkis_job_url}
    context["ti"].xcom_push(key="output", value=output)


def on_jenkis_fail(context, task_id, scene=""):
    jenkis_job_id = context["ti"].xcom_pull(
        task_ids=task_id, key="JENKINS_JOB_BUILD_NUM"
    )
    jenkis_job_url = _JENKINS_BASE_URL.format(
        job_name=_JENKINS_JOB_NAME, job_id=jenkis_job_id
    )
    failures = f"convert {scene} onnx to ifx error! Jenkins Job Page: {jenkis_job_url}"
    context["ti"].xcom_push(key="failures", value=failures)
