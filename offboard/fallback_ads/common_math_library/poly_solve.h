#ifndef OFFBOARD_FALLBACK_ADS_COMMON_MATH_LIBRARY_POLY_SOLVE_H_
#define OFFBOARD_FALLBACK_ADS_COMMON_MATH_LIBRARY_POLY_SOLVE_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <stdbool.h>
#include <stdint.h>

extern bool CML_SolveQuadraticEquation(const float coeff_a, const float coeff_b,
                                       const float coeff_c,
                                       const float default_root,
                                       float root_array[], uint8_t *root_num);

extern int CML_PolySolveQuadratic(const float a, const float b, const float c,
                                  const float *root1, float *root2);

#ifdef __cplusplus
}
#endif

#endif  // OFFBOARD_FALLBACK_ADS_COMMON_MATH_LIBRARY_POLY_SOLVE_H_
