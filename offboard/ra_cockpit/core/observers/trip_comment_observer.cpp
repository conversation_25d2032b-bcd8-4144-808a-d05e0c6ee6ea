#include "trip_comment_observer.h"

#include <utility>

#include "av_comm/topics.h"
#include "glog/logging.h"

namespace remote_assist {

TripCommentObserver::TripCommentObserver(TripCommentCallback callback)
    : trip_comment_callback_(std::move(callback)) {}

bool TripCommentObserver::IsInterestedIn(
    const teleassist::pb::TeleassistRequest& message) {
  return (message.message_type() == kTripComment);
}

std::shared_ptr<google::protobuf::Message> TripCommentObserver::OnMessage(
    const teleassist::pb::TeleassistRequest& message,
    DataChannel data_channel) {
  auto trip_comment = std::make_shared<voy::TripComment>();
  if (!trip_comment->ParseFromString(message.message_data())) {
    LOG(ERROR) << "Received error TripComment message format, len = "
               << message.message_data().length();
    return nullptr;
  }
  if (trip_comment_callback_) {
    trip_comment_callback_(trip_comment, data_channel);
  }
  return trip_comment;
}

}  // namespace remote_assist
