#ifndef ONBOARD_PLANNER_TRAJECTORY_OPTIMIZER_REVERSE_TRAJECTORY_COSTING_H_
#define ONBOARD_PLANNER_TRAJECTORY_OPTIMIZER_REVERSE_TRAJECTORY_COSTING_H_

#include "control_util/costing_base.h"
#include "planner/trajectory/optimizer/trajectory_costing.h"
#include "planner/utility/primitive/primitive_definition/trajectory_primitive.h"
#include "planner_protos/trajectory_costing.pb.h"

namespace control_util {

// Trait for TrajectoryFormula specialization.
template <>
struct FormulaTraits<DdpProblemType::kReverseTrajectory> {
  using StateControlSpecs = vehicle_model::CarModel;

  using PbState = vehicle_model::pb::CarState;
  using PbControl = vehicle_model::pb::CarControl;

  using PbFeatureType = planner::pb::TrajectoryFeatureType;
  using PbWeightAdjustSpec = control_util::pb::CostingWeightAdjustSpec;
  using PbFeatureParameter = planner::pb::TrajectoryFeatureParameter;
  using PbCostingTerm = planner::pb::TrajectoryCostingTerm;
  using Primitives = planner::primitive::TrajectoryPrimitives;
  struct StepwisePrimitives {
    vehicle_model::VehicleRepresentation ego_representation;
  };

  using PbSolution = planner::pb::TrajectorySolution;
  using PbWarmstarterDebug = planner::pb::TrajectoryWarmstarterDebug;
  using PbSolverDebug = planner::pb::TrajectorySolverDebug;
  using PbSolverInterationDebug = planner::pb::TrajectorySolverIteration;
  using PbWarmstartType = planner::pb::TrajectoryWarmstartType;

  static constexpr int FEATURE_DIM =
      planner::pb::TrajectoryFeatureType_ARRAYSIZE;
};
}  // namespace control_util

namespace planner {

using ReverseTrajectoryFormula =
    control_util::Formula<control_util::DdpProblemType::kReverseTrajectory>;

}  // namespace planner

namespace control_util {

// Computes the feature given type.
template <>
void GetFeature<DdpProblemType::kReverseTrajectory>(
    int step, int total_step_size,
    const typename planner::ReverseTrajectoryFormula::PbCostingTerm&
        costing_term,
    const typename planner::ReverseTrajectoryFormula::PbFeatureParameter&
        feature_param,
    const typename planner::ReverseTrajectoryFormula::State& x,
    const typename planner::ReverseTrajectoryFormula::Control& u,
    const typename planner::ReverseTrajectoryFormula::StepwisePrimitives&
        stepwise_primitives,
    const typename planner::ReverseTrajectoryFormula::Primitives& primitives,
    typename planner::ReverseTrajectoryFormula::Feature* feature);

template <>
planner::ReverseTrajectoryFormula::StepwisePrimitives
planner::ReverseTrajectoryFormula::GetStepwisePrimitives(
    int step, bool is_terminal, const State& x, const Control& u,
    const Primitives& primitives) const;

}  // namespace control_util

#endif  // ONBOARD_PLANNER_TRAJECTORY_OPTIMIZER_REVERSE_TRAJECTORY_COSTING_H_
