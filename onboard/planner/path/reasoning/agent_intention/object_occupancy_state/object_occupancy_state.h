#ifndef ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_OBJECT_OCCUPANCY_STATE_OBJECT_OCCUPANCY_STATE_H_
#define ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_OBJECT_OCCUPANCY_STATE_OBJECT_OCCUPANCY_STATE_H_

#include <memory>
#include <unordered_map>
#include <vector>

#include <tbb/concurrent_unordered_map.h>

#include "planner/behavior/util/agent_state/agent_in_lane_state.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_wrapper.h"
#include "planner/global_object_manager/object_information.h"
#include "planner/path/reasoning/agent_intention/object_occupancy_state/object_stamped_snapshot_info.h"
#include "planner/world_model/planner_object/planner_object.h"
#include "prediction_protos/stationary_intention.pb.h"
#include "voy_protos/trajectory.pb.h"

namespace planner {

// This class defines geometrical descriptions about current and predicted poses
// of objects(including agents as well as construction zones) over corridors.
// TODO(Path): Should keep EgoInLaneParams in the agent_in_lane_state.h but
// move it to the object_occupancy_state folder when AgentInLaneState is
// deprecated.
class ObjectOccupancyState {
 public:
  ObjectOccupancyState(
      const PlannerObject& planner_object,
      const ObjectInformation& object_information,
      const std::vector<PredictedTrajectoryWrapper>&
          predicted_trajectory_wrappers,
      const math::geometry::PolylineCurve2d& nominal_path,
      const math::geometry::PolylineCurve2d& left_corridor_boundary,
      const math::geometry::PolylineCurve2d& right_corridor_boundary,
      const EgoInLaneParams& ego_params,
      ObjectStampedSnapshotInfo&& snapshot_info_at_plan_init_time,
      pb::MotionMode motion_mode,
      pb::AgentImaginaryActualType agent_imaginary_actual_type =
          pb::AgentImaginaryActualType::ACTUAL);

  const ObjectStampedSnapshotInfo& current_snapshot_info() const {
    return current_snapshot_info_;
  }

  const std::unordered_map<int, ObjectTrajectoryStates>&
  predicted_trajectory_occupancy_states() const {
    return predicted_trajectory_occupancy_states_;
  }

  int primary_trajectory_id() const { return primary_trajectory_id_; }

  const PlannerObject& planner_object() const { return planner_object_; }

  const TrafficParticipantPose& pose() const {
    return planner_object_.pose_at_plan_init_ts();
  }
  ObjectId object_id() const { return planner_object().id(); }

  // Returns true if the primary predicted trajectory is stationary.
  bool is_primary_stationary() const {
    return planner_object_.is_primary_stationary();
  }

  voy::perception::ObjectType object_type() const {
    return planner_object_.object_type();
  }

  const prediction::pb::PredictedTrajectory* start_to_move_trajectory() const {
    return start_to_move_trajectory_;
  }

  bool has_start_to_move_trajectory() const {
    return start_to_move_trajectory_ != nullptr;
  }

  const prediction::pb::StationaryIntention& stationary_intention() const {
    return planner_object_.stationary_intention();
  }

  pb::AgentImaginaryActualType agent_imaginary_actual_type() const {
    return agent_imaginary_actual_type_;
  }

 private:
  const PlannerObject& planner_object_;

  // The map from traj id to object trajectory states.
  std::unordered_map<int, ObjectTrajectoryStates>
      predicted_trajectory_occupancy_states_;

  // The start-to-move trajectory, nullptr if the agent is not start-to-move.
  const prediction::pb::PredictedTrajectory* start_to_move_trajectory_ =
      nullptr;

  int primary_trajectory_id_ = -1;

  ObjectStampedSnapshotInfo current_snapshot_info_;

  // Whether the state comes from an imaginary planner object.
  pb::AgentImaginaryActualType agent_imaginary_actual_type_ =
      pb::AgentImaginaryActualType::ACTUAL;
};

// The map of the agent object occupancy states.
using ObjectOccupancyStateMap =
    tbb::concurrent_unordered_map<int64_t,
                                  std::unique_ptr<ObjectOccupancyState>>;

}  // namespace planner

#endif  // ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_OBJECT_OCCUPANCY_STATE_OBJECT_OCCUPANCY_STATE_H_
