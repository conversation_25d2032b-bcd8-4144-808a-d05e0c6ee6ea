#ifndef ONBOARD_PLANNER_SPEED_UTIL_PRECEDENCE_ROAD_PRECEDENCE_UTIL_H_
#define ONBOARD_PLANNER_SPEED_UTIL_PRECEDENCE_ROAD_PRECEDENCE_UTIL_H_

#include <string>
#include <utility>
#include <vector>

#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_wrapper.h"
#include "planner/speed/reasoning/overlap_region_info.h"
#include "planner/speed/reasoning_input/reasoning_object.h"
#include "planner/speed/reasoning_input/trajectory_info.h"
#include "planner/speed/reasoning_input/world_context.h"
#include "planner_protos/behavior_common_type.pb.h"
#include "planner_protos/overlap.pb.h"
#include "pnc_map_service/pnc_map_service.h"

namespace planner {
namespace speed {
namespace util {
// Return the road precedence of a predicted agent trajectory aganist ego based
// on traffic regulations summarized by product team.
// https://cooper.didichuxing.com/docs/sheet/2199919919158#MODOC
// NOTE(minhanli): The input |overlap_regions| should come from the same object
// generating the |predicted_trajectory|. DCHECK has been added to enforce this
// requirement.
RoadPrecedenceResult GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
    const TrajectoryInfo& ego_trajectory_info,
    const std::vector<std::pair<int64_t, math::Range<double>>>&
        ego_lane_sequence_id_ra_arclength_ranges,
    const std::vector<std::reference_wrapper<const pb::OverlapRegion>>&
        overlap_regions,
    const std::vector<OverlapRegionInfo>& overlap_region_infos,
    const ReasoningObject& reasoning_object,
    const PredictedTrajectoryWrapper& predicted_trajectory,
    planner::pb::RoadPriorityType::Enum road_priority,
    const std::optional<std::vector<route_association::MapElementAndPoseInfo>>&
        agent_associated_route_opt,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    double ra_to_front_bumper_shift, double ra_to_rear_bumper_shift,
    std::string* debug_string_ptr);

// Gets a source for triggering additional precedence inference besides original
// computed result based on map labeling.
planner::pb::PrecedenceInferenceSource::Enum GetPrecedenceInferenceSource(
    const pnc_map::Lane* conflicting_ego_lane,
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const std::vector<route_association::MapElementAndPoseInfo>& agent_route,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    std::string* debug_string_ptr);

// Gets info of precedence inference for straight merge in junction.
// NOTE: Lane pointers in |InferredPrecedenceInfo| could be |nullptr| when we
// could not find agent entrance lane.
InferredPrecedenceInfo GetInferredPrecedenceInfoForStraightMergeInJunction(
    const pnc_map::Lane* conflicting_ego_lane,
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const std::vector<route_association::MapElementAndPoseInfo>& agent_route,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    std::string* debug_string_ptr);

}  // namespace util
}  // namespace speed
}  // namespace planner

#endif  // ONBOARD_PLANNER_SPEED_UTIL_PRECEDENCE_ROAD_PRECEDENCE_UTIL_H_
