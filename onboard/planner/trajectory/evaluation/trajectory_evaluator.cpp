#include "planner/trajectory/evaluation/trajectory_evaluator.h"

#include <algorithm>
#include <tuple>

#include "geometry/algorithms/intersects.h"
#include "math/range.h"
#include "planner/constants.h"
#include "planner/utility/trajectory_manipulation/trajectory_manipulation_util.h"
#include "trace/trace.h"
#include "voy_trace/trace_planner.h"

namespace planner {
namespace {
// Gets the maximum pose size of the evaluation trajectory.
int GetMaxEvaluationTrajectoryPosesSize(
    const pb::TrajectoryEvaluationConfig& config) {
  return static_cast<int>(config.evaluator_time_horizon_sec() /
                          constants::kTrajectoryIntervalInSec);
}

// Checks whether trajectory contain an extreme acceleration.
std::tuple<double, bool> CheckAcceleration(
    const pb::Trajectory& trajectory, const math::Range<int>& evaluation_range,
    const pb::TrajectoryEvaluationConfig& config) {
  const double max_accel =
      std::max_element(
          trajectory.poses().begin() + evaluation_range.start_pos,
          trajectory.poses().begin() + evaluation_range.end_pos,
          [](const pb::TrajectoryPose& pose1, const pb::TrajectoryPose& pose2) {
            return pose1.accel() < pose2.accel();
          })
          ->accel();
  const bool has_extreme_accel =
      max_accel > config.normal_extreme_acceleration_mpss();

  return {max_accel, has_extreme_accel};
}

// Checks whether trajectory contain an extreme deceleration.
std::tuple<double, bool> CheckDeceleration(
    const pb::Trajectory& trajectory, const math::Range<int>& evaluation_range,
    const pb::TrajectoryEvaluationConfig& config) {
  const double min_decel =
      std::min_element(
          trajectory.poses().begin() + evaluation_range.start_pos,
          trajectory.poses().begin() + evaluation_range.end_pos,
          [](const pb::TrajectoryPose& pose1, const pb::TrajectoryPose& pose2) {
            return pose1.accel() < pose2.accel();
          })
          ->accel();
  const bool has_extreme_decel =
      min_decel < config.normal_extreme_deceleration_mpss();

  return {min_decel, has_extreme_decel};
}

// Checks whether trajectory contain an extreme lateral acceleration.
std::tuple<double, bool> CheckLateralAcceleration(
    const pb::Trajectory& trajectory, const math::Range<int>& evaluation_range,
    const pb::TrajectoryEvaluationConfig& config) {
  const double max_lat_accel =
      std::max_element(
          trajectory.poses().begin() + evaluation_range.start_pos,
          trajectory.poses().begin() + evaluation_range.end_pos,
          [](const pb::TrajectoryPose& pose1, const pb::TrajectoryPose& pose2) {
            return pose1.abs_lat_accel() < pose2.abs_lat_accel();
          })
          ->abs_lat_accel();
  const bool has_extreme_lat_accel =
      max_lat_accel > config.extreme_lateral_acceleration_mpss();

  return {max_lat_accel, has_extreme_lat_accel};
}

// Checks max juke and juke risk status on the given trajectory.
std::tuple<double, bool, JukeRiskStatus> CheckJuke(
    const pb::Trajectory& trajectory, const math::Range<int>& evaluation_range,
    const pb::TrajectoryEvaluationConfig& config) {
  const double max_abs_juke =
      std::max_element(
          trajectory.poses().begin() + evaluation_range.start_pos,
          trajectory.poses().begin() + evaluation_range.end_pos,
          [](const pb::TrajectoryPose& pose1, const pb::TrajectoryPose& pose2) {
            return pose1.abs_juke() < pose2.abs_juke();
          })
          ->abs_juke();
  const bool has_extreme_juke = max_abs_juke > config.extreme_juke_mpsss();

  JukeRiskStatus juke_risk_status = JukeRiskStatus::kNormal;
  if (max_abs_juke > config.critical_juke_mpsss()) {
    juke_risk_status = JukeRiskStatus::kCritical;
  } else if (max_abs_juke > config.abnormal_juke_mpsss()) {
    juke_risk_status = JukeRiskStatus::kAbnormal;
  } else if (max_abs_juke > config.mild_juke_mpsss()) {
    juke_risk_status = JukeRiskStatus::kMild;
  } else {
    juke_risk_status = JukeRiskStatus::kNormal;
  }
  return {max_abs_juke, has_extreme_juke, juke_risk_status};
}

// Checks hard road bondary violation by checking geometric intersections.
bool HasGeometryBasedHardRoadBoundaryViolation(
    const vehicle_model::pb::AxleRectangularMeasurement& shape_measurement,
    const pb::Trajectory& trajectory,
    const std::vector<math::geometry::Polyline2d>& hard_boundary_lines,
    const math::Range<int>& evaluation_range,
    const pb::TrajectoryEvaluationConfig& config) {
  (void)config;
  return std::any_of(
      trajectory.poses().begin() + evaluation_range.start_pos,
      trajectory.poses().begin() + evaluation_range.end_pos,
      [&shape_measurement, &hard_boundary_lines](const auto& pose) {
        const auto contour =
            GetBoundingBoxFromTrajectoryPose(pose, shape_measurement);
        return std::any_of(
            hard_boundary_lines.begin(), hard_boundary_lines.end(),
            [&contour](const auto& hard_boundary_line) {
              // Check for intersection between Polygon and LineString
              return math::geometry::Intersects(contour, hard_boundary_line);
            });
      });
}
}  // namespace

TrajectoryEvaluationResult TrajectoryEvaluator::Evaluate(
    const WorldModel& world_model, const pb::Trajectory& trajectory,
    const std::vector<math::geometry::Polyline2d>& hard_boundary_lines,
    pb::TrajectoryEvaluationDebug* debug) const {
  TRACE_EVENT_SCOPE(planner, TrajectoryEvaluator_Evaluate);

  // TODO(chiyu): Check these values in a single loop.
  math::Range<int> evaluation_range{};
  evaluation_range.start_pos = trajectory.replan_start_idx();
  evaluation_range.end_pos =
      std::min(trajectory.replan_start_idx() +
                   GetMaxEvaluationTrajectoryPosesSize(config_),
               trajectory.poses().size());
  TrajectoryEvaluationResult evaluation_result;
  std::tie(evaluation_result.max_accel, evaluation_result.has_extreme_accel) =
      CheckAcceleration(trajectory, evaluation_range, config_);
  std::tie(evaluation_result.min_decel, evaluation_result.has_extreme_decel) =
      CheckDeceleration(trajectory, evaluation_range, config_);
  std::tie(evaluation_result.max_lat_accel,
           evaluation_result.has_extreme_lat_accel) =
      CheckLateralAcceleration(trajectory, evaluation_range, config_);
  std::tie(evaluation_result.max_abs_juke, evaluation_result.has_extreme_juke,
           evaluation_result.juke_risk_status) =
      CheckJuke(trajectory, evaluation_range, config_);

  const vehicle_model::pb::AxleRectangularMeasurement& shape_measurement =
      world_model.robot_state().car_model_with_shape().shape_measurement();
  evaluation_result.has_hard_road_boundary_violation =
      HasGeometryBasedHardRoadBoundaryViolation(shape_measurement, trajectory,
                                                hard_boundary_lines,
                                                evaluation_range, config_);

  // Check obstacle collision.
  int64_t collision_object_id = -1;
  std::tie(collision_object_id, evaluation_result.has_obstacle_collision) =
      HasCollisionWithObjects(world_model.planner_object_map(), trajectory,
                              shape_measurement, evaluation_range);

  if (debug) {
    debug->set_max_abs_juke(evaluation_result.max_abs_juke);
    debug->set_has_extreme_juke(evaluation_result.has_extreme_juke);
    debug->set_has_hard_boundary_violation(
        evaluation_result.has_hard_road_boundary_violation);
    debug->set_has_obstacle_collision_violation(
        evaluation_result.has_obstacle_collision);
    debug->set_has_extreme_decel(evaluation_result.has_extreme_decel);
    debug->set_extreme_decel(evaluation_result.min_decel);
    debug->set_obstacle_collision_id(collision_object_id);
  }
  evaluation_result.is_valid = !evaluation_result.has_extreme_decel &&
                               !evaluation_result.has_extreme_juke;

  return evaluation_result;
}

void TrajectoryEvaluator::AddTrajectoryAnalytics(
    const TrajectoryEvaluationResult& evaluation_result,
    const pb::Trajectory& trajectory,
    pb::TrajectoryProblemType trajectory_problem_type) const {
  trajectory_evaluator_clerk_.AddTrajectoryAnalytics(
      evaluation_result, trajectory, trajectory_problem_type);
}
}  // namespace planner
