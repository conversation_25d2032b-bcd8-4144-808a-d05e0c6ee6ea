#ifndef ONBOARD_VARCH_VNODE_DETAIL_TRANSPORT_PCIE_FACTORY_H_
#define ONBOARD_VARCH_VNODE_DETAIL_TRANSPORT_PCIE_FACTORY_H_

#include <chrono>
#include <memory>
#include <string>
#include <utility>

#include <boost/asio/spawn.hpp>
#include <boost/asio/steady_timer.hpp>

#include "av_comm/ntb_channel.h"
#include "av_comm/onboard_config.h"
#include "av_comm/sensor_util.h"
#include "onboard/gen4/ntb_bridge/ntb_sender.h"
#include "varch/base/date.h"
#include "varch/base/exception.h"
#include "varch/base/macro.h"
#include "varch/utils/log.h"
#include "varch/vnode/detail/transport/transport_factory_base.h"

// /home/<USER>/CodeBackup/voyager/onboard/common/av_comm/include/
namespace varch {
namespace vnode {
namespace detail {
namespace transport {
class PcieFactory : public TransportFactoryBase {
 public:
  static PcieFactory* GetInstance() {
    static PcieFactory factory;
    return &factory;
  }

  // TODO(fengwei) Remove the following overrides after completely decouple PCIe
  // transport from fastdds pub/sub
  // For customize data_sharing off only
  DDSDataReaderQos CreateReaderQos(const std::string& topic,
                                   DDSSubscriber* publisher) override;
  DDSDataWriterQos CreateWriterQos(const std::string& topic,
                                   DDSPublisher* publisher) override;
  // For customize SHM transport off only
  DDSParticipantQos CreateDDSParticipantQos() override;

 private:
  PcieFactory() : TransportFactoryBase("pcie") {}
};

// The PCIe Non-Transparent Bridging(NTB) technique is used for data
// transition between Cpubox and Xaiver on Gen3 platform. This class aims to
// convert the regular Channel to NtbChannel and publish the message.
//
// The read end type must be one of:
// 1. MessageType
// 2. shared_ptr<MessageType>
// 3. shared_ptr<const MessageType>
// 4. FrameHandleWith<MessageType>
// 5. FrameHandleWith<shared_ptr<MessageType>>
// 6. FrameHandleWith<shared_ptr<const MessageType>>
// 7. com::MessageAdapter<MessageType>
// 8. com::MessageAdapter<shared_ptr<MessageType>>
// 9. com::MessageAdapter<shared_ptr<const MessageType>>
template <typename MessageType>
class NtbPublisher {
 public:
  NtbPublisher(const node::AnyExecutor& ex, const std::string& topic) {
    if (av_comm::IsGen4Platform()) {
      is_gen4 = true;
      ntb_sender_ = std::make_unique<ntb_bridge::NtbSenderWrapper>(topic);
      VLOG_INFO << "NtbSender initialized for Gen4 platform, topic: " << topic;
    } else {
      (void)ex;
      ntb_channel_ = std::make_unique<av_comm::NtbChannel<MessageType>>(
          ex, av_comm::TopicToNtbLinkType(topic),
          av_comm::TopicToNtbPort(topic));
      VLOG_INFO << "NtbChannel initialized for Gen3 platform, topic: " << topic;
    }
  }

  void Publish(const MessageType& message, int64_t publish_timestamp) {
    if (is_gen4) {
      ntb_sender_->Send(message, publish_timestamp);
    } else {
      ntb_channel_->Write(message, publish_timestamp);
    }
  }

 private:
  bool is_gen4{false};
  std::unique_ptr<av_comm::NtbChannel<MessageType>> ntb_channel_;
  std::unique_ptr<ntb_bridge::NtbSenderWrapper> ntb_sender_;
};

class IOServiceManager {
 public:
  static IOServiceManager* GetInstance();
  ~IOServiceManager();
  std::shared_ptr<boost::asio::io_service> GetIOService();

 private:
  IOServiceManager();
  std::shared_ptr<boost::asio::io_service> io_service_;
  int thread_num_ = 4;
};

template <typename MessageType>
class PciePublisherProcessor : public PublisherProcessorInterface<MessageType> {
 public:
  using PublisherOption = varch::vnode::detail::publisher::PublisherOption;
  // 构造函数里填充pcie相关的东西.
  PciePublisherProcessor(const PublisherOption& option,
                         const std::string& type_name)
      : PublisherProcessorInterface<MessageType>(option, type_name),
        io_service_(IOServiceManager::GetInstance()->GetIOService()),
        strand_(io_service_->get_executor()),
        publisher_(io_service_->get_executor(), option.topic) {
    VLOG_INFO << "PciePublisherProcessor Enter:" << option.topic;
  }

  void ProcessAsync(DDSDataWriter* data_writer,
                    std::shared_ptr<const MessageType>&& msg_ptr,
                    const MessageHeader& header) final {
    (void)data_writer;
    const std::string& topic = this->option_.topic;

    boost::asio::spawn(strand_, [this, msg_ptr = std::move(msg_ptr), topic,
                                 header](
                                    boost::asio::yield_context yield) mutable {
      (void)yield;
      publisher_.Publish(
          *msg_ptr,
          header.publish_timestamp()
              ? header.publish_timestamp()
              : std::chrono::system_clock::now().time_since_epoch().count());
      this->last_publish_time_.store(base::GetCurrentNanoSeconds(),
                                     std::memory_order_release);
    });
  }

  void Process(DDSDataWriter* data_writer,
               const std::shared_ptr<const MessageType>& msg_ptr,
               const MessageHeader& header) final {
    Process(data_writer, *msg_ptr, header);
  }

  void Process(DDSDataWriter* data_writer, const MessageType& msg,
               const MessageHeader& header) final {
    (void)data_writer;
    publisher_.Publish(
        msg, header.publish_timestamp()
                 ? header.publish_timestamp()
                 : std::chrono::system_clock::now().time_since_epoch().count());
    this->last_publish_time_.store(base::GetCurrentNanoSeconds(),
                                   std::memory_order_release);
  }

 private:
  std::shared_ptr<boost::asio::io_service> io_service_;
  boost::asio::strand<boost::asio::any_io_executor> strand_;
  NtbPublisher<MessageType> publisher_;
};

}  // namespace transport
}  // namespace detail
}  // namespace vnode
}  // namespace varch

#endif  // ONBOARD_VARCH_VNODE_DETAIL_TRANSPORT_INTER_FACTORY_H_
