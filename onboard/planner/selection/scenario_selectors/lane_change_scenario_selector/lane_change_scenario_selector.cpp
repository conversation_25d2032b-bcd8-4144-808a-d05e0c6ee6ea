#include "planner/selection/scenario_selectors/lane_change_scenario_selector/lane_change_scenario_selector.h"

#include <algorithm>
#include <cmath>
#include <cstddef>
#include <cstdint>
#include <limits>
#include <numeric>
#include <optional>
#include <sstream>
#include <string>
#include <unordered_map>
#include <utility>

#include "lane_change_selection_util.h"
#include "log_utils/log_macros.h"
#include "math/constants.h"
#include "math/interpolation.h"
#include "math/math_util.h"
#include "math/unit_conversion.h"
#include "planner/behavior/util/lane_sequence/route_preview_utility.h"
#include "planner/decoupled_maneuvers/cross_lane/cross_lane_info_manager.h"
#include "planner/decoupled_maneuvers/cross_lane/cross_lane_utils.h"
#include "planner/decoupled_maneuvers/lane_change/lane_change_common_util.h"
#include "planner/decoupled_maneuvers/lane_change/lane_change_env_analyzer_util.h"
#include "planner/decoupled_maneuvers/lane_change/lane_change_object_info.h"
#include "planner/decoupled_maneuvers/lane_change/lane_change_selection_info.h"
#include "planner/planning_gflags.h"
#include "planner/selection/costs/progress_cost.h"
#include "planner/selection/costs/routing_v2_cost.h"
#include "planner/selection/scenario_selectors/lane_change_scenario_selector/lane_change_selection_util.h"
#include "planner/selection/scenario_selectors/selector_utils.h"
#include "planner/selection/selection_utils.h"
#include "planner/selection/trajectory_meta.h"
#include "planner/speed/overlap/overlap_lib_util.h"
#include "planner/speed/reasoning/reasoner/lead_and_merge_agent_reasoner/lead_and_merge_agent_reasoner.h"
#include "planner/speed/solver/speed_result.h"
#include "rt_event/rt_event.h"
#include "voy_rt_event/rt_event_planner.h"

namespace planner {
namespace selection {

// Inefficient routing cost.
constexpr double kInefficiencyOrExcessiveCostForNormalLc = 2.5;
constexpr double kInefficiencyOrExcessiveCostForCrawl = 5.5;
// Cost for left lane change during immediate pull over.
constexpr double kCostForLeftLcDuringImmediatePullOver = 1.0;
// Speed threshold for aborting a lane change due to parked car/CZ.
constexpr double kLcAbortEgoSpeedThreshInMps = 3.0;
// Disadvantage for risky lane change before blockage.
constexpr double kRoutingCostForBlockageLcWithRisk = 1.0;
// Extra routing cost due to blocking traffic for normal lane change.
constexpr double kExtraBlockingTrafficCostForNormalLaneChange = 2.5;
// Extra routing cost due to blocking traffic for high reward lane change, i.e.
// lane change crawl or creep.
constexpr double kExtraBlockingTrafficCostForHighRewardLaneChange = 5.0;

// Thresholds for excessive brake cost in LC, roughly speaking we don't want any
// decel more than min thres during LC preparation and will very likely to abort
// if we readh max thres.
constexpr double kLcMinExcessiveBrake = 1.95;  // m/s^2
constexpr double kLcMaxExcessiveBrake = 3.0;   // m/s^2
constexpr double kLcMaxExcessiveBrakeCost = 0.8;
// Risk cost on LC candidate if its tail/lead agent changes.
constexpr double kObsoleteLcGapMaxRisk = 1.5;
constexpr double kObsoleteLcGapMinEgoSpeedInMps = 3.0;  // m/s
constexpr double kObsoleteLcGapMaxEgoSpeedInMps = 6.0;  // m/s
constexpr double kObsoleteLcGapExcessiveBrakeFactor = 1.2;
constexpr double kObsoleteLcGapSlightBrakeFactor = 0.8;
constexpr double kLcMaxUncomfortableBrake = 1.0;  // m/s^2
constexpr double kObsoleteLcGapSpeedMinFactor = 0.6;
constexpr double kObsoleteLcGapSpeedMaxFactor = 1.0;
// MAx brake check duration for lane change safe yield deceleration risk.
constexpr int64_t kBrakeCheckDurationInMsec = 2000;

bool LaneChangeScenarioSelector::should_recompute_overall_costs_ = false;

bool LaneChangeScenarioSelector::detected_lc_high_risk_ = false;
// The capacity of lc collision risk window.
constexpr size_t kLcCollisionRiskWindowCapacity = 10;
LaneChangeCollisionRiskWindow
    LaneChangeScenarioSelector::lc_collision_risk_window_(
        kLcCollisionRiskWindowCapacity);

void LaneChangeScenarioSelector::AdjustRoutingCostByRisk(
    const std::vector<TrajectoryMetaData*>& candidates,
    std::unordered_map<pb::CostType, CostComputerMetadata>* sub_costs_map_ptr)
    const {
  // Don't adjust routing cost when lane change is already in progress to avoid
  // behavior flicker.
  if (seed_.selected_behavior_type() == planner::pb::BehaviorType::CROSS_LANE) {
    return;
  }

  // Only handle routing cost arbitration for lane change before blockage.
  if (!is_blockage_lane_change()) {
    return;
  }

  // Adjust routing cost according to the risks on CL and LK.
  constexpr double kMaxRiskToleranceAtHighSpeed = 0.05;
  constexpr double kMaxRiskToleranceAtLowSpeed = 0.8;
  constexpr double kSpeedUpperBoundForRiskToleranceInMps = 6.0;
  constexpr double kSpeedLowerBoundForRiskToleranceInMps = 3.0;
  const double ego_speed =
      world_model_.robot_state().plan_init_state_snapshot().speed();
  const double cl_risk_tolerance_wrt_ego_speed = math::GetLinearInterpolatedY(
      kSpeedLowerBoundForRiskToleranceInMps,
      kSpeedUpperBoundForRiskToleranceInMps, kMaxRiskToleranceAtLowSpeed,
      kMaxRiskToleranceAtHighSpeed, ego_speed);

  double max_routing_cost_in_lk = 0.0;
  double min_risk_in_lk = 0.0;
  double max_risk_in_cl = 0.0;
  for (TrajectoryMetaData* candidate : candidates) {
    if (candidate->IsCrossLane()) {
      math::UpdateMax(candidate->pose_collision_risk, max_risk_in_cl);
      candidate->routing_debug_str += absl::StrFormat("\nblockage lc;\n");
    } else {
      math::UpdateMin(candidate->pose_collision_risk, min_risk_in_lk);
      math::UpdateMax(candidate->normalized_routing_cost,
                      max_routing_cost_in_lk);
    }
  }

  if (max_risk_in_cl - min_risk_in_lk < cl_risk_tolerance_wrt_ego_speed) {
    return;
  }

  // CL has higher risk than LK. Shouldn't encourage CL from routing.
  DCHECK(sub_costs_map_ptr != nullptr);
  CostComputerMetadata& routing_cost_metadata =
      (*sub_costs_map_ptr)[pb::CostType::ROUTING];
  DCHECK_EQ(routing_cost_metadata.trajectory_costs.size(), candidates.size());
  for (size_t i = 0; i < candidates.size(); ++i) {
    TrajectoryMetaData* candidate = candidates[i];
    if (!candidate->IsCrossLane()) {
      continue;
    }

    // Add routing disadvantage for CL.
    routing_cost_metadata.trajectory_costs[i] =
        max_routing_cost_in_lk + kRoutingCostForBlockageLcWithRisk;
    // Also update normalized_routing_cost in candidate here to match web
    // monitor visualization.
    candidate->normalized_routing_cost =
        routing_cost_metadata.trajectory_costs[i];
    candidate->routing_debug_str += absl::StrFormat(
        "overwrite routing cost due to cl risk higher than lk; "
        "max_risk_in_cl=%.2f, min_risk_in_lk=%.2f, risk_tolerance=%.2f, "
        "max_routing_cost_in_lk=%.2f\n",
        max_risk_in_cl, min_risk_in_lk, cl_risk_tolerance_wrt_ego_speed,
        max_routing_cost_in_lk);
  }
}

void LaneChangeScenarioSelector::ComputeAndPopulateRoutingCost(
    const std::vector<TrajectoryMetaData*>& candidates) {
  ComputeBasicRoutingCost(candidates);

  const auto& iter = std::find_if(candidates.begin(), candidates.end(),
                                  [](const TrajectoryMetaData* candidate) {
                                    return candidate->IsCrossLane();
                                  });
  if (iter != candidates.end()) {
    set_is_blockage_lane_change(IsLaneChangeDueToBlockage(
        (*iter)->trajectory_info->GetCrossLaneInfo()));
  }

  const bool is_immediate_po_lc =
      std::any_of(candidates.begin(), candidates.end(), [](const auto* cand) {
        return cand->IsCrossLane() &&
               cand->lane_change_status().is_triggered_by_immediate_pull_over;
      });
  const planner::pb::SelectionSeed& driving_state = seed_.selection_seed();
  const bool is_stuck_scene =
      !math::NearZero(driving_state.stuck_signal().stuck_score());
  const bool is_elective_lane_change =
      driving_state.elective_lane_change_decision().is_triggered();
  const bool is_early_lane_change =
      IsEarlyLaneChangeExecutionEnable(world_model_.order_start_time_info()) &&
      iter != candidates.end() &&
      (*iter)->trajectory_info->GetCrossLaneInfo().is_early_lc_sequence;
  const bool is_urgent_lc = is_immediate_po_lc || is_stuck_scene ||
                            is_elective_lane_change || is_early_lane_change;
  const bool selected_cl_and_overwrote_last_cycle =
      (seed_.selected_behavior_type() ==
       planner::pb::BehaviorType::CROSS_LANE) &&
      (seed_.selection_seed().has_cross_lane_selection_seed()
           ? seed_.selection_seed()
                 .cross_lane_selection_seed()
                 .routing_cost_overwritten_for_urgent_cl()
           : seed_.selection_seed().overwrite_routing_for_urgent_lc());
  const bool should_overwrite_routing =
      is_urgent_lc || selected_cl_and_overwrote_last_cycle;
  mutable_seed_.mutable_selection_seed()
      ->mutable_cross_lane_selection_seed()
      ->set_routing_cost_overwritten_for_urgent_cl(should_overwrite_routing);

  blocking_traffic_close_to_lane_change_end_ = false;
  const bool is_high_reward_lane_change = std::any_of(
      candidates.begin(), candidates.end(),
      [](const TrajectoryMetaData* candidate) {
        return candidate->IsCrossLane() &&
               ShouldGiveRewardToLaneChange(*candidate->trajectory_info);
      });
  bool has_inefficient_lane_change = false;
  for (TrajectoryMetaData* candidate : candidates) {
    std::ostringstream debug_oss;
    if (should_overwrite_routing) {
      candidate->normalized_routing_cost =
          static_cast<double>(candidate->IsLaneKeep());
      debug_oss << "\nrouting cost is overwritten. \nis_urgent_lc = "
                << is_urgent_lc << ", selected_cl_and_overwrote_last_cycle = "
                << selected_cl_and_overwrote_last_cycle << ".\n";
    }

    // Handle cases where immediate pull over is triggered during left lane
    // change.
    AdjustCostForLeftLcDuringImmediatePullOver(*candidate, debug_oss);
    candidate->routing_debug_str += debug_oss.str();
    debug_oss.str("");

    if (!candidate->IsCrossLane()) {
      continue;
    }

    // Get reasoning result from lane change info.
    const CrossLaneInfo& cross_lane_info =
        candidate->trajectory_info->GetCrossLaneInfo();
    const double lc_completion_rate =
        cross_lane_info.cross_lane_meta.cross_lane_completion_rate();
    const bool is_excessive_blockage_avoidance_lc =
        IsExcessiveBlockageAvoidanceLc(
            cross_lane_info, seed_.lane_change_info_seed(), debug_oss);

    // Compute extra cost if blocking traffic.
    candidate->normalized_routing_cost +=
        ComputeBlockingTrafficCost(*candidate, lc_completion_rate, debug_oss);
    candidate->routing_debug_str += debug_oss.str();
    LOG(INFO) << kLogHeader << debug_oss.str();
    debug_oss.str("");

    // Handle parked cars and construction zones during lane change.
    // Don't abort the current lane change for parked cars or CZs in the
    // scenario of consecutive lane changes.
    const planner::cross_lane::pb::CrossLaneMetaData& cross_lane_meta =
        candidate->trajectory_info->GetCrossLaneInfo().cross_lane_meta;
    if (cross_lane_meta.is_consecutive_cross_lane_in_same_direction()) {
      DCHECK_GT(cross_lane_meta.cross_lane_count(), 1);
      debug_oss << "\n\nConsecutive lane changes in the same direction.";
      continue;
    }

    // Post processing for CL candidates.
    const double lc_abort_ego_speed_threshold = math::GetLinearInterpolatedY(
        0.0, 1.0, kLcAbortEgoSpeedThreshInMps,
        constants::kDefaultEgoNearStaticSpeedInMps, lc_completion_rate);
    const double ego_speed =
        world_model_.robot_state().plan_init_state_snapshot().speed();

    // Handle parked cars and construction zones for CL candidate. Add an extra
    // routing disadvantage for cases where there are parked cars or CZs ahead
    // and yielded to on the target region during lane change.
    const bool is_inefficient_candiate =
        IsInefficientCandidate(*candidate, debug_oss);
    debug_oss << "\n\n"
              << DUMP_TO_STREAM(is_inefficient_candiate,
                                is_excessive_blockage_avoidance_lc);
    if (is_inefficient_candiate) {
      LOG(INFO) << kLogHeader << __func__ << "\n"
                << candidate->unique_id << debug_oss.str();
      debug_oss << "\nInefficient LC; "
                << DUMP_TO_STREAM(lc_completion_rate,
                                  lc_abort_ego_speed_threshold);
      // Prefer to abort the lane change at a low lane change completion rate
      // or at a low speed.
      if (lc_completion_rate < math::constants::kEpsilon ||
          ego_speed < lc_abort_ego_speed_threshold) {
        debug_oss << "\nEgo not encroached or slow enough;\n";
        candidate->routing_debug_str += debug_oss.str();
        candidate->normalized_routing_cost +=
            is_high_reward_lane_change
                ? kInefficiencyOrExcessiveCostForCrawl
                : kInefficiencyOrExcessiveCostForNormalLc;
        has_inefficient_lane_change = true;
        continue;
      }
      debug_oss << "\nEgo has encroached target lane and not slow enough;\n";
    }

    if (is_excessive_blockage_avoidance_lc) {
      // A higher ego speed brings a lower lc completion rate threshold.
      static constexpr double kExcessiveLcAbortMaxLcCompletionRate = 0.2;
      const double lc_completion_rate_threshold = math::GetLinearInterpolatedY(
          constants::kDefaultEgoNearStaticSpeedInMps,
          kLcAbortEgoSpeedThreshInMps, kExcessiveLcAbortMaxLcCompletionRate,
          0.0, ego_speed);
      const double lc_completion_rate =
          candidate->trajectory_info->GetCrossLaneInfo()
              .cross_lane_meta.cross_lane_completion_rate();
      debug_oss << "\nExcessive LC; "
                << DUMP_TO_STREAM(ego_speed, lc_completion_rate_threshold,
                                  lc_completion_rate);
      // Prefer to abort the excessive lane change at a low speed and a low lc
      // completion rate.
      if (ego_speed < kLcAbortEgoSpeedThreshInMps &&
          lc_completion_rate < lc_completion_rate_threshold) {
        debug_oss << "\nEgo not encroached much and slow enough;\n";
        candidate->routing_debug_str += debug_oss.str();
        candidate->normalized_routing_cost +=
            is_high_reward_lane_change
                ? kInefficiencyOrExcessiveCostForCrawl
                : kInefficiencyOrExcessiveCostForNormalLc;
        continue;
      }
      debug_oss
          << "\nEgo has encroached target lane much or not slow enough;\n";
    }
    candidate->routing_debug_str += debug_oss.str();
  }

  // Update blocking traffic seed.
  if (FLAGS_planning_enable_near_lane_change_end_blocking_traffic_detector) {
    mutable_seed_.mutable_selection_seed()
        ->mutable_cross_lane_selection_seed()
        ->set_is_cl_blocking_traffic(
            blocking_traffic_close_to_lane_change_end_);
  } else {
    mutable_seed_.mutable_selection_seed()
        ->mutable_cross_lane_selection_seed()
        ->set_is_cl_blocking_traffic(false);
  }

  // Post processing for LK candidates. Add an extra routing cost for LK
  // candidates if there are also parked cars / construction zones yielded to.
  if (!has_inefficient_lane_change) {
    return;
  }

  for (TrajectoryMetaData* candidate : candidates) {
    if (candidate->IsCrossLane()) {
      continue;
    }

    std::ostringstream debug_oss;
    if (IsInefficientCandidate(*candidate, debug_oss)) {
      candidate->routing_debug_str += debug_oss.str();
      candidate->normalized_routing_cost +=
          is_high_reward_lane_change ? kInefficiencyOrExcessiveCostForCrawl
                                     : kInefficiencyOrExcessiveCostForNormalLc;
    }
  }
}

// Returns true if there is a yield decision for the parked car or construction
// zone ahead.
bool LaneChangeScenarioSelector::IsInefficientCandidate(
    const TrajectoryMetaData& candidate, std::ostringstream& debug_oss) {
  return IsInefficientCandidateDueToPotentialOrTempParkedCars(candidate,
                                                              debug_oss) ||
         IsInefficientCandidateDueToConstructionZones(candidate, debug_oss);
}

// Returns true if there is a yield decision for the parked car ahead.
bool LaneChangeScenarioSelector::
    IsInefficientCandidateDueToPotentialOrTempParkedCars(
        const TrajectoryMetaData& candidate, std::ostringstream& debug_oss) {
  const CrossLaneInfoManager& cross_lane_info_manager =
      candidate.trajectory_info->cross_lane_info_manager();
  const CrossLaneInfo& cross_lane_info =
      cross_lane_info_manager.GetCrossLaneInfo();
  const CrossLaneRegionInfo* interested_region_info_ptr =
      GetRegionInfoPtrByType(candidate.IsCrossLane()
                                 ? CrossLaneRegionType::TARGET
                                 : CrossLaneRegionType::SOURCE,
                             cross_lane_info.region_infos);

  if (interested_region_info_ptr == nullptr) {
    return false;
  }

  const CrossLaneObjectInfoList* interested_region_object_info_list =
      &(DCHECK_NOTNULL(interested_region_info_ptr)->object_info_list);
  if (interested_region_object_info_list == nullptr) {
    return false;
  }

  debug_oss << "\n\nPotential blockage info:";

  constexpr double kMinRemainingLcDistanceToConsiderPotentialBlockageInMeter =
      100.0;
  const CrossLaneMetaData& cross_lane_meta = cross_lane_info.cross_lane_meta;
  DCHECK(!cross_lane_meta.is_consecutive_cross_lane_in_same_direction());
  int64_t cross_lane_count = cross_lane_meta.cross_lane_count();
  // Hack for the situation that routing gives excessive lane changes when
  // triggering the early lane change or progress ELC.
  // TODO(lane change): Use the accurate lane change count.
  const planner::pb::LaneChangeSignalSourceType&
      lane_change_signal_source_type =
          cross_lane_meta.cross_lane_signal_source_type()
              .lane_change_signal_source_type();
  if (lane_change_signal_source_type ==
          planner::pb::LaneChangeSignalSourceType::
              kLaneChangeSignalFromEarlyLaneChange ||
      lane_change_signal_source_type ==
          planner::pb::LaneChangeSignalSourceType::
              kLaneChangeSignalFromProgressELC) {
    constexpr int64_t kMinConsecutiveLaneChangeCount = 1;
    cross_lane_count =
        std::max(kMinConsecutiveLaneChangeCount, cross_lane_count - 2);
  }

  const double remaining_distance_per_lc =
      cross_lane_meta.remaining_distance_for_cross_lane() / cross_lane_count;
  const bool has_enough_lane_change_span =
      remaining_distance_per_lc >
      kMinRemainingLcDistanceToConsiderPotentialBlockageInMeter;
  const CrossLaneObjectInfoList* interested_neighbor_region_info_ptr =
      cross_lane_info_manager.GetObjectInfoListPtrByRegionType(
          candidate.IsCrossLane() ? CrossLaneRegionType::TARGET_NEIGHBOR
                                  : CrossLaneRegionType::SOURCE_NEIGHBOR);
  const std::optional<double> distance_to_last_lc_chance_point =
      cross_lane_meta.has_dist_to_cross_lane_last_chance_pt()
          ? std::make_optional<double>(
                cross_lane_meta.dist_to_cross_lane_last_chance_pt())
          : std::nullopt;
  debug_oss << "\n"
            << DUMP_TO_STREAM(has_enough_lane_change_span,
                              remaining_distance_per_lc, cross_lane_count)
            << "; queue info, static: "
            << interested_region_info_ptr->has_static_queue
            << ", dynamic: " << interested_region_info_ptr->has_dynamic_queue
            << ";";

  auto is_interested_blocking_object =
      [has_enough_lane_change_span, remaining_distance_per_lc,
       &distance_to_last_lc_chance_point, &interested_region_info_ptr,
       &interested_neighbor_region_info_ptr,
       &debug_oss](const CrossLaneObjectInfo* object_info) -> bool {
    // Ignore non-stationary agents.
    if (!object_info->planner_object().is_primary_stationary()) {
      return false;
    }

    // Don't block or abort LC for agents beyond last lane change point.
    if (distance_to_last_lc_chance_point.has_value() &&
        object_info->object_ego_dist -
                0.5 * object_info->planner_object().length() >
            distance_to_last_lc_chance_point) {
      debug_oss << "\n"
                << object_info->id << ": beyond last lane change point["
                << distance_to_last_lc_chance_point.value() << "m];";
      return false;
    }

    if (object_info->is_parked_car) {
      debug_oss << "\n" << object_info->id << ": parked car;";
      return true;
    }

    // Don't consider the object when the lane change remaining distance is
    // short to avoid unexpected blocking lane change or abort due to FP signal.
    if (!has_enough_lane_change_span) {
      return false;
    }

    // Exclude queue on target region to avoid FP blocking lane change. We only
    // exclude static queue here since we may have some FP dynamic queue signal.
    if (interested_region_info_ptr->has_static_queue) {
      return false;
    }

    // Only deal with stationary objects on the leftmost or rightmost lane
    // to avoid FN queue signal.
    bool is_on_left_or_rightmost_lane =
        object_info->is_in_rightmost_lane || object_info->is_in_leftmost_lane;
    if (!is_on_left_or_rightmost_lane &&
        interested_neighbor_region_info_ptr != nullptr) {
      const CrossLaneObjectInfo* target_neighbor_object_info =
          interested_neighbor_region_info_ptr->Find(object_info->id);
      is_on_left_or_rightmost_lane =
          target_neighbor_object_info != nullptr &&
          (target_neighbor_object_info->is_in_rightmost_lane ||
           target_neighbor_object_info->is_in_leftmost_lane);
    }

    if (!is_on_left_or_rightmost_lane) {
      return false;
    }

    // Handle high parked car score and temporarily parked car.
    if (object_info->is_likely_parked_car_from_score ||
        object_info->is_temp_parked_car) {
      debug_oss << "\n"
                << object_info->id << ": maybe parked car["
                << object_info->is_likely_parked_car_from_score
                << "], temp parked car[" << object_info->is_temp_parked_car
                << "];\n"
                << "is_on_left_or_rightmost_lane:"
                << is_on_left_or_rightmost_lane << ";";
      return true;
    }

    // Regard the lane change as inefficient when the static agent is far away
    // from the ego and it is on the rightmost lane. Don't block the lane change
    // when the static object is on the leftmost lane for now to avoid FP.
    constexpr double kMinDistanceForStaticObjectFarAwayInMeter = 50.0;
    if (object_info->object_ego_dist >
            kMinDistanceForStaticObjectFarAwayInMeter &&
        !object_info->is_in_queue && object_info->is_in_rightmost_lane) {
      debug_oss << "\n"
                << object_info->id << ": " << object_info->object_ego_dist
                << "m; not in queue;\n";
      constexpr double kRemainingLcDistToMonitorStaticObjectNotInQueueInMeter =
          200.0;
      if (remaining_distance_per_lc <
          kRemainingLcDistToMonitorStaticObjectNotInQueueInMeter) {
        const std::string payload =
            "Static object " + std::to_string(object_info->id) +
            "; remaining lc dist:" + std::to_string(remaining_distance_per_lc);
        rt_event::PostRtEvent<
            rt_event::planner::PlannerLaneChangeStaticObjectNotInQueueDetected>(
            payload);
      }
      return true;
    }

    return false;
  };

  const tbb::concurrent_unordered_map<ObjectId,
                                      std::vector<PredictedTrajectoryWrapper>>&
      object_prediction_map = world_model_.object_prediction_map();

  for (const int64_t object_id : interested_region_object_info_list->ids()) {
    const CrossLaneObjectInfo* object_info =
        interested_region_object_info_list->Find(object_id);
    if (object_info == nullptr) {
      continue;
    }

    if (!is_interested_blocking_object(object_info)) {
      continue;
    }

    // Check decision of the potentially blocking object.
    const auto& object_prediction_iter = object_prediction_map.find(object_id);
    DCHECK(object_prediction_iter != object_prediction_map.end());
    if (HasYieldDecision(candidate.speed_result, object_id,
                         object_prediction_iter->second,
                         /*only_check_primary_decision=*/true)) {
      debug_oss << " Yield decision;";
      const std::string payload =
          "Object " + std::to_string(object_id) +
          ": potential parked car. is_parked_car=" +
          std::to_string(object_info->is_parked_car) + ", is_temp_parked_car=" +
          std::to_string(object_info->is_temp_parked_car) +
          ", is_likely_parked_car_from_score=" +
          std::to_string(object_info->is_likely_parked_car_from_score);
      rt_event::PostRtEvent<
          rt_event::planner::PlannerLaneChangePotentialParkedCarDetected>(
          payload);
      return true;
    }

    // Check if the potentially blocking object is ignored in speed due to that
    // it is out of path range. We also want to abort and delay the lane change
    // behind such staic object far ahead.
    auto is_ignored = [](const speed::SpeedResult& speed_result,
                         const int64_t object_id) -> bool {
      // Return true when there is no constraint for the object or all
      // constraints of the object are made ignore decision.
      DCHECK_EQ(speed_result.constraints().size(),
                speed_result.decisions().size());
      for (size_t idx = 0; idx < speed_result.constraints().size(); ++idx) {
        const speed::Constraint& constraint = speed_result.constraints()[idx];
        if (constraint.obj_id == object_id &&
            speed_result.decisions()[idx] != speed::pb::SpeedDecision::IGNORE) {
          return false;
        }
      }
      return true;
    };

    const double path_length =
        candidate.trajectory_info->unextended_path_for_overlap()
            .GetTotalArcLength();
    if (object_info->object_ego_dist > path_length &&
        is_ignored(candidate.speed_result, object_id)) {
      debug_oss << "Ignore decision due to far ahead; path_length: "
                << path_length << "m;";
      return true;
    }

    debug_oss << "No yield or ignore decision;";
  }
  return false;
}

void PopulateOverallCosts(
    const std::unordered_map<pb::CostType, CostComputerMetadata>& sub_costs_map,
    std::vector<double>& overall_costs) {
  const size_t num_trajectories = overall_costs.size();
  for (const auto& [cost_type, cost_metadata] : sub_costs_map) {
    DCHECK_EQ(num_trajectories, cost_metadata.trajectory_costs.size());
    for (size_t i = 0; i < num_trajectories; ++i) {
      overall_costs[i] +=
          cost_metadata.trajectory_costs[i] * cost_metadata.cost_weight;
    }
  }
}

bool LaneChangeScenarioSelector::ShouldRecomputeOverallCosts(
    const std::vector<TrajectoryMetaData*>& candidates,
    const std::unordered_map<pb::CostType, CostComputerMetadata>*
        sub_costs_map_ptr,
    std::unordered_set<CostRecomputationType>& cost_recomputation_types) const {
  cost_recomputation_types.clear();

  std::ostringstream debug_oss;
  // Recompute when there is risky CL candidate.
  const bool has_risky_lane_change = std::any_of(
      candidates.begin(), candidates.end(),
      [](const TrajectoryMetaData* candidate) {
        return candidate->IsCrossLane() && candidate->is_risky_lane_change;
      });
  if (has_risky_lane_change) {
    cost_recomputation_types.insert(CostRecomputationType::IMPLICIT_RISK);
  }

  // TODO(pengfei, elliot): Unify the cost recomputation trigger logic for high
  // risk and seperate the reroute logic.
  // Recompute when consecutive high risk on CL is detected.
  detected_lc_high_risk_ =
      DetectLaneChangeHighRisk(candidates, sub_costs_map_ptr, debug_oss);
  if (detected_lc_high_risk_) {
    rt_event::PostRtEvent<
        rt_event::planner::PlannerLaneChangeHighRiskDetected>();
  }

  const bool detected_lc_high_risk =
      FLAGS_planning_enable_lane_change_high_risk_detector
          ? detected_lc_high_risk_
          : false;
  if (detected_lc_high_risk) {
    cost_recomputation_types.insert(CostRecomputationType::EXPLICIT_RISK);
  }

  // Recompute overall costs when CL candidates in the current frame are all
  // risky and the risky CL has also been selected consecutively for several
  // cycles.
  const bool recompute_for_high_risk =
      detected_lc_high_risk ? false
                            : ShouldRecomputeOverallCostsByRisk(candidates);
  if (recompute_for_high_risk) {
    rt_event::PostRtEvent<rt_event::planner::PlannerRiskyLaneChange>();
    cost_recomputation_types.insert(CostRecomputationType::EXPLICIT_RISK);
  }

  // Recompute when all CL candidates have FS/EB speed profile.
  bool has_success_cl = false;
  for (const TrajectoryMetaData* candidate : candidates) {
    if (!candidate->IsCrossLane()) {
      continue;
    }

    if (!candidate->is_risky_lane_change_due_to_speed_failure) {
      has_success_cl = true;
      break;
    }
  }

  const bool recompute_for_speed_failure = !has_success_cl;
  if (recompute_for_speed_failure) {
    cost_recomputation_types.insert(
        CostRecomputationType::SPEED_SEARCH_FAILURE);
  }

  LOG(INFO) << kLogHeader << __func__ << ":\n"
            << debug_oss.str() << "\n"
            << DUMP_TO_STREAM(has_risky_lane_change, detected_lc_high_risk,
                              recompute_for_speed_failure,
                              recompute_for_high_risk);

  return cost_recomputation_types.size() > 0;
}

void LaneChangeScenarioSelector::CostArbitrator(
    const std::vector<TrajectoryMetaData*>& candidates,
    std::unordered_map<pb::CostType, CostComputerMetadata>* sub_costs_map_ptr)
    const {
  // Adjust lateral discomfort weight for low speed lane change.
  constexpr double kMaxSpeedToRelaxLateralDiscomfortCostWeightInMps =
      math::KmphToMps(10.0);
  constexpr double kMinSpeedToRelaxLateralDiscomfortCostWeightInMps =
      math::KmphToMps(6.0);
  const double init_lateral_discomfort_cost_weight =
      (*sub_costs_map_ptr)[pb::CostType::LATERAL_DISCOMFORT].cost_weight;
  (*sub_costs_map_ptr)[pb::CostType::LATERAL_DISCOMFORT].cost_weight =
      math::GetLinearInterpolatedY(
          kMinSpeedToRelaxLateralDiscomfortCostWeightInMps,
          kMaxSpeedToRelaxLateralDiscomfortCostWeightInMps,
          init_lateral_discomfort_cost_weight * 0.5,
          init_lateral_discomfort_cost_weight,
          world_model_.robot_state().plan_init_state_snapshot().speed());

  // Adjust routing costs by collision risk on CL and LK in scenario of
  // blockage.
  AdjustRoutingCostByRisk(candidates, sub_costs_map_ptr);

  // Arbitrate the lane change risk.
  if (ArbitrateLaneChangeRisk(candidates, sub_costs_map_ptr)) {
    // Decide whether to apply an extra reward for the lane change according to
    // the new risk.
    ArbitrateLaneChangeReward(candidates, sub_costs_map_ptr);
  }

  // Handle costs recomputation when lane change is risky.
  if (seed_.selected_behavior_type() != planner::pb::BehaviorType::CROSS_LANE) {
    return;
  }

  std::unordered_set<CostRecomputationType> cost_recomputation_types;
  should_recompute_overall_costs_ = ShouldRecomputeOverallCosts(
      candidates, sub_costs_map_ptr, cost_recomputation_types);

  if (!should_recompute_overall_costs()) {
    return;
  }

  // Recompute costs when there is risky lane change homotopy. Only use the
  // non-safety related costs here in case that we couldn't abort lane change
  // timely due to other factors.
  for (auto& [cost_type, cost_metadata] : *sub_costs_map_ptr) {
    if (cost_type == pb::CostType::LONGITUDINAL_DISCOMFORT ||
        cost_type == pb::CostType::LATERAL_DISCOMFORT) {
      continue;
    }

    // Add routing preference when the cost recomputation is only triggered by
    // implicit risk.
    if (cost_type == pb::CostType::ROUTING &&
        std::all_of(cost_recomputation_types.begin(),
                    cost_recomputation_types.end(),
                    [](const CostRecomputationType& type) {
                      return type == CostRecomputationType::IMPLICIT_RISK;
                    })) {
      cost_metadata.cost_weight = 0.2;
      continue;
    }

    if (cost_type != pb::CostType::COLLISION_RISK) {
      cost_metadata.cost_weight = 0.0;
      continue;
    }

    // Handle collision risk.
    // Update the final collision risk to the pose collision risk.
    DCHECK_EQ(cost_metadata.trajectory_costs.size(), candidates.size());
    for (size_t i = 0; i < candidates.size(); ++i) {
      TrajectoryMetaData* candidate = candidates[i];
      if (!candidate->IsCrossLane()) {
        continue;
      }
      candidate->collision_risk = candidate->pose_collision_risk;
      cost_metadata.trajectory_costs[i] = candidate->collision_risk;
    }
  }

  for (TrajectoryMetaData* candidate : candidates) {
    std::ostringstream debug_oss;
    debug_oss << "\n\nThere is risky lane change trajectory, use the "
                 "recomputed cost.\nis_risky_lane_change = "
              << candidate->is_risky_lane_change
              << ", detected_lc_high_risk = " << detected_lc_high_risk_
              << ", recompute_for_speed_failure = "
              << candidate->is_risky_lane_change_due_to_speed_failure
              << ", is_risky_cross_lane = " << candidate->is_risky_cross_lane
              << ", collision_risk = " << candidate->collision_risk
              << ", pose_collision_risk = " << candidate->pose_collision_risk
              << ", lc_risk = "
              << candidate->lane_change_selection_info.lc_risk_info.lc_risk;
    candidate->lane_change_selection_info.lc_risk_info.debug_str +=
        debug_oss.str();
  }
}

bool LaneChangeScenarioSelector::ArbitrateLaneChangeRisk(
    const std::vector<TrajectoryMetaData*>& candidates,
    std::unordered_map<pb::CostType, CostComputerMetadata>* sub_costs_map_ptr) {
  // Arbitrate the lane change safe yield deceleration risk by considering the
  // brake and accel on LK.
  const bool should_adjust_safe_yield_deceleration_risk = std::any_of(
      candidates.begin(), candidates.end(),
      [](const TrajectoryMetaData* candidate) {
        return candidate->IsCrossLane() &&
               candidate->lane_change_selection_info.lc_risk_info
                       .GetLaneChangeRiskByType(
                           LaneChangeRiskType::SAFE_YIELD_DECELERATION) >
                   math::constants::kEpsilon;
      });
  // Arbitrate the lane change risky lead risk by considering the brake
  // diff on LK & CL.
  const bool should_adjust_risky_lead_risk =
      std::any_of(candidates.begin(), candidates.end(),
                  [](const TrajectoryMetaData* candidate) {
                    return candidate->IsCrossLane() &&
                           candidate->lane_change_selection_info.lc_risk_info
                                   .GetLaneChangeRiskByType(
                                       LaneChangeRiskType::RISKY_LEAD) >
                               math::constants::kEpsilon;
                  });
  if (!should_adjust_safe_yield_deceleration_risk &&
      !should_adjust_risky_lead_risk) {
    return false;
  }

  // Constants for update RISKY_LEAD in lc_risk.
  constexpr double kMinLKBrakeRatioForAdjustment = 0.2;
  constexpr double kMinScaledRatioForRiskyLeadRisk = 0.2;
  constexpr double kMinLKBrakeDiffForAdjustmentInMpss = 0.5;
  constexpr double kMaxLKBrakeDiffForAdjustmentInMpss = 1.0;
  // Compute the min brake and max accel on LK.
  double min_brake_on_lk = -std::numeric_limits<double>::infinity();
  double max_accel_on_lk = -std::numeric_limits<double>::infinity();
  // Compute the max brake on CL & LK (w/o ML_PATH).
  double max_brake_on_non_ml_lk = -std::numeric_limits<double>::infinity();
  double max_brake_on_non_ml_cl = -std::numeric_limits<double>::infinity();
  for (const TrajectoryMetaData* candidate : candidates) {
    // TODO(ryanliyiyang): current ML trajectory may vanish after lc abort,
    // leaving super harsh-brake LK candidates only. (scenario id: 7440802)
    // Remove when it can be stably latched.
    if (candidate->trajectory_type() != planner::pb::TrajectoryType::ML_PATH) {
      math::UpdateMax(GetMinOrMaxAccelerationInTimeRange(candidate->trajectory),
                      candidate->IsCrossLane() ? max_brake_on_non_ml_cl
                                               : max_brake_on_non_ml_lk);
    }

    if (candidate->IsCrossLane()) {
      continue;
    }
    math::UpdateMax(GetMinOrMaxAccelerationInTimeRange(
                        candidate->trajectory, kBrakeCheckDurationInMsec,
                        /*update_min=*/true),
                    min_brake_on_lk);
    math::UpdateMax(GetMinOrMaxAccelerationInTimeRange(
                        candidate->trajectory, kBrakeCheckDurationInMsec,
                        /*update_min=*/false),
                    max_accel_on_lk);
  }
  min_brake_on_lk =
      std::isinf(min_brake_on_lk) ? 0.0 : std::min(0.0, min_brake_on_lk);
  max_accel_on_lk = std::isinf(max_accel_on_lk) ? 0.0 : max_accel_on_lk;
  max_brake_on_non_ml_cl = -std::min(max_brake_on_non_ml_cl, 0.0);
  max_brake_on_non_ml_lk = -std::min(max_brake_on_non_ml_lk, 0.0);

  // No CL/LK or small brake diff between CL and LK.
  const double max_brake_diff =
      std::max(max_brake_on_non_ml_cl * kMinLKBrakeRatioForAdjustment,
               kMinLKBrakeDiffForAdjustmentInMpss);
  const double brake_diff = max_brake_on_non_ml_lk - max_brake_on_non_ml_cl;
  const bool is_lk_better_than_cl =
      std::isinf(max_brake_on_non_ml_lk) || brake_diff < -max_brake_diff;

  // Arbitrate the safe yield deceleration risk on CL candidate by the brake and
  // accel diff between lane change and lane keep.
  DCHECK(sub_costs_map_ptr != nullptr);
  CostComputerMetadata& risk_cost_metadata =
      (*sub_costs_map_ptr)[pb::CostType::COLLISION_RISK];
  DCHECK_EQ(risk_cost_metadata.trajectory_costs.size(), candidates.size());
  for (size_t i = 0; i < candidates.size(); ++i) {
    TrajectoryMetaData* candidate = candidates[i];
    if (!candidate->IsCrossLane()) {
      continue;
    }

    std::ostringstream debug_oss;
    debug_oss << "\n\n---------------\n";
    // When LK is not better than CL, scale down the risky lead risk
    // considering the brake diff.
    LaneChangeRiskInfo& lane_change_risk_info =
        candidate->lane_change_selection_info.lc_risk_info;
    const double risky_lead_risk =
        lane_change_risk_info.GetLaneChangeRiskByType(
            planner::LaneChangeRiskType::RISKY_LEAD);
    // TODO(ryanliyiyang): refactor the adjustment of risky lead risk into a
    // separate function.
    if (!is_lk_better_than_cl && risky_lead_risk > math::constants::kEpsilon) {
      lane_change_risk_info.UpdateLaneChangeRisk(
          planner::LaneChangeRiskType::RISKY_LEAD,
          risky_lead_risk * math::GetLinearInterpolatedY(
                                -kMinLKBrakeDiffForAdjustmentInMpss,
                                kMaxLKBrakeDiffForAdjustmentInMpss, 1.0,
                                kMinScaledRatioForRiskyLeadRisk, brake_diff));
      risk_cost_metadata.trajectory_costs[i] = std::max(
          candidate->pose_collision_risk, lane_change_risk_info.lc_risk);
      candidate->collision_risk = risk_cost_metadata.trajectory_costs[i];
      debug_oss << "Overwrite collision risk due to lk brake not "
                   "better than cl.\n"
                   "lk_max_brake = "
                << max_brake_on_non_ml_lk
                << ", cl_max_brake = " << max_brake_on_non_ml_cl
                << ", brake_tolerance = " << max_brake_diff
                << ", updated_collision_risk = " << candidate->collision_risk
                << "\n\n";
      rt_event::PostRtEvent<rt_event::planner::PlannerLaneChangeLeadRiskOn>(
          absl::StrFormat("risky_lead_risk: %.2f",
                          lane_change_risk_info.GetLaneChangeRiskByType(
                              planner::LaneChangeRiskType::RISKY_LEAD)));
    }

    const double original_risk =
        candidate->lane_change_selection_info.lc_risk_info
            .GetLaneChangeRiskByType(
                LaneChangeRiskType::SAFE_YIELD_DECELERATION);
    if (original_risk < math::constants::kEpsilon) {
      continue;
    }

    debug_oss << "Safe yield deceleration risk post processing:\n";
    constexpr double kAccelDiffToRelaxLcSafeYieldDecelRiskInMpss = -0.5;
    const double lc_brake =
        std::min(0.0, GetMinOrMaxAccelerationInTimeRange(
                          candidate->trajectory, kBrakeCheckDurationInMsec));
    const double lc_accel = GetMinOrMaxAccelerationInTimeRange(
        candidate->trajectory, kBrakeCheckDurationInMsec,
        /*update_min=*/false);
    const double lc_lk_brake_diff = lc_brake - min_brake_on_lk;
    const double lc_lk_accel_diff = lc_accel - max_accel_on_lk;
    double final_risk = original_risk;
    if (lc_lk_brake_diff > math::constants::kEpsilon) {
      final_risk = 0.0;
      debug_oss << "LC brakes less than LK, no risk;\n";
    } else if (lc_lk_brake_diff < kAccelDiffToRelaxLcSafeYieldDecelRiskInMpss ||
               lc_lk_accel_diff < kAccelDiffToRelaxLcSafeYieldDecelRiskInMpss) {
      final_risk = original_risk;
      debug_oss << "LC brakes much harder than LK or accel much less than LK, "
                   "keep the original risk;\n";
    } else {
      final_risk = math::GetLinearInterpolatedY(
          0.0, kAccelDiffToRelaxLcSafeYieldDecelRiskInMpss, 0.0, original_risk,
          lc_lk_brake_diff);
      debug_oss << "Similar brake and accel on LC and LK, scale the risk;\n";
    }

    // Update the final safe yield deceleration risk and the final lane change
    // risk.
    candidate->lane_change_selection_info.lc_risk_info.UpdateLaneChangeRisk(
        LaneChangeRiskType::SAFE_YIELD_DECELERATION, final_risk);
    // Update the final collision risk.
    candidate->collision_risk =
        std::max(candidate->pose_collision_risk,
                 candidate->lane_change_selection_info.lc_risk_info.lc_risk);
    risk_cost_metadata.trajectory_costs[i] = candidate->collision_risk;
    debug_oss << DUMP_TO_STREAM(original_risk, final_risk,
                                candidate->collision_risk,
                                candidate->pose_collision_risk)
              << "\n"
              << DUMP_TO_STREAM(lc_lk_brake_diff, lc_brake, min_brake_on_lk,
                                lc_lk_accel_diff, lc_accel, max_accel_on_lk);
    candidate->lane_change_selection_info.lc_risk_info.debug_str +=
        debug_oss.str();
  }

  return true;
}

std::vector<double> LaneChangeScenarioSelector::ComputeLcCrawlRewardCost(
    const std::vector<TrajectoryMetaData*>& candidates) {
  std::vector<double> trajectory_costs(candidates.size(), 0.0);

  const auto previous_maneuver_seed =
      Seed::Access<token::DecoupledManeuverSeed>::GetMsg(SpeedLastFrame());

  // Get the minimum risk in safe LK candidates.
  double min_risk_in_lk = std::numeric_limits<double>::infinity();
  for (const TrajectoryMetaData* candidate : candidates) {
    if (candidate->IsCrossLane()) {
      continue;
    }

    // Skip the LK if it violates the dynamic limits.
    if (!candidate->is_safe) {
      continue;
    }

    // Skip the LK if its speed planning fails.
    if (candidate->IsFullStopFromSpeedConflictResolver() ||
        candidate->IsExtraFullStopFromSpeedConflictResolver() ||
        candidate->IsEmergencyBrake()) {
      continue;
    }

    math::UpdateMin(candidate->pose_collision_risk, min_risk_in_lk);
  }

  // Only give reward when CL risk is not much higher than LK.
  auto is_lc_safe_to_give_reward =
      [&previous_maneuver_seed,
       min_risk_in_lk](const TrajectoryMetaData& candidate) -> bool {
    if (!candidate.trajectory_info->is_lane_change_crawl() ||
        previous_maneuver_seed->selected_lane_change_state() !=
            planner::pb::LaneChangeState::LANE_CHANGE_STATE_ABORT) {
      return true;
    }

    constexpr double kRiskDiffTolerance = 1.5;
    const double risk_diff =
        std::isfinite(min_risk_in_lk)
            ? (candidate.pose_collision_risk - min_risk_in_lk)
            : 0.0;
    return risk_diff < kRiskDiffTolerance;
  };

  for (size_t i = 0; i < candidates.size(); ++i) {
    const TrajectoryMetaData* candidate = candidates[i];
    if (candidate->IsCrossLane() &&
        ShouldGiveRewardToLaneChange(*candidate->trajectory_info) &&
        !LaneChangeScenarioSelector::should_recompute_overall_costs() &&
        candidate->collision_risk <
            std::abs(
                LaneChangeScenarioSelector::kLaneChangeCrawlRewardCostWeight *
                LaneChangeScenarioSelector::kLaneChangeCostRangeMax) &&
        is_lc_safe_to_give_reward(*candidate)) {
      trajectory_costs[i] = LaneChangeScenarioSelector::kLaneChangeCostRangeMax;
    }
  }
  return trajectory_costs;
}

void LaneChangeScenarioSelector::ArbitrateLaneChangeReward(
    const std::vector<TrajectoryMetaData*>& candidates,
    std::unordered_map<pb::CostType, CostComputerMetadata>* sub_costs_map_ptr) {
  DCHECK(sub_costs_map_ptr != nullptr);
  CostComputerMetadata& lc_reward_metadata =
      (*sub_costs_map_ptr)[pb::CostType::LC_CRAWL_REWARD];
  DCHECK_EQ(lc_reward_metadata.trajectory_costs.size(), candidates.size());
  lc_reward_metadata.trajectory_costs = ComputeLcCrawlRewardCost(candidates);
}

std::vector<double> LaneChangeScenarioSelector::ComputeLcRaAbortCost(
    const std::vector<TrajectoryMetaData*>& candidates) {
  std::vector<double> trajectory_costs(candidates.size(), 0.0);
  for (size_t i = 0; i < candidates.size(); ++i) {
    const TrajectoryMetaData* candidate = candidates[i];
    if (candidate->IsCrossLane() &&
        candidate->trajectory_info->lane_change_specific_info()
            .mlc_state.is_lc_disallowed()) {
      trajectory_costs[i] = LaneChangeScenarioSelector::kLaneChangeCostRangeMax;
    }
  }
  return trajectory_costs;
}

// Returns true if the construction zone is hard or soft blocking current
// candidate's lane sequence and there is a yield decision for it.
bool LaneChangeScenarioSelector::IsInefficientCandidateDueToConstructionZones(
    const TrajectoryMetaData& candidate, std::ostringstream& debug_oss) {
  // CZ of PLANNER_PASSABLE or WAYPOINT_PASSABLE type is non-blocking.
  auto is_interested_cz_type =
      [](const ConstructionZone* construction_zone_ptr) -> bool {
    return construction_zone_ptr != nullptr &&
           !common::IsMapChangeArea(
               *construction_zone_ptr,
               voy::MapChangeAreaAttribute::PLANNER_PASSABLE) &&
           !common::IsMapChangeArea(
               *construction_zone_ptr,
               voy::MapChangeAreaAttribute::WAYPOINT_PASSABLE);
  };

  const CrossLaneMetaData& cross_lane_meta =
      candidate.trajectory_info->GetCrossLaneInfo().cross_lane_meta;
  const std::optional<double> distance_to_last_lc_chance_point =
      cross_lane_meta.has_dist_to_cross_lane_last_chance_pt()
          ? std::make_optional(
                cross_lane_meta.dist_to_cross_lane_last_chance_pt())
          : std::nullopt;

  const double ego_ra_arclength =
      candidate.IsCrossLane()
          ? cross_lane_meta.ego_ra_arc_length_on_target_region()
          : cross_lane_meta.ego_ra_arc_length_on_source_region();

  const std::vector<const pnc_map::Lane*>& lane_sequence =
      candidate.IsCrossLane()
          ? candidate.trajectory_info->GetCrossLaneInfo()
                .lane_change_instance.target_lane_sequence()
          : candidate.trajectory_info->lane_sequence_iterator().lane_sequence();
  const std::map<int64_t, LaneBlockage>& lane_blockages =
      world_model_.lane_blockage_detector().lane_blockages();
  double accumulated_arclength = 0.0;
  for (const pnc_map::Lane* lane : lane_sequence) {
    const double lane_start_arclength = accumulated_arclength;
    accumulated_arclength += DCHECK_NOTNULL(lane)->length();
    if (accumulated_arclength < ego_ra_arclength) {
      continue;
    }

    const auto& iter = lane_blockages.find(DCHECK_NOTNULL(lane)->id());
    if (iter == lane_blockages.end()) {
      continue;
    }

    const LaneBlockage& lane_blockage = iter->second;
    // Check blocking status.
    const bool is_blocked_by_cz = std::any_of(
        lane_blockage.blocking_objects.begin(),
        lane_blockage.blocking_objects.end(),
        [ego_ra_arclength, lane_start_arclength,
         &distance_to_last_lc_chance_point, &is_interested_cz_type, &candidate,
         &debug_oss](const ObjectBlockingInfo& object) {
          if (!object.IsConstructionZone()) {
            return false;
          }

          if (!is_interested_cz_type(object.construction_zone())) {
            debug_oss << "\ncz " << object.object_id()
                      << ": map change area, PLANNER_PASSABLE or "
                         "WAYPOINT_PASSABLE, non-blocking;";
            return false;
          }

          // Don't block or abort LC for CZ beyond last lane change point.
          const double object_start_arclength =
              lane_start_arclength + object.blocking_range().start_pos;
          if (distance_to_last_lc_chance_point.has_value() &&
              object_start_arclength - ego_ra_arclength >
                  distance_to_last_lc_chance_point) {
            debug_oss << "\ncz " << object.object_id()
                      << ": beyond last lc point["
                      << distance_to_last_lc_chance_point.value() << "m];\n";
            return false;
          }

          if (!object.ShouldConsiderAsSoftBlocking() &&
              !object.ShouldConsiderAsHardBlocking()) {
            return false;
          }

          debug_oss << "\nblocking cz " << object.object_id() << ", ";
          if (HasYieldDecision(candidate.speed_result, object.object_id())) {
            debug_oss << "also yielded to;\n";
            return true;
          }

          debug_oss << "no yield decision;\n";
          return false;
        });
    if (is_blocked_by_cz) {
      return true;
    }
  }

  return false;
}

double LaneChangeScenarioSelector::ComputeBlockingTrafficCostBeyondLaneChangEnd(
    const TrajectoryMetaData& candidate, const double lc_completion_rate,
    std::ostringstream& debug_oss) {
  DCHECK(candidate.IsCrossLane());
  debug_oss << __func__ << "\n";
  // Compute the minimal blocking traffic score to apply extra cost w.r.t. the
  // lane change urgency score.
  constexpr double kBlockingTrafficScoreThreshForUrgentLc = 0.6;
  constexpr double kBlockingTrafficScoreThreshForNonUrgentLc = 0.3;
  const double min_blocking_traffic_score = math::GetLinearInterpolatedY(
      0.0, 1.0, kBlockingTrafficScoreThreshForNonUrgentLc,
      kBlockingTrafficScoreThreshForUrgentLc,
      candidate.trajectory_info->GetCrossLaneInfo().urgency_score);
  const double blocking_traffic_score =
      candidate.trajectory_info->GetCrossLaneInfo().blocking_traffic_score;
  if (blocking_traffic_score < min_blocking_traffic_score) {
    debug_oss << "blocking_traffic_score[" << blocking_traffic_score << "] < "
              << min_blocking_traffic_score
              << ", ignore blocking traffic cost;";
    return 0.0;
  }

  debug_oss << "blocking_traffic_score[" << blocking_traffic_score << "] > "
            << min_blocking_traffic_score << ";";

  // Compute the basic cost w.r.t. the blocking traffic score.
  const double max_blocking_traffic_cost =
      ShouldGiveRewardToLaneChange(*candidate.trajectory_info)
          ? kExtraBlockingTrafficCostForHighRewardLaneChange
          : kExtraBlockingTrafficCostForNormalLaneChange;
  double blocking_traffic_cost = math::GetLinearInterpolatedY(
      min_blocking_traffic_score, 1.0, 0.0, max_blocking_traffic_cost,
      blocking_traffic_score);
  debug_oss << "initial blocking_traffic_cost = " << blocking_traffic_cost
            << "; max_blocking_traffic_cost = " << max_blocking_traffic_cost
            << ";";

  // Scale the blocking traffic cost by trajectory progress. Don't abort lane
  // change when the lane change trajectory could achieve much progress.
  constexpr double kLcTrajProgressUpperBoundInMeter = 5.0;
  constexpr double kLcTrajProgressLowerBoundInMeter = 0.5;

  const double max_traj_progress_in_m = math::GetLinearInterpolatedY(
      0.0, 1.0, kLcTrajProgressUpperBoundInMeter, math::constants::kEpsilon,
      lc_completion_rate);
  const double min_traj_progress_in_m = math::GetLinearInterpolatedY(
      0.0, 1.0, kLcTrajProgressLowerBoundInMeter, 0.0, lc_completion_rate);

  blocking_traffic_cost = math::GetLinearInterpolatedY(
      min_traj_progress_in_m, max_traj_progress_in_m, blocking_traffic_cost,
      0.0, candidate.progress_in_m);
  debug_oss << "final blocking_traffic_cost = " << blocking_traffic_cost
            << "; \ntrajectory progress = " << candidate.progress_in_m
            << "m; max_traj_progress_in_m = " << max_traj_progress_in_m
            << ", min_traj_progress_in_m = " << min_traj_progress_in_m
            << ", lc_completion_rate = " << lc_completion_rate;
  return blocking_traffic_cost;
}

// TODO(elliotsong): Add UTs.
double LaneChangeScenarioSelector::ComputeBlockingTrafficCostNearLaneChangeEnd(
    const TrajectoryMetaData& candidate, const double lc_completion_rate,
    const double ego_speed, std::ostringstream& debug_oss) {
  DCHECK(candidate.IsCrossLane());
  debug_oss << "\n" << __func__ << "\n";
  const CrossLaneInfo& cross_lane_info =
      candidate.trajectory_info->GetCrossLaneInfo();
  if (cross_lane_info.cross_lane_meta.cross_lane_count() > 1) {
    debug_oss << "Ignore consecutive lane change.\n";
    return 0.0;
  }
  const std::optional<LaneChangeUrgencyInfo>& lc_end_urgency_info =
      GetMostUrgencyInfoByUrgencyType(
          cross_lane_info.urgency_infos,
          planner::pb::LaneChangeUrgencyType::VALID_LANE_CHANGE_END);
  const bool has_hard_lane_change_end =
      lc_end_urgency_info.has_value() &&
      lc_end_urgency_info->stop_line_type() ==
          planner::pb::LaneChangeStopLineType::HARD_STOP_LINE;
  if (!has_hard_lane_change_end) {
    debug_oss << "No hard lane change end.\n";
    return 0.0;
  }

  // Don't add extra blocking traffic cost if ego has large lc complete rate.
  constexpr double kMaxBlockingTrafficAbortLcCompletionRate = 0.2;
  constexpr double kMaxBlockingTrafficAbortEgoSpeedInMps = 2.0;
  const double lc_completion_rate_threshold = math::GetLinearInterpolatedY(
      kMaxBlockingTrafficAbortEgoSpeedInMps, 0.0, 0.0,
      kMaxBlockingTrafficAbortLcCompletionRate, ego_speed);
  debug_oss << DUMP_TO_STREAM(ego_speed, lc_completion_rate_threshold,
                              lc_completion_rate);
  if (lc_completion_rate > lc_completion_rate_threshold) {
    debug_oss << "Large lc completion rate.\n";
    return 0.0;
  }

  const std::optional<double> reroute_increasing_distance =
      FLAGS_planning_enable_use_can_reroute_flag_for_lc_blocking_traffic
          ? lane_selection::GetRerouteIncreasingDistToDest(
                cross_lane_info.preview_route_result)
          : lane_selection::GetRerouteIncreasingDistToDestFromSequencePreview(
                cross_lane_info.preview_route_result);
  if (!reroute_increasing_distance.has_value()) {
    debug_oss << "No reroute preview route result.";
    return 0.0;
  }
  constexpr double kMinRerouteIncreasingDistanceInMeter = 400.0;
  constexpr double kMaxRerouteIncreasingDistanceInMeter = 2000.0;
  const double reroute_increasing_distance_score = math::GetInterpolationRatio(
      kMaxRerouteIncreasingDistanceInMeter,
      kMinRerouteIncreasingDistanceInMeter, reroute_increasing_distance.value(),
      /*allow_extension=*/false);

  // Calculate the cross lane end time.
  std::optional<double> cross_lane_start_relative_time;
  std::optional<double> cross_lane_end_relative_time;
  bool can_fully_cross_lane = false;
  ComputeEgoCrossLaneTimeInterval(candidate, cross_lane_start_relative_time,
                                  cross_lane_end_relative_time,
                                  can_fully_cross_lane, debug_oss);
  if (can_fully_cross_lane) {
    debug_oss << "Candidate can reach to cross lane end.";
    return 0.0;
  }

  const double lc_difficulty_score =
      cross_lane_info.lane_change_difficulty_score;
  // Calculate blocking traffic score threshold.
  constexpr double kMinBlockingTrafficScoreThreshold = 0.8;
  constexpr double kMaxBlockingTrafficScoreThreshold = 1.0;
  const double blocking_traffic_score_threshold = math::LinearInterpolate(
      kMaxBlockingTrafficScoreThreshold, kMinBlockingTrafficScoreThreshold,
      std::min(lc_difficulty_score, reroute_increasing_distance_score));
  const double blocking_traffic_score = cross_lane_info.blocking_traffic_score;
  debug_oss << DUMP_TO_STREAM(
      lc_difficulty_score, reroute_increasing_distance.value(),
      reroute_increasing_distance_score, blocking_traffic_score_threshold,
      blocking_traffic_score);
  if (blocking_traffic_score >
      blocking_traffic_score_threshold - math::constants::kEpsilon) {
    rt_event::PostRtEvent<
        rt_event::planner::PlannerNearLaneChangeEndBlockingTrafficDetected>();
    blocking_traffic_close_to_lane_change_end_ = true;
    return kExtraBlockingTrafficCostForHighRewardLaneChange;
  }
  return 0.0;
}

// TODO(elliotsong): Add UTs.
double LaneChangeScenarioSelector::ComputeBlockingTrafficCost(
    const TrajectoryMetaData& candidate, const double lc_completion_rate,
    std::ostringstream& debug_oss) {
  if (!candidate.IsCrossLane()) {
    return 0.0;
  }

  debug_oss << __func__ << "\n";

  constexpr double kMinRemainingDistanceForLaneChangeInMeter = 50.0;
  constexpr double kLaneChangeDurationInSec = 6.0;
  const double remaining_distance_per_lc =
      candidate.trajectory_info->GetCrossLaneInfo()
          .cross_lane_meta.remaining_distance_for_cross_lane() /
      candidate.trajectory_info->GetCrossLaneInfo()
          .cross_lane_meta.cross_lane_count();
  const double ego_speed =
      world_model_.robot_state().plan_init_state_snapshot().speed();
  const double min_lane_change_distance =
      std::fmax(kMinRemainingDistanceForLaneChangeInMeter,
                ego_speed * kLaneChangeDurationInSec);
  if (remaining_distance_per_lc < min_lane_change_distance) {
    return FLAGS_planning_enable_near_lane_change_end_blocking_traffic_detector
               ? ComputeBlockingTrafficCostNearLaneChangeEnd(
                     candidate, lc_completion_rate, ego_speed, debug_oss)
               : 0.0;
  }

  return ComputeBlockingTrafficCostBeyondLaneChangEnd(
      candidate, lc_completion_rate, debug_oss);
}

void LaneChangeScenarioSelector::AdjustCostForLeftLcDuringImmediatePullOver(
    TrajectoryMetaData& candidate, std::ostringstream& debug_oss) {
  if (!world_model_
           .ShouldTriggerImmediateRightLaneChangeForImmediatePullover() ||
      !candidate.IsCrossLane() ||
      candidate.trajectory_info->GetCrossLaneInfo()
              .lane_change_instance.direction() !=
          planner::pb::LaneChangeMode::LEFT_LANE_CHANGE) {
    return;
  }

  candidate.normalized_routing_cost += kCostForLeftLcDuringImmediatePullOver;
  debug_oss << "\nLeft lc during immediate pull over.";
}

bool IsCutinToTargetRegionFromTargetNeighborRegion(
    const CrossLaneInfoManager& cross_lane_info_manager,
    const int64_t object_id, double& cutin_target_region_relative_time,
    double& cutin_target_region_ra_arc_length, std::ostringstream& debug_oss) {
  // Get target neighbor region infos.
  const CrossLaneObjectInfoList* target_neighbor_object_infos =
      cross_lane_info_manager.GetTargetNeighborRegionObjectInfoListPtr();
  if (target_neighbor_object_infos == nullptr) {
    return false;
  }

  const CrossLaneObjectInfo* target_neighbor_object_info =
      target_neighbor_object_infos->Find(object_id);
  if (target_neighbor_object_info == nullptr) {
    return false;
  }

  const std::vector<speed::pb::OverlapRegion>& target_neighbor_overlap_regions =
      target_neighbor_object_info->overlap_regions();
  if (target_neighbor_overlap_regions.empty()) {
    return false;
  }

  // Get target region infos.
  const CrossLaneObjectInfoList* target_region_object_infos =
      cross_lane_info_manager.GetTargetRegionObjectInfoListPtr();
  if (target_region_object_infos == nullptr) {
    return false;
  }

  const CrossLaneObjectInfo* target_object_info =
      target_region_object_infos->Find(object_id);
  if (target_object_info == nullptr) {
    return false;
  }

  const std::vector<speed::pb::OverlapRegion>& target_overlap_regions =
      target_object_info->overlap_regions();
  if (target_overlap_regions.empty()) {
    return false;
  }

  const double target_overlap_end_relative_time_in_sec =
      target_overlap_regions.front().end_padded_relative_time_in_sec();
  const double target_overlap_end_arc_length =
      speed::GetPaddedOverlapEnd(target_overlap_regions.front());

  const double target_neighbor_overlap_start_relative_time_in_sec =
      target_neighbor_overlap_regions.front()
          .start_padded_relative_time_in_sec();
  const double target_neighbor_overlap_start_arc_length =
      speed::GetPaddedOverlapStart(target_neighbor_overlap_regions.front());
  debug_oss << "target_overlap_end_relative_time="
            << target_overlap_end_relative_time_in_sec
            << "s, target_overlap_end_arc_length="
            << target_overlap_end_arc_length
            << "m; target_neighbor_overlap_start_relative_time="
            << target_neighbor_overlap_start_relative_time_in_sec
            << "s, target_neighbor_overlap_start_arc_length="
            << target_neighbor_overlap_start_arc_length << "m;";

  // Use the trajectory interval to identify the cut in time buffer.
  if (target_overlap_end_relative_time_in_sec >
          target_neighbor_overlap_start_relative_time_in_sec +
              constants::kTrajectoryIntervalInSec &&
      target_overlap_end_arc_length >
          target_neighbor_overlap_start_arc_length) {
    cutin_target_region_relative_time =
        target_overlap_regions.front().start_padded_relative_time_in_sec();
    cutin_target_region_ra_arc_length =
        speed::GetPaddedOverlapStart(target_overlap_regions.front());
    return true;
  }

  return false;
}

bool IsRiskyCutinAgentOnTargetNeighborRegion(
    const CrossLaneInfoManager& cross_lane_info_manager,
    const int64_t object_id, std::ostringstream& debug_oss) {
  double cutin_target_region_relative_time =
      std::numeric_limits<double>::infinity();
  double cutin_target_region_ra_arc_length =
      std::numeric_limits<double>::infinity();
  if (!IsCutinToTargetRegionFromTargetNeighborRegion(
          cross_lane_info_manager, object_id, cutin_target_region_relative_time,
          cutin_target_region_ra_arc_length, debug_oss)) {
    debug_oss << "not cutin to target region;";
    return false;
  }

  constexpr double kMaxTimeToConsiderCutinObjectInSec = 5.0;
  constexpr double kMaxDistanceToConsiderCutinObjectInMeter = 30.0;

  if (cutin_target_region_relative_time > kMaxTimeToConsiderCutinObjectInSec) {
    debug_oss << "cutin after " << kMaxTimeToConsiderCutinObjectInSec
              << "s, cutin_time = " << cutin_target_region_relative_time
              << "s;";
    return false;
  }

  if (cutin_target_region_ra_arc_length >
      kMaxDistanceToConsiderCutinObjectInMeter) {
    debug_oss << "cutin after " << kMaxDistanceToConsiderCutinObjectInMeter
              << "m, cutin_arc_length = " << cutin_target_region_ra_arc_length
              << "m;";
    return false;
  }

  debug_oss << "cutin to target region, cutin_arc_length = "
            << cutin_target_region_ra_arc_length
            << "m, cutin_time = " << cutin_target_region_relative_time << "s;";

  return true;
}

// Returns true if the agent on the target neighbor region is not totally behind
// ego and ego is not in the field of view of the agent.
bool IsRiskyAgentDueToFovOnTargetNeighborRegion(
    const RobotState& robot_state,
    const CrossLaneInfoManager& cross_lane_info_manager,
    const int64_t object_id,
    const std::unordered_map<int64_t, PlannerObject>& planner_object_map,
    std::ostringstream& debug_oss) {
  const auto iter = planner_object_map.find(object_id);
  if (iter == planner_object_map.end()) {
    return false;
  }

  const CrossLaneObjectInfo* object_info = DCHECK_NOTNULL(
      DCHECK_NOTNULL(
          cross_lane_info_manager.GetTargetNeighborRegionObjectInfoListPtr())
          ->Find(object_id));
  const double ego_rb_to_object_fb_dist =
      -object_info->object_ego_dist -
      robot_state.car_model_with_shape()
          .shape_measurement()
          .rear_bumper_to_rear_axle() -
      object_info->planner_object().length() * 0.5;
  debug_oss << "ego_rb_to_object_fb_dist = " << ego_rb_to_object_fb_dist
            << "; ";
  if (ego_rb_to_object_fb_dist > 0.0) {
    debug_oss << "ego is in front of agent;";
    return false;
  }

  debug_oss << "ego is not in front of agent;";
  if (!speed::CanAgentSeeEgo(iter->second.tracked_object(), robot_state)) {
    debug_oss << "agent can't see ego;";
    return true;
  }

  debug_oss << "agent can see ego;";
  return false;
}

bool WillEgoHaveLongitudinalOverlapWithAgentDuringCrossingLane(
    const TrajectoryMetaData& candidate,
    const std::vector<speed::pb::OverlapRegion>& overlap_regions,
    const double start_time, const double end_time,
    std::ostringstream& debug_oss) {
  if (candidate.trajectory.poses_size() < 2) {
    return false;
  }

  DCHECK_LE(start_time, end_time);

  constexpr double kArcLengthBufferForOverlapInMeter = 0.5;

  const int lane_change_direction_sign =
      candidate.trajectory_info->GetCrossLaneInfo()
                  .lane_change_instance.direction() ==
              planner::pb::LaneChangeMode::LEFT_LANE_CHANGE
          ? 1
          : -1;
  const int64_t plan_init_state_timestamp =
      candidate.trajectory.poses(0).timestamp();
  const double plan_init_state_odom = candidate.trajectory.poses(0).odom();
  for (const speed::pb::OverlapRegion& overlap_region : overlap_regions) {
    if (overlap_region.start_padded_relative_time_in_sec() > end_time) {
      break;
    }

    if (overlap_region.end_padded_relative_time_in_sec() < start_time) {
      continue;
    }

    for (const speed::pb::OverlapSlice& overlap_slice :
         overlap_region.overlap_slices()) {
      if (overlap_slice.relative_time_in_sec() > end_time) {
        break;
      }

      if (overlap_slice.relative_time_in_sec() < start_time) {
        continue;
      }

      // Laterally far.
      if (overlap_slice.signed_lateral_gap() * lane_change_direction_sign >
          0.0) {
        continue;
      }

      const std::optional<planner::pb::TrajectoryPose>& ego_pose =
          candidate.geo_centered_traj->GetInterpolatedPose2d(
              plan_init_state_timestamp +
              overlap_slice.relative_time_in_msec());
      if (!ego_pose.has_value()) {
        continue;
      }

      // The agent has overlap between the time interval of ego crossing lane.
      // Considering the exact overlap computation is expensive, take the odom
      // distance as the approximation.
      const double ego_front_bumper_arc_length =
          ego_pose->odom() - plan_init_state_odom +
          candidate.ego_shape.length() -
          candidate.ego_shape.rear_bumper_to_rear_axle();
      const double ego_rear_bumper_arc_length =
          ego_pose->odom() - plan_init_state_odom -
          candidate.ego_shape.rear_bumper_to_rear_axle();

      if (math::IsInRange(ego_front_bumper_arc_length,
                          speed::GetPaddedOverlapStart(overlap_slice) -
                              kArcLengthBufferForOverlapInMeter,
                          speed::GetPaddedOverlapEnd(overlap_slice) +
                              kArcLengthBufferForOverlapInMeter,
                          /*tol=*/0.0) ||
          math::IsInRange(ego_rear_bumper_arc_length,
                          speed::GetPaddedOverlapStart(overlap_slice) -
                              kArcLengthBufferForOverlapInMeter,
                          speed::GetPaddedOverlapEnd(overlap_slice) +
                              kArcLengthBufferForOverlapInMeter,
                          /*tol=*/0.0)) {
        debug_oss << "may be parallel with agent at "
                  << overlap_slice.relative_time_in_sec()
                  << "s; ego_odom_distance = "
                  << ego_pose->odom() - plan_init_state_odom
                  << ", ego_arc_length = [" << ego_rear_bumper_arc_length
                  << ", " << ego_front_bumper_arc_length
                  << "], agent_arc_length = ["
                  << speed::GetPaddedOverlapStart(overlap_slice) << ", "
                  << speed::GetPaddedOverlapEnd(overlap_slice) << "];";
        return true;
      }
    }
  }

  return false;
}

void ComputeEgoCrossLaneTimeInterval(
    const TrajectoryMetaData& candidate,
    std::optional<double>& cross_lane_start_relative_time,
    std::optional<double>& cross_lane_end_relative_time,
    bool& can_fully_cross_lane, std::ostringstream& debug_oss) {
  if (candidate.trajectory.poses_size() < 2) {
    return;
  }

  const auto& poses = candidate.trajectory.poses();
  const int64_t plan_init_state_timestamp = poses[0].timestamp();
  for (int i = 0; i < candidate.trajectory.poses_size(); ++i) {
    const std::optional<planner::pb::TrajectoryPose>& ego_pose =
        candidate.geo_centered_traj->GetInterpolatedPose2d(
            poses[i].timestamp());
    if (!ego_pose.has_value()) {
      continue;
    }

    const math::geometry::OrientedBox2d ego_bbox(
        ego_pose->x_pos(), ego_pose->y_pos(), candidate.ego_shape.length(),
        candidate.ego_shape.width(), ego_pose->heading());

    if (candidate.trajectory_info->GetCrossLaneInfo()
            .lane_change_instance.IsEgoFullyInTargetLaneSequence(
                ego_bbox.CornerPoints())) {
      cross_lane_end_relative_time = std::make_optional(
          math::Ms2Sec(poses[i].timestamp() - plan_init_state_timestamp));
      debug_oss << "ego is fully in target lane at "
                << cross_lane_end_relative_time.value()
                << "s, odom_distance = " << ego_pose->odom() << "m;";
      can_fully_cross_lane = true;
      return;
    }

    if (math::geometry::Intersects(ego_bbox,
                                   candidate.trajectory_info->GetCrossLaneInfo()
                                       .lane_change_instance.target_region()
                                       .border())) {
      if (!cross_lane_start_relative_time.has_value()) {
        cross_lane_start_relative_time = std::make_optional(
            math::Ms2Sec(poses[i].timestamp() - plan_init_state_timestamp));
        debug_oss << "ego starts crossing target lane at "
                  << cross_lane_start_relative_time.value()
                  << "s, odom_distance = " << ego_pose->odom() << "m;";
      }
    }
  }

  DCHECK(!cross_lane_end_relative_time.has_value());
  if (cross_lane_start_relative_time.has_value()) {
    cross_lane_end_relative_time = std::make_optional(
        math::Ms2Sec(poses[candidate.trajectory.poses_size() - 1].timestamp() -
                     plan_init_state_timestamp));
    debug_oss << "ego cross lane end time = "
              << cross_lane_end_relative_time.value() << "s;";
  }
  can_fully_cross_lane = false;
}

bool ShouldConsiderRiskWithTargetNeighborAgents(
    const RobotState& robot_state,
    const CrossLaneInfoManager& cross_lane_info_manager,
    const std::vector<int64_t>& risky_agent_ids,
    const speed::pb::SpeedSeed& speed_seed,
    const std::unordered_map<int64_t, PlannerObject>& planner_object_map,
    bool& is_risky_lane_change, std::ostringstream& debug_oss) {
  if (risky_agent_ids.empty()) {
    debug_oss << "\nNo risky agent;";
    return false;
  }

  if (risky_agent_ids.size() == 1) {
    const int64_t object_id = risky_agent_ids.front();
    debug_oss << "\n1 risky agent " << object_id << ";\n";
    if (!speed_seed.lc_guide_seed().empty() &&
        (object_id == speed_seed.lc_guide_seed(0).lead_obj_id() ||
         object_id == speed_seed.lc_guide_seed(0).tail_obj_id())) {
      debug_oss << "gap lead or tail agents, skip;";
      return false;
    }

    // Ignore the risk from the target neighbor agent when ego is inside the
    // field of view of the agent.
    if (!IsRiskyAgentDueToFovOnTargetNeighborRegion(
            robot_state, cross_lane_info_manager, object_id, planner_object_map,
            debug_oss)) {
      debug_oss << "\nnot risky agent due to fov;";
      return false;
    }

    debug_oss << "\nrisky agent due to fov;";
    const bool is_cut_in_agent = IsRiskyCutinAgentOnTargetNeighborRegion(
        cross_lane_info_manager, object_id, debug_oss);
    const bool is_risky_cut_in_agent =
        is_cut_in_agent
            ? IsCutinAgentRiskyByRelativeStatesOnTargetNeighborRegion(
                  robot_state, cross_lane_info_manager, object_id, debug_oss)
            : false;
    if (is_risky_cut_in_agent) {
      is_risky_lane_change = true;
    }

    debug_oss << "\n"
              << DUMP_TO_STREAM(is_cut_in_agent, is_risky_cut_in_agent) << "\n";
    return true;
  }

  // When there is more than one agent on the target neighbor region
  // overlapped with ego longitudinally, compute the risk when they will cutin
  // to the target region from the target neighbor region or there are large
  // vehicles among them.
  debug_oss << "\n" << risky_agent_ids.size() << " risky agents;";

  for (const int64_t object_id : risky_agent_ids) {
    debug_oss << "\nrisky object " << object_id << ": ";
    if (!speed_seed.lc_guide_seed().empty() &&
        (object_id == speed_seed.lc_guide_seed(0).lead_obj_id() ||
         object_id == speed_seed.lc_guide_seed(0).tail_obj_id())) {
      debug_oss << "gap lead or tail agents, skip;";
      continue;
    }

    if (DCHECK_NOTNULL(
            DCHECK_NOTNULL(cross_lane_info_manager
                               .GetTargetNeighborRegionObjectInfoListPtr())
                ->Find(object_id))
            ->planner_object()
            .is_large_vehicle()) {
      debug_oss << "large vehicle;";
      return true;
    }

    if (!IsRiskyAgentDueToFovOnTargetNeighborRegion(
            robot_state, cross_lane_info_manager, object_id, planner_object_map,
            debug_oss)) {
      debug_oss << "\nnot risky agent due to fov;";
      continue;
    }

    debug_oss << "\nrisky agent due to fov;";
    if (!IsRiskyCutinAgentOnTargetNeighborRegion(cross_lane_info_manager,
                                                 object_id, debug_oss)) {
      debug_oss << "\nnon cut-in agent;";
      continue;
    }

    // There are cutin agents. Check the relative states.
    debug_oss << "\ncut-in agent;";
    if (!IsCutinAgentRiskyByRelativeStatesOnTargetNeighborRegion(
            robot_state, cross_lane_info_manager, object_id, debug_oss)) {
      debug_oss << "\nnon risky agent;\n";
    } else {
      // The cutin agent is risky. The lane change is risky.
      debug_oss << "\nrisky agent;\n";
      is_risky_lane_change = true;
    }

    return true;
  }

  debug_oss << "\nNo risk added;\n";
  return false;
}

double ComputeTargetNeighborRiskForLaneChange(
    const RobotState& robot_state, const speed::pb::SpeedSeed& speed_seed,
    const std::unordered_map<int64_t, PlannerObject>& planner_object_map,
    const tbb::concurrent_unordered_map<
        ObjectId, std::vector<PredictedTrajectoryWrapper>>&
        object_prediction_map,
    TrajectoryMetaData* candidate, std::ostringstream& debug_oss) {
  candidate->is_risky_lane_change = false;

  if (!candidate->IsCrossLane()) {
    return 0.0;
  }

  debug_oss << "\nTarget neighbor risk info:\n";

  const CrossLaneObjectInfoList* target_neighbor_object_infos =
      candidate->trajectory_info->cross_lane_info_manager()
          .GetTargetNeighborRegionObjectInfoListPtr();
  if (target_neighbor_object_infos == nullptr) {
    debug_oss << "no target neighbor objects info;";
    return 0.0;
  }

  // Ignore target neighbor agent risk for consecutive lane changes.
  if (candidate->trajectory_info->GetCrossLaneInfo()
              .cross_lane_meta.cross_lane_count() > 1 &&
      target_neighbor_object_infos->ids().size() > 2) {
    debug_oss << "consecutive lane change; "
              << target_neighbor_object_infos->ids().size()
              << " agents on the target neighbor region;";
    return 0.0;
  }

  std::optional<double> cross_lane_start_relative_time;
  std::optional<double> cross_lane_end_relative_time;
  bool can_fully_cross_lane = false;
  ComputeEgoCrossLaneTimeInterval(*candidate, cross_lane_start_relative_time,
                                  cross_lane_end_relative_time,
                                  can_fully_cross_lane, debug_oss);
  if (!cross_lane_start_relative_time.has_value()) {
    debug_oss << "no cross lane start time;";
    return 0.0;
  }

  if (!cross_lane_end_relative_time.has_value()) {
    debug_oss << "no cross lane end time;";
    return 0.0;
  }

  debug_oss << "\n\nTarget neighbor agents info:";

  constexpr double kLcDurationInSec = 6.0;
  constexpr double kMinDistanceToConsiderTargetNeighborObjects = 10.0;
  const double front_distance = std::fmax(
      kMinDistanceToConsiderTargetNeighborObjects,
      robot_state.plan_init_state_snapshot().speed() * kLcDurationInSec);
  const double rear_distance = front_distance * 0.5;

  std::vector<int64_t> risky_agent_ids;
  for (const int64_t object_id : target_neighbor_object_infos->ids()) {
    const CrossLaneObjectInfo* object_info =
        target_neighbor_object_infos->Find(object_id);
    if (object_info == nullptr) {
      continue;
    }

    debug_oss << "\nobject " << object_id << ": ";
    if (!object_info->planner_object().is_vehicle()) {
      debug_oss << "non vehicle;";
      continue;
    }

    // Filter out parked cars without start-to-move signal.
    const auto object_predictions_iter = object_prediction_map.find(object_id);
    if (object_predictions_iter == object_prediction_map.end()) {
      continue;
    }
    if (object_info->planner_object().is_primary_stationary() &&
        std::none_of(
            object_predictions_iter->second.begin(),
            object_predictions_iter->second.end(),
            [](const PredictedTrajectoryWrapper& predicted_trajectory) {
              return predicted_trajectory.MightStartToMoveSoon() &&
                     !predicted_trajectory.is_primary_trajectory();
            })) {
      debug_oss << "static object, not STM;";
      continue;
    }

    const std::vector<speed::pb::OverlapRegion>&
        target_neighbor_overlap_regions = object_info->overlap_regions();
    DCHECK(!target_neighbor_overlap_regions.empty());
    if (speed::GetPaddedOverlapStart(target_neighbor_overlap_regions.front()) <
        -rear_distance) {
      debug_oss << "far behind, arc_length = "
                << speed::GetPaddedOverlapStart(
                       target_neighbor_overlap_regions.front())
                << " < " << -rear_distance;
      continue;
    }

    if (speed::GetPaddedOverlapStart(target_neighbor_overlap_regions.front()) >
        front_distance) {
      debug_oss << "far from the front, arc_length = "
                << speed::GetPaddedOverlapStart(
                       target_neighbor_overlap_regions.front())
                << " > " << front_distance;
      continue;
    }

    if (WillEgoHaveLongitudinalOverlapWithAgentDuringCrossingLane(
            *candidate, object_info->overlap_regions(),
            cross_lane_start_relative_time.value(),
            cross_lane_end_relative_time.value(), debug_oss)) {
      risky_agent_ids.push_back(object_id);
    } else {
      debug_oss << "no longitudinal overlap;";
    }
  }

  bool is_risky_lane_change = false;
  if (!ShouldConsiderRiskWithTargetNeighborAgents(
          robot_state, candidate->trajectory_info->cross_lane_info_manager(),
          risky_agent_ids, speed_seed, planner_object_map, is_risky_lane_change,
          debug_oss)) {
    return 0.0;
  }

  constexpr double kLcTargetNeighborRiskMinEgoSpeedInMps = 3.0;
  constexpr double kLcTargetNeighborRiskMaxEgoSpeedInMps = 6.0;
  double target_neighbor_risk_cost = math::GetLinearInterpolatedY(
      kLcTargetNeighborRiskMinEgoSpeedInMps,
      kLcTargetNeighborRiskMaxEgoSpeedInMps, 0.0,
      LaneChangeScenarioSelector::kLcTargetNeighborRiskCost,
      robot_state.plan_init_state_snapshot().speed());
  debug_oss << "\ninitial_target_neighbor_cost scaled by speed = "
            << target_neighbor_risk_cost;

  target_neighbor_risk_cost = math::GetLinearInterpolatedY(
      0.0, 1.0, target_neighbor_risk_cost, 0.0,
      candidate->trajectory_info->GetCrossLaneInfo().urgency_score);
  debug_oss << ", cost scaled by urgency score = " << target_neighbor_risk_cost;

  // Set the lane change to be risky only when there is a target neighbor agent
  // cutting in (is_risky_lane_change == true) and there is no urgency, and
  // ego's speed is high (the risk cost is not scaled).
  candidate->is_risky_lane_change =
      (is_risky_lane_change &&
       target_neighbor_risk_cost >
           LaneChangeScenarioSelector::kLcTargetNeighborRiskCost -
               math::constants::kEpsilon);

  // Scale the risk by lane change completion rate.
  const double lc_completion_rate =
      candidate->trajectory_info->GetCrossLaneInfo()
          .cross_lane_meta.cross_lane_completion_rate();
  target_neighbor_risk_cost = math::GetLinearInterpolatedY(
      0.0, 1.0, target_neighbor_risk_cost, 0.0, lc_completion_rate);
  debug_oss << ", cost scaled by lc completion rate = "
            << target_neighbor_risk_cost;

  // Scale the risk by traffic density.
  const CrossLaneRegionInfo* target_neighbor_region_info_ptr =
      GetRegionInfoPtrByType(
          CrossLaneRegionType::TARGET_NEIGHBOR,
          candidate->trajectory_info->GetCrossLaneInfo().region_infos);
  DCHECK(target_neighbor_region_info_ptr != nullptr);

  constexpr double kMaxTrafficDensityToConsiderTargetNeighborAgentRisk = 0.5;
  target_neighbor_risk_cost = math::GetLinearInterpolatedY(
      0.0, kMaxTrafficDensityToConsiderTargetNeighborAgentRisk,
      target_neighbor_risk_cost, 0.0,
      target_neighbor_region_info_ptr->traffic_density);
  debug_oss << ", cost scaled by traffic density = "
            << target_neighbor_risk_cost
            << "\nlc_completion_rate = " << lc_completion_rate
            << ", traffic_density = "
            << target_neighbor_region_info_ptr->traffic_density
            << ", urgency_score = "
            << candidate->trajectory_info->GetCrossLaneInfo().urgency_score;

  return target_neighbor_risk_cost;
}

double ComputeLaneChangeFullStopCost(const RobotState& robot_state,
                                     const TrajectoryMetaData& candidate,
                                     std::ostringstream& debug_oss) {
  if (!candidate.IsCrossLane()) {
    return 0.0;
  }

  if (!candidate.IsFullStopFromSpeedConflictResolver() &&
      !candidate.IsEmergencyBrake()) {
    return 0.0;
  }

  debug_oss << "\n\nFull stop / EB risk info:\n";

  if (robot_state.current_state_snapshot().speed() <
      constants::kDefaultEgoNearStaticSpeedInMps) {
    debug_oss << "Ego near static;\n";
    return 0.0;
  }

  // Risk cost for LC candidate if the speed profile is full stop. The value is
  // picked to be high enough that we'll abort immediately.
  constexpr double kNormalLcFullStopRisk = 3.0;
  if (!ShouldGiveRewardToLaneChange(*candidate.trajectory_info)) {
    return kNormalLcFullStopRisk;
  }

  // Compute the full stop cost for crawling lane change.
  constexpr double kCrawlLcFullStopRisk = 6.0;
  const auto& poses = candidate.trajectory.poses();
  DCHECK(!poses.empty());

  // Check the last point of the profile. When the last point is not static or
  // encroaches the target lane, add the full stop cost to encourage aborting
  // the current lane change.
  const planner::pb::TrajectoryPose& last_pose = *poses.rbegin();

  if (last_pose.speed() > constants::kDefaultEgoNearStaticSpeedInMps) {
    debug_oss << "Last pose non-zero speed " << last_pose.speed() << "m/s;\n";
    return kCrawlLcFullStopRisk;
  }

  const double dist_to_cross_lane_curve =
      candidate.trajectory_info->GetCrossLaneInfo()
          .lane_change_instance
          .CalculateEgoFrontBumperCornerDistToCrossLaneCurve(
              {last_pose.x_pos(), last_pose.y_pos()}, last_pose.heading(),
              candidate.ego_shape);
  if (dist_to_cross_lane_curve > math::constants::kEpsilon) {
    debug_oss << "Last pose doesn't encroach target lane, dist = "
              << dist_to_cross_lane_curve << "m;\n";
    return 0.0;
  }

  debug_oss << "Last pose encroaches target lane, dist = "
            << dist_to_cross_lane_curve << "m;\n";
  return kCrawlLcFullStopRisk;
}

// Computes lane change cost when the gap is obsolete.
double ComputeLcGapObsoleteCost(const RobotState& ego,
                                const speed::pb::SpeedSeed& speed_seed,
                                const TrajectoryMetaData& candidate,
                                const double lc_completion_rate,
                                std::ostringstream& debug_oss) {
  debug_oss << "\nobsolete_gap_risk_debug_info:";
  const bool is_lc_gap_obsolete =
      IsLcGapObsolete(speed_seed, candidate, debug_oss);
  debug_oss << "\nis_lc_gap_obsolete = " << is_lc_gap_obsolete << "\n";
  if (!is_lc_gap_obsolete) {
    return 0.0;
  }

  // The more lane change completion rate is, the lower the gap obsolete cost
  // is.
  double obsolete_gap_cost = math::GetLinearInterpolatedY(
      0.0, 1.0, kObsoleteLcGapMaxRisk, 0.0, lc_completion_rate);

  // Adjusts the obsolete gap cost by speed.
  // The higher the speed is, the higher the gap obsolete cost is.
  obsolete_gap_cost *= math::GetLinearInterpolatedY(
      kObsoleteLcGapMinEgoSpeedInMps, kObsoleteLcGapMaxEgoSpeedInMps,
      kObsoleteLcGapSpeedMinFactor, kObsoleteLcGapSpeedMaxFactor,
      ego.plan_init_state_snapshot().speed());

  // Scales obsolete gap cost by lane change urgency.
  return math::GetLinearInterpolatedY(
      0.0, 1.0, obsolete_gap_cost, 0.0,
      candidate.trajectory_info->GetCrossLaneInfo().urgency_score);
}

double ComputeLcRiskyLeadCost(
    const RobotState& ego, const double max_brake,
    const double speed_discomfort, const speed::pb::SpeedSeed& speed_seed,
    const std::unordered_map<int64_t, PlannerObject>& planner_object_map,
    const double lc_completion_rate,
    const CrossLaneInfoManager& cross_lane_info_manager,
    std::ostringstream& debug_oss) {
  // Cost of risky lead to the lead agent.
  constexpr double kBrakeLowerBoundForRiskyLeadInMpss = 2.5;
  constexpr double kReactionTimeInSec = 0.2;
  constexpr double kBrakeHorizonInSec = 3.0;
  constexpr double kMaxRiskyLeadRisk = 2.0;
  constexpr double kMinRiskyLeadBrakeWTailInMpss = 2.0;
  constexpr double kMaxRiskyLeadBrakeWTailInMpss = 3.0;
  constexpr double kMinRiskyLeadBrakeWOTailInMpss = 3.0;
  constexpr double kMaxRiskyLeadBrakeWOTailInMpss = 4.0;
  constexpr double kMinRiskyLeadDistInMeter = 3.0;
  constexpr double kMaxRiskyLeadDistInMeter = 5.0;
  constexpr double kMinRiskyLeadScaleFactor = 0.5;
  constexpr double kMaxRiskyLeadScaleFactor = 1.5;

  // Only consider CL great equal than 0.75 discomfort.
  if (speed_discomfort <
          (speed::Discomforts::kMid + speed::Discomforts::kHighLevelRes * 3.0 -
           math::constants::kEpsilon) &&
      max_brake < kBrakeLowerBoundForRiskyLeadInMpss) {
    if (speed_discomfort >
        (speed::Discomforts::kMid + speed::Discomforts::kHighLevelRes * 2.0 -
         math::constants::kEpsilon)) {
      rt_event::PostRtEvent<
          rt_event::planner::
              PlannerLaneChangeLeadRiskOffAtMidDiscomfortLevel>();
    }
    return 0.0;
  }

  debug_oss << "\nrisky_lead_debug_info:";
  const int64_t lead_agent_id = speed_seed.lc_guide_seed(0).lead_obj_id();
  const bool has_tail_agent = speed_seed.lc_guide_seed(0).tail_obj_id() != 0;
  // Check legality of lead_agent_id.
  const CrossLaneObjectInfoList* objects_on_target_region =
      cross_lane_info_manager.GetTargetRegionObjectInfoListPtr();
  if (objects_on_target_region == nullptr ||
      objects_on_target_region->Find(lead_agent_id) == nullptr) {
    debug_oss << "\nNo lead agent info in LC info. ";
    return 0.0;
  }
  const auto lead_agent_ptr = planner_object_map.find(lead_agent_id);
  if (lead_agent_ptr == planner_object_map.end()) {
    debug_oss << "\nNo lead agent info in obj map. ";
    return 0.0;
  }

  const CrossLaneObjectInfo* object_info = DCHECK_NOTNULL(
      DCHECK_NOTNULL(objects_on_target_region)->Find(lead_agent_id));
  const double object_fb_to_ego_rb_dist =
      object_info->object_ego_dist +
      ego.car_model_with_shape()
          .shape_measurement()
          .rear_bumper_to_rear_axle() -
      ego.car_model_with_shape().shape_measurement().length() -
      object_info->planner_object().length() * 0.5;
  const double ego_speed = ego.plan_init_state_snapshot().speed();
  const double ego_accel = ego.plan_init_state_snapshot().acceleration();
  const double agent_speed = lead_agent_ptr->second.speed();
  const double agent_accel = lead_agent_ptr->second.acceleration();

  // Consider a reaction time delay.
  const double delayed_ego_speed =
      std::max(ego_speed + ego_accel * kReactionTimeInSec, 0.0);
  const double delayed_agent_speed =
      std::max(agent_speed + agent_accel * kReactionTimeInSec, 0.0);

  // Determine min/max brake considering tail agent.
  const double ego_min_brake = has_tail_agent ? kMinRiskyLeadBrakeWTailInMpss
                                              : kMinRiskyLeadBrakeWOTailInMpss;
  const double ego_max_brake = has_tail_agent ? kMaxRiskyLeadBrakeWTailInMpss
                                              : kMaxRiskyLeadBrakeWOTailInMpss;

  // Scales max deceleration by lane change urgency.
  const double ego_max_decel = math::GetLinearInterpolatedY(
      0.0, 1.0, ego_min_brake, ego_max_brake,
      cross_lane_info_manager.GetCrossLaneInfo().urgency_score);
  // Clamp agent accel to 0 after the reaction time.
  const double agent_max_decel = std::min(agent_accel, 0.0);

  // Speed after a time horizon of kBrakeHorizonInSec.
  const double final_ego_speed =
      std::max(delayed_ego_speed - ego_max_decel * kBrakeHorizonInSec, 0.0);
  const double final_agent_speed =
      std::max(delayed_agent_speed + agent_max_decel * kBrakeHorizonInSec, 0.0);

  double min_dist = 0.0;
  // If the lead agent decelerates more than ego's max_brake, check distance
  // at the end of horizon.
  if (ego_max_decel < -agent_accel + math::constants::kEpsilon) {
    const double ego_dist =
        ego_speed * kReactionTimeInSec +
        0.5 * ego_accel * kReactionTimeInSec * kReactionTimeInSec +
        0.5 *
            (final_ego_speed * final_ego_speed -
             delayed_ego_speed * delayed_ego_speed) /
            -ego_max_decel;
    const double agent_dist =
        0.5 *
        (final_agent_speed * final_agent_speed - agent_speed * agent_speed) /
        agent_accel;
    min_dist = object_fb_to_ego_rb_dist - (ego_dist - agent_dist);
  }
  // If the lead agent decelerates less than ego's max_brake.
  else {
    // If ego is slower, return 0 cost.
    if (delayed_ego_speed < delayed_agent_speed - math::constants::kEpsilon) {
      debug_oss << "\nEgo can not catch up with lead agent. ";
      return 0.0;
    }
    // If ego is faster, distance will be shortest when decel to agent's speed.
    const double max_decel_time =
        std::min((delayed_ego_speed - delayed_agent_speed) /
                     (ego_max_decel + agent_max_decel),
                 kBrakeHorizonInSec);
    const double ego_dist =
        ego_speed * kReactionTimeInSec +
        0.5 * ego_accel * kReactionTimeInSec * kReactionTimeInSec +
        delayed_ego_speed * max_decel_time -
        0.5 * ego_max_decel * max_decel_time * max_decel_time;
    const double agent_dist =
        agent_speed * kReactionTimeInSec +
        0.5 * agent_accel * kReactionTimeInSec * kReactionTimeInSec +
        delayed_agent_speed * max_decel_time +
        0.5 * agent_max_decel * max_decel_time * max_decel_time;
    min_dist = object_fb_to_ego_rb_dist - (ego_dist - agent_dist);
  }

  // Scales risky lead cost by desired deceleration.
  const double risky_lead_cost = math::GetLinearInterpolatedY(
      kMinRiskyLeadDistInMeter, kMaxRiskyLeadDistInMeter, kMaxRiskyLeadRisk,
      0.0, min_dist);
  debug_oss << "\ncur_dist = " << object_fb_to_ego_rb_dist
            << ", min_dist = " << min_dist
            << "\noriginal_risky_lead_cost = " << risky_lead_cost;

  // The cost should be higher before crossing lane.
  const double final_risky_lead_cost =
      risky_lead_cost * math::GetLinearInterpolatedY(
                            0.5, 0.0, kMinRiskyLeadScaleFactor,
                            kMaxRiskyLeadScaleFactor, lc_completion_rate);

  return final_risky_lead_cost;
}

double ComputeLcSafeYieldDecelerationCost(
    const RobotState& ego, const TrajectoryMetaData& candidate,
    const speed::pb::SpeedSeed& /* speed_seed */,
    const double lc_completion_rate, std::ostringstream& debug_oss) {
  // All acceleration/deceleration/brake related here are the values with their
  // +/- signs.
  constexpr double kComfortYieldDeceleration = -2.0;
  constexpr double kSafeDecelerationIgnoreIntervalForNormalLc = 0.5;
  constexpr double kSafeDecelerationIgnoreIntervalForCrawl = 0.2;
  constexpr double kSafeDecelerationRedundantIntervalForCrawl = 1.0;
  constexpr double kExceedSafeDecelerationCostForNormalLc = 1.5;
  constexpr double kExceedSafeDecelerationCostForCrawl = 6.0;

  const double ego_safe_deceleration =
      candidate.trajectory_info->GetCrossLaneInfo().safe_yield_acceleration;
  debug_oss << "\n\nSafe yield deceleration risk info:\n"
            << "ego_safe_deceleration = " << ego_safe_deceleration
            << ", ComfortYieldDeceleration = " << kComfortYieldDeceleration
            << "\n";

  // Ignore the brake cost when the safe deceleration is low enough.
  if (ego_safe_deceleration <
      kComfortYieldDeceleration + math::constants::kEpsilon) {
    return 0.0;
  }

  // Compute the max deceleration among the first 2.0 seconds on the
  // trajectory.
  const double max_deceleration =
      std::min(0.0, GetMinOrMaxAccelerationInTimeRange(
                        candidate.trajectory, kBrakeCheckDurationInMsec));

  // Compute the no_cost_deceleration wrt. the ignore interval, which means
  // ego's brake risk exceeding this interval will be ignored.
  const bool is_high_reward_lane_change =
      ShouldGiveRewardToLaneChange(*candidate.trajectory_info);
  const double no_cost_deceleration =
      is_high_reward_lane_change
          ? std::min(
                ego_safe_deceleration + kSafeDecelerationIgnoreIntervalForCrawl,
                0.0)
          : std::min(ego_safe_deceleration +
                         kSafeDecelerationIgnoreIntervalForNormalLc,
                     0.0);
  const double max_cost_deceleration =
      is_high_reward_lane_change
          ? ego_safe_deceleration - kSafeDecelerationRedundantIntervalForCrawl
          : ego_safe_deceleration;
  const double exceed_safe_deceleration_max_cost =
      is_high_reward_lane_change ? kExceedSafeDecelerationCostForCrawl
                                 : kExceedSafeDecelerationCostForNormalLc;
  const double ego_speed = ego.plan_init_state_snapshot().speed();

  debug_oss << "ego_max_deceleration = " << max_deceleration << "(in "
            << kBrakeCheckDurationInMsec
            << "ms), no_cost_deceleration = " << no_cost_deceleration
            << ", max_cost_deceleration = " << max_cost_deceleration
            << ", max_cost = " << exceed_safe_deceleration_max_cost
            << ", lc_completion_rate = " << lc_completion_rate
            << ", ego_speed = " << ego_speed
            << ", is_high_reward_lane_change = " << is_high_reward_lane_change;

  // Compute the deceleration cost wrt. the ego_safe_deceleration,
  // no_cost_deceleration and max_cost_deceleration.
  double yield_deceleration_risk = math::GetLinearInterpolatedY(
      no_cost_deceleration, max_cost_deceleration, 0.0,
      exceed_safe_deceleration_max_cost, max_deceleration);
  debug_oss << "\ninitial_safe_yield_deceleration_cost = "
            << yield_deceleration_risk;

  // Scale the brake risk by speed.
  // The higher the speed is, the higher the brake risk cost is.
  if (is_high_reward_lane_change) {
    const double speed_factor =
        ego_speed < kObsoleteLcGapMinEgoSpeedInMps ? 0.0 : 1.0;
    yield_deceleration_risk *= speed_factor;
  } else {
    yield_deceleration_risk = math::GetLinearInterpolatedY(
        kObsoleteLcGapMinEgoSpeedInMps, kObsoleteLcGapMaxEgoSpeedInMps, 0.0,
        yield_deceleration_risk, ego_speed);
  }
  debug_oss << ", cost scaled by speed = " << yield_deceleration_risk;

  // Scale the brake risk by lane change completion rate.
  yield_deceleration_risk = math::GetLinearInterpolatedY(
      0.0, 1.0, yield_deceleration_risk, 0.0, lc_completion_rate);
  debug_oss << ", cost scaled by lc completion rate = "
            << yield_deceleration_risk;

  return yield_deceleration_risk;
}

// Populates all the brake risk related costs, including excessive brake risk,
// obsolete gap risk and safe yield deceleration risk for tailgators.
double PopulateBrakeRiskRelatedCost(
    const RobotState& ego, const TrajectoryMetaData& candidate,
    const speed::pb::SpeedSeed& speed_seed,
    const double max_brake_before_show_lc_intent,
    std::ostringstream& debug_oss) {
  // Calculates excessive brake risk, which prevents ego from braking too
  // much before showing LC intention.
  double excessive_brake_risk = math::GetLinearInterpolatedY(
      kLcMinExcessiveBrake, kLcMaxExcessiveBrake, 0.0, kLcMaxExcessiveBrakeCost,
      max_brake_before_show_lc_intent);

  // Calculates lane change completion rate.
  const double lc_completion_rate =
      candidate.trajectory_info->GetCrossLaneInfo()
          .cross_lane_meta.cross_lane_completion_rate();

  std::ostringstream inner_debug_oss;
  // Calculates cost when the lane change gap is obsolete.
  double obsolete_gap_risk = ComputeLcGapObsoleteCost(
      ego, speed_seed, candidate, lc_completion_rate, inner_debug_oss);

  // The excessive brake cost should be further increased when the gap is
  // obsolete. And the obsolete gap cost should be further decreased when
  // there is no excessive brake risk.
  if (obsolete_gap_risk > math::constants::kEpsilon) {
    // Increases excessive brake cost when gap is obsolete.
    excessive_brake_risk *= (excessive_brake_risk > math::constants::kEpsilon
                                 ? kObsoleteLcGapExcessiveBrakeFactor
                                 : 1.0);
    // Decreases gap obsolete cost when there is no uncomfortable brake risk.
    obsolete_gap_risk *= math::GetLinearInterpolatedY(
        kLcMaxUncomfortableBrake, kLcMinExcessiveBrake,
        kObsoleteLcGapSlightBrakeFactor, 1.0, max_brake_before_show_lc_intent);
  }

  debug_oss << "\n\nBrake related risk info:\nexcessive_brake_risk = "
            << excessive_brake_risk
            << "[max_brake_before_lc = " << max_brake_before_show_lc_intent
            << "]\nobsolete_gap_risk = " << obsolete_gap_risk
            << "\nlc_completion_rate = " << lc_completion_rate
            << ", ego_speed = " << ego.plan_init_state_snapshot().speed()
            << ", lc_urgency_score = "
            << candidate.trajectory_info->GetCrossLaneInfo().urgency_score
            << "]\n"
            << inner_debug_oss.str();

  return std::max({excessive_brake_risk, obsolete_gap_risk});
}

// Returns LC's rear end risk against lead & tail agent. Current implementation
// computes the projected distance between ego and lead/tail agent along LC
// target lane, then penalized according to a safe lead distance calculated from
// agent and ego projected speed on target lane.
// |             |              |
// | *********** |              |
// | *         * |              |
// | *         * <- Lead Agent  |
// | *         * |              |
// | *         * |              |
// | *********** |--------------|
// |             |      |       |
// |             |      |       |
// |             |      | <- Projected Dist to Lead
// |             |      |       |
// |             |      |       |
// |             * -------------|
// |           *   *            |
// |        *        *          |
// |      *            *        |
// |        *            * <- Ego
// |          *        *        |
// |            *   *           |
// |              * ------------|
// |             |      |       |
// |             |      | <- Projected Dist to Tail
// |             |      |       |
// |             |      |       |
// | *********** |--------------|
// | *         * |              |
// | *         * <- Tail Agent  |
// | *         * |              |
// | *         * |              |
// | *********** |--------------|
double ComputeLaneChangeRisk(
    const RobotState& ego, const speed::pb::SpeedSeed& speed_seed,
    const planner::pb::BehaviorType& last_behavior,
    const std::unordered_map<int64_t, PlannerObject>& planner_object_map,
    const tbb::concurrent_unordered_map<
        ObjectId, std::vector<PredictedTrajectoryWrapper>>& prediction_map,
    const lane_selection::LaneSequenceGeometry* lf_lane_seq_geometry,
    TrajectoryMetaData* candidate) {
  if (!candidate->IsCrossLane()) return 0.0;

  LaneChangeRiskInfo& lane_change_risk_info =
      candidate->lane_change_selection_info.lc_risk_info;
  std::string& lc_risk_debug_str = lane_change_risk_info.debug_str;

  std::ostringstream debug_oss;
  debug_oss << (candidate->trajectory_info->is_lane_change_crawl()
                    ? "\nCrawl lane change risk info:\n"
                    : "\nNormal lane change risk info:\n");

  // Don't start to crawl at high discomfort to be safe.
  const double ego_speed = ego.current_state_snapshot().speed();
  constexpr double kMaxDiscomfortValueToTriggerCrawlAtHighSpeed = 0.25;
  constexpr double kMaxDiscomfortValueToTriggerCrawlAtLowSpeed = 0.50;
  const bool should_add_cost_for_high_discomfort =
      (ego_speed > speed::reasoner::LeadAndMergeAgentReasoner::
                       kMaxSpeedConsiderDiscomfortForProgressDuringCrawlInMps &&
       candidate->speed_discomfort >
           kMaxDiscomfortValueToTriggerCrawlAtHighSpeed +
               math::constants::kEpsilon) ||
      (ego_speed < speed::reasoner::LeadAndMergeAgentReasoner::
                       kMaxSpeedConsiderDiscomfortForProgressDuringCrawlInMps &&
       candidate->speed_discomfort >
           kMaxDiscomfortValueToTriggerCrawlAtLowSpeed +
               math::constants::kEpsilon);
  if (last_behavior != planner::pb::BehaviorType::CROSS_LANE &&
      candidate->trajectory_info->is_lane_change_crawl() &&
      should_add_cost_for_high_discomfort) {
    constexpr double kLaneChangeCrawlCostForHighDiscomfort = 4.5;
    lane_change_risk_info.UpdateLaneChangeRisk(
        LaneChangeRiskType::HIGH_DISCOMFORT,
        kLaneChangeCrawlCostForHighDiscomfort);
    debug_oss << "high discomfort cost for crawl = "
              << kLaneChangeCrawlCostForHighDiscomfort
              << "\ndiscomfort = " << candidate->speed_discomfort
              << ", ego_speed = " << ego_speed << "\n";
    lc_risk_debug_str += debug_oss.str();
    return lane_change_risk_info.lc_risk;
  }

  // 1. Compute LC specific risk associated with full stop or emergency brake
  // during LC.
  std::ostringstream inner_debug_oss;
  const double full_stop_risk =
      ComputeLaneChangeFullStopCost(ego, *candidate, inner_debug_oss);
  lane_change_risk_info.UpdateLaneChangeRisk(LaneChangeRiskType::FULL_STOP,
                                             full_stop_risk);
  candidate->is_risky_lane_change_due_to_speed_failure =
      (full_stop_risk > math::constants::kEpsilon);
  debug_oss << "full_stop_risk = " << full_stop_risk << "\n";

  // 2. Compute the risk from the target neighbor region agents during lane
  // change.
  const double target_neighbor_agent_risk =
      ComputeTargetNeighborRiskForLaneChange(ego, speed_seed,
                                             planner_object_map, prediction_map,
                                             candidate, inner_debug_oss);
  lane_change_risk_info.UpdateLaneChangeRisk(
      LaneChangeRiskType::TARGET_NEIGHBOR_AGENT, target_neighbor_agent_risk);
  debug_oss << "target_neighbor_agent_risk = " << target_neighbor_agent_risk
            << "\n";

  // 3. Compute the safe yield deceleration risk related to the tailgator.
  const double lc_completion_rate =
      candidate->trajectory_info->GetCrossLaneInfo()
          .cross_lane_meta.cross_lane_completion_rate();
  const double safe_yield_deceleration_risk =
      ComputeLcSafeYieldDecelerationCost(ego, *candidate, speed_seed,
                                         lc_completion_rate, inner_debug_oss);
  lane_change_risk_info.UpdateLaneChangeRisk(
      LaneChangeRiskType::SAFE_YIELD_DECELERATION,
      safe_yield_deceleration_risk);
  debug_oss << "safe_yield_deceleration_risk = " << safe_yield_deceleration_risk
            << "\n";

  // 4. Compute the risk related to over yield excessive brake and obsolete gap.
  const int64_t lead_agent_id = speed_seed.lc_guide_seed(0).lead_obj_id();
  const int64_t tail_agent_id = speed_seed.lc_guide_seed(0).tail_obj_id();
  if (prediction_map.find(lead_agent_id) == prediction_map.end() &&
      prediction_map.find(tail_agent_id) == prediction_map.end()) {
    lc_risk_debug_str += debug_oss.str();
    lc_risk_debug_str += inner_debug_oss.str();
    return lane_change_risk_info.lc_risk;
  }
  DCHECK(!candidate->lane_sequence_result().lane_change_instances.empty());
  const pnc_map::Lane& target_lane = candidate->lane_sequence_result()
                                         .lane_change_instances.front()
                                         .target_lane();
  double max_brake_before_show_lc_intent = 0.0;
  double max_brake = 0.0;
  for (const planner::pb::TrajectoryPose& ego_pose :
       candidate->trajectory.poses()) {
    if (!EgoHasShownLcIntention(candidate->ego_shape, ego_pose,
                                lf_lane_seq_geometry, target_lane,
                                *candidate)) {
      math::UpdateMax(-ego_pose.accel(), max_brake_before_show_lc_intent);
    }
    math::UpdateMax(-ego_pose.accel(), max_brake);
  }
  const double brake_related_risk = PopulateBrakeRiskRelatedCost(
      ego, *candidate, speed_seed, max_brake_before_show_lc_intent,
      inner_debug_oss);
  lane_change_risk_info.UpdateLaneChangeRisk(LaneChangeRiskType::BRAKE_RELATED,
                                             brake_related_risk);
  debug_oss << "brake_related_risk = " << brake_related_risk << "\n";

  // 5. Calculates cost when ego may collide into lead agent.
  const double risky_lead_risk = ComputeLcRiskyLeadCost(
      ego, max_brake, candidate->speed_discomfort, speed_seed,
      planner_object_map, lc_completion_rate,
      candidate->trajectory_info->cross_lane_info_manager(), inner_debug_oss);
  lane_change_risk_info.UpdateLaneChangeRisk(LaneChangeRiskType::RISKY_LEAD,
                                             risky_lead_risk);
  debug_oss << "risky_lead_risk = " << risky_lead_risk << "\n";

  lc_risk_debug_str += debug_oss.str();
  lc_risk_debug_str += inner_debug_oss.str();
  return lane_change_risk_info.lc_risk;
}

void LaneChangeCollisionRiskWindow::Add(double value) {
  DCHECK_NE(capacity_, 0);
  if (IsFull()) {
    values_.pop_front();
  }
  values_.push_back(value);
}

bool LaneChangeCollisionRiskWindow::AllSatisfy(
    const std::function<bool(double)>& condition) const {
  return std::all_of(values_.begin(), values_.end(), condition);
}

bool LaneChangeCollisionRiskWindow::HasHighRisk(
    const double new_risk, const double threshold,
    std::ostringstream& debug_oss) const {
  if (IsEmpty() || !IsFull()) {
    return false;
  }
  const double avg_risk =
      std::accumulate(values_.begin(), values_.end(), 0.0) / values_.size();
  debug_oss << "\n" << DUMP_TO_STREAM(avg_risk);
  return new_risk > threshold && avg_risk > threshold;
}

bool LaneChangeCollisionRiskWindow::HasHighRisk(
    const double new_risk, const double threshold, const size_t num,
    std::ostringstream& debug_oss) const {
  size_t clamped_num = std::min(num, kLcCollisionRiskWindowCapacity);
  if (IsEmpty() || size() < clamped_num) {
    return false;
  }
  DCHECK_GT(clamped_num, 0);
  const double avg_risk =
      std::accumulate(values_.end() - clamped_num, values_.end(), 0.0) /
      clamped_num;
  debug_oss << "\n" << DUMP_TO_STREAM(clamped_num, avg_risk);
  return new_risk > threshold && avg_risk > threshold;
}

std::string LaneChangeCollisionRiskWindow::ToString() const {
  std::string str;
  for (auto iter = values_.begin(); iter < values_.end(); ++iter) {
    str += std::to_string(*iter) += " ";
  }
  return str;
}

void LaneChangeCollisionRiskWindow::Clear() { values_.clear(); }

bool LaneChangeScenarioSelector::ShouldRecomputeOverallCostsByRisk(
    const std::vector<TrajectoryMetaData*>& candidates) const {
  const double ego_speed =
      world_model_.robot_state().plan_init_state_snapshot().speed();
  // Compute the threshold for consecutive risky CL cycles.
  constexpr double kMinConsRiskyCLCyclesToRecomputeCosts = 1.0;
  constexpr double kMaxConsRiskyCLCyclesToRecomputeCosts = 5.0;
  constexpr double kLowSpeedInMps = 3.0;
  constexpr double kHighSpeedInMps = 8.0;
  const int consecutive_risky_cl_cycles_thresh =
      static_cast<int>(math::GetLinearInterpolatedY(
          kLowSpeedInMps, kHighSpeedInMps,
          kMaxConsRiskyCLCyclesToRecomputeCosts,
          kMinConsRiskyCLCyclesToRecomputeCosts, ego_speed));
  const int consecutive_risky_cl_cycles = seed_.selection_seed()
                                              .cross_lane_selection_seed()
                                              .consecutive_risky_cl_cycles();

  // Get the minimum risk in safe LK candidates.
  double min_risk_in_lk = std::numeric_limits<double>::infinity();
  for (const TrajectoryMetaData* candidate : candidates) {
    if (candidate->IsCrossLane()) {
      continue;
    }

    // Skip the LK if it violates the dynamic limits.
    if (!candidate->is_safe) {
      continue;
    }

    // Skip the LK if its speed planning fails.
    if (candidate->IsFullStopFromSpeedConflictResolver() ||
        candidate->IsExtraFullStopFromSpeedConflictResolver() ||
        candidate->IsEmergencyBrake()) {
      continue;
    }

    math::UpdateMin(candidate->pose_collision_risk, min_risk_in_lk);
  }

  // Check if the CLs in the current frame are risky.
  bool has_non_risky_cl = false;
  for (TrajectoryMetaData* candidate : candidates) {
    if (!candidate->IsCrossLane()) {
      continue;
    }

    std::ostringstream debug_oss;
    debug_oss << "\n\n---------------\n";
    candidate->is_risky_cross_lane =
        IsRiskyCrossLane(*candidate, min_risk_in_lk, ego_speed, debug_oss);
    debug_oss << "\n"
              << DUMP_TO_STREAM(candidate->is_risky_cross_lane,
                                consecutive_risky_cl_cycles,
                                consecutive_risky_cl_cycles_thresh)
              << "\n";
    candidate->lane_change_selection_info.lc_risk_info.debug_str +=
        debug_oss.str();

    if (!candidate->is_risky_cross_lane) {
      has_non_risky_cl = true;
    }
  }

  return !has_non_risky_cl &&
         (consecutive_risky_cl_cycles >= consecutive_risky_cl_cycles_thresh);
}

bool LaneChangeScenarioSelector::IsRiskyCrossLane(
    const TrajectoryMetaData& candidate, const double min_risk_in_lk,
    const double ego_speed, std::ostringstream& debug_oss) {
  if (!candidate.IsCrossLane()) {
    return false;
  }

  constexpr double kMinAcceptableRiskDiffByLcCompletionRate = 1.5;
  constexpr double kMaxAcceptableRiskDiffByLcCompletionRate = 2.0;
  constexpr double kMaxAcceptableRiskDiffBySpeed = 2.2;
  constexpr double kRiskThreshRelaxFactorForLowSpeed = 1.33;
  constexpr double kRiskThreshRelaxFactorForHighSpeed = 1.0;
  constexpr double kMinSpeedInMps = 6.0;
  constexpr double kMaxSpeedInMps = 8.0;

  // Compute if the CL is risky considering the risk diff between CL and LK, ego
  // speed and the cross lane completion rate.
  const CrossLaneInfo& cross_lane_info =
      candidate.trajectory_info->GetCrossLaneInfo();
  const double lc_completion_rate =
      cross_lane_info.cross_lane_meta.cross_lane_completion_rate();
  const double risk_thresh_speed_factor = math::GetLinearInterpolatedY(
      kMinSpeedInMps, kMaxSpeedInMps, kRiskThreshRelaxFactorForLowSpeed,
      kRiskThreshRelaxFactorForHighSpeed, ego_speed);
  // Relax the risk diff threshold and its upper bound for low speed scenarios
  // to avoid FP abort.
  const double risk_threshold = math::Clamp(
      math::LinearInterpolate(kMinAcceptableRiskDiffByLcCompletionRate,
                              kMaxAcceptableRiskDiffByLcCompletionRate,
                              lc_completion_rate) *
          risk_thresh_speed_factor,
      kMinAcceptableRiskDiffByLcCompletionRate, kMaxAcceptableRiskDiffBySpeed);
  const double candidate_risk = candidate.pose_collision_risk;
  const double risk_diff =
      std::isfinite(min_risk_in_lk) ? (candidate_risk - min_risk_in_lk) : 0.0;
  debug_oss << DUMP_TO_STREAM(risk_diff, risk_threshold, candidate_risk,
                              min_risk_in_lk, lc_completion_rate, ego_speed,
                              risk_thresh_speed_factor);
  return risk_diff > risk_threshold;
}

bool LaneChangeScenarioSelector::DetectLaneChangeHighRisk(
    const std::vector<TrajectoryMetaData*>& candidates,
    const std::unordered_map<pb::CostType, CostComputerMetadata>*
        sub_costs_map_ptr,
    std::ostringstream& debug_oss) const {
  debug_oss << __func__ << ": ";
  DCHECK(!candidates.empty());
  std::vector<double> basic_overall_costs(candidates.size(), 0.0);
  DCHECK(sub_costs_map_ptr != nullptr);
  PopulateOverallCosts(*sub_costs_map_ptr, basic_overall_costs);
  const TrajectoryMetaData& basic_proper_trajectory = *(candidates.at(
      std::min_element(basic_overall_costs.begin(), basic_overall_costs.end()) -
      basic_overall_costs.begin()));

  if (!basic_proper_trajectory.IsCrossLane()) {
    debug_oss << "Basic proper candidate is not CL.";
    return false;
  }

  const CrossLaneInfo& cross_lane_info =
      basic_proper_trajectory.trajectory_info->GetCrossLaneInfo();
  bool can_reroute = false;
  const std::optional<double> lc_route_urgency_score =
      GetLaneChangeRouteUrgencyScore(cross_lane_info,
                                     basic_proper_trajectory.current_lane(),
                                     can_reroute, debug_oss);
  const bool is_creep_for_normal_lc =
      (cross_lane_info.should_creep && !cross_lane_info.can_trigger_crawl);
  if (!lc_route_urgency_score.has_value() && !is_creep_for_normal_lc) {
    debug_oss << "No lc route urgency score for non creep or non normal lc.";
    return false;
  }
  const double lane_change_urgency_score =
      is_creep_for_normal_lc ? 1.0 : lc_route_urgency_score.value();

  const double lc_completion_rate =
      cross_lane_info.cross_lane_meta.cross_lane_completion_rate();
  const double final_score =
      std::max(lane_change_urgency_score, lc_completion_rate);
  debug_oss << DUMP_TO_STREAM(is_creep_for_normal_lc, lane_change_urgency_score,
                              lc_completion_rate, final_score);
  static constexpr double kMinCollisionRiskThreshold = 0.667;
  static constexpr double kMaxCollisionRiskThreshold = 0.833;
  const double ego_speed =
      world_model_.robot_state().current_state_snapshot().speed();
  static constexpr double kMaxCollisionRiskThresholdFactorForLowSpeed = 3.0;
  static constexpr double kMinSpeedInMps = 1.0;
  static constexpr double kMaxSpeedInMps = 3.0;
  // This factor is to avoid some FP high collision risk under low speed.
  const double collision_risk_threshold_factor =
      is_creep_for_normal_lc
          ? kMaxCollisionRiskThresholdFactorForLowSpeed
          : math::GetLinearInterpolatedY(
                kMinSpeedInMps, kMaxSpeedInMps,
                kMaxCollisionRiskThresholdFactorForLowSpeed, 1.0, ego_speed);
  debug_oss << "\n"
            << DUMP_TO_STREAM(ego_speed, collision_risk_threshold_factor);
  const double collision_risk_threshold =
      math::LinearInterpolate(kMinCollisionRiskThreshold,
                              kMaxCollisionRiskThreshold,
                              math::Clamp(final_score, 0.0, 1.0)) *
      collision_risk_threshold_factor;
  const double basic_trajectory_risk =
      basic_proper_trajectory.pose_collision_risk;
  double min_risk_in_lk = std::numeric_limits<double>::infinity();
  for (const TrajectoryMetaData* candidate : candidates) {
    if (!candidate->IsCrossLane()) {
      math::UpdateMin(candidate->pose_collision_risk, min_risk_in_lk);
    }
  }
  const double risk_diff = std::isfinite(min_risk_in_lk)
                               ? (basic_trajectory_risk - min_risk_in_lk)
                               : 0.0;
  debug_oss << "\n"
            << DUMP_TO_STREAM(collision_risk_threshold, basic_trajectory_risk,
                              min_risk_in_lk, risk_diff);
  debug_oss << "\n"
            << "risk_window: " << lc_collision_risk_window_.ToString();
  static constexpr double kMinNumForHighSpeed = 3;
  static constexpr double kMaxNumForLowSpeed = 10;
  static constexpr double kLowSpeedInMps = 1.0;
  static constexpr double kHighSpeedInMps = 8.0;
  const auto num = static_cast<size_t>(math::GetLinearInterpolatedY(
      kLowSpeedInMps, kHighSpeedInMps, kMaxNumForLowSpeed, kMinNumForHighSpeed,
      world_model_.robot_state().current_state_snapshot().speed()));

  const bool has_high_risk = lc_collision_risk_window_.HasHighRisk(
      risk_diff, collision_risk_threshold, num, debug_oss);
  // Publish a rt_event.
  if (has_high_risk) {
    const std::string payload =
        "can_reroute=" + std::to_string(can_reroute) +
        ",is_creep_for_normal_lc=" + std::to_string(is_creep_for_normal_lc);
    rt_event::PostRtEvent<
        rt_event::planner::
            PlannerLaneChangeHighRiskDetectedRegardlessOfReroute>(payload);
  }
  // Currently return false if cannot reroute.
  return can_reroute && has_high_risk;
}

void LaneChangeScenarioSelector::UpdateHighRiskDetectorInfo(
    const std::map<std::string, TrajectoryMetaData*>& candidates) {
  auto selected_trajectory_kv_iter =
      std::find_if(candidates.begin(), candidates.end(), [](const auto& kv) {
        const TrajectoryMetaData& candidate = *kv.second;
        return candidate.is_selected;
      });
  DCHECK(selected_trajectory_kv_iter != candidates.end());
  if (!selected_trajectory_kv_iter->second->IsCrossLane()) {
    lc_collision_risk_window_.Clear();
    return;
  }
  double min_risk_in_lk = std::numeric_limits<double>::infinity();
  for (const auto& [id, candidate] : candidates) {
    if (!candidate->IsCrossLane()) {
      math::UpdateMin(candidate->pose_collision_risk, min_risk_in_lk);
    }
  }
  const double risk_diff =
      std::isfinite(min_risk_in_lk)
          ? selected_trajectory_kv_iter->second->pose_collision_risk -
                min_risk_in_lk
          : 0.0;
  lc_collision_risk_window_.Add(risk_diff);
}

void LaneChangeScenarioSelector::UpdateSeed(
    const std::map<std::string, TrajectoryMetaData*>& candidates,
    const planner::pb::SelectionSeed& prev_seed,
    planner::pb::SelectionSeed* seed) {
  UpdateHighRiskDetectorInfo(candidates);
  if (FLAGS_planning_enable_lane_change_high_risk_detector) {
    DCHECK_NOTNULL(seed)
        ->mutable_cross_lane_selection_seed()
        ->set_is_high_risk_cl(detected_lc_high_risk_);
  } else {
    DCHECK_NOTNULL(seed)
        ->mutable_cross_lane_selection_seed()
        ->set_is_high_risk_cl(false);
  }
  BasicScenarioSelector::UpdateSeed(candidates, prev_seed, seed);
}

void LaneChangeScenarioSelector::Clear() {
  LOG(INFO) << kLogHeader << __func__ << ": Clear historical data.";
  lc_collision_risk_window_.Clear();
  detected_lc_high_risk_ = false;
  should_recompute_overall_costs_ = false;
}

}  // namespace selection
}  // namespace planner
