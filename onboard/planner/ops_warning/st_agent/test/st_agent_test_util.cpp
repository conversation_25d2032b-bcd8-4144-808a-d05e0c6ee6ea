#include "planner/ops_warning/st_agent/test/st_agent_test_util.h"

#include "planner/constants.h"
#include "voy_protos/trajectory.pb.h"

namespace planner {
// Construct the trajectory by const speed.
pb::Trajectory ConstructTrajectory(double v, double x_offset, double y_offset,
                                   double heading) {
  pb::Trajectory trajectory;
  double init_t = 0.0;
  double odom = 0.0;
  const double resolution_s =
      math::Ms2Sec(constants::kTrajectoryPoseResolutionInMSec);
  while (init_t <= constants::kTrajectoryHorizonInSec) {
    pb::TrajectoryPose* pose = trajectory.add_poses();
    pose->set_timestamp(math::Sec2Ms(init_t));
    pose->set_x_pos(std::cos(heading) * odom + x_offset);
    pose->set_y_pos(std::sin(heading) * odom + y_offset);
    pose->set_heading(heading);
    pose->set_steering(0.0);
    pose->set_steering_rate(0.0);
    pose->set_odom(odom);
    pose->set_speed(v);
    pose->set_accel(0.0);
    init_t += resolution_s;
    odom += v * resolution_s;
  }
  return trajectory;
}

}  // namespace planner
