#ifndef ONBOARD_PLANNER_SPEED_TIMEMAP_CONSTRAINT_STATE_PROCESSOR_H_
#define ONBOARD_PLANNER_SPEED_TIMEMAP_CONSTRAINT_STATE_PROCESSOR_H_

#include <vector>

#include "planner/speed/constraint/constraint.h"
#include "planner/speed/constraint/constraint_util.h"
#include "planner/speed/discomforts/discomfort_varying_min_range.h"

namespace planner {
namespace speed {

// Class ConstraintStateProcessor processes constraint states, by transforming
// the constraint state time and raw distance into relative time and distance
// inside speed solver configuration space for speed search and speed opt
// algorithm. Please see this for reference:
// https://docs.google.com/document/d/1hVNaGj9VllRNuDl9ZRgPugMY45EpOL2cNwez7clJMz0/edit.
class ConstraintStateProcessor {
 public:
  ConstraintStateProcessor(double dt, int steps, double ra_to_fb,
                           double ra_to_rb,
                           double min_range_max_required_lat_gap)
      : dt_(dt),
        inv_dt_(1.0 / dt_),
        steps_(steps),
        leading_bumper_offset_(ra_to_fb),
        trailing_bumper_offset_(ra_to_rb),
        min_range_max_required_lat_gap_(min_range_max_required_lat_gap) {
    DCHECK(!math::NearZero(dt_));
  }

  // Processes constraint states by adding time or distance buffer with the
  // function 'process_constraint_state'. If 'ignore_reasoning_context',
  // no reasoning time or distance buffer would be added.
  // If 'only_consider_min_lat_gap', only the states with the lateral gap
  // smaller than min_range_max_required_lat_gap_ would be considered.
  // If 'only_consider_strict', only strict states (aka. of which the lateral
  // gap is zero) and their strict_start/end_x are considered.
  // 'process_constraint_state' is a function that takes in time as first
  // argument and position as second argument.
  // TODO(yuwang): Add unit tests for ProcessStates.
  void ProcessStates(const std::vector<ConstraintState>& states,
                     const ConstraintSettings& settings,
                     const DiscomfortVaryingMinRange& min_range,
                     double discomfort, bool pass,
                     bool ignore_reasoning_context,
                     bool only_consider_min_lat_gap, bool only_consider_strict,
                     const std::function<void(double, double)>&
                         process_constraint_state) const;

  // Processes constraint states for dynamic range state potential by adding
  // time or distance buffer with the function 'process_constraint_state'.
  // 'process_constraint_state' is a function that takes in time as first
  // argument, position as second argument and signed longitudinal speed as
  // third argument. Note that |headway| should be handled inside
  // 'process_constraint_state'.
  // TODO(yuwang): Add unit tests for ProcessStatesForDynamicRange.
  void ProcessStatesForDynamicRange(
      const std::vector<ConstraintState>& states,
      const ConstraintSettings& settings,
      const DiscomfortVaryingMinRange& min_range, double discomfort,
      double immutable_time, bool pass,
      const std::function<void(double, double, double)>&
          process_constraint_state) const;

  // Processes constraint state of one state constraint for barrier and static
  // range repeller by adding time or distance buffer with the function
  // 'process_constraint_state'.
  // 'process_constraint_state' is a function that takes in start time as first
  // argument, end time as second argument and position as third argument.
  //
  // Note that for speed optimizer, if the constraint only has one single state
  // (usually it means a static agent), we will add multiple static range and
  // barrier for the state; if the constraint has multiple states (usually it
  // means a dynamic agent with overlap), we will only add one static range and
  // barrier for one constraint state.
  void ProcessStateOfSingleStateConstraint(
      const ConstraintState& state, const ConstraintSettings& settings,
      const DiscomfortVaryingMinRange& min_range, double discomfort, bool pass,
      bool ignore_reasoning_context, bool only_consider_min_lat_gap,
      const std::function<void(double, double, double)>&
          process_constraint_state) const;

  // Processes constraint standoffs for yielding by adding time or distance
  // buffer with the function 'process_constraint_state'. Note, standoffs are
  // only added for extra yielding regulation.
  // 'process_standoff' is a function that takes in time as first argument and
  // position as second argument.
  void ProcessStandoffs(
      const Standoffs& standoffs, double discomfort,
      const std::function<void(double, double)>& process_standoff) const;

  [[nodiscard]] double GetPassX(const ConstraintState& state,
                                const ConstraintSettings& settings,
                                const DiscomfortVaryingMinRange& min_range,
                                double discomfort, bool use_strict_x,
                                bool ignore_reasoning_context) const;

  [[nodiscard]] double GetYieldX(const ConstraintState& state,
                                 const ConstraintSettings& settings,
                                 const DiscomfortVaryingMinRange& min_range,
                                 double discomfort, bool use_strict_x,
                                 bool ignore_reasoning_context) const;

 private:
  // For passing, we consider end_x at start_time, i.e., the upper left
  // corner. For end_x, we add the min range as a hard safety buffer
  // when the constraint is hard and the lateral gap at the state is
  // smaller than the barrier lateral gap (aka
  // min_range_max_required_lat_gap_). If |use_start_time| is true, we use start
  // time to calculate the pass time, otherwise use end time to calculate the
  // pass time.
  [[nodiscard]] double GetPassTime(const ConstraintState& state,
                                   const ConstraintSettings& settings,
                                   double discomfort,
                                   bool ignore_reasoning_context,
                                   bool use_start_time = true) const;

  // For yielding, we consider start_x at end_time, i.e., the lower right
  // corner.If |use_start_time| is true, we use start time to calculate the
  // yield time, otherwise use end time to calculate the yield time.
  [[nodiscard]] double GetYieldTime(const ConstraintState& state,
                                    const ConstraintSettings& settings,
                                    double discomfort,
                                    bool ignore_reasoning_context,
                                    bool use_start_time = false) const;

  const double dt_;
  const double inv_dt_;
  const int steps_;
  // TODO(nihar): Remove these fields.
  [[maybe_unused]] const double leading_bumper_offset_;
  [[maybe_unused]] const double trailing_bumper_offset_;
  // Required lateral gap to add min-range, aka barrier lateral gap.
  const double min_range_max_required_lat_gap_;
};
}  // namespace speed
}  // namespace planner

#endif  // ONBOARD_PLANNER_SPEED_TIMEMAP_CONSTRAINT_STATE_PROCESSOR_H_
