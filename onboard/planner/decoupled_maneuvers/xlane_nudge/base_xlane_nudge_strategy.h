#ifndef ONBOARD_PLANNER_DECOUPLED_MANEUVERS_XLANE_NUDGE_BASE_XLANE_NUDGE_STRATEGY_H_
#define ONBOARD_PLANNER_DECOUPLED_MANEUVERS_XLANE_NUDGE_BASE_XLANE_NUDGE_STRATEGY_H_

#include <map>
#include <memory>
#include <vector>

#include "geometry/model/polyline_curve.h"
#include "mrc_protos/mrc_request.pb.h"
#include "planner/assist/assist_instruction.h"
#include "planner/decoupled_maneuvers/xlane_nudge/xlane_nudge_param.h"
#include "planner/utility/unstuck/stuck_region/stuck_region.h"
#include "planner/utility/unstuck/stuck_region/stuck_region_common.h"
#include "planner/world_model/lane_blockage/lane_blockage.h"
#include "planner/world_model/snapshot/robot_state.h"
#include "planner/world_model/world_model.h"
#include "planner_protos/agent_intention_config.pb.h"
#include "planner_protos/behavior_reasoners_debug.pb.h"
#include "planner_protos/path_reasoning_seed.pb.h"
#include "pnc_map_service/map_elements/lane.h"
#include "voy_protos/traffic_light.pb.h"

namespace planner {
namespace xlane_nudge {

// Class BaseXLaneNudgeStrategy implements basic pre-check logic for x-lane
// nudge triggering.
// TODO(tianxi): refactor the strategy framework.
class BaseXLaneNudgeStrategy {
 public:
  // Constructor.
  BaseXLaneNudgeStrategy() = delete;
  BaseXLaneNudgeStrategy(
      const WorldModel& world_model, const StuckRegion& stuck_region,
      const std::vector<const pnc_map::Lane*>& lane_sequence,
      const lane_selection::LaneSequenceGeometry& lane_sequence_geometry,
      const pb::XLaneNudgeSeed& last_seed, bool was_ego_stuck,
      bool is_stuck_signal_on_last_lane_sequence,
      const pb::XLaneNudgeDeciderConfig& config);

  // Pre-checks if the x-lane nudge strategy could be executed and records the
  // check result.
  PreCheckResult PreCheck(const XLaneSideParams& opposite_side_params,
                          const XLaneSideParams& params) const;

 protected:
  // Returns true if current strategy is not enable.
  virtual bool IsStrategyEnabled() const { return true; }

  // Returns true if the stuck region is capable for the current x-lane nudge
  // strategy. If a stuck region is out of current scope, e.g., some objects
  // start to move, we might prefer to abort x-lane nudge.
  bool IsCapableStuckRegionForXLaneNudge() const;

  // Returns true if x-lane nudge backs to the origin lane sequence.
  bool IsXLaneNudgeFinished() const;

  // Returns true if ego is x-lane nudging if ego executed same side x-lane
  // nudge on the same lane sequence in the last cycle.
  inline bool IsXLaneNudging(pb::StuckRegion::OvertakeSide side) const {
    return last_seed_.side() != pb::StuckRegion::kNotOvertake &&
           last_seed_.side() == side &&
           IsRegionInLaneSequence(last_seed_.target_stuck_region(),
                                  lane_sequence_);
  }

  // Returns true if ego should keep waiting.
  bool ShouldEmployKeepWaitingPolicy(const XLaneSideParams& params) const;

  // Returns true if traffic flow in the ahead lane sequence is waiting for
  // traffic light.
  bool IsWaitingForTrafficLight(const XLaneSideParams& params) const;

  // Returns true if ra is requesting x-lane nudge.
  inline bool IsRaRequestingXLaneNudge() const {
    return assist_instruction_ != nullptr &&
           !assist_instruction_->xlane_nudge_bypass_objects.empty();
  }

  // Returns the max speed limit for xlane nudge trigger.
  double GetMaxSpeedLimitForTriggering() const;

  // Returns if the side lane marking is not crossable.
  bool IsSideLaneMarkingNotCrossable(
      const XLaneSideParams& current_side_params) const;

  // Returns true if the in-lane clearance is wide enough to allow ego to pass
  // by normal in-lane nudge.
  bool IsClearanceWideEnoughToPass(
      const XLaneSideParams& opposite_side_params,
      const XLaneSideParams& current_side_params) const;

  // Returns true if checks for specialized scenario passed.
  virtual PreCheckResult SpecializedScenarioCheck(
      const XLaneSideParams& params) const {
    (void)params;
    return PreCheckResult(true, pb::XLaneNudgeDebug::kSuccess);
  }

  // Returns true if it's unnecessary to take ego speed into consideration for
  // xlane nudge triggering.
  bool ShouldIgnoreEgoSpeedLimit() const;

  //
  // Data members
  //
  // The stuck region that ego is currently interested.
  const StuckRegion& stuck_region_;
  // The current stuck lane sequence.
  const std::vector<const pnc_map::Lane*>& lane_sequence_;
  // Lane blockages.
  const std::map<int64_t, LaneBlockage>& lane_blockages_;
  // Lane Marking blocking info map. Key is the lane marking id.
  const std::map<int64_t, std::vector<LaneMarkingBlockage>>&
      blocking_lane_marking_map_;
  // Ego initial plan state snapshot.
  const RobotState& robot_state_;
  // Traffic lights detection result.
  const voy::TrafficLights& traffic_lights_;
  // Joint pnp_map_service.
  const pnc_map::JointPncMapService& joint_pnc_map_service_;
  // Assist instruction given by ra, which could be nullptr.
  const AssistInstruction* assist_instruction_;
  // MRC request.
  const mrc::pb::MrcRequest* mrc_request_ = nullptr;
  // The lane sequence geometry.
  const lane_selection::LaneSequenceGeometry& ls_geo_;
  // Seed stores the last x-lane nudge states.
  const pb::XLaneNudgeSeed& last_seed_;
  // Configurations for xlane nudge decider.
  const pb::XLaneNudgeDeciderConfig& config_;
  // Flag indicates if ego was stuck by object or construction zone in the last
  // cycle.
  const bool was_ego_stuck_;
  // Flag indicates if current stuck signal is on the last selected lane
  // sequence.
  const bool is_stuck_signal_on_last_lane_sequence_;
  // Flag indicates if planner should respond mrc.
  const bool should_planner_respond_mrc_;
  // The ego front bumper arclength on the nominal path.
  double ego_fb_arclength_ = 0.0;
  // The ego rear bumper arclength on the nominal path.
  double ego_rb_arclength_ = 0.0;
};

}  // namespace xlane_nudge
}  // namespace planner

#endif  // ONBOARD_PLANNER_DECOUPLED_MANEUVERS_XLANE_NUDGE_BASE_XLANE_NUDGE_STRATEGY_H_
