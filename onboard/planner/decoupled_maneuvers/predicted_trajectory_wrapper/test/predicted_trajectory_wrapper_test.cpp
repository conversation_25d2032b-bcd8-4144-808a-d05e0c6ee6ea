#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_wrapper.h"

#include <gtest/gtest.h>

#include "prediction_protos/stationary_intention.pb.h"

namespace planner {
namespace {
using ObjectType = voy::perception::ObjectType;
}  // namespace

TEST(PredictedTrajectoryWrapperTest, EmptyNonPrimaryTrajectoryTest) {
  voy::TrackedObject tracked_object;
  tracked_object.set_object_type(ObjectType::VEHICLE);
  tracked_object.set_id(1);
  tracked_object.set_center_x(5.0);
  tracked_object.set_length(4.2);
  tracked_object.set_width(1.9);
  tracked_object.set_height(1.2);
  tracked_object.set_velocity(10.0);

  prediction::pb::PredictedTrajectory predicted_trajectory;
  predicted_trajectory.set_id(2);

  const PredictedTrajectoryWrapper predicted_trajectory_wrapper(
      tracked_object, predicted_trajectory);

  EXPECT_EQ(1, predicted_trajectory_wrapper.object_id());
  EXPECT_EQ(1, predicted_trajectory_wrapper.tracked_object().id());
  EXPECT_DOUBLE_EQ(5.0,
                   predicted_trajectory_wrapper.tracked_object().center_x());
  EXPECT_DOUBLE_EQ(4.2, predicted_trajectory_wrapper.tracked_object().length());
  EXPECT_DOUBLE_EQ(1.9, predicted_trajectory_wrapper.tracked_object().width());
  EXPECT_DOUBLE_EQ(1.2, predicted_trajectory_wrapper.tracked_object().height());
  EXPECT_DOUBLE_EQ(10.0,
                   predicted_trajectory_wrapper.tracked_object().velocity());

  EXPECT_EQ(2, predicted_trajectory_wrapper.id());
  EXPECT_FALSE(predicted_trajectory_wrapper.is_primary_trajectory());
  EXPECT_FALSE(predicted_trajectory_wrapper.is_multi_output_trajectory());
  EXPECT_TRUE(predicted_trajectory_wrapper.empty());
  EXPECT_EQ(0, predicted_trajectory_wrapper.size());
  EXPECT_TRUE(predicted_trajectory_wrapper.begin() ==
              predicted_trajectory_wrapper.end());
  EXPECT_TRUE(predicted_trajectory_wrapper.predicted_poses().empty());
}

TEST(PredictedTrajectoryWrapperTest, NonEmptyPrimaryTrajectoryTest) {
  voy::TrackedObject tracked_object;
  tracked_object.set_object_type(ObjectType::VEHICLE);
  tracked_object.set_id(1);
  tracked_object.set_center_x(5.0);
  tracked_object.set_length(4.2);
  tracked_object.set_width(1.9);
  tracked_object.set_height(1.2);
  tracked_object.set_velocity(10.0);

  prediction::pb::PredictedTrajectory predicted_trajectory;
  predicted_trajectory.set_id(2);
  predicted_trajectory.set_is_multi_output_trajectory(true);
  predicted_trajectory.set_probability_in_ppm(700000);

  const int64_t start_ts_in_ms = 1234;
  for (int i = 0; i < 100; i++) {
    auto* traj_pose = predicted_trajectory.add_traj_poses();
    traj_pose->set_x_pos(-5 + i * 0.1);
    traj_pose->set_y_pos(10);
    traj_pose->set_z_pos(1);
    traj_pose->set_heading(0);
    traj_pose->set_timestamp(start_ts_in_ms + i * 100);
    traj_pose->set_speed(3.5);
  }

  const PredictedTrajectoryWrapper predicted_trajectory_wrapper(
      tracked_object, predicted_trajectory, /*is_primary_trajectory=*/true);

  EXPECT_EQ(1, predicted_trajectory_wrapper.object_id());
  EXPECT_EQ(1, predicted_trajectory_wrapper.tracked_object().id());
  EXPECT_DOUBLE_EQ(5.0,
                   predicted_trajectory_wrapper.tracked_object().center_x());
  EXPECT_DOUBLE_EQ(4.2, predicted_trajectory_wrapper.tracked_object().length());
  EXPECT_DOUBLE_EQ(1.9, predicted_trajectory_wrapper.tracked_object().width());
  EXPECT_DOUBLE_EQ(1.2, predicted_trajectory_wrapper.tracked_object().height());
  EXPECT_DOUBLE_EQ(10.0,
                   predicted_trajectory_wrapper.tracked_object().velocity());

  EXPECT_EQ(2, predicted_trajectory_wrapper.id());
  EXPECT_TRUE(predicted_trajectory_wrapper.is_primary_trajectory());
  EXPECT_TRUE(predicted_trajectory_wrapper.is_multi_output_trajectory());
  EXPECT_FALSE(predicted_trajectory_wrapper.empty());
  EXPECT_EQ(100, predicted_trajectory_wrapper.size());
  EXPECT_TRUE(predicted_trajectory_wrapper.begin() + 100 ==
              predicted_trajectory_wrapper.end());
  EXPECT_EQ(100, predicted_trajectory_wrapper.predicted_poses().size());
  EXPECT_DOUBLE_EQ(0.7, predicted_trajectory_wrapper.Likelihood());
  EXPECT_FALSE(predicted_trajectory_wrapper.IsStationary());
}

TEST(PredictedTrajectoryWrapperTest,
     ComputePoseAtTimestampStationaryTrajectory) {
  voy::TrackedObject tracked_object;
  tracked_object.set_id(18845);
  tracked_object.set_center_x(0);
  tracked_object.set_center_y(3);
  tracked_object.set_length(1.8);
  tracked_object.set_width(0.4);
  tracked_object.set_height(1.0);
  tracked_object.set_heading(0.0);
  tracked_object.set_velocity(2.4);

  prediction::pb::PredictedTrajectory predicted_trajectory_proto;
  predicted_trajectory_proto.set_id(2);
  for (int i = 0; i < 10; ++i) {
    int64_t timestamp = i * 100;
    planner::pb::TrajectoryPose* traj_pose =
        predicted_trajectory_proto.add_traj_poses();
    traj_pose->set_timestamp(timestamp);
    traj_pose->set_x_pos(tracked_object.center_x());
    traj_pose->set_y_pos(tracked_object.center_y());
    traj_pose->set_speed(tracked_object.velocity());
    traj_pose->set_heading(tracked_object.heading());
  }
  prediction::pb::StationaryIntention stationary_intention;
  stationary_intention.set_stationary_intention_type(
      prediction::pb::StationaryIntentionType::STATIONARY_NOT_TO_MOVE);
  PredictedTrajectoryWrapper predicted_trajectory(
      tracked_object, predicted_trajectory_proto,
      /*is_primary_trajectory=*/true,
      /*is_agent_stationary=*/std::move(stationary_intention));
  EXPECT_TRUE(predicted_trajectory.IsStationary());

  planner::pb::TrajectoryPose interpolated_pose_1 =
      predicted_trajectory.ComputePoseAtTimestamp(150);
  EXPECT_EQ(interpolated_pose_1.timestamp(), 150);
  EXPECT_EQ(interpolated_pose_1.x_pos(), tracked_object.center_x());
  EXPECT_EQ(interpolated_pose_1.y_pos(), tracked_object.center_y());
  EXPECT_EQ(interpolated_pose_1.speed(), tracked_object.velocity());
  EXPECT_EQ(interpolated_pose_1.heading(), tracked_object.heading());

  planner::pb::TrajectoryPose interpolated_pose_2 =
      predicted_trajectory.ComputePoseAtTimestamp(310);
  EXPECT_EQ(interpolated_pose_2.timestamp(), 310);
  EXPECT_EQ(interpolated_pose_2.x_pos(), tracked_object.center_x());
  EXPECT_EQ(interpolated_pose_2.y_pos(), tracked_object.center_y());
  EXPECT_EQ(interpolated_pose_2.speed(), tracked_object.velocity());
  EXPECT_EQ(interpolated_pose_2.heading(), tracked_object.heading());

  planner::pb::TrajectoryPose interpolated_pose_3 =
      predicted_trajectory.ComputePoseAtTimestamp(840);
  EXPECT_EQ(interpolated_pose_3.timestamp(), 840);
  EXPECT_EQ(interpolated_pose_3.x_pos(), tracked_object.center_x());
  EXPECT_EQ(interpolated_pose_3.y_pos(), tracked_object.center_y());
  EXPECT_EQ(interpolated_pose_3.speed(), tracked_object.velocity());
  EXPECT_EQ(interpolated_pose_3.heading(), tracked_object.heading());
}

TEST(PredictedTrajectoryWrapperTest,
     ComputePoseAtTimestampNonStationaryTrajectory) {
  voy::TrackedObject tracked_object;
  tracked_object.set_id(18845);
  tracked_object.set_center_x(2);
  tracked_object.set_center_y(3);
  tracked_object.set_length(1.8);
  tracked_object.set_width(0.4);
  tracked_object.set_height(1.0);
  tracked_object.set_heading(0.0);
  tracked_object.set_velocity(2.4);

  prediction::pb::PredictedTrajectory predicted_trajectory_proto;
  predicted_trajectory_proto.set_id(2);
  for (int i = 0; i < 10; ++i) {
    int64_t timestamp = i * 100;
    planner::pb::TrajectoryPose* traj_pose =
        predicted_trajectory_proto.add_traj_poses();
    traj_pose->set_timestamp(timestamp);
    traj_pose->set_x_pos(2 + i * 0.1);
    traj_pose->set_y_pos(3 + i * 0.2);
    traj_pose->set_speed(2.4 - i * 0.3);
    traj_pose->set_heading(i * 0.4);
  }
  PredictedTrajectoryWrapper predicted_trajectory(
      tracked_object, predicted_trajectory_proto,
      /*is_primary_trajectory=*/true);
  EXPECT_FALSE(predicted_trajectory.IsStationary());

  planner::pb::TrajectoryPose interpolated_pose_1 =
      predicted_trajectory.ComputePoseAtTimestamp(150);
  EXPECT_EQ(interpolated_pose_1.timestamp(), 150);
  EXPECT_DOUBLE_EQ(interpolated_pose_1.x_pos(), 2.15);
  EXPECT_DOUBLE_EQ(interpolated_pose_1.y_pos(), 3.3);
  EXPECT_DOUBLE_EQ(interpolated_pose_1.speed(), 1.95);
  EXPECT_DOUBLE_EQ(interpolated_pose_1.heading(), 0.6);

  planner::pb::TrajectoryPose interpolated_pose_2 =
      predicted_trajectory.ComputePoseAtTimestamp(310);
  EXPECT_EQ(interpolated_pose_2.timestamp(), 310);
  EXPECT_DOUBLE_EQ(interpolated_pose_2.x_pos(), 2.3);
  EXPECT_DOUBLE_EQ(interpolated_pose_2.y_pos(), 3.6);
  EXPECT_DOUBLE_EQ(interpolated_pose_2.speed(), 1.5);
  EXPECT_DOUBLE_EQ(interpolated_pose_2.heading(), 1.2);

  planner::pb::TrajectoryPose interpolated_pose_3 =
      predicted_trajectory.ComputePoseAtTimestamp(840);
  EXPECT_EQ(interpolated_pose_3.timestamp(), 840);
  EXPECT_DOUBLE_EQ(interpolated_pose_3.x_pos(), 2.84);
  EXPECT_DOUBLE_EQ(interpolated_pose_3.y_pos(), 4.68);
  EXPECT_DOUBLE_EQ(interpolated_pose_3.speed(), -0.12);
  EXPECT_DOUBLE_EQ(interpolated_pose_3.heading(), 3.36);
}

TEST(PredictedTrajectoryWrapperTest,
     GetTrajectoryMaximumSpeedConstNegativeAccel) {
  constexpr double kInitSpeedInMps = 5.0;
  constexpr double kConstAccelInMpss = -1.0;
  voy::TrackedObject tracked_object;
  tracked_object.set_id(18845);
  tracked_object.set_center_x(2);
  tracked_object.set_center_y(3);
  tracked_object.set_length(1.8);
  tracked_object.set_width(0.4);
  tracked_object.set_height(1.0);
  tracked_object.set_heading(0.0);
  tracked_object.set_velocity(kInitSpeedInMps);

  prediction::pb::PredictedTrajectory predicted_trajectory_proto;
  predicted_trajectory_proto.set_id(2);

  for (int i = 0; i < 3; ++i) {
    int64_t timestamp = i * 100;
    int64_t time_in_s = i;
    planner::pb::TrajectoryPose* traj_pose =
        predicted_trajectory_proto.add_traj_poses();
    traj_pose->set_timestamp(timestamp);
    traj_pose->set_x_pos(kInitSpeedInMps * time_in_s +
                         0.5 * kConstAccelInMpss * time_in_s * time_in_s);
    traj_pose->set_y_pos(0);
    traj_pose->set_speed(kInitSpeedInMps + kConstAccelInMpss * time_in_s);
  }
  PredictedTrajectoryWrapper predicted_trajectory(
      tracked_object, predicted_trajectory_proto,
      /*is_primary_trajectory=*/true);
  EXPECT_DOUBLE_EQ(kInitSpeedInMps,
                   predicted_trajectory.GetTrajectoryMaximumSpeed());
}

TEST(PredictedTrajectoryWrapperTest,
     GetTrajectoryMaximumSpeedConstPositiveAccel) {
  constexpr double kInitSpeedInMps = 5.0;
  constexpr double kConstAccelInMpss = 1.0;
  voy::TrackedObject tracked_object;
  tracked_object.set_id(18845);
  tracked_object.set_center_x(2);
  tracked_object.set_center_y(3);
  tracked_object.set_length(1.8);
  tracked_object.set_width(0.4);
  tracked_object.set_height(1.0);
  tracked_object.set_heading(0.0);
  tracked_object.set_velocity(kInitSpeedInMps);

  prediction::pb::PredictedTrajectory predicted_trajectory_proto;
  predicted_trajectory_proto.set_id(2);
  constexpr int kPoses = 3;
  for (int i = 0; i < kPoses; ++i) {
    int64_t timestamp = i * 100;
    int64_t time_in_s = i;
    planner::pb::TrajectoryPose* traj_pose =
        predicted_trajectory_proto.add_traj_poses();
    traj_pose->set_timestamp(timestamp);
    traj_pose->set_x_pos(kInitSpeedInMps * time_in_s +
                         0.5 * kConstAccelInMpss * time_in_s * time_in_s);
    traj_pose->set_y_pos(0);
    traj_pose->set_speed(kInitSpeedInMps + kConstAccelInMpss * time_in_s);
  }
  PredictedTrajectoryWrapper predicted_trajectory(
      tracked_object, predicted_trajectory_proto,
      /*is_primary_trajectory=*/true);
  EXPECT_DOUBLE_EQ(kInitSpeedInMps + (kPoses - 1) * kConstAccelInMpss,
                   predicted_trajectory.GetTrajectoryMaximumSpeed());
}

TEST(PredictedTrajectoryWrapperTest,
     GetTrajectoryMaximumSpeedConstNegativeJerk) {
  constexpr double kInitSpeedInMps = 5.0;
  constexpr double kInitAccelInMpss = 5.0;
  constexpr double kConstJerkInMpsss = -1.0;
  voy::TrackedObject tracked_object;
  tracked_object.set_id(18845);
  tracked_object.set_center_x(2);
  tracked_object.set_center_y(3);
  tracked_object.set_length(1.8);
  tracked_object.set_width(0.4);
  tracked_object.set_height(1.0);
  tracked_object.set_heading(0.0);
  tracked_object.set_velocity(kInitSpeedInMps);

  prediction::pb::PredictedTrajectory predicted_trajectory_proto;
  predicted_trajectory_proto.set_id(2);

  for (int i = 0; i < 10; ++i) {
    int64_t timestamp = i * 100;
    int64_t time_in_s = i;
    planner::pb::TrajectoryPose* traj_pose =
        predicted_trajectory_proto.add_traj_poses();
    traj_pose->set_timestamp(timestamp);
    traj_pose->set_x_pos(kInitSpeedInMps * time_in_s +
                         0.5 * kInitAccelInMpss * std::pow(time_in_s, 2) +
                         1 / 6.0 * kConstJerkInMpsss * std::pow(time_in_s, 3));
    traj_pose->set_y_pos(0);
    traj_pose->set_speed(kInitSpeedInMps + kInitAccelInMpss * time_in_s +
                         0.5 * kConstJerkInMpsss * std::pow(time_in_s, 2));
  }
  PredictedTrajectoryWrapper predicted_trajectory(
      tracked_object, predicted_trajectory_proto,
      /*is_primary_trajectory=*/true);
  constexpr int kMaxSpeedTimeInSecond = 5;
  const double expected_max_speed =
      kInitSpeedInMps + kInitAccelInMpss * kMaxSpeedTimeInSecond +
      0.5 * kConstJerkInMpsss * std::pow(kMaxSpeedTimeInSecond, 2);
  EXPECT_DOUBLE_EQ(expected_max_speed,
                   predicted_trajectory.GetTrajectoryMaximumSpeed());
}

TEST(PredictedTrajectoryWrapperTest, StratToMoveTest) {
  voy::TrackedObject tracked_object;
  tracked_object.set_id(18845);
  tracked_object.set_center_x(2);
  tracked_object.set_center_y(3);
  tracked_object.set_length(1.8);
  tracked_object.set_width(0.4);
  tracked_object.set_height(1.0);
  tracked_object.set_heading(0.0);
  tracked_object.set_velocity(2.4);

  prediction::pb::PredictedTrajectory predicted_trajectory_proto;
  predicted_trajectory_proto.set_id(2);
  for (int i = 0; i < 10; ++i) {
    int64_t timestamp = i * 100;
    planner::pb::TrajectoryPose* traj_pose =
        predicted_trajectory_proto.add_traj_poses();
    traj_pose->set_timestamp(timestamp);
    traj_pose->set_x_pos(2 + i * 0.1);
    traj_pose->set_y_pos(3 + i * 0.2);
    traj_pose->set_speed(2.4 - i * 0.3);
    traj_pose->set_heading(i * 0.4);
  }
  prediction::pb::StationaryIntention stationary_intention;
  stationary_intention.set_stationary_intention_type(
      prediction::pb::STATIONARY_TO_MOVE);
  stationary_intention.set_time_to_move(2.5);
  stationary_intention.set_standard_deviation_of_time_to_move(1.0);
  PredictedTrajectoryWrapper predicted_trajectory(
      tracked_object, predicted_trajectory_proto,
      /*is_primary_trajectory=*/true, std::move(stationary_intention));

  EXPECT_TRUE(predicted_trajectory.MightStartToMoveSoon());
  EXPECT_FALSE(predicted_trajectory.IsStationary());
}

TEST(PredictedTrajectoryWrapperTest, ProtoConversion) {
  voy::TrackedObject tracked_object;
  tracked_object.set_id(18845);
  tracked_object.set_center_x(2);
  tracked_object.set_center_y(3);
  tracked_object.set_length(1.8);
  tracked_object.set_width(0.4);
  tracked_object.set_height(1.0);
  tracked_object.set_heading(0.0);
  tracked_object.set_velocity(2.4);

  prediction::pb::PredictedTrajectory predicted_trajectory_proto;
  predicted_trajectory_proto.set_id(2);
  for (int i = 0; i < 10; ++i) {
    int64_t timestamp = i * 100;
    planner::pb::TrajectoryPose* traj_pose =
        predicted_trajectory_proto.add_traj_poses();
    traj_pose->set_timestamp(timestamp);
    traj_pose->set_x_pos(2 + i * 0.1);
    traj_pose->set_y_pos(3 + i * 0.2);
    traj_pose->set_speed(2.4 - i * 0.3);
    traj_pose->set_heading(i * 0.4);
  }
  prediction::pb::StationaryIntention stationary_intention;
  stationary_intention.set_stationary_intention_type(
      prediction::pb::STATIONARY_TO_MOVE);
  stationary_intention.set_time_to_move(2.5);
  stationary_intention.set_standard_deviation_of_time_to_move(1.0);
  PredictedTrajectoryWrapper predicted_trajectory_wrapper(
      tracked_object, predicted_trajectory_proto,
      /*is_primary_trajectory=*/true, std::move(stationary_intention));

  // Test ToProto().
  pb::PredictedTrajectoryWrapper predicted_trajectory_wrapper_proto =
      predicted_trajectory_wrapper.ToProto();
  // Check tracked_object.
  EXPECT_EQ(predicted_trajectory_wrapper.tracked_object().id(),
            predicted_trajectory_wrapper_proto.tracked_object().id());
  EXPECT_DOUBLE_EQ(
      predicted_trajectory_wrapper.tracked_object().center_x(),
      predicted_trajectory_wrapper_proto.tracked_object().center_x());
  EXPECT_DOUBLE_EQ(
      predicted_trajectory_wrapper.tracked_object().velocity(),
      predicted_trajectory_wrapper_proto.tracked_object().velocity());
  // Check predicted_trajectory.
  EXPECT_EQ(
      predicted_trajectory_wrapper.predicted_trajectory_proto().ByteSizeLong(),
      predicted_trajectory_wrapper_proto.predicted_trajectory().ByteSizeLong());
  EXPECT_EQ(predicted_trajectory_wrapper.predicted_trajectory_proto()
                .traj_poses_size(),
            predicted_trajectory_wrapper_proto.predicted_trajectory()
                .traj_poses_size());
  EXPECT_EQ(predicted_trajectory_wrapper.predicted_trajectory_proto()
                .traj_poses(0)
                .x_pos(),
            predicted_trajectory_wrapper_proto.predicted_trajectory()
                .traj_poses(0)
                .x_pos());
  // Check is_primary_trajectory.
  EXPECT_EQ(predicted_trajectory_wrapper.is_primary_trajectory(),
            predicted_trajectory_wrapper_proto.is_primary_trajectory());
  // Check object_stationary_intention.
  EXPECT_EQ(predicted_trajectory_wrapper.StationaryIntention()
                .stationary_intention_type(),
            predicted_trajectory_wrapper_proto.object_stationary_intention()
                .stationary_intention_type());
  EXPECT_DOUBLE_EQ(
      predicted_trajectory_wrapper.StationaryIntention().stm_prob(),
      predicted_trajectory_wrapper_proto.object_stationary_intention()
          .stm_prob());
  // Check trajectory_motion_type.
  EXPECT_EQ(predicted_trajectory_wrapper.trajectory_motion_type(),
            predicted_trajectory_wrapper_proto.trajectory_motion_type());

  // Test FromProto().
  PredictedTrajectoryWrapper predicted_trajectory_wrapper2(
      predicted_trajectory_wrapper_proto);
  // Check tracked_object.
  EXPECT_EQ(predicted_trajectory_wrapper.tracked_object().id(),
            predicted_trajectory_wrapper2.tracked_object().id());
  EXPECT_DOUBLE_EQ(predicted_trajectory_wrapper.tracked_object().center_x(),
                   predicted_trajectory_wrapper2.tracked_object().center_x());
  EXPECT_DOUBLE_EQ(predicted_trajectory_wrapper.tracked_object().velocity(),
                   predicted_trajectory_wrapper2.tracked_object().velocity());
  // Check predicted_trajectory.
  EXPECT_EQ(
      predicted_trajectory_wrapper.predicted_trajectory_proto().ByteSizeLong(),
      predicted_trajectory_wrapper2.predicted_trajectory_proto()
          .ByteSizeLong());
  EXPECT_EQ(predicted_trajectory_wrapper.predicted_trajectory_proto()
                .traj_poses_size(),
            predicted_trajectory_wrapper2.predicted_trajectory_proto()
                .traj_poses_size());
  if (predicted_trajectory_wrapper.predicted_trajectory_proto()
          .traj_poses_size() > 0) {
    EXPECT_EQ(predicted_trajectory_wrapper.predicted_trajectory_proto()
                  .traj_poses(0)
                  .x_pos(),
              predicted_trajectory_wrapper2.predicted_trajectory_proto()
                  .traj_poses(0)
                  .x_pos());
    EXPECT_EQ(predicted_trajectory_wrapper.predicted_trajectory_proto()
                  .traj_poses(0)
                  .y_pos(),
              predicted_trajectory_wrapper2.predicted_trajectory_proto()
                  .traj_poses(0)
                  .y_pos());
    EXPECT_EQ(predicted_trajectory_wrapper.predicted_trajectory_proto()
                  .traj_poses(0)
                  .speed(),
              predicted_trajectory_wrapper2.predicted_trajectory_proto()
                  .traj_poses(0)
                  .speed());
  }
  // Check is_primary_trajectory.
  EXPECT_EQ(predicted_trajectory_wrapper.is_primary_trajectory(),
            predicted_trajectory_wrapper2.is_primary_trajectory());
  // Check object_stationary_intention.
  EXPECT_EQ(predicted_trajectory_wrapper.StationaryIntention()
                .stationary_intention_type(),
            predicted_trajectory_wrapper2.StationaryIntention()
                .stationary_intention_type());
  EXPECT_DOUBLE_EQ(
      predicted_trajectory_wrapper.StationaryIntention().stm_prob(),
      predicted_trajectory_wrapper2.StationaryIntention().stm_prob());
  // Check trajectory_motion_type.
  EXPECT_EQ(predicted_trajectory_wrapper.trajectory_motion_type(),
            predicted_trajectory_wrapper2.trajectory_motion_type());
  // Check other interal variables.
  EXPECT_EQ(predicted_trajectory_wrapper.IsStationary(),
            predicted_trajectory_wrapper2.IsStationary());
  EXPECT_EQ(predicted_trajectory_wrapper.predicted_poses().size(),
            predicted_trajectory_wrapper2.predicted_poses().size());
  if (!predicted_trajectory_wrapper.predicted_poses().empty()) {
    EXPECT_EQ(
        predicted_trajectory_wrapper.predicted_poses().front().traj_id(),
        predicted_trajectory_wrapper2.predicted_poses().front().traj_id());
    EXPECT_EQ(predicted_trajectory_wrapper.predicted_trajectory_proto()
                  .traj_poses()
                  .size(),
              predicted_trajectory_wrapper2.predicted_trajectory_proto()
                  .traj_poses()
                  .size());
  }
}

}  // namespace planner
