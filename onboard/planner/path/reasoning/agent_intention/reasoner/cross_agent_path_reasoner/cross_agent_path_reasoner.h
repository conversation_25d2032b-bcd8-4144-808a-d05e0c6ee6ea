#ifndef ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_REASONER_CROSS_AGENT_PATH_REASONER_CROSS_AGENT_PATH_REASONER_H_
#define ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_REASONER_CROSS_AGENT_PATH_REASONER_CROSS_AGENT_PATH_REASONER_H_

#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

#include "planner/path/reasoning/agent_intention/reasoner/abstract_reasoner.h"
#include "planner/path/reasoning/agent_intention/reasoner/ego_context_info.h"

namespace planner {
namespace path {

// CrossAgentPathReasoner represents that the agent moves from one side of the
// road to the other side, and it will hard blocking the ego lane generally.
// Most crossing scenarios can give ignore or yield decision directly. But we
// also need to nudge the crossing agent at crosswalk or unprotected left turn
// for soft blocking.
class CrossAgentPathReasoner : public AbstractReasoner {
 public:
  explicit CrossAgentPathReasoner(const EgoContextInfo& ego_context_info);

  virtual ~CrossAgentPathReasoner() = default;

  bool ShouldInvokePreReason(
      const LateralBlockingStateMetaData& blocking_state_meta_data,
      const AgentInLaneStates& agent_inlane_state,
      pb::BehaviorType behavior_type, bool is_stationary) const final {
    (void)agent_inlane_state;
    (void)behavior_type;
    (void)is_stationary;
    return blocking_state_meta_data.reasoning_info.is_crossing() ||
           blocking_state_meta_data.reasoning_info.is_cyclist_semi_crossing();
  }
  bool ShouldInvokePreReason(
      const LateralBlockingStateMetaData& blocking_state_meta_data,
      const ObjectOccupancyState& object_occupancy_state,
      pb::BehaviorType behavior_type, bool is_stationary) const final {
    (void)object_occupancy_state;
    (void)behavior_type;
    (void)is_stationary;
    return blocking_state_meta_data.reasoning_info.is_crossing() ||
           blocking_state_meta_data.reasoning_info.is_cyclist_semi_crossing();
  }
  bool ShouldInvokePreRepulsionReason(
      const LateralBlockingStateMetaData& blocking_state_meta_data,
      voy::perception::ObjectType agent_type, pb::BehaviorType behavior_type,
      bool is_stationary, pb::AgentImaginaryActualType imaginary_actual_type,
      std::string& debug_str) const final {
    (void)blocking_state_meta_data;
    (void)agent_type;
    (void)behavior_type;
    (void)is_stationary;
    (void)imaginary_actual_type;
    (void)debug_str;

    return FLAGS_planning_enable_cross_left_turn_agent_repulsion;
  }

  std::vector<RepulsionMetaData> PreRepulsionReason(
      const RobotStateSnapshot& robot_state_snapshot,
      const AgentInLaneStates& agent_inlane_state,
      const LateralBlockingStateMetaData& blocking_state_data,
      const std::unordered_map<int, LateralBlockingStateMetaData>&
          trajectory_blocking_state_data_map,
      const std::optional<planner::pb::YieldIntentionData>&
          yield_intention_data,
      const std::optional<pb::Path>& reusable_last_path,
      const std::optional<speed::Profile>& reusable_last_speed_profile,
      double repulsion_required_lat_gap,
      const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
      const std::vector<path::FrameAnalyzerResult>& frame_analyzer_results,
      const TrajectoryIntentionMetaData& trajectory_intention_data,
      std::string& debug_str) const;

  std::vector<RepulsionMetaData> PreRepulsionReason(
      const RobotStateSnapshot& robot_state_snapshot,
      const ObjectOccupancyState& object_occupancy_state,
      const LateralBlockingStateMetaData& blocking_state_data,
      const std::unordered_map<int, LateralBlockingStateMetaData>&
          trajectory_blocking_state_data_map,
      const std::optional<planner::pb::YieldIntentionData>&
          yield_intention_data,
      const std::optional<pb::Path>& reusable_last_path,
      const std::optional<speed::Profile>& reusable_last_speed_profile,
      double repulsion_required_lat_gap,
      const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
      const std::vector<path::FrameAnalyzerResult>& frame_analyzer_results,
      std::string& debug_str) const;

  bool ShouldInvokePostReason(
      const LateralBlockingStateMetaData& blocking_state_meta_data,
      voy::perception::ObjectType agent_type, pb::BehaviorType behavior_type,
      bool is_stationary) const final {
    (void)agent_type;
    (void)behavior_type;
    (void)is_stationary;
    return (blocking_state_meta_data.reasoning_info.is_crossing() ||
            blocking_state_meta_data.reasoning_info
                .is_cyclist_semi_crossing()) &&
           !blocking_state_meta_data.reasoning_info.is_cyclist_overtaking();
  }

  bool PostReason(const AgentInLaneStates& agent_inlane_state,
                  const LateralBlockingStateMetaData& blocking_state_data,
                  const TrajectoryIntentionMetaData& trajectory_intention_data,
                  IntentionResultMetaData& intention_result) const final;

  bool PostReason(const ObjectOccupancyState& object_occupancy_state,
                  const LateralBlockingStateMetaData& blocking_state_data,
                  const TrajectoryIntentionMetaData& trajectory_intention_data,
                  IntentionResultMetaData& intention_result) const final;

 protected:
  bool PreReasonForSoftBlockingSequences(
      const AgentInLaneStates& agent_inlane_state,
      const LateralBlockingStateMetaData& blocking_state_data,
      TrajectoryIntentionMetaData& trajectory_intention_data) const final;

  bool PreReasonForHardBlockingSequences(
      const AgentInLaneStates& agent_inlane_state,
      const LateralBlockingStateMetaData& blocking_state_data,
      TrajectoryIntentionMetaData& trajectory_intention_data) const final;

  bool PreReasonForMixedBlockingSequences(
      const AgentInLaneStates& agent_inlane_state,
      const LateralBlockingStateMetaData& blocking_state_data,
      TrajectoryIntentionMetaData& trajectory_intention_data) const final;

  bool PreReasonForSoftBlockingSequences(
      const ObjectOccupancyState& object_occupancy_state,
      const LateralBlockingStateMetaData& blocking_state_data,
      TrajectoryIntentionMetaData& trajectory_intention_data) const final;

  bool PreReasonForHardBlockingSequences(
      const ObjectOccupancyState& object_occupancy_state,
      const LateralBlockingStateMetaData& blocking_state_data,
      TrajectoryIntentionMetaData& trajectory_intention_data) const final;

  bool PreReasonForMixedBlockingSequences(
      const ObjectOccupancyState& object_occupancy_state,
      const LateralBlockingStateMetaData& blocking_state_data,
      TrajectoryIntentionMetaData& trajectory_intention_data) const final;

 private:
  pb::ObstacleSTBufferConfig GetSTBufferConfig(
      const pb::TrajectoryReasoningInfo& reasoning_info) const;

  bool SoftenBlockingSequencesForLeftTurnYieldingAgent(
      const pb::TrajectoryReasoningInfo& reasoning_info,
      const std::vector<pb::BlockingSequence>& blocking_sequences,
      TrajectoryIntentionMetaData& trajectory_intention_data) const;
};

}  // namespace path
}  // namespace planner

#endif  // ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_REASONER_CROSS_AGENT_PATH_REASONER_CROSS_AGENT_PATH_REASONER_H_
