load("//bazel:defs.bzl", "voy_cc_test")

package(default_visibility = ["//visibility:public"])

voy_cc_test(
    name = "free_stop_control_test",
    srcs = ["free_stop_control_test.cpp"],
    copts = [
        "-x",
        "c++",
        "-std=c++14",
    ],
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_long_function",
        "//offboard/fallback_ads/long_function/stop_in_lane:free_stop_control",
        "@voy-sdk//:gtest",
    ],
)

voy_cc_test(
    name = "time_to_collision_test",
    srcs = ["time_to_collision_test.cpp"],
    copts = [
        "-x",
        "c++",
        "-std=c++14",
    ],
    deps = [
        "//offboard/fallback_ads/common_math_library:vehicle_parameters",
        "//offboard/fallback_ads/long_function/stop_in_lane:time_to_collision",
        "@voy-sdk//:gtest",
    ],
)
