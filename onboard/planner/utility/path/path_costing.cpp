#include "planner/utility/path/path_costing.h"

#include <algorithm>
#include <cmath>
#include <utility>
#include <vector>

#include "geometry/model/oriented_box_with_cache.h"
#include "geometry/model/segment_with_cache.h"
#include "math/angle.h"
#include "math/distance_info.h"
#include "planner/behavior/reasoners/util/curvature_driven_speed_analyzer.h"
#include "planner/utility/primitive/primitive_definition/path_primitive.h"
#include "voy_protos/math.pb.h"

namespace planner {

namespace path {

using primitive::GeometricPaddingPrimitive;
using primitive::PathPrimitives;

using StateIndex = PathFormula::StateIndex;
using ControlIndex = PathFormula::ControlIndex;
using CostingUtil = PathFormula::FormulaCostingUtil;
using FeatureParameter = PathFormula::PbFeatureParameter;

using Feature = CostingUtil::Feature;
using FeatureLet = CostingUtil::FeatureLet;
using Hamiltonian = CostingUtil::Hamiltonian;

constexpr int X_DIM = PathFormula::StateIndex::X_DIM;
[[maybe_unused]] constexpr int U_DIM = PathFormula::ControlIndex::U_DIM;

using State = PathFormula::State;
using Control = PathFormula::Control;

namespace {
constexpr int kSampleNumOnEgoBoundary = 5;

// The default weight adjust when costing.
constexpr double kConstWeightAdjust = 1.0;
// By default, we don't decay nudge weights.
constexpr double kDefaultNudgeDecayRatio = 1.0;

// When the speed is lower than this threshold, return max abs curvature for
// speed driven curvature(kMaxAbsCurvatureForSpeedDrivenCurvature) directly.
// Unit: m/s.
constexpr double kLowSpeedThresholdForSpeedDrivenCurvatureInMps = 4.21;
// The max abs curvature for speed driven curvature.
constexpr double kMaxAbsCurvatureForSpeedDrivenCurvature = 0.1;

// The type of clearance. kPositive (kNegative) means that ego should maintain a
// positive (negative) clearance from a given geometry.
enum class ClearanceType {
  kPositive = 0,
  kNegative,
};

// The type of nudge when costing for disk and box.
enum class NudgeType {
  kSafety = 0,
  kComfort,
};

// Gets recommended max abs curvature from speed.
double GetRecommendedMaxAbsCurvatureFromSpeed(double speed) {
  if (speed < kLowSpeedThresholdForSpeedDrivenCurvatureInMps) {
    return kMaxAbsCurvatureForSpeedDrivenCurvature;
  }
  const std::optional<double> recommend_max_abs_curvature =
      GetAbsCurvatureLimitFromSpeed(speed);
  return recommend_max_abs_curvature ? *recommend_max_abs_curvature
                                     : kMaxAbsCurvatureForSpeedDrivenCurvature;
}

// Computes curvature constraint.
bool ConstraintKappa(const State& x, const double max_abs_kappa,
                     Feature* feature) {
  const double kappa = x[StateIndex::KAPPA];
  const double kappa_violation =
      control_util::ComputeViolation(kappa, -max_abs_kappa, max_abs_kappa);

  feature->components.resize(1);
  feature->components[0].val = kappa_violation;
  if (!math::NearZero(feature->components[0].val)) {
    feature->components[0].jacob[StateIndex::KAPPA] = 1.0;
  }

  return true;
}

// Computes curvature rate constraint.
bool ConstraintEta(const Control& u, double max_abs_eta, Feature* feature) {
  const double eta = u[ControlIndex::ETA];
  const double eta_violation =
      control_util::ComputeViolation(eta, -max_abs_eta, max_abs_eta);

  feature->components.resize(1);
  feature->components[0].val = eta_violation;
  if (!math::NearZero(feature->components[0].val)) {
    feature->components[0].jacob[X_DIM + ControlIndex::ETA] = 1.0;
  }

  return true;
}

// Gets reference speed square value for a given point.
double GetReferenceSpeedSquare(
    const std::vector<primitive::SpeedReferenceRegionPrimitive>&
        speed_reference_regions,
    const State& x, double ego_speed_squared,
    math::geometry::PolylineCurve2dProximityCache* cached_poly) {
  if (speed_reference_regions.empty()) {
    return ego_speed_squared;
  }

  const math::EVector<2> query_pt =
      (math::EVector<2>() << x[StateIndex::X_POS], x[StateIndex::Y_POS])
          .finished();
  double dist = 0.0;
  math::EVector<2> d_dist = math::EVector<2>::Zero();
  double arclength = 0.0;
  DCHECK_NOTNULL(cached_poly)->Query(query_pt, &dist, &d_dist, &arclength);
  for (const auto& speed_reference_region : speed_reference_regions) {
    if (math::IsInRange(arclength, speed_reference_region.region.start_pos,
                        speed_reference_region.region.end_pos)) {
      if (speed_reference_region.speed_reference > 0.0) {
        // TODO(hongda): Remove the std::min logic when we have a more accurate
        // reference speed.
        return std::min(speed_reference_region.speed_reference *
                            speed_reference_region.speed_reference,
                        ego_speed_squared);
      }
      return ego_speed_squared;
    }
  }
  return ego_speed_squared;
}

// Computes approximate lateral accel violation.
bool ConstraintApproximateLateralAccel(
    const State& x, double ego_speed_squared, double max_abs_lateral_accel_mpss,
    const std::vector<primitive::SpeedReferenceRegionPrimitive>&
        speed_reference_regions,
    math::geometry::PolylineCurve2dProximityCache* cached_poly,
    Feature* feature) {
  const double speed_reference_squared = GetReferenceSpeedSquare(
      speed_reference_regions, x, ego_speed_squared, cached_poly);

  const double lateral_accel = x[StateIndex::KAPPA] * speed_reference_squared;
  const double lateral_accel_violation = control_util::ComputeViolation(
      lateral_accel, -max_abs_lateral_accel_mpss, max_abs_lateral_accel_mpss);

  feature->components.resize(1);
  feature->components[0].val = lateral_accel_violation;
  if (!math::NearZero(feature->components[0].val)) {
    feature->components[0].jacob[StateIndex::KAPPA] = speed_reference_squared;
  }

  return true;
}

// Computes approximate lateral acceleration effort.
bool EffortApproximateLateralAccel(
    const State& x, double ego_speed_squared,
    const std::vector<primitive::SpeedReferenceRegionPrimitive>&
        speed_reference_regions,
    math::geometry::PolylineCurve2dProximityCache* cached_poly,
    Feature* feature) {
  const double speed_reference_squared = GetReferenceSpeedSquare(
      speed_reference_regions, x, ego_speed_squared, cached_poly);

  const double lateral_accel = x[StateIndex::KAPPA] * speed_reference_squared;

  feature->components.resize(1);
  feature->components[0].val = lateral_accel;
  if (!math::NearZero(feature->components[0].val)) {
    feature->components[0].jacob[StateIndex::KAPPA] = speed_reference_squared;
  }
  return true;
}

// Computes curvature effort.
bool EffortKappa(const State& x, Feature* feature) {
  const double kappa = x[StateIndex::KAPPA];

  feature->components.resize(1);
  feature->components[0].val = kappa;
  feature->components[0].jacob[StateIndex::KAPPA] = 1.0;

  return true;
}

// Computes curvature rate effort.
bool EffortEta(const Control& u, Feature* feature) {
  const double eta = u[ControlIndex::ETA];

  feature->components.resize(1);
  feature->components[0].val = eta;
  feature->components[0].jacob[X_DIM + ControlIndex::ETA] = 1.0;

  return true;
}

// Computes nominal path attraction primitive.
bool PrimitiveSoftNominalAttraction(
    const math::geometry::Point2d& front_bumper_pos, double theta,
    const vehicle_model::pb::GeometricMotionModelParam& param,
    const vehicle_model::pb::AxleRectangularMeasurement& shape_measurement,
    const std::vector<primitive::WeightAdjustedRegionPrimitive>&
        weight_adjusted_regions,
    math::geometry::PolylineCurve2dProximityCache* cached_poly,
    Feature* feature) {
  const double l = param.measurement().wheel_base() +
                   shape_measurement.front_bumper_to_front_axle();

  math::EVector<2> query_pt;
  query_pt << front_bumper_pos.x(), front_bumper_pos.y();

  double dist = 0.0;
  math::EVector<2> d_dist = math::EVector<2>::Zero();
  double arclength = 0.0;
  cached_poly->Query(query_pt, &dist, &d_dist, &arclength);

  feature->components.resize(1);
  feature->components[0].val = dist;
  feature->components[0].jacob[StateIndex::X_POS] = d_dist(0);
  feature->components[0].jacob[StateIndex::Y_POS] = d_dist(1);
  feature->components[0].jacob[StateIndex::THETA] =
      -l * sin(theta) * d_dist(0) + l * cos(theta) * d_dist(1);

  for (const auto& weight_adjusted_region : weight_adjusted_regions) {
    if (weight_adjusted_region.region.start_pos < arclength &&
        arclength < weight_adjusted_region.region.end_pos) {
      feature->components[0].weight_adjust =
          weight_adjusted_region.weight_adjust;
      break;
    }
  }
  return true;
}

// Updates the value of a clearance-related featurelet. Input parameter dist
// represents the distance from a query point to its proximal point on a
// geometry, and buffer represents the required clearance.
inline void UpdateClearanceRelatedFeatureLet(double dist, double jacob_x,
                                             double jacob_y, double jacob_theta,
                                             double buffer,
                                             double weight_adjust,
                                             ClearanceType type,
                                             FeatureLet* feature_let) {
  feature_let->val = type == ClearanceType::kNegative
                         ? std::max(dist + buffer, 0.0)
                         : std::min(dist - buffer, 0.0);
  feature_let->weight_adjust = weight_adjust;

  const bool update_jacob = (type == ClearanceType::kPositive)
                                ? (feature_let->val < 0.0)
                                : (feature_let->val > 0.0);
  if (update_jacob) {
    feature_let->jacob[StateIndex::X_POS] = jacob_x;
    feature_let->jacob[StateIndex::Y_POS] = jacob_y;
    feature_let->jacob[StateIndex::THETA] = jacob_theta;
  }
}

// Computes the polyline distance violation given the a circle-based coverage of
// the vehicle.
bool ComputeFullBodyPolylineViolationFeature(
    const std::vector<vehicle_model::DiskInfo>& ego_disks, double theta,
    ClearanceType type,
    math::geometry::PolylineCurve2dProximityCache* cached_poly,
    double side_buffer_m, Feature* feature) {
  if (cached_poly == nullptr) {
    return true;
  }

  feature->components.resize(ego_disks.size());
  const double sin_theta = sin(theta);
  const double cos_theta = cos(theta);

  for (size_t i = 0; i < ego_disks.size(); ++i) {
    math::EVector2x1 query_pt;
    query_pt << ego_disks[i].disk.center().x(), ego_disks[i].disk.center().y();
    double dist = 0.0;
    math::EVector2x1 d_dist = math::EVector2x1::Zero();
    cached_poly->Query(query_pt, &dist, &d_dist);

    const double l = ego_disks[i].lon_offset;
    const double w = ego_disks[i].lat_offset;
    const double jacob_theta = (-l * sin_theta - w * cos_theta) * d_dist(0) +
                               (l * cos_theta - w * sin_theta) * d_dist(1);
    UpdateClearanceRelatedFeatureLet(dist, d_dist(0), d_dist(1), jacob_theta,
                                     side_buffer_m + ego_disks[i].disk.radius(),
                                     /*weight_adjust=*/1.0, type,
                                     &feature->components[i]);
  }

  return true;
}

// Computes the lateral clearance-based corridor violation given the current
// state.
bool ComputeFullBodyLateralClearanceCorridorViolationFeature(
    const State& x,
    const vehicle_model::pb::AxleRectangularMeasurement& shape_measurement,
    math::pb::Side side,
    const primitive::LateralClearanceCorridorPrimitive& corridor,
    math::geometry::PolylineCurve2dProximityCache* cached_reference_polyline,
    double side_buffer_m, Feature* feature) {
  if (cached_reference_polyline == nullptr) {
    return true;
  }

  feature->components.resize(kSampleNumOnEgoBoundary);

  const bool is_side_left = side == math::pb::kLeft;
  const auto& lateral_clearances = is_side_left
                                       ? corridor.left_lateral_clearances
                                       : corridor.right_lateral_clearances;
  const double default_lateral_clearance =
      corridor.max_abs_lateral_clearance * (is_side_left ? 1 : -1);
  const ClearanceType type = side == math::pb::kLeft ? ClearanceType::kPositive
                                                     : ClearanceType::kNegative;

  const double w = shape_measurement.width() * (is_side_left ? 0.5 : -0.5);
  const double dl = shape_measurement.length() / (kSampleNumOnEgoBoundary - 1);
  const double sin_theta = sin(x[StateIndex::THETA]);
  const double cos_theta = cos(x[StateIndex::THETA]);
  const double x_anchor = x[StateIndex::X_POS] - w * sin_theta;
  const double y_anchor = x[StateIndex::Y_POS] + w * cos_theta;

  for (int i = 0; i < kSampleNumOnEgoBoundary; ++i) {
    const double l = -shape_measurement.rear_bumper_to_rear_axle() + i * dl;

    math::EVector2x1 query_pt;
    query_pt << x_anchor + l * cos_theta, y_anchor + l * sin_theta;
    double dist = 0.0;
    math::EVector2x1 d_dist = math::EVector2x1::Zero();
    cached_reference_polyline->Query(
        query_pt, corridor.resolution, corridor.reference_range.start_pos,
        corridor.reference_range.end_pos, default_lateral_clearance,
        lateral_clearances, &dist, &d_dist);

    const double jacob_theta = (-l * sin_theta - w * cos_theta) * d_dist(0) +
                               (l * cos_theta - w * sin_theta) * d_dist(1);
    UpdateClearanceRelatedFeatureLet(
        dist, d_dist(0), d_dist(1), jacob_theta, side_buffer_m,
        /*weight_adjust=*/1.0, type, &feature->components[i]);
  }

  return true;
}

// Computes the geometric padding violation given the box representation of
// ego.
bool ComputeGeometricPaddingViolationFeatureByEgoBox(
    const math::geometry::OrientedBoxWithCache2d& ego_box,
    const vehicle_model::pb::AxleRectangularMeasurement& shape_measurement,
    const std::vector<GeometricPaddingPrimitive>& geometric_padding_primitives,
    NudgeType type, double nudge_decay_ratio, double init_weight_adjust,
    Feature* feature) {
  if (geometric_padding_primitives.empty()) {
    return true;
  }

  // The rotation center of a car is its rear axle center.
  const double rotation_center_arclength =
      shape_measurement.rear_bumper_to_rear_axle();
  double dist = 0.0;
  math::geometry::Point2d d_dist(0.0, 0.0);
  double dtheta = 0.0;

  feature->components.reserve(geometric_padding_primitives.size());
  for (const auto& geometric_padding_primitive : geometric_padding_primitives) {
    const double nudge_dist =
        type == NudgeType::kSafety
            ? geometric_padding_primitive.safety_distance_m
            : geometric_padding_primitive.comfort_distance_m;
    const double weight_adjust =
        init_weight_adjust *
        (type == NudgeType::kSafety
             ? kConstWeightAdjust
             : geometric_padding_primitive.encroachment_ratio) *
        nudge_decay_ratio;

    FeatureLet component;
    component.component_id = geometric_padding_primitive.object_id;
    switch (geometric_padding_primitive.type) {
      case planner::pb::GeometricPaddingType::kDisk: {
        const auto& obj_disk = std::get<math::geometry::Disk2d>(
            geometric_padding_primitive.geometric_padding);

        const bool overlap = math::geometry::QueryOverlapInfo(
            obj_disk, ego_box, rotation_center_arclength, nudge_dist, &dist,
            &d_dist, &dtheta);
        // If no overlap, set dist to be nudge_dist so that
        // UpdateClearanceRelatedFeatureLet will skip jacob update
        if (!overlap) dist = nudge_dist;
        UpdateClearanceRelatedFeatureLet(dist, d_dist.x(), d_dist.y(), dtheta,
                                         nudge_dist, weight_adjust,
                                         ClearanceType::kPositive, &component);
        feature->components.push_back(std::move(component));
      } break;
      case planner::pb::GeometricPaddingType::kBox: {
        const auto& obj_box = std::get<math::geometry::OrientedBoxWithCache2d>(
            geometric_padding_primitive.geometric_padding);

        const bool overlap = math::geometry::QueryOverlapInfo(
            obj_box, ego_box, rotation_center_arclength, nudge_dist, &dist,
            &d_dist, &dtheta);
        // If no overlap, set dist to be nudge_dist so that
        // UpdateClearanceRelatedFeatureLet will skip jacob update
        if (!overlap) dist = nudge_dist;
        UpdateClearanceRelatedFeatureLet(dist, d_dist.x(), d_dist.y(), dtheta,
                                         nudge_dist, weight_adjust,
                                         ClearanceType::kPositive, &component);
        feature->components.push_back(std::move(component));
      } break;
      default:
        LOG(FATAL) << "Using Undefined Geometric Padding";
    }
  }
  return true;
}

// Computes the information loss due to occlusion in oncoming lane. We define
// information loss as the violation of sensor into the visibility_line.
bool PrimitiveOncomingLaneInformationLoss(
    const math::geometry::Point2d& sensor_pos, double theta,
    const std::vector<math::geometry::SegmentWithCache2d>& visibility_lines,
    const vehicle_model::pb::GeometricMotionModelParam& param,
    Feature* feature) {
  if (visibility_lines.empty()) {
    return true;
  }

  const double l = 0.5 * param.measurement().wheel_base();
  const double sin_theta = sin(theta);
  const double cos_theta = cos(theta);
  feature->components.resize(visibility_lines.size());

  for (size_t i = 0; i < visibility_lines.size(); ++i) {
    const math::ProximityQueryInfo proximity_info =
        visibility_lines[i].GetProximity(sensor_pos,
                                         math::pb::UseExtensionFlag::kAllow);

    if (proximity_info.signed_dist > 0.0 &&
        proximity_info.relative_position == math::RelativePosition::kAfter) {
      const math::geometry::Point2d d_dist(
          -visibility_lines[i].unit_direction().y(),
          visibility_lines[i].unit_direction().x());

      feature->components[i].val = proximity_info.signed_dist;
      feature->components[i].jacob[StateIndex::X_POS] = d_dist.x();
      feature->components[i].jacob[StateIndex::Y_POS] = d_dist.y();
      feature->components[i].jacob[StateIndex::THETA] =
          -l * sin_theta * d_dist.x() + l * cos_theta * d_dist.y();
    }
  }

  return true;
}

}  // namespace

control_util::pb::PathFeatureParameter MergeWithMotionModelParams(
    control_util::pb::PathFeatureParameter feature_param,
    const vehicle_model::GeometricModelWithAxleRectangularShape&
        motion_model_with_shape) {
  *feature_param.mutable_motion_model_param() = motion_model_with_shape.param();
  *feature_param.mutable_shape_measurement() =
      motion_model_with_shape.shape_measurement();
  return feature_param;
}

}  // namespace path
}  // namespace planner

namespace control_util {
template <>
planner::path::PathFormula::StepwisePrimitives
planner::path::PathFormula::GetStepwisePrimitives(
    int step, bool is_terminal, const State& x, const Control& u,
    const Primitives& primitives) const {
  (void)step;
  (void)is_terminal;
  (void)u;
  (void)primitives;
  return {.ego_representation = vehicle_model::VehicleRepresentation(
              feature_param_.shape_measurement(), x[StateIndex::X_POS],
              x[StateIndex::Y_POS], x[StateIndex::THETA],
              /*disk_numbers=*/{3})};
}

// Computes the feature given type.
template <>
void GetFeature<DdpProblemType::kPath>(
    int step, int total_step_size,
    const planner::path::PathFormula::PbCostingTerm& costing_term,
    const planner::path::PathFormula::PbFeatureParameter& feature_param,
    const planner::path::PathFormula::State& x,
    const planner::path::PathFormula::Control& u,
    const planner::path::PathFormula::StepwisePrimitives& stepwise_primitives,
    const planner::path::PathFormula::Primitives& primitives,
    planner::path::PathFormula::Feature* feature) {
  const std::vector<vehicle_model::DiskInfo>& ego_disks =
      stepwise_primitives.ego_representation.disks().at(
          vehicle_model::kDefaultEgoDiskNumber);
  const auto& ego_box = stepwise_primitives.ego_representation.box();
  bool is_terminal = (step == total_step_size);
  const auto& feature_type = costing_term.feature_type();
  switch (feature_type) {
    case planner::path::PathFormula::PbFeatureType::CONSTRAINT_KAPPA: {
      const double max_abs_kappa =
          feature_param.use_speed_driven_kappa_constraint()
              ? planner::path::GetRecommendedMaxAbsCurvatureFromSpeed(
                    std::sqrt(primitives.ego_speed_squared))
              : feature_param.max_abs_kappa();
      planner::path::ConstraintKappa(x, max_abs_kappa, feature);
      break;
    }
    case planner::path::PathFormula::PbFeatureType::CONSTRAINT_ETA:
      if (!is_terminal) {
        planner::path::ConstraintEta(u, feature_param.max_abs_eta(), feature);
      }
      break;
    case planner::path::PathFormula::PbFeatureType::
        CONSTRAINT_APPROXIMATE_LATERAL_ACCEL:
      planner::path::ConstraintApproximateLateralAccel(
          x, primitives.ego_speed_squared,
          feature_param.max_lateral_acceleration(),
          primitives.speed_reference_regions, primitives.reference_curve.get(),
          feature);
      break;
    case planner::path::PathFormula::PbFeatureType::EFFORT_KAPPA:
      planner::path::EffortKappa(x, feature);
      break;
    case planner::path::PathFormula::PbFeatureType::EFFORT_ETA:
      if (!is_terminal) {
        planner::path::EffortEta(u, feature);
      }
      break;
    case planner::path::PathFormula::PbFeatureType::
        EFFORT_APPROXIMATE_LATERAL_ACCEL:
      planner::path::EffortApproximateLateralAccel(
          x, primitives.ego_speed_squared, primitives.speed_reference_regions,
          primitives.reference_curve.get(), feature);
      break;
    case planner::path::PathFormula::PbFeatureType::
        PRIMITIVE_VIOLATION_LEFT_ROAD_CORRIDOR:
      planner::path::ComputeFullBodyLateralClearanceCorridorViolationFeature(
          x, feature_param.shape_measurement(), math::pb::kLeft,
          *DCHECK_NOTNULL(primitives.road_corridor),
          primitives.reference_curve.get(),
          feature_param.comfort_distance_to_road_boundary(), feature);
      break;
    case planner::path::PathFormula::PbFeatureType::
        PRIMITIVE_VIOLATION_RIGHT_ROAD_CORRIDOR:
      planner::path::ComputeFullBodyLateralClearanceCorridorViolationFeature(
          x, feature_param.shape_measurement(), math::pb::kRight,
          *DCHECK_NOTNULL(primitives.road_corridor),
          primitives.reference_curve.get(),
          feature_param.comfort_distance_to_road_boundary(), feature);
      break;
    case planner::path::PathFormula::PbFeatureType::
        PRIMITIVE_VIOLATION_LEFT_LANE_DRIVING_CORRIDOR:
      planner::path::ComputeFullBodyPolylineViolationFeature(
          ego_disks, x[planner::path::StateIndex::THETA],
          planner::path::ClearanceType::kNegative,
          primitives.lane_driving_corridor.left_boundary.get(),
          /*side_buffer_m=*/0.0, feature);
      break;
    case planner::path::PathFormula::PbFeatureType::
        PRIMITIVE_VIOLATION_RIGHT_LANE_DRIVING_CORRIDOR:
      planner::path::ComputeFullBodyPolylineViolationFeature(
          ego_disks, x[planner::path::StateIndex::THETA],
          planner::path::ClearanceType::kPositive,
          primitives.lane_driving_corridor.right_boundary.get(),
          /*side_buffer_m=*/0.0, feature);
      break;
    case planner::path::PathFormula::PbFeatureType::
        PRIMITIVE_VIOLATION_LEFT_NUDGE_CORRIDOR:
      planner::path::ComputeFullBodyPolylineViolationFeature(
          ego_disks, x[planner::path::StateIndex::THETA],
          planner::path::ClearanceType::kNegative,
          primitives.nudge_corridor.left_boundary.get(),
          feature_param.nudge_buffer_distance(), feature);
      break;
    case planner::path::PathFormula::PbFeatureType::
        PRIMITIVE_VIOLATION_RIGHT_NUDGE_CORRIDOR:
      planner::path::ComputeFullBodyPolylineViolationFeature(
          ego_disks, x[planner::path::StateIndex::THETA],
          planner::path::ClearanceType::kPositive,
          primitives.nudge_corridor.right_boundary.get(),
          feature_param.nudge_buffer_distance(), feature);
      break;
    case planner::path::PathFormula::PbFeatureType::
        PRIMITIVE_VIOLATION_NUDGE_SAFETY_PADDING:
      ComputeGeometricPaddingViolationFeatureByEgoBox(
          ego_box, feature_param.shape_measurement(),
          primitives.nudge_geometric_paddings,
          planner::path::NudgeType::kSafety,
          planner::path::kDefaultNudgeDecayRatio,
          /*init_weight_adjust=*/2.0, feature);
      break;

    case planner::path::PathFormula::PbFeatureType::
        PRIMITIVE_SOFT_NUDGE_COMFORT_PADDING:
      ComputeGeometricPaddingViolationFeatureByEgoBox(
          ego_box, feature_param.shape_measurement(),
          primitives.nudge_geometric_paddings,
          planner::path::NudgeType::kComfort,
          planner::path::kDefaultNudgeDecayRatio,
          /*init_weight_adjust=*/1.0, feature);
      break;
    case planner::path::PathFormula::PbFeatureType::
        PRIMITIVE_SOFT_NOMINAL_PATH_ATTRACTION:
      planner::path::PrimitiveSoftNominalAttraction(
          ego_box.center_segment().end(), x[planner::path::StateIndex::THETA],
          feature_param.motion_model_param(), feature_param.shape_measurement(),
          primitives.weight_adjusted_regions, primitives.reference_curve.get(),
          feature);
      break;
    case planner::path::PathFormula::PbFeatureType::
        PRIMITIVE_SOFT_ONCOMING_LANE_INFORMATION_LOSS:
      planner::path::PrimitiveOncomingLaneInformationLoss(
          ego_box.center_segment().GetPoint(0.5 *
                                            ego_box.center_segment().length()),
          x[planner::path::StateIndex::THETA],
          primitives.oncoming_visibility_lines,
          feature_param.motion_model_param(), feature);
      break;
    default:
      DCHECK(false) << "Unrecognized feature type!";
  }
}

}  // namespace control_util
