#include "planner/global_object_manager/planner_object_update_utility/planner_object_update_utility.h"

#include <string>
#include <utility>
#include <vector>

#include <absl/strings/str_format.h>
#include <tbb/parallel_for.h>
#include <tbb/task_arena.h>

#include "planner/constants.h"
#include "planner/world_model/planner_object/planner_object_util.h"
#include "rt_event/rt_event.h"
#include "trace/trace.h"
#include "voy_rt_event/rt_event_planner.h"
#include "voy_trace/trace_planner.h"

namespace planner {
namespace utility {
namespace {
// Use parallel_for if agent count exceeds this threshold
constexpr int kPlannerObjectsParallelProcessThreshold = 32;

// The search range for near junctions or zones of the query position point.
constexpr double kMapElementSearchingRadiusInMeter = 10.0;

// The max absolute distance in meter for considering an agent near a bus bulb.
constexpr double kAbsDistanceThresholdToConsiderNearBusBulbInMeter = 3.0;

// The lateral range within which the object is considered for overtaking.
constexpr double kRequiredGapForOvertakingInMeter = 1.0;

constexpr double kMinDistanceToRecordStartToMoveRTEventInMeter = 5.0;

// Agent/ped num thresh to indicate a highly crowed scenario. Could be used for
// eg. pruning routing diversity(alternative LF sequence)
constexpr int kHighAgentDensityTotalNumThresh = 300;
constexpr int kHighAgentDensityPedNumThresh = 100;
constexpr int kHighCrossingVruDensityNumTHresh = 40;

// Define relevant zone types for filtering
const std::vector<hdmap::Zone::ZoneType>& GetRelevantZoneTypes() {
  static const std::vector<hdmap::Zone::ZoneType> kRelevantZoneTypes = [] {
    return std::vector<hdmap::Zone::ZoneType>{hdmap::Zone::ROAD_EXIT,
                                              hdmap::Zone::BUS_BULB,
                                              hdmap::Zone::DIRECT_BUS_BULB};
  }();
  return kRelevantZoneTypes;
}

// Set Planner Object Attributes
void SetPlannerObjectAttributes(
    const prediction::pb::Agent& agent,
    const pnc_map::PncMapService& pnc_map_service,
    const RobotState& robot_state,
    const PlannerObjectsHistory& planner_objects_history,
    PlannerObject& planner_object) {
  TRACE_EVENT_SCOPE(planner, SetPlannerObjectAttributes);

  hdmap::Point agent_position;
  agent_position.set_x(agent.tracked_object().center_x());
  agent_position.set_y(agent.tracked_object().center_y());

  planner_object.set_is_in_junction(
      pnc_map_service.IsInJunction(agent_position));

  const auto dist_zone_vec =
      pnc_map_service.hdmap()->GetZonesWithDistanceByTypes(
          agent_position, kMapElementSearchingRadiusInMeter,
          GetRelevantZoneTypes());

  for (const auto& [distance, zone] : dist_zone_vec) {
    if (zone == nullptr) {
      continue;
    }
    if (distance <= 0.0) {
      if (zone->type() == hdmap::Zone::BUS_BULB) {
        planner_object.set_is_in_bus_bulb(true);
      }
      if (zone->type() == hdmap::Zone::ROAD_EXIT) {
        planner_object.set_is_in_exit_zone(true);
      }
    }
    if (planner_object.is_bus() &&
        distance <= kAbsDistanceThresholdToConsiderNearBusBulbInMeter &&
        (zone->type() == hdmap::Zone::DIRECT_BUS_BULB ||
         zone->type() == hdmap::Zone::BUS_BULB)) {
      planner_object.set_is_bus_near_bus_bulb(true);
    }
  }

  planner_object.set_is_vehicle_or_cyclist_on_wrong_way_lane(
      IsVehicleOrCyclistOnWrongWayLane(planner_object));

  // Set attribute of vehicles consisting of multiple components.
  planner_object.set_associated_vehicle_head_id(
      agent.tracked_object().associated_vehicle_head_id());
  planner_object.set_associated_vehicle_tail_id(
      agent.tracked_object().associated_vehicle_tail_id());

  const auto& ego_init_state_bbox =
      robot_state.plan_init_state_snapshot().bounding_box();

  planner_object.set_l2_distance_to_ego_m(math::geometry::Distance(
      ego_init_state_bbox, planner_object.pose_at_plan_init_ts().contour()));

  planner_object.set_ttc_from_ego_to_pose_at_plan_init_ts(
      utility::CalculateTtcFromEgoToObjectSeconds(
          ego_init_state_bbox, planner_object.pose_at_plan_init_ts(),
          robot_state.plan_init_state_snapshot().speed(),
          planner_object.l2_distance_to_ego_m()));

  planner_object.set_is_cyc_no_person(
      planner_object.HasPerceptionAttribute(
          voy::perception::CYC_WITHOUT_PERSON_ATTR) &&
      planner_object.l2_distance_to_ego_m() <
          constants::kCycWithoutPersonThresholdInMeter);

  planner_object.set_is_vegetation(
      planner_object.object_type() == voy::perception::ObjectType::UNKNOWN &&
      planner_object.HasPerceptionAttribute(voy::perception::IS_VEGETATION));

  planner_object.set_is_open_door_vehicle(
      planner_object.is_vehicle() && planner_object.HasPerceptionAttribute(
                                         voy::perception::VEHICLE_DOOR_OPEN));

  planner_object.set_optional_observed_stationary_duration_ms(
      agent.stationary_intention().observed_stationary_duration_ms());

  if (agent.stationary_intention().stationary_intention_type() ==
      prediction::pb::STATIONARY_TO_MOVE) {
    planner_object.set_has_stationary_to_move_intention(true);

    if (planner_object.l2_distance_to_ego_m() <
        kMinDistanceToRecordStartToMoveRTEventInMeter) {
      rt_event::PostRtEvent<rt_event::planner::ApproachToStartToMoveAgent>(
          "approaching to start to move agent: " +
          std::to_string(planner_object.id()));
    }
  }

  planner_object.set_stationary_intention(agent.stationary_intention());

  if (planner_object.is_vehicle()) {
    planner_object.set_was_from_exit_zone_vehicle(
        planner_objects_history.WasObjectFromExitZone(planner_object));
  }
}

// Helper to Find Primary Trajectory
const PredictedTrajectoryWrapper& FindPrimaryTrajectory(
    ObjectId object_id,
    const TbbPredictionTrajectoryMap& object_prediction_map) {
  TRACE_EVENT_SCOPE(planner, FindPrimaryTrajectory);

  const auto& predicted_trajectories = object_prediction_map.at(object_id);
  const auto it =
      std::find_if(predicted_trajectories.begin(), predicted_trajectories.end(),
                   [](const PredictedTrajectoryWrapper& traj) {
                     return traj.is_primary_trajectory();
                   });
  DCHECK(it != predicted_trajectories.end())
      << "Cannot find primary predicted trajectory";
  return *it;
}

// Create Planner Object
PlannerObject CreatePlannerObject(
    const prediction::pb::Agent& agent, int64_t plan_init_timestamp,
    int64_t prediction_timestamp,
    const std::vector<const pnc_map::Lane*>&
        tracked_object_perception_range_lanes,
    const pnc_map::PncMapService& pnc_map_service,
    const RobotState& robot_state,
    const ObjectTypeTrackingMap& object_type_tracking_map,
    const PlannerObjectsHistory& planner_objects_history,
    const TbbPredictionTrajectoryMap& object_prediction_map,
    bool is_agent_stationary) {
  TRACE_EVENT_SCOPE(planner, CreatePlannerObject);

  const auto object_id = agent.tracked_object().id();

  const auto it = object_type_tracking_map.find(agent.tracked_object().id());
  const auto history_object_type =
      (it != object_type_tracking_map.end() &&
       it->second.object_type() != voy::perception::ObjectType::UNKNOWN)
          ? it->second.object_type()
          : voy::perception::ObjectType::NOT_GIVEN;

  const auto& primary_trajectory =
      FindPrimaryTrajectory(object_id, object_prediction_map);

  const bool is_primary_stationary = primary_trajectory.IsStationary();

  PlannerObject planner_object(
      agent, prediction_timestamp,
      TrafficParticipantPose(
          agent.tracked_object(),
          primary_trajectory.ComputePoseAtTimestamp(plan_init_timestamp),
          is_primary_stationary),
      tracked_object_perception_range_lanes, is_primary_stationary,
      history_object_type);
  planner_object.set_is_stationary(is_agent_stationary);

  SetPlannerObjectAttributes(agent, pnc_map_service, robot_state,
                             planner_objects_history, planner_object);

  // TODO(DuongLe): move this to constructor
  planner_object.set_predicted_trajectories(
      object_prediction_map.at(object_id));

  return planner_object;
}

// Add Debug Information
void AddPlannerObjectDebug(const prediction::pb::Agent& agent,
                           const PlannerObject& planner_object,
                           pb::PlannerObjectDebug* planner_object_debug) {
  if (!planner_object_debug) {
    return;
  }

  auto* compensated_pose = planner_object_debug->add_compensated_poses();
  compensated_pose->set_center_z(planner_object.pose().center().z());
  compensated_pose->set_height(planner_object.pose_at_plan_init_ts().height());
  *compensated_pose->mutable_contour() =
      math::geometry::Convert<google::protobuf::RepeatedPtrField<voy::Point2d>>(
          planner_object.pose_at_plan_init_ts().contour());

  auto* per_planner_object_debug =
      &(*planner_object_debug
             ->mutable_per_planner_obj_debug())[agent.tracked_object().id()];
  per_planner_object_debug->set_prediction_agent_rank(
      agent.importance_rank_to_ego());
}
}  // namespace

void UpdatePlannerObjectMap(
    const prediction::pb::AgentList& agent_list,
    const std::unordered_set<TypedObjectId>& stationary_object_ids,
    const std::unordered_set<TypedObjectId>& irrelevant_object_ids,
    const RobotState& robot_state,
    const pnc_map::PncMapService& pnc_map_service,
    const ObjectTypeTrackingMap& object_type_tracking_map,
    const std::vector<const pnc_map::Lane*>&
        tracked_object_perception_range_lanes,
    const TbbPredictionTrajectoryMap& object_prediction_map,
    const PlannerObjectsHistory& planner_objects_history,
    PlannerObjectMap* planner_object_map,
    pb::PlannerObjectDebug* planner_object_debug) {
  DCHECK(planner_object_map != nullptr);
  TRACE_EVENT_SCOPE(planner, UpdatePlannerObjectMap);

  const int64_t plan_init_timestamp =
      robot_state.plan_init_state_snapshot().timestamp();
  const int64_t prediction_timestamp = agent_list.timestamp();
  const int agent_list_size = agent_list.agent_list_size();

  // Preallocate a vector to store PlannerObjects by index.
  // Using std::optional avoids unnecessary default construction,
  // and allows safe parallel writes during planner object generation.
  std::vector<std::optional<PlannerObject>> planner_object_list(
      agent_list_size);

  // Step 1: Parallel-safe planner object creation
  auto ProcessAgent = [&](int idx) {
    TRACE_EVENT_SCOPE(planner, UpdatePlannerObjectMap_ProcessAgent);
    const auto& agent = agent_list.agent_list(idx);
    const auto object_id = agent.tracked_object().id();
    const TypedObjectId typed_object_id(object_id,
                                        pb::ObjectSourceType::kTrackedObject);

    if (irrelevant_object_ids.find(typed_object_id) !=
        irrelevant_object_ids.end()) {
      return;
    }

    const bool is_agent_stationary =
        stationary_object_ids.find(typed_object_id) !=
        stationary_object_ids.end();

    planner_object_list[idx].emplace(CreatePlannerObject(
        agent, plan_init_timestamp, prediction_timestamp,
        tracked_object_perception_range_lanes, pnc_map_service, robot_state,
        object_type_tracking_map, planner_objects_history,
        object_prediction_map, is_agent_stationary));
  };

  if (agent_list_size > kPlannerObjectsParallelProcessThreshold) {
    tbb::parallel_for(0, agent_list_size, ProcessAgent);
  } else {
    for (int i = 0; i < agent_list_size; ++i) {
      ProcessAgent(i);
    }
  }

  planner_object_map->reserve(agent_list_size);

  // Step 2: Move results to output map and update debug info
  for (int i = 0; i < agent_list_size; ++i) {
    auto& opt_planner_object = planner_object_list[i];
    if (opt_planner_object.has_value()) {
      const ObjectId object_id = agent_list.agent_list(i).tracked_object().id();
      auto [emplaced_it, success] = planner_object_map->emplace(
          object_id, std::move(opt_planner_object.value()));
      if (success) {
        AddPlannerObjectDebug(agent_list.agent_list(i), emplaced_it->second,
                              planner_object_debug);
      }
    }
  }
}

void UpdateOvertakingInfo(const RobotState& robot_state,
                          OvertakingEvaluator* overtaking_object_evaluator,
                          PlannerObjectMap* planner_object_map) {
  TRACE_EVENT_SCOPE(planner, UpdateOvertakingInfo);
  DCHECK(overtaking_object_evaluator != nullptr);
  DCHECK(planner_object_map != nullptr);

  if (overtaking_object_evaluator->IsEnabled()) {
    const auto& last_nominal_path = robot_state.last_nominal_path();

    const math::geometry::Point2d ego_pose(
        robot_state.plan_init_state_snapshot().x(),
        robot_state.plan_init_state_snapshot().y());

    const auto& car_model_with_shape = robot_state.car_model_with_shape();
    const auto& shape_measurement = car_model_with_shape.shape_measurement();

    overtaking_object_evaluator->UpdateEgo(ego_pose, last_nominal_path,
                                           shape_measurement);

    for (auto& [_, planner_object] : *planner_object_map) {
      const auto state = overtaking_object_evaluator->GetOvertakingState(
          planner_object.pose_at_plan_init_ts(), last_nominal_path,
          kRequiredGapForOvertakingInMeter, planner_object.object_type());
      planner_object.set_is_overtaking(state == OvertakingState::kOvertaking);
      planner_object.set_is_overtaken(state ==
                                      OvertakingState::kBeingOvertaken);
    }
    overtaking_object_evaluator->Clear();
  }
}

void UpdatePlannerObjectsWithAssistInstruction(
    const std::optional<UnstuckDirectives>& unstuck_directives,
    const AssistInstruction* assist_instruction,
    std::vector<ConstructionZone>* construction_zones,
    PlannerObjectMap* planner_object_map) {
  DCHECK(planner_object_map != nullptr);
  DCHECK(construction_zones != nullptr);

  // Process Unstuck Directives
  if (unstuck_directives.has_value()) {
    const auto& instruction = unstuck_directives->assist_instruction;

    // Only set the attribute if the unstuck directive is valid.
    for (const auto& typed_id : instruction.xlane_nudge_bypass_objects) {
      if (typed_id.is_construction_zone()) {
        // No construction zone in planner object.
        continue;
      }
      auto it = planner_object_map->find(typed_id.id);
      if (it != planner_object_map->end()) {
        it->second.set_is_ra_confirmed_blockage(true);
      }
    }

    // Set forward_unstuck_target_objects if valid.
    // 1.For planner objects.
    for (const auto& typed_id : instruction.forward_unstuck_target_objects) {
      auto it = planner_object_map->find(typed_id.id);
      if (it != planner_object_map->end()) {
        it->second.set_should_reduce_critical_req_lat_gap_by_ra_confirmation(
            true);
      }
    }

    // 2.For single ctz.
    for (const auto& typed_id : instruction.forward_unstuck_target_objects) {
      // Check CTZ id consistence.
      auto ctz_it = std::find_if(
          construction_zones->begin(), construction_zones->end(),
          [&typed_id](const auto& ctz) { return ctz.id == typed_id.id; });
      if (ctz_it != construction_zones->end()) {
        ctz_it->should_reduce_critical_req_lat_gap_by_ra_confirmation = true;
      }
    }
  }
  // Process Assist Instruction
  if (assist_instruction == nullptr) {
    // No instruction received, nothing to do.
    return;
  }

  // Set the attribute for agents in the instruction. It will be used to
  // reduce critical req lat gap.
  for (const TypedObjectId& typed_id :
       assist_instruction->creep_around_objects) {
    if (typed_id.is_construction_zone()) {
      // TODO(sixian): Support RA-based creep around for CZ.

      // Find matching construction zone.
      auto ctz_it = std::find_if(
          construction_zones->begin(), construction_zones->end(),
          [&typed_id](const auto& ctz) { return ctz.id == typed_id.id; });

      if (ctz_it != construction_zones->end()) {
        ctz_it->should_reduce_critical_req_lat_gap_by_ra_confirmation = true;

        for (const auto& road_block_id : ctz_it->road_block_track_ids) {
          // For road block objects that are inside the construction zone, also
          // allow critical required lat gap reduce.
          auto road_block_iter = planner_object_map->find(road_block_id);
          if (road_block_iter != planner_object_map->end()) {
            road_block_iter->second
                .set_should_reduce_critical_req_lat_gap_by_ra_confirmation(
                    true);
          }
        }
      }

      continue;
    }

    auto it = planner_object_map->find(typed_id.id);
    if (it == planner_object_map->end() ||
        !it->second.is_primary_stationary()) {
      // Object is no longer around ego, or object started moving. Will not
      // reduce the object's critical req lat gap.
      continue;
    }

    it->second.set_should_reduce_critical_req_lat_gap_by_ra_confirmation(true);
  }
}

bool IsHighAgentDensityScenario(const PlannerObjectMap& planner_object_map) {
  const int total_object_count = planner_object_map.size();
  const int pedestrian_count = std::count_if(
      planner_object_map.begin(), planner_object_map.end(),
      [](const std::pair<ObjectId, PlannerObject>& entry) {
        return entry.second.object_type() == voy::perception::ObjectType::PED;
      });

  const bool is_high_agent_density =
      (total_object_count > kHighAgentDensityTotalNumThresh ||
       pedestrian_count > kHighAgentDensityPedNumThresh);

  if (is_high_agent_density) {
    std::string info = absl::StrFormat(
        "High agent density detected (total = %d, pedestrian = %d)",
        total_object_count, pedestrian_count);
    rt_event::PostRtEvent<rt_event::planner::HighAgentDensityScenario>(info);
  }

  return is_high_agent_density;
}

bool IsHighCrossingVruDensityScenario(
    const PlannerObjectMap& planner_object_map, const RobotState& robot_state) {
  const auto& last_norminal_path = robot_state.last_nominal_path();
  if (last_norminal_path.size() < 2) {
    return false;
  }

  const math::geometry::Point2d ego_pose(
      robot_state.plan_init_state_snapshot().x(),
      robot_state.plan_init_state_snapshot().y());
  const auto& ego_proximity = last_norminal_path.GetProximity(
      {ego_pose.x(), ego_pose.y()}, math::pb::UseExtensionFlag::kForbid);
  const double ego_ra_arc_length = ego_proximity.arc_length;
  const double ego_lateral_dist = ego_proximity.dist;

  const int crossing_vru_count = std::count_if(
      planner_object_map.begin(), planner_object_map.end(),
      [&last_norminal_path, ego_ra_arc_length,
       ego_lateral_dist](const std::pair<ObjectId, PlannerObject>& entry) {
        if (!entry.second.is_pedestrian_or_cyclist()) {
          return false;
        }
        const auto& pose = entry.second.pose_at_plan_init_ts();
        const math::geometry::Point2d center = pose.center_2d();
        const auto& agent_proximity = last_norminal_path.GetProximity(
            math::geometry::Point2d{center.x(), center.y()},
            math::pb::UseExtensionFlag::kForbid);
        const double arc_length = agent_proximity.arc_length;
        const double agent_lateral_dist = agent_proximity.dist;
        // Don't consider VRUs that's behind Ego or still far in front of Ego.
        constexpr double kMaxConsiderationMeters = 10.0;
        if (arc_length < ego_ra_arc_length ||
            arc_length > ego_ra_arc_length + kMaxConsiderationMeters ||
            std::abs(ego_lateral_dist - agent_lateral_dist) > 10.0) {
          return false;
        }

        const double reference_point_heading =
            last_norminal_path.GetInterpTheta(arc_length);
        const double relative_heading_rad =
            math::AngleDiff(pose.heading(), reference_point_heading);
        const double cross_track_speed_mps =
            pose.speed() * sin(relative_heading_rad);
        constexpr double kCrossingVruMinLateralSpeedMps = 0.5;
        return std::abs(cross_track_speed_mps) > kCrossingVruMinLateralSpeedMps;
      });

  const bool high_crossing_vru_density =
      crossing_vru_count > kHighCrossingVruDensityNumTHresh;

  if (high_crossing_vru_density) {
    rt_event::PostRtEvent<rt_event::planner::HighCrossingVruDensityScenario>();
  }

  return high_crossing_vru_density;
}

}  // namespace utility
}  // namespace planner
