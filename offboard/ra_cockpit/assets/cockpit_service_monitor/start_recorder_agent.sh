#!/bin/bash

source ./bazel/scripts/setup.sh
ulimit -c unlimited
echo "/tmp/core-%e-%t" | sudo tee /proc/sys/kernel/core_pattern

sudo setfacl -m u:$USER:rw /dev/video0  
sudo setfacl -m u:$USER:rw /dev/snd/*

if ! pgrep -x recorder_agent > /dev/null
then
    ./bazel-build/bin/offboard/ra_cockpit/recorder_agent  > /dev/null 2>&1 &
    echo "recorder_agent 已在后台启动。"
else
    echo "recorder_agent 进程已经在运行。"
fi
