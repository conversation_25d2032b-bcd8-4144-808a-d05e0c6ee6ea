#include "planner/utility/common/common_utility.h"

#include <optional>
#include <unordered_map>
#include <vector>

#include "boost/geometry.hpp"
#include "gtest/gtest.h"

#include "geometry/model/point_2d.h"
#include "geometry/model/polyline_curve.h"
#include "planner/behavior/util/lane_sequence_geometry/lane_sequence_geometry.h"
#include "planner/world_model/traffic_participant/traffic_participant_pose.h"
#include "pnc_map_service/map_elements/lane.h"
#include "pnc_map_service/map_elements/section.h"
#include "pnc_map_service/test/test_util.h"
#include "voy_protos/perception_object_type.pb.h"

namespace planner::common {

namespace {

// Read LINESTRING and return a math::geometry::PolylineCurve2d line_curve.
math::geometry::PolylineCurve2d ReadLine(const std::string& input) {
  boost::geometry::model::linestring<math::geometry::Point2d> raw_path;
  boost::geometry::read_wkt(input, raw_path);
  return math::geometry::PolylineCurve2d(raw_path);
}

// Returns a pnc_map::Lane with lane_sequence_geometry.
pnc_map::Lane GenerateLane(
    const lane_selection::LaneSequenceGeometry& lane_sequence_geometry,
    const std::vector<hdmap::LaneMarking::Attribute::Type>& attr_types =
        {hdmap::LaneMarking::Attribute::SOLID},
    const hdmap::Lane::LaneType& lane_type = hdmap::Lane::REGULAR,
    const hdmap::Lane::Turn& turn = hdmap::Lane::UNKNOWN_TURN) {
  const auto get_polyline_points =
      [](const math::geometry::PolylineCurve2d& line) {
        std::vector<std::pair<double, double>> points;
        points.reserve(line.points().size());
        for (const auto& point : line.points()) {
          points.push_back({point.x(), point.y()});
        }
        return points;
      };
  hdmap::LaneMarking* left_lane_marking_proto = new hdmap::LaneMarking();
  hdmap::LaneMarking* right_lane_marking_proto = new hdmap::LaneMarking();
  left_lane_marking_proto->set_id(101);
  right_lane_marking_proto->set_id(102);
  *left_lane_marking_proto->mutable_line() = pnc_map::CreatePolylineProto(
      get_polyline_points(lane_sequence_geometry.left_lane_boundary));
  *right_lane_marking_proto->mutable_line() = pnc_map::CreatePolylineProto(
      get_polyline_points(lane_sequence_geometry.right_lane_boundary));

  const size_t num_of_types = attr_types.size();
  DCHECK_GT(num_of_types, 0);

  int left_idx = 0;
  const int left_step_size =
      left_lane_marking_proto->line().points().size() / num_of_types;
  for (int i = 0; i < left_lane_marking_proto->line().points().size(); ++i) {
    if (left_idx * left_step_size <= i) {
      // Reach the end of a lane marking segment, store an attribute.
      hdmap::LaneMarking::Attribute* left_attr =
          left_lane_marking_proto->add_attrs();
      left_attr->set_type(attr_types[left_idx]);
      *(left_attr->mutable_track()->mutable_point()) =
          left_lane_marking_proto->line().points(i);
      // Determine the index for the left boundary type.
      left_idx += 1;
    }
  }

  int right_idx = 0;
  const int right_step_size =
      right_lane_marking_proto->line().points().size() / num_of_types;
  for (int i = 0; i < right_lane_marking_proto->line().points().size(); ++i) {
    if (right_idx * right_step_size <= i) {
      // Reach the end of a lane marking segment, store an attribute.
      hdmap::LaneMarking::Attribute* right_attr =
          right_lane_marking_proto->add_attrs();
      right_attr->set_type(attr_types[right_idx]);
      *(right_attr->mutable_track()->mutable_point()) =
          right_lane_marking_proto->line().points(i);
      // Determine the index for the right boundary type.
      right_idx += 1;
    }
  }
  pnc_map::LaneMarking* left_lane_marking =
      new pnc_map::LaneMarking(left_lane_marking_proto);
  pnc_map::LaneMarking* right_lane_marking =
      new pnc_map::LaneMarking(right_lane_marking_proto);

  hdmap::Lane* lane_proto = new hdmap::Lane();
  lane_proto->set_type(lane_type);
  lane_proto->set_turn(turn);
  lane_proto->set_id(1);
  lane_proto->set_left_lane_marking_id(101);
  lane_proto->set_right_lane_marking_id(102);
  *lane_proto->mutable_assistant_line() = pnc_map::CreatePolylineProto(
      get_polyline_points(lane_sequence_geometry.nominal_path));

  return pnc_map::Lane(lane_proto, left_lane_marking, right_lane_marking);
}
}  // namespace

TEST(CommonUtilityTest, FindClosestLeadAgentTrajectory) {
  voy::Pose ego_pose;
  ego_pose.set_x(10);
  ego_pose.set_y(0);

  // Creates reference path and its lane sequence geometry.
  math::geometry::Polyline2d reference_line;
  reference_line.reserve(80);
  for (int i = 0; i < 80; ++i) {
    reference_line.emplace_back(i, 0);
  }
  math::geometry::PolylineCurve2d reference_curve =
      math::geometry::PolylineCurve2d(reference_line);

  // Creates a regular lane.
  lane_selection::LaneSequenceGeometry regular_lane_sequence_geometry;
  const std::vector<math::geometry::Point2d> regular_lane_points = {
      {0.0, 0.0}, {20.0, 0.0}};
  const math::geometry::PolylineCurve2d regular_lane_curve(regular_lane_points);
  regular_lane_sequence_geometry.nominal_path = regular_lane_curve;
  regular_lane_sequence_geometry.left_lane_boundary =
      ReadLine("LINESTRING(0.0 5.0, 20.0 5.0)");
  regular_lane_sequence_geometry.right_lane_boundary =
      ReadLine("LINESTRING(0.0 -5.0, 20.0 -5.0)");
  regular_lane_sequence_geometry.left_hard_boundary_lines = {
      ReadLine("LINESTRING(0.0 6.0, 20.0 6.0)")};
  regular_lane_sequence_geometry.right_hard_boundary_lines = {
      ReadLine("LINESTRING(0.0 -6.0, 20.0 -6.0)")};

  // Creates a left virtual lane.
  lane_selection::LaneSequenceGeometry virtual_lane_sequence_geometry;
  const std::vector<math::geometry::Point2d> virtual_path_points = {
      {20.0, 0.0}, {40.0, 0.0}};
  const math::geometry::PolylineCurve2d virtual_path_curve(virtual_path_points);
  virtual_lane_sequence_geometry.nominal_path = virtual_path_curve;
  virtual_lane_sequence_geometry.left_lane_boundary =
      ReadLine("LINESTRING(20.0 5.0, 40.0 5.0)");
  virtual_lane_sequence_geometry.right_lane_boundary =
      ReadLine("LINESTRING(20.0 -5.0, 40.0 -5.0)");
  virtual_lane_sequence_geometry.left_hard_boundary_lines = {
      ReadLine("LINESTRING(20.0 6.0, 40.0 6.0)")};
  virtual_lane_sequence_geometry.right_hard_boundary_lines = {
      ReadLine("LINESTRING(20.0 -6.0, 40.0 -6.0)")};

  const pnc_map::Section* section;

  // Lane id = 1;
  pnc_map::Lane regular_lane =
      GenerateLane(regular_lane_sequence_geometry,
                   {hdmap::LaneMarking::Attribute::DOUBLE_SOLID,
                    hdmap::LaneMarking::Attribute::SOLID},
                   hdmap::Lane::REGULAR);
  regular_lane.set_section(section);
  // Lane id = 1;
  pnc_map::Lane virtual_left_lane =
      GenerateLane(virtual_lane_sequence_geometry,
                   {hdmap::LaneMarking::Attribute::VIRTUAL,
                    hdmap::LaneMarking::Attribute::BROKEN},
                   hdmap::Lane::VIRTUAL, hdmap::Lane::LEFT);
  virtual_left_lane.set_section(section);

  // Connects virtual and regular lanes.
  std::vector<const pnc_map::Lane*> lane_sequence = {&regular_lane,
                                                     &virtual_left_lane};

  tbb::concurrent_unordered_map<ObjectId,
                                std::vector<PredictedTrajectoryWrapper>>
      object_prediction_map;
  std::unordered_map<ObjectId, PlannerObject> planner_object_map;
  const std::vector<ObjectId> object_ids{1, 2, 3};
  const std::vector<double> object_center_xs{0.0, 20.0, 40.0};
  const std::vector<double> object_center_ys{0.0, 0.0, 0.0};
  std::vector<prediction::pb::Agent> obj_protos(3, prediction::pb::Agent());
  std::vector<planner::pb::TrajectoryPose> trajectory_poses(
      3, planner::pb::TrajectoryPose());
  std::vector<prediction::pb::PredictedTrajectory> predicted_trajectory_protos(
      3, prediction::pb::PredictedTrajectory());
  for (unsigned i = 0; i < object_ids.size(); ++i) {
    // Construct object_prediction_map.
    const ObjectId id = object_ids[i];
    auto& tracked_object = *obj_protos[i].mutable_tracked_object();
    tracked_object.set_id(id);
    tracked_object.set_object_type(voy::perception::ObjectType::VEHICLE);
    tracked_object.set_center_x(object_center_xs[i]);
    tracked_object.set_center_y(object_center_ys[i]);
    tracked_object.set_width(1.9);
    tracked_object.set_length(5.0);
    tracked_object.set_heading(0);
    trajectory_poses[i].set_timestamp(100);
    trajectory_poses[i].set_x_pos(tracked_object.center_x());
    trajectory_poses[i].set_y_pos(tracked_object.center_y());
    trajectory_poses[i].set_heading(tracked_object.heading());
    predicted_trajectory_protos[i].set_id(id);
    *(predicted_trajectory_protos[i].add_traj_poses()) = trajectory_poses[i];
    PredictedTrajectoryWrapper predicted_trajectory(
        tracked_object, predicted_trajectory_protos[i],
        /*is_primary_trajectory=*/true);
    route_association::MapElementAndPoseInfo pose_info;
    pose_info.lane_ptr = &regular_lane;
    predicted_trajectory.set_latest_isolated_lane_opt(
        std::make_optional<route_association::MapElementAndPoseInfo>(
            pose_info));
    object_prediction_map[id].push_back(std::move(predicted_trajectory));

    // Construct object_prediction_map.
    TrafficParticipantPose traffic_pose(/*timestamp=*/0, tracked_object);
    PlannerObject planner_object(obj_protos[i], /*object_timestamp=*/0,
                                 traffic_pose);
    planner_object.set_l2_distance_to_ego_m(
        std::abs(tracked_object.center_x() - ego_pose.y()));
    planner_object_map.emplace(id, planner_object);
  }

  const auto* closest_lead_agent_trajectory =
      FindClosestLeadAgentTrajectory(ego_pose, lane_sequence, reference_curve,
                                     planner_object_map, object_prediction_map);

  EXPECT_EQ(closest_lead_agent_trajectory->object_id(), 2);
  ASSERT_FALSE(closest_lead_agent_trajectory->empty());
  EXPECT_EQ(closest_lead_agent_trajectory->pose(0).x_pos(), 20.0);
}

}  // namespace planner::common
