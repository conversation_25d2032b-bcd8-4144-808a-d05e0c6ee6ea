#include "planner/speed/reasoning/reasoner/cross_agent_reasoner/cross_agent_reasoner.h"

#include <algorithm>
#include <limits>
#include <string>
#include <utility>
#include <vector>

#include <absl/strings/str_cat.h>
#include <absl/strings/str_format.h>

#include "math/constants.h"
#include "math/math_util.h"
#include "math/range.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_route_association/predicted_trajectory_route_association.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_wrapper.h"
#include "planner/decoupled_maneuvers/required_lateral_gap/control_error.h"
#include "planner/planning_gflags.h"
#include "planner/speed/discomforts/discomfort_varying.h"
#include "planner/speed/overlap/overlap_lib_util.h"
#include "planner/speed/reasoning/overlap_region_info.h"
#include "planner/speed/reasoning/reasoning_util.h"
#include "planner/speed/reasoning_input/reasoning_object.h"
#include "planner/speed/reasoning_input/traffic_rules/conflicting_lane_traffic_rule.h"
#include "planner/speed/reasoning_input/trajectory_info.h"
#include "planner/speed/reasoning_input/world_context.h"
#include "planner_protos/behavior_common_type.pb.h"
#include "planner_protos/overlap.pb.h"
#include "planner_protos/prediction_decision.pb.h"
#include "planner_protos/speed_reasoner.pb.h"
#include "planner_protos/speed_reasoning_debug.pb.h"
#include "voy_rt_event/rt_event_planner.h"

namespace planner {
namespace speed {
namespace reasoner {
namespace {

// TODO(waylon): Move to config file.
// The longitudinal safety distance range for cross agents.
constexpr double kMaxSafetyDistanceToAgentInMeter = 3.0;
constexpr double kMinSafetyDistanceToAgentInMeter = 0.25;
constexpr double kHigherPrecedenceComfortPassExtraTimeInSeconds = 0.75;
constexpr double kHigherPrecedenceCriticalPassExtraTimeInSeconds = 0.0;
constexpr double kLowerPrecedenceComfortPassExtraTimeInSeconds = 0.75;
constexpr double kLowerPrecedenceCriticalPassExtraTimeInSeconds = 0.25;
constexpr double kHigherPrecedenceComfortYieldExtraTimeInSeconds = 0.75;
constexpr double kHigherPrecedenceCriticalYieldExtraTimeInSeconds = 0.0;
constexpr double kLowerPrecedenceComfortYieldExtraTimeInSeconds = 1.0;
constexpr double kLowerPrecedenceCriticalYieldExtraTimeInSeconds = 0.25;
constexpr double kComfortPassExtraTimeAdjustmentInSeconds = 0.25;
// This value is from volvo_xc90_speed.conf, which is the sum of max
// long_track_overshoot_meters and max perception_bounding_box_error_meters.
constexpr double kMinRangeAtLowestDiscomfort = 1.12;
constexpr double kSoftYieldMinAccelInMpss = -2.0;

// yield extra distance for soft constraints.
constexpr double kMaxSafetyDistanceToAgentForSoftConstraintInMeter = 2.5;
constexpr double kMinSafetyDistanceToAgentForSoftConstraintInMeter = 0.5;

// Extra yield extra time/distance buffer for consistent yield object.
constexpr double kYieldExtraTimeForConsistentYieldObjectInSec = 0.3;
// NOTE: the yield extra distance of strict constraint is small and the yield
// extra distance of the corresponding soft constraint is large. so when we add
// an extra buffer when consistently yield we also want to use two parameter so
// that we can tune them separately.
constexpr double kYieldExtraDistanceForDominantRiskObjectInMeter = 0.5;
constexpr double kReducedYieldExtraDistanceForDominantRiskObjectInMeter = 0.25;

// Buffers for reversing vehicles.
constexpr double kComfortPassExtraTimeForReversingVehiclesInSeconds = 1.0;
constexpr double kCriticalPassExtraTimeForReversingVehiclesInSeconds = 0.5;
constexpr double kCriticalPassReqLatGapForReversingVehiclesInMeter = 0.4;
constexpr double kComfortYieldExtraTimeForReversingVehiclesInSeconds = 2.0;
constexpr double kCriticalYieldExtraTimeForReversingVehiclesInSeconds = 0.5;
constexpr double kComfortYieldExtraDistanceForReversingVehiclesInMeter = 2.0;
constexpr double kCriticalYieldExtraDistanceForReversingVehiclesInMeter = 0.5;
constexpr double kSoftYieldExtraDistanceForReversingVehiclesInMeter = 5.0;
constexpr double kComfortYieldExtraDistanceForSoftConstraintInMeter = 1.5;
// Yield extra distance for agent when ego is reverse driving.
constexpr double kYieldExtraDistanceToStaticAgentWhenReversingInMeter = 0.25;
constexpr double kYieldExtraDistanceToNonStaticAgentWhenReversingInMeter = 1.0;
// The reduced yield stop range to move closer to behind agent when reversing so
// that we will have larger space to unstuck.
constexpr double kReducedYieldStopRangeForUnstuckReversingInMeter = 0.1;
// The reduced min creep distance to move closer to behind agent when reversing
// so that we will have larger space to unstuck.
constexpr double kReducedMinCreepDistForUnstuckReversingInMeter = 0.5;

// The reduced min creep distance, yield stop range when ego is in dense
// traffic.
constexpr double kReducedMinCreepDistForDenseTrafficInMeter = 0.5;
constexpr double kReducedYieldStopRangeForDenseTrafficInMeter = 0.2;
// The min speed to creep in dense traffic.
constexpr double kMinCreepingSpeedInMps = math::KmphToMps(2.2);
// Yield buffers for large vehicles.
constexpr double kDefaultComfortYieldExtraDistanceForLargeVehiclesInMeter = 5.0;
constexpr double kDefaultCriticalYieldExtraDistanceForLargeVehiclesInMeter =
    1.0;
constexpr double kReducedYieldExtraDistForPotentialUTurnBlockageInMeter = 1.0;
// The reduced min creep distance for certain u-turn blockage that we want to
// pass by executing open space unstuck behavior.
constexpr double kReducedMinCreepDistForPotentialUTurnBlockageInMeter = 0.2;
// The reduced yield stop range for certain u-turn blockage that we want to
// pass by executing open space unstuck behavior.
constexpr double kReducedYieldStopRangeForPotentialUTurnBlockageInMeter = 0.2;
// The yield extra time for slow agent on ego path in junction in second
constexpr double
    kYieldExtraTimeComfortForSlowAgentOnPathWithFastApproachInSecond = 1.0;
constexpr double
    kYieldExtraTimeCriticalForSlowAgentOnPathWithFastApproachInSecond = 0.5;
constexpr double
    kYieldExtraTimeComfortForSlowAgentOnPathWithMildApproachInSecond = 0.75;
constexpr double
    kYieldExtraTimeCriticalForSlowAgentOnPathWithMildApproachInSecond = 0.25;
// The speed threshold for slow agent on ego path in junction in second
constexpr double kFastApproachEgoSpeedThresholdInMps = 6.0;
constexpr double kMildApproachEgoSpeedThresholdInMps = 3.0;
// The minimal pass required lateral gap for stuck agent
constexpr double kMinResetablePLGForUnstuckSteerAwayAgentInMeters = 0.1;
constexpr double kEpsilon = 1e-3;

// Returns true if the conflicting lanes should be considered when computing
// yield extra distance. The conflicting lanes should not be empty.
const traffic_rules::ConflictingLaneInLaneSequence*
GetConflictingLaneUsedToComputeYieldExtraDistance(
    const std::vector<traffic_rules::ConflictingLaneInLaneSequence>&
        conflicting_lanes,
    const pb::OverlapRegion& overlap_region,
    const double ego_front_bumper_ra_arclength) {
  DCHECK(!conflicting_lanes.empty());

  const traffic_rules::ConflictingLaneInLaneSequence*
      closest_conflicting_lane_before_ego = nullptr;
  for (const auto& conflicting_lane : conflicting_lanes) {
    // Ego has passed the conflicting lane
    if (conflicting_lane.conflicting_range_along_track.start_pos <
        ego_front_bumper_ra_arclength) {
      continue;
    }

    // If overlap region does not overlap with any conflicting lane, we should
    // ignore conflicting lanes.
    const math::Range1d overlap_ra_arclength_range(
        GetPaddedOverlapStart(overlap_region),
        GetPaddedOverlapEnd(overlap_region));
    if (!math::AreRangesOverlapping(
            conflicting_lane.conflicting_range_along_track,
            overlap_ra_arclength_range)) {
      continue;
    }

    if (closest_conflicting_lane_before_ego == nullptr ||
        closest_conflicting_lane_before_ego->conflicting_range_along_track
                .start_pos >
            conflicting_lane.conflicting_range_along_track.start_pos) {
      closest_conflicting_lane_before_ego = &conflicting_lane;
    }
  }

  return closest_conflicting_lane_before_ego;
}

DiscomfortVarying ComputeYieldExtraDistanceForLargeVehicles(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const AgentPolicy& agent_policy, const ReasoningObject& reasoning_object) {
  DCHECK(reasoning_object.IsLargeVehicle());
  constexpr double kSameDirectionAngleThresholdInRad =
      math::Degree2Radian(75.0);
  const bool is_overlap_same_direction_motion =
      std::all_of(agent_policy.OverlapRegion().overlap_slices().begin(),
                  agent_policy.OverlapRegion().overlap_slices().end(),
                  [](const pb::OverlapSlice& slice) {
                    return std::abs(slice.relative_heading()) <=
                           kSameDirectionAngleThresholdInRad;
                  });
  const double additional_yield_distance =
      reasoning_util::ShouldAddExtraBufferForConsistentYieldObject(
          world_context, reasoning_object)
          ? kYieldExtraDistanceForDominantRiskObjectInMeter
          : 0.0;
  if (is_overlap_same_direction_motion) {
    const double yield_distance_to_rear_bumper =
        reasoning_util::ComputeYieldDistanceToAgentRearBumper(
            trajectory_info, agent_policy.agent_trajectory_info(),
            agent_policy.overlap_region_ix(),
            world_context.ra_to_leading_bumper());
    return DiscomfortVarying(
        std::max(kDefaultComfortYieldExtraDistanceForLargeVehiclesInMeter,
                 yield_distance_to_rear_bumper) +
            additional_yield_distance,
        kDefaultCriticalYieldExtraDistanceForLargeVehiclesInMeter +
            additional_yield_distance);
  }
  return DiscomfortVarying(
      {{0.0, kDefaultComfortYieldExtraDistanceForLargeVehiclesInMeter +
                 additional_yield_distance},
       {0.25,
        0.5 * (kDefaultComfortYieldExtraDistanceForLargeVehiclesInMeter +
               kDefaultCriticalYieldExtraDistanceForLargeVehiclesInMeter) +
            additional_yield_distance},
       {0.5, 2.0 * kDefaultCriticalYieldExtraDistanceForLargeVehiclesInMeter +
                 additional_yield_distance},
       {1.0, 0.5 * kDefaultCriticalYieldExtraDistanceForLargeVehiclesInMeter +
                 additional_yield_distance}});
}

// Returns the yield extra distance for the given agent policy.
DiscomfortVarying ComputeYieldExtraDistance(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const AgentPolicy& agent_policy, const ReasoningObject& reasoning_object,
    std::string* debug_str) {
  if (reasoning_object.is_toll_gate_barrier()) {
    // We need to stop close to the toll gate barrier to make sure the camera
    // can capture us. Because we have min_range which can ensure safety, here
    // the yield extra distance is adjusted to 0.
    rt_event::PostRtEvent<
        rt_event::planner::ReduceYieldExtraDistForTollGateBarrier>();
    return DiscomfortVarying(0.0);
  }
  if (FLAGS_planning_enable_uturn_unstuck &&
      reasoning_object.is_potential_uturn_blockage()) {
    return DiscomfortVarying(
        kReducedYieldExtraDistForPotentialUTurnBlockageInMeter);
  }
  if (agent_policy.agent_trajectory_info()
          .is_reversing_trajectory_for_defensive_driving()) {
    return DiscomfortVarying(
        {{0.0, kComfortYieldExtraDistanceForReversingVehiclesInMeter},
         {0.25, 0.5 * kComfortYieldExtraDistanceForReversingVehiclesInMeter},
         {0.5, kCriticalYieldExtraDistanceForReversingVehiclesInMeter},
         {1.0, kCriticalYieldExtraDistanceForReversingVehiclesInMeter}});
  }
  // When ego is almost stopped, the yield extra distance should not be the
  // blocker for the agent to yield at the current position at at discomfort 0.
  // As at this point, reasoning has no way to not set min range, we manually
  // deducted kMinRangeAtLowestDiscomfort to get yield extra distance for ego to
  // properly yield at low discomfort.
  // TODO(xinyue): replace with compensated ra_arc_length_padded once the API is
  // ready.
  if (world_context.EgoAlmostStopped()) {
    const pb::OverlapRegion& overlap_region = agent_policy.OverlapRegion();
    const double comfort_yield_extra_distance = math::Clamp(
        GetPaddedOverlapStart(overlap_region) - kMinRangeAtLowestDiscomfort,
        kMinSafetyDistanceToAgentInMeter, kMaxSafetyDistanceToAgentInMeter);
    return DiscomfortVarying(comfort_yield_extra_distance,
                             kMinSafetyDistanceToAgentInMeter);
  }

  if (agent_policy.associated_conflicting_lanes().empty()) {
    if (debug_str != nullptr) {
      absl::StrAppend(debug_str, "agent_conflicting_lanes_is_empty;");
    }
    // Add longitudinal safety buffer if agent is not in ego conflicting lane.
    double comfort_yield_extra_distance = kMaxSafetyDistanceToAgentInMeter;
    double critical_yield_extra_distance = kMinSafetyDistanceToAgentInMeter;

    // Add extra yield distance to stuck agent for active lane change.
    if (reasoning_object.is_blocking_object()) {
      if (reasoning_util::ShouldAdjustYieldExtraDistanceForPullOver(
              trajectory_info, reasoning_object)) {
        if (debug_str != nullptr) {
          absl::StrAppend(debug_str, "adjust buffer for pull over;");
        }
        return reasoning_util::SetYieldExtraDistanceForPullOver();
      }
      if (debug_str != nullptr) {
        absl::StrAppend(debug_str, "enlarge buffer for blockage;");
      }
      return reasoning_util::ComputeYieldExtraDistanceForBlockage(
          reasoning_object, world_context.robot_state(),
          comfort_yield_extra_distance, critical_yield_extra_distance);
    }
    if (reasoning_object.IsLargeVehicle()) {
      return ComputeYieldExtraDistanceForLargeVehicles(
          world_context, trajectory_info, agent_policy, reasoning_object);
    }
    if (reasoning_util::ShouldAddExtraBufferForConsistentYieldObject(
            world_context, reasoning_object)) {
      comfort_yield_extra_distance +=
          kYieldExtraDistanceForDominantRiskObjectInMeter;
      critical_yield_extra_distance +=
          kYieldExtraDistanceForDominantRiskObjectInMeter;
    }
    return DiscomfortVarying(comfort_yield_extra_distance,
                             critical_yield_extra_distance);
  }

  // Set yield extra distance based on lane conflicting.
  double comfort_yield_extra_distance = kMaxSafetyDistanceToAgentInMeter;
  const traffic_rules::ConflictingLaneInLaneSequence* conflicting_lane_ptr =
      GetConflictingLaneUsedToComputeYieldExtraDistance(
          agent_policy.agent_trajectory_info().associated_conflicting_lanes(),
          agent_policy.OverlapRegion(), world_context.ra_to_leading_bumper());
  if (conflicting_lane_ptr != nullptr) {
    if (reasoning_object.IsLargeVehicle()) {
      return ComputeYieldExtraDistanceForLargeVehicles(
          world_context, trajectory_info, agent_policy, reasoning_object);
    }
    if (debug_str != nullptr) {
      absl::StrAppend(debug_str,
                      "adjust yield extra distance besed on conflicting lane;");
    }
    const double yield_line_ra_arclength =
        conflicting_lane_ptr->conflicting_range_along_track.start_pos;
    const double overlap_region_start_ra_arclength =
        GetPaddedOverlapStart(agent_policy.OverlapRegion());
    comfort_yield_extra_distance = std::max(
        kMinSafetyDistanceToAgentInMeter,
        overlap_region_start_ra_arclength +
            world_context.ra_to_leading_bumper() - yield_line_ra_arclength);
  }

  return DiscomfortVarying(comfort_yield_extra_distance,
                           kMinSafetyDistanceToAgentInMeter);
}

DiscomfortVarying ComputePassExtraTime(
    const AgentTrajectoryInfo& agent_trajectory_info, bool was_pass,
    bool was_yield) {
  if (agent_trajectory_info.is_reversing_trajectory_for_defensive_driving()) {
    return DiscomfortVarying(
        kComfortPassExtraTimeForReversingVehiclesInSeconds,
        kCriticalPassExtraTimeForReversingVehiclesInSeconds);
  }
  const double comfort_pass_extra_time_adjustment =
      was_pass ? -kComfortPassExtraTimeAdjustmentInSeconds
               : (was_yield ? kComfortPassExtraTimeAdjustmentInSeconds : 0.0);
  return agent_trajectory_info.EgoHasHigherRoadPrecedence()
             ? DiscomfortVarying(
                   kHigherPrecedenceComfortPassExtraTimeInSeconds +
                       comfort_pass_extra_time_adjustment,
                   kHigherPrecedenceCriticalPassExtraTimeInSeconds)
             : DiscomfortVarying(
                   kLowerPrecedenceComfortPassExtraTimeInSeconds +
                       comfort_pass_extra_time_adjustment,
                   kLowerPrecedenceCriticalPassExtraTimeInSeconds);
}

DiscomfortVarying ComputeYieldExtraTime(
    const WorldContext& world_context, const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info,
    const pb::OverlapRegion& overlap_region) {
  const OverlapRegionInfo& overlap_region_info =
      agent_trajectory_info.overlap_region_info(overlap_region);
  if (!overlap_region_info.covered_ego_lanes.empty()) {
    const pnc_map::Lane* covered_ego_lane =
        overlap_region_info.covered_ego_lanes.front();
    if (covered_ego_lane->IsInJunction() &&
        (covered_ego_lane->turn() == hdmap::Lane_Turn_STRAIGHT) &&
        agent_trajectory_info.is_cutting_out() &&
        reasoning_object.IsOnEgoPath() &&
        reasoning_object.is_slow_moving_vehicle() &&
        reasoning_object.is_in_junction() && reasoning_object.IsFullyAhead() &&
        !reasoning_object.out_of_extended_path_range()) {
      rt_event::PostRtEvent<
          rt_event::planner::IncreaseYieldExtraTimeForSlowAgentOnPath>();
      const double agent_to_ego_longitudinal_approaching_speed =
          world_context.ego_speed() -
          reasoning_object.object_proximity_info().signed_longitudinal_speed();
      if (agent_to_ego_longitudinal_approaching_speed >
          kFastApproachEgoSpeedThresholdInMps) {
        return DiscomfortVarying(
            kYieldExtraTimeComfortForSlowAgentOnPathWithFastApproachInSecond,
            kYieldExtraTimeCriticalForSlowAgentOnPathWithFastApproachInSecond);
      } else if (agent_to_ego_longitudinal_approaching_speed >
                 kMildApproachEgoSpeedThresholdInMps) {
        return DiscomfortVarying(
            kYieldExtraTimeComfortForSlowAgentOnPathWithMildApproachInSecond,
            kYieldExtraTimeCriticalForSlowAgentOnPathWithMildApproachInSecond);
      }
    }
  }

  if (agent_trajectory_info.is_reversing_trajectory_for_defensive_driving()) {
    return DiscomfortVarying(
        {{0.0, kComfortYieldExtraTimeForReversingVehiclesInSeconds},
         {0.25, 0.5 * kComfortYieldExtraTimeForReversingVehiclesInSeconds},
         {0.5, kCriticalYieldExtraTimeForReversingVehiclesInSeconds},
         {1.0, kCriticalYieldExtraTimeForReversingVehiclesInSeconds}});
  }
  double comfort_yield_extra_time =
      agent_trajectory_info.EgoHasHigherRoadPrecedence()
          ? kHigherPrecedenceComfortYieldExtraTimeInSeconds
          : kLowerPrecedenceComfortYieldExtraTimeInSeconds;
  double critical_yield_extra_time =
      agent_trajectory_info.EgoHasHigherRoadPrecedence()
          ? kHigherPrecedenceCriticalYieldExtraTimeInSeconds
          : kLowerPrecedenceCriticalYieldExtraTimeInSeconds;

  if (reasoning_util::ShouldAddExtraBufferForConsistentYieldObject(
          world_context, reasoning_object)) {
    comfort_yield_extra_time += kYieldExtraTimeForConsistentYieldObjectInSec;
    critical_yield_extra_time += kYieldExtraTimeForConsistentYieldObjectInSec;
  }

  return DiscomfortVarying(comfort_yield_extra_time, critical_yield_extra_time);
}

// Computes pass required lateral gap.
DiscomfortVarying ComputePassRequiredLateralGap(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info,
    const pb::OverlapRegion& overlap_region) {
  if (agent_trajectory_info.overlap_region_info(overlap_region)
          .is_fully_behind_rear_axle_allowing_zero_pass_rlg) {
    // Reduce pass RLG to 0.0 if the overlap region end position is fully behind
    // rear axle and ego is not reversing.
    return DiscomfortVarying(0.0);
  }
  if (reasoning_object.is_plg_resettable_for_unstuck()) {
    const double overlap_abs_lat_gap_to_unstuck =
        std::max((std::abs(reasoning_object.plan_init_pose_overlap_slice_ptr()
                               ->signed_lateral_gap()) -
                  kEpsilon),
                 kMinResetablePLGForUnstuckSteerAwayAgentInMeters);
    return DiscomfortVarying(overlap_abs_lat_gap_to_unstuck);
  }
  if (agent_trajectory_info.is_reversing_trajectory_for_defensive_driving()) {
    return DiscomfortVarying(
        {{0.0, reasoning_object.ComfortRequiredLateralGap()},
         {0.25, reasoning_object.ComfortRequiredLateralGap()},
         {0.75, 0.5 * (reasoning_object.ComfortRequiredLateralGap() +
                       kCriticalPassReqLatGapForReversingVehiclesInMeter)},
         {1.0, kCriticalPassReqLatGapForReversingVehiclesInMeter}});
  }
  if (reasoning_object.is_unstuck_object_in_narrow_passage()) {
    const double unstuck_pass_required_lat_gap =
        (reasoning_object.planner_object().object_type() ==
         voy::perception::ObjectType::TRAFFIC_CONE)
            ? reasoning_util::ComputeUnstuckPassRequiredLatGapForTrafficCone(
                  reasoning_object)
            : planner::control_error::LateralControlError(/*curvature=*/0.0);
    return DiscomfortVarying(unstuck_pass_required_lat_gap);
  }
  // Reduces comfort required lateral gap for static objects that Ego decides to
  // nudge around since Path may not be able to nudge comfort required lateral
  // gap.
  if (reasoning_object.is_static_roadblock()) {
    return DiscomfortVarying(reasoning_object.CriticalRequiredLateralGap());
  }
  if (reasoning_object.is_ego_path_overtaking() ||
      reasoning_object.ReduceCriticalReqLatGapByRaConfirmation()) {
    return reasoning_util::ComputeComfortRequiredLateralGapBaseOnOverlapRegion(
        trajectory_info, reasoning_object, overlap_region);
  }
  // Reduces required lateral gap for agent who is 1) stationary; 2) already
  // behind ego front bumper.
  if (reasoning_object.IsStationary() &&
      reasoning_object.IsBehindEgoLeadingBumper()) {
    return DiscomfortVarying(reasoning_object.CriticalRequiredLateralGap());
  }
  if (reasoning_object.is_cyclist() &&
      agent_trajectory_info.overlap_region_info(overlap_region).is_cut_behind) {
    return (DiscomfortVarying(0.0));
  }
  return DiscomfortVarying(reasoning_object.ComfortRequiredLateralGap(),
                           reasoning_object.CriticalRequiredLateralGap());
}

// Computes yield required lateral gap.
DiscomfortVarying ComputeYieldRequiredLateralGap(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info,
    const pb::OverlapRegion& overlap_region) {
  if (agent_trajectory_info.is_reversing_trajectory_for_defensive_driving()) {
    return DiscomfortVarying(reasoning_object.ComfortRequiredLateralGap());
  }
  // TODO(junying): also set yield side is to keep noop, need to revisit this
  // logic to check whether it is necessary to also adjust the
  // yield_required_lat_gap
  if (reasoning_object.is_static_roadblock()) {
    return DiscomfortVarying(reasoning_object.CriticalRequiredLateralGap());
  }

  if (reasoning_object.is_ego_path_overtaking()) {
    return reasoning_util::ComputeComfortRequiredLateralGapBaseOnOverlapRegion(
        trajectory_info, reasoning_object, overlap_region);
  }
  // Note that adjusting required lateral gap will only take limited effect,
  // because the required lateral gap adjustment happens after constraint states
  // are generated (using the default required lateral gap), so the adjusted
  // required lateral gap will not influence the constraint states start/end x.
  // If an overlap slice's actual lateral gap is less than its pass/yield
  // required lateral gap, downstream will consider it and its start/end x will
  // still be the padded (which is wider than expected) overlap start/end
  // arclength that was originally computed upstream, therefore the start
  // arclength will be earlier than expected. In addition, for the
  // ReqLatGapVarStates, the current API cannot consume pass/yield required
  // lateral gap.
  if (world_context.EgoAlmostStopped()) {
    return DiscomfortVarying(reasoning_object.CriticalRequiredLateralGap());
  }
  return DiscomfortVarying(reasoning_object.ComfortRequiredLateralGap(),
                           reasoning_object.CriticalRequiredLateralGap());
}

// Adjusts the required lateral gap for reversing according to the specific
//  context.
void AdjustRequiredLateralGapForReversing(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info,
    const pb::OverlapRegion& overlap_region, ConstraintMutator& mutator) {
  if (reasoning_object.is_static_roadblock()) {
    mutator.set_required_lat_gap(
        DiscomfortVarying(reasoning_object.CriticalRequiredLateralGap()));
    return;
  }
  // For a static agent we can set a smaller required lateral gap to avoid stuck
  // by it.
  if (agent_trajectory_info.IsStationary()) {
    const double min_lateral_gap =
        GetOverlapRegionMinAbsLateralGap(overlap_region);
    const double critical_required_lateral_gap =
        reasoning_util::GetPrincipledCriticalRequiredLateralGap(
            trajectory_info, reasoning_object, overlap_region);

    // Subtracts a small buffer to account for numerical errors.
    const double kRequiredLateralGapBufferForStationaryObjectInM = 0.05;
    double comfort_required_lateral_gap = std::min(
        reasoning_object.ComfortRequiredLateralGap(),
        min_lateral_gap - kRequiredLateralGapBufferForStationaryObjectInM);
    comfort_required_lateral_gap =
        std::max(comfort_required_lateral_gap, critical_required_lateral_gap);
    // Reduces the comfort and critical at the same time because the constraint
    // is a soft one when reversing.
    mutator.set_pass_required_lat_gap(
        {comfort_required_lateral_gap, critical_required_lateral_gap});
    mutator.set_yield_required_lat_gap(
        {comfort_required_lateral_gap, comfort_required_lateral_gap});
    return;
  }
}

double ComputeYieldLaneMarkToAgentDistanceDuringUPL(
    const std::vector<traffic_rules::EgoLaneConflictingLanesInfo>&
        ego_lane_sequence_conflicting_lanes,
    const std::vector<route_association::MapElementAndPoseInfo>&
        agent_associated_route,
    const AgentPolicy& agent_policy, std::string* debug_str) {
  if (agent_associated_route.empty()) {
    // no associated route is found.
    return std::numeric_limits<double>::infinity();
  }
  for (const traffic_rules::EgoLaneConflictingLanesInfo& info :
       ego_lane_sequence_conflicting_lanes) {
    if (info.scene_type ==
        traffic_rules::ConflictingLaneType::kUnprotectedLeftTurn) {
      // The conflicting lanes are orderd by the conflicting start, here we want
      // to find the frist conflicting lane between ego's route and agent's
      // route.
      auto iter = std::find_first_of(
          info.conflicting_lanes.begin(), info.conflicting_lanes.end(),
          agent_associated_route.begin(), agent_associated_route.end(),
          [](const traffic_rules::ConflictingLaneInLaneSequence& conflict_lane,
             const route_association::MapElementAndPoseInfo& pose_info) {
            return conflict_lane.conflicting_lane_ptr->lane() ==
                       pose_info.lane_ptr &&
                   conflict_lane.conflicting_lane_ptr->hdmap_conflict_type() ==
                       hdmap::ConflictLane::YIELD_LANE;
          });
      if (iter != info.conflicting_lanes.end()) {
        if (debug_str != nullptr) {
          absl::StrAppendFormat(debug_str,
                                "\nobject %i: lane id %i, dist: %.3lf",
                                agent_policy.reasoning_object().id(),
                                iter->conflicting_lane_ptr->lane()->id(),
                                iter->conflicting_range_along_track.start_pos);
        }
        return GetPaddedOverlapStart(agent_policy.OverlapRegion()) -
               iter->conflicting_range_along_track.start_pos;
      }
    }
  }
  return std::numeric_limits<double>::infinity();
}

double ComputeAgentMaxLateralShiftDistance(
    const pb::OverlapRegion& overlap_region) {
  constexpr double kMaxLateralSpeedInMps = 0.2;
  constexpr double kMaxShiftDistanceUpperBoundInMeters = 1.0;
  const double max_kinematic_shift_distance =
      std::max(overlap_region.start_strict_relative_time_in_sec(), 0.0) *
      kMaxLateralSpeedInMps;
  return std::min(max_kinematic_shift_distance,
                  kMaxShiftDistanceUpperBoundInMeters);
}

// From the associated_route, We can know the lane which covers the agent's
// prediction and overlaps with ego's path. This function returns the distance
// between the agent and the lane marking of the lane mentioned above.
double ComputeYieldLaneMarkToAgentDistanceDuringUPL(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const AgentPolicy& agent_policy, std::string* debug_str) {
  const PredictedTrajectoryWrapper& predicted_trajectory =
      agent_policy.agent_trajectory_info().predicted_trajectory();
  const auto& optional_associated_route =
      agent_policy.agent_trajectory_info().associated_route_opt();
  const std::vector<route_association::MapElementAndPoseInfo>&
      agent_associated_route =
          optional_associated_route.has_value()
              ? optional_associated_route.value()
              : reasoning_util::GetAdHocAssociatedRouteForPredictedTrajectory(
                    agent_policy.reasoning_object(), predicted_trajectory,
                    *world_context.joint_pnc_map_service(),
                    /*allow_using_for_cyclist=*/true);
  return ComputeYieldLaneMarkToAgentDistanceDuringUPL(
      trajectory_info.traffic_rules().lane_sequence_conflicting_lanes,
      agent_associated_route, agent_policy, debug_str);
}

double ComputeSoftConstraintYieldExtraDistance(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const AgentPolicy& agent_policy, std::string* debug_str) {
  if (agent_policy.reasoning_object()
          .is_reversing_vehicle_for_defensive_driving()) {
    return kSoftYieldExtraDistanceForReversingVehiclesInMeter;
  }
  if (FLAGS_planning_enable_uturn_unstuck &&
      agent_policy.reasoning_object().is_potential_uturn_blockage()) {
    if (debug_str != nullptr) {
      absl::StrAppendFormat(debug_str, "reduce yield extra dist to unstuck");
    }
    return kReducedYieldExtraDistForPotentialUTurnBlockageInMeter;
  }
  // UPL scenario. UPL uses conflicting lane to compute the yield extra
  // distance.
  // TODO(junying): check whether this logic is necessary.
  if (trajectory_info.IsInUnprotectedLeftTurn(
          GetPaddedOverlapStart(agent_policy.OverlapRegion()),
          GetPaddedOverlapEnd(agent_policy.OverlapRegion()))) {
    return std::clamp(
        ComputeYieldLaneMarkToAgentDistanceDuringUPL(
            world_context, trajectory_info, agent_policy, debug_str),
        kMinSafetyDistanceToAgentForSoftConstraintInMeter,
        kMaxSafetyDistanceToAgentForSoftConstraintInMeter);
  }
  return kComfortYieldExtraDistanceForSoftConstraintInMeter;
}

bool ShouldAllowEmergencyBrake(
    const WorldContext& world_context, const ReasoningObject& reasoning_object,
    const pb::OverlapRegion& overlap_region,
    const speed::OverlapRegionInfo& overlap_region_info) {
  // Enable EB if the front agent is already blocking ego path.
  if (reasoning_util::ShouldAllowEmergencyBrake(world_context,
                                                reasoning_object)) {
    return true;
  }

  if (reasoning_object.is_vehicle()) {
    if (overlap_region_info.is_in_u_turn &&
        overlap_region_info.is_scene_vulnerable_to_yield_paradox) {
      return true;
    }
    constexpr double kMaxConsiderFutureTimeInSec = 1.5;
    return reasoning_util::WithEBInterestedRange(world_context,
                                                 reasoning_object) &&
           (overlap_region.contain_strict_overlap() &&
            overlap_region.start_strict_relative_time_in_sec() <
                kMaxConsiderFutureTimeInSec);
  }

  if (reasoning_object.is_cyclist()) {
    return reasoning_util::ShouldAllowEmergencyBrakeForVRU(
        world_context, reasoning_object, overlap_region,
        /*reasoner_in_charge=*/pb::ReasonerId::CROSS_AGENT);
  }

  return false;
}

// Returns true if honk should be activated in yield scenes. The logics in this
// function follows the instruction in
// https://cooper.didichuxing.com/docs2/document/2201992838237.
bool ShouldActivateHonkForYieldScenario(const WorldContext& world_context,
                                        const TrajectoryInfo& trajectory_info,
                                        const AgentPolicy& agent_policy,
                                        pb::CrossAgentHonkDebug* honk_debug) {
  DCHECK(!agent_policy.reasoning_object().is_pedestrian())
      << "Will NOT honk to pedestrians.";

  if (!trajectory_info.IsGoingStraight()) {
    if (honk_debug != nullptr) {
      honk_debug->set_disallowed_reason(pb::CrossAgentHonkDebug::NA);
      honk_debug->set_should_trigger(false);
    }
    return false;
  }

  constexpr int64_t kMinEgoSpeedToHonkInYieldInMps = 8.0;
  if (world_context.ego_speed() < kMinEgoSpeedToHonkInYieldInMps) {
    if (honk_debug != nullptr) {
      honk_debug->set_disallowed_reason(
          pb::CrossAgentHonkDebug::EGO_SLOW_SPEED);
      honk_debug->set_should_trigger(false);
    }
    return false;
  }

  const bool is_ego_lower_precedence =
      agent_policy.agent_trajectory_info().EgoHasLowerRoadPrecedence();
  if (is_ego_lower_precedence) {
    if (honk_debug != nullptr) {
      honk_debug->set_disallowed_reason(
          pb::CrossAgentHonkDebug::EGO_LOW_PRECEDENCE);
      honk_debug->set_should_trigger(false);
    }
    return false;
  }

  constexpr int64_t kMinActiveTimeForHonkSignalInMSec = 1000;
  constexpr int64_t kMaxActiveTimeForHonkSignalInMSec = 5000;
  const bool should_trigger_honk_in_temporal_context =
      agent_policy.OverlapRegion().contain_strict_overlap() &&
      (agent_policy.OverlapRegion().start_strict_relative_time_in_msec() >
       kMinActiveTimeForHonkSignalInMSec) &&
      (agent_policy.OverlapRegion().start_strict_relative_time_in_msec() <
       kMaxActiveTimeForHonkSignalInMSec);
  if (!should_trigger_honk_in_temporal_context) {
    if (honk_debug != nullptr) {
      honk_debug->set_disallowed_reason(
          pb::CrossAgentHonkDebug::OUT_OF_TEMPORAL_CONTEXT);
      honk_debug->set_should_trigger(false);
    }
    return false;
  }

  const pb::ObjectProximityInfo& proximity_info =
      agent_policy.reasoning_object().object_proximity_info();
  constexpr int64_t kMaxLateralGapToHonkInYieldInMeters = 1.0;
  constexpr int64_t kMinLateralSpeedToHonkInYieldInMps = 5.0;
  const bool is_agent_behind_ego_front_axle =
      proximity_info.projected_ra_arc_length().end() <
      world_context.ra_to_front_axle();
  const bool is_agent_laterally_close_to_ego =
      proximity_info.abs_lateral_gap() <= kMaxLateralGapToHonkInYieldInMeters;
  const bool is_agent_laterally_fast = proximity_info.signed_lateral_speed() >=
                                       kMinLateralSpeedToHonkInYieldInMps;

  if (is_agent_behind_ego_front_axle ||
      (!is_agent_laterally_close_to_ego && !is_agent_laterally_fast)) {
    if (honk_debug != nullptr) {
      honk_debug->set_disallowed_reason(pb::CrossAgentHonkDebug::PROXIMITY);
      honk_debug->set_should_trigger(false);
    }
    return false;
  }
  if (honk_debug != nullptr) {
    honk_debug->set_disallowed_reason(pb::CrossAgentHonkDebug::NA);
    honk_debug->set_should_trigger(true);
  }
  return true;
}

// We allow MAX_SPEED for vehicles when ego is in UPL.
bool ShouldAllowMaxSpeed(const WorldContext& world_context,
                         const ReasoningObject& reasoning_object) {
  return reasoning_object.is_vehicle() ||
         reasoning_util::ShouldAllowMaxSpeedForCyclist(world_context,
                                                       reasoning_object);
}

// Returns the first crossing overlap region segment in the whole overlap
// region. The range is compatible with the definition of the STL, i.e. the end
// is not included.
std::pair<int, int> ComputeTheFirstCrossingOverlapRegionSegment(
    const pb::OverlapRegion& overlap_region) {
  // Considering that the uncertainty of predicted trajectories, some overlap
  // slices after the crossing overlap segment should be used to add constraint
  // to guarantee safety.
  constexpr int kExtraNumberOfOverlapSlicesForSafety = 10;
  int end_index = 0;
  bool has_encounter_crossing_overlap_slice = false;
  for (; end_index < overlap_region.overlap_slices_size(); end_index++) {
    const pb::OverlapSlice& overlap_slice =
        overlap_region.overlap_slices(end_index);
    if (overlap_slice.motion_type() == pb::OVERLAP_MOTION_CROSSING &&
        !has_encounter_crossing_overlap_slice) {
      has_encounter_crossing_overlap_slice = true;
    }

    if (overlap_slice.motion_type() != pb::OVERLAP_MOTION_CROSSING &&
        has_encounter_crossing_overlap_slice) {
      break;
    }
  }

  end_index = std::min(overlap_region.overlap_slices_size(),
                       end_index + kExtraNumberOfOverlapSlicesForSafety);

  return {0, end_index};
}

// Returns a truncated overlap region segment with abs relative heading less
// than a predefined threshold in the whole overlap region for a
// crossing-oncoming trajectory. The range is compatible with the definition of
// the STL, i.e. the end is not included.
std::pair<int, int>
ComputeTruncatedOverlapRegionSegmentInCrossingToOncomingTrajectory(
    const pb::OverlapRegion& overlap_region) {
  constexpr double kMinTimeHorizonForSafetyInSec = 3.0;
  constexpr double kMaxAbsRelHeadingToConsiderInRad = math::Degree2Radian(160);
  int end_index = 0;
  for (; end_index < overlap_region.overlap_slices_size(); end_index++) {
    const pb::OverlapSlice& overlap_slice =
        overlap_region.overlap_slices(end_index);
    if (overlap_slice.relative_time_in_sec() > kMinTimeHorizonForSafetyInSec &&
        std::abs(overlap_slice.relative_heading()) >
            kMaxAbsRelHeadingToConsiderInRad) {
      break;
    }
  }
  return {0, end_index};
}

// Appends segment index range to the constraint id.
std::string GetConstraintIdForNominalSegmentConstraint(
    const pb::OverlapRegion& overlap_region,
    const std::pair<int, int>& overlap_segment) {
  std::string constraint_id = GetOverlapRegionUniqueId(overlap_region);

  if (overlap_segment.first != 0 ||
      overlap_segment.second != overlap_region.overlap_slices_size()) {
    absl::StrAppendFormat(&constraint_id, "-seg-%d-%d", overlap_segment.first,
                          overlap_segment.second);
  }

  return constraint_id;
}

// Set the pass AR for the creep constraint, the pass AR params is set to the
// comfort value of the original ar params.
// TODO(jinhao): Move this to the slow down strategy file.
void SetComfortPassAgentReactionForSoftCreepConstraint(
    const AgentReactionParams& original_ar_params,
    ConstraintMutator& constraint_mutator) {
  const double comfort_slow_down_accel =
      original_ar_params.slow_down_params.pass.accel(0.0);
  const double comfort_max_speed_reduction =
      original_ar_params.slow_down_params.pass.max_speed_reduction(0.0);
  const double comfort_reaction_time =
      original_ar_params.reaction_times.pass(0.0);
  SlowDownParams slow_down_params;
  ReactionTimes reaction_times;
  slow_down_params.pass.accel = DiscomfortVarying(comfort_slow_down_accel);
  slow_down_params.pass.max_speed_reduction =
      DiscomfortVarying(comfort_max_speed_reduction);
  reaction_times.pass = DiscomfortVarying(comfort_reaction_time);
  constraint_mutator.set_pass_agent_reaction(reaction_times, slow_down_params);
}

void SetPassOptionForYieldStrictConstraint(
    const AgentPolicy& agent_policy, const ReasoningObject& reasoning_object,
    const DiscomfortVarying& pass_required_lateral_gap,
    ConstraintMutator& mutator) {
  mutator.set_pass_required_lat_gap(pass_required_lateral_gap);
  if ((reasoning_object.is_cyclist() &&
       agent_policy.overlap_region_info().is_cut_behind) ||
      agent_policy.overlap_region_info()
          .is_fully_behind_rear_axle_allowing_zero_pass_rlg ||
      reasoning_object.is_unstuck_object_in_narrow_passage() ||
      reasoning_object.is_plg_resettable_for_unstuck()) {
    mutator.set_allow_ignoring_min_range_lat_gap();
  }
  mutator.set_pass_extra_time(ComputePassExtraTime(
      agent_policy.agent_trajectory_info(), reasoning_object.was_pass(),
      reasoning_object.was_yield()));
}

void SetYieldOptionForYieldStrictConstraint(
    const WorldContext& world_context, const UrgencyInfo& urgency_info,
    const ReasoningObject& reasoning_object, const AgentPolicy& agent_policy,
    ConstraintMutator& mutator) {
  // NOTE: We set the required lateral gap to zero, which violates the
  // requirement that "system error should be less than required lateral gap" in
  // the speed pipeline.
  mutator.set_allow_ignoring_min_range_lat_gap();

  mutator.set_yield_required_lat_gap(DiscomfortVarying(0.0));
  double yield_extra_distance = kMinSafetyDistanceToAgentInMeter;
  if (reasoning_util::ShouldAddExtraBufferForConsistentYieldObject(
          world_context, reasoning_object)) {
    yield_extra_distance +=
        kReducedYieldExtraDistanceForDominantRiskObjectInMeter;
  }
  // Set negative yield buffer based on agent shift distance if the scene is
  // considered being vulnerable to yield paradox. The negative buffer starts at
  // discomfort 0.25 given the strong AR typically starts at discomfort 0.5.
  if (agent_policy.overlap_region_info().is_scene_vulnerable_to_yield_paradox) {
    const double shifted_yield_distance =
        ComputeAgentMaxLateralShiftDistance(agent_policy.OverlapRegion());
    mutator.set_yield_extra_distance(
        DiscomfortVarying(
            {{0.0, kMinSafetyDistanceToAgentInMeter},
             {0.25, kMinSafetyDistanceToAgentInMeter - shifted_yield_distance},
             {1.0, kMinSafetyDistanceToAgentInMeter - shifted_yield_distance}}),
        /*allow_negative_value=*/true);
  } else {
    mutator.set_yield_extra_distance(DiscomfortVarying(yield_extra_distance));
  }

  if (FLAGS_planning_enable_uturn_unstuck &&
      reasoning_object.is_potential_uturn_blockage()) {
    mutator.set_min_creep_distance(
        kReducedMinCreepDistForPotentialUTurnBlockageInMeter);
    mutator.set_yield_stop_range(DiscomfortVarying(
        kReducedYieldStopRangeForPotentialUTurnBlockageInMeter));
  }

  // TODO(hezhihang): Wrap |ShouldProceedUrgentlyInRightHook| into
  // |ShouldProceedUrgently|.
  if ((urgency_info.ShouldProceedUrgently() ||
       urgency_info.ShouldProceedUrgentlyInRightHook(
           agent_policy.OverlapRegion(), world_context.ra_to_leading_bumper(),
           world_context.robot_state().GetLength())) &&
      !reasoning_object.IsStationary()) {
    mutator.set_min_creep_distance(kReducedMinCreepDistForDenseTrafficInMeter);
    mutator.set_yield_stop_range(
        DiscomfortVarying(kReducedYieldStopRangeForDenseTrafficInMeter));
  }
  mutator.set_yield_extra_time(DiscomfortVarying(0.0));
}

void SetYieldOptionForSoftConstraint(const WorldContext& world_context,
                                     const TrajectoryInfo& trajectory_info,
                                     const AgentPolicy& agent_policy,
                                     const ReasoningObject& reasoning_object,
                                     ConstraintMutator& mutator,
                                     std::string* debug_str) {
  mutator.set_soft_yield_with_min_a(kSoftYieldMinAccelInMpss);

  mutator.set_yield_required_lat_gap(
      DiscomfortVarying(reasoning_object.ComfortRequiredLateralGap()));
  // Yield extra distance is discomfort varying so that we can follow the
  // leading vehicle closer when discomfort for progress.
  const double comfort_yield_extra_distance =
      ComputeSoftConstraintYieldExtraDistance(world_context, trajectory_info,
                                              agent_policy, debug_str);
  // Solver doesn't support discomfort varying yield buffer for
  // YIELD_ALWAYS_POSSIBLE constraints.
  // NOTE: the YIELD_STRICT_WITH_SOFT constraint and YIELD_ALWAYS_POSSIBLE
  // should share the same yield buffer as there may some transition when ego
  // starts to move(change from YIELD_ALWAYS_POSSIBLE to
  // YIELD_STRICT_WITH_SOFT), and if there are some diffs between the two
  // constraints, there may be some behavior change.
  mutator.set_yield_extra_distance(
      DiscomfortVarying(comfort_yield_extra_distance));
  if (FLAGS_planning_enable_uturn_unstuck &&
      reasoning_object.is_potential_uturn_blockage()) {
    mutator.set_min_creep_distance(
        kReducedMinCreepDistForPotentialUTurnBlockageInMeter);
    mutator.set_yield_stop_range(DiscomfortVarying(
        kReducedYieldStopRangeForPotentialUTurnBlockageInMeter));
  }

  const double comfort_yield_extra_time =
      agent_policy.EgoHasHigherRoadPrecedence()
          ? kHigherPrecedenceComfortYieldExtraTimeInSeconds
          : kLowerPrecedenceComfortYieldExtraTimeInSeconds;

  const bool yield_always_possible = agent_policy.yield_always_possible();
  const double extra_yield_extra_time =
      yield_always_possible
          ? reasoning_util::ComputeExtraYieldExtraTimeForYieldAlwaysPossible(
                world_context, agent_policy.OverlapRegion(),
                kMaxSafetyDistanceToAgentInMeter)
          : 0.0;
  mutator.set_yield_extra_time(
      DiscomfortVarying(comfort_yield_extra_time + extra_yield_extra_time));
  // SOFT yield does not support agent reaction
  mutator.set_yield_agent_reaction(ReactionTimes(), SlowDownParams());
}

void SetYieldAlwaysPossibleForHardConstraint(
    const UrgencyInfo& urgency_info,
    const DiscomfortVarying& yield_extra_distance, ConstraintMutator& mutator) {
  constexpr double kHighDiscomfortForProgress = 0.5;
  // TODO(jinhao): Use a general signal to set discomfort for progress.
  const bool set_discomfort_for_progress =
      urgency_info.ShouldProceedUrgently() ||
      urgency_info.ShouldSetDiscomfortForProgressInUPL() ||
      urgency_info.HasUrgencyReason(planner::pb::UrgencyReason::BlockingUTurn);
  // TODO(jinhao): Use discomfort varying yield extra distance when solver
  // supports.
  mutator.set_yield_extra_distance(DiscomfortVarying(yield_extra_distance(
      set_discomfort_for_progress ? kHighDiscomfortForProgress
                                  : Discomforts::kMid)));
  mutator.set_hard_yield_with_min_accel(kSoftYieldMinAccelInMpss);
  mutator.set_yield_agent_reaction(ReactionTimes(), SlowDownParams());
}

}  // namespace

bool CrossAgentReasoner::AddConstraintForPolicy(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const UrgencyInfo& urgency_info, const AgentPolicy& agent_policy,
    ConstraintCreator* constraint_creator_ptr, HonkRequest* honk_request_ptr,
    pb::CrossAgentPolicyDebug* agent_policy_debug) {
  if (agent_policy_debug != nullptr) {
    agent_policy_debug->set_overlap_region_id(
        agent_policy.OverlapRegion().region_id());
  }

  if (trajectory_info.IsReverseDriving()) {
    return AddReversingConstraintForPolicy(
        trajectory_info, agent_policy, constraint_creator_ptr,
        agent_policy_debug != nullptr
            ? agent_policy_debug->mutable_policy_debug_str()
            : nullptr);
  }

  const pb::GenerativeConstraintType::Enum generative_constraint_type =
      agent_policy.generative_constraint_type();
  switch (generative_constraint_type) {
    case pb::GenerativeConstraintType::NOMINAL:
      return AddNominalConstraintForPolicy(
          world_context, trajectory_info, urgency_info, agent_policy,
          constraint_creator_ptr, honk_request_ptr,
          agent_policy_debug != nullptr ? agent_policy_debug : nullptr);
    case pb::GenerativeConstraintType::NOMINAL_WITH_SOFT:
      return AddNominalWithSoftConstraintForPolicy(
          world_context, trajectory_info, urgency_info, agent_policy,
          constraint_creator_ptr,
          agent_policy_debug != nullptr
              ? agent_policy_debug->mutable_policy_debug_str()
              : nullptr);
    case pb::GenerativeConstraintType::YIELD_STRICT_WITH_SOFT:
      return AddYieldStrictWithSoftConstraintForPolicy(
          world_context, trajectory_info, urgency_info, agent_policy,
          constraint_creator_ptr,
          agent_policy_debug != nullptr
              ? agent_policy_debug->mutable_policy_debug_str()
              : nullptr);
    case pb::GenerativeConstraintType::NOMINAL_SEGMENT:
      return AddNominalConstraintForOverlapSegment(
          world_context, trajectory_info, urgency_info, agent_policy,
          constraint_creator_ptr, honk_request_ptr,
          agent_policy_debug != nullptr ? agent_policy_debug : nullptr);
    case pb::GenerativeConstraintType::SOFT:
      return AddSoftConstraintForPolicy(world_context, trajectory_info,
                                        agent_policy, constraint_creator_ptr,
                                        agent_policy_debug);
    case pb::GenerativeConstraintType::CREEP:
      return AddCreepConstraintsForPolicy(world_context, trajectory_info,
                                          urgency_info, agent_policy,
                                          constraint_creator_ptr);
    default:
      DCHECK(false)
          << " The pb::GenerativeConstraintType is NOT handled by cross "
             "agent reasoner!";
  }
  return false;
}

bool CrossAgentReasoner::AddReversingConstraintForPolicy(
    const TrajectoryInfo& trajectory_info, const AgentPolicy& agent_policy,
    ConstraintCreator* constraint_creator_ptr, std::string* debug_str) {
  const ReasoningObject& reasoning_object = agent_policy.reasoning_object();
  const std::string constraint_id =
      GetOverlapRegionUniqueId(agent_policy.OverlapRegion());
  ConstraintMutator constraint_mutator =
      agent_policy.AddConstraintWhenReversing(
          pb::FenceType::kCross, reasoning_object.required_lateral_gap(),
          constraint_id, constraint_creator_ptr);
  // Reduces the comfort and critical required lateral gap for static objects
  // that ego decides to nudge around since Path may not be able to nudge
  // comfort required lateral gap. It need to reduce the comfort and critical at
  // the same time because the constraint is a soft one when reversing.
  AdjustRequiredLateralGapForReversing(
      trajectory_info, reasoning_object, agent_policy.agent_trajectory_info(),
      agent_policy.OverlapRegion(), constraint_mutator);

  // TODO(Junying, Jiakai): Revisit the decision making and buffer tuning for
  // open-space behavior and k-turn.
  if (trajectory_info.open_space_info().IsActive() &&
      trajectory_info.open_space_info().unstuck_scene_type ==
          planner::pb::UnstuckSceneType::PULLOUT) {
    // Skip stop distance reduction for pullout reverse.
    return true;
  }
  const bool is_stationary_trajectory =
      agent_policy.agent_trajectory_info().IsStationary();
  constraint_mutator.set_yield_extra_distance(DiscomfortVarying(
      is_stationary_trajectory
          ? kYieldExtraDistanceToStaticAgentWhenReversingInMeter
          : kYieldExtraDistanceToNonStaticAgentWhenReversingInMeter));
  if (debug_str != nullptr) {
    absl::StrAppendFormat(debug_str,
                          "set closer yield fence when reverse driving.");
  }
  if (is_stationary_trajectory) {
    constraint_mutator.set_yield_stop_range(
        DiscomfortVarying(kReducedYieldStopRangeForUnstuckReversingInMeter));
    constraint_mutator.set_min_creep_distance(
        kReducedMinCreepDistForUnstuckReversingInMeter);
    absl::StrAppendFormat(debug_str,
                          "reduce yield_stop_range & creep_distance.");
  }
  return true;
}

bool CrossAgentReasoner::AddSoftConstraintForPolicy(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const AgentPolicy& agent_policy, ConstraintCreator* constraint_creator_ptr,
    pb::CrossAgentPolicyDebug* agent_policy_debug) {
  const ReasoningObject& reasoning_object = agent_policy.reasoning_object();
  DCHECK(reasoning_object.is_better_not_drive());
  DCHECK(constraint_creator_ptr != nullptr);
  const std::string constraint_id =
      absl::StrCat(GetOverlapRegionUniqueId(agent_policy.OverlapRegion()),
                   "-better-not-drive-soft");

  const DiscomfortVarying pass_required_lateral_gap =
      ComputePassRequiredLateralGap(trajectory_info, reasoning_object,
                                    agent_policy.agent_trajectory_info(),
                                    agent_policy.OverlapRegion());
  const DiscomfortVarying yield_required_lateral_gap =
      ComputeYieldRequiredLateralGap(
          world_context, trajectory_info, reasoning_object,
          agent_policy.agent_trajectory_info(), agent_policy.OverlapRegion());

  ConstraintMutator mutator = agent_policy.AddConstraint(
      pb::FenceType::kCross, pass_required_lateral_gap,
      yield_required_lateral_gap, constraint_id, constraint_creator_ptr);

  mutator.set_yield_extra_distance(ComputeYieldExtraDistance(
      world_context, trajectory_info, agent_policy, reasoning_object,
      agent_policy_debug != nullptr
          ? agent_policy_debug->mutable_policy_debug_str()
          : nullptr));

  // We only want to brake up to -3.0 m/s/s and 15 km/h for better_not_drive
  // object.
  const auto& fod_intention_map =
      trajectory_info.ego_intention().fod_intention_map();
  const auto iter = fod_intention_map.find(reasoning_object.id());

  // If we decide to undercarriage over the better not drive object, only brake
  // up to -3 m/s/s and 15 km/h. Otherwise we allow brake up to -5 m/s/s and to
  // full stop.
  // TODO(Ziyue): Currently the fod intention map will not contain the FOD after
  // the ego has passed it. So removed the DCHECK. Will further investigate the
  // reason behind it and add the DCHECK back after it's fixed.
  if (iter != fod_intention_map.end() &&
      iter->second == planner::pb::IntentionResult::UNDERCARRIAGE_OVER) {
    constexpr double kMinAccelForUnderCarriageOverBetterNotDriveInMpss = -3.0;
    constexpr double kMinSpeedForUnderCarriageOverBetterNotDriveInMps =
        math::KmphToMps(15);
    mutator.set_soft_yield_with_min_speed_accel(
        kMinSpeedForUnderCarriageOverBetterNotDriveInMps,
        kMinAccelForUnderCarriageOverBetterNotDriveInMpss);
  } else {
    constexpr double kMinAccelForBetterNotDriveInMpss = -5.0;
    mutator.set_soft_yield_with_min_a(kMinAccelForBetterNotDriveInMpss);
  }

  // SOFT yield does not support agent reaction
  mutator.set_yield_agent_reaction(ReactionTimes(), SlowDownParams());
  mutator.set_allow_ignoring_min_range_lat_gap();

  // We do not want to consider headway and yield stop range for soft
  // constraints to avoid optimizer overshoot.
  mutator.set_yield_headway(DiscomfortVarying(0.0));
  mutator.set_yield_stop_range(DiscomfortVarying(0.0));
  return true;
}

bool CrossAgentReasoner::AddCreepConstraintsForPolicy(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const UrgencyInfo& urgency_info, const AgentPolicy& agent_policy,
    ConstraintCreator* constraint_creator_ptr) {
  DCHECK(urgency_info.ShouldProceedUrgently() ||
         urgency_info.ShouldProceedUrgentlyInRightHook(
             agent_policy.OverlapRegion(), world_context.ra_to_leading_bumper(),
             world_context.robot_state().GetLength()));

  // The strict part keeps the same with the |YieldStrictWithSoft| constraint
  AddStrictPartOfYieldStrictWithSoftConstraint(
      world_context, trajectory_info, urgency_info, agent_policy,
      constraint_creator_ptr, /*constraint_group_id=*/"");

  AddSoftPartOfCreepConstraints(trajectory_info, agent_policy,
                                constraint_creator_ptr);

  return true;
}

bool CrossAgentReasoner::AddNominalConstraintForPolicy(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const UrgencyInfo& urgency_info, const AgentPolicy& agent_policy,
    ConstraintCreator* constraint_creator_ptr, HonkRequest* honk_request_ptr,
    pb::CrossAgentPolicyDebug* agent_policy_debug) {
  const ReasoningObject& reasoning_object = agent_policy.reasoning_object();
  const std::string constraint_id =
      GetOverlapRegionUniqueId(agent_policy.OverlapRegion());

  const DiscomfortVarying nominal_pass_required_lateral_gap =
      ComputePassRequiredLateralGap(trajectory_info, reasoning_object,
                                    agent_policy.agent_trajectory_info(),
                                    agent_policy.OverlapRegion());
  const DiscomfortVarying nominal_yield_required_lateral_gap =
      ComputeYieldRequiredLateralGap(
          world_context, trajectory_info, reasoning_object,
          agent_policy.agent_trajectory_info(), agent_policy.OverlapRegion());

  ConstraintMutator mutator = agent_policy.AddConstraint(
      pb::FenceType::kCross, nominal_pass_required_lateral_gap,
      nominal_yield_required_lateral_gap, constraint_id,
      constraint_creator_ptr);

  if ((reasoning_object.is_cyclist() &&
       agent_policy.overlap_region_info().is_cut_behind) ||
      agent_policy.overlap_region_info()
          .is_fully_behind_rear_axle_allowing_zero_pass_rlg ||
      reasoning_object.is_unstuck_object_in_narrow_passage() ||
      reasoning_object.is_plg_resettable_for_unstuck()) {
    mutator.set_allow_ignoring_min_range_lat_gap();
  }

  mutator.set_pass_extra_time(ComputePassExtraTime(
      agent_policy.agent_trajectory_info(), reasoning_object.was_pass(),
      reasoning_object.was_yield()));
  mutator.set_yield_extra_time(ComputeYieldExtraTime(
      world_context, reasoning_object, agent_policy.agent_trajectory_info(),
      agent_policy.OverlapRegion()));
  const DiscomfortVarying yield_extra_distance = ComputeYieldExtraDistance(
      world_context, trajectory_info, agent_policy, reasoning_object,
      agent_policy_debug != nullptr
          ? agent_policy_debug->mutable_policy_debug_str()
          : nullptr);
  mutator.set_yield_extra_distance(yield_extra_distance);
  if (FLAGS_planning_enable_uturn_unstuck &&
      reasoning_object.is_potential_uturn_blockage()) {
    mutator.set_min_creep_distance(
        kReducedMinCreepDistForPotentialUTurnBlockageInMeter);
    mutator.set_yield_stop_range(DiscomfortVarying(
        kReducedYieldStopRangeForPotentialUTurnBlockageInMeter));
  }
  if (reasoning_object.is_toll_gate_barrier()) {
    // The reduced min creep distance for toll gate barrier to move closer to it
    // so that the camera can capture ego.
    // We also reduce the stop range to a very small value for exact stop point.
    constexpr double kReducedMinCreepDistForTollGateBarrierInMeter = 0.1;
    constexpr double kReducedStopRangeForTollGateBarrierInMeter = 0.02;
    mutator.set_min_creep_distance(
        kReducedMinCreepDistForTollGateBarrierInMeter);
    mutator.set_yield_stop_range(
        DiscomfortVarying(kReducedStopRangeForTollGateBarrierInMeter));
  }
  if (ShouldAllowEmergencyBrake(world_context, reasoning_object,
                                agent_policy.OverlapRegion(),
                                agent_policy.overlap_region_info())) {
    mutator.set_allow_emergency_brake();
  }
  if (FLAGS_planning_enable_speed_efs_for_non_drivable_unknown &&
      reasoning_object.IsUnknownObject()) {
    mutator.set_allow_extra_full_stop();
  }
  if (ShouldAllowMaxSpeed(world_context, reasoning_object)) {
    mutator.set_allow_max_speed();
  }

  if (ShouldActivateHonkForYieldScenario(
          world_context, trajectory_info, agent_policy,
          agent_policy_debug != nullptr
              ? agent_policy_debug->mutable_honk_debug()
              : nullptr)) {
    // The minimum active discomfort level for honk signal in yield scenario.
    constexpr double kMinActiveDiscomfortForHonkSignal = 0.5;
    constexpr int64_t kHonkingHornTimeIntervalInMSec = 3000;  // ms
    if (honk_request_ptr != nullptr) {
      honk_request_ptr->add_honk_constraint(
          constraint_id, kMinActiveDiscomfortForHonkSignal,
          kHonkingHornTimeIntervalInMSec,
          ::planner::pb::HonkScene::YIELD_CONSTRAINT);
    }
  }

  if (agent_policy.yield_always_possible()) {
    SetYieldAlwaysPossibleForHardConstraint(urgency_info, yield_extra_distance,
                                            mutator);
  }

  return true;
}

bool CrossAgentReasoner::AddNominalWithSoftConstraintForPolicy(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const UrgencyInfo& urgency_info, const AgentPolicy& agent_policy,
    ConstraintCreator* constraint_creator_ptr, std::string* debug_str) {
  const std::string constraint_group_id = absl::StrCat(
      GetOverlapRegionUniqueId(agent_policy.OverlapRegion()), "-nominal&soft");
  const ReasoningObject& reasoning_object = agent_policy.reasoning_object();

  {
    const std::string constraint_id =
        GetOverlapRegionUniqueId(agent_policy.OverlapRegion());
    const DiscomfortVarying nominal_pass_required_lateral_gap =
        ComputePassRequiredLateralGap(trajectory_info, reasoning_object,
                                      agent_policy.agent_trajectory_info(),
                                      agent_policy.OverlapRegion());
    const DiscomfortVarying nominal_yield_required_lateral_gap =
        ComputeYieldRequiredLateralGap(
            world_context, trajectory_info, reasoning_object,
            agent_policy.agent_trajectory_info(), agent_policy.OverlapRegion());

    ConstraintMutator nominal_mutator = agent_policy.AddConstraint(
        pb::FenceType::kCross, nominal_pass_required_lateral_gap,
        nominal_yield_required_lateral_gap, constraint_id,
        constraint_creator_ptr);
    if (agent_policy.overlap_region_info()
            .is_fully_behind_rear_axle_allowing_zero_pass_rlg ||
        reasoning_object.is_unstuck_object_in_narrow_passage() ||
        reasoning_object.is_plg_resettable_for_unstuck()) {
      nominal_mutator.set_allow_ignoring_min_range_lat_gap();
    }
    nominal_mutator.set_group_id(constraint_group_id);
    nominal_mutator.set_pass_extra_time(ComputePassExtraTime(
        agent_policy.agent_trajectory_info(), reasoning_object.was_pass(),
        reasoning_object.was_yield()));
    nominal_mutator.set_yield_extra_time(ComputeYieldExtraTime(
        world_context, reasoning_object, agent_policy.agent_trajectory_info(),
        agent_policy.OverlapRegion()));
    const DiscomfortVarying yield_extra_distance =
        ComputeYieldExtraDistance(world_context, trajectory_info, agent_policy,
                                  reasoning_object, debug_str);
    nominal_mutator.set_yield_extra_distance(yield_extra_distance);
    if (FLAGS_planning_enable_uturn_unstuck &&
        reasoning_object.is_potential_uturn_blockage()) {
      nominal_mutator.set_min_creep_distance(
          kReducedMinCreepDistForPotentialUTurnBlockageInMeter);
      nominal_mutator.set_yield_stop_range(DiscomfortVarying(
          kReducedYieldStopRangeForPotentialUTurnBlockageInMeter));
    }
    if (ShouldAllowEmergencyBrake(world_context, reasoning_object,
                                  agent_policy.OverlapRegion(),
                                  agent_policy.overlap_region_info())) {
      nominal_mutator.set_allow_emergency_brake();
    }
    if (FLAGS_planning_enable_speed_efs_for_non_drivable_unknown &&
        reasoning_object.IsUnknownObject()) {
      nominal_mutator.set_allow_extra_full_stop();
    }
    if (ShouldAllowMaxSpeed(world_context, reasoning_object)) {
      nominal_mutator.set_allow_max_speed();
    }
    if (agent_policy.yield_always_possible()) {
      SetYieldAlwaysPossibleForHardConstraint(
          urgency_info, yield_extra_distance, nominal_mutator);
    }
  }

  {
    // Add soft constraint to shape the braking profile.
    const DiscomfortVarying soft_required_lat_gap(
        reasoning_object.ComfortRequiredLateralGap());
    const std::string constraint_id = absl::StrCat(
        GetOverlapRegionUniqueId(agent_policy.OverlapRegion()), "-soft");

    ConstraintMutator soft_mutator =
        agent_policy.AddConstraint(pb::FenceType::kCross, soft_required_lat_gap,
                                   constraint_id, constraint_creator_ptr);
    soft_mutator.set_group_id(constraint_group_id);
    soft_mutator.set_pass_always_possible();
    soft_mutator.set_soft_yield_with_min_a(kSoftYieldMinAccelInMpss);
    soft_mutator.set_yield_extra_time(ComputeYieldExtraTime(
        world_context, reasoning_object, agent_policy.agent_trajectory_info(),
        agent_policy.OverlapRegion()));
    soft_mutator.set_yield_extra_distance(
        DiscomfortVarying(ComputeSoftConstraintYieldExtraDistance(
            world_context, trajectory_info, agent_policy, debug_str)));
    if (FLAGS_planning_enable_uturn_unstuck &&
        reasoning_object.is_potential_uturn_blockage()) {
      soft_mutator.set_min_creep_distance(
          kReducedMinCreepDistForPotentialUTurnBlockageInMeter);
      soft_mutator.set_yield_stop_range(DiscomfortVarying(
          kReducedYieldStopRangeForPotentialUTurnBlockageInMeter));
    }
    soft_mutator.set_yield_agent_reaction(ReactionTimes(), SlowDownParams());
  }
  return true;
}

bool CrossAgentReasoner::AddStrictPartOfYieldStrictWithSoftConstraint(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const UrgencyInfo& urgency_info, const AgentPolicy& agent_policy,
    ConstraintCreator* constraint_creator_ptr,
    const std::string& constraint_group_id) {
  const ReasoningObject& reasoning_object = agent_policy.reasoning_object();
  const std::string yield_strict_constraint_id = absl::StrCat(
      GetOverlapRegionUniqueId(agent_policy.OverlapRegion()), "-strict-yield");

  const DiscomfortVarying pass_required_lateral_gap =
      ComputePassRequiredLateralGap(trajectory_info, reasoning_object,
                                    agent_policy.agent_trajectory_info(),
                                    agent_policy.OverlapRegion());
  const bool allow_zero_pass_rlg =
      agent_policy.overlap_region_info()
          .is_fully_behind_rear_axle_allowing_zero_pass_rlg;

  ConstraintMutator yield_strict_mutator =
      agent_policy.AddYieldStrictConstraint(
          pb::FenceType::kCross, reasoning_object.required_lateral_gap(),
          yield_strict_constraint_id, constraint_creator_ptr,
          allow_zero_pass_rlg);
  yield_strict_mutator.set_group_id(constraint_group_id);

  SetPassOptionForYieldStrictConstraint(agent_policy, reasoning_object,
                                        pass_required_lateral_gap,
                                        yield_strict_mutator);

  SetYieldOptionForYieldStrictConstraint(world_context, urgency_info,
                                         reasoning_object, agent_policy,
                                         yield_strict_mutator);

  if (ShouldAllowEmergencyBrake(world_context, reasoning_object,
                                agent_policy.OverlapRegion(),
                                agent_policy.overlap_region_info())) {
    yield_strict_mutator.set_allow_emergency_brake();
  }
  if (FLAGS_planning_enable_speed_efs_for_non_drivable_unknown &&
      reasoning_object.IsUnknownObject()) {
    yield_strict_mutator.set_allow_extra_full_stop();
  }
  if (ShouldAllowMaxSpeed(world_context, reasoning_object)) {
    yield_strict_mutator.set_allow_max_speed();
  }
  if (agent_policy.yield_always_possible()) {
    SetYieldAlwaysPossibleForHardConstraint(
        urgency_info, /*yield_extra_distance=*/
        DiscomfortVarying(kMinSafetyDistanceToAgentInMeter),
        yield_strict_mutator);
  }

  return true;
}

bool CrossAgentReasoner::AddSoftPartOfYieldStrictWithSoftConstraint(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const AgentPolicy& agent_policy, ConstraintCreator* constraint_creator_ptr,
    const std::string& constraint_group_id, std::string* debug_str) {
  const ReasoningObject& reasoning_object = agent_policy.reasoning_object();
  const std::string soft_constraint_id = absl::StrCat(
      GetOverlapRegionUniqueId(agent_policy.OverlapRegion()), "-soft");
  ConstraintMutator soft_mutator = agent_policy.AddConstraint(
      pb::FenceType::kCross, reasoning_object.required_lateral_gap(),
      soft_constraint_id, constraint_creator_ptr);
  soft_mutator.set_group_id(constraint_group_id);

  // When used together with nominal/strict constraint, soft constraint
  // is only used to shape the yield profile and should not impact pass
  // option.
  soft_mutator.set_pass_always_possible();

  SetYieldOptionForSoftConstraint(world_context, trajectory_info, agent_policy,
                                  reasoning_object, soft_mutator, debug_str);
  return true;
}

bool CrossAgentReasoner::AddSoftPartOfCreepConstraints(
    const TrajectoryInfo& trajectory_info, const AgentPolicy& agent_policy,
    ConstraintCreator* constraint_creator_ptr) {
  const ReasoningObject& reasoning_object = agent_policy.reasoning_object();
  const std::string soft_creep_constraint_id = absl::StrCat(
      GetOverlapRegionUniqueId(agent_policy.OverlapRegion()), "-soft-creep");

  ConstraintMutator soft_creep_mutator = agent_policy.AddConstraintForCreeping(
      pb::FenceType::kCross,
      DiscomfortVarying(reasoning_object.ComfortRequiredLateralGap()),
      soft_creep_constraint_id,
      /*creeping_speed=*/kMinCreepingSpeedInMps,
      /*yield_min_a=*/kSoftYieldMinAccelInMpss,
      /*use_new_creep_api=*/true, constraint_creator_ptr);

  // Pass settings. The pass side of the soft creep constraint is to determine
  // if ego should keep creeping or pass without hesitation.
  soft_creep_mutator.set_pass_required_lat_gap(ComputePassRequiredLateralGap(
      trajectory_info, reasoning_object, agent_policy.agent_trajectory_info(),
      agent_policy.OverlapRegion()));
  soft_creep_mutator.set_pass_extra_time(ComputePassExtraTime(
      agent_policy.agent_trajectory_info(), reasoning_object.was_pass(),
      reasoning_object.was_yield()));

  soft_creep_mutator.set_allow_ignoring_min_range_lat_gap();

  SetComfortPassAgentReactionForSoftCreepConstraint(
      agent_policy.agent_reaction_params(), soft_creep_mutator);

  // Yield settings
  soft_creep_mutator.set_yield_required_lat_gap(
      DiscomfortVarying(reasoning_object.ComfortRequiredLateralGap()));
  soft_creep_mutator.set_yield_extra_distance(
      DiscomfortVarying(kComfortYieldExtraDistanceForSoftConstraintInMeter));

  return true;
}

bool CrossAgentReasoner::AddYieldStrictWithSoftConstraintForPolicy(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const UrgencyInfo& urgency_info, const AgentPolicy& agent_policy,
    ConstraintCreator* constraint_creator_ptr, std::string* debug_str) {
  const std::string constraint_group_id =
      absl::StrCat(GetOverlapRegionUniqueId(agent_policy.OverlapRegion()),
                   "-strict&soft-yield");
  AddStrictPartOfYieldStrictWithSoftConstraint(
      world_context, trajectory_info, urgency_info, agent_policy,
      constraint_creator_ptr, constraint_group_id);
  AddSoftPartOfYieldStrictWithSoftConstraint(
      world_context, trajectory_info, agent_policy, constraint_creator_ptr,
      constraint_group_id, debug_str);

  return true;
}

bool CrossAgentReasoner::AddNominalConstraintForOverlapSegment(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const UrgencyInfo& urgency_info, const AgentPolicy& agent_policy,
    ConstraintCreator* constraint_creator_ptr, HonkRequest* honk_request_ptr,
    pb::CrossAgentPolicyDebug* agent_policy_debug) {
  const ReasoningObject& reasoning_object = agent_policy.reasoning_object();
  const pb::OverlapRegion& overlap_region = agent_policy.OverlapRegion();
  // TODO(speed): Consolidate the overlap trimming logic in a single function
  // with the same method to avoid duplicate codes spreading across different
  // topics.
  const std::pair<int, int> overlap_segment =
      agent_policy.agent_trajectory_info()
              .is_vehicle_turning_from_crossing_to_oncoming_wrt_ego_current_lane_pose()
          ? ComputeTruncatedOverlapRegionSegmentInCrossingToOncomingTrajectory(
                overlap_region)
          : ComputeTheFirstCrossingOverlapRegionSegment(overlap_region);

  if (agent_policy.agent_trajectory_info()
          .is_vehicle_turning_from_crossing_to_oncoming_wrt_ego_current_lane_pose() &&
      overlap_segment.second !=
          agent_policy.OverlapRegion().overlap_slices_size() &&
      agent_policy_debug != nullptr) {
    absl::StrAppendFormat(
        agent_policy_debug->mutable_policy_debug_str(),
        "truncate overlap after rel time %.1f sec for X oncoming turning;",
        agent_policy.OverlapRegion()
            .overlap_slices()[overlap_segment.second]
            .relative_time_in_sec());
  }
  // In case all overlaps are trimmed out, directly return false to avoid DCHECK
  // failure;
  if (overlap_segment.second == 0) {
    return false;
  }

  const DiscomfortVarying nominal_pass_required_lateral_gap =
      ComputePassRequiredLateralGap(trajectory_info, reasoning_object,
                                    agent_policy.agent_trajectory_info(),
                                    agent_policy.OverlapRegion());
  const DiscomfortVarying nominal_yield_required_lateral_gap =
      ComputeYieldRequiredLateralGap(
          world_context, trajectory_info, reasoning_object,
          agent_policy.agent_trajectory_info(), agent_policy.OverlapRegion());

  const std::string constraint_id = GetConstraintIdForNominalSegmentConstraint(
      overlap_region, overlap_segment);

  ConstraintMutator mutator = agent_policy.AddConstraintWithSegmentIndex(
      pb::FenceType::kCross, nominal_pass_required_lateral_gap,
      nominal_yield_required_lateral_gap, constraint_id, overlap_segment.first,
      overlap_segment.second, constraint_creator_ptr);
  if (nominal_pass_required_lateral_gap(Discomforts::kMax) == 0.0 ||
      reasoning_object.is_unstuck_object_in_narrow_passage() ||
      reasoning_object.is_plg_resettable_for_unstuck()) {
    mutator.set_allow_ignoring_min_range_lat_gap();
  }

  mutator.set_pass_extra_time(ComputePassExtraTime(
      agent_policy.agent_trajectory_info(), reasoning_object.was_pass(),
      reasoning_object.was_yield()));
  mutator.set_yield_extra_time(ComputeYieldExtraTime(
      world_context, reasoning_object, agent_policy.agent_trajectory_info(),
      agent_policy.OverlapRegion()));
  const DiscomfortVarying yield_extra_distance = ComputeYieldExtraDistance(
      world_context, trajectory_info, agent_policy, reasoning_object,
      agent_policy_debug != nullptr
          ? agent_policy_debug->mutable_policy_debug_str()
          : nullptr);
  mutator.set_yield_extra_distance(yield_extra_distance);
  if (FLAGS_planning_enable_uturn_unstuck &&
      reasoning_object.is_potential_uturn_blockage()) {
    mutator.set_min_creep_distance(
        kReducedMinCreepDistForPotentialUTurnBlockageInMeter);
    mutator.set_yield_stop_range(DiscomfortVarying(
        kReducedYieldStopRangeForPotentialUTurnBlockageInMeter));
  }
  if (ShouldAllowEmergencyBrake(world_context, reasoning_object, overlap_region,
                                agent_policy.overlap_region_info())) {
    mutator.set_allow_emergency_brake();
  }
  if (FLAGS_planning_enable_speed_efs_for_non_drivable_unknown &&
      reasoning_object.IsUnknownObject()) {
    mutator.set_allow_extra_full_stop();
  }
  if (ShouldAllowMaxSpeed(world_context, reasoning_object)) {
    mutator.set_allow_max_speed();
  }
  // Consider vehicles for now to keep the honk logic no-op when migrating the
  // crossing-oncoming vehicle's interaction strategy from
  // |AddNominalConstraintForPolicy| to |AddNominalConstraintForOverlapSegment|.
  // TODO(speed): Develop the honk logic specifically for
  // |AddNominalConstraintForOverlapSegment|.
  if (reasoning_object.is_vehicle() &&
      ShouldActivateHonkForYieldScenario(
          world_context, trajectory_info, agent_policy,
          agent_policy_debug != nullptr
              ? agent_policy_debug->mutable_honk_debug()
              : nullptr)) {
    // The minimum active discomfort level for honk signal in yield scenario.
    constexpr double kMinActiveDiscomfortForHonkSignal = 0.5;
    constexpr int64_t kHonkingHornTimeIntervalInMSec = 3000;  // ms
    if (honk_request_ptr != nullptr) {
      honk_request_ptr->add_honk_constraint(
          constraint_id, kMinActiveDiscomfortForHonkSignal,
          kHonkingHornTimeIntervalInMSec,
          ::planner::pb::HonkScene::YIELD_CONSTRAINT);
    }
  }

  if (agent_policy.yield_always_possible()) {
    SetYieldAlwaysPossibleForHardConstraint(urgency_info, yield_extra_distance,
                                            mutator);
  }

  return true;
}

bool CrossAgentReasoner::AddConstraintForPrediction(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const UrgencyInfo& urgency_info,
    const PredictionDecision& prediction_decision,
    ConstraintCreator* constraint_creator_ptr, HonkRequest* honk_request_ptr,
    pb::CrossAgentTrajectoryDebug* trajectory_debug) {
  bool constraint_added = false;
  if (prediction_decision.Ignore()) {
    DCHECK(prediction_decision.agent_policies().empty());
    return false;
  }

  for (const AgentPolicy& agent_policy : prediction_decision.agent_policies()) {
    pb::CrossAgentPolicyDebug agent_policy_debug;
    // NOTE: CrossAgentReasoner only handle agent policy with CROSS_AGENT tag.
    if (agent_policy.reasoner_in_charge() != pb::ReasonerId::CROSS_AGENT) {
      continue;
    }

    constraint_added |= AddConstraintForPolicy(
        world_context, trajectory_info, urgency_info, agent_policy,
        constraint_creator_ptr, honk_request_ptr,
        trajectory_debug != nullptr ? &agent_policy_debug : nullptr);
    if (trajectory_debug != nullptr) {
      *trajectory_debug->add_agent_policies() = std::move(agent_policy_debug);
    }
  }

  return constraint_added;
}

bool CrossAgentReasoner::Reason(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const UrgencyInfo& urgency_info,
    const std::vector<PredictionDecisionMakerOutput>&
        wrapped_prediction_decisions,
    ConstraintCreator* constraint_creator_ptr, HonkRequest* honk_request_ptr,
    speed::pb::ReasoningDebug* debug_proto_ptr) {
  pb::CrossAgentReasonerDebug* cross_agent_reasoner_debug =
      debug_proto_ptr ? debug_proto_ptr->mutable_reasoner_debug()
                            ->mutable_cross_agent_reasoner()
                      : nullptr;
  bool constraint_added = false;

  for (const PredictionDecisionMakerOutput& output :
       wrapped_prediction_decisions) {
    if (!output.HasAnyAgentPolicyHandledByGivenReasoner(id())) {
      continue;
    }

    // The agent should be handled by cross agent reasoner.
    // TODO(waylon): Move to a function when the debug info is complicated.
    pb::CrossAgentDebug* agent_debug =
        cross_agent_reasoner_debug ? cross_agent_reasoner_debug->add_agents()
                                   : nullptr;
    if (agent_debug) {
      agent_debug->set_object_id(output.reasoning_object().id());
    }

    for (const PredictionDecision& prediction_decision :
         output.prediction_decisions()) {
      // Handle an agent's one predicted trajectory.
      pb::CrossAgentTrajectoryDebug* trajectory_debug =
          agent_debug ? agent_debug->add_agent_trajectories() : nullptr;
      if (trajectory_debug) {
        trajectory_debug->set_trajectory_id(
            prediction_decision.trajectory_id());
      }

      constraint_added |= AddConstraintForPrediction(
          world_context, trajectory_info, urgency_info, prediction_decision,
          constraint_creator_ptr, honk_request_ptr, trajectory_debug);
    }
  }

  return constraint_added;
}

}  // namespace reasoner
}  // namespace speed
}  // namespace planner
