# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: telemetry.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x0ftelemetry.proto\x12\x02pb"Z\n\x10TelemetryMessage\x12\x11\n\ttimestamp\x18\x01 \x01(\x03\x12\x0e\n\x06\x63\x61r_id\x18\x02 \x01(\t\x12\x10\n\x08msg_type\x18\x03 \x01(\t\x12\x11\n\tmsg_value\x18\x04 \x01(\x0c"!\n\x0fRosParamMessage\x12\x0e\n\x06region\x18\x01 \x01(\t"\xd2\x01\n\x13TopicControlMessage\x12\x11\n\ttimestamp\x18\x01 \x01(\x04\x12\x36\n\x04mode\x18\x02 \x01(\x0e\x32(.pb.TopicControlMessage.TopicControlMode\x12\x12\n\ntopic_name\x18\x03 \x01(\t\x12\x10\n\x08interval\x18\x04 \x01(\r\x12\x10\n\x08\x64uration\x18\x05 \x01(\r"8\n\x10TopicControlMode\x12\x0b\n\x07UNKNOWN\x10\x00\x12\n\n\x06\x45NABLE\x10\x01\x12\x0b\n\x07\x44ISABLE\x10\x02\x42\x16Z\x14../protocol;protocolb\x06proto3'
)

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, "telemetry_pb2", globals())
if _descriptor._USE_C_DESCRIPTORS == False:

    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"Z\024../protocol;protocol"
    _TELEMETRYMESSAGE._serialized_start = 23
    _TELEMETRYMESSAGE._serialized_end = 113
    _ROSPARAMMESSAGE._serialized_start = 115
    _ROSPARAMMESSAGE._serialized_end = 148
    _TOPICCONTROLMESSAGE._serialized_start = 151
    _TOPICCONTROLMESSAGE._serialized_end = 361
    _TOPICCONTROLMESSAGE_TOPICCONTROLMODE._serialized_start = 305
    _TOPICCONTROLMESSAGE_TOPICCONTROLMODE._serialized_end = 361
# @@protoc_insertion_point(module_scope)
