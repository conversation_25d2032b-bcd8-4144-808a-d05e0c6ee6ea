#ifndef ONBOARD_PLANNER_WORLD_MODEL_WAYPOINT_SEARCH_RELAX_LANE_CHANGE_PARAMETER_H_
#define ONBOARD_PLANNER_WORLD_MODEL_WAYPOINT_SEARCH_RELAX_LANE_CHANGE_PARAMETER_H_

#include <map>
#include <optional>
#include <set>

#include "hdmap/lib/hdmap.h"
#include "hdmap/lib/hdmap_util.h"
#include "hdmap/lib/relax_lane_marking_util.h"
#include "planner/planning_gflags.h"
#include "planner/utility/common/common_utility.h"
#include "planner/world_model/drivable_lane_reasoner/drivable_lane_reasoner.h"
#include "planner/world_model/regional_map/stuck_avoidance/relax_lane_direction_parameter.h"
#include "planner/world_model/stuck_avoidance_guidance/stuck_avoidance_guidance_info.h"
#include "planner/world_model/waypoint_search/waypoint.h"
#include "planner/world_model/waypoint_search/waypoint_edge.h"
#include "planner/world_model/waypoint_search/waypoint_lane_utility.h"
#include "planner_protos/relax_lane_sequence_parameter.pb.h"
#include "pnc_map_service/joint_pnc_map_service.h"
#include "pnc_map_service/map_elements/lane.h"
#include "pnc_map_service/map_elements/pnc_object.h"
#include "pnc_map_service/pnc_map_service.h"
#include "pnc_map_service/util/pnc_map_service_utility.h"

namespace planner {
namespace lane_selection {

// The parameter for relax lane change at a waypoint and a specific direction.
struct RelaxLaneChangeParameter {
  // TODO(zhanshushi): Do we need to involve the source? Or we just use the
  // types to handle this?
  std::set<pb::RelaxLaneChangeParamSource> sources;
  std::set<pb::RelaxLaneChangeConstrainType> relaxed_constraint_types;
  std::optional<double> mini_required_distance = std::nullopt;
};

// This class stores the parameters for relaxing edges from different source.
class WaypointRelaxEdgeParameters {
 public:
  WaypointRelaxEdgeParameters() = default;
  WaypointRelaxEdgeParameters(
      const std::optional<std::map<int64_t, ViolableLaneMarkingRange>>&
          violable_lane_marking_ranges)
      : violable_lane_marking_ranges_(violable_lane_marking_ranges) {}

  // Clears fields.
  // [Note]: Please call this function every cycle. And all fields should be
  // added in this interface.
  void Clear();

  // Accessors
  const std::map<int64_t,
                 std::map<hdmap::RelaxDirection, RelaxLaneChangeParameter>>&
  waypoint_relax_lane_change_map() const {
    return waypoint_relax_lane_change_map_;
  }
  const std::optional<std::map<int64_t, ViolableLaneMarkingRange>>&
  violable_lane_marking_ranges() const {
    return violable_lane_marking_ranges_;
  }

  const std::map<int64_t, pnc_map::CrossRoadLaneChangeLanes>&
  cross_road_lane_change_map() const {
    return cross_road_lane_change_map_;
  }

  const std::map<hdmap::LaneMarkingId, hdmap::util::RelaxedLaneMarkingInfo>&
  relaxed_lane_marking_info_map() const {
    return relaxed_lane_marking_info_map_;
  }

  const std::set<int64_t>& allow_immediate_stop_source_waypoint_ids() const {
    return allow_immediate_stop_source_waypoint_ids_;
  }

  const lane_selection::RelaxLaneDirectionParamInfo&
  relax_lane_direction_lane_follow_info() const {
    return relax_lane_direction_lane_follow_info_;
  }

  lane_selection::RelaxLaneDirectionParamInfo*
  mutable_relax_lane_direction_lane_follow_info() {
    return &relax_lane_direction_lane_follow_info_;
  }

  bool allow_to_lane_change_in_junction_no_from_ego() const {
    return allow_to_lane_change_in_junction_no_from_ego_;
  }

  // Setters.
  void set_violable_lane_marking_ranges(
      const std::optional<std::map<int64_t, ViolableLaneMarkingRange>>&
          violable_lane_marking_ranges) {
    if (!violable_lane_marking_ranges.has_value()) {
      return;
    }
    violable_lane_marking_ranges_ = violable_lane_marking_ranges.value();
  }

  void set_violable_lane_marking_info_map(
      const std::map<hdmap::LaneMarkingId, hdmap::util::RelaxedLaneMarkingInfo>&
          relaxed_lane_marking_info_map) {
    if (relaxed_lane_marking_info_map.empty()) {
      return;
    }
    relaxed_lane_marking_info_map_ = relaxed_lane_marking_info_map;
  }

  void set_relax_scenario_types(
      const std::set<pb::RelaxLaneSequenceScenarioType>& relax_scenario_types) {
    relax_scenario_types_ = relax_scenario_types;
  }

  void set_relax_lane_direction_lane_follow_info(
      const lane_selection::RelaxLaneDirectionParamInfo&
          relax_lane_direction_lane_follow_info) {
    relax_lane_direction_lane_follow_info_ =
        relax_lane_direction_lane_follow_info;
  }

  void set_allow_to_lane_change_in_junction_no_from_ego(
      bool allow_to_lane_change_in_junction_no_from_ego) {
    allow_to_lane_change_in_junction_no_from_ego_ =
        allow_to_lane_change_in_junction_no_from_ego;
  }

  // Adds relax parameter for a waypoint.
  void AddWaypointRelaxLaneChangeInfo(
      const pnc_map::JointPncMapService& joint_pnc_map_service,
      const DrivableLaneReasoner& drivable_lane_reasoner,
      const Waypoint& waypoint, hdmap::RelaxDirection direction,
      pb::RelaxLaneChangeParamSource source,
      const std::optional<double>& required_distance);

  // Updates the source and target lanes for cross road lane change.
  void UpdateCrossRoadLaneChangeMap(
      const pnc_map::JointPncMapService& joint_pnc_map_service,
      const std::vector<const pnc_map::Lane*>& current_lanes,
      const std::vector<pb::RelaxLaneChangeParam>& relax_lane_change_params);

  // Adds waypoints who are allow to used as an immediate stop of lane follow
  // edge.
  // [Note]: The first waypoint should be a key point.
  void AddAllowImmediateStopSourceWaypoints(
      const std::vector<const Waypoint*>& waypoints);

  // Returns true if a lane change edge is added by relaxing lane change
  // constraint for stuck avoidance.
  bool IsLaneChangeForStuckAvoidance(const WaypointEdge& edge,
                                     bool check_soft_blockage) const;

  // Returns the relax lane change parameters for  a lane change edge, if it
  // exists.
  const RelaxLaneChangeParameter* GetEdgeRelaxLaneChangeParameter(
      const Waypoint& waypoint, bool to_left) const;

  // Returns the relax lane change parameters for  a lane change edge, if it
  // exists.
  const RelaxLaneChangeParameter* GetEdgeRelaxLaneChangeParameter(
      const WaypointEdge& edge) const;

  // Returns true if a lane change edge is relaxed by a specific source.
  bool IsLaneChangeEdgeRelaxedBySource(
      const Waypoint& waypoint, bool to_left,
      pb::RelaxLaneChangeParamSource source) const;

  // Returns true if a lane change edge is relaxed by a specific source.
  bool IsLaneChangeEdgeRelaxedBySource(
      const WaypointEdge& edge, pb::RelaxLaneChangeParamSource source) const;

  // Returns true if a lane change edge is relaxed by a specific constraint
  // type.
  bool IsLaneChangeEdgeRelaxedByConstraintType(
      const Waypoint& waypoint, bool to_left,
      pb::RelaxLaneChangeConstrainType type) const;

  // Returns true if  a lane change edge is relaxed by a specific constraint
  // type.
  bool IsLaneChangeEdgeRelaxedByConstraintType(
      const WaypointEdge& edge, pb::RelaxLaneChangeConstrainType type) const;

  // Returns true if the lane can be a source lane of cross road lane change.
  bool IsSourceLaneForCrossRoadLaneChange(int64_t lane_id) const;

  // Returns true if the lane can be a target lane of cross road lane change.
  bool IsSourceLaneForCrossRoadLaneChange(int64_t lane_id, bool to_left) const;

  // Returns the neighbor lane on neighbor road for a lane.
  // If the lane is not allow to lane change to neighbor road, nullptr is
  // expected.
  const pnc_map::Lane* GetLaneChangeLaneFromNeighborRoad(int64_t lane_id,
                                                         bool to_left) const;

  // Returns true if a lane follow edge start from a waypoint can stop at next
  // normal waypoint.
  bool CanLaneFollowEdgeStopAtNextWaypoint(const Waypoint& waypoint) const;

 private:
  void AddCrossRoadLaneChangeLanes(
      const pnc_map::JointPncMapService& joint_pnc_map_service,
      const pnc_map::Lane* curr_lane,
      const pb::RelaxLaneChangeParam& relax_lane_change_param);

  // Returns true if a lane marking is set as violable by specific data source.
  bool IsViolableLaneMarkingBySource(
      hdmap::LaneMarkingId lane_marking_id, double arc_length,
      bool is_left_lane_change,
      hdmap::ViolateLaneMarkingDataSource source) const;

  // The lane change relax parameters for waypoints.
  // The key is waypoint ID.
  // [Note]: The key |RelaxDirection| would only contains |kToLeft| and
  // |kToRight|. The |kToBoth| will be convert to two records.
  std::map<int64_t, std::map<hdmap::RelaxDirection, RelaxLaneChangeParameter>>
      waypoint_relax_lane_change_map_;

  // The set of waypoint IDs, a lane follow edge starts from any waypoint in
  // this set can immediate stop at next waypoint.
  std::set<int64_t> allow_immediate_stop_source_waypoint_ids_;

  // These violable lane markings.
  std::optional<std::map<int64_t, ViolableLaneMarkingRange>>
      violable_lane_marking_ranges_ = std::nullopt;

  // The cross road lane change map. The key is source lane id. The value is
  // target lane found from neighbor road.
  std::map<int64_t, pnc_map::CrossRoadLaneChangeLanes>
      cross_road_lane_change_map_;

  // The violable lane markings' map. The key is lane marking ID. The
  // value is the relaxed information.
  std::map<hdmap::LaneMarkingId, hdmap::util::RelaxedLaneMarkingInfo>
      relaxed_lane_marking_info_map_;

  // The relax scenario type that ego is processing.
  std::set<pb::RelaxLaneSequenceScenarioType> relax_scenario_types_;

  // It holds the information that we can relax the turn direction of lanes to
  // connect a lane follow edge. For example, we connect a lane follow edge to
  // turn right directly from a lane with turn direction of straight.
  lane_selection::RelaxLaneDirectionParamInfo
      relax_lane_direction_lane_follow_info_;
  // True if lane change not from ego in junction is allowed.
  bool allow_to_lane_change_in_junction_no_from_ego_ = false;
};

}  // namespace lane_selection
}  // namespace planner

#endif  // ONBOARD_PLANNER_WORLD_MODEL_WAYPOINT_SEARCH_RELAX_LANE_CHANGE_PARAMETER_H_
