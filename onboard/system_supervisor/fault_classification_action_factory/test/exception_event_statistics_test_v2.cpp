#include <bitset>
#include <functional>
#include <glog/logging.h>
#include <gtest/gtest.h>
#include <memory>
#include <ros/ros.h>
#include <string>
#include <vector>

#include <google/protobuf/util/json_util.h>
#include "action_factory_protos/ss_exception_event_statistics.pb.h"
#include "av_comm/car_id.h"
#include "av_comm/components.h"
#include "av_comm/node_names.h"
#include "av_comm/params.h"
#include "av_comm/system_control_config.h"
#include "base/base_dir.h"
#include "base/elapsed_timer.h"
#include "base/now.h"
#include "common/param_getter.h"
#include "exception_event_statistics.h"
#include "mock_param_getter.h"
#include "voy_protos/fault_level_manifest.pb.h"
#include "voy_protos/health.pb.h"
#include "voy_protos/system_control.pb.h"
#include "voy_protos/system_event.pb.h"

namespace system_supervisor {
namespace action_factory {
namespace ss_exception_event_statistics {
namespace {
constexpr time_t kExceptionEventTestBaseTimestamp = 1724428800000LL;
constexpr char kCarIDForTest[] = "17123";
constexpr char kGen4EtCarIDForTest[] = "10244";
constexpr char kACUVersionForTest[] =
    "1.gen3-driverless-release-20250321-odd0.782";
constexpr char kFaultLevelManifestForTest[] =
    R"({"fault_levels":[{"fault_unit":[
    {"node":"tracking_node","component":"node","component_instance":"test_instance",
    "code":1000,"fault_level_value":{
      "system_available":"MAIN_AND_SOFTWARE_REDUNDANCY_UNAVAILABLE",
      "business_impact":"HIGH_OPERATIONAL_RISK",
      "park_urgency_level":"STOP_VALUE_06","manual_fault_level":"ERROR",
      "manual_process_code":"SOFTWARE_ERROR",
      "fault_exception_type":"ALGORITHM"},
    "fault_recovery":{"fault_recover_type":"RECOVERY_NORMALLY"}},
    {"node":"planner_node","component":"node","component_instance":"test_instance",
    "code":1005,"fault_level_value":{
      "system_available":"ONLY_MAIN_SYSTEM_UNAVAILABLE",
      "business_impact":"HIGH_OPERATIONAL_RISK",
      "park_urgency_level":"STOP_VALUE_06","manual_fault_level":"ERROR",
      "manual_process_code":"SOFTWARE_ERROR",
      "fault_exception_type":"ALGORITHM"},
      "fault_recovery":{"fault_recover_type":"RECOVERY_NORMALLY"}}]}],
  "version":"20250108-v1.93"})";
}  // namespace

class ExceptionEventStatisticsTestV2 : public ::testing::Test {
 protected:
  // Helper to create a Fault
  pb::Fault CreateFaultPb(const std::string& component,
                          const std::string& instance, int code,
                          const std::string& fault_level, uint64_t timestamp,
                          bool is_major = false) {
    pb::Fault fault;
    fault.set_component(component);
    fault.set_component_instance(instance);
    fault.set_code(code);
    fault.set_fault_level(fault_level);
    fault.set_timestamp(timestamp);  // Fault's own timestamp
    fault.set_is_major_fault(is_major);
    return fault;
  }

  // Helper to create a SystemState
  std::shared_ptr<pb::SystemState> CreateSystemState(
      const std::string& system_level_str, uint64_t af_dispose_time) {
    auto system_state = std::make_shared<pb::SystemState>();
    system_state->set_system_level(system_level_str);
    system_state->set_af_dispose_time(af_dispose_time);
    return system_state;
  }

  // Helper to create a SystemLevel
  std::string GetSystemLevel(const std::string& system_available,
                             int risk_value, int stop_value) {
    std::bitset<4> system_available_bitset(system_available);
    uint8_t value = system_available_bitset.to_ulong();
    char hex_char = "0123456789abcdef"[value];
    return std::string(1, hex_char) + std::to_string(risk_value) +
           std::to_string(stop_value);
  }

  std::unique_ptr<ExceptionEventStatistics> GetExceptionEventStatisticsInstance(
      bool is_gen4_et_env, const IParamGetter& param_getter) {
    auto stats_manager = std::make_unique<ExceptionEventStatistics>(
        fault_level_manifest_, param_getter);
    stats_manager->is_unit_test_env = true;
    stats_manager->acu_version_ = kACUVersionForTest;
    stats_manager->exception_event_timestamp_ =
        kExceptionEventTestBaseTimestamp;
    stats_manager->startup_timestamp_ = kExceptionEventTestBaseTimestamp / 1000;
    stats_manager->SetCarId(is_gen4_et_env ? kGen4EtCarIDForTest
                                           : kCarIDForTest);
    stats_manager->CheckVehicleRuntimeEnv();
    return stats_manager;
  }

  void SetUp() override {
    FLAGS_ss_exception_event_statistics_enable_filter = false;
    // Load manifest
    fault_level_manifest_ = std::make_shared<FaultLevelManifest>();
    ASSERT_TRUE(fault_level_manifest_->LoadFaultExceptionType(
        kFaultLevelManifestForTest));
  }

  void TearDown() override { fault_level_manifest_.reset(); }

  std::shared_ptr<FaultLevelManifest> fault_level_manifest_;
};

TEST_F(ExceptionEventStatisticsTestV2, UpdateVehicleExceptionId_Normal) {
  MockParamGetter param_getter;
  auto stats = GetExceptionEventStatisticsInstance(false, param_getter);
  time_t curr_timestamp = kExceptionEventTestBaseTimestamp + 1000;
  // We need to control exception_event_timestamp_ for this private method test
  stats->exception_event_timestamp_ = curr_timestamp;
  // No exception id yet
  std::string curr_system_level = GetSystemLevel("0000", 0, 0);
  stats->UpdateVehicleExceptionId(curr_system_level);
  std::string id1 = stats->vehicle_exception_id_.second;
  EXPECT_TRUE(id1.empty());

  // New exception, but vehicle_exception_id_ is the same
  stats->exception_event_timestamp_ = curr_timestamp + 1000;
  curr_system_level = GetSystemLevel("0001", 0, 0);
  stats->UpdateVehicleExceptionId(curr_system_level);
  std::string id2 = stats->vehicle_exception_id_.second;
  EXPECT_TRUE(id2.empty());

  // New exception, and vehicle_exception_id_ is different due to system level
  // change
  stats->exception_event_timestamp_ = curr_timestamp + 2000;
  curr_system_level = GetSystemLevel("0101", 0, 0);
  stats->UpdateVehicleExceptionId(curr_system_level);
  std::string id3 = stats->vehicle_exception_id_.second;
  EXPECT_EQ(id3, std::string(kCarIDForTest) + "-" +
                     std::to_string(curr_timestamp + 2000) + "-" +
                     curr_system_level);

  // New exception, and vehicle_exception_id_ is different due to risk value
  // change
  stats->exception_event_timestamp_ = curr_timestamp + 3000;
  curr_system_level = GetSystemLevel("0101", 1, 0);
  stats->UpdateVehicleExceptionId(curr_system_level);
  std::string id4 = stats->vehicle_exception_id_.second;
  EXPECT_EQ(id4, std::string(kCarIDForTest) + "-" +
                     std::to_string(curr_timestamp + 3000) + "-" +
                     curr_system_level);

  // New exception, but vehicle_exception_id_ is the same.
  stats->exception_event_timestamp_ = curr_timestamp + 4000;
  curr_system_level = GetSystemLevel("0100", 1, 0);
  stats->UpdateVehicleExceptionId(curr_system_level);
  std::string id5 = stats->vehicle_exception_id_.second;
  EXPECT_EQ(id5, id4);
}

TEST_F(ExceptionEventStatisticsTestV2,
       CheckSystemState_NewFault_GeneratesAppearedEvent) {
  MockParamGetter param_getter;
  ON_CALL(param_getter, getTripID(testing::Eq(true)))
      .WillByDefault(
          testing::Return(std::make_optional("/trip_id17188_20250416_145335")));
  auto stats = GetExceptionEventStatisticsInstance(false, param_getter);
  std::string curr_system_level = GetSystemLevel("0010", 0, 0);
  auto system_state = CreateSystemState(curr_system_level,
                                        kExceptionEventTestBaseTimestamp - 10);
  auto* nhi = system_state->add_node_health_infos();
  nhi->set_node_name("tracking_node");
  // Use enum for fault_level
  *nhi->mutable_health()->add_faults() =
      CreateFaultPb("node", "test_instance", 1000, "SOFTWARE_ERROR",
                    kExceptionEventTestBaseTimestamp - 10);

  std::vector<std::shared_ptr<::action_factory::pb::SsExceptionEventStatistics>>
      events;

  time_t fault_event_time = kExceptionEventTestBaseTimestamp + 40;
  stats->exception_event_timestamp_ = fault_event_time;

  stats->CheckSystemState(system_state, events, voy::VehicleMode::AUTO_FULL,
                          false);

  ASSERT_EQ(events.size(), 1);
  const auto& event = events[0];
  EXPECT_EQ(event->event_type(),
            ::action_factory::pb::SsExceptionEventStatistics::FAULT_APPEARED);
  EXPECT_EQ(event->fault_unit(), "tracking_node-node-test_instance-1000");
  EXPECT_EQ(event->car_id(), kCarIDForTest);
  EXPECT_EQ(event->fault_event_time(), fault_event_time);
  EXPECT_EQ(event->vehicle_exception_id(),
            std::string(kCarIDForTest) + "-" +
                std::to_string(fault_event_time) + "-" + curr_system_level);
  EXPECT_EQ(event->trip_id(), "17188_20250416_145335");

  const auto& node_data = stats->GetExceptionEventData().at("tracking_node");
  ASSERT_EQ(node_data.active_faults.size(), 1);
  EXPECT_EQ(node_data.active_faults.begin()->second.first_detected_timestamp,
            event->fault_event_time());

  // Fault continue, but no report.
  nhi->mutable_health()->mutable_faults()->Clear();
  nhi->mutable_health()->mutable_faults()->Add(
      CreateFaultPb("node", "test_instance", 1000, "SOFTWARE_ERROR",
                    kExceptionEventTestBaseTimestamp + 60));
  fault_event_time += 50;
  stats->exception_event_timestamp_ = fault_event_time;
  events.clear();
  stats->CheckSystemState(system_state, events, voy::VehicleMode::AUTO_FULL,
                          false);
  ASSERT_EQ(events.size(), 0);
}

TEST_F(ExceptionEventStatisticsTestV2,
       CheckSystemState_FaultDisappears_GeneratesDisappearedEvent) {
  MockParamGetter param_getter;
  ON_CALL(param_getter, getTripID(testing::Eq(true)))
      .WillByDefault(
          testing::Return(std::make_optional("/trip_id17188_20250416_145335")));
  auto stats = GetExceptionEventStatisticsInstance(false, param_getter);
  std::string system_level_with_fault = GetSystemLevel("0010", 0, 0);
  std::string system_level_without_fault = GetSystemLevel("0000", 0, 0);

  std::vector<std::shared_ptr<::action_factory::pb::SsExceptionEventStatistics>>
      events;

  // Step 1: Fault appears
  time_t fault_event_time = kExceptionEventTestBaseTimestamp + 40;
  auto system_state_with_fault = CreateSystemState(
      system_level_with_fault, kExceptionEventTestBaseTimestamp - 10);
  auto* nhi = system_state_with_fault->add_node_health_infos();
  nhi->set_node_name("tracking_node");
  *nhi->mutable_health()->add_faults() =
      CreateFaultPb("node", "test_instance", 1000, "SOFTWARE_ERROR",
                    kExceptionEventTestBaseTimestamp - 10);

  stats->exception_event_timestamp_ = fault_event_time;
  stats->CheckSystemState(system_state_with_fault, events,
                          voy::VehicleMode::AUTO_FULL, false);
  ASSERT_EQ(events.size(), 1);
  time_t t0_actual_event_time = events[0]->fault_event_time();
  std::string t0_fault_event_id = events[0]->fault_event_id();
  events.clear();

  // Step 2: Fault disappears (but within debounce period initially)
  fault_event_time += 50;
  auto system_state_without_fault = CreateSystemState(
      system_level_without_fault, kExceptionEventTestBaseTimestamp + 70);
  stats->exception_event_timestamp_ = fault_event_time;
  stats->CheckSystemState(system_state_without_fault, events,
                          voy::VehicleMode::AUTO_FULL, false);
  ASSERT_EQ(events.size(), 0);

  // Step 3: Fault appears again (but within debounce period initially)
  fault_event_time += 50;
  auto system_state_with_fault_2 = CreateSystemState(
      system_level_with_fault, kExceptionEventTestBaseTimestamp + 130);
  auto* nhi_2 = system_state_with_fault_2->add_node_health_infos();
  nhi_2->set_node_name("tracking_node");
  *nhi_2->mutable_health()->add_faults() =
      CreateFaultPb("node", "test_instance", 1000, "SOFTWARE_ERROR",
                    kExceptionEventTestBaseTimestamp + 130);
  stats->exception_event_timestamp_ = fault_event_time;
  stats->CheckSystemState(system_state_with_fault, events,
                          voy::VehicleMode::AUTO_FULL, false);
  ASSERT_EQ(events.size(), 0);

  // Step 4: Fault disappears (but within debounce period initially)
  fault_event_time += 50;
  system_state_without_fault->set_af_dispose_time(
      kExceptionEventTestBaseTimestamp + 150);
  stats->exception_event_timestamp_ = fault_event_time;
  stats->CheckSystemState(system_state_without_fault, events,
                          voy::VehicleMode::AUTO_FULL, false);
  ASSERT_EQ(events.size(), 0);
  std::string t0_vehicle_ex_id = stats->vehicle_exception_id_.second;

  // Step 5: Fault truly disappears, but a new fault comes in.
  fault_event_time += 50 * 20;
  auto system_state_with_fault_3 =
      CreateSystemState(system_level_with_fault,
                        kExceptionEventTestBaseTimestamp + 140 + 50 * 20);
  auto* nhi_3 = system_state_with_fault_3->add_node_health_infos();
  nhi_3->set_node_name("planner_node");
  *nhi_3->mutable_health()->add_faults() =
      CreateFaultPb("node", "test_instance", 1005, "ALGORITHM",
                    kExceptionEventTestBaseTimestamp + 140 + 50 * 20);
  stats->exception_event_timestamp_ = fault_event_time;
  stats->CheckSystemState(system_state_with_fault_3, events,
                          voy::VehicleMode::AUTO_FULL, false);
  ASSERT_EQ(events.size(), 2);
  auto& event = events[0];
  EXPECT_EQ(event->event_type(),
            ::action_factory::pb::SsExceptionEventStatistics::FAULT_APPEARED);
  EXPECT_EQ(event->fault_unit(), "planner_node-node-test_instance-1005");
  EXPECT_EQ(event->car_id(), kCarIDForTest);
  EXPECT_EQ(event->fault_level(), "ALGORITHM");

  event = events[1];
  EXPECT_EQ(
      event->event_type(),
      ::action_factory::pb::SsExceptionEventStatistics::FAULT_DISAPPEARED);
  EXPECT_EQ(event->fault_unit(), "tracking_node-node-test_instance-1000");
  EXPECT_EQ(event->fault_event_time(), t0_actual_event_time);
  EXPECT_EQ(event->vehicle_exception_id(), t0_vehicle_ex_id);
  EXPECT_EQ(event->fault_event_id(), t0_fault_event_id);
  EXPECT_EQ(event->car_id(), kCarIDForTest);
  EXPECT_EQ(event->fault_level(), "SOFTWARE_ERROR");
  EXPECT_EQ(event->trip_id(), "17188_20250416_145335");
}

TEST_F(ExceptionEventStatisticsTestV2, ShouldSkipAddFault_ManualModeAndFilter) {
  MockParamGetter param_getter;
  auto stats = GetExceptionEventStatisticsInstance(false, param_getter);
  ExceptionEventStatistics::FaultProcessingContext context_manual(
      ::voy::VehicleMode::MANUAL, GetSystemLevel("0101", 1, 2), 0, false, {});
  EXPECT_TRUE(stats->ShouldSkipAddFault(context_manual));

  FLAGS_ss_exception_event_statistics_enable_filter = true;
  ExceptionEventStatistics::FaultProcessingContext context_filter(
      ::voy::VehicleMode::AUTO_FULL, "010", 0, false,
      {});  // "010" is kFilterExceptionEventBySystemLevel
  EXPECT_TRUE(stats->ShouldSkipAddFault(context_filter));
  FLAGS_ss_exception_event_statistics_enable_filter = false;

  ExceptionEventStatistics::FaultProcessingContext context_normal(
      ::voy::VehicleMode::AUTO_FULL, GetSystemLevel("0101", 1, 2), 0, false,
      {});
  EXPECT_FALSE(stats->ShouldSkipAddFault(context_normal));
}

TEST_F(ExceptionEventStatisticsTestV2,
       SetSsExceptionEventStatistics_PopulatesFields) {
  MockParamGetter param_getter;
  auto stats = GetExceptionEventStatisticsInstance(false, param_getter);
  time_t first_detect_ts = kExceptionEventTestBaseTimestamp;
  time_t current_event_processing_time =
      kExceptionEventTestBaseTimestamp + 1000;

  stats->exception_event_timestamp_ =
      current_event_processing_time;  // Simulate current time for this event
                                      // generation
  stats->vehicle_exception_id_ = {
      "F00", std::string(kCarIDForTest) + "-" +
                 std::to_string(current_event_processing_time) + "-F00"};

  stats->curr_trip_id_ = "17188_20250416_145335";

  ActiveFaultInfo fault_info;
  fault_info.first_detected_timestamp = first_detect_ts;
  fault_info.last_detected_timestamp =
      first_detect_ts;  // For an appearance event
  fault_info.original_fault_detail =
      CreateFaultPb("node", "", 1000, "ERROR", first_detect_ts - 100, true);
  fault_info.fault_event_id = stats->GenerateFaultEventId(
      "tracking_node", fault_info.original_fault_detail);
  fault_info.fault_type = fault_detector::pb::FaultExceptionType::ALGORITHM;

  stats->exception_event_data_["tracking_node"] =
      NodeEventData(::action_factory::pb::SsExceptionEventStatistics::NORMALLY);

  auto event_pb = stats->SetSsExceptionEventStatistics(
      voy::VehicleMode::REMOTE_CONTROL, "F00", "tracking_node", fault_info,
      stats->vehicle_exception_id_.second, 54321UL,
      {fault_detector::pb::FaultExceptionType::ALGORITHM},
      ::action_factory::pb::SsExceptionEventStatistics::FAULT_APPEARED);

  EXPECT_EQ(event_pb->car_id(), kCarIDForTest);
  EXPECT_EQ(event_pb->acu_version(), kACUVersionForTest);
  EXPECT_EQ(event_pb->fault_event_id(), fault_info.fault_event_id);
  EXPECT_EQ(event_pb->vehicle_exception_id(),
            stats->vehicle_exception_id_.second);
  EXPECT_EQ(event_pb->fault_event_time(), first_detect_ts);
  EXPECT_EQ(event_pb->vehicle_mode(), voy::VehicleMode::REMOTE_CONTROL);
  EXPECT_EQ(event_pb->fault_level(), "ERROR");
  EXPECT_EQ(event_pb->fault_type(),
            fault_detector::pb::FaultExceptionType::ALGORITHM);
  EXPECT_EQ(event_pb->system_level(), "F00");
  EXPECT_EQ(event_pb->fault_unit(), "tracking_node-node--1000");
  EXPECT_EQ(event_pb->fault_cause(),
            ::action_factory::pb::SsExceptionEventStatistics::NORMALLY);
  EXPECT_EQ(event_pb->fault_appear_time(),
            fault_info.original_fault_detail.timestamp());
  EXPECT_EQ(event_pb->af_dispose_time(), 54321UL);
  EXPECT_TRUE(event_pb->is_major_fault());
  EXPECT_EQ(event_pb->event_type(),
            ::action_factory::pb::SsExceptionEventStatistics::FAULT_APPEARED);
  EXPECT_EQ(event_pb->fault_disappear_time(), 0);
  ASSERT_EQ(event_pb->system_fault_type_size(), 1);
  EXPECT_EQ(event_pb->system_fault_type(0),
            fault_detector::pb::FaultExceptionType::ALGORITHM);
  uint64_t current_seq = stats->sequence_counter_;  // It was incremented
  std::string expected_seq_str = std::to_string(stats->startup_timestamp_) +
                                 "_" + std::to_string(current_seq);
  EXPECT_EQ(event_pb->sequence_counter(), expected_seq_str);
  EXPECT_EQ(event_pb->trip_id(), "17188_20250416_145335");
}

TEST_F(ExceptionEventStatisticsTestV2, UpdateTripID_Success) {
  MockParamGetter param_getter;
  EXPECT_CALL(param_getter, getTripID(testing::Eq(true)))
      .WillOnce(
          testing::Return(std::make_optional("/trip_id17188_20250416_145335")));
  auto stats = GetExceptionEventStatisticsInstance(false, param_getter);
  stats->UpdateTripID();
  ASSERT_EQ(stats->curr_trip_id_, "17188_20250416_145335");
}

TEST_F(ExceptionEventStatisticsTestV2, UpdateTripID_FailsToGetParam) {
  MockParamGetter param_getter;
  EXPECT_CALL(param_getter, getTripID(testing::Eq(true)))
      .WillOnce(testing::Return(std::nullopt));
  auto stats = GetExceptionEventStatisticsInstance(false, param_getter);
  stats->curr_trip_id_ = "17188_20250416_145337";  // 设置一个初始值
  stats->UpdateTripID();
  ASSERT_EQ(stats->curr_trip_id_, "17188_20250416_145337");
}

}  // namespace ss_exception_event_statistics

}  // namespace action_factory
}  // namespace system_supervisor
