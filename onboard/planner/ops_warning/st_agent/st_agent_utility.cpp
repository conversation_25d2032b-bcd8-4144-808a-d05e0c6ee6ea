#include "planner/ops_warning/st_agent/st_agent_utility.h"

#include "geometry/model/oriented_box.h"
#include "geometry/model/point_2d.h"
#include "geometry/model/polyline_curve.h"
#include "math/math_util.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_wrapper.h"

namespace planner {
namespace ops_warning {

namespace {

// SampledValue stores the odom/speed pairs that are needed to be sampled.
class SampledValue {
 public:
  SampledValue() = default;
  SampledValue(double odom, double speed) : odom_(odom), speed_(speed) {}

  void AddDeltaValue(const SampledValue& value) {
    odom_ += value.odom_;
    speed_ += value.speed_;
  }

  double odom() { return odom_; }

  double speed() { return speed_; }

 private:
  double odom_ = 0.0;
  double speed_ = 0.0;
};

// Calculates the delta odom and delta speed.
SampledValue CalculateDeltaValueByConstAcc(double speed, double acc,
                                           int64_t time_ms) {
  const double time_s = math::Ms2Sec(time_ms);
  // If acc is negative, speed should not less than zero.
  const double temp_time = speed + acc * time_s > 0 || math::NearZero(acc)
                               ? time_s
                               : speed / std::fabs(acc);
  return {speed * temp_time + acc * temp_time * temp_time / 2, acc * temp_time};
}

}  // namespace

STAgentParam TransformSTAgentParam(
    const PredictedTrajectoryWrapper& predicted_trajectory) {
  pb::Trajectory trajectory;
  const auto& predicted_poses = predicted_trajectory.predicted_poses(
      /*use_legacy_logic=*/true);
  for (const PredictedPose& pose : predicted_poses) {
    pb::TrajectoryPose* trajectory_pose = trajectory.add_poses();
    trajectory_pose->set_timestamp(pose.timestamp());
    trajectory_pose->set_x_pos(pose.center().x());
    trajectory_pose->set_y_pos(pose.center().y());
    trajectory_pose->set_z_pos(0);
    trajectory_pose->set_heading(pose.heading());
    trajectory_pose->set_speed(pose.speed());
    // There is no accel info in the TrafficParticipantPose class.
    trajectory_pose->set_accel(0.0);
    trajectory_pose->set_odom(pose.odom());
    // Sets default value.
    trajectory_pose->set_steering(0);
    trajectory_pose->set_steering_rate(0);
    trajectory_pose->set_steering_accel(0);
    trajectory_pose->set_jerk(0);
    trajectory_pose->set_curvature(0);
    trajectory_pose->set_abs_lat_accel(0);
    trajectory_pose->set_abs_juke(0);
  }
  return {trajectory, predicted_poses.front().width(),
          predicted_poses.front().length()};
}

std::vector<double> GetSampledOdomsByConstAcc(
    double speed, const std::vector<OdomSampleInfo>& sample_info_vec,
    int64_t resolution_ms, double odom_offset) {
  DCHECK_GT(resolution_ms, 0);
  std::vector<double> odom_vec;
  int64_t accumulated_time = 0;
  SampledValue accumulated_value(odom_offset, speed);

  size_t acc_time_vec_index = 0;
  while (accumulated_time <= sample_info_vec.back().end_time) {
    odom_vec.emplace_back(accumulated_value.odom());
    if (accumulated_time + resolution_ms <=
        sample_info_vec[acc_time_vec_index].end_time) {
      // If accumulated sampled time is less than the end time of current acc,
      // compute the delta_value directly.
      const SampledValue delta_value = CalculateDeltaValueByConstAcc(
          accumulated_value.speed(), sample_info_vec[acc_time_vec_index].accel,
          resolution_ms);
      accumulated_value.AddDeltaValue(delta_value);

    } else {
      // If accumulated sampled time is more than the end time of current acc,
      // separates the sampling time.
      const int64_t time_in_first_acc =
          sample_info_vec[acc_time_vec_index].end_time - accumulated_time;
      SampledValue delta_value = CalculateDeltaValueByConstAcc(
          accumulated_value.speed(), sample_info_vec[acc_time_vec_index].accel,
          time_in_first_acc);
      accumulated_value.AddDeltaValue(delta_value);

      if (acc_time_vec_index < sample_info_vec.size() - 1) {
        const int64_t time_in_second_acc =
            accumulated_time + resolution_ms -
            sample_info_vec[acc_time_vec_index].end_time;
        delta_value = CalculateDeltaValueByConstAcc(
            accumulated_value.speed(),
            sample_info_vec[++acc_time_vec_index].accel, time_in_second_acc);
        accumulated_value.AddDeltaValue(delta_value);
      }
    }
    accumulated_time += resolution_ms;
  }
  return odom_vec;
}

std::vector<pb::TrajectoryPose> InterpolatePosesByOdom(
    const math::geometry::PolylineCurve2d& trajectory_curve,
    const std::vector<double>& sampled_odoms) {
  std::vector<pb::TrajectoryPose> interpolated_trajectory_poses;
  interpolated_trajectory_poses.reserve(sampled_odoms.size());
  for (size_t i = 0; i < sampled_odoms.size(); ++i) {
    const double current_arc_length = sampled_odoms[i];
    // We only set value for x, y & heading for further usage, others are
    // irrelevant.
    pb::TrajectoryPose interpolated_pose;
    const auto position = trajectory_curve.GetInterp(current_arc_length);
    interpolated_pose.set_x_pos(position.x());
    interpolated_pose.set_y_pos(position.y());
    interpolated_pose.set_heading(
        trajectory_curve.GetInterpTheta(current_arc_length));
    interpolated_trajectory_poses.emplace_back(std::move(interpolated_pose));
  }
  return interpolated_trajectory_poses;
}

void TransformTrajectoryPoseToBox(
    const STAgentParam& param,
    const std::vector<pb::TrajectoryPose>& trajectory_poses,
    std::vector<STInfo>* st_info_vec_ptr) {
  st_info_vec_ptr->reserve(trajectory_poses.size());
  for (const auto& pose : trajectory_poses) {
    const math::geometry::OrientedBox2d box(
        pose.x_pos(), pose.y_pos(), param.length, param.width, pose.heading());
    st_info_vec_ptr->emplace_back(box, pose.timestamp());
  }
}

}  // namespace ops_warning
}  // namespace planner
