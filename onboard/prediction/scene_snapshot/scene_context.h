#ifndef ONBOARD_PREDICTION_SCENE_SNAPSHOT_SCENE_CONTEXT_H_
#define ONBOARD_PREDICTION_SCENE_SNAPSHOT_SCENE_CONTEXT_H_

#include <map>
#include <memory>
#include <utility>
#include <vector>

#include "av_comm/car_id.h"
#include "av_comm/history_buffer.h"
#include "planner_protos/planning_lane_sequence.pb.h"
#include "pnc_map_service/map_elements/crosswalk.h"
#include "pnc_map_service/map_elements/lane.h"
#include "pnc_map_service/map_elements/zone.h"
#include "pnc_map_service/prediction_map_service.h"
#include "prediction/common/definition/ego_lane_sequence.h"
#include "prediction/common/definition/prediction_constants.h"
#include "prediction/raw_sensor_process/obstacle_segmentation.h"
#include "prediction/scene_snapshot/traffic_light_infer.h"
#include "voy_protos/construction_zones.pb.h"
#include "voy_protos/control.pb.h"
#include "voy_protos/traffic_light.pb.h"

namespace prediction {

// It is the common info needed by both traditional prediction and vectornet
// prediction. It is part of SceneSnapshot.
class SceneContext {
 public:
  using MapObjectToTrafficLightState =
      std::map<std::pair<pb::MapObject::MapObjectType, int64_t>,
               TrafficLightState>;
  using TrafficLightStateHistoryBuffer =
      av_comm::HistoryBuffer<int64_t, MapObjectToTrafficLightState>;

  using TrafficLightHistoryBuffer =
      av_comm::HistoryBuffer<int64_t,
                             std::shared_ptr<const voy::TrafficLights>>;

  using EgoLaneSequenceHistoryBuffer = av_comm::HistoryBuffer<
      int64_t, std::shared_ptr<const planner::pb::PlanningLaneSequence>>;

  using ConstructionZoneHistoryBuffer =
      av_comm::HistoryBuffer<int64_t,
                             std::shared_ptr<const voy::ConstructionZoneList>>;

  SceneContext() = default;
  SceneContext(
      const pnc_map::PredictionMapService& prediction_map_service,
      av_comm::CarRegion car_region, int64_t timestamp, voy::Pose ego_pose,
      const TrafficLightHistoryBuffer& traffic_lights_queue,
      const EgoLaneSequenceHistoryBuffer& ego_lane_sequence_queue,
      const ConstructionZoneHistoryBuffer& construction_zones_queue,
      std::shared_ptr<const control::pb::Control> control_message = nullptr,
      std::shared_ptr<const voy::TrackedObjectList> tracked_objects = nullptr,
      std::shared_ptr<ObstacleSegmentationResult> obstacle_segmentation_result =
          nullptr,
      const TrafficLightStateHistoryBuffer* map_object_to_tl_state_queue =
          nullptr,
      int64_t point_cloud_timestamp = constants::kInvalidTimestamp);

  // Getters.
  const std::vector<const pnc_map::Lane*>& nearby_lanes() const {
    return nearby_lanes_;
  }
  const std::vector<const pnc_map::Crosswalk*>& nearby_crosswalks() const {
    return nearby_crosswalks_;
  }
  const std::vector<const pnc_map::Zone*>& nearby_exit_zones() const {
    return nearby_exit_zones_;
  }
  const std::vector<const pnc_map::Zone*>& nearby_median_strip_zones() const {
    return nearby_median_strip_zones_;
  }
  const std::vector<const pnc_map::Zone*>& nearby_bus_stop_zones() const {
    return nearby_bus_stop_zones_;
  }
  const std::vector<const pnc_map::Zone*>& nearby_fences() const {
    return nearby_fences_;
  }
  const std::vector<const pnc_map::Junction*>& nearby_junctions() const {
    return nearby_junctions_;
  }
  const std::vector<const pnc_map::Road*>& nearby_roads() const {
    return nearby_roads_;
  }
  const std::vector<const pnc_map::Zone*>& nearby_sidewalks() const {
    return nearby_sidewalks_;
  }
  const std::vector<const pnc_map::Zone*>& nearby_roundabouts() const {
    return nearby_roundabouts_;
  }
  const std::vector<const pnc_map::Zone*>& nearby_map_construction_zones()
      const {
    return nearby_map_construction_zones_;
  }
  const std::vector<const pnc_map::Zone*>& nearby_diversion_zones() const {
    return nearby_diversion_zones_;
  }
  const std::vector<voy::TrackedObject>& nearby_traffic_cones_and_barriers()
      const {
    return nearby_traffic_cones_and_barriers_;
  }
  const MapObjectToTrafficLightState& map_obj_to_tl_state() const {
    return map_object_to_tl_state_;
  }
  const std::map<int64_t, math::geometry::PolylineCurve2d> nearby_curbs()
      const {
    return nearby_curbs_;
  }
  // Traffic light states for all map objects in history.
  // The return items is sorted ascendingly by message timestamp.
  const std::vector<MapObjectToTrafficLightState>& map_obj_to_tl_state_history()
      const {
    DCHECK_EQ(map_object_to_tl_state_history_.size(),
              constants::kTrafficLightHistoryItemSize);
    return map_object_to_tl_state_history_;
  }
  const EgoLaneSequence& ego_lane_sequence() const {
    return ego_lane_sequence_;
  }
  const std::vector<const voy::ConstructionZone*>& construction_zones() const {
    return construction_zones_;
  }
  const voy::Pose& ego_pose() const { return ego_pose_; }

  int64_t observed_latency_from_tracking_to_lidar() const {
    return observed_latency_from_tracking_to_lidar_;
  }

  std::shared_ptr<ObstacleSegmentationResult> obstacle_segmentation_result()
      const {
    return obstacle_segmentation_result_ptr_;
  }

  std::shared_ptr<const control::pb::Control> ego_control_message() const {
    return ego_control_message_;
  }

  int64_t timestamp() const { return timestamp_; }

 private:
  std::vector<const pnc_map::Lane*> nearby_lanes_;
  std::vector<const pnc_map::Crosswalk*> nearby_crosswalks_;
  std::vector<const pnc_map::Zone*> nearby_exit_zones_;
  std::vector<const pnc_map::Zone*> nearby_median_strip_zones_;
  std::vector<const pnc_map::Zone*> nearby_bus_stop_zones_;
  std::vector<const pnc_map::Zone*> nearby_fences_;
  std::vector<const pnc_map::Junction*> nearby_junctions_;
  std::vector<const pnc_map::Road*> nearby_roads_;
  std::vector<const pnc_map::Zone*> nearby_sidewalks_;
  std::vector<const pnc_map::Zone*> nearby_roundabouts_;
  std::vector<const pnc_map::Zone*> nearby_map_construction_zones_;
  std::vector<const pnc_map::Zone*> nearby_diversion_zones_;

  std::vector<voy::TrackedObject> nearby_traffic_cones_and_barriers_;
  std::map<int64_t, math::geometry::PolylineCurve2d> nearby_curbs_;

  MapObjectToTrafficLightState map_object_to_tl_state_;
  std::vector<MapObjectToTrafficLightState> map_object_to_tl_state_history_;

  EgoLaneSequence ego_lane_sequence_;

  std::shared_ptr<ObstacleSegmentationResult> obstacle_segmentation_result_ptr_;

  // Raw ROS message from /perception/construction_zone_list topic.
  std::shared_ptr<const voy::ConstructionZoneList> raw_construction_zone_list_;
  // Filtered construction zones from the raw message, only select zones from
  // perception and realtime map, excluding zones from occupancy grids.
  std::vector<const voy::ConstructionZone*> construction_zones_;

  voy::Pose ego_pose_;
  int64_t timestamp_;

  // The latency (in ms) from the latest received tracking message to the latest
  // received lidar message.
  int64_t observed_latency_from_tracking_to_lidar_ =
      constants::kInvalidTimestamp;

  std::shared_ptr<const control::pb::Control> ego_control_message_;

  friend class SceneSnapshotTestMocker;
  friend class VectornetFeatureExtractorTest;
  FRIEND_TEST(SceneSnapshotTest, GetObjectInEgoPathGeometryTest);
  FRIEND_TEST(PedScenarioAnalyzerTest, GetScenarioType);
};

}  // namespace prediction

#endif  // ONBOARD_PREDICTION_SCENE_SNAPSHOT_SCENE_CONTEXT_H_
