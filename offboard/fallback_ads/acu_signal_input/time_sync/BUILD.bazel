package(default_visibility = ["//visibility:public"])

cc_library(
    name = "time_sync",
    srcs = [
        "time_sync.cpp",
    ],
    hdrs = [
        "time_sync.h",
    ],
    copts = [
        "-x",
        "c++",
        "-std=c++14",
    ],
    include_prefix = "acu_signal_input/time_sync/",
    deps = [
        "//offboard/fallback_ads/acu_signal_input:acu_signal_input_parameter",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_acu_sig_input",
        "//offboard/fallback_ads/common_math_library:debounce_method",
    ],
)
