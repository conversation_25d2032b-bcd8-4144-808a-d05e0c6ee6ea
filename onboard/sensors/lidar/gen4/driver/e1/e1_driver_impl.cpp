#include "e1_driver_impl.h"

#include <iomanip>
#include <map>
#include <utility>

#include <boost/asio/io_context.hpp>
#include <boost/asio/spawn.hpp>
#include <glog/logging.h>
#include <tbb/concurrent_queue.h>

#include "add_data.h"
#include "base/now.h"
#include "driver/lidar_event.h"
#include "e1_packet_checker.h"
#include "fault_message.h"
#include "module/udp/udp_receiver.h"
#include "moodycamel_queue/readerwritercircularbuffer.h"
#include "node/timer.h"
#include "voy_protos/lidar_scan.pb.h"

namespace lidar {
namespace {
// E1 pkt_cnt from 0 to 287. A frame contains 288 packets(single)
// E1 pkt_cnt from 0 to 575. A frame contains 576 packets(double)
constexpr int kE1SingleLastPktCnt = 287;
constexpr int kE1DoubleLastPktCnt = 575;

// Maximum pkt_cnt interval
constexpr int kE1MaxPktCntInterval = 50;

constexpr int kHardWarePubPeriodS = 10;

}  // namespace

using onboard_monitor::AddHardwareMonitorData;

E1DriverImpl::E1DriverImpl(
    const std::string &ip_address, const std::string &remote_ip_address,
    const std::string &interface_address, bool is_multicast, int udp_port,
    LidarOptions lidar_options, IScanDataHandler *scan_data_handler,
    const std::function<void(pcl::PointCloud<lidar::PointXYZIR>::Ptr, int64_t,
                             bool)> &data_callback,
    const std::function<void(LidarEvent, const std::string &)>
        &message_callback,
    boost::asio::io_service *io_service)
    : io_service_(io_service),
      ip_address_(ip_address),
      remote_ip_address_(remote_ip_address),
      interface_address_(interface_address),
      is_multicast_(is_multicast),
      udp_port_(udp_port),
      last_pkt_cnt_(-1),
      scan_data_handler_(scan_data_handler),
      data_callback_(data_callback),
      message_callback_(message_callback),
      e1_udp_parser_(lidar_options),
      e1_packet_checker_(ip_address),
      fault_message_parse_(ip_address) {
  LOG(INFO) << " E1DriverImpl constrcutor called. "
            << " ip_address: " << ip_address_
            << " remote_ip_address: " << remote_ip_address_
            << " interface_address: " << interface_address_
            << " udp_port: " << udp_port_
            << " options min_distance: " << lidar_options.min_range
            << " options max_distance: " << lidar_options.max_range;
}

boost::system::error_code E1DriverImpl::Start() {
  std::shared_ptr<CircularQueue> packet_queue_ptr =
      std::make_shared<CircularQueue>(1024 * 4);
  point_cloud_queue_.set_capacity(1260 * 8);
  auto on_packet_callback =
      [this,
       packet_queue_ptr](std::shared_ptr<std::vector<uint8_t>> packet_buffer) {
        while (!packet_queue_ptr->try_enqueue(packet_buffer)) {
          std::shared_ptr<std::vector<uint8_t>> drop_packet;
          packet_queue_ptr->try_dequeue(drop_packet);
          LOG_EVERY_N(WARNING, 100) << "The udp packet queue is full and "
                                       "cleaning up old data. udp_port: "
                                    << udp_port_;
        }
      };

  work_thread_ = std::make_unique<std::thread>([this,
                                                packet_queue_ptr]() -> void {
    while (!this->stop_flag_) {
      try {
        std::shared_ptr<std::vector<uint8_t>> packet_buffer;
        if (packet_queue_ptr->wait_dequeue_timed(packet_buffer, 30 * 1000)) {
          this->OnPacket(packet_buffer);
        }
      } catch (const std::exception &e) {
        LOG(ERROR) << "Exception caught in receive thread. " << e.what();
      }
    }
  });

  boost::asio::spawn(*io_service_, [this](boost::asio::yield_context yield) {
    auto timer = std::make_unique<node::Timer>(*io_service_);
    boost::system::error_code ec;
    while (!ec) {
      timer->expires_from_now(std::chrono::seconds(kHardWarePubPeriodS), ec);
      scan_data_handler_->PublishHardwareMonitor(monitor_data_single_);
      timer->async_wait(yield[ec]);
    }
  });

  return ReadSpawn(
      io_service_, udp_port_, 200, remote_ip_address_, is_multicast_,
      interface_address_, on_packet_callback,
      std::bind(&E1DriverImpl::OnError, this, std::placeholders::_1));
}

E1DriverImpl::~E1DriverImpl() { Stop(); }

boost::system::error_code E1DriverImpl::Stop() {
  // TODO(peytonwangpeng): Implement specific functions
  if (!stop_flag_) {
    stop_flag_ = true;
    work_thread_->join();
  }
  return boost::system::error_code();
}

boost::system::error_code E1DriverImpl::GetData(
    pcl::PointCloud<lidar::PointXYZIR>::Ptr &point_cloud, int64_t &timestamp,
    bool &is_cycle_completed) {
  std::tuple<pcl::PointCloud<lidar::PointXYZIR>::Ptr, int64_t, bool> tuple;
  if (!point_cloud_queue_.try_pop(tuple)) {
    return boost::system::errc::make_error_code(
        boost::system::errc::no_message_available);
  }

  point_cloud = std::get<0>(tuple);
  timestamp = std::get<1>(tuple);
  is_cycle_completed = std::get<2>(tuple);
  return boost::system::error_code();
}

boost::system::error_code E1DriverImpl::GetStatus(int & /* temperature */,
                                                  int & /* motor */,
                                                  int & /* ptp_status */) {
  return boost::system::error_code();
}

void E1DriverImpl::OnError(const boost::system::error_code &error_code) {
  LOG_EVERY_N(ERROR, 100) << "Received data transmission error. error: "
                          << error_code.message();
  if (message_callback_) {
    // TODO(peytonwangpeng): Implement specific functions
    message_callback_({LidarEvent::EventCode::kUdpReceivedTimeoutError,
                       LidarEvent::State::kOccur},
                      "LidarEvent::DataReceiveTimeout");
  }
}

void E1DriverImpl::OnPacket(
    std::shared_ptr<std::vector<uint8_t>> packet_buffer_ptr) {
  if (packet_buffer_ptr->empty()) {
    LOG(ERROR) << "Incorrect packet. size:0 ip: " << ip_address_;
    return;
  }
  if (packet_buffer_ptr->size() == kDifopPktLength) {
    OnFaultMessage(std::move(packet_buffer_ptr));
  } else if (packet_buffer_ptr->size() == kMsopPktLength) {
    OnData(std::move(packet_buffer_ptr));
  } else {
    LOG_EVERY_N(WARNING, 100) << "Ignore the message. ip: " << ip_address_
                              << ". size:" << packet_buffer_ptr->size();
  }
}

void E1DriverImpl::OnData(
    std::shared_ptr<std::vector<uint8_t>> packet_buffer_ptr) {
  if (packet_buffer_ptr->data()[0] != 0x55 ||
      packet_buffer_ptr->data()[1] != 0xAA ||
      packet_buffer_ptr->data()[2] != 0x5A ||
      packet_buffer_ptr->data()[3] != 0xA5) {
    LOG(ERROR) << "Incorrect E1 data header. ip: " << ip_address_;
    return;
  }
  e1_packet_checker_.CountPacketReceived();
  int32_t invalid_point_cnt;

  DCHECK(scan_data_handler_);

  const RSE1SMsopPkt *raw_packet =
      reinterpret_cast<const RSE1SMsopPkt *>(&packet_buffer_ptr->data()[0]);
  int16_t pkt_cnt = ntohs(raw_packet->header.pkt_seq);
  int8_t return_mode = static_cast<int>(raw_packet->header.return_mode);
  int64_t timestamp = e1_udp_parser_.ParseTimeUs(&raw_packet->header.timestamp);

  if (IsDiscard(pkt_cnt)) {
    return;
  }

  // TODO(caochunpeng): We will remove this code when the vendor fix timestamp
  // error
  if (is_scan_cycle_complete_) {
    timestamp_diff_ = static_cast<int64_t>(timestamp) / 1000 % 100;
    if (timestamp_diff_ > 2) {
      LOG_EVERY_N(WARNING, 200) << "timestamp jump. ip: " << ip_address_
                                << ". timestamp_diff_: " << timestamp_diff_;
    }
  }

  is_scan_cycle_complete_ = IsScanCycleComplete(pkt_cnt, return_mode);
  is_scan_cycle_complete_lost_last_pkt_ =
      IsScanCycleCompleteLostLastPkt(pkt_cnt, return_mode);
  if (is_scan_cycle_complete_lost_last_pkt_ && scan_data_handler_) {
    scan_data_handler_->OnScanCycleComplete();
    // TODO(caochunpeng): We will remove this code when the vendor fix timestamp
    // error
    timestamp_diff_ = static_cast<int64_t>(timestamp) / 1000 % 100;
    if (timestamp_diff_ > 2) {
      LOG_EVERY_N(WARNING, 200) << "timestamp jump. ip: " << ip_address_
                                << ". timestamp_diff_: " << timestamp_diff_;
    }
  }
  pb::LidarScan_ReturnType return_type;
  switch (return_mode) {
    case 0:
      return_type = pb::LidarScan::LAST;
      break;
    case 4:
      return_type = pb::LidarScan::STRONGEST;
      break;
    case 7:
      return_type = pb::LidarScan::FIRST;
      break;
    case 9:
      return_type = pb::LidarScan::DUAL;
      break;
    default:
      LOG(ERROR) << "Invalid return mode. ip: " << ip_address_;
      return_type = pb::LidarScan::DUAL;
      break;
  }

  pb::LidarScanSector *lidar_scan_sector = AllocateScanSector(return_type);
  e1_udp_parser_.ParseUdpData(*packet_buffer_ptr, invalid_point_cnt, pkt_cnt,
                              return_mode, lidar_scan_sector);
  lidar_scan_sector->set_timestamp(static_cast<int64_t>(timestamp / 1000) -
                                   timestamp_diff_);
  if (lidar_scan_sector->positions_size() == 0) {
    LOG_EVERY_N(WARNING, 1000)
        << "The valid point cloud data in the packet is empty.[1/1000]. ip: "
        << ip_address_;
  }

  scan_data_handler_->OnUdpPacket(*packet_buffer_ptr);

  if (is_scan_cycle_complete_ && scan_data_handler_) {
    scan_data_handler_->OnScanCycleComplete();
  }

  std::string error_message;
  LidarEvent::EventCode event_code;
  std::tie(event_code, error_message) = e1_packet_checker_.Check(
      packet_buffer_ptr, timestamp, pkt_cnt, last_pkt_cnt_, invalid_point_cnt,
      is_scan_cycle_complete_lost_last_pkt_ || is_scan_cycle_complete_,
      proto_stage_);

  last_pkt_cnt_ = pkt_cnt;
  FillHardwareMonitorData(static_cast<int>(raw_packet->header.time_mode),
                          static_cast<int>(raw_packet->header.temperature) - 80,
                          e1_packet_checker_.GetPktLatency(),
                          e1_packet_checker_.GetLostPacketCnt());
  if (event_code != LidarEvent::EventCode::kNormal) {
    if (message_callback_) {
      message_callback_({event_code, LidarEvent::State::kOccur}, error_message);
    }
  }
}

void E1DriverImpl::FillHardwareMonitorData(int time_mode, int temperature,
                                           int64_t latency,
                                           int lost_pkt_in_frame) {
  std::string plugin_name = "lidar_" + std::to_string(udp_port_);

  std::lock_guard<std::mutex> lock(mtx);
  monitor_data_single_.Clear();
  AddHardwareMonitorData(monitor_data_single_, plugin_name, "E1_time_mode",
                         time_mode);
  AddHardwareMonitorData(monitor_data_single_, plugin_name, "E1_temp",
                         temperature);
  AddHardwareMonitorData(monitor_data_single_, plugin_name, "E1_pkt_latency",
                         latency);
  AddHardwareMonitorData(monitor_data_single_, plugin_name,
                         "E1_lost_pkt_in_frame", lost_pkt_in_frame);
}

void E1DriverImpl::OnFaultMessage(
    std::shared_ptr<std::vector<uint8_t>> packet_buffer_ptr) {
  // TODO(caochunpeng): We will remove this code when the vendor slows down the
  // frequency
  static int16_t difop_pkt_cnt = 0;
  if (difop_pkt_cnt++ % 1000 == 0) {
    difop_pkt_cnt = 0;
    const RSE1DifopPkt *difop_pkt =
        reinterpret_cast<const RSE1DifopPkt *>(packet_buffer_ptr->data());
    if (!fault_message_parse_.CheckMessageHeader(difop_pkt->header)) {
      LOG(ERROR) << ip_address_
                 << " Incorrect data header. This is not fault message. ";
      return;
    }

    proto_stage_ = static_cast<PrototypeStage>(difop_pkt->proto_stage);

    std::string error_message;
    LidarEvent::EventCode event_code;
    std::tie(event_code, error_message) =
        fault_message_parse_.ParserE1FaultMessage(difop_pkt);
    if (event_code != LidarEvent::EventCode::kNormal) {
      if (message_callback_) {
        message_callback_({event_code, LidarEvent::State::kOccur},
                          error_message);
      }
    }
  }
}

bool E1DriverImpl::IsScanCycleComplete(int16_t pkt_cnt,
                                       int8_t &return_mode) const {
  int16_t max_pkt_cnt =
      (return_mode == 0 || return_mode == 4 || return_mode == 7)
          ? kE1SingleLastPktCnt
          : kE1DoubleLastPktCnt;
  if (pkt_cnt == max_pkt_cnt) {
    return true;
  }
  return false;
}

bool E1DriverImpl::IsScanCycleCompleteLostLastPkt(int16_t pkt_cnt,
                                                  int8_t &return_mode) const {
  int16_t max_pkt_cnt =
      (return_mode == 0 || return_mode == 4 || return_mode == 7)
          ? kE1SingleLastPktCnt
          : kE1DoubleLastPktCnt;
  if (((pkt_cnt < last_pkt_cnt_) && (last_pkt_cnt_ != max_pkt_cnt) &&
       std::abs(pkt_cnt - last_pkt_cnt_) > kE1MaxPktCntInterval)) {
    return true;
  }
  return false;
}

bool E1DriverImpl::IsDiscard(int16_t pkt_cnt) const {
  if ((pkt_cnt < last_pkt_cnt_) &&
      std::abs(pkt_cnt - last_pkt_cnt_) < kE1MaxPktCntInterval) {
    LOG(INFO) << "Packet is out of order, discard this packet. pkt_cnt: "
              << pkt_cnt << ". last_pkt_cnt_: " << last_pkt_cnt_
              << ". ip: " << ip_address_;
    return true;
  }
  return false;
}

pb::LidarScanSector *E1DriverImpl::AllocateScanSector(
    pb::LidarScan_ReturnType return_type) {
  pb::LidarScanSector *lidar_scan_sector =
      scan_data_handler_->AllocateScanSector(return_type);
  DCHECK(lidar_scan_sector != nullptr);
  lidar_scan_sector->mutable_positions()->Reserve(kBlocksPerPkt * 3);
  lidar_scan_sector->mutable_intensities()->reserve(kBlocksPerPkt);
  lidar_scan_sector->mutable_ring_numbers()->reserve(kBlocksPerPkt);
  lidar_scan_sector->mutable_confidences()->reserve(kBlocksPerPkt);
  return lidar_scan_sector;
}

}  // namespace lidar
