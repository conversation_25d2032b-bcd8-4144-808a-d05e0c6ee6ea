#include "planner/speed/occlusion/hallucinated_agent_manager.h"

#include <algorithm>
#include <cmath>
#include <cstdint>
#include <limits>
#include <optional>
#include <set>
#include <string>
#include <utility>

#include "geometry/model/oriented_box.h"
#include "geometry/model/point_2d.h"
#include "geometry/model/polygon_with_cache.h"
#include "geometry/model/polyline_curve.h"
#include "hdmap_protos/crosswalk.pb.h"
#include "hdmap_protos/geometry.pb.h"
#include "hdmap_protos/lane.pb.h"
#include "math/interpolation.h"
#include "math/math_util.h"
#include "math/unit_conversion.h"
#include "planner/planning_gflags.h"
#include "planner/speed/occlusion/occlusion_area.h"
#include "planner/speed/occlusion/util/debug_util.h"
#include "planner/speed/occlusion/util/hallucinated_agent_manager_util.h"
#include "planner/speed/occlusion/util/hallucinated_agent_util.h"
#include "planner/speed/occlusion/util/occlusion_area_util.h"
#include "planner/speed/reasoning/reasoner/stop_sign_reasoner/stop_sign_reasoner.h"
#include "planner/speed/reasoning/reasoning_util.h"
#include "planner/speed/reasoning_input/traffic_rules/conflicting_lane_traffic_rule.h"
#include "planner/speed/reasoning_input/traffic_rules/crosswalk_traffic_rule.h"
#include "planner/speed/reasoning_input/traffic_rules/junction_traffic_rule.h"
#include "planner/speed/reasoning_input/traffic_rules/traffic_light_info.h"
#include "planner/speed/util/precedence/crosswalk_road_precedence_util.h"
#include "planner/utility/common/common_utility.h"
#include "planner/utility/occlusion/clear_region_grid.h"
#include "planner/utility/occlusion/occlusion_checker.h"
#include "planner/world_model/planner_object/planner_object.h"
#include "planner_protos/agent_intention.pb.h"
#include "planner_protos/behavior_common_type.pb.h"
#include "planner_protos/trajectory_guider_output.pb.h"
#include "pnc_map_service/map_elements/junction.h"
#include "pnc_map_service/map_elements/lane.h"
#include "pnc_map_service/map_elements/road.h"
#include "pnc_map_service/map_elements/traffic_signal.h"
#include "rt_event/rt_event.h"
#include "voy_protos/math.pb.h"
#include "voy_rt_event/rt_event_planner.h"
#include "voy_trace/trace_planner.h"

namespace planner {
namespace speed {
namespace {
// TODO(pengxu): Move to a config file after this setting is stable.
// The distance below indicates how far a junction shall be considered.
constexpr double kJunctionIgnoreDistanceInMeter = 90;  // m
// The distance below indicates how far a crosswalk shall be considered.
constexpr double kCrosswalkIgnoreDistanceInMeter = 90;  // m
// The distance below indicates a range ahead of a hallucinated agent we would
// like to search for a possible leading/blocking agent, which can be referenced
// for the hallucinated agent's speed.
constexpr double kAgentAheadDistanceThreshInMeter = 25.0;  // m
// The lateral gap threshold to consider an agent in the same path.
constexpr double kAgentInSamePathLateralGapThreshInMeter = 1.0;  // m
// The following value reflects the estimated speed of a cyclist following the
// traffic law.
constexpr double kHallucinatedCyclistSpeedFollowingTrafficLawInMps =
    math::KmphToMps(20.0);  // m/s
// The following value reflects the estimated speed of a cyclist violating the
// traffic law.
constexpr double kHallucinatedCyclistSpeedViolatingTrafficLawInMps =
    math::KmphToMps(25.0);  // m/s
// The interval distance between control points used in Bezier curve generation
// in exit zones.
[[maybe_unused]] constexpr double
    kBezierCurveIntervalDistanceBetweenControlPoints = 4.0;  // m
// The (near) stop speed threshold to filter out too slow hallucinated agents.
constexpr double kStopSpeedThresholdInMps = 0.1;
// The spatial search horizon for occlusion area in conflicting lane
// predecessors.
constexpr double kPredecessorLaneSearchHorizonInMeter = 100;  // m

// Count the digits of an integer.
int CountDigits(int64_t n) { return static_cast<int>(floor(log10(n) + 1)); }

// Constructs a tracked object.
voy::TrackedObject ConstructTrackedObject(
    const voy::perception::ObjectType object_type, const int64_t id,
    const math::geometry::Point2d& pos, const double width, const double length,
    const double heading, const double velocity) {
  voy::TrackedObject tracked_object;
  tracked_object.set_object_type(object_type);
  tracked_object.set_id(id);
  tracked_object.set_center_x(pos.x());
  tracked_object.set_center_y(pos.y());
  tracked_object.set_width(width);
  tracked_object.set_length(length);
  tracked_object.set_heading(heading);
  tracked_object.set_velocity(velocity);
  return tracked_object;
}

// Finds the point that has the minimum distance to a curve among a group of
// points.
math::geometry::Point2d FindClosestPointToCurve(
    const math::geometry::PolylineCurve2d& curve,
    const std::vector<math::geometry::Point2d>& points) {
  DCHECK(points.empty() == false);
  double min_dist = std::numeric_limits<double>::infinity();
  math::geometry::Point2d min_dist_point(0, 0);
  for (const math::geometry::Point2d& point : points) {
    const double dist =
        curve.GetProximity(point, math::pb::UseExtensionFlag::kAllow).dist;
    if (dist < min_dist) {
      min_dist = dist;
      min_dist_point = point;
    }
  }
  DCHECK(!std::isinf(min_dist));
  return min_dist_point;
}

// Finds the point that has the minimum distance to a given point among a group
// of points.
math::geometry::Point2d FindClosestPointToPoint(
    const math::geometry::Point2d& point,
    const std::vector<math::geometry::Point2d>& points) {
  DCHECK(points.empty() == false);
  double min_dist = std::numeric_limits<double>::infinity();
  math::geometry::Point2d min_dist_point(0, 0);
  for (const math::geometry::Point2d& p : points) {
    const double dist = math::geometry::Distance(point, p);
    if (dist < min_dist) {
      min_dist = dist;
      min_dist_point = p;
    }
  }
  DCHECK(!std::isinf(min_dist));
  return min_dist_point;
}

// Finds the projected point on a curve.
math::geometry::Point2d FindProjectedPointOnCurve(
    const math::geometry::PolylineCurve2d& curve,
    const math::geometry::Point2d& point) {
  const math::ProximityQueryInfo proximity =
      curve.GetProximity(point, math::pb::UseExtensionFlag::kAllow);
  return math::geometry::Point2d(proximity.x, proximity.y);
}

// Returns a slice of a vector with the index range from m to n.
template <typename T>
std::vector<T> Slice(std::vector<T> const& v, int m, int n) {
  auto first = v.cbegin() + m;
  auto last = v.cbegin() + n + 1;
  std::vector<T> vec(first, last);
  return vec;
}

// Returns a list of points smoothly connecting 3 points with a Bezier curve:
// https://stackoverflow.com/questions/785097/how-do-i-implement-a-b%C3%A9zier-curve-in-c
[[maybe_unused]] std::vector<math::geometry::Point2d> ConnectSmoothlyWithBezier(
    const math::geometry::Point2d& p1, const math::geometry::Point2d& p2,
    const math::geometry::Point2d& p3, const double smoothness = 0.1) {
  std::vector<math::geometry::Point2d> result;
  result.reserve(static_cast<int>(1.0 / smoothness + 1));
  // Calculate intermediate points using Bezier interpolation
  for (double t = 0.0; t <= 1.0; t += smoothness) {
    math::geometry::Point2d pa = math::LinearInterpolate(p1, p2, t);
    math::geometry::Point2d pb = math::LinearInterpolate(p2, p3, t);
    math::geometry::Point2d smooth_point = math::LinearInterpolate(pa, pb, t);
    result.emplace_back(smooth_point);
  }
  return result;
}

// Returns a roughly estimate of the hallucinated vehicle in the exit zone, in
// range (-pi,+pi].
// Assuming a vehicle that is merging from the exit zone to the main road, the
// heading of the vehicle would be roughly perpendicular with ego path or ego
// heading. In a right-handed traffic, the heading of the agent can be simply
// deduced by rotating pi/2 radius counterclockwise, which is
// computing-efficient and gives a good enough result.
double RoughlyEstimateVehicleHeadingInExitZone(const double ego_heading) {
  return math::NormalizeMinusPiToPi(ego_heading + M_PI_2);
}

}  // namespace

// Code unique hallucinated agent id which would be
//   - unique with existing perceived agent id;
//   - consistent and trackable in sequential frames.
// In the end, the resulting id should be like "10xxx0y0z", where "xxx" is the
// road element id, y is the occlusion scenario and z is the local index.
// Between the first digit '1' and "xxx" there will be one or more digits of
// '0'. It would be quite easy to distinguish a hallucinated agent from the id.
int64_t CodeUniqueHallucinatedAgentId(
    int64_t base, const int64_t road_element_id,
    const planner::pb::OcclusionScenario::Enum occlusion_scenario,
    const int local_index) {
  // Firstly construct an initial id using the road element where the
  // hallucinated agent appears in and the local index to make it consistent in
  // sequential frames.
  const int64_t unique_id = road_element_id * 10000 +
                            100 * static_cast<int64_t>(occlusion_scenario) +
                            local_index;
  // Secondly add a base number which is bigger than any existing perceived
  // agent id to avoid conflicts.
  if (base < unique_id) {
    base = static_cast<int64_t>(std::pow(10, CountDigits(unique_id) + 1));
  }
  return base + unique_id;
}

HallucinatedAgentManager::HallucinatedAgentManager(
    const voy::Pose& ego_pose,
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const tbb::concurrent_unordered_map<
        ObjectId, std::vector<PredictedTrajectoryWrapper>>&
        object_prediction_map,
    const OcclusionChecker& occlusion_checker,
    const math::geometry::PolylineCurve2d& nominal_path,
    const planner::LaneSequenceIterator& lane_sequence_iterator,
    const voy::TrafficLights& traffic_light_detection,
    const planner::pb::DecoupledManeuverSeed& previous_iter_seed,
    const RobotState& robot_state,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const bool will_ego_borrow_opposite_road,
    const planner::pb::MotionMode motion_mode, const int64_t current_timestamp,
    const bool filter_crosswalk,
    pb::HallucinatedAgentManagerDebug* debug_proto_ptr)
    : ego_pose_(ego_pose),
      // TODO(zhihao): clean up ego_pose_ in a future CR, only use robot_state
      robot_state_(robot_state),
      planner_object_map_(planner_object_map),
      object_prediction_map_(object_prediction_map),
      occlusion_checker_(occlusion_checker),
      nominal_path_(nominal_path),
      lane_sequence_iterator_(lane_sequence_iterator),
      traffic_light_detection_(traffic_light_detection),
      will_ego_borrow_opposite_road_(will_ego_borrow_opposite_road),
      motion_mode_(motion_mode),
      current_timestamp_(current_timestamp),
      plan_init_state_ra_arc_length_(
          nominal_path
              .GetProximity(
                  robot_state_.plan_init_state_snapshot().rear_axle_position(),
                  math::pb::UseExtensionFlag::kAllow)
              .arc_length),
      joint_pnc_map_service_(joint_pnc_map_service),
      lane_sequence_traffic_lights_(
          motion_mode == planner::pb::MotionMode::BACKWARD
              ? std::vector<traffic_rules::TrafficLightInfo>()
              : traffic_rules::GetLaneSequenceTrafficLights(
                    lane_sequence_iterator, joint_pnc_map_service, nominal_path,
                    traffic_light_detection,
                    robot_state_.plan_init_state_snapshot().timestamp(),
                    robot_state_.region(), &previous_iter_seed,
                    -plan_init_state_ra_arc_length_)),
      lane_sequence_crosswalks_(
          motion_mode == planner::pb::MotionMode::BACKWARD
              ? std::vector<traffic_rules::CrosswalkInLaneSequence>()
              : traffic_rules::ComputeCrosswalkTrafficRule(
                    lane_sequence_iterator, joint_pnc_map_service,
                    lane_sequence_traffic_lights_, traffic_light_detection,
                    nominal_path, -plan_init_state_ra_arc_length_)) {
  // Check the prerequisite conditions to generate hallucinated agents. (1) In
  // backward mode, the occlusion grid towards forward is useless. (2) A
  // occlusion grid is necessary to generate hallucinated agents.
  if (motion_mode == planner::pb::MotionMode::BACKWARD ||
      !occlusion_checker_.HasOriginalOcclusionGrid()) {
    return;
  }

  Initialize();

  GenerateHallucinatedAgentList(joint_pnc_map_service, filter_crosswalk,
                                debug_proto_ptr);

  ConstructPlannerTrackedObjects();

  // Populate debug info.
  if (debug_proto_ptr != nullptr) {
    // Populate hallucinated agent debug info.
    std::for_each(hallucinated_planner_object_map_.begin(),
                  hallucinated_planner_object_map_.end(),
                  [&debug_proto_ptr](
                      const std::pair<int64_t, PlannerObject>& id_object_pair) {
                    pb::HallucinatedAgentDebug* hallucinated_agent_debug =
                        debug_proto_ptr->add_hallucinated_agents();
                    PopulateHallucinatedAgentDebug(id_object_pair.second,
                                                   hallucinated_agent_debug);
                  });

    // Populate will_ego_borrow_opposite_road debug info.
    debug_proto_ptr->set_will_ego_borrow_opposite_road(
        will_ego_borrow_opposite_road_);
  }
}

void HallucinatedAgentManager::Initialize() {
  const ClearRegionGrid clear_region_grid(
      occlusion_checker_.occlusion_grid_param(), planner_object_map_,
      object_prediction_map_, nominal_path_, current_timestamp_);
  clear_region_mat_ = clear_region_grid.clear_region_mat();

  hallucinated_agent_list_.set_timestamp(current_timestamp_);

  // Find the max existing object id so we won't assign a duplicated id to a new
  // hallucinated agent.
  int64_t max_existing_object_id = 0;
  for (const auto& [object_id, planner_object] : planner_object_map_) {
    max_existing_object_id = std::max(max_existing_object_id, object_id);
  }
  hallucinated_agent_id_base_ = static_cast<int64_t>(
      std::pow(10, CountDigits(max_existing_object_id) + 1));

  // Compute the traffic signs along lane sequences.
  lane_sequence_traffic_signs_ =
      traffic_rules::ComputeTrafficSignInLaneSequence(
          lane_sequence_iterator_, nominal_path_,
          -plan_init_state_ra_arc_length_);
  // Compute the zones along lane sequences.
  lane_sequence_zones_ = traffic_rules::ComputeZoneInLaneSequence(
      lane_sequence_iterator_, nominal_path_,
      /*ego_arclength=*/0.0, -plan_init_state_ra_arc_length_);
  // Compute conflicting lanes with ego lane sequences and reason right-of-way
  // from traffic lights.
  lane_sequence_conflicting_lanes_ =
      traffic_rules::ComputeConflictingLanesInLaneSequence(
          lane_sequence_iterator_, joint_pnc_map_service_, nominal_path_,
          lane_sequence_traffic_lights_, motion_mode_,
          -plan_init_state_ra_arc_length_);
  lane_sequence_junctions_ = traffic_rules::ComputeJunctionsInLaneSequence(
      lane_sequence_iterator_, nominal_path_, motion_mode_,
      -plan_init_state_ra_arc_length_);
}

void HallucinatedAgentManager::GenerateHallucinatedAgentList(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    bool filter_crosswalk, pb::HallucinatedAgentManagerDebug* debug_ptr) {
  TRACE_EVENT_SCOPE(planner,
                    HallucinatedAgentManager_GenerateHallucinatedAgentList);

  // Create hallucinated agents for the first coming intersection.
  const traffic_rules::JunctionInLaneSequence* first_junction =
      traffic_rules::GetFirstJunctionAhead(lane_sequence_junctions_);
  if (first_junction != nullptr &&
      first_junction->ra_arclength_range_m.start_pos <
          kJunctionIgnoreDistanceInMeter) {
    // Compute ego lane traffic light info.
    const traffic_rules::TrafficLightInfo* ego_lane_traffic_light =
        traffic_rules::ComputeEgoLaneInJunctionTrafficLight(
            *first_junction, lane_sequence_traffic_lights_);
    // Split into different scenarios.
    if (!traffic_rules::IsJunctionWithTrafficLight(
            *first_junction, lane_sequence_traffic_lights_,
            joint_pnc_map_service)) {
      MaybeCreateHallucinatedAgentsForUnsignalizedIntersection(
          nominal_path_, *first_junction, debug_ptr);
    } else if (reasoning_util::IsUnprotectedLeftTurn(
                   first_junction->ego_lane_in_junction_ptr,
                   lane_sequence_conflicting_lanes_)) {
      MaybeCreateHallucinatedAgentsForUnprotectedLeftTurn(
          nominal_path_, *first_junction, debug_ptr);
    } else if (reasoning_util::IsUnprotectedRightTurn(
                   first_junction->ego_lane_in_junction_ptr,
                   traffic_light_detection_, joint_pnc_map_service)) {
      MaybeCreateHallucinatedAgentsForUnprotectedRightTurn(
          nominal_path_, *first_junction, debug_ptr);
    } else if (
        FLAGS_planning_enable_hallucinated_agent_for_signalized_junction &&
        ego_lane_traffic_light != nullptr) {
      if (ego_lane_traffic_light->is_green() ||
          (*lane_sequence_iterator_.current_lane())->IsInJunction()) {
        MaybeCreateHallucinatedAgentsForSignalizedIntersection(
            nominal_path_, *first_junction, debug_ptr);
      }
    }

    // TODO(pengxu): In practice, cyclists may cross a junction even if there is
    // not a marked crosswalk. We may want to create a virtual crosswalk in the
    // junction entrance for creating a hallucinated agent in the area if
    // occlusion happens.
    //
    // Relevant utility functions are moved into util/virtual_crosswalk_util.cpp
    //
    // Related CR:
    // https://kunpeng.xiaojukeji.com/view/revision/3657999
    // https://kunpeng.xiaojukeji.com/view/revision/3521391
  }

  // Create hallucinate agents for the first coming U-Turn with non-empty
  // conflicting lane.
  const auto uturn_lane_iter = std::find_if(
      lane_sequence_iterator_.current_lane(), lane_sequence_iterator_.end(),
      [](const pnc_map::Lane* lane) {
        return reasoning_util::IsUTurn(lane) &&
               !lane->conflicting_lanes().empty();
      });
  const bool is_uturn_lane_sequence =
      uturn_lane_iter != lane_sequence_iterator_.end();
  if (is_uturn_lane_sequence) {
    MaybeCreateHallucinatedAgentsForUTurn(nominal_path_, *uturn_lane_iter,
                                          debug_ptr);
  }

  // Create hallucinated agents for the first coming crosswalk.
  const traffic_rules::CrosswalkInLaneSequence* first_crosswalk =
      reasoning_util::GetFirstCrosswalkAhead(lane_sequence_crosswalks_);
  if (first_crosswalk != nullptr) {
    double first_crosswalk_min_arclength = std::numeric_limits<double>::max();
    for (const traffic_rules::YieldZoneInfo& yield_zone_info :
         first_crosswalk->yield_zone_infos) {
      first_crosswalk_min_arclength =
          std::min(first_crosswalk_min_arclength,
                   yield_zone_info.ra_arclength_range.start_pos);
    }
    if (first_crosswalk_min_arclength < kCrosswalkIgnoreDistanceInMeter &&
        !filter_crosswalk) {
      MaybeCreateHallucinatedAgentsForCrosswalk(nominal_path_, *first_crosswalk,
                                                debug_ptr);
    }
  }

  // Create hallucinated agents for the coming exit zones within the right most
  // lane sequence range of the lane sequence.

  // Find the right most lane sequence if existing, assuming there is at most
  // one continuous right most lane sequence in the lane sequence.
  std::vector<const pnc_map::Lane*> right_most_lane_sequence;
  right_most_lane_sequence.reserve(lane_sequence_iterator_.size());
  for (auto iter = lane_sequence_iterator_.current_lane();
       iter != lane_sequence_iterator_.end(); ++iter) {
    if ((*iter)->IsRightmostVehicleLane()) {
      right_most_lane_sequence.emplace_back(*iter);
    }
  }

  // Skip if there is no right most lane in the lane sequence.
  if (!right_most_lane_sequence.empty()) {
    // Get the right most lane sequence range.
    const math::Range1d rightmost_lane_arclength_range(
        reasoning_util::GetPointArcLengthWithOffset(
            right_most_lane_sequence.front()->center_line().GetStartPoint(),
            nominal_path_, -plan_init_state_ra_arc_length_),
        reasoning_util::GetPointArcLengthWithOffset(
            right_most_lane_sequence.back()->center_line().GetEndPoint(),
            nominal_path_, -plan_init_state_ra_arc_length_));
    // The computed rightmost lane arclength range is possibly not valid in some
    // cases, e.g. in waypoint assistant mode.
    if (IsValidRange(rightmost_lane_arclength_range)) {
      for (const traffic_rules::ZoneInLaneSequence& zone :
           lane_sequence_zones_) {
        // Early stop if the zone is beyond the occlusion map detect range. As
        // the zones in the list is sorted by the start arclength, we could
        // early stop if the condition is met.
        if (zone.start_ra_arclength >
            occlusion_checker_.occlusion_grid_param().forward_lookahead) {
          break;
        }
        // Skip if zone is already behind ego.
        if (zone.end_ra_arclength < 0) {
          continue;
        }
        // Skip if the zone is not a road exit zone.
        if (DCHECK_NOTNULL(zone.zone_ptr)->type() != hdmap::Zone::ROAD_EXIT) {
          continue;
        }
        // Skip if the zone is not within the right most lane sequence range.
        if (zone.start_ra_arclength <
                rightmost_lane_arclength_range.start_pos ||
            zone.end_ra_arclength > rightmost_lane_arclength_range.end_pos) {
          continue;
        }
        MaybeCreateHallucinatedAgentsForExitZone(nominal_path_, zone,
                                                 debug_ptr);
      }
    } else {
      std::string lane_sequence_debug_info = "Lane ids:";
      for (auto iter = lane_sequence_iterator_.current_lane();
           iter != lane_sequence_iterator_.end(); ++iter) {
        absl::StrAppend(&lane_sequence_debug_info,
                        strings::StringPrintf(" %ld", (*iter)->id()));
      }
      std::string right_most_lane_sequence_debug_info = "Lane ids:";
      for (const pnc_map::Lane* lane : right_most_lane_sequence) {
        absl::StrAppend(&right_most_lane_sequence_debug_info,
                        strings::StringPrintf(" %ld", lane->id()));
      }
      LOG(ERROR) << "Computed right most lane arclength range: ("
                 << rightmost_lane_arclength_range.start_pos << ","
                 << rightmost_lane_arclength_range.end_pos << "). "
                 << "Original lane sequence debug info: "
                 << lane_sequence_debug_info << ". "
                 << "Right most lane sequence debug info: "
                 << right_most_lane_sequence_debug_info << ".";
    }
  }

  // Create hallucinated agents for the oncoming lane during a XLane Nudge.
  // Occlusion during a XLane Nudge usually happens when the borrowed lane is
  // an opposite lane.
  //
  // We will skip checking for the cases of borrowing the same direction lane.
  // We will also skip checking for U-Turn cases, as it will always encroach
  // opposite road but has been handled separately
  // (MaybeCreateHallucinatedAgentsForUTurn).
  //
  // Assuming we only borrow one lane in the opposite road, then the target lane
  // would be the left most lane in the intersecting section of the opposite
  // road.
  if (!is_uturn_lane_sequence && will_ego_borrow_opposite_road_) {
    const pnc_map::Lane* current_lane = *lane_sequence_iterator_.current_lane();
    const pnc_map::Road* opposite_road =
        current_lane->section()->road()->opposite_road();
    if (opposite_road != nullptr) {
      const pnc_map::Lane* oncoming_lane = nullptr;
      for (const pnc_map::Section* section : opposite_road->sections()) {
        if (section->lanes().empty()) {
          continue;
        }
        // Since the lanes in a section is ordered from left to right, the
        // first in the lane vector is naturally the left most one.
        oncoming_lane = section->lanes().front();
        // No need to check further since we have the closest oncoming lane.
        break;
      }
      if (oncoming_lane != nullptr) {
        MaybeCreateHallucinatedAgentsForXLaneNudge(ego_pose_, oncoming_lane,
                                                   debug_ptr);
      }
    }
  }
}

void HallucinatedAgentManager::ConstructPlannerTrackedObjects() {
  hallucinated_tracked_objects_.reserve(
      hallucinated_agent_list_.agent_list().size());
  for (const prediction::pb::Agent& agent :
       hallucinated_agent_list_.agent_list()) {
    planner::TrackedObject tracked_object(
        current_timestamp_, agent,
        /*max_interested_prediction_timestamp=*/
        std::numeric_limits<int64_t>::max(),
        /*perception_range_lanes=*/std::vector<const pnc_map::Lane*>());
    hallucinated_tracked_objects_.push_back(std::move(tracked_object));
  }
}

const PlannerObject& HallucinatedAgentManager::UpdatePlannerObjectMap(
    const prediction::pb::Agent& hallucinated_agent,
    const std::vector<planner::PredictedTrajectoryWrapper>&
        object_predicted_trajectories,
    const planner::pb::OcclusionScenario::Enum occlusion_scenario) {
  const int64_t timestamp = hallucinated_agent_list_.timestamp();
  ObjectId object_id = hallucinated_agent.tracked_object().id();
  const PredictedTrajectoryWrapper& primary_trajectory =
      FindPrimaryTrajectory(object_predicted_trajectories);
  const planner::pb::TrajectoryPose trajectory_pose_at_plan_init_ts =
      primary_trajectory.ComputePoseAtTimestamp(timestamp);
  const TrafficParticipantPose pose_at_plan_init_ts(
      hallucinated_agent.tracked_object(), trajectory_pose_at_plan_init_ts);
  PlannerObject& planner_object =
      hallucinated_planner_object_map_
          .emplace(object_id, PlannerObject{hallucinated_agent, timestamp,
                                            pose_at_plan_init_ts})
          .first->second;
  
  planner_object.set_predicted_trajectories(object_predicted_trajectories);
  // Set is_hallucinated as true.
  planner_object.set_is_hallucinated(true);
  planner_object.set_occlusion_scenario(occlusion_scenario);
  return planner_object;
}

const std::vector<planner::PredictedTrajectoryWrapper>&
HallucinatedAgentManager::UpdateObjectPredictionMap(
    const prediction::pb::Agent& hallucinated_agent) {
  std::vector<PredictedTrajectoryWrapper>& new_bps =
      hallucinated_object_prediction_map_[hallucinated_agent.tracked_object()
                                              .id()];
  const prediction::pb::PredictedTrajectory& bp =
      hallucinated_agent.predicted_trajectories().at(0);
  new_bps.emplace_back(hallucinated_agent.tracked_object(), bp,
                       /*is_primary=*/true);
  return new_bps;
}

bool HallucinatedAgentManager::MaybeCreateHallucinatedAgentInConflictingLane(
    const math::geometry::PolylineCurve2d& path, const pnc_map::Lane* ego_lane,
    const pnc_map::Lane* conflicting_lane,
    const planner::pb::OcclusionScenario::Enum occlusion_scenario,
    const voy::perception::ObjectType hallucinated_agent_type,
    pb::HallucinatedAgentManagerDebug* debug_ptr) {
  // Check conflicting lane for the occluded points. If conflicting lane is not
  // occluded and it has lower road precedence, no need to spawn any
  // hallucinated agent.
  bool is_ego_right_turn = reasoning_util::IsRightTurn(ego_lane);
  std::vector<math::geometry::Point2d> occluded_points =
      GetOccludedPositionsInPartialLane(
          conflicting_lane, path, occlusion_checker_, clear_region_mat_,
          ego_pose_, is_ego_right_turn, hallucinated_agent_type);

  if (occluded_points.empty() &&
      reasoning_util::IsRoadPrecedenceLowerAgainstEgoLane(
          ego_lane, conflicting_lane, lane_sequence_traffic_signs_)) {
    if (debug_ptr != nullptr) {
      pb::OcclusionAreaDebug* occlusion_area_debug =
          debug_ptr->add_occlusion_areas();
      occlusion_area_debug->set_debug_str(absl::StrFormat(
          "No Strategy: no occlusion points && conflicting lane %i is lower "
          "precedence than ego lane %i;",
          conflicting_lane->id(), ego_lane->id()));
      PopulateOcclusionAreaContourDebug(
          conflicting_lane->id(), occlusion_scenario,
          conflicting_lane->border(), occlusion_area_debug);
    }
    return false;
  }

  // Search along the predecessors of conflicting lane to find a hallucinated
  // agent target lane which overlaps with occlusion region.
  //
  // We're prefer the STRAIGHT predecessor lane b/c it comes with higher
  // probability of a high-speed oncoming occluded vehicle.

  const pnc_map::Lane* target_lane = conflicting_lane;
  const pnc_map::Lane* predecessor_lane = reasoning_util::GetPredecessorLane(
      target_lane, /*prefer_straight_lane=*/true);

  std::vector<const pnc_map::Lane*> reference_lanes;
  reference_lanes.emplace_back(target_lane);

  // Keep searching for occluded lanes along the sequential predecessor until
  // the search distance exceeds kPredecessorLaneSearchHorizonInMeter.
  double search_distance = target_lane->length();

  while (occluded_points.empty() && predecessor_lane != nullptr &&
         search_distance <= kPredecessorLaneSearchHorizonInMeter) {
    target_lane = predecessor_lane;
    search_distance += target_lane->length();

    occluded_points =
        GetOccludedPositionsInLane(target_lane, occlusion_checker_,
                                   clear_region_mat_, hallucinated_agent_type);
    reference_lanes.insert(reference_lanes.begin(), target_lane);
    if (occluded_points.empty()) {
      predecessor_lane = reasoning_util::GetPredecessorLane(
          target_lane, /*prefer_straight_lane=*/true);
    }
  }

  // Skip if no occluded point is found.
  if (occluded_points.empty()) {
    if (debug_ptr != nullptr) {
      pb::OcclusionAreaDebug* occlusion_area_debug =
          debug_ptr->add_occlusion_areas();
      occlusion_area_debug->set_debug_str("No Strategy: no occlusion points;");
      PopulateOcclusionAreaContourDebug(target_lane->id(), occlusion_scenario,
                                        target_lane->border(),
                                        occlusion_area_debug);
    }
    return false;
  }

  // Create the occlusion area with the target lane.
  OcclusionArea& occlusion_area = occlusion_areas_.emplace_back(
      target_lane->id(), occlusion_scenario, target_lane->border());
  occlusion_area.set_occluded_points(occluded_points);

  // Update the associated ego lane of the occlusion area.
  occlusion_area.set_associated_ego_lane(ego_lane);

  // Update the associated conflicting lane of the occlusion area.
  occlusion_area.set_associated_conflicting_lane(conflicting_lane);

  // Construct a reference line from concatenating sequential center lines.
  DCHECK(!conflicting_lane->successors().empty());
  const pnc_map::Lane* successor_lane = conflicting_lane->successors().front();
  reference_lanes.emplace_back(successor_lane);
  math::geometry::PolylineCurve2d reference_line =
      common::ConnectLanesCenterLines(reference_lanes);

  // Find the spawn point to place a hallucinated agent.
  //   1) Find the closest point to path among the occluded points;
  //   2) Project the closest point onto the lane center line, assuming
  //   the vehicle is always moving around the center line.
  const math::geometry::Point2d closest_occluded_point =
      FindClosestPointToCurve(path, occluded_points);
  const math::geometry::Point2d spawn_point =
      FindProjectedPointOnCurve(reference_line, closest_occluded_point);

  // Read the heading from the lane info.
  const double hallucinated_agent_heading =
      target_lane->GetLaneDirection(spawn_point);

  // Get the hallucinated vehicle speed by reasoning based on the surrounding
  // traffic.
  const PlannerObject* agent_ahead =
      FindAgentAheadAlongPath(planner_object_map_, reference_line, spawn_point,
                              kAgentAheadDistanceThreshInMeter,
                              kAgentInSamePathLateralGapThreshInMeter);
  const double hallucinated_agent_speed = ReasonHallucinatedVehicleSpeedInLane(
      spawn_point, target_lane, agent_ahead, hallucinated_agent_heading,
      /*is_predecessor_lane=*/target_lane->id() != conflicting_lane->id());

  // Stop spawning a too slow / stopped hallucinated agent which has not much
  // value for ego speed reasoning.
  if (hallucinated_agent_speed < kStopSpeedThresholdInMps) {
    if (debug_ptr != nullptr) {
      pb::OcclusionAreaDebug* occlusion_area_debug =
          debug_ptr->add_occlusion_areas();
      occlusion_area_debug->set_debug_str(absl::StrFormat(
          "No Strategy: target lane %i hallucinated agent speed %.3f < stop "
          "speed threshold %.3f;",
          target_lane->id(), hallucinated_agent_speed,
          kStopSpeedThresholdInMps));
      PopulateOcclusionAreaContourDebug(occlusion_area.id(), occlusion_scenario,
                                        occlusion_area.contour(),
                                        occlusion_area_debug);
    }
    return false;
  }

  // Assign a hallucinated agent id.
  const int64_t hallucinated_agent_id = CodeUniqueHallucinatedAgentId(
      hallucinated_agent_id_base_, conflicting_lane->id(), occlusion_scenario,
      /*local_index=*/1);
  DCHECK(object_id_occlusion_scenario_map_.count(hallucinated_agent_id) == 0)
      << absl::StrFormat("hallucinated_agent_id %i already exists!",
                         hallucinated_agent_id);

  // Construct the hallucinated agent.
  const bool is_ped_or_cyc =
      (hallucinated_agent_type == voy::perception::ObjectType::PED ||
       hallucinated_agent_type == voy::perception::ObjectType::CYCLIST);
  const planner::HallucinatedAgentSizeParam& hallucinated_agent_size_param =
      OcclusionChecker::GetHallucinatedAgentSizeParam();
  const double hallucinated_agent_width =
      (is_ped_or_cyc) ? hallucinated_agent_size_param.pedestrian_radius * 2
                      : hallucinated_agent_size_param.car_width;
  const double hallucinated_agent_length =
      (is_ped_or_cyc) ? hallucinated_agent_size_param.pedestrian_radius * 2
                      : hallucinated_agent_size_param.car_length;

  voy::TrackedObject tracked_object = ConstructTrackedObject(
      hallucinated_agent_type, hallucinated_agent_id, spawn_point,
      hallucinated_agent_width, hallucinated_agent_length,
      hallucinated_agent_heading, hallucinated_agent_speed);

  prediction::pb::Agent& hallucinated_agent =
      *hallucinated_agent_list_.add_agent_list();
  hallucinated_agent.mutable_tracked_object()->Swap(&tracked_object);

  // Save occlusion scenario type.
  object_id_occlusion_scenario_map_.emplace(
      hallucinated_agent.tracked_object().id(), occlusion_scenario);

  // Add predicted trajectory to the hallucinated agent.
  AddCurvedPredictedTrajectory(reference_line, /*trajectory_id=*/1,
                               current_timestamp_, &hallucinated_agent);

  // Update Object Prediction Map. Note that predicted trajectories need to be
  // constructed before planner objects.
  const std::vector<planner::PredictedTrajectoryWrapper>&
      object_predicted_trajectories =
          UpdateObjectPredictionMap(hallucinated_agent);

  // Update Planner Object Map.
  const PlannerObject& hallucinated_planner_object = UpdatePlannerObjectMap(
      hallucinated_agent, object_predicted_trajectories, occlusion_scenario);

  // Update the occlusion area with the created hallucinated agent.
  occlusion_area.set_hallucinated_agent_ptr(&hallucinated_planner_object);

  // Update the occlusion area with the agent ahead of the hallucinated agent.
  occlusion_area.set_agent_ahead_ptr(agent_ahead);

  // Update the width and length of the occluded points cluster where
  // hallucinated agent was spawned.
  if (FLAGS_planning_enable_occlusion_grid_clustering) {
    const std::vector<math::geometry::Point2d> connected_occluded_points =
        occlusion_checker_.GetAgentConnectedOccludedPositions(
            occlusion_area.occluded_points(), closest_occluded_point);
    const std::optional<math::geometry::OrientedBox2d>
        occlusion_cluster_contour =
            MaybeGetOccludedPointClusterContour(connected_occluded_points);
    if (occlusion_cluster_contour.has_value()) {
      occlusion_area.set_occlusion_cluster_contour(*occlusion_cluster_contour);
    }
  }

  // Populate occlusion area debug info.
  pb::OcclusionAreaDebug* occlusion_area_debug =
      (debug_ptr != nullptr) ? debug_ptr->add_occlusion_areas() : nullptr;
  PopulateOcclusionAreaDebug(occlusion_area, occlusion_area_debug);

  return true;
}

bool HallucinatedAgentManager::MaybeCreateHallucinatedAgentsForUTurn(
    const math::geometry::PolylineCurve2d& path,
    const pnc_map::Lane* u_turn_lane,
    pb::HallucinatedAgentManagerDebug* debug_ptr) {
  bool is_hallucinated_agent_added = false;

  // Loop over the conflicting lanes of the u-turn lane.
  for (const pnc_map::ConflictingLane& conflicting_lane_in_junction :
       u_turn_lane->conflicting_lanes()) {
    const pnc_map::Lane* conflicting_lane = conflicting_lane_in_junction.lane();

    // Skip non-straight lanes.
    if (conflicting_lane->turn() != hdmap::Lane::STRAIGHT) {
      continue;
    }

    is_hallucinated_agent_added |=
        MaybeCreateHallucinatedAgentInConflictingLane(
            path, u_turn_lane, conflicting_lane,
            planner::pb::OcclusionScenario::U_TURN,
            conflicting_lane->IsRobotDrivable()
                ? voy::perception::ObjectType::VEHICLE
                : voy::perception::ObjectType::CYCLIST,
            debug_ptr);
  }

  if (is_hallucinated_agent_added) {
    rt_event::PostRtEvent<rt_event::planner::HallucinatedAgentDuringUTurn>();
  }

  return is_hallucinated_agent_added;
}

bool HallucinatedAgentManager::
    MaybeCreateHallucinatedAgentsForUnsignalizedIntersection(
        const math::geometry::PolylineCurve2d& path,
        const traffic_rules::JunctionInLaneSequence& junction,
        pb::HallucinatedAgentManagerDebug* debug_ptr) {
  bool is_hallucinated_agent_added = false;

  // Go through conflicting lanes in the junction and consider the lane sequence
  // that contains the conflicting lane.
  for (const pnc_map::ConflictingLane& conflicting_lane_in_junction :
       junction.ego_lane_in_junction_ptr->conflicting_lanes()) {
    const pnc_map::Lane* conflicting_lane = conflicting_lane_in_junction.lane();

    // Skip if the conflicting lane is not robot-drivable. A lane that is not
    // robot-drivable is usually a bicycle lane. We don't want to create a
    // vehicle in a bicycle lane.
    if (!conflicting_lane->IsRobotDrivable()) {
      continue;
    }

    // Skip if the lane is an unrealistic turning lane.
    if (IsUnrealisticTurning(conflicting_lane)) {
      continue;
    }

    is_hallucinated_agent_added |=
        MaybeCreateHallucinatedAgentInConflictingLane(
            path, junction.ego_lane_in_junction_ptr, conflicting_lane,
            planner::pb::OcclusionScenario::UNSIGNALIZED_INTERSECTION,
            conflicting_lane->IsRobotDrivable()
                ? voy::perception::ObjectType::VEHICLE
                : voy::perception::ObjectType::CYCLIST,
            debug_ptr);
  }

  return is_hallucinated_agent_added;
}

bool HallucinatedAgentManager::
    MaybeCreateHallucinatedAgentsForSignalizedIntersection(
        const math::geometry::PolylineCurve2d& path,
        const traffic_rules::JunctionInLaneSequence& junction,
        pb::HallucinatedAgentManagerDebug* debug_ptr) {
  // Find the conflicting lanes to the corresponding ego lane in junction.
  const auto conflicting_lanes_in_junction_iter =
      std::find_if(lane_sequence_conflicting_lanes_.begin(),
                   lane_sequence_conflicting_lanes_.end(),
                   [&junction](const traffic_rules::EgoLaneConflictingLanesInfo&
                                   ego_conflicting_lane_info) {
                     return ego_conflicting_lane_info.ego_lane.id() ==
                            junction.ego_lane_in_junction_ptr->id();
                   });
  if (conflicting_lanes_in_junction_iter ==
      lane_sequence_conflicting_lanes_.end()) {
    return false;
  }
  const traffic_rules::EgoLaneConflictingLanesInfo&
      conflicting_lanes_in_junction = *conflicting_lanes_in_junction_iter;

  const double ego_ra_to_front_bumper_m =
      robot_state_.GetLength() - robot_state_.car_model_with_shape()
                                     .shape_measurement()
                                     .rear_bumper_to_rear_axle();

  // Go through conflicting lanes in the junction and consider the lane sequence
  // that contains the conflicting lane.
  // NOTE: conflicting_lane_in_lane_sequence are already sorted by intersection
  // ranges.
  std::vector<const traffic_rules::ConflictingLaneInLaneSequence*>
      candidate_conflicting_lanes;
  for (const traffic_rules::ConflictingLaneInLaneSequence&
           conflicting_lane_in_junction :
       conflicting_lanes_in_junction.conflicting_lanes) {
    // Skip if the conflicting lane if its intersection ranges are already
    // behind ego. Intersection ranges are already offset by ego plan init
    // arclength.
    if (conflicting_lane_in_junction.conflicting_range_along_track.end_pos <=
        ego_ra_to_front_bumper_m) {
      continue;
    }
    // Skip if the lane is an unrealistic turning lane.
    if (IsUnrealisticTurning(
            conflicting_lane_in_junction.conflicting_lane().lane())) {
      continue;
    }
    // Add the remaining conflicting lane as a candidate for hallucinated agent
    // generation.
    candidate_conflicting_lanes.push_back(&conflicting_lane_in_junction);
  }

  if (candidate_conflicting_lanes.empty()) {
    return false;
  }

  // Associate all planner objects to candidate conflicting lanes. The return
  // association result is a set sorted according to the number of objects
  // associated to each conflicting lane, from the fewest objects to the most
  // objects. If there are multiple conflicting lanes associated to the same
  // number of objects, it will be sorted by conflicting intersection ranges.

  // TODO(zhihao,shenqu): move the object association logic to world_model when
  // world_model.traffic_flow is ready.
  const ConflictingLaneObjectAssociationResult lane_object_association_result =
      AssociateObjectsToConflictingLanes(candidate_conflicting_lanes,
                                         object_prediction_map_);

  // To save computation, at most add 3 hallucinated agents and its occlusion
  // areas.
  constexpr int kMaxNewHallucinatedAgentCount = 3;
  bool is_hallucinated_agent_added = false;

  // We prefer to use CYCLIST in all conflicting lanes for hallucinated agent,
  // no matter whether it is a drivable lane or not. We have observed
  // issues where a cyc comes out from occluded area on a drivable lane in
  // junction. If we use vehicle, we will use the size of a vehicle to erode the
  // original perception occlusion map, which might miss some small gaps between
  // large vehicles (we won't consider it a valid point to fit in an occluded
  // vehicle). To address issues for both occluded vehicle and occluded cyc, we
  // have to choose whichever the smaller size, so that we can have more
  // possible hallucinated spawning point coverage. Therefore, using cyc might
  // be better.
  const voy::perception::ObjectType hallucinated_agent_type =
      voy::perception::ObjectType::CYCLIST;

  // Prioritize adding hallucinated agents to the top 3 conflicting lanes with
  // the most associated objects. If no associated object to all conflicting
  // lanes, add the 3 closest conflicting lanes to ego.
  int hallucinated_agent_count = 0;
  for (auto rit =
           lane_object_association_result.non_empty_object_association.rbegin();
       rit !=
       lane_object_association_result.non_empty_object_association.rend();
       ++rit) {
    const bool create_successful =
        MaybeCreateHallucinatedAgentInConflictingLane(
            path, junction.ego_lane_in_junction_ptr,
            rit->first->conflicting_lane().lane(),
            planner::pb::OcclusionScenario::SIGNALIZED_INTERSECTION,
            hallucinated_agent_type, debug_ptr);
    is_hallucinated_agent_added |= create_successful;
    if (create_successful) {
      ++hallucinated_agent_count;
    }
    if (hallucinated_agent_count >= kMaxNewHallucinatedAgentCount) {
      break;
    }
  }

  if (hallucinated_agent_count < kMaxNewHallucinatedAgentCount) {
    for (auto it =
             lane_object_association_result.empty_object_association.begin();
         it != lane_object_association_result.empty_object_association.end();
         ++it) {
      const bool create_successful =
          MaybeCreateHallucinatedAgentInConflictingLane(
              path, junction.ego_lane_in_junction_ptr,
              it->first->conflicting_lane().lane(),
              planner::pb::OcclusionScenario::SIGNALIZED_INTERSECTION,
              hallucinated_agent_type, debug_ptr);
      is_hallucinated_agent_added |= create_successful;
      if (create_successful) {
        ++hallucinated_agent_count;
      }
      if (hallucinated_agent_count >= kMaxNewHallucinatedAgentCount) {
        break;
      }
    }
  }

  return is_hallucinated_agent_added;
}

bool HallucinatedAgentManager::
    MaybeCreateHallucinatedAgentsForUnprotectedLeftTurn(
        const math::geometry::PolylineCurve2d& path,
        const traffic_rules::JunctionInLaneSequence& junction,
        pb::HallucinatedAgentManagerDebug* debug_ptr) {
  bool is_hallucinated_agent_added = false;

  // Go through conflicting lanes in the junction and consider the lane sequence
  // that contains the conflicting lane.
  for (const pnc_map::ConflictingLane& conflicting_lane_in_junction :
       junction.ego_lane_in_junction_ptr->conflicting_lanes()) {
    const pnc_map::Lane* conflicting_lane = conflicting_lane_in_junction.lane();

    // Skip non-vehicle lanes.
    if (!conflicting_lane->IsRobotDrivable()) {
      continue;
    }

    // Skip non-straight lanes.
    if (!conflicting_lane->IsTurnable(planner::pb::TurnMode::STRAIGHT)) {
      continue;
    }

    const double ego_lane_initial_heading =
        junction.ego_lane_in_junction_ptr->GetLaneDirection(
            junction.ego_lane_in_junction_ptr->center_line().points().front());
    const double conflicting_lane_heading = conflicting_lane->GetLaneDirection(
        conflicting_lane->center_line().points().front());
    const double heading_diff = math::WrapFrom0To2Pi(ego_lane_initial_heading -
                                                     conflicting_lane_heading);
    // Make sure the conflicting lane is an oncoming lane. The difference
    // between ego lane (before entering the junction) heading and the oncoming
    // lane heading should be around 180 degrees.
    if (std::abs(heading_diff - M_PI) > M_PI / 6) {
      continue;
    }

    is_hallucinated_agent_added |=
        MaybeCreateHallucinatedAgentInConflictingLane(
            path, junction.ego_lane_in_junction_ptr, conflicting_lane,
            planner::pb::OcclusionScenario::UNPROTECTED_LEFT,
            conflicting_lane->IsRobotDrivable()
                ? voy::perception::ObjectType::VEHICLE
                : voy::perception::ObjectType::CYCLIST,
            debug_ptr);
  }

  if (is_hallucinated_agent_added) {
    rt_event::PostRtEvent<
        rt_event::planner::HallucinatedAgentDuringUnprotectedLeft>();
  }

  return is_hallucinated_agent_added;
}

bool HallucinatedAgentManager::
    MaybeCreateHallucinatedAgentsForUnprotectedRightTurn(
        const math::geometry::PolylineCurve2d& path,
        const traffic_rules::JunctionInLaneSequence& junction,
        pb::HallucinatedAgentManagerDebug* debug_ptr) {
  bool is_hallucinated_agent_added = false;

  // Go through conflicting lanes in the junction and consider the lane sequence
  // that contains the conflicting lane.
  for (const pnc_map::ConflictingLane& conflicting_lane_in_junction :
       junction.ego_lane_in_junction_ptr->conflicting_lanes()) {
    const pnc_map::Lane* conflicting_lane = conflicting_lane_in_junction.lane();
    // Skip non-straight lanes.
    if (!conflicting_lane->IsTurnable(planner::pb::TurnMode::STRAIGHT)) {
      continue;
    }

    is_hallucinated_agent_added |=
        MaybeCreateHallucinatedAgentInConflictingLane(
            path, junction.ego_lane_in_junction_ptr, conflicting_lane,
            planner::pb::OcclusionScenario::UNPROTECTED_RIGHT,
            conflicting_lane->IsRobotDrivable()
                ? voy::perception::ObjectType::VEHICLE
                : voy::perception::ObjectType::CYCLIST,
            debug_ptr);
  }

  if (is_hallucinated_agent_added) {
    rt_event::PostRtEvent<
        rt_event::planner::HallucinatedAgentDuringUnprotectedRight>();
  }

  return is_hallucinated_agent_added;
}

bool HallucinatedAgentManager::MaybeCreateHallucinatedAgentsForCrosswalk(
    const math::geometry::PolylineCurve2d& path,
    const traffic_rules::CrosswalkInLaneSequence& crosswalk,
    pb::HallucinatedAgentManagerDebug* debug_ptr) {
  // Get the occlusion area if occlusion occurs in the crosswalk.
  std::optional<OcclusionArea> occlusion_area_result =
      MaybeGetOcclusionAreaInCrosswalk(
          crosswalk, occlusion_checker_, clear_region_mat_, path,
          plan_init_state_ra_arc_length_, ego_pose_, joint_pnc_map_service_,
          (debug_ptr != nullptr) ? debug_ptr->mutable_debug_str() : nullptr);

  if (occlusion_area_result == std::nullopt) {
    return false;
  }

  if (occlusion_area_result->occluded_points().empty()) {
    if (debug_ptr != nullptr) {
      pb::OcclusionAreaDebug* occlusion_area_debug =
          debug_ptr->add_occlusion_areas();
      occlusion_area_debug->set_debug_str("No Strategy: no occlusion points;");
      PopulateOcclusionAreaContourDebug(
          occlusion_area_result->id(),
          planner::pb::OcclusionScenario::CROSSWALK,
          occlusion_area_result->contour(), occlusion_area_debug);
    }
    return false;
  }

  OcclusionArea& occlusion_area =
      occlusion_areas_.emplace_back(std::move(occlusion_area_result.value()));

  // Associated ego lane of the occlusion area is unused for now.
  DCHECK(occlusion_area.associated_ego_lane() == nullptr);

  const int64_t hallucinated_agent_id = CodeUniqueHallucinatedAgentId(
      hallucinated_agent_id_base_, crosswalk.crosswalk_ptr->id(),
      planner::pb::OcclusionScenario::CROSSWALK, /*local_index=*/1);
  DCHECK(object_id_occlusion_scenario_map_.count(hallucinated_agent_id) == 0)
      << absl::StrFormat("hallucinated_agent_id %i already exists!",
                         hallucinated_agent_id);

  // Find the spawn point to place the hallucinated agent. In the case of a
  // crosswalk, a hallucinated agent would be created and the spawn
  // point is defined as the closest (occluded) point to the ego path.
  const math::geometry::Point2d spawn_point =
      FindClosestPointToCurve(path, occlusion_area.occluded_points());

  // Find the heading of the hallucinated agent, assuming it is moving
  // along the crosswalk and towards the path.
  const double hallucinated_agent_heading = FindAgentHeadingAlongCrosswalk(
      crosswalk.crosswalk_ptr, spawn_point, path);

  const double hallucinated_agent_width =
      OcclusionChecker::GetHallucinatedAgentSizeParam().pedestrian_radius * 2;
  const double hallucinated_agent_length =
      OcclusionChecker::GetHallucinatedAgentSizeParam().pedestrian_radius * 2;

  // We roughly assume the pedestrian on the crosswalk would use a normal
  // walking speed when the crosswalk has HIGHER or EQUAL road precedence over
  // the ego lane, while a more aggressive speed would be used when the road
  // precedence is LOWER or the pedestrian is violating the traffic law.
  const double hallucinated_agent_speed =
      (crosswalk.road_precedence == planner::pb::RoadPrecedence::LOWER)
          ? kHallucinatedCyclistSpeedViolatingTrafficLawInMps
          : kHallucinatedCyclistSpeedFollowingTrafficLawInMps;

  // Assign the object id with the crosswalk id and the index of the side.
  voy::TrackedObject tracked_object = ConstructTrackedObject(
      voy::perception::ObjectType::CYCLIST, hallucinated_agent_id, spawn_point,
      hallucinated_agent_width, hallucinated_agent_length,
      hallucinated_agent_heading, hallucinated_agent_speed);

  prediction::pb::Agent& hallucinated_agent =
      *(hallucinated_agent_list_.add_agent_list());
  *(hallucinated_agent.mutable_tracked_object()) = std::move(tracked_object);

  // Save occlusion scenario type.
  object_id_occlusion_scenario_map_.emplace(
      hallucinated_agent.tracked_object().id(),
      planner::pb::OcclusionScenario::CROSSWALK);

  // Add predicted trajectory to the hallucinated agent.
  AddStraightPredictedTrajectory(/*trajectory_id=*/1, current_timestamp_,
                                 &hallucinated_agent);

  // Update Object Prediction Map. Note that predicted trajectories need to be
  // constructed before planner objects.
  const std::vector<planner::PredictedTrajectoryWrapper>&
      object_predicted_trajectories =
          UpdateObjectPredictionMap(hallucinated_agent);

  // Update Planner Object Map.
  const PlannerObject& hallucinated_planner_object =
      UpdatePlannerObjectMap(hallucinated_agent, object_predicted_trajectories,
                             planner::pb::OcclusionScenario::CROSSWALK);

  // Update the occlusion area with the created hallucinated agent.
  occlusion_area.set_hallucinated_agent_ptr(&hallucinated_planner_object);

  // Update the road precedence of the occlusion area over the ego lane.
  occlusion_area.set_road_precedence(crosswalk.road_precedence);

  // Update the width and length of the occluded points cluster where
  // hallucinated agent was spawned.
  if (FLAGS_planning_enable_occlusion_grid_clustering) {
    const std::vector<math::geometry::Point2d> connected_occluded_points =
        occlusion_checker_.GetAgentConnectedOccludedPositions(
            occlusion_area.occluded_points(), spawn_point);
    const std::optional<math::geometry::OrientedBox2d>
        occlusion_cluster_contour =
            MaybeGetOccludedPointClusterContour(connected_occluded_points);
    if (occlusion_cluster_contour.has_value()) {
      occlusion_area.set_occlusion_cluster_contour(*occlusion_cluster_contour);
    }
  }

  // Populate occlusion area debug info.
  pb::OcclusionAreaDebug* occlusion_area_debug =
      (debug_ptr != nullptr) ? debug_ptr->add_occlusion_areas() : nullptr;
  PopulateOcclusionAreaDebug(occlusion_area, occlusion_area_debug);

  rt_event::PostRtEvent<rt_event::planner::HallucinatedAgentAroundCrosswalk>();

  return true;
}

bool HallucinatedAgentManager::MaybeCreateHallucinatedAgentsForExitZone(
    const math::geometry::PolylineCurve2d& path,
    const traffic_rules::ZoneInLaneSequence& exit_zone,
    pb::HallucinatedAgentManagerDebug* debug_ptr) {
  // Roughly estimate the heading of the hallucinated vehicle in this zone.
  const double roughly_estimated_heading =
      RoughlyEstimateVehicleHeadingInExitZone(ego_pose_.yaw());

  // Get the occlusion area if occlusion occurs in the exit zone.
  OcclusionArea occlusion_area_result = GetOcclusionAreaInExitZone(
      exit_zone.zone_ptr, occlusion_checker_, clear_region_mat_, path,
      roughly_estimated_heading);

  if (occlusion_area_result.occluded_points().empty()) {
    if (debug_ptr != nullptr) {
      pb::OcclusionAreaDebug* occlusion_area_debug =
          debug_ptr->add_occlusion_areas();
      occlusion_area_debug->set_debug_str("No Strategy: no occluded points;");
      PopulateOcclusionAreaContourDebug(
          occlusion_area_result.id(), planner::pb::OcclusionScenario::EXIT_ZONE,
          occlusion_area_result.contour(), occlusion_area_debug);
    }
    return false;
  }

  OcclusionArea& occlusion_area =
      occlusion_areas_.emplace_back(std::move(occlusion_area_result));

  // Associated ego lane of the occlusion area is unused for now.
  DCHECK(occlusion_area.associated_ego_lane() == nullptr);

  // The hallucinated agent will be generated at the center point of the
  // occlusion area. Find two end points to create a fake straight trajectory
  // for the hallucinated agent.

  // 1. Use the occlusion area center point as the start point of the fake
  // trajectory.
  const math::geometry::Point2d hallucinated_agent_pos =
      occlusion_area.center_point();

  // 2. Project the center point to ego path and get the projected point.
  const math::ProximityQueryInfo proximity = path.GetProximity(
      hallucinated_agent_pos, math::pb::UseExtensionFlag::kAllow);
  const math::geometry::Point2d projected_point(proximity.x, proximity.y);

  // Construct a reference line by the connecting points.
  const math::geometry::PolylineCurve2d reference_line(
      {hallucinated_agent_pos, projected_point});

  // Find the heading according to the reference line.
  const double hallucinated_agent_heading =
      reference_line.GetInterpTheta(/*arc_length=*/0.0);

  // Get the hallucinated vehicle speed by reasoning based on the surrounding
  // traffic.
  const PlannerObject* agent_ahead_ptr = FindAgentAheadAlongPath(
      planner_object_map_, reference_line, hallucinated_agent_pos,
      kAgentAheadDistanceThreshInMeter,
      kAgentInSamePathLateralGapThreshInMeter);
  const double hallucinated_agent_speed =
      ReasonHallucinatedVehicleSpeedInExitZone(agent_ahead_ptr,
                                               hallucinated_agent_heading);

  const int64_t hallucinated_agent_id = CodeUniqueHallucinatedAgentId(
      hallucinated_agent_id_base_, exit_zone.zone_ptr->id(),
      planner::pb::OcclusionScenario::EXIT_ZONE, /*local_index=*/1);
  DCHECK(object_id_occlusion_scenario_map_.count(hallucinated_agent_id) == 0)
      << absl::StrFormat("hallucinated_agent_id %i already exists!",
                         hallucinated_agent_id);

  voy::TrackedObject tracked_object = ConstructTrackedObject(
      voy::perception::ObjectType::VEHICLE, hallucinated_agent_id,
      hallucinated_agent_pos,
      OcclusionChecker::GetHallucinatedAgentSizeParam().car_width,
      OcclusionChecker::GetHallucinatedAgentSizeParam().car_length,
      hallucinated_agent_heading, hallucinated_agent_speed);

  prediction::pb::Agent& hallucinated_agent =
      *(hallucinated_agent_list_.add_agent_list());
  *(hallucinated_agent.mutable_tracked_object()) = std::move(tracked_object);

  // Save occlusion scenario type.
  object_id_occlusion_scenario_map_.emplace(
      hallucinated_agent.tracked_object().id(),
      planner::pb::OcclusionScenario::EXIT_ZONE);

  // Add predicted trajectory to hallucinated vehicle.
  AddCurvedPredictedTrajectory(reference_line, /*trajectory_id=*/1,
                               current_timestamp_, &hallucinated_agent);

  // Update Object Prediction Map. Note that predicted trajectories need to be
  // constructed before planner objects.
  const std::vector<planner::PredictedTrajectoryWrapper>&
      object_predicted_trajectories =
          UpdateObjectPredictionMap(hallucinated_agent);

  // Update Planner Object Map.
  const PlannerObject& hallucinated_planner_object =
      UpdatePlannerObjectMap(hallucinated_agent, object_predicted_trajectories,
                             planner::pb::OcclusionScenario::EXIT_ZONE);

  // Update the occlusion area with the created hallucinated agent.
  occlusion_area.set_hallucinated_agent_ptr(&hallucinated_planner_object);

  // The width and length of the occluded points cluster are unused for now.
  DCHECK(!occlusion_area.occlusion_cluster_contour().has_value());

  // Populate occlusion area debug info.
  pb::OcclusionAreaDebug* occlusion_area_debug =
      (debug_ptr != nullptr) ? debug_ptr->add_occlusion_areas() : nullptr;
  PopulateOcclusionAreaDebug(occlusion_area, occlusion_area_debug);

  rt_event::PostRtEvent<rt_event::planner::HallucinatedAgentAroundExitZone>();

  return true;
}

bool HallucinatedAgentManager::MaybeCreateHallucinatedAgentsForXLaneNudge(
    const voy::Pose& ego_pose, const pnc_map::Lane* oncoming_lane,
    pb::HallucinatedAgentManagerDebug* debug_ptr) {
  DCHECK(oncoming_lane != nullptr);
  // Consider the lane sequence that goes through the oncoming lane.
  // A predecessor lane of the oncoming lane would not be considered if it is in
  // junction.
  const pnc_map::Lane* predecessor_lane =
      (oncoming_lane->predecessors().empty() ||
       oncoming_lane->predecessors().front()->IsInJunction())
          ? nullptr
          : oncoming_lane->predecessors().front();
  const std::vector<const pnc_map::Lane*> sequential_lanes =
      (predecessor_lane == nullptr)
          ? std::vector<const pnc_map::Lane*>({oncoming_lane})
          : std::vector<const pnc_map::Lane*>(
                {predecessor_lane, oncoming_lane});

  // Construct a reference line from connecting sequential center lines.
  const math::geometry::PolylineCurve2d reference_line =
      common::ConnectLanesCenterLines(sequential_lanes);

  bool hallucinated_agent_created = false;

  for (const pnc_map::Lane* target_lane : {oncoming_lane, predecessor_lane}) {
    // TODO(zhihaoruan): There is no guarantee that target_lane != nullptr.
    // However this is counterintuitive. If predecessor_lane is nullptr, it
    // should not be added to this for loop. Fix this in another CR.
    if (target_lane == nullptr) continue;

    // Check the area of the oncoming lane.
    std::vector<math::geometry::Point2d> occluded_points =
        GetOccludedPositionsInLane(target_lane, occlusion_checker_,
                                   clear_region_mat_,
                                   voy::perception::ObjectType::VEHICLE);

    // End searching here if no point found in the oncoming lane.
    if (occluded_points.empty()) {
      if (debug_ptr != nullptr) {
        pb::OcclusionAreaDebug* occlusion_area_debug =
            debug_ptr->add_occlusion_areas();
        occlusion_area_debug->set_debug_str("No Strategy: no occluded points;");
        PopulateOcclusionAreaContourDebug(
            target_lane->id(), planner::pb::OcclusionScenario::XLANE_NUDGE,
            target_lane->border(), occlusion_area_debug);
      }
      continue;
    }

    // Create the occlusion area for the target lane.
    OcclusionArea& occlusion_area = occlusion_areas_.emplace_back(
        oncoming_lane->id(), planner::pb::OcclusionScenario::XLANE_NUDGE,
        oncoming_lane->border());

    // Update the occlusion area with the occluded points inside the area.
    occlusion_area.set_occluded_points(occluded_points);

    // Associated ego lane of the occlusion area is unused for now.
    DCHECK(occlusion_area.associated_ego_lane() == nullptr);

    // Find the spawn point to place a hallucinated agent.
    //   1) Find the closest point to ego current position among the occluded
    //   points; 2) Project the closest point onto the lane center line,
    //   assuming the vehicle is always moving around the center line.
    const math::geometry::Point2d ego_current_position = {ego_pose.x(),
                                                          ego_pose.y()};
    const math::geometry::Point2d closest_occluded_point =
        FindClosestPointToPoint(ego_current_position, occluded_points);
    const math::geometry::Point2d spawn_point =
        FindProjectedPointOnCurve(reference_line, closest_occluded_point);

    // Read the heading from the lane info.
    const double hallucinated_vehicle_heading =
        target_lane->GetLaneDirection(spawn_point);

    // Get the hallucinated vehicle speed by reasoning based on the surrounding
    // traffic.
    const PlannerObject* agent_ahead_ptr =
        FindAgentAheadAlongPath(planner_object_map_, reference_line,
                                spawn_point, kAgentAheadDistanceThreshInMeter,
                                kAgentInSamePathLateralGapThreshInMeter);

    const double hallucinated_vehicle_speed =
        ReasonHallucinatedVehicleSpeedInLane(spawn_point, target_lane,
                                             agent_ahead_ptr,
                                             hallucinated_vehicle_heading,
                                             /*is_predecessor_lane=*/false);

    // Assign an unique agent id.
    const int64_t hallucinated_agent_id = CodeUniqueHallucinatedAgentId(
        hallucinated_agent_id_base_, target_lane->id(),
        planner::pb::OcclusionScenario::XLANE_NUDGE, /*local_index=*/1);
    DCHECK(object_id_occlusion_scenario_map_.count(hallucinated_agent_id) == 0)
        << absl::StrFormat("hallucinated_agent_id %i already exists!",
                           hallucinated_agent_id);

    // Construct the hallucinated agent.
    voy::TrackedObject tracked_object = ConstructTrackedObject(
        voy::perception::ObjectType::VEHICLE, hallucinated_agent_id,
        spawn_point,
        OcclusionChecker::GetHallucinatedAgentSizeParam().car_width,
        OcclusionChecker::GetHallucinatedAgentSizeParam().car_length,
        hallucinated_vehicle_heading, hallucinated_vehicle_speed);

    prediction::pb::Agent& hallucinated_agent =
        *(hallucinated_agent_list_.add_agent_list());
    *(hallucinated_agent.mutable_tracked_object()) = std::move(tracked_object);

    // Save occlusion scenario type.
    object_id_occlusion_scenario_map_.emplace(
        hallucinated_agent.tracked_object().id(),
        planner::pb::OcclusionScenario::XLANE_NUDGE);

    // Add predicted trajectory to the hallucinated agent.
    AddCurvedPredictedTrajectory(reference_line, /*trajectory_id=*/1,
                                 current_timestamp_, &hallucinated_agent);

    // Update Object Prediction Map. Note that predicted trajectories need to be
    // constructed before planner objects.
    const std::vector<planner::PredictedTrajectoryWrapper>&
        object_predicted_trajectories =
            UpdateObjectPredictionMap(hallucinated_agent);

    // Update Planner Object Map.
    const PlannerObject& hallucinated_planner_object = UpdatePlannerObjectMap(
        hallucinated_agent, object_predicted_trajectories,
        planner::pb::OcclusionScenario::XLANE_NUDGE);

    // Update the occlusion area with the created hallucinated agent.
    occlusion_area.set_hallucinated_agent_ptr(&hallucinated_planner_object);

    // The width and length of the occluded points cluster are unused for now.
    DCHECK(!occlusion_area.occlusion_cluster_contour().has_value());

    // Populate occlusion area debug info.
    pb::OcclusionAreaDebug* occlusion_area_debug =
        (debug_ptr != nullptr) ? debug_ptr->add_occlusion_areas() : nullptr;
    PopulateOcclusionAreaDebug(occlusion_area, occlusion_area_debug);

    hallucinated_agent_created = true;

    // Once there is a hallucinated agent created, there is no need to check the
    // further lane.
    break;
  }

  return hallucinated_agent_created;
}

const PredictedTrajectoryWrapper&
HallucinatedAgentManager::FindPrimaryTrajectory(
    const std::vector<planner::PredictedTrajectoryWrapper>&
        object_predicted_trajectories) const {
  const auto& primary_trajectory =
      std::find_if(object_predicted_trajectories.begin(),
                   object_predicted_trajectories.end(),
                   [](const PredictedTrajectoryWrapper& predicted_trajectory) {
                     return predicted_trajectory.is_primary_trajectory();
                   });
  DCHECK(primary_trajectory != object_predicted_trajectories.end());
  return *primary_trajectory;
}

}  // namespace speed
}  // namespace planner
