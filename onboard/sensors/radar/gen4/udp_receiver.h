#ifndef ONBOARD_SENSORS_RADAR_GEN4_UDP_RECEIVER_H_
#define ONBOARD_SENSORS_RADAR_GEN4_UDP_RECEIVER_H_

#include <functional>
#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include <boost/asio.hpp>

#include "add_data.h"
#include "node/fault_reporter.h"
#include "radar_parser_base.h"
#include "voy_protos/capture.pb.h"

// Add necessary overrides for boost::asio::ip::udp::endpoint to be used as a
// key in std::unordered_map.
namespace std {
template <>
struct hash<boost::asio::ip::udp::endpoint> {
  std::size_t operator()(const boost::asio::ip::udp::endpoint& endpoint) const {
    return hash<std::string>()(endpoint.address().to_string()) ^
           hash<uint16_t>()(endpoint.port());
  }
};

template <>
struct equal_to<boost::asio::ip::udp::endpoint> {
  bool operator()(const boost::asio::ip::udp::endpoint& lhs,
                  const boost::asio::ip::udp::endpoint& rhs) const {
    return lhs.address() == rhs.address() && lhs.port() == rhs.port();
  }
};
}  // namespace std

namespace radar {

using boost::asio::ip::udp;
using Publisher = varch::vnode::message_util::Publisher;

class UdpReceiver {
 public:
  explicit UdpReceiver(boost::asio::io_service* io_service);
  void InitUnicast1vN();
  void InitMulticast1v1(const std::string& facing);
  // Sinpro only, output debug log.
  void ReceiveLogSpawn();
  void ReceiveSpawn();
  void SendSpawn();
  void RecordLogRawPacket(const PacketPtr& pkt_buf_ptr, int64_t pkt_time_ns);

  std::unordered_map<udp::endpoint, std::unique_ptr<RadarParser>>&
  GetParserMap() {
    return parser_map_;
  }

  // Register parser (rule of receiver:sender detail below)
  //  - Multicast i:  Different group(ET), 1:1
  //  - Multicast ii: Adapt to Gen3, 1:N
  //  - Unicast   i:  Same recv endpoint(muler car), 1:N
  //  - Unicast   ii: Different recv endpoint, 1:1
  template <typename Parser>
  void RegisterParser(const std::string& facing) {
    std::string radar_ip = GetRadarConfig<RadarConfigItem::IP_ADDRESS>(facing);
    udp::endpoint radar_ep = udp::endpoint(
        boost::asio::ip::make_address(radar_ip), FLAGS_radar_port);
    auto parser = std::make_unique<Parser>(facing);
    parser_map_[radar_ep] = std::move(parser);
  }

  void ReportAllTimeouts() {
    for (const auto& [_, p] : parser_map_) {
      p->ReportTimeout(true);
    }
  }

 private:
  boost::asio::io_service* io_service_;
  // Receive & save pointcloud and system packets.
  std::shared_ptr<udp::socket> socket_;
  // Receive & save log packets.
  std::shared_ptr<udp::socket> socket_debug_;
  Publisher radar_log_pkt_pub_;
  Publisher radar_hardware_monitor_pub_;
  std::shared_ptr<voy::Capture> radar_log_pkt_;
  // Radar endpoint to parser map.
  std::unordered_map<udp::endpoint, std::unique_ptr<RadarParser>> parser_map_;
};

}  // namespace radar

#endif  // ONBOARD_SENSORS_RADAR_GEN4_UDP_RECEIVER_H_
