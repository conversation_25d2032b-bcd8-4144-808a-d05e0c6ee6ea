#ifndef ONBOARD_PLANNER_UTILITY_PATH_JUKE_INTEGRATED_PATH_COSTING_H_
#define ONBOARD_PLANNER_UTILITY_PATH_JUKE_INTEGRATED_PATH_COSTING_H_

#include "control_util/costing_base.h"
#include "planner/utility/path/juke_path_costing_util.h"
#include "planner/utility/path/path_kinematics_util.h"
#include "planner/utility/primitive/primitive_definition/decoupled_path_primitive.h"
#include "vehicle_model/utils.h"

namespace control_util {
// PathFeatureParameter includes parameters defined through motion/shape model
// that are not included in the top level costing .conf files since they are
// vehicle-specific. This helper function merges feature parameter from .conf
// file with motion model to create the full PathFeatureParameter object.
control_util::pb::PathFeatureParameter MergeWithMotionModelParams(
    control_util::pb::PathFeatureParameter feature_param,
    const vehicle_model::JukeIntegratedModelWithAxleRectangularShape&
        motion_model_with_shape);

// Computes the feature given type.
template <>
void GetFeature<DdpProblemType::kJukeIntegratedPath>(
    int step, int total_step_size,
    const planner::path::JukeIntegratedPathFormula::PbCostingTerm& costing_term,
    const planner::path::JukeIntegratedPathFormula::PbFeatureParameter&
        feature_param,
    const planner::path::JukeIntegratedPathFormula::State& x,
    const planner::path::JukeIntegratedPathFormula::Control& u,
    const planner::path::JukeIntegratedPathFormula::StepwisePrimitives&
        stepwise_primitives,
    const planner::path::JukeIntegratedPathFormula::Primitives& primitives,
    planner::path::JukeIntegratedPathFormula::Feature* feature);

// Compute the primitives at each step.
template <>
planner::path::JukeIntegratedPathFormula::StepwisePrimitives
planner::path::JukeIntegratedPathFormula::GetStepwisePrimitives(
    int step, bool is_terminal, const State& x, const Control& u,
    const Primitives& primitives) const;
}  // namespace control_util

#endif  // ONBOARD_PLANNER_UTILITY_PATH_JUKE_INTEGRATED_PATH_COSTING_H_
