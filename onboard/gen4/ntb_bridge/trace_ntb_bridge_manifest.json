{"component_name": "ntb_bridge", "events": [{"name": "Receiver<PERSON><PERSON><PERSON>ushed", "type": "instant"}, {"name": "ReceiverDispatch", "type": "duration", "suffix": [{"type": "int", "name": "dst_host_id"}]}, {"name": "ReceiverWaitingForData", "type": "duration", "suffix": [{"type": "int", "name": "dst_host_id"}]}, {"name": "ReceiverPacketUnpack", "type": "duration", "suffix": [{"type": "int", "name": "dst_host_id"}], "args": [{"name": "index", "type": "int64_t"}, {"name": "size", "type": "uint32_t"}, {"name": "len", "type": "uint32_t"}]}, {"name": "ReceiverBuildAndPub", "type": "duration", "suffix": [{"type": "string", "name": "topic"}], "args": [{"name": "data_size", "type": "int"}, {"name": "hw_time", "type": "int64_t"}, {"name": "msg_pub_time", "type": "int64_t"}, {"name": "msg_send_time", "type": "int64_t"}, {"name": "total_use_ns", "type": "int64_t", "is_lref": true}]}, {"name": "SenderSendMsg", "type": "duration", "suffix": [{"type": "string", "name": "topic"}], "args": [{"name": "hw_timestamp", "type": "int64_t", "is_lref": true}, {"name": "msg_pub_time", "type": "int64_t"}]}]}