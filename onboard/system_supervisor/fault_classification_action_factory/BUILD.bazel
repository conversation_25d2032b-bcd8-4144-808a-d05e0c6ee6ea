load("@rules_proto//proto:defs.bzl", "proto_library")
load("//bazel:defs.bzl", "install", "shared_library", "voy_add_rt_event", "voy_cc_test")

package(default_visibility = ["//visibility:public"])

voy_add_rt_event(
    name = "voy_rt_event_action_factory",
    manifest_file = ":rt_event_action_factory_manifest.json",
)

proto_library(
    name = "protos",
    srcs = [
        "action_factory_protos/action_factory_re_engage.proto",
        "action_factory_protos/camera_self_cleaning_message.proto",
        "action_factory_protos/can_communication_record.proto",
        "action_factory_protos/collision_upstream_faults.proto",
        "action_factory_protos/ss_exception_event_statistics.proto",
        "action_factory_protos/system_supervisor_mrc_triggers.proto",
        "action_factory_protos/system_supervisor_ticket_event.proto",
        "action_factory_protos/vehicle_exception_status.proto",
        "action_factory_protos/vehicle_parking_status.proto",
    ],
    strip_import_prefix = "/onboard/system_supervisor/fault_classification_action_factory",
    deps = [
        "//onboard/common/voy_protos:protos",
        "//onboard/mrc:protos",
    ],
)

cc_library(
    name = "toolkit",
    srcs = [
        "tool_kits/canbus_client.cpp",
        "tool_kits/util.cpp",
    ],
    hdrs = [
        "tool_kits/canbus_client.h",
        "tool_kits/util.h",
    ],
    include_prefix = ".",
    deps = [
        "//onboard/common/av_comm:voy_common_common",
        "//onboard/common/base:voy_base",
        "//onboard/common/node",
        "//onboard/system_supervisor/platform_interface",
        "//protobuf_cpp:protos_cpp",
        "@com_github_grpc_grpc//:grpc++",
        "@voy-sdk//:glog",
    ],
)

cc_library(
    name = "status_machine",
    srcs = [
        "status_machine/common.cpp",
        "status_machine/status_auto.cpp",
        "status_machine/status_bit.cpp",
        "status_machine/status_fallback_adas_control.cpp",
        "status_machine/status_fallback_autonomy_control.cpp",
        "status_machine/status_initialization.cpp",
        "status_machine/status_machine.cpp",
        "status_machine/status_machine_message.cpp",
        "status_machine/status_manual.cpp",
        "status_machine/status_need_disengage.cpp",
        "status_machine/status_remote_control.cpp",
        "status_machine/status_unimplemented.cpp",
        "status_machine/status_uninitialization.cpp",
    ],
    hdrs = [
        "status_machine/common.h",
        "status_machine/status_auto.h",
        "status_machine/status_bit.h",
        "status_machine/status_fallback_adas_control.h",
        "status_machine/status_fallback_autonomy_control.h",
        "status_machine/status_initialization.h",
        "status_machine/status_machine.h",
        "status_machine/status_machine_message.h",
        "status_machine/status_manual.h",
        "status_machine/status_need_disengage.h",
        "status_machine/status_remote_control.h",
        "status_machine/status_unimplemented.h",
        "status_machine/status_uninitialization.h",
    ],
    defines = select({
        "@//bazel/platforms:is_onboard_only": ["ONBOARD_ONLY"],
        "//conditions:default": [],
    }),
    include_prefix = ".",
    deps = [
        ":toolkit",
        "//onboard/common/av_comm:voy_common_common",
        "//onboard/fallback_level1:odd_utils",
        "//onboard/system_supervisor/platform_interface",
        "//onboard/third_party/json",
        "//protobuf_cpp:protos_cpp",
        "@voy-sdk//:gmock",
    ],
)

shared_library(
    name = "voy_fault_classification_action_factory",
    srcs = [
        "exception_event_statistics.cpp",
        "exception_event_statistics_nodelet.cpp",
        "status_machine_nodelet.cpp",
        "work_order_nodelet.cpp",
    ],
    hdrs = [
        "exception_event_statistics.h",
        "exception_event_statistics_nodelet.h",
        "status_machine_nodelet.h",
        "vehicle_system_level.h",
        "work_order_nodelet.h",
        "work_order_strategy.h",
    ],
    include_prefix = ".",
    deps = [
        ":status_machine",
        ":toolkit",
        ":voy_rt_event_action_factory",
        "//onboard/common/av_comm:voy_common_common",
        "//onboard/common/node",
        "//onboard/system_supervisor/common:system_supervisor_common",
        "//onboard/system_supervisor/platform_interface",
        "//onboard/third_party/coordinate_converter",
        "//onboard/third_party/json",
        "//protobuf_cpp:protos_cpp",
    ],
)

install(
    name = "install_rospkg",
    srcs = [":package.xml"],
    dest = "share/fault_classification_action_factory",
)

install(
    name = "install_vgraph",
    srcs = ["onboard/system_supervisor/fault_classification_action_factory/vgraph"],
    dest = "share/fault_classification_action_factory",
)

install(
    name = "install_config",
    srcs = ["onboard/system_supervisor/fault_classification_action_factory/config"],
    dest = "share/fault_classification_action_factory",
)

voy_cc_test(
    name = "test",
    srcs = [
        "test/construct_message.cpp",
        "test/construct_message.h",
        "test/event_handle_test.cpp",
        "test/status_machine_message_test_v2.cpp",
        "test/status_machine_nodelet_test.cpp",
        "test/work_order_nodelet_test.cpp",
        "test/work_order_strategy_test.cpp",
    ],
    defines = select({
        "@//bazel/platforms:is_onboard_only": ["ONBOARD_ONLY"],
        "//conditions:default": [],
    }),
    deps = [
        ":status_machine",
        ":toolkit",
        ":voy_fault_classification_action_factory",
        "//onboard/common/av_comm:voy_common_common",
        "//onboard/varch/tools:varch_emulator",
        "//protobuf_cpp:protos_cpp",
        "@voy-sdk//:gtest",
    ],
)

voy_cc_test(
    name = "re_engage_test",
    srcs = [
        "test/status_machine_message_test.cpp",
    ],
    defines = select({
        "@//bazel/platforms:is_onboard_only": ["ONBOARD_ONLY"],
        "//conditions:default": [],
    }),
    deps = [
        ":status_machine",
        ":toolkit",
        ":voy_fault_classification_action_factory",
        "//onboard/common/av_comm:voy_common_common",
        "//onboard/varch/tools:varch_emulator",
        "//protobuf_cpp:protos_cpp",
        "@voy-sdk//:gtest",
    ],
)

voy_cc_test(
    name = "ss_exception_event_statistics_test",
    srcs = [
        "test/exception_event_statistics_test_v2.cpp",
        "test/mock_param_getter.h",
    ],
    deps = [
        ":voy_fault_classification_action_factory",
        "//onboard/varch/tools:varch_emulator",
        "@voy-sdk//:gmock",
        "@voy-sdk//:gtest",
    ],
)
