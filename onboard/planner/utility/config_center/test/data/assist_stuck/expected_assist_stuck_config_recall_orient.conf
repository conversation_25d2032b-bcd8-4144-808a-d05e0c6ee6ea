config_tag: RECALL_ORIENT
ego_stuck_feature_extraction_config {
    n_frames: 2
    n_frames_adapted_features: 200
    low_speed_threshold: 0.1
    num_cycles_per_feature_dump: 10
}
assist_stuck_estimator_config {
    gbm_config {
        n_frames: 10
        num_cycles_per_model_inference: 10
        clamp_threshold: 1e5
        dump_adapted_features: false
        is_time_descending_order: true
        enable_2024q4_gbm: true
    }
    dnn_config {
        n_frames: 100
        num_cycles_per_model_inference: 10
        enable: true
        tags: ["beta"]
        input_dim: 9800
        is_time_descending_order: true
        model_inputs_name: [
            "old_dnn_features"
        ]
    }
    scenario_dnn_config {
        n_frames: 100
        num_cycles_per_model_inference: 10
        enable: true
        tags: ["vectorized_stuck_scenario_model"]
        input_dim: 9800
        is_time_descending_order: true
        model_inputs_name: [
            "old_dnn_features",
            "ego_geometric",
            "ego_heading",
            "ego_continuous",       
            "ego_discrete",         
            "ego_valid_geometric",
            "ego_valid_history",    
            "agent_geometric",      
            "agent_heading",
            "agent_continuous",     
            "agent_discrete",       
            "agent_valid_geometric",
            "agent_valid_history",  
            "lane_geometric",       
            "lane_continuous",
            "lane_discrete",        
            "lane_valid_geometric", 
            "lane_valid_history",
            "road_geometric",       
            "road_discrete",        
            "road_valid_geometric",
            "road_valid_history",   
            "zone_geometric",       
            "zone_discrete",
            "zone_valid_geometric", 
            "zone_valid_history",   
            "obj_geometric",
            "obj_discrete",         
            "obj_valid_geometric",  
            "obj_valid_history",
            "tl_continuous",        
            "tl_discrete",          
            "tl_valid_history"
        ]
        model_outputs_name: [
            "stuck_score"
        ]
    }
    use_dnn_model: true
    use_scenario_dnn_model: false
    time_diff_threshold_between_cur_and_pre_frame_in_ms: 1500
}
fp_rules_switch_config {
    enable_complex: false
    enable_crosswalk: false
    enable_holding: true
    enable_planned_max_speed: false
    enable_pull_over: true
    enable_queuing: true
    enable_start_up: false
    enable_traffic_jam: false
    enable_traffic_light: true
    enable_yield_dynamic_object: true
    enable_yielding_on_turn: true
    enable_occlusion: false
    enable_remote_speed_limit: true
    enable_stationary_leading_vehicle_in_queuing: true
}
ego_stuck_voluntary_behavior_config {
    stuck_speed_threshold_in_mps: 2.0
    voluntary_undetected_stuck_scene_time_threshold_in_msec: 1000
    voluntary_unstuck_time_threshold_in_msec: 5000
}
stuck_model_inference_config {
    cycle_count_threshold_for_ignore_only: 20
    cycle_count_to_model_infer_stuck_threshold: [
        {cycle: 30        threshold: 0.6},
        {cycle: 300       threshold: 0.4},
        {cycle: 600       threshold: 0.3},
        {cycle: 1200      threshold: 0.2},
        {cycle: 10000     threshold: 0.1}
    ]
    cycle_count_to_model_infer_stuck_threshold_for_parked: [
        {cycle: 30        threshold: 0.5},
        {cycle: 200       threshold: 0.3},
        {cycle: 600       threshold: 0.1},
        {cycle: 10000     threshold: 0.05}
    ]
    default_stuck_threshold: 0.01
    stuck_likelihood_queue_size: 10
    stuck_likelihood_queue_expire_dur_in_ms: 12000
    n_times_positive_for_recall: 3
    stuck_likelihood_continuous_threshold: 1500
    stuck_threshold_reduce_for_ignore_only: 0.08
}
stuck_request_config {
    hold_stuck_scene_request_time_threshold_in_msec: 2000
    model_request_hold_cycle_count: 300
    ops_refused_feedback_cycle_count: 900
}
stuck_scene_detector_fn_rules_config {
    inference_stuck_threshold: 0.2
    slow_moving_count: 4
    trigger_cycle_count: 60
    trajectory_length_threshold_for_boundary_in_meter: 2.1
    dist_to_physical_boundary_in_meter: 0.25
    trajectory_length_threshold_for_speed_limit_in_meter: 2.1
    min_discomfort_speed_threshold: 0.1
    valid_progress_threshold_in_m: 12.0
    dist_to_eol_fence_in_meter: 5.0
    dist_to_pull_over_destination_fence_in_meter: 2.0
    fn_perception_fp_config {
      ignorable_object_types: UNKNOWN
      ignorable_object_types: TRAFFIC_CONE
      ignorable_object_types: BARRIER
      non_ignorable_object_attributes: CYC_WITHOUT_PERSON_ATTR
    }
    fn_lane_change_stuck_config {
      extreme_low_speed_threshold : 0.1
      normal_urgency_distance_threshold: 20.0
      extreme_urgency_distance_threshold: 10.0
      normal_blocking_traffic_score_threshold: 0.5
      extreme_blocking_traffic_score_threshold: 0.01
    }
}
stuck_scene_detector_fp_rules_config {
    avg_a_with_in_future_three_sec_in_mpss: 0.3
    avg_v_after_future_five_sec_in_mps: 5
    congestion_prob: 0.6
    dist_to_cross_walk_in_meter: 8
    crosswalk_yield_object_count: 5
    crosswalk_yield_cycle_count: 40
    dist_to_end_of_lane_sequence_fence_in_meter: 15
    dist_to_junction_in_meter: 30
    dist_to_holding_in_meter: 3
    dist_to_traffic_light_in_meter: 60
    dist_to_yield_in_meter: 8
    planned_max_speed_in_mps: 5.0
    queuing_distance_in_meter: 30
    red_light_turn_hold_cycles: 80
    slow_moving_count: 3
    test_connective_bfs_cache_size: 128
    test_connective_bfs_max_depth: 5
    yield_hold_cycles: 20
    yield_temp_parked_cycles: 100
    dist_to_traffic_light_fence_for_complex_scene_in_meter: 30
    special_scenario_yielding_time_in_cycles: 80
    slow_moving_obj_in_other_lane_yield_cycles: 50
    right_lane_yield_cycles: 30
    total_yielding_cycles: 450
    mainly_yielding_static_ratio: 0.5
    yielding_collection_cycles: 300
    observed_stationary_duration_in_ms: 8000
    occlusion_config {
        interesting_dist_in_meter: 30
        occlusion_object_height_in_meter: 2.5
    }
    queuing_config {
        temp_parked_cars_near_boundary_config {
            interesting_dist_in_meter: 30
            farther_side_inlane_dist_in_meter: 2.0
            nearer_side_hard_boundary_dist_in_meter: 1.0
            farther_side_hard_boundary_dist_in_meter: 2.8
            temp_parked_cars_count: 2
        }
    }
    yield_temp_parked_cycles_when_pullover: 100
    yielding_on_turn_config {
        temp_parked_cars_near_boundary_config {
            interesting_dist_in_meter: 30
            farther_side_inlane_dist_in_meter: 1.0
            nearer_side_hard_boundary_dist_in_meter: 0.6
            farther_side_hard_boundary_dist_in_meter: 2.8
            temp_parked_cars_count: 1
        }
    }
    num_yield_dynamic_objects_varying: 3
    cycles_yield_dynamic_objects_varying: 30
}
fn_rules_switch_config {
    enable_better_progress_candidate: true
    enable_breakdown_car: false
    enable_near_hard_boundry: true
    enable_map_change_area: true
    enable_perception_fp: true
    enable_fn_eol: true
    enable_construction_zone: true
    enable_fn_no_block: true
    enable_fn_lane_change_stuck: true
    enable_fn_vehicle_hazard_signal: true
    enable_forcing_recall: true
}
blockage_reasoning_config {
    dynamic_types: VEHICLE
    dynamic_types: PED
    dynamic_types: CYCLIST
    dynamic_types: TRICYCLE
    dynamic_types: ANIMAL
    temp_parked_cars_near_boundary_config {
        interesting_dist_in_meter: 30
        farther_side_inlane_dist_in_meter: 0.9
        nearer_side_hard_boundary_dist_in_meter: 0.6
        farther_side_hard_boundary_dist_in_meter: 2.8
        gap_between_farther_and_near_inlane_dist_in_meter: 0.5
        temp_parked_cars_count: 2
    }
    occlusion_object_height_in_meter: 2.5
    interesting_distance_for_stationary_transition_in_meter: 20
}
forcing_recall_config {
    default_trigger_cycle: 300
    road_structure_to_trigger_cycle: [
        {
            attr: kBusBulb,
            cycle: 150
        },
        {
            attr: kEntryAndExit,
            cycle: 150
        },
        {
            attr: kMonoUTurn,
            cycle: 100
        },
        {
            attr: kInJunction,
            cycle: 100
        },
        {
            attr: kBeforeJunction,
            cycle: 100
        },
        {
            attr: kAfterJunction,
            cycle: 100
        },
        {
            attr: kCrosswalk,
            cycle: 100
        },
        {
            attr: kMonoRightTurn,
            cycle: 100
        },
        {
            attr: kMonoLeftTurn,
            cycle: 100
        }
    ]
    pre_trigger_cycle: 50
    reset_speed_mps: 2.0
    forcing_trigger_count_low_speed: 1800
}
