#ifndef ONBOARD_PLANNER_SPEED_SOLVER_OPTIMIZER_LQR_POTENTIALS_GENERATOR_H_
#define ONBOARD_PLANNER_SPEED_SOLVER_OPTIMIZER_LQR_POTENTIALS_GENERATOR_H_

#include <cmath>
#include <string>
#include <utility>
#include <vector>

#include "absl/container/flat_hash_map.h"
#include "adv_geom/path2d_with_juke.h"
#include "planner/speed/constraint/decision.h"
#include "planner/speed/constraint/gap_align_info.h"
#include "planner/speed/discomforts/discomfort_varying_limits.h"
#include "planner/speed/discomforts/discomfort_varying_min_range.h"
#include "planner/speed/discomforts/dynamic_limits.h"
#include "planner/speed/profile/profile.h"
#include "planner/speed/solver/optimizer/lqr_potentials.h"
#include "planner/speed/solver/optimizer/speed_opt_input.h"
#include "planner/speed/timemap/constraint_state_processor.h"
#include "planner/speed/timemap/time_map.h"
#include "planner_protos/speed_solver_debug.pb.h"

namespace planner {
namespace speed {

// This class generates LQR potentials for a given set of constraints,
// decisions, settings, configs and upstream profiles.
// TODO(yuwang): Split it into small pieces further.
class LqrPotentialsGenerator {
 public:
  LqrPotentialsGenerator(const SpeedOptInput& opt_input,
                         speed::pb::SpeedOptPotentialDebug* potential_debug);

  // Accessors.
  const LqrPotentials& lqr_potentials() { return lqr_potentials_; }
  LqrPotentials& mutable_lqr_potentials() { return lqr_potentials_; }

  // The flag indicates whether the ego is creeping.
  [[nodiscard]] bool is_creep() const { return is_creep_; }

 private:
  static bool ShouldAddStateBarriers(const Constraint& constraint, bool pass);
  static bool ShouldAddStateRepellers(const Constraint& constraint, bool pass);
  bool AddStateBarrierAndRepeller(const Constraint& constraint, bool pass);
  void AddSwitchPointYieldBarrierForRiskMitigation();

  static bool ShouldAddStaticRanges(const Constraint& constraint, bool pass);
  bool AddStaticRanges(int32_t c_ix, const Constraint& constraint, bool pass);

  static bool ShouldAddDynamicRanges(const Constraint& constraint, bool pass);
  bool AddDynamicRanges(const Constraint& constraint, bool pass);

  static bool ShouldAddProximitySpeed(
      const pb::ProximitySpeedParams& proximity_speed_params,
      double global_discomfort);
  bool AddProximitySpeed(
      const Constraint& constraint,
      tbb::concurrent_vector<
          ::planner::speed::pb::ProximitySpeedConstraintDebug>&
          proximity_speed_debugs);

  void AddSpeedAttractor();
  void AddSpeedAttractorForState(int state_ix, double gain);

  void AddLengthAttractor();
  void AddLengthAttractorForState(int state_ix, double gain);

  static bool ShouldAddFollowingAttractor(const Constraint& constraint);
  void AddFollowingAttractor(const Constraint& constraint);
  bool AddFollowingAttractor(const std::vector<Constraint>& constraints,
                             const std::vector<Decision>& decisions);

  // Adds following attractor for the closest yield speed object constraint if
  // enable_fast_acceleration. Returns true if following attractor is added to
  // 'lqr_potentials_'.
  void AddConstraintAttractor(const Constraint& constraint);
  bool AddConstraintAttractor(const std::vector<Constraint>& constraints,
                              const std::vector<Decision>& decisions);
  // Returns true if following attractor is added to 'lqr_potentials_'.
  bool AddCreepAttractor(const std::vector<Constraint>& constraints,
                         const std::vector<Decision>& decisions);

  static bool ShouldAddLastProfileJerkAttractor(
      const std::optional<Profile>& last_profile, const Limits& limits,
      double global_discomfort, bool enable_fast_acceleration,
      bool stay_stopped);
  void AddLastProfileJerkAttractor();

  bool SetStatePotentialsForOneConstraint(
      const Constraint& constraint, const Decision& decision,
      size_t constraint_ix,
      tbb::concurrent_vector<
          ::planner::speed::pb::ProximitySpeedConstraintDebug>&
          proximity_speed_debugs);

  void PruneStatePotentials();
  void PruneBarriers();
  void PruneRepellers();
  void PruneStaticRanges();
  void PruneDynamicRanges();
  void PruneAlConstraintsOnPosition();
  // Generate the lower bound of the repeller which we do not want to violate by
  // the force of attractor.
  void LowerBoundOfPrunedYieldRepeller();

  void UpdateStatePotentialDebug(
      const tbb::concurrent_vector<
          ::planner::speed::pb::ProximitySpeedConstraintDebug>&
          proximity_speed_debugs,
      speed::pb::SpeedOptPotentialDebug* potential_debug);

  const SpeedOptConfs& opt_conf_;
  const Profiles& profiles_;
  const TreeSearchResult& tree_search_result_;
  const absl::flat_hash_map<int, GapAlignInfo>& gap_align_info_map_;
  const SpeedOptSettings& opt_settings_;
  const double global_discomfort_;
  const pb::PotentialParams& potential_params_;

  // Immutable time from plan_init_time time to the first state where the
  // profile can be mutable. This value is stored corresponding to the selected
  // discomfort that we get from the profile searcher. The initial regulation is
  // currently on hold, so the value of this variable is set to 0.0. Once it is
  // confirmed that this phase is not needed, this member variable will be
  // removed.
  const double speed_immutable_t_ = 0.0;

  const TimeMapper time_mapper_;
  const ConstraintStateProcessor constraint_state_processor_;

  LqrPotentials lqr_potentials_;

  std::vector<double> yield_repeller_lower_bound_;
  bool is_creep_ = false;

  // By using the friend class mechanism, access private members in unit tests.
  friend class PrivateMemberAccessorForUT;
};

};  // namespace speed
}  // namespace planner

#endif  // ONBOARD_PLANNER_SPEED_SOLVER_OPTIMIZER_LQR_POTENTIALS_GENERATOR_H_
