#include "planner/ops_warning/st_agent/st_agent.h"

#include <vector>

#include <gtest/gtest.h>

#include "planner/ops_warning/st_agent/test/st_agent_test_util.h"
namespace planner {
namespace ops_warning {
namespace {
constexpr double kVehicleWidthInMeter = 1.864;
constexpr double kVehicleLengthInMeter = 4.925;
}  // namespace

TEST(STAgentTest, UpdateOdomSamples) {
  const pb::Trajectory trajectory = ConstructTrajectory(/*v=*/10.0);
  const STAgentParam param = {trajectory, kVehicleWidthInMeter,
                              kVehicleLengthInMeter};
  STAgent agent(param);
  const std::vector<OdomSampleInfo> const_speed_info = {{2000, 0.0}};
  // Test resolution_ms 1000.
  agent.UpdateOdomSamples(const_speed_info, /*base_time_ms=*/0,
                          /*resolution_ms=*/1000);
  EXPECT_EQ(3, agent.st_info_vec().size());

  // Test resolution_ms 100.
  agent.UpdateOdomSamples(const_speed_info, /*base_time_ms=*/0,
                          /*resolution_ms=*/100);
  EXPECT_EQ(21, agent.st_info_vec().size());

  // Test time base 100.
  agent.UpdateOdomSamples(const_speed_info, /*base_time_ms=*/100,
                          /*resolution_ms=*/100);
  EXPECT_DOUBLE_EQ(21.0, agent.st_info_vec().back().space.center().x());
  EXPECT_DOUBLE_EQ(0.0, agent.st_info_vec().back().space.center().y());
}

TEST(STAgentTest, Collide) {
  const pb::Trajectory trajectory_front = ConstructTrajectory(/*v=*/10.0);
  const STAgentParam param_front = {trajectory_front, kVehicleWidthInMeter,
                                    kVehicleLengthInMeter};
  STAgent agent_front(param_front);
  const std::vector<OdomSampleInfo> const_speed_info = {{2000, 0.0}};
  agent_front.UpdateOdomSamples(const_speed_info, /*base_time_ms=*/0,
                                /*resolution_ms=*/100);

  const pb::Trajectory trajectory_rear =
      ConstructTrajectory(/*v=*/10.0, /*x_offset=*/-10);
  const STAgentParam param_rear = {trajectory_rear, kVehicleWidthInMeter,
                                   kVehicleLengthInMeter};
  STAgent agent_rear(param_rear);
  agent_rear.UpdateOdomSamples(const_speed_info, /*base_time_ms=*/100,
                               /*resolution_ms=*/100);
  EXPECT_DEATH(agent_front.Collide(agent_rear), "");

  agent_rear.UpdateOdomSamples(const_speed_info, /*base_time_ms=*/0,
                               /*resolution_ms=*/200);
  EXPECT_DEATH(agent_front.Collide(agent_rear), "");

  agent_rear.UpdateOdomSamples(const_speed_info, /*base_time_ms=*/0,
                               /*resolution_ms=*/100);
  EXPECT_EQ(false, agent_front.Collide(agent_rear));

  const std::vector<OdomSampleInfo> const_acc_info = {{2000, 5.0}};
  agent_rear.UpdateOdomSamples(const_acc_info, /*base_time_ms=*/0,
                               /*resolution_ms=*/100);
  EXPECT_EQ(true, agent_front.Collide(agent_rear));
}

}  // namespace ops_warning
}  // namespace planner
