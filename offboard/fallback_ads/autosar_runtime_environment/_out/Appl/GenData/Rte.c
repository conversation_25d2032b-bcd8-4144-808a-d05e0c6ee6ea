/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *             File:  Rte.c
 *           Config:  DiDi_FBU.dpa
 *      ECU-Project:  DiDi_FBU
 *
 *        Generator:  MICROSAR RTE Generator Version 4.27.0
 *                    RTE Core Version 1.27.0
 *          License:  CBD2100894
 *
 *      Description:  RTE implementation file
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  INCLUDES
 *********************************************************************************************************************/

/* PRQA S 0777, 0779, 0857 EOF */ /* MD_MSR_Rule5.1, MD_MSR_Rule5.2, MD_MSR_Dir1.1 */
# include "_out/Appl/GenData/Rte.h"
#define RTE_CORE
// #include "Os.h" /* PRQA S 0828, 0883 */ /* MD_MSR_Dir1.1, MD_Rte_Os */
// #include "_out/Appl/GenData/Rte_Type.h"
// #include "Rte_Main.h"

// #include "_out/Appl/GenData/Components/Rte_AcuSigInput.h"
// #include "Rte_BswM.h"
// #include "_out/Appl/GenData/Components/Rte_CameraSigInput.h"
// #include "Rte_ComM.h"
// #include "_out/Appl/GenData/Components/Rte_Control.h"
// #include "Rte_CtApMyApp_Core1.h"
// #include "Rte_CtApMyApp_Core2.h"
// #include "Rte_CtApMyApp_Core3.h"
// #include "Rte_CtApMyApp_Core4.h"
// #include "Rte_CtApMyApp_Core5.h"
// #include "Rte_CtBswAdapt.h"
// #include "Rte_CtCdd.h"
// #include "Rte_CtSysCtrl.h"
// #include "Rte_CtVehCtrl.h"
// #include "Rte_Dcm.h"
// #include "_out/Appl/GenData/Components/Rte_Debug.h"
// #include "Rte_DemMaster_0.h"
// #include "Rte_DemSatellite_0.h"
// #include "Rte_Det.h"
// #include "Rte_EcuM.h"
// #include "_out/Appl/GenData/Components/Rte_FallbackSigOutput.h"
// #include "Rte_IoHwAb.h"
// #include "_out/Appl/GenData/Components/Rte_LatCtrlFct.h"
// #include "_out/Appl/GenData/Components/Rte_LgtCtrlFct.h"
// #include "Rte_NvM.h"
// #include "Rte_Os_OsCore0_swc.h"
// #include "Rte_Os_OsCore1_swc.h"
// #include "Rte_Os_OsCore2_swc.h"
// #include "Rte_Os_OsCore3_swc.h"
// #include "Rte_Os_OsCore4_swc.h"
// #include "Rte_Os_OsCore5_swc.h"
// #include "Rte_StbM.h"
// #include "_out/Appl/GenData/Components/Rte_VehSigInput.h"
// #include "Rte_WdgM_SystemApplication_OsCore0.h"
// #include "Rte_WdgM_SystemApplication_OsCore1.h"
// #include "Rte_WdgM_SystemApplication_OsCore2.h"
// #include "Rte_WdgM_SystemApplication_OsCore3.h"
// #include "SchM_BswM.h"
// #include "SchM_Can.h"
// #include "SchM_CanIf.h"
// #include "SchM_CanSM.h"
// #include "SchM_CanTp.h"
// #include "SchM_Com.h"
// #include "SchM_ComM.h"
// #include "SchM_Dcm.h"
// #include "SchM_Dem.h"
// #include "SchM_Det.h"
// #include "SchM_DoIP.h"
// #include "SchM_EcuM.h"
// #include "SchM_EthIf.h"
// #include "SchM_EthSM.h"
// #include "SchM_EthSwt_30_Sja1105PQRS.h"
// #include "SchM_EthTrcv_30_Tja1100.h"
// #include "SchM_Eth_30_Tc3xx.h"
// #include "SchM_Fee.h"
// #include "SchM_Fls_17_Dmu.h"
// #include "SchM_IoHwAb.h"
// #include "SchM_Mcu.h"
// #include "SchM_NvM.h"
// #include "SchM_PduR.h"
// #include "SchM_SoAd.h"
// #include "SchM_SomeIpXf.h"
// #include "SchM_StbM.h"
// #include "SchM_TcpIp.h"
// #include "SchM_WdgM.h"
// #include "SchM_Wdg_17_Scu.h"
// #include "SchM_Xcp.h"

// #include "Rte_Hook.h"

// #include "Com.h"
#if defined(IL_ASRCOM_VERSION)
# define RTE_USE_COM_TXSIGNAL_RDACCESS
#endif

// #include "Rte_Cbk.h"

// /* AUTOSAR 3.x compatibility */
// #if !defined (RTE_LOCAL)
// # define RTE_LOCAL static
// #endif


// /**********************************************************************************************************************
//  * API for enable / disable interrupts global
//  *********************************************************************************************************************/

// #if defined(osDisableGlobalKM) && !defined(RTE_DISABLE_ENHANCED_INTERRUPT_LOCK_API)
// # define Rte_DisableAllInterrupts() osDisableGlobalKM()   /* MICROSAR OS */
// #else
// # define Rte_DisableAllInterrupts() DisableAllInterrupts()   /* AUTOSAR OS */
// #endif

// #if defined(osEnableGlobalKM) && !defined(RTE_DISABLE_ENHANCED_INTERRUPT_LOCK_API)
// # define Rte_EnableAllInterrupts() osEnableGlobalKM()   /* MICROSAR OS */
// #else
// # define Rte_EnableAllInterrupts() EnableAllInterrupts()   /* AUTOSAR OS */
// #endif

// /**********************************************************************************************************************
//  * API for enable / disable interrupts up to the systemLevel
//  *********************************************************************************************************************/

// #if defined(osDisableLevelKM) && !defined(RTE_DISABLE_ENHANCED_INTERRUPT_LOCK_API)
// # define Rte_DisableOSInterrupts() osDisableLevelKM()   /* MICROSAR OS */
// #else
// # define Rte_DisableOSInterrupts() SuspendOSInterrupts()   /* AUTOSAR OS */
// #endif

// #if defined(osEnableLevelKM) && !defined(RTE_DISABLE_ENHANCED_INTERRUPT_LOCK_API)
// # define Rte_EnableOSInterrupts() osEnableLevelKM()   /* MICROSAR OS */
// #else
// # define Rte_EnableOSInterrupts() ResumeOSInterrupts()   /* AUTOSAR OS */
// #endif

// /**********************************************************************************************************************
//  * Rte Init State Variable
//  *********************************************************************************************************************/

// #define RTE_START_SEC_VAR_SystemApplication_OsCore0_NOCACHE_ZERO_INIT_8BIT
// #include "Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

// volatile VAR(uint8, RTE_VAR_ZERO_INIT_NOCACHE) Rte_InitState = RTE_STATE_UNINIT; /* PRQA S 3408 */ /* MD_Rte_3408 */
// volatile VAR(uint8, RTE_VAR_ZERO_INIT_NOCACHE) Rte_StartTiming_InitState = RTE_STATE_UNINIT; /* PRQA S 0850, 3408, 1514 */ /* MD_MSR_MacroArgumentEmpty, MD_Rte_3408, MD_Rte_1514 */

// #define RTE_STOP_SEC_VAR_SystemApplication_OsCore0_NOCACHE_ZERO_INIT_8BIT
// #include "Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

// #define RTE_START_SEC_VAR_SystemApplication_OsCore1_NOCACHE_ZERO_INIT_8BIT
// #include "Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

// volatile VAR(uint8, RTE_VAR_ZERO_INIT_NOCACHE) Rte_InitState_1 = RTE_STATE_UNINIT; /* PRQA S 3408 */ /* MD_Rte_3408 */

// #define RTE_STOP_SEC_VAR_SystemApplication_OsCore1_NOCACHE_ZERO_INIT_8BIT
// #include "Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

// #define RTE_START_SEC_VAR_SystemApplication_OsCore2_NOCACHE_ZERO_INIT_8BIT
// #include "Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

// volatile VAR(uint8, RTE_VAR_ZERO_INIT_NOCACHE) Rte_InitState_2 = RTE_STATE_UNINIT; /* PRQA S 3408 */ /* MD_Rte_3408 */

// #define RTE_STOP_SEC_VAR_SystemApplication_OsCore2_NOCACHE_ZERO_INIT_8BIT
// #include "Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

// #define RTE_START_SEC_VAR_SystemApplication_OsCore3_NOCACHE_ZERO_INIT_8BIT
// #include "Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

// volatile VAR(uint8, RTE_VAR_ZERO_INIT_NOCACHE) Rte_InitState_3 = RTE_STATE_UNINIT; /* PRQA S 3408 */ /* MD_Rte_3408 */

// #define RTE_STOP_SEC_VAR_SystemApplication_OsCore3_NOCACHE_ZERO_INIT_8BIT
// #include "Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

// #define RTE_START_SEC_VAR_SystemApplication_OsCore4_NOCACHE_ZERO_INIT_8BIT
// #include "Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

// volatile VAR(uint8, RTE_VAR_ZERO_INIT_NOCACHE) Rte_InitState_4 = RTE_STATE_UNINIT; /* PRQA S 3408 */ /* MD_Rte_3408 */

// #define RTE_STOP_SEC_VAR_SystemApplication_OsCore4_NOCACHE_ZERO_INIT_8BIT
// #include "Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

// #define RTE_START_SEC_VAR_SystemApplication_OsCore5_NOCACHE_ZERO_INIT_8BIT
// #include "Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

// volatile VAR(uint8, RTE_VAR_ZERO_INIT_NOCACHE) Rte_InitState_5 = RTE_STATE_UNINIT; /* PRQA S 3408 */ /* MD_Rte_3408 */

// #define RTE_STOP_SEC_VAR_SystemApplication_OsCore5_NOCACHE_ZERO_INIT_8BIT
// #include "Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


// /**********************************************************************************************************************
//  * Constants
//  *********************************************************************************************************************/

// #define RTE_START_SEC_CONST_UNSPECIFIED
// #include "Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(AcuControlTimestamp, RTE_CONST) Rte_C_AcuControlTimestamp_0 = {
//   0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(GnssPoseTimeStamp, RTE_CONST) Rte_C_GnssPoseTimeStamp_0 = {
//   0U, 0U, 0U, 0U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(PoseTimeStamp, RTE_CONST) Rte_C_PoseTimeStamp_0 = {
//   0U, 0U, 0U, 0U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(TrajectoryInfoInitTimeStamp, RTE_CONST) Rte_C_TrajectoryInfoInitTimeStamp_0 = {
//   0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(AcuControl, RTE_CONST) Rte_C_AcuControl_0 = {
//   0U, {0.0F}
// };
// CONST(BDP_FilteredLane_Struct, RTE_CONST) Rte_C_BDP_FilteredLane_Struct_0 = {
//   0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0U, 0.0F
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(CDS_CollisionDetection_Struct, RTE_CONST) Rte_C_CDS_CollisionDetection_Struct_0 = {
//   0.0F, 0.0F, 0.0F, FALSE, FALSE, FALSE, FALSE, 0.0F, 0.0F, 0.0F, 0.0F, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(CSI_LaneInfo_Struct, RTE_CONST) Rte_C_CSI_LaneInfo_Struct_0 = {
//   0U, 0U, 0.0F, 0U, 0U, 0U, 0.0F, 0.0F, 0.0F, 0.0F, 0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0U, 0U, 0U, 0U, 0U, 0U, 
//   0.0F, 0.0F, 0.0F, 0.0F, 0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0U, 0U, 0U, 0U, 0U, 0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 
//   0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0U, 0U, 0U, 0U, 0U, 0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0U, 0.0F, 0.0F, 0.0F, 0.0F, 
//   0.0F, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0.0F, 0.0F, 0.0F, 0.0F, 0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0U, 0U, 0U, 0U, 
//   0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(CSI_ObjectInfo_Struct, RTE_CONST) Rte_C_CSI_ObjectInfo_Struct_0 = {
//   0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0U, 0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 
//   0U, 0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0U, 0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 
//   0.0F, 0.0F, 0.0F, 0U, 0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0U, 0U, 0.0F, 0.0F, 
//   0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0U, 0U, 0.0F, 0.0F
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(ControlDebug, RTE_CONST) Rte_C_ControlDebug_0 = {
//   0ULL, {0.0F, 0.0F, 0.0F, 0.0F}, {{{0.0F, 0.0F, 0.0F, 0.0F}, {0.0F, 0.0F, 0.0F, 0.0F}, 0.0F, 0.0F}, {{0.0F, 0.0F, 
//   0.0F, 0.0F}, {0.0F, 0.0F, 0.0F, 0.0F}, 0.0F, 0.0F}}
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(ControlOutput, RTE_CONST) Rte_C_ControlOutput_0 = {
//   0U, 0.0F, 0.0F, 0.0F, 0.0F, FALSE, FALSE
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(EstimationDebug, RTE_CONST) Rte_C_EstimationDebug_0 = {
//   0ULL, {0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, 0U, FALSE, 0.0F, {0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 
//   0.0F}
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(FBS_DebugInfo_Struct, RTE_CONST) Rte_C_FBS_DebugInfo_Struct_0 = {
//   0U, 0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(FSA_SystemState_Struct, RTE_CONST) Rte_C_FSA_SystemState_Struct_0 = {
//   0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(FSC_FreeStopControl_Struct, RTE_CONST) Rte_C_FSC_FreeStopControl_Struct_0 = {
//   0.0F, FALSE, 0.0F, 0.0F
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(GnssPoseInput, RTE_CONST) Rte_C_GnssPoseInput_0 = {
//   0U, 0.0F, 0.0F, 0.0F, 0.0F, 0U, 0U, 0U, 0.0F, 0.0F
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(LAT_CtrlCmd_Struct, RTE_CONST) Rte_C_LAT_CtrlCmd_Struct_0 = {
//   0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0U, 0.0F, 0.0F, 0U, 0.0F, 
//   0.0F, 0.0F, 0.0F, 0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(LGT_CtrlCmd_Struct, RTE_CONST) Rte_C_LGT_CtrlCmd_Struct_0 = {
//   0U, 0.0F, 0.0F, {FALSE, 0.0F, FALSE, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0.0F, FALSE, 0.0F, 0U, 
//   0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0.0F, 0.0F, 0.0F, FALSE, FALSE, FALSE, FALSE, 0.0F, 0.0F, 0.0F, 0.0F, 0U}, {
//   0.0F, FALSE, 0.0F, 0.0F}
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(LSI_LatSigInput_Struct, RTE_CONST) Rte_C_LSI_LatSigInput_Struct_0 = {
//   FALSE, 0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0U, 0U, 0.0F, 0U, FALSE, FALSE, FALSE, FALSE, FALSE, 
//   FALSE, FALSE, FALSE, FALSE, FALSE, 0U, 0U, 0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0U, 0U, FALSE, 0.0F, 0.0F, 0.0F, 
//   0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0U, 0.0F, FALSE, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 
//   0.0F, 0U, 0U, FALSE, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0U, 0U, FALSE
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(LocalizationPoseInput, RTE_CONST) Rte_C_LocalizationPoseInput_0 = {
//   0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(OPS_ObjectList_Struct, RTE_CONST) Rte_C_OPS_ObjectList_Struct_0 = {
//   0.0F, FALSE, 0.0F, 0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_ALgtMaxAvl, RTE_CONST) Rte_C_SG_ALgtMaxAvl_0 = {
//   30U, 30U, 0U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_ALnchTiDly3, RTE_CONST) Rte_C_SG_ALnchTiDly3_0 = {
//   0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_AccrPedlPsd, RTE_CONST) Rte_C_SG_AccrPedlPsd_0 = {
//   FALSE, 0U, 0U, FALSE
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_AdNomALgtReqGroupSafe_A, RTE_CONST) Rte_C_SG_AdNomALgtReqGroupSafe_A_0 = {
//   3840U, 1920U, 0U, 0U, 2000U, 4000U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_AdPrimALgtLimReqGroupSafe_A, RTE_CONST) Rte_C_SG_AdPrimALgtLimReqGroupSafe_A_0 = {
//   3840U, 1920U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_AdPrimPose_A, RTE_CONST) Rte_C_SG_AdPrimPose_A_0 = {
//   524290U, 0U, 524290U, 0U, 524290U, 0U, 0U, 0U, 0U, 32760U, 0U, 524300U, 0U, 0, 0U, 0, 0U, 32760U, 0U, 524300U, 0U, 
//   0U, 0U, 12768U, 0U, 8192U, 0U, 8388000U, 0U, 2097000U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_AdPrimSafeStopActvGroupSafe, RTE_CONST) Rte_C_SG_AdPrimSafeStopActvGroupSafe_0 = {
//   0U, 0U, FALSE
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_AdPrimSteerStsSafeGroup, RTE_CONST) Rte_C_SG_AdPrimSteerStsSafeGroup_0 = {
//   20U, 0U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_AdPrimWhlAgEstimdGroupSafe, RTE_CONST) Rte_C_SG_AdPrimWhlAgEstimdGroupSafe_0 = {
//   0U, 0U, 0U, 16194U, 14000U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_AdPrimWhlAgReqGroupSafe_A, RTE_CONST) Rte_C_SG_AdPrimWhlAgReqGroupSafe_A_0 = {
//   0U, 0U, 16194U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_AdSecALgtLimReqGroupSafe_A, RTE_CONST) Rte_C_SG_AdSecALgtLimReqGroupSafe_A_0 = {
//   3840U, 1920U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_AdSecBlindStopMonActvnGroupSaf, RTE_CONST) Rte_C_SG_AdSecBlindStopMonActvnGroupSaf_0 = {
//   FALSE, 0U, 0U, FALSE
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_AdSecBrkActvnGroupSafe, RTE_CONST) Rte_C_SG_AdSecBrkActvnGroupSafe_0 = {
//   FALSE, 0U, 0U, FALSE
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_AdSecPahStsGroupSafe, RTE_CONST) Rte_C_SG_AdSecPahStsGroupSafe_0 = {
//   0U, 0U, FALSE, FALSE, FALSE
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_AdSecSafeStopActvGroupSafe, RTE_CONST) Rte_C_SG_AdSecSafeStopActvGroupSafe_0 = {
//   0U, 0U, FALSE
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_AdSecSteerActvnGroupSafe, RTE_CONST) Rte_C_SG_AdSecSteerActvnGroupSafe_0 = {
//   FALSE, FALSE, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_AdSecSteerModStsSafeGroupByGat, RTE_CONST) Rte_C_SG_AdSecSteerModStsSafeGroupByGat_0 = {
//   2U, 0U, 0U, FALSE, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_AdSecSteerStsSafeGroup, RTE_CONST) Rte_C_SG_AdSecSteerStsSafeGroup_0 = {
//   20U, 4U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_AdSecSteerStsSafeGroupByGatewy, RTE_CONST) Rte_C_SG_AdSecSteerStsSafeGroupByGatewy_0 = {
//   20U, 4U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_AdSecWhlAgRateLimEstimdSafe, RTE_CONST) Rte_C_SG_AdSecWhlAgRateLimEstimdSafe_0 = {
//   14000U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_AdSecWhlAgReqGroupSafe_A, RTE_CONST) Rte_C_SG_AdSecWhlAgReqGroupSafe_A_0 = {
//   0U, 0U, 16194U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_AdSteerPaddlPsdGroupSafe, RTE_CONST) Rte_C_SG_AdSteerPaddlPsdGroupSafe_0 = {
//   0U, 0U, FALSE, FALSE, FALSE, FALSE
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_AgDataRawSafe, RTE_CONST) Rte_C_SG_AgDataRawSafe_0 = {
//   255U, 0U, 0, 1U, 0, 1U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_AlrmSts1, RTE_CONST) Rte_C_SG_AlrmSts1_0 = {
//   0U, 0U, FALSE, FALSE, FALSE
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_AutnmsDrvModMngtExtSafe, RTE_CONST) Rte_C_SG_AutnmsDrvModMngtExtSafe_0 = {
//   0U, 0U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_AutnmsDrvModMngtGlbSafe1, RTE_CONST) Rte_C_SG_AutnmsDrvModMngtGlbSafe1_0 = {
//   0U, 0U, 0U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_AutnmsDrvStReq_A, RTE_CONST) Rte_C_SG_AutnmsDrvStReq_A_0 = {
//   0U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_BltLockStAtDrvr, RTE_CONST) Rte_C_SG_BltLockStAtDrvr_0 = {
//   TRUE, FALSE
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_BltLockStAtPass, RTE_CONST) Rte_C_SG_BltLockStAtPass_0 = {
//   TRUE, FALSE
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_BltLockStAtRowSecLe, RTE_CONST) Rte_C_SG_BltLockStAtRowSecLe_0 = {
//   TRUE, FALSE, TRUE
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_BltLockStAtRowSecRi, RTE_CONST) Rte_C_SG_BltLockStAtRowSecRi_0 = {
//   TRUE, FALSE, TRUE
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_BrkDegraded, RTE_CONST) Rte_C_SG_BrkDegraded_0 = {
//   0U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_BrkDegradedRdnt, RTE_CONST) Rte_C_SG_BrkDegradedRdnt_0 = {
//   0U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_BrkFricTqTotAtWhlsAct, RTE_CONST) Rte_C_SG_BrkFricTqTotAtWhlsAct_0 = {
//   0U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_BrkPedlPsdSafeGroup, RTE_CONST) Rte_C_SG_BrkPedlPsdSafeGroup_0 = {
//   FALSE, FALSE, 0U, 0U, 1U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_BrkPedlVal, RTE_CONST) Rte_C_SG_BrkPedlVal_0 = {
//   0U, 1U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_BrkTqMinReq, RTE_CONST) Rte_C_SG_BrkTqMinReq_0 = {
//   0U, 2U, 2U, 0U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_CarModInCrashStsSafe, RTE_CONST) Rte_C_SG_CarModInCrashStsSafe_0 = {
//   0U, 0U, FALSE
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_CllsnAidSnvtySeld, RTE_CONST) Rte_C_SG_CllsnAidSnvtySeld_0 = {
//   0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_ClstrSts1ForAutnmsDrv, RTE_CONST) Rte_C_SG_ClstrSts1ForAutnmsDrv_0 = {
//   0U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_ClstrSts2ForAutnmsDrv, RTE_CONST) Rte_C_SG_ClstrSts2ForAutnmsDrv_0 = {
//   0U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_DoorPassRePosnStsToAPI, RTE_CONST) Rte_C_SG_DoorPassRePosnStsToAPI_0 = {
//   0U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_DrvrGearShiftManReq, RTE_CONST) Rte_C_SG_DrvrGearShiftManReq_0 = {
//   FALSE, FALSE, FALSE, FALSE, FALSE
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_DrvrIntvSts, RTE_CONST) Rte_C_SG_DrvrIntvSts_0 = {
//   FALSE, FALSE, FALSE
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_DrvrPrsntGroup, RTE_CONST) Rte_C_SG_DrvrPrsntGroup_0 = {
//   1U, 1U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_EngFltIndcn, RTE_CONST) Rte_C_SG_EngFltIndcn_0 = {
//   FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, 
//   FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_FricEstimnFromVehDynGroup, RTE_CONST) Rte_C_SG_FricEstimnFromVehDynGroup_0 = {
//   0U, 1U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_OvrdDecelByDrvr, RTE_CONST) Rte_C_SG_OvrdDecelByDrvr_0 = {
//   FALSE, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_PrimALatDataRawSafe, RTE_CONST) Rte_C_SG_PrimALatDataRawSafe_0 = {
//   0U, 0U, 16384U, 16384U, 0U, 16384U, 1U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_PrimALgtDataRawSafe, RTE_CONST) Rte_C_SG_PrimALgtDataRawSafe_0 = {
//   0U, 0U, 16384U, 16384U, 0U, 16384U, 1U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_PrimAxleSlipStsAndRelAg, RTE_CONST) Rte_C_SG_PrimAxleSlipStsAndRelAg_0 = {
//   1U, 1U, 1U, 0, 1U, 0, 1U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_PrimVLatSafe, RTE_CONST) Rte_C_SG_PrimVLatSafe_0 = {
//   0U, 0U, 0, 0, 0U, 0, 1U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_PrimVehMSafe, RTE_CONST) Rte_C_SG_PrimVehMSafe_0 = {
//   0U, 0U, 1U, 0U, 0U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_PrimVehSpdGroupSafe, RTE_CONST) Rte_C_SG_PrimVehSpdGroupSafe_0 = {
//   0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 1U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_PrimWhlAgSpdFrntSafe, RTE_CONST) Rte_C_SG_PrimWhlAgSpdFrntSafe_0 = {
//   0U, 0U, 0, 1U, 0, 1U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_PrimWhlAgSpdReSafe, RTE_CONST) Rte_C_SG_PrimWhlAgSpdReSafe_0 = {
//   0U, 0U, 0, 1U, 0, 1U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_PrimWhlRotDirReSafe1, RTE_CONST) Rte_C_SG_PrimWhlRotDirReSafe1_0 = {
//   0U, 0U, 0U, 1U, 0U, 1U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_PrimWhlRotToothCntr, RTE_CONST) Rte_C_SG_PrimWhlRotToothCntr_0 = {
//   0U, 0U, 0U, 1U, 0U, 1U, 0U, 1U, 0U, 1U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_PrimYawRateSafe, RTE_CONST) Rte_C_SG_PrimYawRateSafe_0 = {
//   0U, 0U, 0, 0, 0U, 0, 1U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_PrpsnTqDir, RTE_CONST) Rte_C_SG_PrpsnTqDir_0 = {
//   0U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_PrpsnTqDirCpby, RTE_CONST) Rte_C_SG_PrpsnTqDirCpby_0 = {
//   0U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_RoadLoadNom, RTE_CONST) Rte_C_SG_RoadLoadNom_0 = {
//   0U, 0, 0U, FALSE
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_SSMBDegraded, RTE_CONST) Rte_C_SG_SSMBDegraded_0 = {
//   0U, 0U, 3U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_SSMDegraded, RTE_CONST) Rte_C_SG_SSMDegraded_0 = {
//   0U, 0U, 3U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_SecAdNomALgtReqGroupSafe_A, RTE_CONST) Rte_C_SG_SecAdNomALgtReqGroupSafe_A_0 = {
//   1920U, 1920U, 0U, 0U, 2000U, 2000U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_SecMaxALatEstimdGroup, RTE_CONST) Rte_C_SG_SecMaxALatEstimdGroup_0 = {
//   0, 0
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_SecPoseMonSafe, RTE_CONST) Rte_C_SG_SecPoseMonSafe_0 = {
//   0U, 0U, 0, 0, 0U, 0
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_SecSteerMotTq, RTE_CONST) Rte_C_SG_SecSteerMotTq_0 = {
//   0, 1U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_SecWhlLockSts, RTE_CONST) Rte_C_SG_SecWhlLockSts_0 = {
//   0U, 0U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_SnsrClngErrIf, RTE_CONST) Rte_C_SG_SnsrClngErrIf_0 = {
//   FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_StandStillMgrStsForHldSafe, RTE_CONST) Rte_C_SG_StandStillMgrStsForHldSafe_0 = {
//   0U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_SteerWhlSnsr, RTE_CONST) Rte_C_SG_SteerWhlSnsr_0 = {
//   0, 0, 1U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_SteerWhlTqGroup, RTE_CONST) Rte_C_SG_SteerWhlTqGroup_0 = {
//   0, 1U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_SwtExtrLiToAPI, RTE_CONST) Rte_C_SG_SwtExtrLiToAPI_0 = {
//   0U, 0U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_SwtIndcrToAPI, RTE_CONST) Rte_C_SG_SwtIndcrToAPI_0 = {
//   0U, 0U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_TirePWarnFrntRi, RTE_CONST) Rte_C_SG_TirePWarnFrntRi_0 = {
//   0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_TirePWarnReLe, RTE_CONST) Rte_C_SG_TirePWarnReLe_0 = {
//   0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_TirePWarnReRi, RTE_CONST) Rte_C_SG_TirePWarnReRi_0 = {
//   0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_VehMGroup, RTE_CONST) Rte_C_SG_VehMGroup_0 = {
//   0U, 0U, 1U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_WhlAgReqFb, RTE_CONST) Rte_C_SG_WhlAgReqFb_0 = {
//   0U, 0U, 14000U, 16194U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_WhlAgReqFbRdnt, RTE_CONST) Rte_C_SG_WhlAgReqFbRdnt_0 = {
//   0U, 0U, 14000U, 16194U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(SG_WhlLockSts, RTE_CONST) Rte_C_SG_WhlLockSts_0 = {
//   0U, 0U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(TrajectoryInput, RTE_CONST) Rte_C_TrajectoryInput_0 = {
//   0.0F, {{0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 
//   0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 
//   0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {
//   0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 
//   0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F
//   }, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 
//   0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 
//   0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 
//   0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 
//   0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F
//   }, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 
//   0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 
//   0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 
//   0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 
//   0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F
//   }, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 
//   0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 
//   0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 
//   0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 
//   0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F
//   }, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 
//   0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 
//   0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 
//   0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 
//   0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F
//   }, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 
//   0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 
//   0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 
//   0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 
//   0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F
//   }, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 
//   0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 
//   0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 
//   0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 
//   0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F
//   }, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 
//   0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 
//   0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 
//   0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 
//   0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F
//   }, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 
//   0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, {0U, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}}, {0U, 0U, FALSE, FALSE}
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(VDP_VehicleState_Struct, RTE_CONST) Rte_C_VDP_VehicleState_Struct_0 = {
//   0U, 0U, 0U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(VSI_McuCanTimeout_Struct, RTE_CONST) Rte_C_VSI_McuCanTimeout_Struct_0 = {
//   0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, FALSE, FALSE, FALSE, FALSE, 
//   FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(VSI_VehInfoFor1V1R_Struct, RTE_CONST) Rte_C_VSI_VehInfoFor1V1R_Struct_0 = {
//   0.0F, 0U, 0.0F, 0U, 0.0F, 0U, 0.0F, 0U, 0.0F, 0U, 0.0F, 0U, 0.0F, 0U, 0.0F, 0.0F, 0U, 0.0F, 0U, 0U, 0U
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(VSI_VehicleInfo_Struct, RTE_CONST) Rte_C_VSI_VehicleInfo_Struct_0 = {
//   0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0U, 0U, FALSE, FALSE, 0U, 0U, FALSE, FALSE
// };
// /* PRQA L:L1 */
// /* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
// CONST(VSP_VehicleSignal_Struct, RTE_CONST) Rte_C_VSP_VehicleSignal_Struct_0 = {
//   FALSE, 0.0F, FALSE, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F
// };
// /* PRQA L:L1 */

// #define RTE_STOP_SEC_CONST_UNSPECIFIED
// #include "Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */
// /**********************************************************************************************************************
//  * Defines for Rte_ComSendSignalProxy
//  *********************************************************************************************************************/
// #define RTE_COM_SENDSIGNALPROXY_NOCHANGE       (0U)
// #define RTE_COM_SENDSIGNALPROXY_SEND           (1U)
// #define RTE_COM_SENDSIGNALPROXY_INVALIDATE     (2U)



// /**********************************************************************************************************************
//  * Internal C/S connections
//  *********************************************************************************************************************/
// #define Rte_CS_ClientConfigIndex_CpVehCtrl_GlobalTime_Slave_StbMSynchronizedTimeBase_EthTsyn_GetCurrentTime 0U

// /* Queue definitions for internal C/S connections */
// #define RTE_START_SEC_VAR_NOCACHE_NOINIT_UNSPECIFIED
// #include "Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

// VAR(Rte_CS_ClientQueueType_StbM_GlobalTime_Slave_StbMSynchronizedTimeBase_EthTsyn_GetCurrentTime, RTE_VAR_NOINIT_NOCACHE) Rte_CS_ClientQueue_CpVehCtrl_GlobalTime_Slave_StbMSynchronizedTimeBase_EthTsyn_GetCurrentTime; /* PRQA S 1504 */ /* MD_MSR_Rule8.7 */

// #define RTE_STOP_SEC_VAR_NOCACHE_NOINIT_UNSPECIFIED
// #include "Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


// #define RTE_START_SEC_CONST_UNSPECIFIED
// #include "Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

// CONST(TaskType, RTE_CONST) Rte_CS_WaitingTaskList_CpVehCtrl_GlobalTime_Slave_StbMSynchronizedTimeBase_EthTsyn_GetCurrentTime[1] = {
//   MainTask_Core1_10ms
// };

// CONST(Rte_CS_ClientConfigType_StbM_GlobalTime_Slave_StbMSynchronizedTimeBase_EthTsyn_GetCurrentTime, RTE_CONST) Rte_CS_ClientConfig_StbM_GlobalTime_Slave_StbMSynchronizedTimeBase_EthTsyn_GetCurrentTime[1] = { /* PRQA S 1514, 1533 */ /* MD_Rte_1514, MD_Rte_1533 */
//   {
//     1,
//     Rte_CS_WaitingTaskList_CpVehCtrl_GlobalTime_Slave_StbMSynchronizedTimeBase_EthTsyn_GetCurrentTime,
//     &Rte_CS_ClientQueue_CpVehCtrl_GlobalTime_Slave_StbMSynchronizedTimeBase_EthTsyn_GetCurrentTime
//   }
// };

// #define RTE_STOP_SEC_CONST_UNSPECIFIED
// #include "Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

// #define RTE_START_SEC_CODE
// #include "Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

// FUNC(void, RTE_CODE) Rte_MemClr(P2VAR(void, AUTOMATIC, RTE_VAR_NOINIT) ptr, uint32_least num);
// FUNC(void, RTE_CODE) Rte_MemCpy(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) destination, P2CONST(void, AUTOMATIC, RTE_APPL_DATA) source, uint32_least num); /* PRQA S 1505, 3408 */ /* MD_MSR_Rule8.7, MD_Rte_3408 */
// FUNC(void, RTE_CODE) Rte_MemCpy32(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) destination, P2CONST(void, AUTOMATIC, RTE_APPL_DATA) source, uint32_least num); /* PRQA S 1505, 3408 */ /* MD_MSR_Rule8.7, MD_Rte_3408 */
// FUNC(uint8, RTE_CODE) Rte_GetInternalModeIndex_BswM_ESH_Mode(BswM_ESH_Mode mode); /* PRQA S 3408 */ /* MD_Rte_3408 */
// FUNC(uint8, RTE_CODE) Rte_GetInternalModeIndex_Dcm_DcmCommunicationControl(Dcm_CommunicationModeType mode); /* PRQA S 3408 */ /* MD_Rte_3408 */
// FUNC(uint8, RTE_CODE) Rte_GetInternalModeIndex_Dcm_DcmControlDtcSetting(Dcm_ControlDtcSettingType mode); /* PRQA S 3408 */ /* MD_Rte_3408 */
// FUNC(uint8, RTE_CODE) Rte_GetInternalModeIndex_Dcm_DcmDiagnosticSessionControl(Dcm_DiagnosticSessionControlType mode); /* PRQA S 3408 */ /* MD_Rte_3408 */
// FUNC(uint8, RTE_CODE) Rte_GetInternalModeIndex_Dcm_DcmEcuReset(Dcm_EcuResetType mode); /* PRQA S 3408 */ /* MD_Rte_3408 */

// #define RTE_STOP_SEC_CODE
// #include "Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


// #define RTE_START_SEC_CODE
// #include "Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

// /**********************************************************************************************************************
//  * Helper functions for mode management
//  *********************************************************************************************************************/
// FUNC(uint8, RTE_CODE) Rte_GetInternalModeIndex_BswM_ESH_Mode(BswM_ESH_Mode mode) /* PRQA S 3408 */ /* MD_Rte_3408 */
// {
//   uint8 ret;

//   if (mode == 0U)
//   {
//     ret = 3U;
//   }
//   else if (mode == 1U)
//   {
//     ret = 1U;
//   }
//   else if (mode == 2U)
//   {
//     ret = 0U;
//   }
//   else if (mode == 3U)
//   {
//     ret = 4U;
//   }
//   else if (mode == 4U)
//   {
//     ret = 2U;
//   }
//   else
//   {
//     ret = 5U;
//   }

//   return ret;
// } /* PRQA S 6080 */ /* MD_MSR_STMIF */

// FUNC(uint8, RTE_CODE) Rte_GetInternalModeIndex_Dcm_DcmCommunicationControl(Dcm_CommunicationModeType mode) /* PRQA S 3408 */ /* MD_Rte_3408 */
// {
//   uint8 ret;

//   if (mode == 0U)
//   {
//     ret = 10U;
//   }
//   else if (mode == 1U)
//   {
//     ret = 7U;
//   }
//   else if (mode == 2U)
//   {
//     ret = 1U;
//   }
//   else if (mode == 3U)
//   {
//     ret = 4U;
//   }
//   else if (mode == 4U)
//   {
//     ret = 9U;
//   }
//   else if (mode == 5U)
//   {
//     ret = 6U;
//   }
//   else if (mode == 6U)
//   {
//     ret = 0U;
//   }
//   else if (mode == 7U)
//   {
//     ret = 3U;
//   }
//   else if (mode == 8U)
//   {
//     ret = 11U;
//   }
//   else if (mode == 9U)
//   {
//     ret = 8U;
//   }
//   else if (mode == 10U)
//   {
//     ret = 2U;
//   }
//   else if (mode == 11U)
//   {
//     ret = 5U;
//   }
//   else
//   {
//     ret = 12U;
//   }

//   return ret;
// } /* PRQA S 6030, 6080 */ /* MD_MSR_STCYC, MD_MSR_STMIF */

// FUNC(uint8, RTE_CODE) Rte_GetInternalModeIndex_Dcm_DcmControlDtcSetting(Dcm_ControlDtcSettingType mode) /* PRQA S 3408 */ /* MD_Rte_3408 */
// {
//   uint8 ret;

//   if (mode == 0U)
//   {
//     ret = 1U;
//   }
//   else if (mode == 1U)
//   {
//     ret = 0U;
//   }
//   else
//   {
//     ret = 2U;
//   }

//   return ret;
// }

// FUNC(uint8, RTE_CODE) Rte_GetInternalModeIndex_Dcm_DcmDiagnosticSessionControl(Dcm_DiagnosticSessionControlType mode) /* PRQA S 3408 */ /* MD_Rte_3408 */
// {
//   uint8 ret;

//   if (mode == 1U)
//   {
//     ret = 0U;
//   }
//   else if (mode == 2U)
//   {
//     ret = 2U;
//   }
//   else if (mode == 3U)
//   {
//     ret = 1U;
//   }
//   else
//   {
//     ret = 3U;
//   }

//   return ret;
// }

// FUNC(uint8, RTE_CODE) Rte_GetInternalModeIndex_Dcm_DcmEcuReset(Dcm_EcuResetType mode) /* PRQA S 3408 */ /* MD_Rte_3408 */
// {
//   uint8 ret;

//   if (mode == 0U)
//   {
//     ret = 5U;
//   }
//   else if (mode == 1U)
//   {
//     ret = 1U;
//   }
//   else if (mode == 2U)
//   {
//     ret = 4U;
//   }
//   else if (mode == 3U)
//   {
//     ret = 6U;
//   }
//   else if (mode == 4U)
//   {
//     ret = 2U;
//   }
//   else if (mode == 5U)
//   {
//     ret = 3U;
//   }
//   else if (mode == 6U)
//   {
//     ret = 0U;
//   }
//   else
//   {
//     ret = 7U;
//   }

//   return ret;
// } /* PRQA S 6080 */ /* MD_MSR_STMIF */

// #define RTE_STOP_SEC_CODE
// #include "Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


// /**********************************************************************************************************************
//  * Timer handling
//  *********************************************************************************************************************/

// #if defined OS_US2TICKS_SystemTimer_Core0
// # define RTE_USEC_SystemTimer_Core0 OS_US2TICKS_SystemTimer_Core0
// #else
// # define RTE_USEC_SystemTimer_Core0(val) ((TickType)RTE_CONST_USEC_SystemTimer_Core0_##val) /* PRQA S 0342 */ /* MD_MSR_Rule20.10_0342 */
// #endif

// #if defined OS_MS2TICKS_SystemTimer_Core0
// # define RTE_MSEC_SystemTimer_Core0 OS_MS2TICKS_SystemTimer_Core0
// #else
// # define RTE_MSEC_SystemTimer_Core0(val) ((TickType)RTE_CONST_MSEC_SystemTimer_Core0_##val) /* PRQA S 0342 */ /* MD_MSR_Rule20.10_0342 */
// #endif

// #if defined OS_SEC2TICKS_SystemTimer_Core0
// # define RTE_SEC_SystemTimer_Core0 OS_SEC2TICKS_SystemTimer_Core0
// #else
// # define RTE_SEC_SystemTimer_Core0(val)  ((TickType)RTE_CONST_SEC_SystemTimer_Core0_##val) /* PRQA S 0342 */ /* MD_MSR_Rule20.10_0342 */
// #endif

// #if defined OS_US2TICKS_SystemTimer_Core1
// # define RTE_USEC_SystemTimer_Core1 OS_US2TICKS_SystemTimer_Core1
// #else
// # define RTE_USEC_SystemTimer_Core1(val) ((TickType)RTE_CONST_USEC_SystemTimer_Core1_##val) /* PRQA S 0342 */ /* MD_MSR_Rule20.10_0342 */
// #endif

// #if defined OS_MS2TICKS_SystemTimer_Core1
// # define RTE_MSEC_SystemTimer_Core1 OS_MS2TICKS_SystemTimer_Core1
// #else
// # define RTE_MSEC_SystemTimer_Core1(val) ((TickType)RTE_CONST_MSEC_SystemTimer_Core1_##val) /* PRQA S 0342 */ /* MD_MSR_Rule20.10_0342 */
// #endif

// #if defined OS_SEC2TICKS_SystemTimer_Core1
// # define RTE_SEC_SystemTimer_Core1 OS_SEC2TICKS_SystemTimer_Core1
// #else
// # define RTE_SEC_SystemTimer_Core1(val)  ((TickType)RTE_CONST_SEC_SystemTimer_Core1_##val) /* PRQA S 0342 */ /* MD_MSR_Rule20.10_0342 */
// #endif

// #if defined OS_US2TICKS_SystemTimer_Core2
// # define RTE_USEC_SystemTimer_Core2 OS_US2TICKS_SystemTimer_Core2
// #else
// # define RTE_USEC_SystemTimer_Core2(val) ((TickType)RTE_CONST_USEC_SystemTimer_Core2_##val) /* PRQA S 0342 */ /* MD_MSR_Rule20.10_0342 */
// #endif

// #if defined OS_MS2TICKS_SystemTimer_Core2
// # define RTE_MSEC_SystemTimer_Core2 OS_MS2TICKS_SystemTimer_Core2
// #else
// # define RTE_MSEC_SystemTimer_Core2(val) ((TickType)RTE_CONST_MSEC_SystemTimer_Core2_##val) /* PRQA S 0342 */ /* MD_MSR_Rule20.10_0342 */
// #endif

// #if defined OS_SEC2TICKS_SystemTimer_Core2
// # define RTE_SEC_SystemTimer_Core2 OS_SEC2TICKS_SystemTimer_Core2
// #else
// # define RTE_SEC_SystemTimer_Core2(val)  ((TickType)RTE_CONST_SEC_SystemTimer_Core2_##val) /* PRQA S 0342 */ /* MD_MSR_Rule20.10_0342 */
// #endif

// #if defined OS_US2TICKS_SystemTimer_Core3
// # define RTE_USEC_SystemTimer_Core3 OS_US2TICKS_SystemTimer_Core3
// #else
// # define RTE_USEC_SystemTimer_Core3(val) ((TickType)RTE_CONST_USEC_SystemTimer_Core3_##val) /* PRQA S 0342 */ /* MD_MSR_Rule20.10_0342 */
// #endif

// #if defined OS_MS2TICKS_SystemTimer_Core3
// # define RTE_MSEC_SystemTimer_Core3 OS_MS2TICKS_SystemTimer_Core3
// #else
// # define RTE_MSEC_SystemTimer_Core3(val) ((TickType)RTE_CONST_MSEC_SystemTimer_Core3_##val) /* PRQA S 0342 */ /* MD_MSR_Rule20.10_0342 */
// #endif

// #if defined OS_SEC2TICKS_SystemTimer_Core3
// # define RTE_SEC_SystemTimer_Core3 OS_SEC2TICKS_SystemTimer_Core3
// #else
// # define RTE_SEC_SystemTimer_Core3(val)  ((TickType)RTE_CONST_SEC_SystemTimer_Core3_##val) /* PRQA S 0342 */ /* MD_MSR_Rule20.10_0342 */
// #endif

// #if defined OS_US2TICKS_SystemTimer_Core4
// # define RTE_USEC_SystemTimer_Core4 OS_US2TICKS_SystemTimer_Core4
// #else
// # define RTE_USEC_SystemTimer_Core4(val) ((TickType)RTE_CONST_USEC_SystemTimer_Core4_##val) /* PRQA S 0342 */ /* MD_MSR_Rule20.10_0342 */
// #endif

// #if defined OS_MS2TICKS_SystemTimer_Core4
// # define RTE_MSEC_SystemTimer_Core4 OS_MS2TICKS_SystemTimer_Core4
// #else
// # define RTE_MSEC_SystemTimer_Core4(val) ((TickType)RTE_CONST_MSEC_SystemTimer_Core4_##val) /* PRQA S 0342 */ /* MD_MSR_Rule20.10_0342 */
// #endif

// #if defined OS_SEC2TICKS_SystemTimer_Core4
// # define RTE_SEC_SystemTimer_Core4 OS_SEC2TICKS_SystemTimer_Core4
// #else
// # define RTE_SEC_SystemTimer_Core4(val)  ((TickType)RTE_CONST_SEC_SystemTimer_Core4_##val) /* PRQA S 0342 */ /* MD_MSR_Rule20.10_0342 */
// #endif

// #if defined OS_US2TICKS_SystemTimer_Core5
// # define RTE_USEC_SystemTimer_Core5 OS_US2TICKS_SystemTimer_Core5
// #else
// # define RTE_USEC_SystemTimer_Core5(val) ((TickType)RTE_CONST_USEC_SystemTimer_Core5_##val) /* PRQA S 0342 */ /* MD_MSR_Rule20.10_0342 */
// #endif

// #if defined OS_MS2TICKS_SystemTimer_Core5
// # define RTE_MSEC_SystemTimer_Core5 OS_MS2TICKS_SystemTimer_Core5
// #else
// # define RTE_MSEC_SystemTimer_Core5(val) ((TickType)RTE_CONST_MSEC_SystemTimer_Core5_##val) /* PRQA S 0342 */ /* MD_MSR_Rule20.10_0342 */
// #endif

// #if defined OS_SEC2TICKS_SystemTimer_Core5
// # define RTE_SEC_SystemTimer_Core5 OS_SEC2TICKS_SystemTimer_Core5
// #else
// # define RTE_SEC_SystemTimer_Core5(val)  ((TickType)RTE_CONST_SEC_SystemTimer_Core5_##val) /* PRQA S 0342 */ /* MD_MSR_Rule20.10_0342 */
// #endif

// #define RTE_CONST_MSEC_SystemTimer_Core0_0 (0UL)
// #define RTE_CONST_MSEC_SystemTimer_Core1_0 (0UL)
// #define RTE_CONST_MSEC_SystemTimer_Core2_0 (0UL)
// #define RTE_CONST_MSEC_SystemTimer_Core3_0 (0UL)
// #define RTE_CONST_MSEC_SystemTimer_Core4_0 (0UL)
// #define RTE_CONST_MSEC_SystemTimer_Core5_0 (0UL)
// #define RTE_CONST_MSEC_SystemTimer_Core0_10 (1000000UL)
// #define RTE_CONST_MSEC_SystemTimer_Core1_10 (1000000UL)
// #define RTE_CONST_MSEC_SystemTimer_Core2_10 (1000000UL)
// #define RTE_CONST_MSEC_SystemTimer_Core3_10 (1000000UL)
// #define RTE_CONST_MSEC_SystemTimer_Core4_10 (1000000UL)
// #define RTE_CONST_MSEC_SystemTimer_Core5_10 (1000000UL)
// #define RTE_CONST_MSEC_SystemTimer_Core0_20 (2000000UL)
// #define RTE_CONST_MSEC_SystemTimer_Core0_5 (500000UL)
// #define RTE_CONST_MSEC_SystemTimer_Core1_5 (500000UL)
// #define RTE_CONST_MSEC_SystemTimer_Core0_60 (6000000UL)


/**********************************************************************************************************************
 * Internal definitions
 *********************************************************************************************************************/

#define RTE_TASK_TIMEOUT_EVENT_MASK   ((EventMaskType)0x01)
#define RTE_TASK_WAITPOINT_EVENT_MASK ((EventMaskType)0x02)

/**********************************************************************************************************************
 * RTE life cycle API
 *********************************************************************************************************************/

#define RTE_START_SEC_CODE
#include "Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

FUNC(void, RTE_CODE) Rte_MemCpy(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) destination, P2CONST(void, AUTOMATIC, RTE_APPL_DATA) source, uint32_least num) /* PRQA S 3408, 1505 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
{
  P2CONST(uint8, AUTOMATIC, RTE_APPL_DATA) src = (P2CONST(uint8, AUTOMATIC, RTE_APPL_DATA)) source; /* PRQA S 0316 */ /* MD_Rte_0316 */
  P2VAR(uint8, AUTOMATIC, RTE_APPL_VAR) dst = (P2VAR(uint8, AUTOMATIC, RTE_APPL_VAR)) destination; /* PRQA S 0316 */ /* MD_Rte_0316 */
  uint32_least i;
  for (i = 0; i < num; i++)
  {
    dst[i] = src[i];
  }
}

// void Rte_MemCpy()

// #define RTE_MEMCPY32ALIGN (sizeof(uint32) - 1U)

// FUNC(void, RTE_CODE) Rte_MemCpy32(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) destination, P2CONST(void, AUTOMATIC, RTE_APPL_DATA) source, uint32_least num)
// {
//   P2CONST(uint32, AUTOMATIC, RTE_APPL_DATA) asrc = (P2CONST(uint32, AUTOMATIC, RTE_APPL_DATA)) source; /* PRQA S 0316 */ /* MD_Rte_0316 */
//   P2VAR(uint32, AUTOMATIC, RTE_APPL_VAR) adst = (P2VAR(uint32, AUTOMATIC, RTE_APPL_VAR)) destination; /* PRQA S 0316 */ /* MD_Rte_0316 */
//   P2CONST(uint8, AUTOMATIC, RTE_APPL_DATA) src = (P2CONST(uint8, AUTOMATIC, RTE_APPL_DATA)) source; /* PRQA S 0316 */ /* MD_Rte_0316 */
//   P2VAR(uint8, AUTOMATIC, RTE_APPL_VAR) dst = (P2VAR(uint8, AUTOMATIC, RTE_APPL_VAR)) destination; /* PRQA S 0316 */ /* MD_Rte_0316 */
//   uint32_least i = 0;

//   if (num >= 16U)
//   {
//     if (((((uint32)src) & RTE_MEMCPY32ALIGN) == 0U) && ((((uint32)dst) & RTE_MEMCPY32ALIGN) == 0U)) /* PRQA S 0306 */ /* MD_Rte_0306 */
//     {
//       uint32_least asize = num / sizeof(uint32);
//       uint32_least rem = num & RTE_MEMCPY32ALIGN;
//       for (i = 0; i < (asize - 3U); i += 4U)
//       {
//         adst[i] = asrc[i];
//         adst[i+1U] = asrc[i+1U];
//         adst[i+2U] = asrc[i+2U];
//         adst[i+3U] = asrc[i+3U];
//       }

//       while (i < asize)
//       {
//         adst[i] = asrc[i];
//         ++i;
//       }
//       i = num - rem;
//     }
//     else
//     {
//       for (i = 0; (i + 15U) < num; i += 16U)
//       {
//         dst[i] = src[i];
//         dst[i+1U] = src[i+1U];
//         dst[i+2U] = src[i+2U];
//         dst[i+3U] = src[i+3U];
//         dst[i+4U] = src[i+4U];
//         dst[i+5U] = src[i+5U];
//         dst[i+6U] = src[i+6U];
//         dst[i+7U] = src[i+7U];
//         dst[i+8U] = src[i+8U];
//         dst[i+9U] = src[i+9U];
//         dst[i+10U] = src[i+10U];
//         dst[i+11U] = src[i+11U];
//         dst[i+12U] = src[i+12U];
//         dst[i+13U] = src[i+13U];
//         dst[i+14U] = src[i+14U];
//         dst[i+15U] = src[i+15U];
//       }
//     }

//   }
//   while (i < num)
//   {
//     dst[i] = src[i];
//     ++i;
//   }
// }

// FUNC(void, RTE_CODE) Rte_MemClr(P2VAR(void, AUTOMATIC, RTE_VAR_NOINIT) ptr, uint32_least num)
// {
//   P2VAR(uint8, AUTOMATIC, RTE_VAR_NOINIT) dst = (P2VAR(uint8, AUTOMATIC, RTE_VAR_NOINIT))ptr; /* PRQA S 0316 */ /* MD_Rte_0316 */
//   uint32_least i;
//   for (i = 0; i < num; i++)
//   {
//     dst[i] = 0;
//   }
// }

// FUNC(void, RTE_CODE) SchM_Init(void)
// {
//   uint32 id = GetCoreID();
//   if (id == OS_CORE_ID_0) /* PRQA S 1843 */ /* MD_Rte_Os */
//   {
//     /* activate the tasks */
//     (void)ActivateTask(MainTask_Core0_10ms); /* PRQA S 3417 */ /* MD_Rte_Os */
//     (void)ActivateTask(MainTask_Core0_5ms); /* PRQA S 3417 */ /* MD_Rte_Os */
//     (void)ActivateTask(SchMTask_Core0); /* PRQA S 3417 */ /* MD_Rte_Os */

//     /* activate the alarms used for TimingEvents */
//     (void)SetRelAlarm(Rte_Al_TE2_MainTask_Core0_5ms_0_10ms, RTE_MSEC_SystemTimer_Core0(0) + (TickType)1, RTE_MSEC_SystemTimer_Core0(10)); /* PRQA S 3417 */ /* MD_Rte_Os */
//     (void)SetRelAlarm(Rte_Al_TE2_MainTask_Core0_5ms_0_5ms, RTE_MSEC_SystemTimer_Core0(0) + (TickType)1, RTE_MSEC_SystemTimer_Core0(5)); /* PRQA S 3417 */ /* MD_Rte_Os */
//     (void)SetRelAlarm(Rte_Al_TE2_MainTask_Core0_20ms_0_20ms, RTE_MSEC_SystemTimer_Core0(0) + (TickType)1, RTE_MSEC_SystemTimer_Core0(20)); /* PRQA S 3417 */ /* MD_Rte_Os */
//     (void)SetRelAlarm(Rte_Al_TE2_MainTask_Core0_10ms_0_10ms, RTE_MSEC_SystemTimer_Core0(0) + (TickType)1, RTE_MSEC_SystemTimer_Core0(10)); /* PRQA S 3417 */ /* MD_Rte_Os */
//     (void)SetRelAlarm(Rte_Al_TE2_SchMTask_Core0_0_10ms, RTE_MSEC_SystemTimer_Core0(0) + (TickType)1, RTE_MSEC_SystemTimer_Core0(10)); /* PRQA S 3417 */ /* MD_Rte_Os */
//     (void)SetRelAlarm(Rte_Al_TE2_SchMTask_Core0_0_5ms, RTE_MSEC_SystemTimer_Core0(0) + (TickType)1, RTE_MSEC_SystemTimer_Core0(5)); /* PRQA S 3417 */ /* MD_Rte_Os */

//     Rte_InitState = RTE_STATE_SCHM_INIT;
//   }
//   if (id == OS_CORE_ID_1) /* PRQA S 1843 */ /* MD_Rte_Os */
//   {
//     /* activate the alarms used for TimingEvents */
//     (void)SetRelAlarm(Rte_Al_TE2_SchMTask_Core1_0_10ms, RTE_MSEC_SystemTimer_Core1(0) + (TickType)1, RTE_MSEC_SystemTimer_Core1(10)); /* PRQA S 3417 */ /* MD_Rte_Os */

//     Rte_InitState_1 = RTE_STATE_SCHM_INIT;
//   }
//   if (id == OS_CORE_ID_2) /* PRQA S 1843 */ /* MD_Rte_Os */
//   {
//     /* activate the alarms used for TimingEvents */
//     (void)SetRelAlarm(Rte_Al_TE2_SchMTask_Core2_0_10ms, RTE_MSEC_SystemTimer_Core2(0) + (TickType)1, RTE_MSEC_SystemTimer_Core2(10)); /* PRQA S 3417 */ /* MD_Rte_Os */

//     Rte_InitState_2 = RTE_STATE_SCHM_INIT;
//   }
//   if (id == OS_CORE_ID_3) /* PRQA S 1843 */ /* MD_Rte_Os */
//   {
//     /* activate the alarms used for TimingEvents */
//     (void)SetRelAlarm(Rte_Al_TE2_SchMTask_Core3_0_10ms, RTE_MSEC_SystemTimer_Core3(0) + (TickType)1, RTE_MSEC_SystemTimer_Core3(10)); /* PRQA S 3417 */ /* MD_Rte_Os */

//     Rte_InitState_3 = RTE_STATE_SCHM_INIT;
//   }
//   if (id == OS_CORE_ID_4) /* PRQA S 1843 */ /* MD_Rte_Os */
//   {
//     /* activate the alarms used for TimingEvents */
//     (void)SetRelAlarm(Rte_Al_TE5_EcuM_EcuM_MainFunction, RTE_MSEC_SystemTimer_Core4(0) + (TickType)1, RTE_MSEC_SystemTimer_Core4(10)); /* PRQA S 3417 */ /* MD_Rte_Os */

//     Rte_InitState_4 = RTE_STATE_SCHM_INIT;
//   }
//   if (id == OS_CORE_ID_5) /* PRQA S 1843 */ /* MD_Rte_Os */
//   {
//     /* activate the alarms used for TimingEvents */
//     (void)SetRelAlarm(Rte_Al_TE6_EcuM_EcuM_MainFunction, RTE_MSEC_SystemTimer_Core5(0) + (TickType)1, RTE_MSEC_SystemTimer_Core5(10)); /* PRQA S 3417 */ /* MD_Rte_Os */

//     Rte_InitState_5 = RTE_STATE_SCHM_INIT;
//   }
// }

// FUNC(Std_ReturnType, RTE_CODE) Rte_Start(void)
// {
//   uint32 id = GetCoreID();
//   if (id == OS_CORE_ID_0) /* PRQA S 1843 */ /* MD_Rte_Os */
//   {
//     /* C/S queue initialization */
//     Rte_CS_ClientQueue_CpVehCtrl_GlobalTime_Slave_StbMSynchronizedTimeBase_EthTsyn_GetCurrentTime.Rte_CallCompleted = TRUE;

//     /* activate the tasks */
//     (void)ActivateTask(OsTask_Init_Core0_APP); /* PRQA S 3417 */ /* MD_Rte_Os */

//     /* activate the alarms used for TimingEvents */
//     (void)SetRelAlarm(Rte_Al_TE_CpCdd_Cdd_5ms, RTE_MSEC_SystemTimer_Core0(0) + (TickType)1, RTE_MSEC_SystemTimer_Core0(5)); /* PRQA S 3417, 1840 */ /* MD_Rte_Os, MD_Rte_Os */
//     (void)SetRelAlarm(Rte_Al_TE_CpApMyApp_Core5_Runnable_10ms_Core5, RTE_MSEC_SystemTimer_Core5(0) + (TickType)1, RTE_MSEC_SystemTimer_Core5(10)); /* PRQA S 3417, 1840 */ /* MD_Rte_Os, MD_Rte_Os */
//     (void)SetRelAlarm(Rte_Al_TE_CpApMyApp_Core4_Runnable_10ms_Core4, RTE_MSEC_SystemTimer_Core4(0) + (TickType)1, RTE_MSEC_SystemTimer_Core4(10)); /* PRQA S 3417, 1840 */ /* MD_Rte_Os, MD_Rte_Os */
//     (void)SetRelAlarm(Rte_Al_TE_CpApMyApp_Core3_Runnable_10ms_Core3, RTE_MSEC_SystemTimer_Core3(0) + (TickType)1, RTE_MSEC_SystemTimer_Core3(10)); /* PRQA S 3417, 1840 */ /* MD_Rte_Os, MD_Rte_Os */
//     (void)SetRelAlarm(Rte_Al_TE_CpApMyApp_Core2_Runnable_10ms_Core2, RTE_MSEC_SystemTimer_Core2(0) + (TickType)1, RTE_MSEC_SystemTimer_Core2(10)); /* PRQA S 3417, 1840 */ /* MD_Rte_Os, MD_Rte_Os */
//     (void)SetRelAlarm(Rte_Al_TE_CpVehCtrl_VehCtrl_5ms, RTE_MSEC_SystemTimer_Core1(0) + (TickType)1, RTE_MSEC_SystemTimer_Core1(5)); /* PRQA S 3417, 1840 */ /* MD_Rte_Os, MD_Rte_Os */
//     (void)SetRelAlarm(Rte_Al_TE_MainTask_Core1_10ms_0_10ms, RTE_MSEC_SystemTimer_Core1(0) + (TickType)1, RTE_MSEC_SystemTimer_Core1(10)); /* PRQA S 3417, 1840 */ /* MD_Rte_Os, MD_Rte_Os */
//     (void)SetRelAlarm(Rte_Al_TE_MainTask_Core0_10ms_0_10ms, RTE_MSEC_SystemTimer_Core0(0) + (TickType)1, RTE_MSEC_SystemTimer_Core0(10)); /* PRQA S 3417, 1840 */ /* MD_Rte_Os, MD_Rte_Os */
//     (void)SetRelAlarm(Rte_Al_TE_MainTask_Core0_60ms_0_60ms, RTE_MSEC_SystemTimer_Core0(0) + (TickType)1, RTE_MSEC_SystemTimer_Core0(60)); /* PRQA S 3417, 1840 */ /* MD_Rte_Os, MD_Rte_Os */

//     Rte_StartTiming_InitState = RTE_STATE_INIT;
//     Rte_InitState = RTE_STATE_INIT;

//   }
//   if (id == OS_CORE_ID_1) /* PRQA S 1843 */ /* MD_Rte_Os */
//   {
//     /* activate the tasks */
//     (void)ActivateTask(MainTask_Core1_10ms); /* PRQA S 3417 */ /* MD_Rte_Os */
//     (void)ActivateTask(OsTask_Init_Core1_APP); /* PRQA S 3417 */ /* MD_Rte_Os */

//     Rte_InitState_1 = RTE_STATE_INIT;

//   }
//   if (id == OS_CORE_ID_2) /* PRQA S 1843 */ /* MD_Rte_Os */
//   {
//     /* activate the tasks */
//     (void)ActivateTask(OsTask_Init_Core2_APP); /* PRQA S 3417 */ /* MD_Rte_Os */

//     Rte_InitState_2 = RTE_STATE_INIT;

//   }
//   if (id == OS_CORE_ID_3) /* PRQA S 1843 */ /* MD_Rte_Os */
//   {
//     /* activate the tasks */
//     (void)ActivateTask(OsTask_Init_Core3_APP); /* PRQA S 3417 */ /* MD_Rte_Os */

//     Rte_InitState_3 = RTE_STATE_INIT;

//   }
//   if (id == OS_CORE_ID_4) /* PRQA S 1843 */ /* MD_Rte_Os */
//   {
//     /* activate the tasks */
//     (void)ActivateTask(OsTask_Init_Core4_APP); /* PRQA S 3417 */ /* MD_Rte_Os */

//     Rte_InitState_4 = RTE_STATE_INIT;

//   }
//   if (id == OS_CORE_ID_5) /* PRQA S 1843 */ /* MD_Rte_Os */
//   {
//     /* activate the tasks */
//     (void)ActivateTask(OsTask_Init_Core5_APP); /* PRQA S 3417 */ /* MD_Rte_Os */

//     Rte_InitState_5 = RTE_STATE_INIT;

//   }
//   return RTE_E_OK;
// } /* PRQA S 6050 */ /* MD_MSR_STCAL */

// FUNC(Std_ReturnType, RTE_CODE) Rte_Stop(void)
// {
//   uint32 id = GetCoreID();
//   if (id == OS_CORE_ID_0) /* PRQA S 1843 */ /* MD_Rte_Os */
//   {
//     Rte_StartTiming_InitState = RTE_STATE_UNINIT;
//     /* deactivate alarms */
//     (void)CancelAlarm(Rte_Al_TE_MainTask_Core0_10ms_0_10ms); /* PRQA S 3417 */ /* MD_Rte_Os */
//     (void)CancelAlarm(Rte_Al_TE_CpCdd_Cdd_5ms); /* PRQA S 3417 */ /* MD_Rte_Os */
//     (void)CancelAlarm(Rte_Al_TE_MainTask_Core0_60ms_0_60ms); /* PRQA S 3417 */ /* MD_Rte_Os */
//     (void)CancelAlarm(Rte_Al_TE_CpVehCtrl_VehCtrl_5ms); /* PRQA S 3417 */ /* MD_Rte_Os */
//     (void)CancelAlarm(Rte_Al_TE_MainTask_Core1_10ms_0_10ms); /* PRQA S 3417 */ /* MD_Rte_Os */
//     (void)CancelAlarm(Rte_Al_TE_CpApMyApp_Core2_Runnable_10ms_Core2); /* PRQA S 3417 */ /* MD_Rte_Os */
//     (void)CancelAlarm(Rte_Al_TE_CpApMyApp_Core3_Runnable_10ms_Core3); /* PRQA S 3417 */ /* MD_Rte_Os */
//     (void)CancelAlarm(Rte_Al_TE_CpApMyApp_Core4_Runnable_10ms_Core4); /* PRQA S 3417 */ /* MD_Rte_Os */
//     (void)CancelAlarm(Rte_Al_TE_CpApMyApp_Core5_Runnable_10ms_Core5); /* PRQA S 3417 */ /* MD_Rte_Os */

//     Rte_InitState = RTE_STATE_SCHM_INIT;

//   }
//   if (id == OS_CORE_ID_1) /* PRQA S 1843 */ /* MD_Rte_Os */
//   {
//     Rte_InitState_1 = RTE_STATE_SCHM_INIT;

//   }
//   if (id == OS_CORE_ID_2) /* PRQA S 1843 */ /* MD_Rte_Os */
//   {
//     Rte_InitState_2 = RTE_STATE_SCHM_INIT;

//   }
//   if (id == OS_CORE_ID_3) /* PRQA S 1843 */ /* MD_Rte_Os */
//   {
//     Rte_InitState_3 = RTE_STATE_SCHM_INIT;

//   }
//   if (id == OS_CORE_ID_4) /* PRQA S 1843 */ /* MD_Rte_Os */
//   {
//     Rte_InitState_4 = RTE_STATE_SCHM_INIT;

//   }
//   if (id == OS_CORE_ID_5) /* PRQA S 1843 */ /* MD_Rte_Os */
//   {
//     Rte_InitState_5 = RTE_STATE_SCHM_INIT;

//   }
//   return RTE_E_OK;
// }

// FUNC(void, RTE_CODE) SchM_Deinit(void)
// {
//   uint32 id = GetCoreID();
//   if (id == OS_CORE_ID_0) /* PRQA S 1843 */ /* MD_Rte_Os */
//   {
//     /* deactivate alarms */
//     (void)CancelAlarm(Rte_Al_TE2_MainTask_Core0_10ms_0_10ms); /* PRQA S 3417 */ /* MD_Rte_Os */
//     (void)CancelAlarm(Rte_Al_TE2_MainTask_Core0_20ms_0_20ms); /* PRQA S 3417 */ /* MD_Rte_Os */
//     (void)CancelAlarm(Rte_Al_TE2_MainTask_Core0_5ms_0_10ms); /* PRQA S 3417 */ /* MD_Rte_Os */
//     (void)CancelAlarm(Rte_Al_TE2_MainTask_Core0_5ms_0_5ms); /* PRQA S 3417 */ /* MD_Rte_Os */
//     (void)CancelAlarm(Rte_Al_TE2_SchMTask_Core0_0_10ms); /* PRQA S 3417 */ /* MD_Rte_Os */
//     (void)CancelAlarm(Rte_Al_TE2_SchMTask_Core0_0_5ms); /* PRQA S 3417 */ /* MD_Rte_Os */

//     Rte_InitState = RTE_STATE_UNINIT;
//   }
//   if (id == OS_CORE_ID_1) /* PRQA S 1843 */ /* MD_Rte_Os */
//   {
//     /* deactivate alarms */
//     (void)CancelAlarm(Rte_Al_TE2_SchMTask_Core1_0_10ms); /* PRQA S 3417 */ /* MD_Rte_Os */

//     Rte_InitState_1 = RTE_STATE_UNINIT;
//   }
//   if (id == OS_CORE_ID_2) /* PRQA S 1843 */ /* MD_Rte_Os */
//   {
//     /* deactivate alarms */
//     (void)CancelAlarm(Rte_Al_TE2_SchMTask_Core2_0_10ms); /* PRQA S 3417 */ /* MD_Rte_Os */

//     Rte_InitState_2 = RTE_STATE_UNINIT;
//   }
//   if (id == OS_CORE_ID_3) /* PRQA S 1843 */ /* MD_Rte_Os */
//   {
//     /* deactivate alarms */
//     (void)CancelAlarm(Rte_Al_TE2_SchMTask_Core3_0_10ms); /* PRQA S 3417 */ /* MD_Rte_Os */

//     Rte_InitState_3 = RTE_STATE_UNINIT;
//   }
//   if (id == OS_CORE_ID_4) /* PRQA S 1843 */ /* MD_Rte_Os */
//   {
//     /* deactivate alarms */
//     (void)CancelAlarm(Rte_Al_TE5_EcuM_EcuM_MainFunction); /* PRQA S 3417 */ /* MD_Rte_Os */

//     Rte_InitState_4 = RTE_STATE_UNINIT;
//   }
//   if (id == OS_CORE_ID_5) /* PRQA S 1843 */ /* MD_Rte_Os */
//   {
//     /* deactivate alarms */
//     (void)CancelAlarm(Rte_Al_TE6_EcuM_EcuM_MainFunction); /* PRQA S 3417 */ /* MD_Rte_Os */

//     Rte_InitState_5 = RTE_STATE_UNINIT;
//   }
// }

// FUNC(void, RTE_CODE) Rte_InitMemory(void)
// {
//   uint32 id;
//   id = GetCoreID();
//   if (id == OS_CORE_ID_0) /* PRQA S 1843 */ /* MD_Rte_Os */
//   {
//     Rte_InitState = RTE_STATE_UNINIT;
//     Rte_StartTiming_InitState = RTE_STATE_UNINIT;
//     Rte_InitMemory_SystemApplication_OsCore0();
//   }
//   else if (id == OS_CORE_ID_1) /* PRQA S 2004, 1843 */ /* MD_MSR_EmptyClause, MD_Rte_Os */
//   {
//     Rte_InitState_1 = RTE_STATE_UNINIT;
//     Rte_InitMemory_SystemApplication_OsCore1();
//   }
//   else if (id == OS_CORE_ID_2) /* PRQA S 2004, 1843 */ /* MD_MSR_EmptyClause, MD_Rte_Os */
//   {
//     Rte_InitState_2 = RTE_STATE_UNINIT;
//     Rte_InitMemory_SystemApplication_OsCore2();
//   }
//   else if (id == OS_CORE_ID_3) /* PRQA S 2004, 1843 */ /* MD_MSR_EmptyClause, MD_Rte_Os */
//   {
//     Rte_InitState_3 = RTE_STATE_UNINIT;
//     Rte_InitMemory_SystemApplication_OsCore3();
//   }
//   else if (id == OS_CORE_ID_4) /* PRQA S 2004, 1843 */ /* MD_MSR_EmptyClause, MD_Rte_Os */
//   {
//     Rte_InitState_4 = RTE_STATE_UNINIT;
//     Rte_InitMemory_SystemApplication_OsCore4();
//   }
//   else if (id == OS_CORE_ID_5) /* PRQA S 2004, 1843 */ /* MD_MSR_EmptyClause, MD_Rte_Os */
//   {
//     Rte_InitState_5 = RTE_STATE_UNINIT;
//     Rte_InitMemory_SystemApplication_OsCore5();
//   }
// }


// /**********************************************************************************************************************
//  * Exclusive area access
//  *********************************************************************************************************************/

// FUNC(void, RTE_CODE) SchM_Enter_Fls_17_Dmu_Erase(void)
// {
//   /* RteAnalyzer(ExclusiveArea, ALL_INTERRUPT_BLOCKING) */
//   SuspendAllInterrupts();
// }

// FUNC(void, RTE_CODE) SchM_Exit_Fls_17_Dmu_Erase(void)
// {
//   /* RteAnalyzer(ExclusiveArea, ALL_INTERRUPT_BLOCKING) */
//   ResumeAllInterrupts();
// }


// FUNC(void, RTE_CODE) SchM_Enter_Fls_17_Dmu_Init(void)
// {
//   /* RteAnalyzer(ExclusiveArea, ALL_INTERRUPT_BLOCKING) */
//   SuspendAllInterrupts();
// }

// FUNC(void, RTE_CODE) SchM_Exit_Fls_17_Dmu_Init(void)
// {
//   /* RteAnalyzer(ExclusiveArea, ALL_INTERRUPT_BLOCKING) */
//   ResumeAllInterrupts();
// }


// FUNC(void, RTE_CODE) SchM_Enter_Fls_17_Dmu_Main(void)
// {
//   /* RteAnalyzer(ExclusiveArea, ALL_INTERRUPT_BLOCKING) */
//   SuspendAllInterrupts();
// }

// FUNC(void, RTE_CODE) SchM_Exit_Fls_17_Dmu_Main(void)
// {
//   /* RteAnalyzer(ExclusiveArea, ALL_INTERRUPT_BLOCKING) */
//   ResumeAllInterrupts();
// }


// FUNC(void, RTE_CODE) SchM_Enter_Fls_17_Dmu_ResumeErase(void)
// {
//   /* RteAnalyzer(ExclusiveArea, ALL_INTERRUPT_BLOCKING) */
//   SuspendAllInterrupts();
// }

// FUNC(void, RTE_CODE) SchM_Exit_Fls_17_Dmu_ResumeErase(void)
// {
//   /* RteAnalyzer(ExclusiveArea, ALL_INTERRUPT_BLOCKING) */
//   ResumeAllInterrupts();
// }


// FUNC(void, RTE_CODE) SchM_Enter_Fls_17_Dmu_UserContentCount(void)
// {
//   /* RteAnalyzer(ExclusiveArea, ALL_INTERRUPT_BLOCKING) */
//   SuspendAllInterrupts();
// }

// FUNC(void, RTE_CODE) SchM_Exit_Fls_17_Dmu_UserContentCount(void)
// {
//   /* RteAnalyzer(ExclusiveArea, ALL_INTERRUPT_BLOCKING) */
//   ResumeAllInterrupts();
// }


// FUNC(void, RTE_CODE) SchM_Enter_Fls_17_Dmu_Write(void)
// {
//   /* RteAnalyzer(ExclusiveArea, ALL_INTERRUPT_BLOCKING) */
//   SuspendAllInterrupts();
// }

// FUNC(void, RTE_CODE) SchM_Exit_Fls_17_Dmu_Write(void)
// {
//   /* RteAnalyzer(ExclusiveArea, ALL_INTERRUPT_BLOCKING) */
//   ResumeAllInterrupts();
// }


// FUNC(void, RTE_CODE) SchM_Enter_Mcu_AtomAgcReg(void)
// {
//   /* RteAnalyzer(ExclusiveArea, ALL_INTERRUPT_BLOCKING) */
//   SuspendAllInterrupts();
// }

// FUNC(void, RTE_CODE) SchM_Exit_Mcu_AtomAgcReg(void)
// {
//   /* RteAnalyzer(ExclusiveArea, ALL_INTERRUPT_BLOCKING) */
//   ResumeAllInterrupts();
// }


// FUNC(void, RTE_CODE) SchM_Enter_Mcu_TomTgcReg(void)
// {
//   /* RteAnalyzer(ExclusiveArea, ALL_INTERRUPT_BLOCKING) */
//   SuspendAllInterrupts();
// }

// FUNC(void, RTE_CODE) SchM_Exit_Mcu_TomTgcReg(void)
// {
//   /* RteAnalyzer(ExclusiveArea, ALL_INTERRUPT_BLOCKING) */
//   ResumeAllInterrupts();
// }


// FUNC(void, RTE_CODE) SchM_Enter_Wdg_17_Scu_ChangeMode(void)
// {
//   /* RteAnalyzer(ExclusiveArea, ALL_INTERRUPT_BLOCKING) */
//   SuspendAllInterrupts();
// }

// FUNC(void, RTE_CODE) SchM_Exit_Wdg_17_Scu_ChangeMode(void)
// {
//   /* RteAnalyzer(ExclusiveArea, ALL_INTERRUPT_BLOCKING) */
//   ResumeAllInterrupts();
// }


// FUNC(void, RTE_CODE) SchM_Enter_Wdg_17_Scu_CpuEndInit(void)
// {
//   /* RteAnalyzer(ExclusiveArea, ALL_INTERRUPT_BLOCKING) */
//   SuspendAllInterrupts();
// }

// FUNC(void, RTE_CODE) SchM_Exit_Wdg_17_Scu_CpuEndInit(void)
// {
//   /* RteAnalyzer(ExclusiveArea, ALL_INTERRUPT_BLOCKING) */
//   ResumeAllInterrupts();
// }


// FUNC(void, RTE_CODE) SchM_Enter_Wdg_17_Scu_TimerHandling(void)
// {
//   /* RteAnalyzer(ExclusiveArea, ALL_INTERRUPT_BLOCKING) */
//   SuspendAllInterrupts();
// }

// FUNC(void, RTE_CODE) SchM_Exit_Wdg_17_Scu_TimerHandling(void)
// {
//   /* RteAnalyzer(ExclusiveArea, ALL_INTERRUPT_BLOCKING) */
//   ResumeAllInterrupts();
// }


// FUNC(void, RTE_CODE) SchM_Enter_Wdg_17_Scu_Trigger(void)
// {
//   /* RteAnalyzer(ExclusiveArea, ALL_INTERRUPT_BLOCKING) */
//   SuspendAllInterrupts();
// }

// FUNC(void, RTE_CODE) SchM_Exit_Wdg_17_Scu_Trigger(void)
// {
//   /* RteAnalyzer(ExclusiveArea, ALL_INTERRUPT_BLOCKING) */
//   ResumeAllInterrupts();
// }



// /**********************************************************************************************************************
//  * RTE Schedulable entity for COM-Access from different partitions
//  *********************************************************************************************************************/
// FUNC(void, RTE_CODE) Rte_ComSendSignalProxyPeriodic(void)
// {
// } /* PRQA S 6010, 6030, 6050 */ /* MD_MSR_STPTH, MD_MSR_STCYC, MD_MSR_STCAL */

// #define RTE_STOP_SEC_CODE
// #include "Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/**********************************************************************************************************************
 MISRA 2012 violations and justifications
 *********************************************************************************************************************/

/* module specific MISRA deviations:
   MD_Rte_0306:  MISRA rule: Rule11.4
     Reason:     An optimized copy algorithm can be used for aligned data. To check if pointers are aligned, pointers need to be casted to an integer type.
     Risk:       No functional risk. Only the lower 8 bits of the address are checked, therefore all integer types are sufficient.
     Prevention: Not required.

   MD_Rte_0316:  MISRA rule: Dir1.1
     Reason:     Pointer cast to uint8* because a direct byte access is necessary.
     Risk:       No functional risk. Only a cast to uint8* is performed.
     Prevention: Not required.

   MD_Rte_1514:  MISRA rule: Rule8.9
     Reason:     Because of external definition, misra does not see the call.
     Risk:       No functional risk. There is no side effect.
     Prevention: Not required.

   MD_Rte_1533:  MISRA rule: Rule8.9
     Reason:     Object is referenced by more than one function in different configurations.
     Risk:       No functional risk.
     Prevention: Not required.

   MD_Rte_3408:  MISRA rule: Rule8.4
     Reason:     For the purpose of monitoring during calibration or debugging it is necessary to use non-static declarations.
                 This is covered in the MISRA C compliance section of the Rte specification.
     Risk:       No functional risk.
     Prevention: Not required.

   MD_Rte_Os:
     Reason:     This justification is used as summary justification for all deviations caused by the MICROSAR OS
                 which is for testing of the RTE. Those deviations are no issues in the RTE code.
     Risk:       No functional risk.
     Prevention: Not required.

*/
