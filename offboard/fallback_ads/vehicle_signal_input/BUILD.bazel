package(default_visibility = ["//visibility:public"])

cc_library(
    name = "vehicle_signal_input_main",
    srcs = [
        "vehicle_signal_input_main.c",
    ],
    hdrs = [
        "vehicle_signal_input_main.h",
    ],
    copts = [
        "-x",
        "c",
        "-std=c11",
        "-Wno-missing-field-initializers",
    ],
    include_prefix = "vehicle_signal_input/",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_vehicle_signal_input",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_vehicle_signal_input_type",
        "//offboard/fallback_ads/vehicle_signal_input:vehicle_signal_input_parameter",
        "//offboard/fallback_ads/vehicle_signal_input:vehicle_signal_input_type",
        "//offboard/fallback_ads/vehicle_signal_input/can_signal_decode",
        "//offboard/fallback_ads/vehicle_signal_input/can_timeout_detection",
    ],
)

cc_library(
    name = "vehicle_signal_input_parameter",
    srcs = [
        "vehicle_signal_input_parameter.c",
    ],
    hdrs = [
        "vehicle_signal_input_parameter.h",
    ],
    copts = [
        "-x",
        "c",
        "-std=c11",
    ],
    include_prefix = "vehicle_signal_input/",
)

cc_library(
    name = "vehicle_signal_input_type",
    hdrs = [
        "vehicle_signal_input_type.h",
    ],
    copts = [
        "-x",
        "c",
        "-std=c11",
    ],
    include_prefix = "vehicle_signal_input/",
)
