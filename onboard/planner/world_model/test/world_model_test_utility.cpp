#include "planner/world_model/test/world_model_test_utility.h"

#include <limits>
#include <map>
#include <memory>
#include <optional>
#include <utility>

#include "av_comm/calib_config.h"
#include "av_comm/car_id.h"
#include "base/base_dir.h"
#include "geometry/model/polyline_curve.h"
#include "planner/utility/config_center/planner_config_center.h"
#include "planner/utility/seed/planning_seed.h"
#include "planner/world_model/current_lane_associator/current_lane_associator.h"
#include "planner/world_model/drivable_lane_reasoner/drivable_lane_reasoner.h"
#include "planner/world_model/lane_blockage/lane_blockage_detector.h"
#include "planner/world_model/regional_map/regional_map_generator.h"
#include "planner/world_model/regional_map/regional_map_utility.h"
#include "planner/world_model/regional_map/regional_path_generator.h"
#include "planner/world_model/regional_map/regional_path_utility.h"
#include "planner/world_model/regional_map/stuck_avoidance/stuck_avoidance_param.h"
#include "planner/world_model/regional_sections_locator/regional_sections_locator.h"
#include "planner/world_model/replan_locator/test/replan_locator_test_utility.h"
#include "planner/world_model/traffic_participant/test/traffic_participant_test_utility.h"
#include "planner_protos/behavior_reasoner.pb.h"
#include "planner_protos/behavior_reasoner_config.pb.h"
#include "planner_protos/behavior_reasoner_seed.pb.h"
#include "pnc_map_service/pnc_map_service.h"
#include "pnc_map_service/test/test_util.h"
#include "routing/engine/edge_filter_manager.h"
#include "routing_protos/route_solution.pb.h"

namespace planner {

namespace {
// The regional map range, in meters.
constexpr double kRegionalMapRangeInMeter = 1000.0;
// Trajectory horizon for agents.
constexpr double kTestTrajectoryTimeHorizonInSec = 10.0;
// Test trajectory duration(ms) for agents.
constexpr int64_t kTestTrajectoryDuration =
    math::Sec2Ms(kTestTrajectoryTimeHorizonInSec) +
    constants::kTrajectoryPoseResolutionInMSec;

pb::RouteEvent GetDefaultRoutEvent() {
  pb::RouteEvent route_event;
  route_event.set_event_type(pb::RouteEventType::kOnTrip);
  route_event.set_mission_id(1);
  route_event.set_route_timestamp(1);
  route_event.set_proposing_reason(routing::pb::ProposingReason::kNewRoute);
  return route_event;
}

// TODO(speed): move this to a shared util file.
const glm::dmat4& GetSensorToCar() {
  // The matrix is got by real velodyne64 extrinsic.
  static const glm::dmat4 sensor_to_car = glm::dmat4{
      0.999277,    -0.0376242, 0.********,  0,          0.0376424, 0.999286,
      -0.********, 0,          -0.********, 0.********, 0.99998,   0,
      -0.35,       -0.05,      1.97,        1};
  return sensor_to_car;
}

// Gets the test calibration config path.
std::string TestCalibPath() {
  return base::GetBaseDirPath(base::BaseDir::kDataDir) +
         "/localization/test_data/calibration.yaml";
}

// Sets the calib config instance.
void SetCalibConfigInstance() {
  YAML::Node node = YAML::LoadFile(TestCalibPath());
  auto calib_config = std::make_unique<calibration::CalibConfig>(
      node, av_comm::CarGeneration::GEN3);
  calibration::CalibConfig::SetInstance(calib_config.get());
}
}  // namespace

std::unique_ptr<pnc_map::MapTestUtil> InitAndUpdateMapTest(double x, double y) {
  pnc_map::MapTestUtil map_test_util;
  // Init PNC Map & update location.
  map_test_util.InitPncMapService(x, y, /*z=*/0);

  // Return test map pointer.
  return std::make_unique<pnc_map::MapTestUtil>(std::move(map_test_util));
}

RobotStateSnapshot ConstructRobotStateSnapshot(double x, double y) {
  // Construct snapshot of robot state.
  const int64_t kPoseStartTime = 0;
  const voy::Pose current_pose = ConstructCurrentPose(kPoseStartTime, x, y);
  const vehicle_model::CarModelWithAxleRectangularShape car_model_with_shape(
      vehicle_model::GetCarMotionModelParamByType(av_comm::CarType::kTest),
      vehicle_model::GetEgoAxleRectangularMeasurementByType(
          av_comm::CarType::kTest),
      /*method=*/math::IntegrationMethod::RK_2);
  // Generate pose snapshot of robot state.
  return RobotStateSnapshot(current_pose,
                            replan_locator_test_constants::kSteeringAngle,
                            car_model_with_shape);
}

std::unique_ptr<RobotState> ConstructRobotState(double x, double y) {
  // Construct snapshot of robot state.
  std::unique_ptr<RobotState> robot_state_ptr = std::make_unique<RobotState>(
      PlannerConfigCenter::GetInstance()
          .world_model_config()
          .replan_locator_config(),
      av_comm::CarType::kTest, av_comm::CarRegion::kChina);
  voy::Pose pose;
  pose.set_x(x);
  pose.set_y(y);
  const std::shared_ptr<const voy::Canbus> canbus_ptr =
      std::make_shared<const voy::Canbus>();
  robot_state_ptr->UpdatePoseCanbus(pose, canbus_ptr,
                                    /*replan_request=*/nullptr,
                                    /*replan_locator_debug=*/nullptr);
  return robot_state_ptr;
}

RegionalPath GetRegionalPath(
    const pnc_map::PncMapService* pnc_map_service,
    const routing::pb::RouteSolution& route_solution, const voy::Pose& pose,
    const std::shared_ptr<routing::EdgeFilterManager>& edge_filter_manager,
    const DrivableLaneReasoner& drivable_lane_reasoner, double robot_max_speed,
    double regional_path_range) {
  RegionalSectionsLocator regional_sections_locator(
      pnc_map_service, &drivable_lane_reasoner, robot_max_speed,
      regional_path_range);
  const RobotStateSnapshot robot_state_snapshot =
      ConstructRobotStateSnapshot(pose.x(), pose.y());
  pnc_map::JointPncMapService joint_pnc_map_service(pnc_map_service);
  regional_sections_locator.Update(
      joint_pnc_map_service, pose, /*waypoint_count=*/0,
      /*is_in_autonomous_mode*/ true,
      /*is_waypoint_assist_invoked=*/false,
      /*was_reversing=*/false,
      /*last_selected_lane_sequence=*/{},
      /*last_selected_lane_sequence_candidate=*/nullptr, &route_solution,
      /*regional_sections_locator_debug=*/nullptr);
  const pb::RouteEvent route_event = GetDefaultRoutEvent();

  // Construct route_model_param.
  const RouteModel route_model(pnc_map_service, edge_filter_manager);
  auto route_status_ptr = std::make_shared<routing::pb::RouteStatus>();
  *route_status_ptr->mutable_proposed_route_solution() = route_solution;
  const GlobalRouteSolution global_route_solution(*pnc_map_service,
                                                  route_status_ptr,
                                                  /*waypoint_count=*/0,
                                                  /*arrived_section_ids=*/{});
  const RouteModelParam route_model_param =
      RouteModelParam(route_model.cost_generator(), global_route_solution,
                      route_model.order_info());

  CurrentLaneAssociator current_lane_associator(&drivable_lane_reasoner);
  pb::PullOutJumpOutSeed pull_out_jump_out_seed = pb::PullOutJumpOutSeed();
  const lane_selection::LaneSequenceProposalInfo lane_sequence_proposal_info;
  current_lane_associator.Update(
      robot_state_snapshot, pose, joint_pnc_map_service,
      *route_model_param.cost_generator,
      regional_sections_locator.locator_selected_near_lanes(),
      regional_sections_locator.potential_sections(),
      regional_sections_locator.extend_potential_sections(),
      regional_sections_locator.regional_section_vertices(),
      *route_model_param.global_route_solution,
      /*last_cycle_section_sequence=*/{},
      /*last_selected_lane_sequence=*/
      std::vector<const pnc_map::Lane*>(),
      /*last_selected_lane_sequence_candidate=*/nullptr,
      /*last_lane_to_junction_info_map=*/{},
      /*is_new_trip=*/false,
      /*should_generate_pull_out_jump_out_sequence=*/false,
      /*is_waypoint_assist_invoked=*/false,
      /*is_in_autonomous_mode=*/false,
      lane_selection::StuckAvoidanceParameter(), pb::LaneBlockageDetectorSeed(),
      lane_sequence_proposal_info, pb::DecoupledManeuverSeed(),
      /*has_ego_reached_target_lane_sequence=*/
      pb::EgoReachTargetLaneSequenceState::NOT_DEFINED,
      &pull_out_jump_out_seed);

  pb::LaneGraphSearchInfo lane_graph_search_info;
  for (const pnc_map::Lane* lane :
       current_lane_associator.current_lanes_for_regional_path()) {
    lane_graph_search_info.add_current_lanes(lane->id());
  }

  [[maybe_unused]] const std::vector<lane_candidate::CurrentLaneCandidate>&
      current_drivable_lanes_for_regional_path =
          current_lane_associator.current_lane_candidates_for_regional_path();
  const routing::pb::RouteQuery route_query = lane_selection::GetRouteQuery(
      {robot_state_snapshot.x(), robot_state_snapshot.y()},
      robot_state_snapshot.timestamp(),
      current_lane_associator.current_lanes_for_regional_path(),
      global_route_solution,
      /*constraint_name=*/"",
      /*predefined_road_ids=*/{},
      /*match_type=*/order::pb::MatchType::UNKNOWN_MATCH,
      route_model.cost_generator()
          .cost_engine_config()
          .cost_engine_config_info()
          .speed_for_path_point_meters_per_sec(),
      routing::cost_engine::cost_utils::GetFeatureCostEstimateCost(
          route_model.cost_generator()
              .all_feature_cost_estimates()
              .unreachable_lane_cost_estimates,
          route_model.cost_generator().use_zero_cost()),
      routing::cost_engine::cost_utils::GetFeatureCostEstimateCost(
          route_model.cost_generator()
              .all_feature_cost_estimates()
              .non_last_selected_current_lane_cost_estimates,
          route_model.cost_generator().use_zero_cost()),
      /*last_selected_current_lane=*/nullptr,
      /*followed_path_type=*/routing::pb::QueryFollowedPathType::NONE_REPLAN,
      route_model.coverage_areas());

  WaypointLanesCache waypoint_lanes_cache(
      pnc_map_service->zone_lane_occupancy_map(), edge_filter_manager,
      drivable_lane_reasoner,
      /*cache_capacity=*/600);
  const std::vector<const pnc_map::Section*>
      destination_successor_sections_for_pullover;
  const double pullover_final_extend_section_percentage = 1.0;
  RegionalPathGenerator regional_path_generator(&waypoint_lanes_cache);
  const std::optional<RegionalPath> regional_path =
      regional_path_generator.Generate(
          joint_pnc_map_service,
          EgoPosition(
              robot_state_snapshot.x(), robot_state_snapshot.y(),
              robot_state_snapshot.heading(),
              current_lane_associator
                  .current_lane_candidates_for_regional_path(),
              current_lane_associator.GetDrivablePhysicalLanes(),
              current_lane_associator.GetBackupLanes(joint_pnc_map_service),
              regional_sections_locator.potential_sections()),
          robot_state_snapshot, route_model_param,
          RegionalPathQueryParam(
              route_query, lane_graph_search_info,
              regional_sections_locator.regional_section_vertices(),
              LaneBlockageDetector(PlannerConfigCenter::GetInstance()),
              pb::ManeuverType::LANE_FOLLOW, pb::LaneSequencePlanInitState(),
              /*is_current_query_use_prior_map=*/false,
              lane_selection::StuckAvoidanceParameter(),
              pb::ExtraPlannerSignals::LANE_KEEP, drivable_lane_reasoner,
              /*section_preference_param=*/
              lane_selection::SectionPreferenceParam(),
              /*forbidden_fork_sections=*/{}, pb::LaneSequenceCandidates(),
              /*lane_object_info_map=*/LaneObjectInfoMap(),
              /*lane_to_junction_info_map=*/nullptr,
              /*is_pulling_out_in=*/false,
              destination_successor_sections_for_pullover,
              pullover_final_extend_section_percentage,
              /*has_any_potential_stuck_scenario_for_rp=*/false),
          /*last_selected_lane_sequence=*/{},
          /*last_cycle_section_sequence=*/{}, pb::RouteEvent(),
          pb::RegionalMapConfig(), /*last_cycle_lane_sequence=*/{},
          /*last_cycle_turn_info=*/{},
          /*lane_sequence_proposal_info=*/{},
          /*regional_map_generator_seed=*/pb::RegionalMapGeneratorSeed(),
          /*is_in_autonomous_mode=*/true,
          /*last_cycle_regional_path_update_failed=*/false,
          /*light_regional_path_candidates=*/nullptr,
          /*regional_map_debug=*/nullptr);
  return regional_path ? *regional_path : RegionalPath();
}

RegionalMap GetRegionalMap(
    const pnc_map::PncMapService* pnc_map_service,
    const routing::pb::RouteSolution& route_solution, const voy::Pose& pose,
    const std::shared_ptr<routing::EdgeFilterManager>& edge_filter_manager,
    const DrivableLaneReasoner& drivable_lane_reasoner, double robot_max_speed,
    double regional_path_range) {
  const std::unique_ptr<RobotState> robot_state_ptr =
      ConstructRobotState(pose.x(), pose.y());
  RegionalSectionsLocator regional_sections_locator(
      pnc_map_service, &drivable_lane_reasoner, robot_max_speed,
      regional_path_range);
  const RobotStateSnapshot& robot_state_snapshot =
      robot_state_ptr->current_state_snapshot();
  pnc_map::JointPncMapService joint_pnc_map_service(pnc_map_service);
  regional_sections_locator.Update(
      joint_pnc_map_service, pose, /*waypoint_count=*/0,
      /*is_in_autonomous_mode*/ true,
      /*is_waypoint_assist_invoked=*/false,
      /*was_reversing=*/false,
      /*last_selected_lane_sequence=*/{},
      /*last_selected_lane_sequence_candidate=*/nullptr, &route_solution,
      /*regional_sections_locator_debug=*/nullptr);
  const pb::RouteEvent route_event = GetDefaultRoutEvent();

  // Construct route_model_param.
  const RouteModel route_model(pnc_map_service, edge_filter_manager);
  auto route_status_ptr = std::make_shared<routing::pb::RouteStatus>();
  *route_status_ptr->mutable_proposed_route_solution() = route_solution;
  const GlobalRouteSolution global_route_solution(*pnc_map_service,
                                                  route_status_ptr,
                                                  /*waypoint_count=*/0,
                                                  /*arrived_section_ids=*/{});
  const RouteModelParam route_model_param =
      RouteModelParam(route_model.cost_generator(), global_route_solution,
                      route_model.order_info());

  CurrentLaneAssociator current_lane_associator(&drivable_lane_reasoner);
  pb::PullOutJumpOutSeed pull_out_jump_out_seed = pb::PullOutJumpOutSeed();
  const lane_selection::LaneSequenceProposalInfo lane_sequence_proposal_info;
  current_lane_associator.Update(
      robot_state_snapshot, pose, joint_pnc_map_service,
      *route_model_param.cost_generator,
      regional_sections_locator.locator_selected_near_lanes(),
      regional_sections_locator.potential_sections(),
      regional_sections_locator.extend_potential_sections(),
      regional_sections_locator.regional_section_vertices(),
      *route_model_param.global_route_solution,
      /*last_cycle_section_sequence=*/{},
      /*last_selected_lane_sequence=*/
      std::vector<const pnc_map::Lane*>(),
      /*last_selected_lane_sequence_candidate=*/nullptr,
      /*last_lane_to_junction_info_map=*/{},
      /*is_new_trip=*/false,
      /*should_generate_pull_out_jump_out_sequence=*/false,
      /*is_waypoint_assist_invoked=*/false,
      /*is_in_autonomous_mode=*/false,
      lane_selection::StuckAvoidanceParameter(), pb::LaneBlockageDetectorSeed(),
      lane_sequence_proposal_info, pb::DecoupledManeuverSeed(),
      /*has_ego_reached_target_lane_sequence=*/
      pb::EgoReachTargetLaneSequenceState::NOT_DEFINED,
      &pull_out_jump_out_seed);

  pb::LaneGraphSearchInfo lane_graph_search_info;
  for (const pnc_map::Lane* lane :
       current_lane_associator.current_lanes_for_regional_path()) {
    lane_graph_search_info.add_current_lanes(lane->id());
  }

  const std::vector<const pnc_map::Lane*>
      current_drivable_lanes_for_regional_path =
          current_lane_associator.current_lanes_for_regional_path();
  const routing::pb::RouteQuery route_query = lane_selection::GetRouteQuery(
      {robot_state_snapshot.x(), robot_state_snapshot.y()},
      robot_state_snapshot.timestamp(),
      current_drivable_lanes_for_regional_path, global_route_solution,
      /*constraint_name=*/"",
      /*predefined_road_ids=*/{},
      /*match_type=*/order::pb::MatchType::UNKNOWN_MATCH,
      route_model.cost_generator()
          .cost_engine_config()
          .cost_engine_config_info()
          .speed_for_path_point_meters_per_sec(),
      routing::cost_engine::cost_utils::GetFeatureCostEstimateCost(
          route_model.cost_generator()
              .all_feature_cost_estimates()
              .unreachable_lane_cost_estimates,
          route_model.cost_generator().use_zero_cost()),
      routing::cost_engine::cost_utils::GetFeatureCostEstimateCost(
          route_model.cost_generator()
              .all_feature_cost_estimates()
              .non_last_selected_current_lane_cost_estimates,
          route_model.cost_generator().use_zero_cost()),
      /*last_selected_current_lane=*/nullptr,
      /*followed_path_type=*/routing::pb::QueryFollowedPathType::NONE_REPLAN,
      route_model.coverage_areas());

  // NOTE(Tingran): For simplicity, the logic here is not exactly the same as
  // what we does in world model.
  RegionalMap empty_regional_map;
  WaypointLanesCache waypoint_lanes_cache(
      pnc_map_service->zone_lane_occupancy_map(), edge_filter_manager,
      drivable_lane_reasoner,
      /*cache_capacity=*/600);
  const std::vector<const pnc_map::Section*>
      destination_successor_sections_for_pullover;
  const double pullover_final_extend_section_percentage = 1.0;
  RegionalMapGenerator regional_map_generator(
      pb::RegionalMapConfig(), &drivable_lane_reasoner, &waypoint_lanes_cache);
  RegionalMapReturnType regional_map_return_type =
      regional_map_generator.Generate(
          joint_pnc_map_service, *robot_state_ptr,
          regional_sections_locator.potential_sections(),
          current_lane_associator.current_lane_candidates_for_regional_path(),
          current_lane_associator.GetDrivablePhysicalLanes(),
          current_lane_associator.GetBackupLanes(joint_pnc_map_service),
          route_model_param,
          RegionalPathQueryParam(
              route_query, lane_graph_search_info,
              regional_sections_locator.regional_section_vertices(),
              LaneBlockageDetector(PlannerConfigCenter::GetInstance()),
              pb::ManeuverType::LANE_FOLLOW, pb::LaneSequencePlanInitState(),
              /*is_current_query_use_prior_map=*/false,
              lane_selection::StuckAvoidanceParameter(),
              pb::ExtraPlannerSignals::LANE_KEEP, drivable_lane_reasoner,
              /*section_preference_param=*/
              lane_selection::SectionPreferenceParam(),
              /*forbidden_fork_sections=*/{}, pb::LaneSequenceCandidates(),
              /*lane_object_info_map=*/LaneObjectInfoMap(),
              /*lane_to_junction_info_map=*/nullptr,
              /*is_pulling_out=*/false,
              destination_successor_sections_for_pullover,
              pullover_final_extend_section_percentage,
              /*has_any_potential_stuck_scenario_for_rp=*/false),
          route_event, empty_regional_map,
          /*lane_sequence_proposal_info=*/{},
          /*last_selected_lane_sequence=*/{},
          /*regional_map_debug=*/nullptr);
  if (!regional_map_return_type.regional_map) {
    return empty_regional_map;
  }
  RegionalMap regional_map = std::move(*regional_map_return_type.regional_map);
  // Update roads and zones for |regional_map_|
  regional_map_generator.UpdateRegionalMapRoadAndZone(
      route_model_param, &regional_map,
      /*regional_map_debug=*/nullptr);
  return regional_map;
}

LaneMarkingPtr MockLaneMarking(
    int64_t lane_marking_id, const std::vector<math::geometry::Point2d>& points,
    hdmap::LaneMarking* lane_marking_proto) {
  lane_marking_proto->set_id(lane_marking_id);

  // Construct point vector from points.
  std::vector<std::pair<double, double>> points_vec;
  points_vec.reserve(points.size());
  for (const math::geometry::Point2d point : points) {
    points_vec.emplace_back(point.x(), point.y());
  }
  *lane_marking_proto->mutable_line() =
      pnc_map::CreatePolylineProto(points_vec);

  // Arbitrary attributes.
  float s = 0;
  for ([[maybe_unused]] const hdmap::Point& point :
       lane_marking_proto->line().points()) {
    Attribute* attr = lane_marking_proto->add_attrs();
    attr->mutable_track()->set_s(s++);
  }

  return std::make_shared<pnc_map::LaneMarking>(lane_marking_proto);
}

// TODO(zhangyanbo): Currently incomplete since attributes
// setting interface for lanes are not supported.
LanePtr MockLanePtr(int64_t lane_id, int64_t section_id,
                    const LaneMarkingPtr& left_lane_marking_ptr,
                    const LaneMarkingPtr& right_lane_marking_ptr,
                    const std::shared_ptr<math::Curve2d>& center_line) {
  (void)section_id;
  // Construct proto of lane.
  hdmap::Lane lane_proto;
  lane_proto.set_id(lane_id);
  lane_proto.set_type(hdmap::Lane::REGULAR);
  lane_proto.set_turn(hdmap::Lane::STRAIGHT);

  // Construct point vector using start and end point of curve.
  const math::geometry::Point2d start = center_line->GetStartPoint();
  const math::geometry::Point2d end = center_line->GetEndPoint();
  *lane_proto.mutable_assistant_line() = pnc_map::CreatePolylineProto(
      {{start.x(), start.y()}, {end.x(), end.y()}});

  hdmap::Limit* limit = lane_proto.add_speed_limits();
  limit->set_limit_max(5);
  limit = lane_proto.add_speed_limits();
  limit->set_limit_max(6);

  lane_proto.set_left_lane_marking_id(left_lane_marking_ptr->id());
  lane_proto.set_right_lane_marking_id(right_lane_marking_ptr->id());

  return std::make_shared<pnc_map::Lane>(
      &lane_proto, left_lane_marking_ptr.get(), right_lane_marking_ptr.get());
}

std::vector<LaneMarkingPtr> GetLaneMarkings(
    const std::vector<std::vector<math::geometry::Point2d>>&
        lane_marking_points,
    std::vector<hdmap::LaneMarking>* lane_marking_protos) {
  std::vector<LaneMarkingPtr> lane_markings;
  lane_markings.reserve(lane_marking_points.size());
  lane_marking_protos->resize(lane_marking_points.size());

  for (size_t i = 0; i < lane_marking_points.size(); ++i) {
    lane_markings.emplace_back(MockLaneMarking(i, lane_marking_points[i],
                                               &((*lane_marking_protos)[i])));
  }

  return lane_markings;
}

std::vector<std::shared_ptr<math::geometry::PolylineCurve2d>> GetCenterLines(
    const std::vector<std::vector<math::geometry::Point2d>>& center_points) {
  std::vector<std::shared_ptr<math::geometry::PolylineCurve2d>> center_lines;
  center_lines.reserve(center_points.size());

  for (size_t i = 0; i < center_points.size(); ++i) {
    center_lines.emplace_back(
        std::make_shared<math::geometry::PolylineCurve2d>(center_points[i]));
  }

  return center_lines;
}

WorldModel CreateDummyWorldModel(
    const pb::WorldModelConfig& config, const voy::Pose& pose,
    const voy::Canbus& canbus,
    const std::vector<prediction::pb::Agent>& predicted_agents,
    const pnc_map::PncMapService* pnc_map_service,
    const voy::TrafficLights* traffic_light,
    const std::optional<routing::pb::RouteSolution>& route_solution_in,
    std::shared_ptr<const pb::TrajectoryGuiderOutput> trajectory_guider_output,
    const std::vector<voy::ConstructionZone>& construction_zones,
    const std::string& constraint_name,
    const std::optional<mrc::pb::MrcRequest>& mrc_request,
    const std::optional<const std::vector<pb::AssistResponse>>&
        assist_responses) {
  DCHECK(pnc_map_service);
  SetCalibConfigInstance();
  // Create dummy robot_state.
  std::unique_ptr<RobotState> robot_state_ptr = std::make_unique<RobotState>(
      config.replan_locator_config(), av_comm::CarType::kTest,
      av_comm::CarRegion::kChina);
  const std::shared_ptr<const voy::Canbus> canbus_ptr =
      std::make_shared<const voy::Canbus>(canbus);
  pb::ReplanLocatorDebug replan_locator_debug;
  robot_state_ptr->UpdatePoseCanbus(
      pose, canbus_ptr, /*replan_request=*/nullptr, &replan_locator_debug);

  // Construct and update regional route.
  static std::shared_ptr<routing::EdgeFilterManager> edge_filter_manager =
      std::make_shared<routing::EdgeFilterManager>(
          /*hdmap=*/*pnc_map_service->hdmap(),
          /*in_use_constraint_name=*/constraint_name,
          /*lock_map_after_initing=*/true,
          /*cache_all_filters=*/true);

  // Construct and update traffic_signal_reasoner.
  TrafficSignalReasoner traffic_signal_reasoner;

  // Construct tracked object poses and construction zone.
  std::vector<TrafficParticipantPose> tracked_object_poses;
  prediction::pb::AgentList prediction_agent_list;
  ::google::protobuf::RepeatedPtrField<prediction::pb::Agent>* agent_list =
      prediction_agent_list.mutable_agent_list();

  tracked_object_poses.reserve(predicted_agents.size());
  // Modify the input prediction_agent_list to accommodate legacy unit tests.
  // These tests use an outdated tracked_object format that may prevent
  // proper construction of planner objects in
  // world_model.global_object_manager.
  for (const prediction::pb::Agent& input_agent_proto : predicted_agents) {
    prediction::pb::Agent* agent_proto = agent_list->Add();
    agent_proto->CopyFrom(input_agent_proto);
    auto tracked_object = agent_proto->tracked_object();
    const int64_t timestamp = 0;

    // Ensure the agent has a predicted trajectories.
    // Certain planner logic (e.g., GetPrimaryTrajectoryIndex) depends on this.
    if (agent_proto->predicted_trajectories_size() == 0) {
      auto* dummy_trajectory = agent_proto->add_predicted_trajectories();
      dummy_trajectory->CopyFrom(ConstructPredictedTrajectory(
          tracked_object, timestamp, kTestTrajectoryDuration,
          /*is_output_trajectory=*/true));
    }

    for (auto& predicted_trajectory :
         *agent_proto->mutable_predicted_trajectories()) {
      predicted_trajectory.set_is_multi_output_trajectory(true);
    }

    // Store the tracked object pose at planning initialization timestamp
    tracked_object_poses.emplace_back(timestamp, tracked_object);
  }

  voy::ConstructionZoneList construction_zone_list;
  ::google::protobuf::RepeatedPtrField<voy::ConstructionZone>*
      construction_zones_real_time_map =
          construction_zone_list.mutable_construction_zones_real_time_map();

  for (const voy::ConstructionZone& construction_zone : construction_zones) {
    voy::ConstructionZone* construction_zone_real_time_map =
        construction_zones_real_time_map->Add();
    *construction_zone_real_time_map = construction_zone;
  }

  // Create dummy snapshot.
  std::unique_ptr<Snapshot> snapshot_ptr = std::make_unique<Snapshot>();
  snapshot_ptr->tracked_object_poses = std::move(tracked_object_poses);
  snapshot_ptr->filtered_construction_zones = construction_zones;
  snapshot_ptr->traffic_light_ptr =
      traffic_light != nullptr
          ? std::make_shared<voy::TrafficLights>(*traffic_light)
          : std::make_shared<voy::TrafficLights>();
  snapshot_ptr->pose_ptr = std::make_shared<voy::Pose>(pose);
  snapshot_ptr->trajectory_guider_output_ptr =
      std::move(trajectory_guider_output);
  snapshot_ptr->agent_list_ptr =
      std::make_shared<prediction::pb::AgentList>(prediction_agent_list);
  snapshot_ptr->construction_zone_list_ptr =
      std::make_shared<voy::ConstructionZoneList>(construction_zone_list);
  // Occlusion map and Occlusion checker are needed for applying Occlusion
  // Reasoner.
  snapshot_ptr->obstacles_ptr = std::make_shared<voy::Obstacles>();
  snapshot_ptr->occlusion_map_ptr = std::make_unique<perception::OcclusionMap>(
      *snapshot_ptr->obstacles_ptr, GetSensorToCar(), *snapshot_ptr->pose_ptr);
  cuda_util::OcclusionMapInquirer occlusion_map_inquirer;
  // Occlusion timestamp grid seed.
  planner::pb::OcclusionTimestampGridSeed occlusion_timestamp_grid_seed;
  // Update the occlusion checker.
  snapshot_ptr->occlusion_checker.Update(
      *snapshot_ptr->occlusion_map_ptr, snapshot_ptr->tracked_object_poses,
      robot_state_ptr->current_state_snapshot(), &occlusion_map_inquirer,
      &occlusion_timestamp_grid_seed);
  snapshot_ptr->order_service_ptr =
      std::make_shared<const order::pb::OrderService>(
          order::pb::OrderService());
  snapshot_ptr->route_status_ptr =
      std::make_shared<const routing::pb::RouteStatus>(
          routing::pb::RouteStatus());
  snapshot_ptr->mrc_request_ptr =
      mrc_request.has_value()
          ? std::make_shared<const mrc::pb::MrcRequest>(*mrc_request)
          : nullptr;
  snapshot_ptr->assist_responses_ptr =
      assist_responses.has_value()
          ? std::make_shared<const std::vector<pb::AssistResponse>>(
                *assist_responses)
          : nullptr;
  std::shared_ptr<GlobalObjectManager> global_object_manager =
      std::make_shared<GlobalObjectManager>(pb::RequiredLateralGapConfig(),
                                            config.planner_object_config());
  global_object_manager->UpdateRequiredLateralGapInfo();
  // Create world model
  WorldModel world_model(config, pnc_map_service, edge_filter_manager,
                         global_object_manager,
                         /*robot_max_speed=*/50.0 / 3.6,
                         /*regional_path_range=*/500.0);
  world_model.UpdateFrameView(PlanningSeedAccess::GetPathFrameView());

  world_model.latest_snapshot_ptr_ = (std::move(snapshot_ptr));
  world_model.robot_state_ptr_ = (std::move(robot_state_ptr));
  const pb::WorldModelSeed previous_world_model_seed;
  world_model.UpdateRegionalSectionsLocator(
      previous_world_model_seed,
      /*regional_sections_locator_debug=*/nullptr);
  // Update planner object.
  auto planner_object_map_ptr = world_model.mutable_global_object_manager_ptr();

  planner_object_map_ptr->Update(
      world_model.snapshot_timestamp(), world_model.agent_list(),
      pb::EgoOffroadInfoSeed(),
      world_model.assist_blockage_analyzer().perception_fp_obstacle_tracker(),
      world_model.regional_map().regional_path.distance_to_destination_m,
      world_model.robot_state(), *world_model.pnc_map_service(),
      planner::pb::WorldModelSeed().object_type_tracking_map(),
      world_model.regional_sections_locator()
          .tracked_object_perception_range_lanes(),
      world_model.assist_directive_generator().unstuck_directives(),
      world_model.GetStuckAssistInstruction(), nullptr);

  world_model.mutable_global_object_manager_ptr()
      ->UpdateIsHighAgentDensityScenario();

  world_model.traffic_signal_reasoner_ = std::move(traffic_signal_reasoner);

  routing::pb::RouteSolution route_solution;
  if (route_solution_in) {
    route_solution = *route_solution_in;
  } else {
    // Update regional map.
    pnc_map::MapTestUtil map_test_util;
    // Init PNC Map & update location.
    map_test_util.InitPncMapService(pose.x(), pose.y(), /*z=*/0);
    route_solution = map_test_util.route_solution();
  }

  world_model.UpdateTrafficSignalReasoner(/*current_lanes=*/{});
  world_model.UpdateTrackedObjects(
      /*max_interested_prediction_timestamp=*/std::numeric_limits<
          int64_t>::max());
  world_model.UpdateConstructionZones();
  DrivableLaneReasoner drivable_lane_reasoner(
      edge_filter_manager, &world_model.traffic_signal_reasoner_);
  world_model.regional_map_ =
      GetRegionalMap(pnc_map_service, route_solution, pose, edge_filter_manager,
                     drivable_lane_reasoner,
                     /*robot_max_speed=*/50.0 / 3.6, kRegionalMapRangeInMeter);
  world_model.pickup_dropoff_zone_generator_ptr_ =
      std::make_unique<PickupDropoffZoneInfoGenerator>(
          world_model.robot_state_ptr_->car_model_with_shape()
              .shape_measurement());
  planner::pb::DecoupledManeuverSeed previous_iter_seed;
  world_model.UpdateLaneBlockage(previous_iter_seed,
                                 /*lane_blockage_debug=*/nullptr);
  world_model.SetUpEmptyRoute(*pnc_map_service);
  world_model.UpdateOrderStartTimeInfo();
  if (world_model.latest_snapshot_ptr_->assist_responses_ptr) {
    world_model.assist_task_scheduler_.UpdateAssistTaskQueue(
        world_model.assist_directive_generator_.unstuck_directives(),
        world_model.mrc_request_ptr(),
        std::make_unique<std::vector<pb::AssistResponse>>(
            *(world_model.latest_snapshot_ptr_->assist_responses_ptr.get())),
        world_model.last_selected_lane_sequence(),
        *(world_model.latest_snapshot_ptr_->assist_responses_ptr.get()),
        *(world_model.robot_state_ptr_), pb::UnstuckSeed(),
        world_model.current_lane_associator(),
        world_model.current_lane_reasoner(),
        *(world_model.global_route_solution()),
        /*assist_task_scheduler_debug=*/nullptr);

    // Update unstuck info.
    pnc_map::JointPncMapService joint_pnc_map_service(pnc_map_service);
    CurrentLaneAssociator current_lane_associator(&drivable_lane_reasoner);
    world_model.assist_directive_generator_.UpdateUnstuckInfo(
        world_model.assist_task_scheduler_.assist_task_queue(),
        *(world_model.mutable_global_object_manager_ptr()
              ->planner_object_map_ptr()),
        *(world_model.latest_snapshot_ptr_->assist_responses_ptr.get()),
        world_model.assist_task_scheduler_.remote_assist_signal(),
        *(world_model.robot_state_ptr_), /*ego_current_lane=*/nullptr,
        joint_pnc_map_service, /*remote_speed_limit=*/nullptr,
        pb::UnstuckSeed(), previous_iter_seed.mutable_light_assist_seed(),
        /*assist_directive_generation_debug=*/nullptr);
  }

  return world_model;
}
}  // namespace planner
