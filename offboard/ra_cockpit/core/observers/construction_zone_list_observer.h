#ifndef OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_CONSTRUCTION_ZONE_LIST_OBSERVER_H_
#define OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_CONSTRUCTION_ZONE_LIST_OBSERVER_H_

#include <functional>
#include <memory>

#include "../data_observer.h"
#include "teleassist_protos/teleassist.pb.h"
#include "voy_protos/construction_zones.pb.h"

namespace remote_assist {

class ConstructionZoneListObserver : public IDataObserver {
 public:
  using ConstructionZoneListCallback = std::function<void(
      std::shared_ptr<voy::ConstructionZoneList>, DataChannel)>;

  explicit ConstructionZoneListObserver(ConstructionZoneListCallback callback);

  bool IsInterestedIn(
      const teleassist::pb::TeleassistRequest& message) override;

  std::shared_ptr<google::protobuf::Message> OnMessage(
      const teleassist::pb::TeleassistRequest& message,
      DataChannel data_channel) override;

 private:
  ConstructionZoneListCallback callback_;
};

}  // namespace remote_assist

#endif  // OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_CONSTRUCTION_ZONE_LIST_OBSERVER_H_
