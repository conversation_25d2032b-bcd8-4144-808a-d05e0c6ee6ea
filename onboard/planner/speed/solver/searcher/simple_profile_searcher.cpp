#include "planner/speed/solver/searcher/simple_profile_searcher.h"

#include <algorithm>
#include <cmath>
#include <iostream>
#include <iterator>
#include <limits>
#include <optional>
#include <string>
#include <utility>

#include <glog/logging.h>
#include "planner/speed/discomforts/dynamic_limits.h"
#include "strings/stringprintf.h"

#include "log_utils/log_macros.h"
#include "math/interpolation.h"
#include "math/math_util.h"
#include "math/tagged_range.h"
#include "planner/constants.h"
#include "planner/speed/constraint/constraint_util.h"
#include "planner/speed/discomforts/discomforts.h"
#include "planner/speed/profile/profile.h"
#include "planner/speed/profile/profile_util.h"
#include "planner/speed/speed_common/error_tolerance.h"
#include "planner_protos/speed_decision.pb.h"
#include "trace/trace.h"
#include "voy_trace/trace_planner.h"

namespace planner {
namespace speed {

namespace {

// If last profile is less than this, we can not use it.
constexpr int kMinLastProfileHorizon = 70;

// The overshoot speed tolerance in simple profile generation.
constexpr double kOvershootSpeedTolerance = 0.1;  // m/s

// The analytical upper bound of the residual speed error for the final
// state calculated by JerkTowardsSpeedAndAccelInThreeStages, see
// https://docs.google.com/document/d/1ImHDyxMC2FU4mUMeLGLB39ssDN3a-4lpEHHGMfFDfCY/edit#bookmark=id.tj3vyovbok34
constexpr double kBrakeToStopSmallVToleranceInMps = 0.0125;

// A speed error less than 0.0015, can result in an error of
// 1.5 cm in 10 seconds, which should be ignorable.
constexpr double kDefaultSmallVToleranceInMps = 0.0015;

// The threshold used to confirm whether the ego has completely stopped.
constexpr double kEgoNearStopStateTolerance = 0.1;
// Limit for speed/accel/jerk to trigger comfort brake profile generation when
// near stop.
constexpr double kComfortJerkLimitNearStop = 0.1;

// Returns debug string for jerk sequence.
[[maybe_unused]] std::string JerkSequenceDebugString(
    const JerkSequence& jerks) {
  std::string debug_str = "jerks=[";
  for (JerkAndSteps jerk : jerks) {
    absl::StrAppendFormat(&debug_str, "%.6f, %d ", jerk.jerk, jerk.steps);
  }
  return debug_str;
}

constexpr int kSpeedVlogDebugLevel = 2;

// Returns true if a given speed state is at steady state, aka, its speed state
// does not change.
bool HasReachedSteadyState(
    const State& state,
    const double small_v_tolerance = kDefaultSmallVToleranceInMps) {
  const bool v_is_zero = math::NearZero(state.v);
  const bool a_is_zero = math::NearZero(state.a);
  if (v_is_zero && a_is_zero) {
    return true;
  }
  // A zero acceleration with speed less than 0.001, can result in an error of
  // 1.5 cm in 10 seconds, which should be ignorable.
  const bool small_v = math::NearZero(state.v, small_v_tolerance);
  if (a_is_zero && small_v) {
    DVLOG(kSpeedVlogDebugLevel)
        << "steady state reached with small v: " << state.DebugString();
    return true;
  }
  // Due to computation errors, we can have slight negative acceleration when
  // speed is zero. We stop at this state, as it is impossible to brake from
  // this state any more.
  const bool negative_a = (state.a < 0.0);
  if (v_is_zero && negative_a) {
    DVLOG(kSpeedVlogDebugLevel)
        << "steady state reached with negative a: " << state.DebugString();
    return true;
  }

  return false;
}

// Returns true if a given speed state is steady at the expected speed.
bool HasReachedConstSpeedState(
    const State& state, double expected_v,
    const double small_v_tolerance = kDefaultSmallVToleranceInMps) {
  if (math::NearZero(state.a) &&
      math::IsApprox(state.v, expected_v, small_v_tolerance)) {
    return true;
  }
  if (math::NearZero(expected_v, small_v_tolerance)) {
    return HasReachedSteadyState(state, small_v_tolerance);
  }
  return false;
}

// Updates |mutable_profile| starting from |brake_first_ix| using the control
// from |braking_jerks|. If |check_neg_speed| is true, explicitly checks
// every states do not have neg speed when moving backward.
void ModifyProfileGivenJerkSequence(const JerkSequence& braking_jerks,
                                    const int brake_first_ix,
                                    const int brake_end_ix, const double dt,
                                    const bool check_neg_speed,
                                    Profile* mutable_profile) {
  DCHECK(mutable_profile != nullptr);
  DCHECK_LT(brake_first_ix, brake_end_ix);
  mutable_profile->erase(mutable_profile->begin() + brake_first_ix + 1,
                         mutable_profile->end());
  State state = mutable_profile->back();
  for (size_t brake_ix = 0; brake_ix < braking_jerks.size(); ++brake_ix) {
    const auto& [jerk, steps] = braking_jerks[brake_ix];
    for (int ix = 0; ix < steps; ix++) {
      if (static_cast<int64_t>(mutable_profile->size()) > brake_end_ix) {
        // GenerateBrakeProfile might generate a profile that's longer than
        // what's expected downstream. In this case, we will break early.
        break;
      }
      mutable_profile->back().j = jerk;
      state.j = jerk;
      // As before calling this function, we have ensured that 'braking_jerks'
      // does not cause negative speed. Here if users do not explicitly
      // request, we do not need to check negative speed. Even if users
      // request to check it, we still allow negative speed for the last step,
      // as in the last phase of ReleaseGasAndGetOnBrake, we could have small
      // residual negative acceleration due to computation errors.
      const bool is_last_step = (brake_ix + 1 == braking_jerks.size());
      state.MoveInPlaceNonBackward(
          dt, /*check_neg_speed=*/check_neg_speed && !is_last_step,
          /*allowed_error=*/kPositionError);
      mutable_profile->push_back(state);
    }
  }

  // Make sure profile is at least as long as (brake_end_ix + 1).
  const int residual_steps = brake_end_ix + 1 - mutable_profile->size();
  if (residual_steps > 0) {
    // Allow negative speed if we reach steady state with zero speed and slight
    // negative acceleration.
    // TODO(shiying): maybe allow this always.
    const bool allow_neg_speed =
        math::IsApprox(mutable_profile->back().v, 0.0) &&
        mutable_profile->back().a < -kErrorTolerance;
    for (int ix = 0; ix < residual_steps; ix++) {
      mutable_profile->back().j = 0.0;
      state.j = 0.0;
      state.MoveInPlaceNonBackward(
          dt, /*check_neg_speed=*/!allow_neg_speed && check_neg_speed,
          /*allowed_error=*/kPositionError);
      mutable_profile->push_back(state);
    }
  }
}

// Returns true if we can not avoid overshoot based on the first state and
// updates 'mutable_profile' with inevitable overshoot. O.w. returns false and
// do not update mutable_profile
bool GenerateProfileWithInevitableOvershoot(
    const Limits& limits, int profile_first_ix, double dt, int n_horizon,
    double goal_v, const Profile* nullable_sampled_ref_profile,
    Profile* mutable_profile) {
  int release_gas_steps = 0;
  JerkSequence release_gas_jerks;
  const bool can_release_gas = CanReleaseGasToZeroAccelWithoutOvershoot(
      limits, mutable_profile->back(), dt, goal_v, &release_gas_steps,
      &release_gas_jerks);
  if (!can_release_gas) {
    if (release_gas_steps > 0) {
      ModifyProfileGivenJerkSequence(release_gas_jerks, profile_first_ix,
                                     profile_first_ix + release_gas_steps, dt,
                                     /*check_neg_speed=*/false,
                                     mutable_profile);
    }
    // After finishing release gas, we should already been overshoot.
    DCHECK_GE(mutable_profile->back().v, goal_v - kErrorTolerance);
    // Start to brake from here.
    GenerateBrakeProfileToExpectedSpeed(
        /*brake_first_ix=*/profile_first_ix + release_gas_steps,
        /*brake_end_ix=*/n_horizon - 1, limits, dt, goal_v,
        nullable_sampled_ref_profile, mutable_profile);
    return true;
  }
  return false;
}

// Returns true if the time map entry pos is considered as a conflict to the
// profile state x.
bool IsConflictEntryPos(const Decision decision, const bool pass,
                        const double profile_state_x, const double entry_pos) {
  if (decision == Decision::IGNORE) {
    return false;
  }
  if (pass && decision == Decision::YIELD) {
    return false;
  }
  if (!pass &&
      (decision == Decision::PASS || decision == Decision::NOT_DECIDED)) {
    return false;
  }
  if (pass && profile_state_x > entry_pos) {
    return false;
  }
  if (!pass && profile_state_x < entry_pos) {
    return false;
  }
  return true;
}

// Returns true if the time map entry is considered as a conflict to the profile
// considering the constraint's type.
bool IsConflictEntry(
    const pb::TreeSearchType::Enum tree_search_type, const Profile& profile,
    const TimeMap::Entry& entry, const Constraint& constraint,
    const absl::flat_hash_map<int, GapAlignInfo>& gap_align_info_map,
    const int time_ix, const int steps, const Decision decision,
    const bool pass, const int tree_switch_time_ix, const double t_horizon,
    const bool adjust_for_tree_branch) {
  if (!IsConflictEntryPos(decision, pass, profile[time_ix].x,
                          entry.start_or_end_x)) {
    return false;
  }

  // There is a timemap conflict if reaching here. We further check if the
  // constraint should not be considered in the current search. If not, the
  // conflict is valid.

  // Check risk mitigation constraint.
  if (tree_search_type == pb::TreeSearchType_Enum_kRiskMitigation &&
      IsValidRiskMitigationConstraint(constraint, t_horizon)) {
    DCHECK(constraint.type != ConstraintType::Constraint_Type_GAP_ALIGN);
    // If a potential conflict of risk mitigation
    // constraint happens during a tree search, we consider it as a true
    // conflict. Otherwise, it is not a conflict. Note that a conflict can
    // happen before switch time during the risk profile search.
    return adjust_for_tree_branch;
  }

  // Determine conflict for gap align tree search.
  if (tree_search_type == pb::TreeSearchType_Enum_kLaneChangeGapAlign &&
      time_ix >= tree_switch_time_ix) {
    if (constraint.settings.is_for_lane_change_tree_search) {
      DCHECK(constraint.type == ConstraintType::Constraint_Type_GAP_ALIGN);
      if (!MaybeConsiderAsConflictForConflictAfterLcSwitchTime(
              adjust_for_tree_branch, pass, constraint)) {
        return false;
      }
    } else {
      // The conflict is NOT for lane change tree search, so we do not consider
      // it as a conflict if we are searching on the tree branch and it is NOT a
      // gap align constraint. NOTE that we need to consider all gap align
      // constraints on tree branch regardless of its tree search policy
      // disabled or not.
      if (adjust_for_tree_branch &&
          constraint.type != ConstraintType::Constraint_Type_GAP_ALIGN) {
        return false;
      }
    }
  }

  // Check gap align constraint.
  if (constraint.type == ConstraintType::Constraint_Type_GAP_ALIGN) {
    // For gap align constraint, find its gap align info in the
    // |gap_align_info_map|.
    const auto iter = gap_align_info_map.find(entry.constraint_ix);
    DCHECK(iter != gap_align_info_map.end());
    const PassYieldOption pass_yield_option =
        (pass ? constraint.settings.pass_option
              : constraint.settings.yield_option);
    const int pass_yield_active_time_ix =
        pass ? iter->second.pass_active_time_ix
             : iter->second.yield_active_time_ix;
    if (!MaybeConsiderAsConflictAccordingToActiveTime(
            pass_yield_option, pass_yield_active_time_ix, steps, time_ix)) {
      return false;
    }
  }

  return true;
}

// Returns true if there is a conflict between profile and the constraints with
// decisions. Populates the conflict.
bool FindAndPopulateConflict(
    const bool pass, const pb::TreeSearchType::Enum tree_search_type,
    const Profile& profile, const std::vector<Constraint>& constraints,
    const std::vector<Decision>& decisions,
    const absl::flat_hash_map<int, GapAlignInfo>& gap_align_info_map,
    const TimeMap& time_map, const int tree_switch_time_ix,
    const double t_horizon, const bool adjust_for_tree_branch,
    const int search_for_conflict_start_ix, Conflict* conflict) {
  TRACE_EVENT_INSTANT(planner, FindAndPopulateConflict);
  DCHECK(conflict != nullptr);
  const int steps = time_map.steps();
  for (const auto& [time_ix, entries] : time_map.entries(pass)) {
    if (time_ix < search_for_conflict_start_ix) {
      // Do not consider entry before |search_for_conflict_start_ix|.
      continue;
    }
    for (const auto& entry : entries) {
      const Constraint& constraint = constraints[entry.constraint_ix];
      const Decision decision = decisions[entry.constraint_ix];
      if (!IsConflictEntry(tree_search_type, profile, entry, constraint,
                           gap_align_info_map, time_ix, steps, decision, pass,
                           tree_switch_time_ix, t_horizon,
                           adjust_for_tree_branch)) {
        continue;
      }
      // We have found a conflict.
      *conflict =
          Conflict({entry.constraint_ix, time_ix, entry.start_or_end_x, pass});
      return true;
    }
  }
  return false;
}

// Updates the max jerk such that it does not violate
// the discomfort jerk limit and also if applied to
// current state, does not lead to an accel value that
// violates discomfort min accel limit.
void UpdateMaxJerkToStayInLimits(const Limits& limits,
                                 const State& current_state, const double dt,
                                 double& max_jerk) {
  const double min_j_to_not_exceed_min_a =
      (limits.brake_a.min - current_state.a) / dt;
  max_jerk = std::max(max_jerk, min_j_to_not_exceed_min_a);
  if (current_state.a < 0) {
    max_jerk = math::Clamp(max_jerk, limits.brake_j.min, limits.brake_j.max);
  } else {
    max_jerk = math::Clamp(max_jerk, limits.accel_j.min, limits.accel_j.max);
  }
}

}  // namespace

std::string Conflict::DebugString() const {
  return absl::StrFormat("constraint ix: %d, time ix: %d, %s, position: %.3f",
                         constraint_ix, time_ix, pass ? "pass" : "yield", pos);
}

void Conflict::Clear() {
  constraint_ix = -1;
  time_ix = -1;
  pos = std::numeric_limits<double>::infinity();
  pass = true;
}

// TODO(shiying): consider merge BrakeToMinAccel with
// ReleaseGasAndGetOnBrake to one single function with two call options.
int ReleaseGasAndGetOnBrake(const Limits& limits, const double dt,
                            const double expected_v, State& mutable_state,
                            bool* reach_expected_v, JerkSequence* brake_jerks) {
  *reach_expected_v = false;
  // No need to release gas if we are already braking.
  if (mutable_state.a < -kErrorTolerance) {
    // We should not be in an ill-pose now.
    DCHECK(CanReleaseBrakeToZeroAccelWithoutUndershoot(limits, mutable_state,
                                                       dt, /*expected_v=*/0.0))
        << "ERROR: initial speed state is bad:";
    return 0;
  }
  // Step 1: Release gas to get to negative acceleration.
  State prev_state = mutable_state;
  int release_gas_steps = 0;
  while (mutable_state.a >= -kErrorTolerance) {
    mutable_state.j = limits.accel_j.min;
    // Allow one last jerk to lead to negative speed as we will be limiting
    // it below.
    const double neg_v_min_jerk =
        MinJerkToAvoidNegativeSpeedAndDisplacement(dt, mutable_state);
    const bool check_neg_speed = mutable_state.j >= neg_v_min_jerk;
    prev_state = mutable_state;
    mutable_state.MoveInPlaceNonBackward(dt, check_neg_speed, kPositionError);
    release_gas_steps += 1;
    if (!check_neg_speed) {
      DCHECK_LT(mutable_state.a, -kErrorTolerance)
          << "prev state: " << prev_state.DebugString()
          << "; current state: " << mutable_state.DebugString()
          << "; accel j limits: " << limits.accel_j.DebugString();
    }
  }

  DCHECK_LT(mutable_state.a, -kErrorTolerance);
  // If we release gas to reach a negative acceleration, and we will be able to
  // release brake from this negative accel without causing negative speed (aka.
  // will not lead to an ill-pose). Return early with the release-gas steps.
  const bool can_release_wo_neg_speed =
      CanReleaseBrakeToZeroAccelWithoutUndershoot(limits, mutable_state, dt,
                                                  /*expected_v=*/0.0);
  if (can_release_wo_neg_speed) {
    if (brake_jerks != nullptr && release_gas_steps > 0) {
      brake_jerks->push_back(
          JerkAndSteps({limits.accel_j.min, release_gas_steps}));
    }
    return release_gas_steps;
  }

  // Here, the acceleration at the mutable state is negative, but we will reach
  // a negative speed even immediately releasing brakes (that means, ego state
  // is negative accel and near-zero speed). To avoid the resulted negative
  // speed, we need to go back one step to re-calculate from there. We will use
  // a minor jerk to bring ego to exactly to the expected-speed state.
  DCHECK_GE(prev_state.a, -kErrorTolerance);
  DCHECK_LT(mutable_state.a, -kErrorTolerance);
  // Store the earlier release-gas jerk steps first.
  if (brake_jerks != nullptr) {
    if (release_gas_steps > 1) {
      brake_jerks->push_back(
          JerkAndSteps({limits.accel_j.min, release_gas_steps - 1}));
    }
  }
  mutable_state = prev_state;
  DCHECK_GE(mutable_state.v, expected_v);
  // Early return if the previous state is already zero-speed.
  if (HasReachedSteadyState(mutable_state)) {
    *reach_expected_v = HasReachedConstSpeedState(mutable_state, expected_v);
    return std::max(0, release_gas_steps - 1);
  }
  // Do last phase to reach steady state.
  int last_phase_steps = 0;
  *reach_expected_v = DoLastPhaseToExpectedSpeed(
      limits, dt, expected_v, mutable_state.DebugString(), mutable_state,
      &last_phase_steps, brake_jerks);
  DCHECK(*reach_expected_v);
  return std::max(release_gas_steps - 1 + last_phase_steps, 0);
}

int BrakeToMinAccel(const Limits& limits, const double dt,
                    const double expected_v, State& mutable_state,
                    bool* can_release_wo_undershoot_expected_v,
                    JerkSequence* brake_jerks) {
  DCHECK(can_release_wo_undershoot_expected_v != nullptr);
  // Only allow brake_min_j when decelerating.
  DCHECK_LT(mutable_state.a, kErrorTolerance);
  // Return early if we already undershoot min_accel.
  if (mutable_state.a <= limits.brake_a.min) {
    *can_release_wo_undershoot_expected_v =
        math::IsApprox(mutable_state.a, limits.brake_a.min);
    return 0;
  }
  // Step 1: brake to min_accel.
  State prev_state = mutable_state;
  int brake_steps = 0;
  bool inside_while_loop = false;
  while (CanReleaseBrakeToZeroAccelWithoutUndershoot(limits, mutable_state, dt,
                                                     expected_v)) {
    inside_while_loop = true;
    if (mutable_state.a <= limits.brake_a.min) {
      break;
    }
    const double neg_v_min_jerk =
        MinJerkToAvoidNegativeSpeedAndDisplacement(dt, mutable_state);
    // If we are about to undershoot zero speed, we should return early to
    // release brake.
    if (limits.brake_j.min < neg_v_min_jerk) {
      if (brake_jerks != nullptr && brake_steps > 0) {
        brake_jerks->push_back(JerkAndSteps({limits.brake_j.min, brake_steps}));
      }
      *can_release_wo_undershoot_expected_v = true;
      return brake_steps;
    }
    prev_state = mutable_state;
    mutable_state.j = limits.brake_j.min;
    mutable_state.MoveInPlaceNonBackward(dt, true, kPositionError);
    brake_steps += 1;
  }
  // If we can not release brake to avoid undershoot, we should not
  // include this step of braking and just return.
  *can_release_wo_undershoot_expected_v =
      CanReleaseBrakeToZeroAccelWithoutUndershoot(limits, mutable_state, dt,
                                                  expected_v);
  if (!(*can_release_wo_undershoot_expected_v)) {
    mutable_state = prev_state;
    if (brake_jerks != nullptr && brake_steps > 0) {
      brake_jerks->push_back(
          JerkAndSteps({limits.brake_j.min, brake_steps - 1}));
    }
    *can_release_wo_undershoot_expected_v = inside_while_loop;
    return std::max(0, brake_steps - 1);
  }

  DCHECK(*can_release_wo_undershoot_expected_v);
  if (math::IsApprox(mutable_state.a, limits.brake_a.min)) {
    if (brake_jerks != nullptr && brake_steps > 0) {
      brake_jerks->push_back(JerkAndSteps({limits.brake_j.min, brake_steps}));
    }
    return brake_steps;
  }

  // At here, 'can_release' must be true and we undershoot min_accel at
  // 'mutable_state', we go back one step to reduce the jerk at 'prev_state'
  // to reach min_accel in one step.
  DCHECK_GT(prev_state.a, limits.brake_a.min - kErrorTolerance);
  DCHECK_LT(mutable_state.a, limits.brake_a.min + kErrorTolerance);
  // Due to the above negative speed handling, here the neg_speed_jerk_limit
  // will always be smaller than brake_min_j. So no need to clamp.
  double transition_jerk = (limits.brake_a.min - prev_state.a) / dt;
  transition_jerk =
      std::max(transition_jerk,
               MinJerkToAvoidNegativeSpeedAndDisplacement(dt, prev_state));
  // This transition jerk must be a negative value inside limits.
  DCHECK_GE(transition_jerk, limits.brake_j.min - kErrorTolerance);
  DCHECK_LT(transition_jerk, 0.0);
  mutable_state = prev_state;
  mutable_state.j = transition_jerk;
  mutable_state.MoveInPlaceNonBackward(dt, true, kPositionError);
  *can_release_wo_undershoot_expected_v =
      CanReleaseBrakeToZeroAccelWithoutUndershoot(limits, mutable_state, dt,
                                                  expected_v);
  DCHECK(*can_release_wo_undershoot_expected_v) << mutable_state.DebugString();

  // This should always be true.
  DCHECK_GE(mutable_state.a, limits.brake_a.min - kErrorTolerance);
  DCHECK_GE(transition_jerk, limits.brake_j.min - kErrorTolerance);
  if (brake_jerks != nullptr) {
    if (brake_steps > 1) {
      brake_jerks->push_back(
          JerkAndSteps({limits.brake_j.min, brake_steps - 1}));
    }
    brake_jerks->push_back(JerkAndSteps({transition_jerk, 1}));
  }
  return brake_steps;
}

bool CanReleaseBrakeToZeroAccelWithoutUndershoot(
    const Limits& limits, const State& current_state, const double dt,
    const double expected_v, double* feasible_initial_min_accel,
    int* release_brake_step, JerkSequence* release_brake_jerks) {
  if (release_brake_step != nullptr) {
    *release_brake_step = 0;
  }
  if (current_state.v < expected_v) {
    if (feasible_initial_min_accel != nullptr) {
      *feasible_initial_min_accel = 0.0;
    }
    return false;
  }
  if (current_state.a >= 0.0) {
    return true;
  }
  if (feasible_initial_min_accel != nullptr) {
    *feasible_initial_min_accel =
        -std::sqrt(2.0 * limits.brake_j.max * (current_state.v - expected_v));
  }
  // Use the max jerk to go to zero accel, and see if it can result in negative
  // speed. If we can not go to zero accel with max jerk with positive speed,
  // then this state is ill-posed.
  // Step 1: use max jerk and multiple of time steps to go to zero acceleration.
  DCHECK_NE(limits.brake_j.max, 0.0);
  const double inv_dt = 1.0 / dt;
  const int max_jerk_steps =
      math::FloorToIntWithTol(-inv_dt * current_state.a / limits.brake_j.max);
  const double delta_t = max_jerk_steps * dt;
  DCHECK_GE(delta_t, -kErrorTolerance)
      << "current_state.a: " << current_state.a
      << ", limits.brake_j.max: " << limits.brake_j.max;
  State mutable_state = current_state;
  mutable_state.j = limits.brake_j.max;
  mutable_state.MoveInPlace(delta_t, /*check_neg_speed=*/false, 0.001);
  if (release_brake_step != nullptr && max_jerk_steps > 0) {
    DCHECK(release_brake_jerks != nullptr);
    *release_brake_step += max_jerk_steps;
    release_brake_jerks->push_back(
        JerkAndSteps({limits.brake_j.max, max_jerk_steps}));
  }
  const bool is_step_1_state_undershoot = (mutable_state.v < expected_v);

  // Step 2: if there is still residual of acceleration that does not reach zero
  // acceleration, use one time step dt but the jerk needed should be less than
  // max jerk above.
  const double last_step_jerk = -mutable_state.a * inv_dt;
  DCHECK_LE(last_step_jerk, limits.brake_j.max + kErrorTolerance);
  // Acceleration at the last state could be a small negative value due to
  // FloorToInWithTol(), which could be as small as
  // limits.brake_j.max * dt * kErrorTolerance.
  DCHECK_GE(last_step_jerk, -limits.brake_j.max * kErrorTolerance);
  mutable_state.j = last_step_jerk;
  mutable_state.MoveInPlace(dt, /*check_neg_speed=*/false, 0.001);
  if (release_brake_step != nullptr) {
    DCHECK(release_brake_jerks != nullptr);
    *release_brake_step += 1;
    release_brake_jerks->push_back(JerkAndSteps({last_step_jerk, 1}));
  }
  const bool is_step_2_state_undershoot = (mutable_state.v < expected_v);

  return !is_step_1_state_undershoot && !is_step_2_state_undershoot;
}

bool CanReleaseGasToZeroAccelWithoutOvershoot(const Limits& limits,
                                              const State& current_state,
                                              double dt, double goal_v,
                                              int* release_gas_step,
                                              JerkSequence* release_gas_jerks) {
  if (release_gas_step != nullptr) {
    *release_gas_step = 0;
  }
  if (current_state.v > goal_v) {
    return false;
  }
  if (current_state.a <= 0.0) {
    return true;
  }
  // Step 1: use min jerk and multiple of time steps to go to zero acceleration.
  DCHECK_NE(limits.accel_j.min, 0.0);
  const double inv_dt = 1.0 / dt;
  const int min_jerk_steps =
      math::FloorToIntWithTol(-inv_dt * current_state.a / limits.accel_j.min);
  const double delta_t = min_jerk_steps * dt;
  DCHECK_GE(delta_t, 0.0);
  State mutable_state = current_state;
  mutable_state.j = limits.accel_j.min;
  mutable_state.MoveInPlace(delta_t, /*check_neg_speed=*/false,
                            /*allowed_error=*/0.001);
  if (release_gas_step != nullptr && min_jerk_steps > 0) {
    DCHECK(release_gas_jerks != nullptr);
    *release_gas_step += min_jerk_steps;
    release_gas_jerks->push_back(
        JerkAndSteps({limits.accel_j.min, min_jerk_steps}));
  } else {
    // We can return early if we don't need the full |release_gas_jerks| info.
    if (mutable_state.v > goal_v) {
      return false;
    }
  }

  // Step 2: if there is still residual of acceleration that does not reach zero
  // acceleration, use one time step dt but the jerk needed should be less than
  // min jerk above.
  const double last_step_jerk = -mutable_state.a * inv_dt;
  DCHECK_GE(last_step_jerk, limits.accel_j.min - kErrorTolerance);
  // Acceleration at the last state could be a small positive value due to
  // FloorToInWithTol(), which could be as small as
  // limits.accel_j.min * dt * kErrorTolerance.
  DCHECK_LE(last_step_jerk, -limits.accel_j.min * kErrorTolerance);
  mutable_state.j = last_step_jerk;
  mutable_state.MoveInPlace(dt, /*check_neg_speed=*/false,
                            /*allowed_error=*/0.001);
  if (release_gas_step != nullptr) {
    DCHECK(release_gas_jerks != nullptr);
    *release_gas_step += 1;
    release_gas_jerks->push_back(JerkAndSteps({last_step_jerk, 1}));
  }
  return mutable_state.v <= goal_v;
}

bool ReleaseBrakeToZeroAccelToExpectedSpeed(
    const Limits& limits, const double dt, const double expected_v,
    const bool check_neg_accel, State& mutable_state, int* brake_steps,
    JerkSequence* brake_jerks) {
  DCHECK(brake_steps != nullptr);
  // We must have been braking to release brake.
  DCHECK_LT(mutable_state.a, kErrorTolerance);
  State prev_state;
  *brake_steps = 0;
  // Record false if the accel at any state in the release-brake process
  // will unavoidably lead to negative speed.
  bool no_neg_speed = true;
  while (mutable_state.a < 0.0) {
    prev_state = mutable_state;
    mutable_state.j = limits.brake_j.max;
    const double neg_v_min_accel =
        MinAccelToAvoidNegativeSpeed(dt, limits.brake_j.max, mutable_state.v);
    // If we are decelerating with large deceleration, we will overshoot zero
    // speed in dt and thus fail to brake to zero acceleration with max
    // deceleration.
    if (check_neg_accel) {
      DCHECK_GE(mutable_state.a, neg_v_min_accel - kErrorTolerance);
    } else if (mutable_state.a < neg_v_min_accel) {
      // This state will unavoidably lead to negative speed.
      no_neg_speed = false;
      DVLOG(kSpeedVlogDebugLevel)
          << "ReleaseBrakeToZeroAccel gets a bad state: "
          << mutable_state.DebugString();
    }
    mutable_state.MoveInPlaceNonBackward(dt, check_neg_accel, kPositionError);
    *brake_steps += 1;
  }
  if (brake_jerks != nullptr && *brake_steps > 1) {
    brake_jerks->push_back(
        JerkAndSteps({limits.brake_j.max, *brake_steps - 1}));
  }

  // If we overshoot zero accel, use smaller jerk.
  if (mutable_state.a > 0.0) {
    DCHECK_LT(prev_state.a, kErrorTolerance);
    DCHECK_GT(mutable_state.a, -kErrorTolerance);
    const double neg_v_min_j =
        MinJerkToAvoidNegativeSpeedAndDisplacement(dt, prev_state);
    // It is possible here that, neg_v_min_j is larger than reach_zero_a_jerk.
    // we can have a larger than we want.
    const double reach_zero_a_jerk = -prev_state.a / dt;
    if (neg_v_min_j > reach_zero_a_jerk) {
      DVLOG(kSpeedVlogDebugLevel)
          << "we can reach zero speed with some residual positive a: "
          << prev_state.DebugString() << "; neg_v_min_j: " << neg_v_min_j
          << ";reach_zero_a_jerk: " << reach_zero_a_jerk;
    }
    const double transition_jerk =
        check_neg_accel ? std::max(reach_zero_a_jerk, neg_v_min_j)
                        : reach_zero_a_jerk;
    DCHECK(JerkWithinLimits(prev_state.a, transition_jerk, limits))
        << "state: " << prev_state.DebugString()
        << "; jerk: " << transition_jerk
        << "; brake j: " << limits.brake_j.DebugString();
    mutable_state = prev_state;
    mutable_state.j = transition_jerk;
    mutable_state.MoveInPlaceNonBackward(dt, check_neg_accel, kPositionError);
    if (brake_jerks != nullptr) {
      brake_jerks->push_back(JerkAndSteps({transition_jerk, 1}));
    }
  } else if (brake_jerks != nullptr) {
    DCHECK(math::IsApprox(mutable_state.a, 0.0));
    brake_jerks->push_back(JerkAndSteps({limits.brake_j.max, 1}));
  }
  // Release-brake is failed if any state in the process has a negative speed.
  if (!no_neg_speed) {
    return false;
  }
  // Release-brake is successful if we have reached the expected speed.
  if (HasReachedConstSpeedState(mutable_state, expected_v)) {
    return true;
  }

  // We should now be close to the target speed. Finish to the goal state.
  // Early return false if an undershoot is unavoidable from here.
  if (!CanReleaseBrakeToZeroAccelWithoutUndershoot(limits, mutable_state, dt,
                                                   expected_v)) {
    LOG(ERROR) << "From " << mutable_state.DebugString()
               << " can not brake to const-speed state with expected v: "
               << expected_v;
    return false;
  }
  double jerk1 = NAN, jerk2 = NAN, jerk3 = NAN;
  int jerk1_steps = 0, jerk2_steps = 0;
  // If the mutable_state is still relatively far away from the goal_state,
  // the function may fail to find a solution, in which case, the release-brake
  // process is not successful.
  // TODO(tienan): better adjust the mutable_state to approach the goal_state
  // before calling the function to increase the successful rate here.
  if (!JerkTowardsSpeedAndAccelInThreeStages(
          dt, expected_v, /*goal_a=*/0.0, limits, mutable_state, &jerk1, &jerk2,
          &jerk3, &jerk1_steps, &jerk2_steps)) {
    return false;
  }
  for (int i = 0; i < jerk1_steps; ++i) {
    mutable_state.j = jerk1;
    mutable_state = mutable_state.MoveNonBackward(dt, /*check_neg_speed=*/true,
                                                  kPositionError);
  }
  for (int i = 0; i < jerk2_steps; ++i) {
    mutable_state.j = jerk2;
    mutable_state = mutable_state.MoveNonBackward(dt, /*check_neg_speed=*/true,
                                                  kPositionError);
  }
  mutable_state.j = jerk3;
  // Allow negative speed in the third step as expected_v can not be strictly
  // enforced by JerkTowardsSpeedAndAccelInThreeStages. So there could
  // be slight speed overshoot/undershoot because of jerk3.
  mutable_state.MoveInPlaceNonBackward(dt, /*check_neg_speed=*/false,
                                       kPositionError);
  if (HasReachedConstSpeedState(mutable_state, expected_v,
                                kBrakeToStopSmallVToleranceInMps)) {
    if (brake_jerks != nullptr) {
      brake_jerks->push_back(JerkAndSteps({jerk1, jerk1_steps}));
      brake_jerks->push_back(JerkAndSteps({jerk2, jerk2_steps}));
      brake_jerks->push_back(JerkAndSteps({jerk3, 1}));
    }
    *brake_steps += (jerk1_steps + jerk2_steps + 1);
    return true;
  }
  return false;
}

bool DoLastPhaseToExpectedSpeed(const Limits& limits, double dt,
                                const double expected_v,
                                const std::string& debug_str,
                                State& mutable_state, int* steps,
                                JerkSequence* brake_jerks) {
  DCHECK(steps != nullptr);
  if (HasReachedConstSpeedState(mutable_state, expected_v)) {
    *steps = 0;
    return true;
  }
  const State init_state = mutable_state;

  // Find the control within limits to go to zero speed also.
  double jerk1 = NAN, jerk2 = NAN;
  JerkTowardsSpeedAndAccelInTwoSteps(dt, expected_v, /*goal_a=*/0.0, limits,
                                     mutable_state, &jerk1, &jerk2);
  jerk1 = std::max(
      jerk1, MinJerkToAvoidNegativeSpeedAndDisplacement(dt, mutable_state));
  DCHECK(JerkWithinLimits(mutable_state.a, jerk1, limits))
      << "mutable_state: " << mutable_state.DebugString()
      << "; jerk1: " << jerk1
      << "; brake j limits: " << limits.brake_j.DebugString()
      << "; accel j limits: " << limits.accel_j.DebugString() << "; \n"
      << debug_str;
  mutable_state.j = jerk1;
  // As we have ensured above that it can not move forward, so we use
  // MoveInPlaceNonBackward.
  mutable_state.MoveInPlaceNonBackward(dt, /*check_neg_speed=*/true,
                                       /*allowed_error=*/kPositionError);

  if (HasReachedConstSpeedState(mutable_state, expected_v)) {
    if (brake_jerks != nullptr) {
      brake_jerks->push_back(JerkAndSteps({jerk1, 1}));
    }
    *steps = 1;
    return true;
  }

  const bool can_release = CanReleaseBrakeToZeroAccelWithoutUndershoot(
      limits, mutable_state, dt, /*expected_v=*/0.0);
  if (can_release) {
    mutable_state.j = jerk2;
    mutable_state.MoveInPlaceNonBackward(dt, /*check_neg_speed=*/true,
                                         /*allowed_error=*/kPositionError);
    if (HasReachedConstSpeedState(mutable_state, expected_v)) {
      if (brake_jerks != nullptr) {
        brake_jerks->push_back(JerkAndSteps({jerk1, 1}));
        brake_jerks->push_back(JerkAndSteps({jerk2, 1}));
      }
      *steps = 2;
      return true;
    }
  }

  // We need to re-calculate the jerk sequence.
  double jerk3 = NAN;
  int jerk1_steps = 0, jerk2_steps = 0;
  mutable_state = init_state;
  JerkTowardsSpeedAndAccelInThreeStages(dt, expected_v, /*goal_a=*/0.0, limits,
                                        mutable_state, &jerk1, &jerk2, &jerk3,
                                        &jerk1_steps, &jerk2_steps);
  mutable_state.j = jerk1;
  const double t1 = dt * jerk1_steps;
  mutable_state.MoveInPlaceNonBackward(t1, /*check_neg_speed=*/true,
                                       /*allowed_error=*/kPositionError);
  mutable_state.j = jerk2;
  const double t2 = dt * jerk2_steps;
  mutable_state.MoveInPlaceNonBackward(t2, /*check_neg_speed=*/true,
                                       /*allowed_error=*/kPositionError);
  mutable_state.j = jerk3;
  // Allow negative speed for last step, as it is definite if we can not
  // release using max jerk in fixed sampling steps.
  mutable_state.MoveInPlaceNonBackward(dt, /*check_neg_speed=*/false,
                                       /*allowed_error=*/kPositionError);
  if (brake_jerks != nullptr) {
    brake_jerks->push_back(JerkAndSteps({jerk1, jerk1_steps}));
    brake_jerks->push_back(JerkAndSteps({jerk2, jerk2_steps}));
    brake_jerks->push_back(JerkAndSteps({jerk3, 1}));
  }
  *steps = 2 + jerk2_steps;
  DCHECK(HasReachedConstSpeedState(mutable_state, expected_v,
                                   kBrakeToStopSmallVToleranceInMps))
      << " Can not reach zero speed after the three-stage method. Init state: "
      << init_state.DebugString()
      << "; Final state: " << mutable_state.DebugString() << " expected_v is "
      << expected_v;
  return true;
}

void GenerateBrakeProfile(const int brake_first_ix, const int brake_end_ix,
                          const Limits& limits, const double dt,
                          Profile* mutable_profile) {
  GenerateBrakeProfileToExpectedSpeed(brake_first_ix, brake_end_ix, limits, dt,
                                      /*expected_v=*/0.0,
                                      /*nullable_sampled_ref_profile=*/nullptr,
                                      mutable_profile);
}

void GenerateBrakeProfileToExpectedSpeed(
    const int brake_first_ix, const int brake_end_ix, const Limits& limits,
    const double dt, const double expected_v,
    const Profile* nullable_sampled_ref_profile, Profile* mutable_profile) {
  DCHECK(mutable_profile != nullptr);
  // Should use AccelerateToExpectedSpeed if the current speed is lower
  // than expected_v.
  DCHECK_GE((*mutable_profile)[brake_first_ix].v, expected_v - kErrorTolerance);
  if (brake_first_ix >= static_cast<int>(mutable_profile->size())) {
    // If the user requests to brake after the last state,
    // there is nothing to adjust.
    return;
  }

  if (brake_first_ix >= brake_end_ix) {
    return;
  }

  // Remove the original profile after the first state that needs to be
  // adjusted.
  mutable_profile->erase(mutable_profile->begin() + brake_first_ix + 1,
                         mutable_profile->end());
  DCHECK_EQ(brake_first_ix + 1, mutable_profile->size());

  State mutable_state = (*mutable_profile)[brake_first_ix];
  JerkSequence braking_jerks;
  braking_jerks.reserve(8);
  bool has_reached_target_state =
      HasReachedConstSpeedState(mutable_state, expected_v);

  // Process -1: return early if the state is a steady const-speed state.
  if (has_reached_target_state) {
    ModifyProfileGivenJerkSequence(braking_jerks, brake_first_ix, brake_end_ix,
                                   dt, /*check_neg_speed=*/false,
                                   mutable_profile);
    return;
  }

  // Process 0: release brake right now if we can not avoid undershoot.
  int release_brake_steps = 0;
  JerkSequence process_0_release_brake_jerks;
  const bool process_0_can_release =
      CanReleaseBrakeToZeroAccelWithoutUndershoot(
          limits, mutable_profile->back(), dt, expected_v,
          /*feasible_initial_min_accel=*/nullptr, &release_brake_steps,
          &process_0_release_brake_jerks);
  if (!process_0_can_release) {
    if (!math::NearZero(expected_v)) {
      const int process_0_end_ix = brake_first_ix + release_brake_steps;
      ModifyProfileGivenJerkSequence(
          process_0_release_brake_jerks, brake_first_ix, process_0_end_ix, dt,
          /*check_neg_speed=*/false, mutable_profile);
      // After finishing release brake, we should already been undershoot.
      DCHECK_LE(mutable_profile->back().v, expected_v + kErrorTolerance);
      // Start to accelerate from here.
      // TODO(tienan): see if we can just use
      // AccelerateToExpectedSpeedUnderReference() from here.
      GenerateProfileToExpectedSpeed(
          *DCHECK_NOTNULL(nullable_sampled_ref_profile), process_0_end_ix,
          brake_end_ix, limits, dt, expected_v, mutable_profile);
      return;
    }
    // If the expected speed is zero, We should just use max jerk and ramp up.
    int brake_steps = 0;
    // TODO(tienan): see if we can reuse the braking jerks from the above
    // CanReleaseBrakeToZeroAccelWithoutUndershoot().
    const bool can_release = ReleaseBrakeToZeroAccelToExpectedSpeed(
        limits, dt, /*expected_v=*/0.0, /*check_neg_accel=*/false,
        mutable_state, &brake_steps, &braking_jerks);
    if (!can_release) {
      DVLOG(kSpeedVlogDebugLevel)
          << "ERROR: can not release from initial state: "
          << mutable_state.DebugString();
    }
    DCHECK(HasReachedSteadyState(mutable_state))
        << mutable_state.DebugString() << "; initial state: "
        << (*mutable_profile)[brake_first_ix].DebugString();

    ModifyProfileGivenJerkSequence(braking_jerks, brake_first_ix, brake_end_ix,
                                   dt, /*check_neg_speed=*/false,
                                   mutable_profile);
    return;
  }

  // Process 1: release gas and get on brake.
  const int process_1_end_ix =
      brake_first_ix +
      ReleaseGasAndGetOnBrake(limits, dt, expected_v, mutable_state,
                              &has_reached_target_state, &braking_jerks);
  // Early return if the process 1 duration is longer than what is requested,
  // or we have already reached the expected const-speed state.
  if (process_1_end_ix >= brake_end_ix || has_reached_target_state) {
    // Do not need to check neg speed if reach_expected_v (we can accept
    // slightly speed error for a const-speed state from
    // JerkTowardsSpeedAndAccelInThreeStages).
    ModifyProfileGivenJerkSequence(
        braking_jerks, brake_first_ix, brake_end_ix, dt,
        /*check_neg_speed=*/has_reached_target_state, mutable_profile);
    return;
  }
  // Process 2: go to min_accel.
  bool can_release_wo_undershoot_expected_v = false;
  const int process_2_end_ix =
      process_1_end_ix + BrakeToMinAccel(limits, dt, expected_v, mutable_state,
                                         &can_release_wo_undershoot_expected_v,
                                         &braking_jerks);
  // If we will undershoot expected_v at process 2, this process should
  // have zero step.
  DCHECK(can_release_wo_undershoot_expected_v ||
         process_2_end_ix == process_1_end_ix);
  if (process_2_end_ix >= brake_end_ix) {
    ModifyProfileGivenJerkSequence(braking_jerks, brake_first_ix, brake_end_ix,
                                   dt, /*check_neg_speed=*/true,
                                   mutable_profile);
    return;
  }
  // Process 3: hold at the min_accel, jerk stays at zero. At each state,
  // check if we can release brake to get to zero acceleration without
  // undershooting expected speed. This proceeds until we will undershoot
  // if not release.
  int process_3_brake_steps = 0;
  State prev_state = mutable_state;
  while (CanReleaseBrakeToZeroAccelWithoutUndershoot(limits, mutable_state, dt,
                                                     expected_v)) {
    prev_state = mutable_state;
    // Early return if we already reach the brake_end_ix.
    if (process_2_end_ix + process_3_brake_steps >= brake_end_ix) {
      braking_jerks.push_back(JerkAndSteps({0.0, process_3_brake_steps}));
      // We should ensure we are holding correctly at positive speed.
      DCHECK_GE(mutable_state.v, -kErrorTolerance);
      ModifyProfileGivenJerkSequence(braking_jerks, brake_first_ix,
                                     brake_end_ix, dt,
                                     /*check_neg_speed=*/true, mutable_profile);
      return;
    }
    process_3_brake_steps += 1;
    const double neg_v_min_j =
        MinJerkToAvoidNegativeSpeedAndDisplacement(dt, mutable_state);
    // We should release brake now to avoid negative speed and displacement.
    if (neg_v_min_j > 0.0) {
      // Add one step to proc3_brake_steps so we can -1 later.
      break;
    }
    // At this point, we can still 1) release brake to get to zero accel without
    // undershoot, 2) do not need to immediately release brake to avoid negative
    // speed. Keep holding at max deceleration aka zero jerk.
    mutable_state.j = 0.0;
    mutable_state.MoveInPlaceNonBackward(dt, /*check_neg_speed=*/true,
                                         /*allowed_error=*/kPositionError);
  }
  mutable_state = prev_state;
  braking_jerks.push_back(
      JerkAndSteps({0.0, std::max(process_3_brake_steps - 1, 0)}));
  const int process3_end_ix =
      process_2_end_ix + std::max(process_3_brake_steps - 1, 0);

  // Process 4: release brake to zero acceleration to the expected speed.
  int process_4_brake_steps = 0;
  JerkSequence release_brake_jerks;
  const bool success = ReleaseBrakeToZeroAccelToExpectedSpeed(
      limits, dt, expected_v, /*check_neg_accel=*/true, mutable_state,
      &process_4_brake_steps, &release_brake_jerks);
  braking_jerks.insert(braking_jerks.end(), release_brake_jerks.begin(),
                       release_brake_jerks.end());
  const int process4_end_ix = process3_end_ix + process_4_brake_steps;
  if (process4_end_ix >= brake_end_ix) {
    ModifyProfileGivenJerkSequence(braking_jerks, brake_first_ix, brake_end_ix,
                                   dt, /*check_neg_speed=*/true,
                                   mutable_profile);
    return;
  }
  if (!success) {
    // If we cannot reach the expected speed after the process 4 yet.
    // Recursively call the function to reach the goal state.
    ModifyProfileGivenJerkSequence(braking_jerks, brake_first_ix,
                                   process4_end_ix, dt,
                                   /*check_neg_speed=*/true, mutable_profile);
    if (nullable_sampled_ref_profile == nullptr) {
      DCHECK(math::NearZero(expected_v));
      GenerateBrakeProfile(process4_end_ix, brake_end_ix, limits, dt,
                           mutable_profile);
    } else {
      // TODO(tienan): see if we can just use
      // GenerateBrakeProfileToExpectedSpeed for a stricter recursion
      // definition.
      GenerateProfileToExpectedSpeed(
          *DCHECK_NOTNULL(nullable_sampled_ref_profile), process4_end_ix,
          brake_end_ix, limits, dt, expected_v, mutable_profile);
    }
    return;
  }
  // Check process_4_end_ix equals to (mutable_state.t -profile front t)/dt.
  // With this, we allow error that is smaller than sampling time.
  const int diff_index = math::CeilToIntWithTol(
      (mutable_state.t - mutable_profile->front().t) / dt);
  DCHECK_EQ(process4_end_ix, diff_index)
      << "process_4_end_ix: " << process4_end_ix << "; dt: " << dt
      << "; time diff: " << mutable_state.t - mutable_profile->front().t
      << "; diff_index: " << diff_index << "; brake_end_ix: " << brake_end_ix;
  ModifyProfileGivenJerkSequence(braking_jerks, brake_first_ix, brake_end_ix,
                                 dt, /*check_neg_speed=*/false,
                                 mutable_profile);
  // The final state should satisfies the requirement after the last process.
  DCHECK(HasReachedConstSpeedState((*mutable_profile)[brake_end_ix], expected_v,
                                   kBrakeToStopSmallVToleranceInMps))
      << "final state is: " << (*mutable_profile)[brake_end_ix].DebugString()
      << " expected_v is: " << expected_v;
}

void GenerateStopProfileWithLimits(const State& planning_init_state,
                                   int num_steps, const Limits& limits,
                                   Profile* stopped_profile) {
  DCHECK_NOTNULL(stopped_profile)->reserve(num_steps);
  stopped_profile->push_back(planning_init_state);
  GenerateBrakeProfile(/*brake_first_ix=*/0,
                       /*brake_end_ix=*/constants::kTrajectoryStateNum, limits,
                       /*dt=*/constants::kTrajectoryIntervalInSec,
                       stopped_profile);
}

Profile GenerateComfortStoppingProfile(const State& initial_state,
                                       int num_steps) {
  Profile comfort_stopping_profile;
  DCHECK_LE(initial_state.v, kEgoNearStopStateTolerance);
  DCHECK_LE(initial_state.a, kEgoNearStopStateTolerance);

  const LimitRange comfort_range(/*min_val=*/-kComfortJerkLimitNearStop,
                                 /*max_val=*/kComfortJerkLimitNearStop);
  Limits comfort_limit_for_stopping(
      /*brake_a=*/comfort_range, /*accel_a=*/comfort_range,
      /*brake_j=*/comfort_range,
      /*accel_j=*/comfort_range, /*max_v=*/kComfortJerkLimitNearStop,
      /*max_brake2accel_j=*/kComfortJerkLimitNearStop);

  GenerateStopProfileWithLimits(initial_state, num_steps,
                                comfort_limit_for_stopping,
                                &comfort_stopping_profile);

  return comfort_stopping_profile;
}

// This function splits the brake into two steps: Before switch time, use
// regular limits to brake; After switch time, use elevated limits to brake if
// for risk search profile.
void GenerateBrakeProfileWithProfileLimits(int brake_first_ix, int brake_end_ix,
                                           const ProfileLimits& profile_limits,
                                           double dt, bool for_risk_search,
                                           Profile* mutable_profile) {
  if (!for_risk_search) {
    GenerateBrakeProfile(brake_first_ix, brake_end_ix,
                         profile_limits.GetLimitsBeforeSwitchTime(), dt,
                         mutable_profile);
    return;
  }

  // For risk tree profile.

  // Brake using regular limits before the switch time first.
  GenerateBrakeProfile(
      brake_first_ix, std::min(brake_end_ix, profile_limits.switch_ix()),
      profile_limits.GetLimitsBeforeSwitchTime(), dt, mutable_profile);
  // Brake using elevated limits after the switch time.
  GenerateBrakeProfile(std::max(brake_first_ix, profile_limits.switch_ix()),
                       brake_end_ix, profile_limits.GetLimitsAfterSwitchTime(),
                       dt, mutable_profile);
}

bool CanBrakeForConflict(const int brake_first_ix, const Conflict& conflict,
                         const ProfileLimits& profile_limits,
                         const Profile& profile, const double dt,
                         const bool for_risk_search) {
  Profile mutable_profile = profile;
  GenerateBrakeProfileWithProfileLimits(brake_first_ix, conflict.time_ix,
                                        profile_limits, dt, for_risk_search,
                                        &mutable_profile);
  // Need to make sure brake profile is at least as long as conflict.t_ix.
  return mutable_profile[conflict.time_ix].x < conflict.pos;
}

bool BrakeForYieldConflict(const Conflict& conflict,
                           const ProfileLimits& profile_limits,
                           const int start_search_ix, const double dt,
                           const bool for_risk_search, Profile* mutable_profile,
                           int* first_change_ix, double* x_resolve) {
  DCHECK(mutable_profile);
  DCHECK(first_change_ix);
  DCHECK(x_resolve);
  // Initialize to no solution.
  bool succeed = false;
  *first_change_ix = -1;
  // Start from start_search_ix, use a greedy binary search to find the latest
  // index that we can brake to avoid the conflict.
  {
    int brake_first_ix = start_search_ix;
    // TODO(shiying): consider the case when a timemap entry has time
    // offset smaller than sampling time.
    int brake_last_ix = conflict.time_ix;
    while (brake_first_ix <= brake_last_ix) {
      int trial_ix = static_cast<int>((brake_first_ix + brake_last_ix) / 2);
      if (CanBrakeForConflict(trial_ix, conflict, profile_limits,
                              *mutable_profile, dt, for_risk_search)) {
        brake_first_ix = trial_ix + 1;
        *first_change_ix = trial_ix;
        succeed = true;
      } else {
        brake_last_ix = trial_ix - 1;
      }
    }  // end of while-loop
  }

  // Return early if we fail.
  if (!succeed) {
    // Even we fail, still generate the extreme case brake profile for debug.
    GenerateBrakeProfileWithProfileLimits(/*brake_first_ix=*/0,
                                          conflict.time_ix, profile_limits, dt,
                                          for_risk_search, mutable_profile);

    *x_resolve = mutable_profile->at(conflict.time_ix).x;
    *first_change_ix = -1;
    return false;
  }

  // We can avoid conflict even we do not brake.
  if (*first_change_ix == conflict.time_ix) {
    // Start braking from conflict index does not change the profile before
    // conflict, so it should be always true that the profile can avoid the
    // conflict already.
    DCHECK(CanBrakeForConflict(conflict.time_ix, conflict, profile_limits,
                               *mutable_profile, dt, for_risk_search));
    *first_change_ix = mutable_profile->size();
  } else {
    // Actually modify the profile if we succeed.
    GenerateBrakeProfileWithProfileLimits(*first_change_ix, conflict.time_ix,
                                          profile_limits, dt, for_risk_search,
                                          mutable_profile);
    *x_resolve = mutable_profile->at(conflict.time_ix).x;
  }

  // Verify that the profile avoids the conflict;
  DCHECK_LT(conflict.time_ix, mutable_profile->size());
  DCHECK_LT((*mutable_profile)[conflict.time_ix].x,
            conflict.pos + kErrorTolerance)
      << "conflict.time_ix: " << conflict.time_ix;

  return true;
}

bool CanAccelWithoutConflict(const Profile& sampled_ref_profile,
                             const int trial_ix,
                             const Conflict& prev_yield_conflict,
                             const ProfileLimits& profile_limits,
                             const Profile& profile, const double dt,
                             const int n_steps, const bool for_risk_search) {
  Profile mutable_profile = profile;
  AccelerateTowardsReferenceProfileWithProfileLimits(
      sampled_ref_profile, profile_limits, trial_ix, dt, for_risk_search,
      n_steps, /*max_v=*/std::nullopt, mutable_profile);
  return mutable_profile[prev_yield_conflict.time_ix].x <
         prev_yield_conflict.pos;
}

bool EarliestAccelWhileAvoidYieldConflict(
    const Conflict& prev_yield_conflict, const ProfileLimits& profile_limits,
    const double dt, const bool for_risk_search,
    const Profile& sampled_ref_profile, const int n_steps,
    Profile& mutable_profile, int& earliest_accel_time_ix_for_adjust_for_pass) {
  // Initialize to no solution.
  bool succeed = false;
  // Between the earliest time ix that we can accelerate from (i.e. 0) and the
  // prev_yield_conflict time ix, do a greedy binary search to find the earliest
  // index that we can accel and avoid the conflict.

  // Since the initial |mutable_profile| is a brake profile, and the original
  // profile has already been adjusted for conflict, it must be conflict-free
  // when accel at prev_yield_conflict.time_ix.
  int earliest_feasible_accel_ix = prev_yield_conflict.time_ix;
  int right_accel_ix = prev_yield_conflict.time_ix;
  {
    int left_accel_ix = 0;
    while (left_accel_ix < right_accel_ix) {
      int trial_ix = static_cast<int>((left_accel_ix + right_accel_ix) / 2);
      if (CanAccelWithoutConflict(
              sampled_ref_profile, trial_ix, prev_yield_conflict,
              profile_limits, mutable_profile, dt, n_steps, for_risk_search)) {
        right_accel_ix = trial_ix;
        succeed = true;
      } else {
        left_accel_ix = trial_ix + 1;
      }
    }  // end of while-loop
  }

  if (!succeed) {
    // Set the index to profile size (currently 80) to indicate that the adjust
    // for pass is triggered, but it is not successful.
    earliest_accel_time_ix_for_adjust_for_pass = mutable_profile.size();
    return false;
  }

  earliest_feasible_accel_ix = right_accel_ix;
  // We cannot accel even at the the yield conflict time. This should never
  // happen because we have already adjusted the profile for the yield conflict.
  DCHECK(earliest_feasible_accel_ix < prev_yield_conflict.time_ix);
  earliest_accel_time_ix_for_adjust_for_pass = earliest_feasible_accel_ix;
  // Actually modify the profile if we succeed.
  AccelerateTowardsReferenceProfileWithProfileLimits(
      sampled_ref_profile, profile_limits, earliest_feasible_accel_ix, dt,
      for_risk_search, n_steps, /*max_v=*/std::nullopt, mutable_profile);
  return true;
}

// TODO(tienan): better unify the params and API of
// GenerateBrakeProfileToExpectedSpeed and AccelerateToExpectedSpeed.
void GenerateProfileToExpectedSpeed(const Profile& sampled_ref_profile,
                                    int modified_first_ix, int modified_end_ix,
                                    const Limits& limits, double dt,
                                    double expected_v,
                                    Profile* mutable_profile) {
  DCHECK_GT(mutable_profile->size(), modified_first_ix);
  const double first_state_speed = (*mutable_profile)[modified_first_ix].v;
  if (first_state_speed > expected_v) {
    GenerateBrakeProfileToExpectedSpeed(modified_first_ix, modified_end_ix,
                                        limits, dt, expected_v,
                                        &sampled_ref_profile, mutable_profile);
    return;
  }
  if (first_state_speed < expected_v) {
    AccelerateToExpectedSpeedUnderReference(
        sampled_ref_profile, limits, modified_first_ix, dt,
        /*n_horizon=*/modified_end_ix + 1, expected_v, mutable_profile);
    return;
  }
  DCHECK(math::IsApprox(first_state_speed, expected_v));
  const double first_state_a = (*mutable_profile)[modified_first_ix].a;
  if (first_state_a > 0.0) {
    GenerateBrakeProfileToExpectedSpeed(modified_first_ix, modified_end_ix,
                                        limits, dt, expected_v,
                                        &sampled_ref_profile, mutable_profile);
  } else {
    AccelerateToExpectedSpeedUnderReference(
        sampled_ref_profile, limits, modified_first_ix, dt,
        /*n_horizon=*/modified_end_ix + 1, expected_v, mutable_profile);
  }
}

void ReleaseBrakeForOneStep(const Limits& limits, double dt, Profile* profile) {
  DCHECK(profile != nullptr);
  DCHECK_GT(profile->size(), 0);
  DCHECK_LT(profile->back().a, kErrorTolerance);
  profile->back().j = limits.brake_j.max;
  State next_state =
      profile->back().MoveNonBackward(dt, /*check_neg_speed=*/false);
  profile->push_back(std::move(next_state));
}

Profile ResampleProfile(const Profile& reference_profile, const Limits& limits,
                        double dt, int n_horizon, int* start_ix) {
  int state_ix = -1;
  DCHECK(ProfileIsConsistent(reference_profile, &state_ix))
      << reference_profile[state_ix].DebugString() << " \n "
      << reference_profile[state_ix + 1].DebugString();

  // Extend the profile to the desired length.
  Profile profile = reference_profile;
  ExtendProfileByZeroJerkOrReleasingBrake(n_horizon, dt, limits, &profile);

  // Updates the profile such that all states are within limits.
  const double inv_dt = 1.0 / dt;
  // Skip the initial state and as it is the planning initial state and
  // can not be modified.
  DCHECK_GT(profile.size(), 1);
  State next_state = profile[0];
  for (size_t ix = 0; ix < profile.size(); ix++) {
    next_state.j = profile[ix].j;
    profile[ix] = next_state;
    auto& state = profile[ix];
    // Release as soon as possible if it can undershoot zero speed.
    const bool can_release = CanReleaseBrakeToZeroAccelWithoutUndershoot(
        limits, state, dt, /*expected_v=*/0.0);
    if (!can_release) {
      DCHECK_LT(state.a, kErrorTolerance);
      state.j = limits.brake_j.max;
      next_state =
          state.MoveNonBackward(dt, /*check_neg_speed=*/false, kPositionError);
      continue;
    }
    const double neg_v_min_j =
        MinJerkToAvoidNegativeSpeedAndDisplacement(dt, state);
    LimitRange jerk_limit;
    MinMaxJerkToAvoidExceedingAccel(limits, state.a, inv_dt, &jerk_limit);
    jerk_limit.min = std::max(neg_v_min_j, jerk_limit.min);
    if (jerk_limit.min <= jerk_limit.max) {
      state.j = math::Clamp(state.j, jerk_limit.min, jerk_limit.max);
    } else {
      // It is possible if the first few states are already out of limits. E.g.,
      // acceleration is positive and out of limits. Or, negative acceleration,
      // but can release.
      state.j = state.a < 0.0 ? jerk_limit.min : jerk_limit.max;
      DVLOG(kSpeedVlogDebugLevel)
          << "will overshoot. state: " << state.DebugString()
          << "; min jerk: " << jerk_limit.min
          << "; max jerk: " << jerk_limit.max << "; ix: " << ix
          << "brake a limits: " << limits.brake_a.DebugString()
          << "; accel a limits: " << limits.accel_a.DebugString()
          << "; brake j limits: " << limits.brake_j.DebugString()
          << "; accel j limits: " << limits.accel_j.DebugString();
      if (start_ix != nullptr) {
        *start_ix = ix + 2;
      }
    }

    next_state =
        state.MoveNonBackward(dt, /*check_neg_speed=*/true, kPositionError);
  }

  return profile;
}

void MoveNonBackwardOneStepGivenJerk(const Limits& limits, double dt,
                                     double jerk_sol, double min_jerk,
                                     double max_jerk,
                                     Profile& mutable_profile) {
  State& current_state = mutable_profile.back();
  // Make sure the min jerk does not violate the jerk limits.
  math::UpdateMax(
      current_state.a < 0.0 ? limits.brake_j.min : limits.accel_j.min,
      min_jerk);
  // Make sure that max jerk does not violate current discomfort
  // accel and jerk limits.
  UpdateMaxJerkToStayInLimits(limits, current_state, dt, max_jerk);
  if (min_jerk <= max_jerk) {
    jerk_sol = math::Clamp(jerk_sol, min_jerk, max_jerk);
  } else {
    // Will overshoot max speed or undershoot neg speed, just use max jerk or
    // min jerk.
    jerk_sol = current_state.a < 0.0 ? min_jerk : max_jerk;
    if (jerk_sol <
            (current_state.a < 0.0 ? limits.brake_j.min : limits.accel_j.min) ||
        jerk_sol >
            (current_state.a < 0.0 ? limits.brake_j.max : limits.accel_j.max)) {
      DVLOG(kSpeedVlogDebugLevel)
          << "might overshoot max speed or undershoot zero speed, state: "
          << "; min jerk: " << min_jerk << "; max jerk: " << max_jerk
          << ";jerk: " << jerk_sol
          << "; current_state: " << current_state.DebugString()
          << ";profile size: " << mutable_profile.size();
    }
  }
  current_state.j = jerk_sol;
  mutable_profile.push_back(current_state.MoveNonBackward(
      dt, /*check_neg_speed=*/true, kPositionError));
}

void AccelerateTowardsReferenceProfile(const Profile& sampled_ref_profile,
                                       const Limits& limits,
                                       const int profile_first_ix,
                                       const double dt, const int n_horizon,
                                       const std::optional<double>& max_v,
                                       Profile* mutable_profile,
                                       const bool is_ref_reachable) {
  DCHECK(mutable_profile != nullptr);
  DCHECK_GE(sampled_ref_profile.size(), n_horizon + 2);
  DCHECK_LT(profile_first_ix, mutable_profile->size())
      << "Should not accelerate from a not existing state";
  if (profile_first_ix >= n_horizon) {
    return;
  }
  if (mutable_profile->empty()) {
    // Resample reference profile such that it has size as n_horizon and starts
    // from immutable time with sampling interval dt.
    *mutable_profile = sampled_ref_profile;
    mutable_profile->erase(mutable_profile->begin() + n_horizon,
                           mutable_profile->end());
    return;
  }
  // NOTE(Tingran): Initial accel may be different at different discomfort
  // levels due to init state regulation. So we only check x and v.
  DCHECK(math::IsApprox(sampled_ref_profile.front().x,
                        mutable_profile->front().x));
  DCHECK(math::IsApprox(sampled_ref_profile.front().v,
                        mutable_profile->front().v));

  // Directly return ref profile to save computation. If reference is not
  // reachable (e.g. for risk search), we cannot early return.
  if (profile_first_ix == 0 && !max_v.has_value() && is_ref_reachable) {
    *mutable_profile = sampled_ref_profile;
    mutable_profile->erase(mutable_profile->begin() + n_horizon,
                           mutable_profile->end());
    return;
  }
  if (profile_first_ix + 1 < static_cast<int>(mutable_profile->size())) {
    // Only keep the first profile_first_ix + 1 states, as we will be generating
    // from profile_first_ix later.
    mutable_profile->erase(mutable_profile->begin() + profile_first_ix + 1,
                           mutable_profile->end());
  }

  DCHECK_EQ(profile_first_ix + 1, mutable_profile->size());

  // Generate profile towards reference profile, also make sure, at any time, we
  // may start braking to avoid overshoot max speed.
  while (static_cast<int>(mutable_profile->size()) < n_horizon) {
    const bool can_release = CanReleaseBrakeToZeroAccelWithoutUndershoot(
        limits, mutable_profile->back(), dt, /*expected_v=*/0.0);
    if (!can_release) {
      ReleaseBrakeForOneStep(limits, dt, mutable_profile);
      continue;
    }
    // Find the next state that has x larger than current_state.x.
    const State& current_state = mutable_profile->back();
    auto goal_iter = std::upper_bound(
        sampled_ref_profile.begin(), sampled_ref_profile.end(), current_state.x,
        [](double state_x, const State& state) { return state_x < state.x; });
    if (goal_iter == sampled_ref_profile.end()) {
      MoveForwardOneStepWithZeroJerk(limits, dt, mutable_profile);
      continue;
    }
    DCHECK(goal_iter != sampled_ref_profile.end());
    DCHECK_GT(goal_iter->x, current_state.x - kErrorTolerance);
    double goal_v = goal_iter->v;
    double goal_a = goal_iter->a;
    if (max_v.has_value() && math::UpdateMin(*max_v, goal_v)) {
      goal_a = 0.0;
    }
    double jerk1 = NAN, jerk2 = NAN;
    JerkTowardsSpeedAndAccelInTwoSteps(dt, goal_v, goal_a, limits,
                                       current_state, &jerk1, &jerk2);
    const int dist = std::distance(goal_iter, sampled_ref_profile.end() - 1);
    const int look_ahead_ix = std::min(12, dist);
    auto look_ahead_iter = goal_iter + look_ahead_ix;
    DCHECK(look_ahead_iter < sampled_ref_profile.end());
    double look_ahead_jerk = 0.0;
    State look_ahead_state = *look_ahead_iter;
    if (max_v.has_value()) {
      look_ahead_state.v = std::min(look_ahead_state.v, *max_v);
    }
    const bool can_find = MaxJerkToAvoidExceedingGoalSpeed(
        current_state, look_ahead_state, dt, &look_ahead_jerk);
    const double max_v_jerk =
        can_find
            ? look_ahead_jerk
            : (current_state.a < 0.0 ? limits.brake_j.max : limits.accel_j.max);
    const double neg_v_min_jerk =
        MinJerkToAvoidNegativeSpeedAndDisplacement(dt, current_state);
    math::UpdateMax(neg_v_min_jerk, jerk1);
    // Use jerk2 instead if jerk1 if jerk1 practically has no impact.
    // See more details on why we are doing this in
    // https://docs.google.com/document/d/19VPO0T3dJ5at2PmSya7Zd88D1zFFKzwNSHis9PdUfmk
    if (math::IsApprox(jerk1, 0.0)) {
      MoveNonBackwardOneStepGivenJerk(limits, dt, jerk2, neg_v_min_jerk,
                                      max_v_jerk, *mutable_profile);
    } else {
      MoveNonBackwardOneStepGivenJerk(limits, dt, jerk1, neg_v_min_jerk,
                                      max_v_jerk, *mutable_profile);
    }
  }
}

void AccelerateTowardsReferenceProfileWithProfileLimits(
    const Profile& sampled_ref_profile, const ProfileLimits& profile_limits,
    const int profile_first_ix, const double dt, const bool for_risk_search,
    const int n_horizon, const std::optional<double>& max_v,
    Profile& mutable_profile) {
  if (!for_risk_search) {
    AccelerateTowardsReferenceProfile(
        sampled_ref_profile, profile_limits.GetLimitsBeforeSwitchTime(),
        profile_first_ix, dt, n_horizon, max_v, &mutable_profile,
        /*is_ref_reachable=*/true);
    return;
  }

  // For risk tree profile.
  if (profile_limits.switch_ix() > 0) {
    // Phase 1: Accel using regular limits before the switch time first.
    // NOTE: This function takes in number of states we need to generate. If we
    // want to change control starting from switch_ix, we need to generate
    // switch_ix + 1 number of states, aka, including switch state itself.
    const int phase_1_horizon = profile_limits.switch_ix() + 1;
    AccelerateTowardsReferenceProfile(
        sampled_ref_profile, profile_limits.GetLimitsBeforeSwitchTime(),
        profile_first_ix, dt, std::min(phase_1_horizon, n_horizon), max_v,
        &mutable_profile, /*is_ref_reachable=*/false);
  }
  // Phase 2: Accel using elevated limits after the switch time.
  const int phase_2_profile_first_ix =
      std::max(profile_first_ix, profile_limits.switch_ix());
  AccelerateTowardsReferenceProfile(
      sampled_ref_profile, profile_limits.GetLimitsAfterSwitchTime(),
      phase_2_profile_first_ix, dt, n_horizon, max_v, &mutable_profile,
      /*is_ref_reachable=*/phase_2_profile_first_ix == 0);
  DCHECK_EQ(mutable_profile.size(), n_horizon);
}

void AccelerateToExpectedSpeed(const Limits& limits, int profile_first_ix,
                               double dt, int n_horizon, double goal_v,
                               Profile* mutable_profile) {
  DCHECK(false) << "AccelerateToExpectedSpeed is no longer a supported API."
                   " Use AccelerateToExpectedSpeedUnderReference instead.";
  DCHECK(mutable_profile != nullptr);
  // Should use GenerateBrakeProfileToExpectedSpeed if the current speed
  // is higher than expected_v.
  DCHECK_LE((*mutable_profile)[profile_first_ix].v, goal_v + kErrorTolerance);

  mutable_profile->erase(mutable_profile->begin() + profile_first_ix + 1,
                         mutable_profile->end());
  DCHECK_EQ(profile_first_ix + 1, mutable_profile->size());

  // Handle the case when we already can not avoid overshoot based on the first
  // state.
  if (GenerateProfileWithInevitableOvershoot(
          limits, profile_first_ix, dt, n_horizon, goal_v,
          /*nullable_sampled_ref_profile=*/nullptr, mutable_profile)) {
    return;
  }

  while (static_cast<int>(mutable_profile->size()) < n_horizon) {
    State& last_state = mutable_profile->back();
    // Handle the bad initial states with low speed and negative acceleration.
    const bool can_release_brake = CanReleaseBrakeToZeroAccelWithoutUndershoot(
        limits, last_state, dt, /*expected_v=*/0.0);
    if (!can_release_brake) {
      ReleaseBrakeForOneStep(limits, dt, mutable_profile);
      continue;
    }
    // No need to accelerate or decelerate more if already reaching goal state.
    if (HasReachedConstSpeedState(last_state, goal_v)) {
      last_state.j = 0.0;
      mutable_profile->push_back(last_state.MoveNonBackward(
          dt, /*check_neg_speed=*/true, kPositionError));
      continue;
    }

    // Calculate the next control with two-steps look forward.
    double jerk1 = NAN, jerk2 = NAN;
    JerkTowardsSpeedAndAccelInTwoSteps(dt, goal_v, /*goal_a=*/0.0, limits,
                                       last_state, &jerk1, &jerk2);
    last_state.j = jerk1;
    State new_state = last_state.MoveNonBackward(dt, /*check_neg_speed=*/true,
                                                 kPositionError);

    // Check if the |new_state| from the above two-steps look-forward leads to
    // overshoot. If so, start from |last_state| to re-calculate the jerk
    // sequence.
    const bool can_release_gas =
        CanReleaseGasToZeroAccelWithoutOvershoot(limits, new_state, dt, goal_v);
    if (can_release_gas) {
      mutable_profile->push_back(new_state);
    } else {
      // we need to release gas immediately from |last_state| until the accel
      // is just a small positive value.
      new_state = last_state;
      if (new_state.a > 0.0) {
        const double release_gas_j = limits.accel_j.min;
        const double inv_dt = 1.0 / dt;
        const int release_gas_steps =
            math::FloorToIntWithTol(-inv_dt * last_state.a / release_gas_j);
        if (release_gas_steps > 0) {
          for (int i = 0; i < release_gas_steps; ++i) {
            new_state.j = release_gas_j;
            new_state = new_state.MoveNonBackward(dt, /*check_neg_speed=*/true,
                                                  kPositionError);
            mutable_profile->push_back(new_state);
          }
          continue;
        }
      }
      // Finish the final part to converge to the goal state using three-stages
      // look-forward.
      double jerk3 = NAN;
      int jerk1_steps = 0, jerk2_steps = 0;
      JerkTowardsSpeedAndAccelInThreeStages(dt, goal_v, /*goal_a=*/0.0, limits,
                                            new_state, &jerk1, &jerk2, &jerk3,
                                            &jerk1_steps, &jerk2_steps);
      for (int i = 0; i < jerk1_steps; ++i) {
        new_state.j = jerk1;
        new_state = new_state.MoveNonBackward(dt, /*check_neg_speed=*/true,
                                              kPositionError);
        mutable_profile->push_back(new_state);
      }
      for (int i = 0; i < jerk2_steps; ++i) {
        new_state.j = jerk2;
        new_state = new_state.MoveNonBackward(dt, /*check_neg_speed=*/true,
                                              kPositionError);
        mutable_profile->push_back(new_state);
      }
      new_state.j = jerk3;
      // Allow negative speed in the third step as goal_v can not be strictly
      // enforced by JerkTowardsSpeedAndAccelInThreeStages. So there could
      // be slight speed overshoot/undershoot because of jerk3.
      new_state.MoveInPlaceNonBackward(dt, /*check_neg_speed=*/false,
                                       kPositionError);
      mutable_profile->push_back(new_state);
      DCHECK(math::IsApprox(new_state.v, goal_v, kOvershootSpeedTolerance))
          << DUMP_TO_STREAM(new_state.v, goal_v);
      DCHECK(
          math::IsApprox(new_state.a, 0.0, kErrorTolerance))  // 0.0 is goal_a
          << DUMP_TO_STREAM(new_state.a);
    }
  }
}

void AccelerateToExpectedSpeedUnderReference(const Profile& sampled_ref_profile,
                                             const Limits& limits,
                                             int profile_first_ix, double dt,
                                             int n_horizon, double goal_v,
                                             Profile* mutable_profile) {
  DCHECK(mutable_profile != nullptr);
  // Should use GenerateBrakeProfileToExpectedSpeed if the current speed
  // is higher than expected_v.
  DCHECK_LE((*mutable_profile)[profile_first_ix].v, goal_v + kErrorTolerance);

  mutable_profile->erase(mutable_profile->begin() + profile_first_ix + 1,
                         mutable_profile->end());
  DCHECK_EQ(profile_first_ix + 1, mutable_profile->size());

  // Handle the case when we already can not avoid overshoot based on the first
  // state.
  if (GenerateProfileWithInevitableOvershoot(
          limits, profile_first_ix, dt, n_horizon, goal_v, &sampled_ref_profile,
          mutable_profile)) {
    return;
  }

  AccelerateTowardsReferenceProfile(sampled_ref_profile, limits,
                                    profile_first_ix, dt, n_horizon, goal_v,
                                    mutable_profile);
}

// TODO(shiying): to make things simple, any time a profile is adjusted
// down, we accelerate to the reference profile to the end of time horizon, but
// for efficiency, we only need to accelerate it until it reaches any time map
// entry.
bool FindFirstConflictForProfile(
    const pb::TreeSearchType::Enum tree_search_type, const TimeMap& time_map,
    const Profile& profile, const std::vector<Constraint>& constraints,
    const std::vector<Decision>& decisions,
    const int search_for_conflict_start_ix,
    const absl::flat_hash_map<int, GapAlignInfo>& gap_align_info_map,
    const int tree_switch_time_ix, const bool adjust_for_tree_branch,
    Conflict* conflict) {
  TRACE_EVENT_INSTANT(planner, FindFirstConflictForProfile);
  const double t_horizon = time_map.dt() * time_map.steps();
  // Scan through all the pass entries if no conflict is found, scan through all
  // yield conflicts.
  if (FindAndPopulateConflict(
          /*pass=*/true, tree_search_type, profile, constraints, decisions,
          gap_align_info_map, time_map, tree_switch_time_ix, t_horizon,
          adjust_for_tree_branch, search_for_conflict_start_ix, conflict)) {
    return true;
  }
  if (FindAndPopulateConflict(
          /*pass=*/false, tree_search_type, profile, constraints, decisions,
          gap_align_info_map, time_map, tree_switch_time_ix, t_horizon,
          adjust_for_tree_branch, search_for_conflict_start_ix, conflict)) {
    return true;
  }
  return false;
}

void ExtendProfileByZeroJerkOrReleasingBrake(int n_horizon, double dt,
                                             const speed::Limits& limits,
                                             Profile* mutable_profile) {
  DCHECK(mutable_profile != nullptr);
  while (static_cast<int>(mutable_profile->size()) < n_horizon) {
    // There are two cases where we can not release: 1) we are
    // decelerating and can not avoid negative speed, 2) we are already
    // at negative speed, note this is possible due to the negative
    // speed tolerance from the opt profile in the last cycle (refer to
    // kp-2885649).
    const State& last_state = mutable_profile->back();
    const bool can_release = CanReleaseBrakeToZeroAccelWithoutUndershoot(
        limits, last_state, dt, /*expected_v=*/0.0);
    // Only in the case 1, aka. our speed is positive, we need to
    // release brake for one step.

    if (!can_release && last_state.v >= 0.0) {
      ReleaseBrakeForOneStep(limits, dt, mutable_profile);
    } else {
      // When the 'reference_profile' is shorter than desired time horizon,
      // extend the last state assuming constant jerk from the
      // reference_profile's last state.
      // We do not check negative speed to tolerate inconsistent opt profile for
      // warm start.
      MoveForwardOneStepWithZeroJerk(limits, dt, mutable_profile,
                                     /*check_neg_speed=*/false);
    }
  }
}

std::optional<Profile> GenerateWarmStartSpeedProfileFromLastProfile(
    const Profile& last_profile, double plan_init_time, double plan_init_speed,
    double plan_init_accel, int n_horizon, double dt,
    const speed::Limits& limits, double min_speed_tolerance,
    double min_odom_tolerance, bool check_speed_accel_error) {
  // NOTE(Tingran): We do not check if warm start profile is consistent or not,
  // as it can be from search or stop profile or from opt profile, while opt
  // profile can have small negative speed so it is not strictly consistent.

  DCHECK_GT(last_profile.size(), 0);
  State plan_init_state;
  int unused_time_ix = -1;
  // Check plan init state.
  if (!GetStateAtTime(last_profile, plan_init_time, &plan_init_state,
                      unused_time_ix)) {
    return std::nullopt;
  }

  if (check_speed_accel_error &&
      (!math::IsApprox(plan_init_state.v, plan_init_speed, kSpeedError) ||
       !math::IsApprox(plan_init_state.a, plan_init_accel, kAccelError))) {
    return std::nullopt;
  }

  const double plan_init_odom = plan_init_state.x;
  plan_init_state.x = 0.0;

  Profile warm_start_speed_profile;
  warm_start_speed_profile.push_back(plan_init_state);

  State state;
  // Get States from last profile.
  for (int i = 1; i < n_horizon; ++i) {
    const double new_t = plan_init_time + i * dt;
    // GetStateAtTime returns false if new_t out of range.
    if (!GetStateAtTime(last_profile, new_t, &state, unused_time_ix)) {
      break;
    }
    state.x -= plan_init_odom;
    if (!IsStateFeasible(state, min_speed_tolerance, min_odom_tolerance)) {
      return std::nullopt;
    }
    // TODO(huanming) Add consistent and monotonic check here.
    warm_start_speed_profile.push_back(state);
  }

  // Warm start speed profile is too short to use.
  if (warm_start_speed_profile.size() < kMinLastProfileHorizon) {
    return std::nullopt;
  }

  // If last trajectory is short, move forward with zero jerk.
  ExtendProfileByZeroJerkOrReleasingBrake(n_horizon, dt, limits,
                                          &warm_start_speed_profile);

  // TODO(huanming) Move those check to for-loop.
  // Warm start speed profile should be monotonic.
  if (!IsProfileMonotonic(warm_start_speed_profile, min_speed_tolerance,
                          min_odom_tolerance)) {
    return std::nullopt;
  }
  return warm_start_speed_profile;
}
}  // namespace speed
}  // namespace planner
