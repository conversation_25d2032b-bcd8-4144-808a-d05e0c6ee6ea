
#include "lateral_function/lane_center/lane_departure.h"

#include <gtest/gtest.h>

TEST(CheckLeftLaneDeparture, CheckLeftLaneDepartureTest) {
  bool left_lane_exist = true;
  float left_lane_y0 = 1.75F;
  float left_lane_heading = -0.1F;
  float ego_long_velocity = 9.0F;

  // left lane departure by time to crossing
  EXPECT_TRUE(CheckLeftLaneDeparture(left_lane_exist, left_lane_y0,
                                     left_lane_heading, ego_long_velocity));
}

TEST(CheckRightLaneDeparture, CheckRightLaneDepartureTest) {
  bool right_lane_exist = true;
  float right_lane_y0 = -1.75F;
  float right_lane_heading = 0.1F;
  float ego_long_velocity = 9.0F;

  // right lane departure by time to crossing
  EXPECT_TRUE(CheckRightLaneDeparture(right_lane_exist, right_lane_y0,
                                      right_lane_heading, ego_long_velocity));
}
