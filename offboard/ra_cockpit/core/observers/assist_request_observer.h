#ifndef OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_ASSIST_REQUEST_OBSERVER_H_
#define OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_ASSIST_REQUEST_OBSERVER_H_

#include <functional>
#include <memory>

#include "../data_observer.h"
#include "planner_protos/remote_assist.pb.h"
#include "teleassist_protos/teleassist.pb.h"

namespace remote_assist {

class AssistRequestObserver : public IDataObserver {
 public:
  using AssistRequestCallback = std::function<void(
      std::shared_ptr<planner::pb::AssistRequest>, DataChannel)>;

  explicit AssistRequestObserver(AssistRequestCallback callback);

  bool IsInterestedIn(
      const teleassist::pb::TeleassistRequest& message) override;

  std::shared_ptr<google::protobuf::Message> OnMessage(
      const teleassist::pb::TeleassistRequest& message,
      DataChannel data_channel) override;

 private:
  AssistRequestCallback callback_;
};

}  // namespace remote_assist

#endif  // OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_ASSIST_REQUEST_OBSERVER_H_
