#include "planner/speed/reasoning/reasoner/lane_change_speed_reasoner/lane_change_speed_reasoner.h"

#include <boost/scope_exit.hpp>

#include <algorithm>
#include <limits>
#include <sstream>
#include <string>
#include <utility>
#include <vector>

#include <absl/strings/str_cat.h>
#include <absl/strings/str_format.h>

#include "log_utils/log_macros.h"
#include "math/constants.h"
#include "math/range.h"
#include "planner/behavior/util/lane_common/lane_change_instance.h"
#include "planner/decoupled_maneuvers/lane_change/lane_change_common.h"
#include "planner/decoupled_maneuvers/lane_change/lane_change_object_info.h"
#include "planner/speed/agent_reaction/agent_reaction_strategy/lane_change_slow_down_strategy.h"
#include "planner/speed/agent_reaction/reaction_utils.h"
#include "planner/speed/reasoning/reasoner/lane_change_speed_reasoner/lane_change_speed_reasoner_util.h"
#include "planner_protos/lane_change.pb.h"
#include "planner_protos/speed_reasoning_debug.pb.h"
#include "prediction_protos/predicted_trajectory.pb.h"
#include "rt_event/rt_event.h"
#include "voy_rt_event/rt_event_planner.h"

namespace planner {
namespace speed {
namespace reasoner {

namespace {
// TODO(howard): move the constants into lane change speed reasoner config.
// We further limit the max allowed discomfort to pass an agent when it is in
// front of ego. This is to avoid hard acceleration to pass the agent during gap
// align.
constexpr double kGapAlignPassMaxDiscomfortForLeadingAgent = 0.3;
constexpr int kSpeedLimitIdShiftNumber = 100;
// The max allowed amount of extra time of pass active time more than yield
// active time. It indicates the extra effort we can afford to pass a gap align
// constraint to gain more progress than to yield a gap align constraint. The
// larger the value is, the more progress we will prefer but in the meantime we
// are being aggressive to pass.
constexpr double kMaxAllowedExtraTimeForPassInSecond = 1.0;
constexpr double kDistanceToleranceInMeter = 0.1;
// Computes distance from ego path to target lane left/right boundary.
[[maybe_unused]] double ComputeLateralSearchRange(
    const speed::TrajectoryInfo& trajectory_info,
    const LaneChangeInstance& lc_instance) {
  const double ego_half_width =
      trajectory_info.unextended_path_for_overlap().vehicle_params().half_width;
  return lc_instance.source_lane().avg_sampled_width() / 2 +
         lc_instance.target_lane().avg_sampled_width() - ego_half_width;
}

// Returns true if we should prefer slow down for yield even if when tree search
// is enabled.
bool ShouldPreferSlowDownForYield(
    const bool is_in_queue,
    const bool should_decrease_yield_params_for_agent_in_front_of_ego,
    const bool should_set_soft_yield) {
  return is_in_queue ||
         should_decrease_yield_params_for_agent_in_front_of_ego ||
         should_set_soft_yield;
}

}  // namespace

void LaneChangeSpeedReasoner::AddPassHeadway(
    const PlannerObject& planner_object,
    const LaneChangeObjectInfo& object_info,
    const planner::pb::SelectedLaneChangeGapInfo& last_selected_gap_info,
    const std::optional<double>& dist_to_last_lc_chance_point,
    const double ego_speed, ConstraintMutator& mutator,
    std::ostringstream& debug_oss) {
  if (ShouldAddPassHeadwayForAgent(
          planner_object, object_info, last_selected_gap_info,
          dist_to_last_lc_chance_point, ego_speed, debug_oss)) {
    constexpr double kMinPassHeadwayInSec = 0.5;
    constexpr double kMaxPassHeadwayInSec = 2.0;
    constexpr double kSpeedToAddMaxPasswayInMps = 16.67;
    const double pass_headway = math::LinearInterpolate(
        kMinPassHeadwayInSec, kMaxPassHeadwayInSec,
        planner_object.speed() / kSpeedToAddMaxPasswayInMps);
    mutator.set_pass_headway(DiscomfortVarying(pass_headway));
    debug_oss << "Add pass headway: " << pass_headway << "\n";
  }
}

void LaneChangeSpeedReasoner::AddAgentReactionForOverlapRegion(
    const double ego_speed, const PlannerObject& planner_object,
    const LaneChangeObjectInfo& object_info,
    const pb::NominalModelConfig& nominal_model_config,
    const double generic_urgency_score, const bool is_in_collision_info_map,
    const bool has_static_queue, ConstraintMutator& mutator) const {
  // Do not add agent reaction to the non-unknown object and non-vehicle or
  // non-cyclist.
  if (!(planner_object.object_type() == voy::perception::ObjectType::UNKNOWN ||
        planner_object.is_vehicle() || planner_object.is_cyclist())) {
    return;
  }

  SlowDownParams slow_down_params;
  // Set speed reduction.
  const DiscomfortVarying& agent_speed_at_pass_reaction_time =
      object_info.agent_speed_at_pass_reaction_time;
  constexpr double kMinEgoSpeedToApplyUrgencyMaxSpeedReductionInMps = 3.0;
  constexpr double kMinUrgencyScoreForUrgencyARForStaticQueue = 0.2;
  const bool should_use_urgency_ar =
      ego_speed > kMinEgoSpeedToApplyUrgencyMaxSpeedReductionInMps &&
      object_info.object_ego_dist < 0.0 &&
      ((generic_urgency_score > kMinUrgencyScoreForUrgencyARForStaticQueue &&
        has_static_queue) ||
       (generic_urgency_score > 1.0 - math::constants::kEpsilon));

  if (should_use_urgency_ar) {
    std::ostringstream debug_oss;
    debug_oss << "urgency_score: " << generic_urgency_score
              << ", has_static_queue: " << has_static_queue;
    rt_event::PostRtEvent<rt_event::planner::PlannerUseUrgencyAgentReaction>(
        debug_oss.str());
  }

  // Set accel.
  const pb::NominalModel& nominal_model =
      GetNominalModel(nominal_model_config, planner_object.object_type(),
                      planner_object.is_large_vehicle());
  // For agent reaction to cyclist, use a harder decel than config.
  constexpr double kComfortAccelForCyclist = -1.0;
  constexpr double kCriticalAccelInMpss = -2.5;
  const double comfort_accel = planner_object.is_cyclist()
                                   ? kComfortAccelForCyclist
                                   : nominal_model.accel().comfort();

  const double critical_accel =
      should_use_urgency_ar ? kCriticalAccelInMpss : comfort_accel;
  slow_down_params.pass.accel = DiscomfortVarying({{0.0, 0.0},
                                                   {0.25, comfort_accel},
                                                   {0.5, critical_accel},
                                                   {1.0, critical_accel}});

  if (is_in_collision_info_map) {
    // Use a larger speed reduction for tailgaters in collision info map.
    slow_down_params.pass.max_speed_reduction = DiscomfortVarying(
        {{0.0, 0.5 * agent_speed_at_pass_reaction_time(0.0)},
         {0.25, 0.75 * agent_speed_at_pass_reaction_time(0.25)},
         {0.5, 1.0 * agent_speed_at_pass_reaction_time(0.5)},
         {1.0, 1.0 * agent_speed_at_pass_reaction_time(1.0)}});
  } else if (planner_object.is_large_vehicle()) {
    const double max_speed_reduction_at_0_5 = should_use_urgency_ar ? 1.0 : 0.2;
    const double max_speed_reduction_at_0_25 =
        has_static_queue ? max_speed_reduction_at_0_5 : 0.1;
    // Use a smaller speed reduction for large vehicles.
    slow_down_params.pass.max_speed_reduction =
        DiscomfortVarying({{0.0, 0.0},
                           {0.25, max_speed_reduction_at_0_25 *
                                      agent_speed_at_pass_reaction_time(0.25)},
                           {0.5, max_speed_reduction_at_0_5 *
                                     agent_speed_at_pass_reaction_time(0.5)},
                           {1.0, max_speed_reduction_at_0_5 *
                                     agent_speed_at_pass_reaction_time(1.0)}});
  } else {
    const double max_speed_reduction_at_0_5 = should_use_urgency_ar ? 1.0 : 0.3;
    const double max_speed_reduction_at_0_25 =
        has_static_queue ? max_speed_reduction_at_0_5 : 0.15;
    // Default speed reduction.
    slow_down_params.pass.max_speed_reduction =
        DiscomfortVarying({{0.0, 0.0},
                           {0.25, max_speed_reduction_at_0_25 *
                                      agent_speed_at_pass_reaction_time(0.25)},
                           {0.5, max_speed_reduction_at_0_5 *
                                     agent_speed_at_pass_reaction_time(0.5)},
                           {1.0, max_speed_reduction_at_0_5 *
                                     agent_speed_at_pass_reaction_time(1.0)}});
  }

  mutator.set_pass_agent_reaction(object_info.reaction_times, slow_down_params);
}

bool LaneChangeSpeedReasoner::AddConstraintFromPrimaryTrajectory(
    const pb::SpeedSeed& last_speed_seed,
    const planner::pb::SelectedLaneChangeGapInfo& last_selected_gap_info,
    const LaneChangeObjectInfo* last_selected_lead_object_info,
    const LaneChangeObjectInfo* last_selected_tail_object_info,
    const PlannerObject& planner_object,
    const LaneChangeObjectInfo& object_info,
    const std::optional<double>& dist_to_last_lc_chance_point,
    const std::optional<int64_t>& soft_pass_id,
    const std::optional<int64_t>& soft_yield_id,
    const double generic_urgency_score, const double ego_speed,
    const double safe_yield_acceleration, const double safe_pass_max_speed,
    const bool should_set_no_yield, const bool is_in_collision_info_map,
    const bool has_enough_room_for_speed_up,
    const bool is_ego_overtaking_nearby_static_vehicle,
    const bool has_static_queue,
    const pb::NominalModelConfig& nominal_model_config,
    ConstraintCreator* constraint_creator_ptr,
    pb::LaneChangeSpeedReasonerDebug* lc_speed_debug,
    pb::LaneChangeSpeedReasonerSeed* mutable_current_iter_seed,
    std::ostringstream& debug_oss) {
  bool constraint_added = false;
  for (const pb::OverlapRegion& overlap_region : object_info.overlap_regions) {
    if (lc_speed_debug) {
      *lc_speed_debug->add_gap_align_overlap_regions() = overlap_region;
    }
    const std::string constraint_id =
        absl::StrCat(GetOverlapRegionUniqueId(overlap_region), "-gap-align");
    const std::string pred_traj_id = GetBpUniqueId(
        overlap_region.object_id(), overlap_region.trajectory_id());
    ConstraintMutator mutator =
        constraint_creator_ptr->AddGapAlignSpeedConstraintFromOverlapRegion(
            pb::FenceType::kGapAlign, id(), constraint_id, overlap_region,
            overlap_region.object_id(), pred_traj_id,
            /*type=*/pb::GapAlignType::kLaneChange,
            /*should_consider_in_seed_population=*/true);

    // Set pass params for gap align.
    const double suggested_pass_extra_distance =
        GetSuggestedPassExtraDistance(generic_urgency_score);
    const double suggested_pass_extra_time =
        GetSuggestedPassExtraTime(generic_urgency_score);
    const bool should_set_soft_pass =
        soft_pass_id.has_value() && soft_pass_id.value() == object_info.id;
    mutator.set_pass_extra_distance(
        DiscomfortVarying(suggested_pass_extra_distance));
    mutator.set_pass_extra_time(DiscomfortVarying(suggested_pass_extra_time));
    // Set pass kinematic params for gap align.
    SetPassKinematicParams(last_selected_tail_object_info, object_info,
                           should_set_soft_pass, generic_urgency_score,
                           ego_speed, safe_pass_max_speed, mutator, debug_oss);

    // Set yield params for gap align.
    const double suggested_yield_extra_distance =
        GetSuggestedYieldExtraDistance(generic_urgency_score);
    const double suggested_yield_extra_time =
        GetSuggestedYieldExtraTime(generic_urgency_score);
    const bool should_set_soft_yield =
        soft_yield_id.has_value() && soft_yield_id.value() == object_info.id;
    mutator.set_yield_extra_distance(
        DiscomfortVarying(suggested_yield_extra_distance));
    mutator.set_yield_extra_time(DiscomfortVarying(suggested_yield_extra_time));
    // Set yield kinematic params for gap align.
    bool should_decrease_yield_params_for_agent_in_front_of_ego = false;
    SetYieldKinematicParams(
        last_selected_lead_object_info, planner_object, object_info,
        dist_to_last_lc_chance_point, should_set_soft_yield,
        generic_urgency_score, ego_speed, safe_yield_acceleration,
        has_enough_room_for_speed_up, is_ego_overtaking_nearby_static_vehicle,
        suggested_yield_extra_distance, suggested_yield_extra_time,
        should_decrease_yield_params_for_agent_in_front_of_ego,
        mutable_current_iter_seed, mutator, debug_oss);

    // TODO(howardgao): Polish the negative scenarios if the tree search should
    // be enabled.
    if (FLAGS_planning_enable_lane_change_tree_search) {
      // Enable tree search.
      mutator.set_is_for_lane_change_tree_search();
      if (ShouldPreferSlowDownForYield(
              object_info.is_in_queue,
              should_decrease_yield_params_for_agent_in_front_of_ego,
              should_set_soft_yield)) {
        // When we prefer to slow down more, we forbid yield decision
        // branching for this constraint to enforce both main and tree branch
        // will observe all yield time map entries after yield active time
        // regardless of switch time.
        mutator.set_forbid_yield_decision_branching_in_tree_search();
      }
    }

    AddPassHeadway(planner_object, object_info, last_selected_gap_info,
                   dist_to_last_lc_chance_point, ego_speed, mutator, debug_oss);

    AddAgentReactionForOverlapRegion(
        ego_speed, planner_object, object_info, nominal_model_config,
        generic_urgency_score, is_in_collision_info_map, has_static_queue,
        mutator);

    if (should_set_no_yield) {
      mutator.set_no_yield();
    }

    AddHysteresisForLastSelectedGap(last_speed_seed, planner_object.id(),
                                    ego_speed, mutator);
    mutator.set_max_allowed_extra_time_for_pass(
        kMaxAllowedExtraTimeForPassInSecond);

    constraint_added |= true;
  }

  return constraint_added;
}

void LaneChangeSpeedReasoner::
    AddProximitySpeedConstraintForObjectsOnTargetNeighborRegion(
        const LaneChangeInfo& lane_change_info,
        ConstraintCreator* constraint_creator_ptr) const {
  // Do not invoke proximity when urgency is larger than 0.2.
  constexpr double kMaxAllowedUrgencyScore = 0.2;
  constexpr double kDynamicWeightForUrgencyScore = 0.6;
  const LaneChangeRegionInfo* target_region_info_ptr =
      GetLaneChangeRegionInfoPtrByType(
          planner::pb::LaneChangeRegionType::TARGET,
          lane_change_info.region_infos());
  DCHECK(target_region_info_ptr != nullptr);
  if (lane_change_info.urgency_score() +
          kDynamicWeightForUrgencyScore *
              target_region_info_ptr->traffic_density >
      kMaxAllowedUrgencyScore) {
    return;
  }

  // Do not invoke proximity speed when the target neighbor region's traffic
  // density is larger than 0.2.
  constexpr double kMaxAllowedTrafficDensity = 0.2;
  const LaneChangeRegionInfo* target_neighbor_region_info_ptr =
      GetLaneChangeRegionInfoPtrByType(
          planner::pb::LaneChangeRegionType::TARGET_NEIGHBOR,
          lane_change_info.region_infos());
  if (target_neighbor_region_info_ptr == nullptr ||
      target_neighbor_region_info_ptr->traffic_density >
          kMaxAllowedTrafficDensity) {
    return;
  }
  const LaneChangeObjectInfoList& target_neighbor_region_object_list =
      target_neighbor_region_info_ptr->object_info_list;
  const LaneChangeObjectInfoList& target_region_object_list =
      *DCHECK_NOTNULL(GetTargetRegionObjectInfoListPtr(lane_change_info));

  // Calculate the required lateral gap for proximity gap. The proximity speed
  // applies on the whole overlap region to ensure ego can keep enough
  // longitudinal dist with the object on the target neighbor region.
  const double required_lat_gap =
      lane_change_info.lane_change_instance.source_lane().avg_sampled_width() *
      2;

  for (const int64_t object_id :
       target_neighbor_region_object_list.non_leaving_ids()) {
    const LaneChangeObjectInfo* object_info =
        target_neighbor_region_object_list.Find(object_id);
    DCHECK(object_info != nullptr);
    // Ignore the object if it's on the target region as well.
    if (target_region_object_list.FindNonLeaving(object_id) != nullptr) {
      continue;
    }

    // Ignore the object if the max speed is invalid.
    if (std::isinf(object_info->max_speed_for_proximity_speed)) {
      continue;
    }

    for (const pb::OverlapRegion& overlap_region :
         object_info->overlap_regions) {
      const std::string constraint_id = absl::StrCat(
          GetOverlapRegionUniqueId(overlap_region), "-proximity-speed-lc");
      const std::string pred_traj_id = GetBpUniqueId(
          overlap_region.object_id(), overlap_region.trajectory_id());
      // TODO(howardgao): Use lane change finished time to split the
      // overlap_region.
      ConstraintMutator mutator =
          constraint_creator_ptr->AddProximitySpeedConstraintFromOverlapRegion(
              pb::FenceType::kProximity,
              pb::ReasonerId::LANE_CHANGE_PREPARATION, constraint_id,
              overlap_region, overlap_region.object_id(), pred_traj_id);
      mutator.set_proximity_speed_params(
          required_lat_gap, object_info->max_speed_for_proximity_speed);
    }
  }
}

void LaneChangeSpeedReasoner::SetPassKinematicParams(
    const LaneChangeObjectInfo* last_selected_tail_object_info,
    const LaneChangeObjectInfo& object_info, const bool set_soft_constraint,
    const double generic_urgency_score, const double ego_speed,
    const double safe_pass_max_speed, ConstraintMutator& mutator,
    std::ostringstream& debug_oss) {
  const double suggested_pass_max_v = GetSuggestedPassMaximumSpeed(
      ego_speed, object_info.will_see_ego_time, object_info.agent_max_speed);
  const double suggested_pass_max_a =
      GetSuggestedPassMaximumAccel(generic_urgency_score);
  const bool ahead_of_last_tail =
      last_selected_tail_object_info != nullptr &&
      object_info.object_ego_dist >
          last_selected_tail_object_info->object_ego_dist +
              kDistanceToleranceInMeter;
  const double suggested_pass_max_j =
      GetSuggestedPassMaximumJerk(ahead_of_last_tail, generic_urgency_score);

  debug_oss << "----------" << __func__ << "----------\n"
            << "Suggested pass max v: " << suggested_pass_max_v
            << ", Suggested pass max a: " << suggested_pass_max_a
            << ", Suggested pass max j: " << suggested_pass_max_j
            << ", Set soft pass: " << set_soft_constraint << "\n";

  if (object_info.object_ego_dist > 0.0) {
    // If the agent is in front of ego, we only allow pass under
    // kGapAlignPassMaxDiscomfortForLeadingAgent.
    mutator.set_pass_if_possible_below_discomfort(
        kGapAlignPassMaxDiscomfortForLeadingAgent);
  }

  SetPassWithMaxSpeedAccelJerk(
      std::min(safe_pass_max_speed, suggested_pass_max_v), suggested_pass_max_a,
      suggested_pass_max_j, set_soft_constraint, mutator);

  if (safe_pass_max_speed < suggested_pass_max_v) {
    debug_oss << "Decrease v to: " << safe_pass_max_speed << "\n";
  }
}

void LaneChangeSpeedReasoner::SetYieldKinematicParams(
    const LaneChangeObjectInfo* last_selected_lead_object_info,
    const PlannerObject& planner_object,
    const LaneChangeObjectInfo& object_info,
    const std::optional<double>& dist_to_last_lc_chance_point,
    const bool set_soft_constraint, const double generic_urgency_score,
    const double ego_speed, const double safe_yield_acceleration,
    const bool has_enough_room_for_speed_up,
    const bool is_ego_overtaking_nearby_static_vehicle,
    const double yield_extra_distance, const double yield_extra_time,
    bool& should_decrease_yield_params_for_agent_in_front_of_ego,
    pb::LaneChangeSpeedReasonerSeed* mutable_current_iter_seed,
    ConstraintMutator& mutator, std::ostringstream& debug_oss) const {
  const double suggested_yield_min_v = GetSuggestedYieldMinimumSpeed(
      ego_speed, object_info.agent_min_speed, generic_urgency_score);
  const double suggested_yield_min_a = GetSuggestedYieldMinimumAccel(
      safe_yield_acceleration, generic_urgency_score);
  const bool behind_last_lead =
      last_selected_lead_object_info != nullptr &&
      object_info.object_ego_dist + kDistanceToleranceInMeter <
          last_selected_lead_object_info->object_ego_dist;
  const double suggested_yield_min_j =
      GetSuggestedYieldMinimumJerk(behind_last_lead, generic_urgency_score);

  debug_oss << "----------" << __func__ << "----------\n"
            << "Suggested yield min v: " << suggested_yield_min_v
            << ", Suggested yield min a: " << suggested_yield_min_a
            << ", Suggested yield min j: " << suggested_yield_min_j
            << ", Set soft yield: " << set_soft_constraint << "\n";

  double decreased_yield_min_v = 0.0;
  double decreased_yield_min_a = 0.0;
  double decreased_yield_min_j = 0.0;
  // We decrease the yield min v,a,j to naturally align with the yield gap.
  if (ShouldDecreaseYieldKinematicParamsForAgent(
          planner_object, object_info, dist_to_last_lc_chance_point, ego_speed,
          suggested_yield_min_v, suggested_yield_min_a, suggested_yield_min_j,
          yield_extra_distance, yield_extra_time, decreased_yield_min_v,
          decreased_yield_min_a, decreased_yield_min_j)) {
    DCHECK_LE(decreased_yield_min_v, suggested_yield_min_v);
    DCHECK_LE(decreased_yield_min_a, suggested_yield_min_a);
    DCHECK_LE(decreased_yield_min_j, suggested_yield_min_j);
    SetYieldWithMinSpeedAccelJerk(decreased_yield_min_v, decreased_yield_min_a,
                                  decreased_yield_min_j, set_soft_constraint,
                                  mutator);
    should_decrease_yield_params_for_agent_in_front_of_ego = true;

    debug_oss << "Decrease v to: " << decreased_yield_min_v
              << ", Decrease a to: " << decreased_yield_min_a
              << ", Decrease j to: " << decreased_yield_min_j << "\n";
    return;
  }

  double increased_yield_min_v = 0.0;
  // We increase the yield min v to naturally align with the yield gap.
  if (ShouldIncreaseYieldMinSpeedForAgent(
          object_info, generic_urgency_score, ego_speed, suggested_yield_min_v,
          suggested_yield_min_a, has_enough_room_for_speed_up,
          is_ego_overtaking_nearby_static_vehicle, yield_extra_distance,
          yield_extra_time, increased_yield_min_v)) {
    DCHECK_GE(increased_yield_min_v, suggested_yield_min_v);
    mutable_current_iter_seed->mutable_object_id_to_yield_min_v_map()->insert(
        {object_info.id, increased_yield_min_v});
    SetYieldWithMinSpeedAccelJerk(increased_yield_min_v, suggested_yield_min_a,
                                  suggested_yield_min_j, set_soft_constraint,
                                  mutator);

    debug_oss << DUMP_TO_STREAM(has_enough_room_for_speed_up,
                                is_ego_overtaking_nearby_static_vehicle)
              << "\nIncrease v to: " << increased_yield_min_v << "\n";
    return;
  }

  // Assign the suggested yield min v.
  mutable_current_iter_seed->mutable_object_id_to_yield_min_v_map()->insert(
      {object_info.id, suggested_yield_min_v});
  SetYieldWithMinSpeedAccelJerk(suggested_yield_min_v, suggested_yield_min_a,
                                suggested_yield_min_j, set_soft_constraint,
                                mutator);
}

void LaneChangeSpeedReasoner::AddStopConstraintForUrgencyInfo(
    const std::optional<LaneChangeUrgencyInfo>& urgency_info,
    const RobotStateSnapshot& snapshot, const bool can_trigger_crawl,
    const bool is_inexecutable_lc_signal, const double safe_yield_acceleration,
    ConstraintCreator* constraint_creator_ptr) const {
  if (!urgency_info.has_value()) {
    return;
  }

  if ((urgency_info->urgency_type() ==
       planner::pb::LaneChangeUrgencyType::VALID_LANE_CHANGE_END) &&
      (urgency_info->stop_line_type() !=
       planner::pb::LaneChangeStopLineType::HARD_STOP_LINE)) {
    return;
  }

  std::ostringstream debug_oss;
  const std::optional<double> stop_line_distance =
      GetStopConstraintDistanceFromRAForUrgencyInfo(
          can_trigger_crawl, is_inexecutable_lc_signal, urgency_info.value(),
          snapshot, safe_yield_acceleration, debug_oss);
  if (!stop_line_distance.has_value()) {
    LOG(INFO) << kLogHeader << __func__
              << ": No stop constraint for urgency info: "
              << planner::pb::LaneChangeUrgencyType_Name(
                     urgency_info->urgency_type())
              << "\n"
              << debug_oss.str();
    return;
  }

  LOG(INFO) << kLogHeader << __func__ << ": Adding "
            << planner::pb::LaneChangeStopLineType_Name(
                   urgency_info->stop_line_type())
            << " for "
            << planner::pb::LaneChangeUrgencyType_Name(
                   urgency_info->urgency_type())
            << " at " << stop_line_distance.value() << "m: " << debug_oss.str();
  const std::string& stop_line_name =
      urgency_info->stop_line_type() ==
              planner::pb::LaneChangeStopLineType::SOFT_STOP_LINE
          ? "soft_lane_change_end"
          : "hard_lane_change_end";
  constraint_creator_ptr->AddStopPoint(
      /*start_time=*/0, /*end_time=*/constants::kTrajectoryHorizonInSec,
      stop_line_distance.value(), pb::FenceType::kDestination, id(),
      stop_line_name);
}

void LaneChangeSpeedReasoner::AddSpeedLimitByConsecutiveLaneChangeInfo(
    const planner::pb::LaneChangeMetadata& lane_change_metadata,
    CautiousDrivingLimiter& reference_limiter_ptr,
    std::ostringstream& debug_oss) const {
  debug_oss << __func__ << ": max_longitudinal_span_for_current_lc: "
            << lane_change_metadata.max_longitudinal_span_for_current_lc()
            << ", speed_limit_at_current_lc_end: "
            << lane_change_metadata.speed_limit_at_current_lc_end()
            << ", min_acc_for_speed_limit: "
            << lane_change_metadata.min_acc_for_speed_limit() << ".\n";
  if (lane_change_metadata.speed_limit_at_current_lc_end() <
      math::constants::kEpsilon) {
    debug_oss << __func__ << ": No need to apply speed limit.\n";
    return;
  }

  const double speed_limit_at_current_lc_end =
      lane_change_metadata.speed_limit_at_current_lc_end();
  constexpr double kSpeedLimitRelaxForMaxDiscomfortInMps = 5.0;
  debug_oss << __func__ << ": Applying speed limit at path arc-length "
            << lane_change_metadata.max_longitudinal_span_for_current_lc()
            << ".\n";
  const int speed_limit_id =
      -(static_cast<int>(id()) * kSpeedLimitIdShiftNumber +
        static_cast<int>(SpeedLimitType::kConsecutiveLaneChange));
  reference_limiter_ptr.AddReferenceLimit(
      speed_limit_at_current_lc_end,
      speed_limit_at_current_lc_end + kSpeedLimitRelaxForMaxDiscomfortInMps,
      lane_change_metadata.max_longitudinal_span_for_current_lc() -
          math::constants::kEpsilon,
      lane_change_metadata.max_longitudinal_span_for_current_lc(),
      lane_change_metadata.min_acc_for_speed_limit(),
      /*object_id=*/speed_limit_id);
}

void LaneChangeSpeedReasoner::AddSpeedLimitByLaneChangeEnd(
    const bool is_lane_change_to_fork,
    const std::optional<LaneChangeUrgencyInfo>& lane_change_end_urgency_info,
    const RobotStateSnapshot& snapshot, const double safe_yield_acc,
    CautiousDrivingLimiter& reference_limiter,
    std::ostringstream& debug_oss) const {
  if (!lane_change_end_urgency_info.has_value() ||
      (lane_change_end_urgency_info.value().stop_line_type() ==
       planner::pb::LaneChangeStopLineType::HARD_STOP_LINE)) {
    debug_oss << __func__ << ": No need to apply speed limit.\n";
    return;
  }

  if ((lane_change_end_urgency_info.value().stop_line_type() ==
       planner::pb::LaneChangeStopLineType::IMPLICIT_STOP_LINE) &&
      !is_lane_change_to_fork) {
    debug_oss << __func__
              << ": No need to apply speed limit for implict stop line and non "
                 "fork lane change.\n";
    return;
  }
  const LaneChangeUrgencyInfo& urgency_info =
      lane_change_end_urgency_info.value();
  const double ego_speed = snapshot.speed();
  debug_oss << __func__ << ": Stop_line_type "
            << planner::pb::LaneChangeStopLineType_Name(
                   urgency_info.stop_line_type())
            << ", urgency_dist " << urgency_info.urgency_dist()
            << "m, ego_speed " << ego_speed << "m/s, safe_yield_acc"
            << safe_yield_acc << "m/s^2.\n";
  double limit_arc_length = 0.0;
  double min_comfort_speed = 0.0;
  double min_accel = -0.0;
  if (!GetSpeedLimitInfoFromUrgencyInfo(urgency_info, safe_yield_acc, ego_speed,
                                        limit_arc_length, min_comfort_speed,
                                        min_accel, debug_oss)) {
    debug_oss << "\n Don't add speed limit.\n";
    return;
  }
  constexpr double kSpeedLimitRelaxForMaxDiscomfortInMps = 2.0;
  debug_oss << "\n Applying speed limit for lane change end at path arc-length "
            << limit_arc_length << "m, min_comfort_speed " << min_comfort_speed
            << "m/s, min_accel " << min_accel << "m/s^2.\n";
  const int speed_limit_id =
      -(static_cast<int>(id()) * kSpeedLimitIdShiftNumber +
        static_cast<int>(SpeedLimitType::kLaneChangeEnd));
  reference_limiter.AddReferenceLimit(
      min_comfort_speed,
      min_comfort_speed + kSpeedLimitRelaxForMaxDiscomfortInMps,
      limit_arc_length - math::constants::kEpsilon, limit_arc_length, min_accel,
      /*object_id=*/speed_limit_id);
}

bool LaneChangeSpeedReasoner::Reason(
    const WorldContext& world_context,
    const std::vector<PredictionDecisionMakerInput>& reasoning_inputs,
    const TrajectoryInfo& trajectory_info,
    const std::unordered_map<ObjectId, planner::PlannerObject>&
        planner_object_map,
    const planner::pb::DecoupledManeuverSeed& previous_iter_seed,
    const pb::NominalModelConfig& nominal_model_config,
    pb::LaneChangeSpeedReasonerSeed* mutable_current_iter_seed,
    ConstraintCreator* constraint_creator_ptr,
    GlobalStrategySetter* global_strategy_setter,
    CautiousDrivingLimiter* reference_limiter_ptr,
    GapQualityEvaluator* gap_quality_evaluator_ptr,
    pb::ReasoningDebug* debug_proto_ptr) {
  DCHECK(mutable_current_iter_seed != nullptr);
  DCHECK(constraint_creator_ptr != nullptr);
  DCHECK(global_strategy_setter != nullptr);
  DCHECK(reference_limiter_ptr != nullptr);
  DCHECK(debug_proto_ptr != nullptr);

  // Logging & debugging setup.
  std::ostringstream debug_oss;
  pb::LaneChangeSpeedReasonerDebug* lc_speed_debug =
      debug_proto_ptr ? debug_proto_ptr->mutable_reasoner_debug()
                            ->mutable_lane_change_speed_reasoner()
                      : nullptr;
  BOOST_SCOPE_EXIT(  // NOLINT, FP when checking macro BOOST_SCOPE_EXIT
      &debug_oss, lc_speed_debug) {
    const std::string& debug_str = debug_oss.str();
    if (debug_str.empty()) {
      return;
    }
    if (lc_speed_debug) {
      lc_speed_debug->set_debug_str(debug_str);
    }
  }
  BOOST_SCOPE_EXIT_END

  // Don't run lane change speed reasoning when LC state is none.
  const LaneChangeStatusForSpeed& lane_change_status =
      trajectory_info.lane_change_status();
  if (lane_change_status.lane_change_state ==
      planner::pb::LANE_CHANGE_STATE_NONE) {
    return false;
  }

  const LaneChangeInfo& lane_change_info =
      trajectory_info.lane_change_specific_info();
  const planner::pb::LaneChangeMetadata& lane_change_metadata =
      lane_change_info.lane_change_metadata;
  const double safe_yield_acceleration =
      lane_change_info.safe_yield_acceleration();

  // Apply the speed limit near the current lane change end if we need to slow
  // down for an incoming consecutive lane change.
  AddSpeedLimitByConsecutiveLaneChangeInfo(lane_change_metadata,
                                           *reference_limiter_ptr, debug_oss);

  // Apply the proximity speed for the objects on the target neighbor region.
  AddProximitySpeedConstraintForObjectsOnTargetNeighborRegion(
      lane_change_info, constraint_creator_ptr);

  // In the second section, we will run speed reasoning in preparation and
  // abort state.
  if (!trajectory_info.should_run_lane_change_gap_align()) {
    return false;
  }

  // Apply the speed limit for lane change implicit or soft lane change end.
  AddSpeedLimitByLaneChangeEnd(
      lane_change_info.lane_change_instance.is_lane_change_to_fork(),
      GetMostUrgencyInfoByUrgencyType(
          lane_change_info.urgency_infos(),
          planner::pb::LaneChangeUrgencyType::VALID_LANE_CHANGE_END),
      world_context.robot_state().current_state_snapshot(),
      safe_yield_acceleration, *reference_limiter_ptr, debug_oss);

  const double generic_urgency_score =
      FLAGS_planning_enable_gap_align_urgency_score_for_lane_change
          ? lane_change_info.generic_urgency_score()
          : lane_change_info.urgency_score();

  // Add stop constraints to improve lane change ability.
  // Most urgent soft constraint.
  AddStopConstraintForUrgencyInfo(
      GetMostUrgencyInfoByStopLineType(
          lane_change_info.urgency_infos(),
          planner::pb::LaneChangeStopLineType::SOFT_STOP_LINE),
      world_context.robot_state().current_state_snapshot(),
      lane_change_info.can_trigger_crawl,
      lane_change_info.is_inexecutable_lane_change_signal,
      safe_yield_acceleration, constraint_creator_ptr);

  // ActionItem(howard): This is just a quick fix for hard stop line.
  // We should compare the lane sequence type in the future.
  const planner::pb::LaneSequenceCostFactors& lane_sequence_cost_factors =
      trajectory_info.lane_sequence_cost_factors();
  const bool is_last_lane_change_chance_from_sequence =
      lane_sequence_cost_factors.has_last_lane_change_chance_point() &&
      (lane_sequence_cost_factors.last_lane_change_chance_point()
               .lane_change_reason() ==
           planner::pb::LastLaneChangeChancePoint::ROUTE ||
       lane_sequence_cost_factors.last_lane_change_chance_point()
               .lane_change_reason() ==
           planner::pb::LastLaneChangeChancePoint::BLOCKAGE);
  const std::optional<LaneChangeUrgencyInfo> lane_change_end_info =
      GetMostUrgencyInfoByUrgencyType(
          lane_change_info.urgency_infos(),
          planner::pb::LaneChangeUrgencyType::VALID_LANE_CHANGE_END);
  const bool is_last_lane_change_chance_from_lane_change_info =
      lane_change_end_info.has_value() &&
      lane_change_end_info->stop_line_type() ==
          planner::pb::LaneChangeStopLineType::HARD_STOP_LINE;
  const bool last_selected_state_is_in_progress =
      previous_iter_seed.selected_lane_change_state() ==
      planner::pb::LANE_CHANGE_STATE_IN_PROGRESS;
  if ((is_last_lane_change_chance_from_sequence ||
       (is_last_lane_change_chance_from_lane_change_info &&
        !last_selected_state_is_in_progress)) &&
      !trajectory_info.IsXLaneNudging()) {
    // Most urgent hard constraint.
    AddStopConstraintForUrgencyInfo(
        GetMostUrgencyInfoByStopLineType(
            lane_change_info.urgency_infos(),
            planner::pb::LaneChangeStopLineType::HARD_STOP_LINE),
        world_context.robot_state().current_state_snapshot(),
        lane_change_info.can_trigger_crawl,
        lane_change_info.is_inexecutable_lane_change_signal,
        safe_yield_acceleration, constraint_creator_ptr);
  }

  if (lane_change_info.is_inexecutable_lane_change_signal) {
    debug_oss << "Don't add gap align constraint for inexecutable lane change "
                 "signal.\n";
    return true;
  }

  if (FLAGS_planning_enable_lane_change_gap_align_diversity) {
    constexpr int kNumOfExtraGap = 1;
    constexpr double kDeltaDiscomfort = 0.5;
    global_strategy_setter->set_gap_diversity_settings(
        /*faster_gap_num=*/kNumOfExtraGap, /*slower_gap_num=*/kNumOfExtraGap,
        /*delta_discomfort=*/kDeltaDiscomfort);
  }

  // Set global pass/yield max active time.
  global_strategy_setter->set_global_gap_align_pass_max_active_time(
      kGapAlignMaxPassActiveTimeInSec);

  // Reduce yield max active time to 0 when it is not safe to decelerate:
  // - When ego is braking harder than safe yield deceleration.
  // - When ego is aborting a LC.
  const bool is_safe_to_yield =
      IsSafeToYieldForGapAlign(world_context.ego_acceleration(),
                               safe_yield_acceleration, generic_urgency_score,
                               previous_iter_seed.speed_seed()
                                   .speed_reasoner_seeds()
                                   .lane_change_speed_reasoner_seed()
                                   .is_safe_to_yield());
  mutable_current_iter_seed->set_is_safe_to_yield(is_safe_to_yield);
  const bool is_in_abort_state = lane_change_status.lane_change_state ==
                                 planner::pb::LANE_CHANGE_STATE_ABORT;
  debug_oss << DUMP_TO_STREAM(is_safe_to_yield, is_in_abort_state) << "\n";
  global_strategy_setter->set_global_gap_align_yield_max_active_time(
      (is_safe_to_yield && !is_in_abort_state)
          ? kGapAlignMaxYieldActiveTimeInSec
          : 0.0);

  const double ego_speed = world_context.ego_speed();
  const std::optional<double> dist_to_last_lc_chance_point =
      lane_sequence_cost_factors.has_last_lane_change_chance_point()
          ? std::make_optional<double>(
                lane_sequence_cost_factors.last_lane_change_chance_point()
                    .dist_from_ego())
          : std::nullopt;

  // Add gap align constraint for interested object on the target region and
  // set params.
  const LaneChangeRegionInfo* target_region_info_ptr =
      GetLaneChangeRegionInfoPtrByType(
          planner::pb::LaneChangeRegionType::TARGET,
          lane_change_info.region_infos());
  if (target_region_info_ptr == nullptr) {
    debug_oss << "No target region info.\n";
    return false;
  }
  const std::unordered_map<int64_t, ObjectCollisionInfo>&
      object_collision_info_map =
          lane_change_info.lane_change_env_info.object_collision_info_map;
  const LaneChangeObjectInfoList& target_region_object_info_list =
      target_region_info_ptr->object_info_list;
  const bool has_queue_object_in_front =
      target_region_info_ptr->HasQueueObjectInFrontOfEgo();
  const bool has_static_queue = target_region_info_ptr->has_static_queue;

  // Related info to avoid being too close with source agent in front of ego.
  const LaneChangeRegionInfo* source_region_info_ptr =
      GetLaneChangeRegionInfoPtrByType(
          planner::pb::LaneChangeRegionType::SOURCE,
          lane_change_info.region_infos());
  const double safe_pass_max_v =
      GetSafePassMaxSpeed(reasoning_inputs, source_region_info_ptr, ego_speed);

  // Optimal reasoning gap info.
  const ReasoningGapInfo* optimal_reasoning_gap =
      DCHECK_NOTNULL(gap_quality_evaluator_ptr)
          ->GetOptimalReasoningGap(world_context.robot_state(),
                                   planner_object_map, *target_region_info_ptr,
                                   debug_oss);
  if (lc_speed_debug != nullptr) {
    if (optimal_reasoning_gap != nullptr) {
      lc_speed_debug->mutable_optimal_reasoning_gap_info()->CopyFrom(
          optimal_reasoning_gap->ToProto());
    } else {
      lc_speed_debug->mutable_optimal_reasoning_gap_info()->Clear();
    }
  }
  // For now disable soft pass the gap before ego.
  const std::optional<int64_t> soft_pass_id = std::nullopt;
  const std::optional<int64_t> soft_yield_id =
      (optimal_reasoning_gap != nullptr &&
       optimal_reasoning_gap->gap_ego_distance < 0.0)
          ? std::make_optional(optimal_reasoning_gap->lead_obj_id)
          : std::nullopt;

  // Get last selected gap object info.
  const planner::pb::SelectedLaneChangeGapInfo& last_selected_gap_info =
      previous_iter_seed.selection_seed()
          .lane_change_selection_seed()
          .selected_lane_change_gap_info();
  const LaneChangeObjectInfo* last_select_lead_object_info = nullptr;
  const LaneChangeObjectInfo* last_select_tail_object_info = nullptr;
  if (last_selected_gap_info.gap_selected_cycles() > 0) {
    const int64_t tail_obj_id = last_selected_gap_info.tail_obj_id();
    const int64_t lead_obj_id = last_selected_gap_info.lead_obj_id();
    if (tail_obj_id != 0) {
      last_select_tail_object_info =
          target_region_object_info_list.Find(tail_obj_id);
    }
    if (lead_obj_id != 0) {
      last_select_lead_object_info =
          target_region_object_info_list.Find(lead_obj_id);
    }
  }
  bool constraint_added = false;
  for (const int64_t id : target_region_object_info_list.non_leaving_ids()) {
    const LaneChangeObjectInfo* lane_change_object_info_ptr =
        target_region_object_info_list.Find(id);
    DCHECK(lane_change_object_info_ptr != nullptr);
    const LaneChangeObjectInfo& lane_change_object_info =
        *lane_change_object_info_ptr;

    if (!lane_change_object_info.is_interested_for_gap_align) {
      continue;
    }

    if (lc_speed_debug != nullptr) {
      lc_speed_debug->add_interested_objects(id);
    }

    // Add debug log for this object.
    debug_oss << "\n"
              << kLogHeader << " Object ID: " << lane_change_object_info.id
              << "\n";

    DCHECK(planner_object_map.find(id) != planner_object_map.end());
    const planner::PlannerObject& planner_object = planner_object_map.at(id);
    const bool is_reversing_or_kturn_large_vehicle =
        IsReversingOrKTurnLargeVehicle(reasoning_inputs, id);
    const bool is_ego_in_junction = trajectory_info.IsEgoInJunction();
    const bool has_red_light_ahead = HasRedLightAhead(trajectory_info);
    const bool should_set_no_yield = ShouldSetNoYieldForObject(
        world_context, lane_change_info, target_region_object_info_list,
        lane_change_object_info, planner_object, dist_to_last_lc_chance_point,
        has_queue_object_in_front, is_reversing_or_kturn_large_vehicle,
        is_ego_in_junction, has_red_light_ahead, debug_oss);
    if (should_set_no_yield) {
      mutable_current_iter_seed->add_set_no_yield_object_ids(id);
    }
    const bool is_in_collision_info_map =
        object_collision_info_map.find(id) != object_collision_info_map.end();
    constraint_added |= AddConstraintFromPrimaryTrajectory(
        previous_iter_seed.speed_seed(), last_selected_gap_info,
        last_select_lead_object_info, last_select_tail_object_info,
        planner_object, lane_change_object_info, dist_to_last_lc_chance_point,
        soft_pass_id, soft_yield_id, generic_urgency_score, ego_speed,
        safe_yield_acceleration, safe_pass_max_v, should_set_no_yield,
        is_in_collision_info_map,
        HasEnoughRoomForSpeedUpBeforeLaneChange(lane_sequence_cost_factors),
        IsEgoOvertakingNearbyStaticVehicle(reasoning_inputs), has_static_queue,
        nominal_model_config, constraint_creator_ptr, lc_speed_debug,
        mutable_current_iter_seed, debug_oss);
  }

  return constraint_added;
}

}  // namespace reasoner
}  // namespace speed
}  // namespace planner
