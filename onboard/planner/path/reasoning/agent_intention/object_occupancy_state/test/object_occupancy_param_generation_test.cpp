#include "planner/path/reasoning/agent_intention/object_occupancy_state/object_occupancy_param_generation.h"

#include <gtest/gtest.h>

#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_wrapper.h"
#include "planner/world_model/planner_object/planner_object.h"

namespace planner {

namespace {

class ObjectOccupancyParamGenerationTest : public ::testing::Test {
 protected:
  void SetUp() override {
    auto& tracked_object = *agent_proto_.mutable_tracked_object();
    tracked_object.set_object_type(voy::perception::ObjectType::VEHICLE);
    tracked_object.set_id(1);
    tracked_object.set_center_x(5.0);
    tracked_object.set_center_y(2.0);
    tracked_object.set_heading(0.0);
    tracked_object.set_length(4.0);
    tracked_object.set_width(2.0);
    tracked_object.set_height(1.2);
    tracked_object.set_velocity(5.0);
    tracked_object.set_acceleration(1.0);
    const int64_t timestamp = 0;
    // Dummy pose at plan init ts.
    const TrafficParticipantPose pose_at_plan_init_ts_(
        timestamp, agent_proto_.tracked_object());
    predicted_trajectory_.set_id(2);
  }

  prediction::pb::Agent agent_proto_;
  const int64_t timestamp = 0;
  // Dummy pose at plan init ts.
  const TrafficParticipantPose pose_at_plan_init_ts_;
  prediction::pb::PredictedTrajectory predicted_trajectory_;
};
}  // namespace

// Unit test scenario, with ego car width being 2.0 m and length being 4.0 m:
//
//  (0, 3)    ____________________________  (10, 3)          left boundary
//                     | Ego  |
//                     |______|
//
//  (0, 0)    - - - - - - - - - - - - - - - (10, 0)          nominal path
//
//
//
//  (0, -3)   ____________________________  (10, -3)         right boundary
//
//

TEST_F(ObjectOccupancyParamGenerationTest, SingleObjectBasicTest) {
  const std::vector<math::geometry::Point2d> nominal_path_points = {
      {0.0, 0.0}, {10.0, 0.0}};
  const math::geometry::PolylineCurve2d nominal_path_curve(nominal_path_points);
  const math::Range1d driving_arclength_range(0.0, 10.0);
  const std::vector<math::geometry::Point2d> left_boundary_points = {
      {0.0, 3.0}, {10.0, 3.0}};
  const math::geometry::PolylineCurve2d left_boundary_curve(
      left_boundary_points);
  const std::vector<math::geometry::Point2d> right_boundary_points = {
      {0.0, -3.0}, {10.0, -3.0}};
  const math::geometry::PolylineCurve2d right_boundary_curve(
      right_boundary_points);
  const PlannerObject planner_object(agent_proto_, timestamp,
                                     pose_at_plan_init_ts_);
  ObjectOccupancyParam object_occupancy_param =
      ComputeObjectSnapshotOccupancyParam(
          planner_object.pose(), nominal_path_curve, left_boundary_curve,
          right_boundary_curve);

  EXPECT_EQ(object_occupancy_param.full_body_start_arclength_m, 3.0);
  EXPECT_EQ(object_occupancy_param.full_body_end_arclength_m, 7.0);
  EXPECT_EQ(object_occupancy_param.full_body_start_lateral_distance_m, 1.0);
  EXPECT_EQ(object_occupancy_param.full_body_end_lateral_distance_m, 3.0);
  EXPECT_EQ(object_occupancy_param.left_boundary_clearance_m, 0.0);
  EXPECT_EQ(object_occupancy_param.right_boundary_clearance_m, 4.0);
  EXPECT_EQ(object_occupancy_param.dist_to_center_line_range.start_pos, 1.0);
  EXPECT_EQ(object_occupancy_param.dist_to_center_line_range.end_pos, 3.0);
  EXPECT_EQ(object_occupancy_param.center_line_clearance_m, 1.0);
  EXPECT_EQ(object_occupancy_param.center_line_side, math::pb::kLeft);
  EXPECT_EQ(object_occupancy_param.cross_track_encroachment_m, 2.0);
  EXPECT_EQ(object_occupancy_param.snapshot_start_arclength_m, 3.0);
  EXPECT_EQ(object_occupancy_param.snapshot_end_arclength_m, 7.0);
  EXPECT_EQ(object_occupancy_param.min_corridor_width_m, 6.0);
  EXPECT_EQ(object_occupancy_param.max_left_violation, 0.0);
  EXPECT_EQ(object_occupancy_param.max_right_violation, -4.0);
  EXPECT_EQ(object_occupancy_param.relative_heading_rad, 0.0);
  EXPECT_EQ(object_occupancy_param.along_track_speed_mps, 5.0);
  EXPECT_EQ(object_occupancy_param.cross_track_speed_mps, 0.0);
  EXPECT_EQ(object_occupancy_param.speed_mps, 5.0);
  EXPECT_EQ(object_occupancy_param.velocity->x(), 5.0);
  EXPECT_EQ(object_occupancy_param.velocity->y(), 0.0);
  EXPECT_EQ(object_occupancy_param.acceleration_mpss, 1.0);
  EXPECT_TRUE(object_occupancy_param.is_fully_within_corridor);
}

// In this test, the object crosses the left boundary.
//                      ______
//  (0, 2)    _________|_Ego__|_____________  (10, 2)          left boundary
//                     |______|
//
//
//  (0, -1)    - - - - - - - - - - - - - - - (10, -1)          nominal path
//
//
//
//  (0, -4)   ____________________________  (10, -4)         right boundary
//
//
TEST_F(ObjectOccupancyParamGenerationTest, SingleObjectCrossBoundaryTest) {
  const std::vector<math::geometry::Point2d> nominal_path_points = {
      {0.0, -1.0}, {10.0, -1.0}};
  const math::geometry::PolylineCurve2d nominal_path_curve(nominal_path_points);
  const math::Range1d driving_arclength_range(0.0, 10.0);
  const std::vector<math::geometry::Point2d> left_boundary_points = {
      {0.0, 2.0}, {10.0, 2.0}};
  const math::geometry::PolylineCurve2d left_boundary_curve(
      left_boundary_points);
  const std::vector<math::geometry::Point2d> right_boundary_points = {
      {0.0, -4.0}, {10.0, -4.0}};
  const math::geometry::PolylineCurve2d right_boundary_curve(
      right_boundary_points);
  const PlannerObject planner_object(agent_proto_, timestamp,
                                     pose_at_plan_init_ts_);
  ObjectOccupancyParam object_occupancy_param =
      ComputeObjectSnapshotOccupancyParam(
          planner_object.pose(), nominal_path_curve, left_boundary_curve,
          right_boundary_curve);

  EXPECT_EQ(object_occupancy_param.full_body_start_arclength_m, 3.0);
  EXPECT_EQ(object_occupancy_param.full_body_end_arclength_m, 7.0);
  EXPECT_EQ(object_occupancy_param.full_body_start_lateral_distance_m, 2.0);
  EXPECT_EQ(object_occupancy_param.full_body_end_lateral_distance_m, 4.0);
  EXPECT_EQ(object_occupancy_param.left_boundary_clearance_m, 0.0);
  EXPECT_EQ(object_occupancy_param.right_boundary_clearance_m, 5.0);
  EXPECT_EQ(object_occupancy_param.center_line_clearance_m, 2.0);
  EXPECT_EQ(object_occupancy_param.dist_to_center_line_range.start_pos, 2.0);
  EXPECT_EQ(object_occupancy_param.dist_to_center_line_range.end_pos, 4.0);
  EXPECT_EQ(object_occupancy_param.center_line_side, math::pb::kLeft);
  EXPECT_EQ(object_occupancy_param.cross_track_encroachment_m, 1.0);
  EXPECT_EQ(object_occupancy_param.snapshot_start_arclength_m, 3.0);
  EXPECT_EQ(object_occupancy_param.snapshot_end_arclength_m, 7.0);
  EXPECT_EQ(object_occupancy_param.min_corridor_width_m, 6.0);
  EXPECT_EQ(object_occupancy_param.max_left_violation, 1.0);
  EXPECT_EQ(object_occupancy_param.max_right_violation, -5.0);
  EXPECT_EQ(object_occupancy_param.relative_heading_rad, 0.0);
  EXPECT_EQ(object_occupancy_param.along_track_speed_mps, 5.0);
  EXPECT_EQ(object_occupancy_param.cross_track_speed_mps, 0.0);
  EXPECT_EQ(object_occupancy_param.speed_mps, 5.0);
  EXPECT_EQ(object_occupancy_param.velocity->x(), 5.0);
  EXPECT_EQ(object_occupancy_param.velocity->y(), 0.0);
  EXPECT_EQ(object_occupancy_param.acceleration_mpss, 1.0);
  EXPECT_FALSE(object_occupancy_param.is_fully_within_corridor);
}

// In this test, the object is to the right of the right boundary.
//  (0, 10)   ____________________________  (10, 10)         left boundary
//
//
//
//  (0, 7)    - - - - - - - - - - - - - - - (10, 7)          nominal path
//
//
//  (0, 4)    _____________________________  (10, 4)          right boundary
//
//                       _______
//                      | Ego   |
//                      |_(5,2)_|

TEST_F(ObjectOccupancyParamGenerationTest, SingleObjectBeyondBoundaryTest) {
  const std::vector<math::geometry::Point2d> nominal_path_points = {
      {0.0, 7.0}, {10.0, 7.0}};
  const math::geometry::PolylineCurve2d nominal_path_curve(nominal_path_points);
  const math::Range1d driving_arclength_range(0.0, 10.0);
  const std::vector<math::geometry::Point2d> left_boundary_points = {
      {0.0, 10.0}, {10.0, 10.0}};
  const math::geometry::PolylineCurve2d left_boundary_curve(
      left_boundary_points);
  const std::vector<math::geometry::Point2d> right_boundary_points = {
      {0.0, 4.0}, {10.0, 4.0}};
  const math::geometry::PolylineCurve2d right_boundary_curve(
      right_boundary_points);
  const PlannerObject planner_object(agent_proto_, timestamp,
                                     pose_at_plan_init_ts_);
  ObjectOccupancyParam object_occupancy_param =
      ComputeObjectSnapshotOccupancyParam(
          planner_object.pose(), nominal_path_curve, left_boundary_curve,
          right_boundary_curve);

  EXPECT_EQ(object_occupancy_param.full_body_start_arclength_m, 3.0);
  EXPECT_EQ(object_occupancy_param.full_body_end_arclength_m, 7.0);
  EXPECT_EQ(object_occupancy_param.full_body_start_lateral_distance_m, -6.0);
  EXPECT_EQ(object_occupancy_param.full_body_end_lateral_distance_m, -4.0);
  EXPECT_EQ(object_occupancy_param.left_boundary_clearance_m, 6.0);
  EXPECT_EQ(object_occupancy_param.right_boundary_clearance_m, 6.0);
  EXPECT_EQ(object_occupancy_param.center_line_clearance_m, 4.0);
  EXPECT_EQ(object_occupancy_param.dist_to_center_line_range.start_pos, -6.0);
  EXPECT_EQ(object_occupancy_param.dist_to_center_line_range.end_pos, -4.0);
  EXPECT_EQ(object_occupancy_param.center_line_side, math::pb::kRight);
  EXPECT_EQ(object_occupancy_param.cross_track_encroachment_m, -1.0);
  EXPECT_EQ(object_occupancy_param.snapshot_start_arclength_m, 3.0);
  EXPECT_EQ(object_occupancy_param.snapshot_end_arclength_m, 7.0);
  EXPECT_EQ(object_occupancy_param.min_corridor_width_m, 6.0);
  EXPECT_EQ(object_occupancy_param.max_left_violation, -7.0);
  EXPECT_EQ(object_occupancy_param.max_right_violation, 3.0);
  EXPECT_EQ(object_occupancy_param.relative_heading_rad, 0.0);
  EXPECT_EQ(object_occupancy_param.along_track_speed_mps, 5.0);
  EXPECT_EQ(object_occupancy_param.cross_track_speed_mps, 0.0);
  EXPECT_EQ(object_occupancy_param.speed_mps, 5.0);
  EXPECT_EQ(object_occupancy_param.velocity->x(), 5.0);
  EXPECT_EQ(object_occupancy_param.velocity->y(), 0.0);
  EXPECT_EQ(object_occupancy_param.acceleration_mpss, 1.0);
  EXPECT_FALSE(object_occupancy_param.is_fully_within_corridor);
}
}  // namespace planner
