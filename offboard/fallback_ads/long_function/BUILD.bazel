package(default_visibility = ["//visibility:public"])

cc_library(
    name = "long_function_main",
    srcs = [
        "long_function_main.c",
    ],
    hdrs = [
        "long_function_main.h",
    ],
    copts = [
        "-x",
        "c",
        "-std=c11",
        "-Wno-missing-field-initializers",
    ],
    include_prefix = "long_function/",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_long_function",
        "//offboard/fallback_ads/common_math_library:common_method",
        "//offboard/fallback_ads/common_math_library:vehicle_parameters",
        "//offboard/fallback_ads/long_function/situation_evaluation:situation_evaluation_decision",
        "//offboard/fallback_ads/long_function/stop_in_lane:avoidance_deceleration",
        "//offboard/fallback_ads/long_function/stop_in_lane:free_stop_control",
        "//offboard/fallback_ads/long_function/stop_in_lane:long_threat_assessor",
        "//offboard/fallback_ads/long_function/stop_in_lane:time_to_collision",
    ],
)

cc_library(
    name = "long_function_parameter",
    srcs = [
        "long_function_parameter.c",
    ],
    hdrs = [
        "long_function_parameter.h",
    ],
    copts = [
        "-x",
        "c",
        "-std=c11",
    ],
    include_prefix = "long_function/",
    deps = [
        "//offboard/fallback_ads/common_math_library:interpolation",
    ],
)

cc_library(
    name = "long_function_type",
    hdrs = [
        "long_function_type.h",
    ],
    copts = [
        "-x",
        "c",
        "-std=c11",
    ],
    include_prefix = "long_function/",
)
