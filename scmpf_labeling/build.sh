set -x
set -e

cd `dirname $0`

PROTOC_DIR='./proto_path/'
PROTOC_PATH="${PROTOC_DIR}/bin/"
PROJECT_DIR='../offboard/labeling'
OUTPUT='./output'
LABELING_PROTOS_DIR=${PROJECT_DIR}'/common/protos_for_labeling'
ELASTIC_DOCKERFILE='./elastic_dockerfile'

VOY_BASE_SRC="../onboard/common/base/src/voy_base"

function main()
{
    rm -rf $OUTPUT
    mkdir $OUTPUT

    prepare_protoc
    compile_labeling_protos

    ls $PROJECT_DIR | grep -v output | xargs -I '{}' cp -r $PROJECT_DIR/{} $OUTPUT

    cp -r $ELASTIC_DOCKERFILE/* $OUTPUT

    if [ "$OE_BRANCH_NAME"x != "master"x ] && [ "$OE_BRANCH_NAME"x != "trail-pre"x ]; then
        touch $OUTPUT/development.conf
    fi
}

function prepare_protoc()
{
    rm -frv ${PROTOC_DIR}
    mkdir -p ${PROTOC_DIR}

    PROTOC_FILE='protoc-3.14.0.zip'
    wget http://gift-pyvoyager-cdn.didistatic.com/static/pyvoyager/protoc-3.14.0-linux-x86_64.zip -O ${PROTOC_FILE}
    unzip ${PROTOC_FILE} -d ${PROTOC_DIR}
}

function compile_labeling_protos()
{
    mkdir -p ${LABELING_PROTOS_DIR}
    ${PROTOC_PATH}/protoc --version
    ${PROTOC_PATH}/protoc \
        --proto_path=../offboard/common/ \
        --proto_path=../offboard/perception/ \
        --proto_path=../onboard/common/trail/ \
        --proto_path=../onboard/common/ \
        --python_out=${LABELING_PROTOS_DIR} \
        ../offboard/common/labeling_protos/label_attribute_v2.proto \
        ../offboard/common/labeling_protos/label_object_type.proto \
        ../offboard/common/labeling_protos/label_v2.proto \
        ../offboard/common/labeling_protos/object_attribute.proto \
        ../offboard/common/labeling_protos/label.proto \
        ../offboard/common/labeling_protos/labeled_v2.proto \
        ../offboard/common/labeling_protos/image_task.proto \
        ../offboard/common/labeling_protos/label_v2_operation_log.proto \
        ../offboard/common/labeling_protos/labeling_report.proto \
        ../offboard/common/labeling_protos/label_setting.proto \
        ../offboard/common/labeling_protos/label_task_snapshot_v2.proto \
        ../offboard/common/labeling_protos/label_task_spec.proto \
        ../offboard/common/labeling_protos/task.proto \
        ../offboard/common/labeling_protos/image_label.proto \
        ../offboard/common/sim_protos/trip_segment.proto \
        ../offboard/perception/perception_protos/labeled_trip_segment.proto \
        ../onboard/common/trail/onboard_trail_protos/options.proto \
        ../onboard/common/voy_protos/axis_aligned_box2d.proto \
        ../onboard/common/voy_protos/camera_segmentation.proto \
        ../onboard/common/voy_protos/cuboid.proto \
        ../onboard/common/voy_protos/hdmap_layer.proto \
        ../onboard/common/voy_protos/sensor.proto \
        ../onboard/common/voy_protos/perception_segmentation_type.proto \
        ../onboard/common/voy_protos/pose.proto \
        ../onboard/common/voy_protos/point.proto \
        ../onboard/common/voy_protos/polygon.proto
}

main

echo -e 'build done'
exit 0
