#include "common_math_library/matrix.h"

#include <math.h>
#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>
#include <stdio.h>

#include "common_math_library/assert_cfg.h"
#include "common_math_library/common_method.h"

// set all the elements of the matrix with a const value
void CML_MatrixSetAll(CML_Matrix* matrix_ptr, uint32_t rows, uint32_t cols,
                      float value) {
  uint32_t size = cols * rows;
  if (matrix_ptr->max_size >= size) {
    // set new dimension
    matrix_ptr->cols = (uint8_t)cols;
    matrix_ptr->rows = (uint8_t)rows;
    // set elements
    for (uint32_t index = 0u; index < size; ++index) {
      matrix_ptr->data_ptr[index] = value;
    }
  } else {
    // return empty matrix
    matrix_ptr->cols = (uint8_t)0u;
    matrix_ptr->rows = (uint8_t)0u;
    CML_ASSERT(false);
  }
}

void CML_MatrixSetZero(CML_Matrix* matrix_ptr) {
  CML_MatrixSetAll(matrix_ptr, matrix_ptr->rows, matrix_ptr->cols, 0.0F);
}

// set matrix with identity matrix
void CML_MatrixSetIdentity(CML_Matrix* matrix_ptr, uint32_t size) {
  uint32_t size_square = size * size;

  if (matrix_ptr->max_size >= size_square) {
    // set new dimension
    matrix_ptr->cols = (uint8_t)size;
    matrix_ptr->rows = (uint8_t)size;

    // set with zero
    CML_MatrixSetZero(matrix_ptr);

    // set diagonal to one
    for (uint32_t index = 0u; index < size_square;
         index += ((uint32_t)matrix_ptr->cols + 1U)) {
      matrix_ptr->data_ptr[index] = 1.0F;
    }
  } else {
    // set empty matrix
    matrix_ptr->cols = (uint8_t)0u;
    matrix_ptr->rows = (uint8_t)0u;
    CML_ASSERT(false);
  }
}

// matrix addition c = a + b, two matrices a and b with same dimensions
void CML_MatrixAdd(CML_Matrix* matrix_c_ptr, const CML_Matrix* matrix_a_ptr,
                   const CML_Matrix* matrix_b_ptr) {
  uint32_t size = (uint32_t)matrix_a_ptr->cols * (uint32_t)matrix_a_ptr->rows;

  // check if matrix dimensions are compatible for addition
  if ((matrix_a_ptr->cols == matrix_b_ptr->cols) &&
      (matrix_a_ptr->rows == matrix_b_ptr->rows) &&
      (matrix_c_ptr->max_size >= size)) {
    // add elements
    if (matrix_a_ptr->data_ptr == matrix_c_ptr->data_ptr) {
      for (uint32_t index = 0u; index < size; ++index) {
        matrix_c_ptr->data_ptr[index] += matrix_b_ptr->data_ptr[index];
      }
    } else if (matrix_b_ptr->data_ptr == matrix_c_ptr->data_ptr) {
      for (uint32_t index = 0u; index < size; ++index) {
        matrix_c_ptr->data_ptr[index] += matrix_a_ptr->data_ptr[index];
      }
    } else {
      for (uint32_t index = 0u; index < size; ++index) {
        matrix_c_ptr->data_ptr[index] =
            matrix_a_ptr->data_ptr[index] + matrix_b_ptr->data_ptr[index];
      }
    }
    // update size for result matrix
    matrix_c_ptr->cols = matrix_a_ptr->cols;
    matrix_c_ptr->rows = matrix_a_ptr->rows;
  } else {
    // set empty matrix
    matrix_c_ptr->cols = (uint8_t)0u;
    matrix_c_ptr->rows = (uint8_t)0u;
    CML_ASSERT(false);
  }
}

// matrix subtraction c = a - b, two matrices a and b with same dimensions
void CML_MatrixSub(CML_Matrix* matrix_c_ptr, const CML_Matrix* matrix_a_ptr,
                   const CML_Matrix* matrix_b_ptr) {
  uint32_t size = (uint32_t)matrix_a_ptr->cols * (uint32_t)matrix_a_ptr->rows;

  // check if matrix have same dimensions
  if ((matrix_a_ptr->cols == matrix_b_ptr->cols) &&
      (matrix_a_ptr->rows == matrix_b_ptr->rows) &&
      (matrix_c_ptr->max_size >= matrix_a_ptr->cols * matrix_a_ptr->rows)) {
    // substract elements
    if (matrix_a_ptr->data_ptr == matrix_c_ptr->data_ptr) {
      for (uint32_t index = 0u; index < size; ++index) {
        matrix_c_ptr->data_ptr[index] -= matrix_b_ptr->data_ptr[index];
      }
    } else {
      for (uint32_t index = 0u; index < size; ++index) {
        matrix_c_ptr->data_ptr[index] =
            matrix_a_ptr->data_ptr[index] - matrix_b_ptr->data_ptr[index];
      }
    }

    // update size for result matrix
    matrix_c_ptr->cols = matrix_a_ptr->cols;
    matrix_c_ptr->rows = matrix_a_ptr->rows;
  } else {
    // set empty matrix
    matrix_c_ptr->cols = (uint8_t)0;
    matrix_c_ptr->rows = (uint8_t)0;
    CML_ASSERT(false);
  }
}

// matrix multiplication, c = a * b
void CML_MatrixMul(CML_Matrix* matrix_c_ptr, const CML_Matrix* matrix_a_ptr,
                   const CML_Matrix* matrix_b_ptr) {
  // check if matrix dimensions are compatible for multiplication and ensure
  // result matrix is distinct from input matrices
  if ((matrix_a_ptr->cols != (uint8_t)0U) &&
      (matrix_a_ptr->cols == matrix_b_ptr->rows) &&
      (matrix_a_ptr->data_ptr != matrix_c_ptr->data_ptr) &&
      (matrix_b_ptr->data_ptr != matrix_c_ptr->data_ptr) &&
      (matrix_c_ptr->max_size >= matrix_b_ptr->cols * matrix_a_ptr->rows)) {
    // multiply matrix elements
    for (uint32_t i = 0; i < matrix_a_ptr->rows; ++i) {
      for (uint32_t j = 0; j < matrix_b_ptr->cols; ++j) {
        float sum = 0.0F;
        for (uint32_t k = 0; k < matrix_a_ptr->cols; ++k) {
          sum += matrix_a_ptr->data_ptr[i * matrix_a_ptr->cols + k] *
                 matrix_b_ptr->data_ptr[k * matrix_b_ptr->cols + j];
        }
        matrix_c_ptr->data_ptr[i * matrix_b_ptr->cols + j] = sum;
      }
    }
    // update dimensions for result matrix
    matrix_c_ptr->cols = matrix_b_ptr->cols;
    matrix_c_ptr->rows = matrix_a_ptr->rows;
  } else {
    // set empty matrix
    matrix_c_ptr->cols = (uint8_t)0u;
    matrix_c_ptr->rows = (uint8_t)0u;
    CML_ASSERT(false);
  }
}

// multiplies the elements of matrix a by the constant factor
void CML_MatrixScale(CML_Matrix* matrix_a_ptr, float factor) {
  uint32_t size = (uint32_t)matrix_a_ptr->cols * (uint32_t)matrix_a_ptr->rows;
  // scale elements
  for (uint32_t index = 0u; index < size; ++index) {
    matrix_a_ptr->data_ptr[index] *= factor;
  }
}

// transpose matrix, at = transpose(a)
void CML_MatrixTranspose(CML_Matrix* matrix_at_ptr,
                         const CML_Matrix* matrix_a_ptr) {
  // check if result matrix has enough space
  if (matrix_a_ptr->data_ptr != matrix_at_ptr->data_ptr &&
      (matrix_at_ptr->max_size >= (matrix_a_ptr->rows * matrix_a_ptr->cols))) {
    // transpose elements
    for (uint32_t i = 0ul; i < matrix_a_ptr->rows; ++i) {
      for (uint32_t j = 0ul; j < matrix_a_ptr->cols; ++j) {
        matrix_at_ptr->data_ptr[j * matrix_a_ptr->rows + i] =
            matrix_a_ptr->data_ptr[i * matrix_a_ptr->cols + j];
      }
    }
    // update dimensions for result matrix
    matrix_at_ptr->rows = matrix_a_ptr->cols;
    matrix_at_ptr->cols = matrix_a_ptr->rows;
  } else {
    // set empty matrix
    matrix_at_ptr->rows = (uint8_t)0u;
    matrix_at_ptr->cols = (uint8_t)0u;
    CML_ASSERT(false);
  }
}

// this function compute matrix(2x2) inverse: Res = inv(A)
//                    Uses Cramer's Rule for matrix size 2x2.
//                    If A = |a b| and det(A) = (ad-bc),
//                           |c d|
//                    det(A) should be a non-zero value, then,
//                    Inverse of A, inv(A) = (1/det(A)) * | d -b|
//                                                        |-c  a|
static bool CML_Matrix2x2Inv(const float matrix_a[4], float matrix_res[4]) {
  bool be_solved = false;
  // calculate determinant
  float det = (matrix_a[0] * matrix_a[3]) - (matrix_a[1] * matrix_a[2]);

  // check if matrix is invertible
  if (CML_IsNonZero(det)) {
    // calculate inverse
    matrix_res[0] = matrix_a[3] / det;
    matrix_res[1] = -matrix_a[1] / det;
    matrix_res[2] = -matrix_a[2] / det;
    matrix_res[3] = matrix_a[0] / det;
    be_solved = true;
  } else {
    be_solved = false;
  }

  return be_solved;
}

// this function compute matrix(3x3) inverse: Res = inv(A)
static bool CML_Matrix3x3Inv(const float matrix_a[9], float matrix_res[9]) {
  bool be_solved = false;

  // Cramer's Rule for matrix size == 3
  float det = (((matrix_a[0] * matrix_a[4]) - (matrix_a[3] * matrix_a[1])) *
               matrix_a[8]) +
              (((matrix_a[3] * matrix_a[7]) - (matrix_a[6] * matrix_a[4])) *
               matrix_a[2]) +
              (((matrix_a[6] * matrix_a[1]) - (matrix_a[0] * matrix_a[7])) *
               matrix_a[5]);

  if (CML_IsNonZero(det)) {
    matrix_res[0] =
        ((matrix_a[4] * matrix_a[8]) - (matrix_a[5] * matrix_a[7])) / det;
    matrix_res[1] =
        ((matrix_a[2] * matrix_a[7]) - (matrix_a[1] * matrix_a[8])) / det;
    matrix_res[2] =
        ((matrix_a[1] * matrix_a[5]) - (matrix_a[2] * matrix_a[4])) / det;
    matrix_res[3] =
        ((matrix_a[5] * matrix_a[6]) - (matrix_a[3] * matrix_a[8])) / det;
    matrix_res[4] =
        ((matrix_a[0] * matrix_a[8]) - (matrix_a[2] * matrix_a[6])) / det;
    matrix_res[5] =
        ((matrix_a[2] * matrix_a[3]) - (matrix_a[0] * matrix_a[5])) / det;
    matrix_res[6] =
        ((matrix_a[3] * matrix_a[7]) - (matrix_a[4] * matrix_a[6])) / det;
    matrix_res[7] =
        ((matrix_a[1] * matrix_a[6]) - (matrix_a[0] * matrix_a[7])) / det;
    matrix_res[8] =
        ((matrix_a[0] * matrix_a[4]) - (matrix_a[1] * matrix_a[3])) / det;

    be_solved = true;
  } else {
    be_solved = false;
  }

  return be_solved;
}

bool CML_MatrixInv(CML_Matrix* matrix_res_ptr, CML_Matrix* matrix_a_ptr) {
  bool is_solved = false;
  float* matrix_a_data_ptr = matrix_a_ptr->data_ptr;
  float* matrix_res_data_ptr = matrix_res_ptr->data_ptr;

  // Check if the matrix is a square matrix.
  if ((matrix_a_ptr->cols != (uint8_t)0U) &&
      (matrix_a_ptr->cols == matrix_a_ptr->rows) &&
      (matrix_a_ptr->data_ptr != matrix_res_ptr->data_ptr) &&
      (matrix_res_ptr->max_size >= (matrix_a_ptr->cols * matrix_a_ptr->rows))) {
    if (matrix_a_ptr->cols == (uint8_t)1U) {
      // 1x1 matrix inversion.
      if (CML_IsNonZero(matrix_a_data_ptr[0])) {
        *matrix_res_data_ptr = 1.0F / (*matrix_a_data_ptr);
        is_solved = true;
      } else {
        is_solved = false;
      }
    } else if (matrix_a_ptr->cols == (uint8_t)2U) {
      // 2x2 matrix inversion.
      is_solved = CML_Matrix2x2Inv(matrix_a_data_ptr, matrix_res_data_ptr);
    } else if (matrix_a_ptr->cols == (uint8_t)3U) {
      // 3x3 matrix inversion.
      is_solved = CML_Matrix3x3Inv(matrix_a_data_ptr, matrix_res_data_ptr);
    } else {
      // Gauss Jordan elimination method.
      uint32_t size = (uint32_t)matrix_a_ptr->cols;
      const float tolerance = 1e-10F;
      CML_MatrixSetIdentity(matrix_res_ptr, size);
      for (uint32_t i = 0; i < size; i++) {
        // Choose pivot Row.
        uint32_t pivotRow = i;
        float maxVal = fabs(matrix_a_data_ptr[i * size + i]);
        for (uint32_t k = i + 1; k < size; k++) {
          if (fabs(matrix_a_data_ptr[k * size + i]) > maxVal) {
            maxVal = fabs(matrix_a_data_ptr[k * size + i]);
            pivotRow = k;
          }
        }

        // If the pivot element are too small, the matrix is irreversible.
        if (fabs(matrix_a_data_ptr[pivotRow * size + i]) < tolerance) {
          is_solved = false;
          break;
        }

        // Swap rows (swap original matrix and result matrix simultaneously).
        if (pivotRow != i) {
          for (uint32_t j = 0; j < size; j++) {
            float tempA = matrix_a_data_ptr[i * size + j];
            matrix_a_data_ptr[i * size + j] =
                matrix_a_data_ptr[pivotRow * size + j];
            matrix_a_data_ptr[pivotRow * size + j] = tempA;

            float tempRes = matrix_res_data_ptr[i * size + j];
            matrix_res_data_ptr[i * size + j] =
                matrix_res_data_ptr[pivotRow * size + j];
            matrix_res_data_ptr[pivotRow * size + j] = tempRes;
          }
        }

        // Normalize the pivot element rows (simultaneously operate on the
        // original matrix and result matrix).
        float pivot = matrix_a_data_ptr[i * size + i];
        for (uint32_t j = 0; j < size; j++) {
          matrix_a_data_ptr[i * size + j] /= pivot;
          matrix_res_data_ptr[i * size + j] /= pivot;
        }

        // Eliminate the pivot column elements of other rows.(simultaneously
        // operate on the original matrix and result matrix)
        for (uint32_t k = 0; k < size; k++) {
          if (k != i) {
            float factor = matrix_a_data_ptr[k * size + i];
            for (uint32_t j = 0; j < size; j++) {
              matrix_a_data_ptr[k * size + j] -=
                  factor * matrix_a_data_ptr[i * size + j];
              matrix_res_data_ptr[k * size + j] -=
                  factor * matrix_res_data_ptr[i * size + j];
            }
          }
        }
      }
      is_solved = true;
    }
  } else {
    is_solved = false;
  }

  if (is_solved) {
    // Update the dimensions of the result matrix.
    matrix_res_ptr->cols = matrix_a_ptr->cols;
    matrix_res_ptr->rows = matrix_a_ptr->rows;
  } else {
    // Set as an empty matrix.
    matrix_res_ptr->cols = (uint8_t)0u;
    matrix_res_ptr->rows = (uint8_t)0u;
    CML_ASSERT(false);
  }

  return is_solved;
}

// function to return the maximum value in the matrix
float CML_MatrixMax(const CML_Matrix* matrix_ptr) {
  uint32_t size = (uint32_t)matrix_ptr->cols * (uint32_t)matrix_ptr->rows;
  float* matrix_data_ptr = matrix_ptr->data_ptr;
  float max_value = matrix_data_ptr[0];

  for (uint32_t index = 1u; index < size; ++index) {
    if (matrix_data_ptr[index] > max_value) {
      max_value = matrix_data_ptr[index];
    }
  }

  return max_value;
}

// function to return the minimum value in the matrix
float CML_MatrixMin(const CML_Matrix* matrix_ptr) {
  uint32_t size = (uint32_t)matrix_ptr->cols * (uint32_t)matrix_ptr->rows;
  float* matrix_data_ptr = matrix_ptr->data_ptr;
  float min_value = matrix_data_ptr[0];

  for (uint32_t index = 1u; index < size; ++index) {
    if (matrix_data_ptr[index] < min_value) {
      min_value = matrix_data_ptr[index];
    }
  }

  return min_value;
}

// copy data from src matrix to dest matrix
void CML_MatrixCopy(CML_Matrix* matrix_dest_ptr,
                    const CML_Matrix* matrix_src_ptr) {
  uint32_t size =
      (uint32_t)matrix_src_ptr->cols * (uint32_t)matrix_src_ptr->rows;
  float* matrix_a_data_ptr = matrix_src_ptr->data_ptr;
  float* matrix_res_data_ptr = matrix_dest_ptr->data_ptr;

  // check if result matrix has enough space
  if (matrix_dest_ptr->max_size >= size) {
    // copy elements
    for (uint32_t index = 0u; index < size; ++index) {
      matrix_res_data_ptr[index] = matrix_a_data_ptr[index];
    }
    // update dimensions for result matrix
    matrix_dest_ptr->cols = matrix_src_ptr->cols;
    matrix_dest_ptr->rows = matrix_src_ptr->rows;
  } else {
    // set empty matrix
    matrix_dest_ptr->cols = (uint8_t)0u;
    matrix_dest_ptr->rows = (uint8_t)0u;
    CML_ASSERT(false);
  }
}

// swap data from matrix a to matrix b
void CML_MatrixSwap(CML_Matrix* matrix_a_ptr, CML_Matrix* matrix_b_ptr) {
  // swap dimensions
  uint8_t temp_col = matrix_a_ptr->cols;
  uint8_t temp_row = matrix_a_ptr->rows;
  matrix_a_ptr->cols = matrix_b_ptr->cols;
  matrix_a_ptr->rows = matrix_b_ptr->rows;
  matrix_b_ptr->cols = temp_col;
  matrix_b_ptr->rows = temp_row;

  // swap data pointers
  float* temp_data_ptr = matrix_a_ptr->data_ptr;
  matrix_a_ptr->data_ptr = matrix_b_ptr->data_ptr;
  matrix_b_ptr->data_ptr = temp_data_ptr;
}

void CML_MatrixPrint(const CML_Matrix* matrix_ptr) {
#if defined(__TASKING__)
  return;
#else
  for (uint8_t row = 0; row < matrix_ptr->rows; ++row) {
    for (uint8_t col = 0; col < matrix_ptr->cols; ++col) {
      printf("%.4f ", CML_MatrixGetElement(matrix_ptr, row, col));
    }
    printf("\n");
  }
#endif
}
