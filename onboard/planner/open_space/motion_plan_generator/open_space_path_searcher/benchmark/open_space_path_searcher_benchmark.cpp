#include <vector>

#include <benchmark/benchmark.h>
#include <google/protobuf/text_format.h>

#include "base/base_dir.h"
#include "math/enum.h"
#include "onboard/planner/open_space/constraints/motion_plan_problem_manager.h"
#include "planner/behavior/util/lane_sequence/test/test_utility.h"
#include "planner/open_space/motion_plan_generator/open_space_path_searcher/open_space_path_searcher.h"
#include "planner/world_model/planner_object/planner_object.h"
#include "planner/world_model/snapshot/robot_state_snapshot.h"
#include "proto_util/proto_io.h"
#include "sim_protos/virtual_scene.pb.h"
#include "vehicle_model/motion_model.h"
#include "vehicle_model/motion_model_with_shape.h"

#define DEBUG false

namespace planner {

namespace open_space {

namespace {

// Get Robot State Snapshot.
RobotStateSnapshot GenerateRobotStateSnapshot(const voy::Pose& center_pose) {
  // Generate pose.
  // Generate parameter.
  const vehicle_model::CarModelWithAxleRectangularShape car_model_with_shape(
      vehicle_model::CarModel(
          vehicle_model::CarModel::GetMotionModelParam(av_comm::CarType::kTest),
          /*method=*/math::IntegrationMethod::RK_2),
      vehicle_model::GetEgoAxleRectangularMeasurementByType(
          av_comm::CarType::kTest));

  // Generate pose snapshot.
  return RobotStateSnapshot(center_pose, 0.0, car_model_with_shape);
}

void LoadInputsFromVirtualSceneFile(
    const sim::pb::VirtualScene& virtual_scene, double lon_buffer,
    double lat_buffer, voy::Pose* end_pose_ptr,
    std::unordered_map<ObjectId, PlannerObject>* planner_object_map,
    lane_selection::LaneSequenceGeometry* lane_sequence_geometry_ptr,
    std::vector<prediction::pb::Agent>* agent_list) {  // Load target pose.
  voy::Pose& end_pose = *DCHECK_NOTNULL(end_pose_ptr);
  for (const sim::pb::Agent& agent : virtual_scene.agents()) {
    if (agent.name() == "target_pose") {
      if (DEBUG) {
        LOG(ERROR) << "Target pose found";
      }
      const sim::pb::TrajectoryWaypoint& agent_pose =
          agent.traffic_agent().follow_trajectory_action().trajectory_waypoints(
              0);
      end_pose.set_x(agent_pose.x_pos());
      end_pose.set_y(agent_pose.y_pos());
      end_pose.set_yaw(
          math::NormalizeMinusPiToPi(agent_pose.heading().value()));
      std::shared_ptr<RobotStateSnapshot> robot_snapshot =
          std::make_shared<RobotStateSnapshot>(
              GenerateRobotStateSnapshot(end_pose));
      end_pose.set_x(robot_snapshot->x());
      end_pose.set_y(robot_snapshot->y());
      end_pose.set_yaw(robot_snapshot->heading());
      break;
    }
  }

  // Load lane sequence geometry.
  for (const sim::pb::Agent& agent : virtual_scene.agents()) {
    if (!agent.has_construction_zone()) {
      continue;
    }
    const ::voy::Point2d& position =
        agent.construction_zone().transform().position();
    const math::geometry::Point2d center(position.x(), position.y());
    std::vector<const pnc_map::Lane*> lanes;
    math::geometry::Polyline2d boundary_line;
    for (const ::voy::Point2d& point : agent.construction_zone().contour()) {
      boundary_line.push_back(math::geometry::Point2d(
          point.x() + position.x(), point.y() + position.y()));
    }
    boundary_line.pop_back();
    if (agent.name() == "left_boundary") {
      lane_sequence_geometry_ptr->left_hard_boundary_lines.push_back(
          math::geometry::PolylineCurve2d(boundary_line));
    } else if (agent.name() == "right_boundary") {
      lane_sequence_geometry_ptr->right_hard_boundary_lines.push_back(
          math::geometry::PolylineCurve2d(boundary_line));
    } else if (agent.name() == "nominal_path") {
      lane_sequence_geometry_ptr->nominal_path =
          math::geometry::PolylineCurve2d(boundary_line);
    }
  }

  // Prepare obstacles.
  agent_list->reserve(virtual_scene.agents_size());
  for (const sim::pb::Agent& agent : virtual_scene.agents()) {
    if (agent.name() == "target_pose") {
      continue;
    }
    if (agent.has_traffic_agent()) {
      const sim::pb::TrajectoryWaypoint& agent_pose =
          agent.traffic_agent().follow_trajectory_action().trajectory_waypoints(
              0);
      auto& tracked_object =
          *agent_list->emplace_back().mutable_tracked_object();
      tracked_object.set_center_x(agent_pose.x_pos());
      tracked_object.set_center_y(agent_pose.y_pos());
      tracked_object.set_width(agent.dimensions().width() + 2.0 * lat_buffer);
      tracked_object.set_length(agent.dimensions().length() + 2.0 * lon_buffer);
      tracked_object.set_heading(agent_pose.heading().value());

      TrafficParticipantPose traffic_pose(/*timestamp=*/0, tracked_object);

      planner_object_map->insert(std::make_pair(
          agent.id(),
          PlannerObject(agent_list->back(), /*timestamp=*/0, traffic_pose)));
    }
  }
}

// Populates a motion plan problem with the input MotionPlanProblemManager.
void PopulateMotionPlanProblem(
    const RobotState& robot_state, const voy::Pose& target_pose,
    const lane_selection::LaneSequenceGeometry& lane_seq_geom,
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const std::unordered_map<ConstructionZoneId, const ConstructionZone*>&
        construction_zone_ptr_map,
    MotionPlanProblemManager* motion_plan_problem_manager_ptr) {
  MotionPlanProblemManager& motion_plan_problem_manager =
      *DCHECK_NOTNULL(motion_plan_problem_manager_ptr);

  motion_plan_problem_manager.SetStartPoseInfoFromRobotState(
      MotionModeGuidance(planner::pb::MotionMode::BACKWARD,
                         MotionModeGuidance::GuidanceLevel::ENFORCE));
  motion_plan_problem_manager.AppendTargetPoseInfo(
      target_pose.x(), target_pose.y(), target_pose.yaw(),
      /*terminal_motion_mode_guidance=*/std::nullopt,
      /*xy_tolerance_roi=*/std::nullopt,
      /*heading_tolerance_range=*/std::nullopt);
  motion_plan_problem_manager.SetMotionPlanROIFromStartAndTargetPoses(
      /*start_pose_xy_buffer=*/2.0 * robot_state.GetLength(),
      /*target_pose_xy_buffer=*/2.0 * robot_state.GetLength());
  motion_plan_problem_manager.PopulateOccupanciesFromStructuredDrivingInputs(
      planner_object_map, construction_zone_ptr_map, lane_seq_geom);
}

}  // namespace

using ::google::protobuf::TextFormat;
class OpenSpacePathSearcherBenchmark : public benchmark::Fixture {
 public:
  void SetUp(const ::benchmark::State& state) {
    (void)state;
    CHECK(TextFormat::ParseFromString(
        R"pb(
          grid_config {
            xy_grid_resolution_in_meters: 0.5
            heading_slot_resolution_in_rad: 0.1
          }
          searcher_config {
            num_next_node_samples: 10
            path_segment_sampling_step_in_meters: 0.5
            max_num_of_feasible_path_solutions_to_find: 100
          }
          costing_config {
            forward_penalty: 1.0
            backward_penalty: 1.0
            gear_switch_penalty: 5.0
            steering_angle_penalty: 3.0
            steering_angle_change_penalty: 3.0
            heuristic_distance_penalty: 5.0
          }
          rs_path_generator_config {
            traj_short_length_penalty: 1.0
            traj_expected_shortest_length: 1.0
          }
        )pb",
        &planner_open_space_config_));

    const std::vector<std::string> virtual_scene_file_names = {
        "pull_out_reverse_case_test_1744951_lane.json",
        "pull_out_reverse_case_test_1744986_lane.json",
        "pull_out_reverse_case_test_1881176_lane.json",
        "pull_out_reverse_case_test_1744984_lane.json",
        "pull_out_reverse_case_test_1793478_lane.json"};

    // Change this const flag to benchmark different virtual scene test cases.
    constexpr int kSelectedVirtualSceneIdx = 4;
    DCHECK_GE(kSelectedVirtualSceneIdx, 0);
    DCHECK_LT(kSelectedVirtualSceneIdx, virtual_scene_file_names.size());

    const std::string& virtual_scene_file_name =
        virtual_scene_file_names[kSelectedVirtualSceneIdx];
    const std::string virtual_scene_json_path =
        base::GetBaseDirPath(base::BaseDir::kDataDir) +
        "/planner/open_space_path_search_test/data/" + virtual_scene_file_name;

    sim::pb::VirtualScene virtual_scene;
    proto_util::ReadJsonProtoFile(virtual_scene_json_path, &virtual_scene,
                                  true);

    voy::Pose start_pose = virtual_scene.initial_pose();
    start_pose.set_yaw(math::NormalizeMinusPiToPi(start_pose.yaw()));
    robot_state_ =
        std::make_unique<RobotState>(test_utility::ConstructRobotState(
            /*robot_x=*/start_pose.x(), /*robot_y=*/start_pose.y(),
            /*vel_x=*/start_pose.vel_x(), /*vel_y=*/start_pose.vel_y()));
    pb::ReplanLocatorDebug debug;
    robot_state_->UpdatePoseCanbus(start_pose, robot_state_->canbus(),
                                   /*replan_request=*/nullptr, &debug);

    const double lon_buffer = 0.0;
    const double lat_buffer = 0.0;
    LoadInputsFromVirtualSceneFile(virtual_scene, lon_buffer, lat_buffer,
                                   &end_pose_, &planner_object_map_,
                                   &lane_sequence_geometry_, &agent_list_);

    open_space_path_searcher_ =
        std::make_unique<OpenSpacePathSearcher>(planner_open_space_config_);

    if (DEBUG) {
      LOG(ERROR) << "==========================================";
      LOG(ERROR)
          << robot_state_->current_state_snapshot().rear_bumper_position();
      LOG(ERROR) << robot_state_->car_model_with_shape()
                        .param()
                        .measurement()
                        .DebugString();
      LOG(ERROR) << end_pose_.DebugString();
      LOG(ERROR) << planner_object_map_.size();
      LOG(ERROR) << dummy_construction_zone_ptr_map_.size();
      LOG(ERROR) << "==========================================";
    }
  }

 protected:
  std::unique_ptr<OpenSpacePathSearcher> open_space_path_searcher_;
  pb::OpenSpaceSearchConfig planner_open_space_config_;
  std::unordered_map<ObjectId, PlannerObject> planner_object_map_;
  std::unordered_map<ConstructionZoneId, const ConstructionZone*>
      dummy_construction_zone_ptr_map_;
  std::vector<prediction::pb::Agent> agent_list_;
  lane_selection::LaneSequenceGeometry lane_sequence_geometry_;
  std::unique_ptr<RobotState> robot_state_;
  voy::Pose end_pose_;
};

BENCHMARK_DEFINE_F(OpenSpacePathSearcherBenchmark, OpenSpaceGrid)
(benchmark::State& state) {  // NOLINT
  MotionPlanProblemManager motion_plan_problem_manager(*robot_state_);
  PopulateMotionPlanProblem(
      *robot_state_, end_pose_, lane_sequence_geometry_, planner_object_map_,
      dummy_construction_zone_ptr_map_, &motion_plan_problem_manager);
  while (state.KeepRunning()) {
    open_space_path_searcher_->grid_.Update(
        motion_plan_problem_manager.motion_plan_problem());
  }
}

BENCHMARK_DEFINE_F(OpenSpacePathSearcherBenchmark, OpenSpaceSearch)
(benchmark::State& state) {  // NOLINT
  MotionPlanProblemManager motion_plan_problem_manager(*robot_state_);
  PopulateMotionPlanProblem(
      *robot_state_, end_pose_, lane_sequence_geometry_, planner_object_map_,
      dummy_construction_zone_ptr_map_, &motion_plan_problem_manager);
  for (const auto _ : state) {
    benchmark::DoNotOptimize(open_space_path_searcher_->Search(
        motion_plan_problem_manager.motion_plan_problem()));
  }
}

BENCHMARK_REGISTER_F(OpenSpacePathSearcherBenchmark, OpenSpaceGrid);
BENCHMARK_REGISTER_F(OpenSpacePathSearcherBenchmark, OpenSpaceSearch);

}  // namespace open_space

}  // namespace planner

BENCHMARK_MAIN();

/*
Run on (16 X 4800 MHz CPU s)

1744951
-----------------------------------------------------------------------------------------
Benchmark                                         Time       CPU Iterations
-----------------------------------------------------------------------------------------
OpenSpacePathSearcherBenchmark/OpenSpaceGrid      3862351 ns 3862130 ns 188
OpenSpacePathSearcherBenchmark/OpenSpaceSearch    7838894 ns 7838821 ns 86
-----------------------------------------------------------------------------------------

1744986
<USER>
<GROUP>                                         Time       CPU Iterations
-----------------------------------------------------------------------------------------
OpenSpacePathSearcherBenchmark/OpenSpaceGrid      4307874 ns 4307845 ns 160
OpenSpacePathSearcherBenchmark/OpenSpaceSearch    8477462 ns 8475893 ns 82
-----------------------------------------------------------------------------------------

1881176
<USER>
<GROUP>                                         Time       CPU Iterations
-----------------------------------------------------------------------------------------
OpenSpacePathSearcherBenchmark/OpenSpaceGrid      4028733 ns 4028665 ns 176
OpenSpacePathSearcherBenchmark/OpenSpaceSearch    7009084 ns 7007748 ns 92
-----------------------------------------------------------------------------------------

1744984
<USER>
<GROUP>                                         Time       CPU Iterations
-----------------------------------------------------------------------------------------
OpenSpacePathSearcherBenchmark/OpenSpaceGrid      4501681 ns 4501381 ns 158
OpenSpacePathSearcherBenchmark/OpenSpaceSearch    8614339 ns 8614198 ns 76

1793478
-----------------------------------------------------------------------------------------
Benchmark                                         Time       CPU Iterations
-----------------------------------------------------------------------------------------
OpenSpacePathSearcherBenchmark/OpenSpaceGrid      5801722 ns 5801632 ns 116
OpenSpacePathSearcherBenchmark/OpenSpaceSearch    9138897 ns 9124484 ns 75
-----------------------------------------------------------------------------------------
*/
