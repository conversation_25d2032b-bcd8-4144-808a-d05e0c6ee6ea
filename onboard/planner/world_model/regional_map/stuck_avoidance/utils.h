#ifndef ONBOARD_PLANNER_WORLD_MODEL_REGIONAL_MAP_STUCK_AVOIDANCE_UTILS_H_
#define ONBOARD_PLANNER_WORLD_MODEL_REGIONAL_MAP_STUCK_AVOIDANCE_UTILS_H_

#include <limits>
#include <map>
#include <set>
#include <string>
#include <utility>
#include <vector>

#include "planner/utility/common/common_utility.h"
#include "planner/world_model/current_lane_reasoner/current_lane_candidate.h"
#include "planner/world_model/drivable_lane_reasoner/drivable_lane_reasoner.h"
#include "planner/world_model/lane_blockage/lane_blockage.h"
#include "planner/world_model/regional_map/stuck_avoidance/changed_lane_structure_param.h"
#include "planner/world_model/regional_map/stuck_avoidance/stuck_avoidance_param.h"
#include "planner/world_model/snapshot/robot_state_snapshot.h"
#include "pnc_map_service/map_elements/lane.h"

namespace planner {
namespace lane_selection {
// The default threshold to wait for vehicle start to move.
constexpr double kDefaultStartToMoveThresholdInMSec = 10000;
// Sample resolution for get skeleton points from curve.
constexpr double kSkeletonSampleResolutionInMeter = 3.0;

// It stores the information from an start point to an object.
struct StartPointToObjectInfo {
  StartPointToObjectInfo() = delete;
  explicit StartPointToObjectInfo(
      const ObjectBlockingInfo* object_in, const pnc_map::Lane* object_lane_in,
      double distance_from_start_point_in,
      std::vector<common::LaneSegment> lane_segments_in,
      std::optional<std::vector<const pnc_map::Lane*>> search_lane_sequence_in =
          std::nullopt)
      : object(object_in),
        object_lane(object_lane_in),
        distance_from_start_point(distance_from_start_point_in),
        lane_segments(std::move(lane_segments_in)),
        search_lane_sequence(std::move(search_lane_sequence_in)) {}
  // The object.
  const ObjectBlockingInfo* object = nullptr;
  // The lane where the object in.
  const pnc_map::Lane* object_lane = nullptr;
  // The distance from the start point to the object.
  double distance_from_start_point = std::numeric_limits<double>::max();
  // The lane segments from the start point to the object.
  std::vector<common::LaneSegment> lane_segments;
  std::optional<std::vector<const pnc_map::Lane*>> search_lane_sequence;
};
/**
 * @brief Trims lane segments based on relaxed lane marking parameters
 * This function processes each lane segment and adjusts their end positions
 * based on relaxed lane marking constraints while maintaining minimum viable
 * segment lengths.
 *
 * @param relax_lane_marking_param Contains information about which lane
 * markings can be relaxed
 * @param stuck_lane_segments Vector of lane segments that need to be trimmed
 *
 * Key steps:
 * 1. Check left and right lane markings for each segment
 * 2. Find applicable relaxed ranges for each marking
 * 3. Calculate new end position considering both markings
 * 4. Ensure trimmed segments maintain minimum length
 */
void TrimStuckLaneSegmentByRelaxLaneMarkingParamEndPos(
    hdmap::util::RelaxLaneMarkingParameter& relax_lane_marking_param,
    std::vector<common::LaneSegment>* stuck_lane_segments);

void UpdateStuckLaneSegmentsToMakeSectionPreferenceValid(
    const std::vector<const pnc_map::Lane*>& global_lane_sequence,
    const std::optional<size_t>& current_lane_on_global_idx,
    const std::optional<size_t>& first_enter_lane_along_global_idx,
    std::vector<common::LaneSegment>* stuck_lane_segments);

void GetLaneSegmentsAroundObject(
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const std::vector<common::LaneSegment>& segments_at_object,
    double max_backward_length_around_object,
    double max_forward_length_around_object,
    std::vector<common::LaneSegment>* lane_segments_around_object);

void UpdateRelaxLaneMarkingsAroundObjects(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const std::map<int64_t, std::vector<LaneMarkingBlockage>>&
        blocked_lane_marking_segments,
    bool allow_cross_road_lane_change,
    const std::set<StuckObject>& stuck_objects,
    hdmap::util::RelaxLaneMarkingParameter* relax_lane_marking_param);

// Base monitor event functions for different parameter types
pb::UnstuckMonitorEventInfo CreateMonitorEventInfo(
    const RelaxLaneSequenceScenarioParameter& scenario_param,
    const int mission_id);

pb::UnstuckMonitorEventInfo CreateMonitorEventInfo(
    const StuckAvoidanceParameter& stuck_param, const int mission_id);

void PublishMonitorEvent(const pb::UnstuckMonitorEventInfo& event_info,
                         const std::string& stage);

// Template function to handle both parameter types
template <typename ParamType>
void UpdateMonitorEvent(const ParamType& stuck_param, const std::string& stage,
                        const int mission_id) {
  pb::UnstuckMonitorEventInfo event_info =
      CreateMonitorEventInfo(stuck_param, mission_id);
  PublishMonitorEvent(event_info, stage);
}

// Gets search lane on specific direction.
std::vector<const pnc_map::Lane*> GetSearchLanesOnDirection(
    const pnc_map::Lane* lane, pb::SearchDirection search_direction);

// Returns the first target object info along the lane sequence or its
// neighbor.
std::optional<StartPointToObjectInfo>
FindFirstTargetObjectInfoAlongLaneSequence(
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const std::map<int64_t, LaneBlockage>& lane_blockages,
    const std::optional<const ObjectBlockingInfo*>& ignore_object,
    const pnc_map::Lane& start_lane, double start_arc_length,
    double max_length_to_find, bool stop_at_first_lane_change,
    bool (*IsTargetObject)(const ObjectBlockingInfo& object),
    pb::SearchDirection search_direction);

// Returns the first target object info along lane sequence by reverse
// searching.
std::optional<StartPointToObjectInfo>
FindFirstTargetObjectInfoAlongLaneSequenceByReverseSearching(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const std::map<int64_t, LaneBlockage>& lane_blockages,
    const std::optional<const ObjectBlockingInfo*>& ignore_object,
    const pnc_map::Lane& start_lane, double search_start_arc_length,
    double max_length_to_find,
    bool (*IsTargetObject)(const ObjectBlockingInfo& object));

// Gets the objects in the range who has length of max_length_to_find along the
// objects.
std::vector<StartPointToObjectInfo> FindTargetsObjectInfoAlongLaneSequence(
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const std::map<int64_t, LaneBlockage>& lane_blockages,
    const std::optional<const ObjectBlockingInfo*>& ignore_object,
    const pnc_map::Lane& start_lane, double start_arc_length,
    double max_length_to_find, bool stop_at_first_lane_change,
    bool (*IsTargetObject)(const ObjectBlockingInfo& object),
    pb::SearchDirection search_direction);

// Gets the first current lane candidate who is on the lane sequence.
// TODO(hehuoliang): Wrap as an interface when refactoring current lane
// associator.
const lane_candidate::CurrentLaneCandidate*
GetFirstCurrentLaneCandidateOnSequence(
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence,
    const std::vector<lane_candidate::CurrentLaneCandidate>&
        current_lane_candidates);

// Gets the optimal lane follow sequence candidate from last seed.
const pb::LaneSequenceCandidate*
GetOptimalLaneFollowSequenceCandidateFromLastSeed(
    const pb::LaneSequenceCandidates& last_candidates);

// Gets the optimal lane change sequence candidate from last seed.
const pb::LaneSequenceCandidate*
GetOptimalLaneChangeSequenceCandidateFromLastSeed(
    const pb::LaneSequenceCandidates& last_candidates);

// Returns true if the blocking object is a stationary car.
bool IsObjectStationary(const PlannerObject& planner_object);

// Returns true if the object is a valid stationary vehicle.
bool IsValidStationaryVehicle(const ObjectBlockingInfo& object);

// Returns true if the lane segment is blocked by a given object.
bool IsLaneSegmentBlocked(const std::map<int64_t, LaneBlockage>& lane_blockages,
                          int64_t lane_id,
                          const std::optional<double>& start_arc_percent,
                          const std::optional<double>& end_arc_percent,
                          const std::optional<int64_t>& object_id,
                          const std::optional<pb::BlockingType>& object_type);

// Returns true, if the lane segment is blocked by any objects.
bool IsLaneSegmentBlocked(const std::map<int64_t, LaneBlockage>& lane_blockages,
                          const common::LaneSegment& lane_segment);

// Returns true if the lane is blocked by a given object.
bool IsLaneBlocked(const std::map<int64_t, LaneBlockage>& lane_blockages,
                   int64_t lane_id, const std::optional<int64_t>& object_id,
                   const std::optional<pb::BlockingType>& object_type);

// Returns true if any lane is blocked by a given object.
bool IsLanesBlocked(const std::map<int64_t, LaneBlockage>& lane_blockages,
                    const std::vector<int64_t>& lane_ids,
                    const std::optional<int64_t>& object_id,
                    const std::optional<pb::BlockingType>& object_type);
bool IsLanesBlocked(const std::map<int64_t, LaneBlockage>& lane_blockages,
                    const std::vector<common::LaneSegment>& lane_segments,
                    const std::optional<int64_t>& object_id,
                    const std::optional<pb::BlockingType>& object_type);

// Returns true, if any lane segment is blocked by any objects.
bool IsLaneSegmentsBlocked(
    const std::map<int64_t, LaneBlockage>& lane_blockages,
    const std::vector<common::LaneSegment>& lane_segments);

// Returns true if these objects associated lane segments are blocked by any
// objects.
bool IsObjectsAssociatedLaneSegmentsBlocked(
    const std::map<int64_t, LaneBlockage>& lane_blockages,
    const std::set<StuckObject>& objects);

// Returns true if we need to consider the blockage as a real one in stuck
// avoidance.
bool ShouldConsiderBlockageAsReal(const ObjectBlockingInfo& object);

// Returns true if a car is high and may cause occlusion.
bool VehicleMayCauseOcclusion(const ObjectBlockingInfo& current_object);

// Gets the distance to next junction along a lane follow lane sequence.
// [Note]: We just search the junction in a range who has max length of
// |max_find_length|.
// Scenario 1: If we can find a junction before we search enough
// distance, we return the accumulated distance.
// Scenario 2: Do not find a junction in the max length range, we return double
// max.
// Scenario 3: Do not find a junction in the whole lane sequence, we return
// std::nullopt.
// And in some other unexpected scenarios, for example, we meet a lane change
// instance before junction, we return nullopt. It means can not determine the
// distance to junction.
std::optional<double> GetDistanceToJunctionAlongLaneSequence(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const std::vector<const pnc_map::Lane*>& lf_sequence_from_current_lane,
    const routing::pb::RouteLane& destination_lane,
    const pnc_map::Lane& start_lane, double start_percentage,
    bool stop_if_start_in_junction, double max_find_length);

// Constructs lane segments from lane follow sequence.
std::vector<common::LaneSegment> ConstructLaneSegmentsFromLFSequence(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const pnc_map::Lane* start_lane, double start_arc_length);

// Returns true if the vehicle is still stationary. If it is not certain, a
// nullopt is expected.
std::optional<bool> IsTheStuckVehicleStillStationary(
    const std::map<int64_t, LaneBlockage>& lane_blockages,
    const std::optional<int64_t>& object_id,
    const std::optional<int64_t>& object_lane_id,
    pb::BlockingType stuck_object_type);

// Returns true if the blockage which caused stuck has disappeared.
bool HasStuckObjectDisappeared(
    const StuckObject& stuck_object,
    const std::map<int64_t, LaneBlockage>& lane_blockages);

// Returns true if all of the blockage which caused stuck has disappeared.
bool HasStuckObjectsDisappeared(
    const std::set<StuckObject>& stuck_objects,
    const std::map<int64_t, LaneBlockage>& lane_blockages);

// Updates segments for staged unstuck.
// For some cases, we need to keep stuck signal valid until ego returns the
// original lane. In these cases, we consider the neighbor lanes are also
// in stuck.
void UpdateSegmentsForStagedUnstuck(
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const std::vector<common::LaneSegment>& raw_lane_segments,
    const bool need_to_lane_change_back,
    const hdmap::RelaxDirection avoid_direction,
    std::vector<common::LaneSegment>* stuck_lane_segments,
    std::vector<DirectedRelaxLaneSegments>*
        staged_relax_lane_segments_sequence);

// Returns true, if ego is in a lane segment. If ego is within any segment in
// the sequence, we say that ego intersect with the sequence.
bool IsEgoInLaneSegmentSequence(
    const RobotStateSnapshot& robot_state_snapshot,
    const std::vector<common::LaneSegment>& stuck_lane_segments,
    bool check_section_range);

// Converts object into PB.
void ConvertObjectInfosIntoStuckObjects(
    const std::vector<const ObjectBlockingInfo*>& object_infos,
    std::set<StuckObject>* stuck_objects);

// Gets the lane segments where these objects are in.
void GetObjectsAssociateLaneSegments(
    const std::set<StuckObject>& stuck_objects,
    std::vector<common::LaneSegment>* lane_segments_contains_objects);

// Returns true if the lane sequence pass the lane segments where the objects is
// in.
bool IsLaneSequencePassObjectAssociateLanes(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const std::set<StuckObject>& stuck_objects);

// Returns true if the lane sequence after search start pass the lane segments
// where the objects is in.
bool IsLaneSequencePassObjectAssociateLaneSegments(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const std::set<StuckObject>& stuck_objects, int64_t start_lane_id,
    double start_arc_length);

// Checks if a lane marking range is valid for lane change.
// Note: if |use_raw_attributes| is true, it is expected to check the original
// attributes of the lane marking.
bool IsValidLaneMarkingRangeForLaneChange(
    const pnc_map::LaneMarking* lane_marking,
    const math::pb::Range& arc_length_range_m, bool to_left,
    bool use_raw_attributes);

// Reasons the preferred section sequence to follow global route.
void ReasonSectionPreferenceParamAlongGlobalRoute(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const std::vector<const pnc_map::Lane*>& global_lane_sequence,
    size_t first_enter_lane_along_global_idx,
    SectionPreferenceParamForUnstuck* section_preference_param);

// Returns true if any of the given lanes has associated traffic lights.
bool HasTrafficLightAssociatedLane(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const std::vector<const pnc_map::Lane*>& lanes);

// Returns true if any lane is controlled by the Red traffic light.
bool IsLaneControlledByTrafficLight(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const pnc_map::Lane& lane, const voy::TrafficLights& traffic_lights,
    double waiting_duration = kDefaultStartToMoveThresholdInMSec);

// Returns true if the lanes after start lane in lane sequence is waiting for
// traffic light.
bool IsWaitingForTrafficLight(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const pnc_map::Lane& start_lane, const voy::TrafficLights& traffic_lights,
    double waiting_duration = kDefaultStartToMoveThresholdInMSec);

// Constructs a lane segment sequence along a lane sequence.
std::vector<common::LaneSegment> ConstructLaneSegmentsFromLaneSequence(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const pnc_map::Lane& start_lane, double start_arc_length,
    double max_length_to_find, bool stop_at_first_lane_change);

// Returns true if the |start_idx| in |global_lane_sequence| is the enter
// lane for right turn only road.
bool IsEnterLaneForRightTurnOnlyRoad(
    const std::vector<const pnc_map::Lane*>& global_lane_sequence,
    size_t start_idx);

// Returns true if the road is right turn only.
bool IsRightTurnOnlyRoad(const pnc_map::Road& road);

void MergeOverlappingSegments(
    std::vector<common::LaneSegment>* stuck_lane_segments);

hdmap::Polyline CreatePolylineProto(
    const std::vector<math::geometry::Point2d>& points);

hdmap::Polygon CreatePolygonProto(
    const std::vector<std::vector<math::geometry::Point2d>>& rings);

// Gets reference points for regular turn type.
std::vector<math::geometry::Point2d> GetReferencePointsForRegularTurn(
    const math::geometry::PolylineCurve2d& source_line,
    const math::geometry::PolylineCurve2d& via_line);

std::vector<math::geometry::Point2d> GetReferencePointsInJunction(
    const pnc_map::Lane& relax_enter_lane,
    const pnc_map::Lane& via_lane_in_junction, const pnc_map::Lane& exit_lane);

std::vector<math::geometry::Point2d> GetLaneMarkingPointsInJunction(
    const pnc_map::Lane& relax_enter_lane,
    const pnc_map::Lane& via_lane_in_junction, const pnc_map::Lane& exit_lane,
    bool to_left);

// Returns the relative side of relax direction.
std::optional<math::pb::Side> GetRelativeSideOfRelaxDirection(
    const pnc_map::Lane& relax_enter_lane,
    const pnc_map::Lane& via_lane_in_junction);

// Returns the start arclength on lane with hard boundary.
std::optional<double> GetStartArclengthOnLaneWithHardBoundary(
    const pnc_map::Lane& lane, bool check_left_side);
}  // namespace lane_selection
}  // namespace planner

#endif  // ONBOARD_PLANNER_WORLD_MODEL_REGIONAL_MAP_STUCK_AVOIDANCE_UTILS_H_
