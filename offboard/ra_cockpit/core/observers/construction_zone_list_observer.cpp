#include "construction_zone_list_observer.h"

#include <utility>

#include "av_comm/topics.h"
#include "glog/logging.h"

namespace remote_assist {

ConstructionZoneListObserver::ConstructionZoneListObserver(
    ConstructionZoneListCallback callback)
    : callback_(std::move(callback)) {}

bool ConstructionZoneListObserver::IsInterestedIn(
    const teleassist::pb::TeleassistRequest& message) {
  return (message.message_type() == kConstructionZoneList);
}

std::shared_ptr<google::protobuf::Message>
ConstructionZoneListObserver::OnMessage(
    const teleassist::pb::TeleassistRequest& message,
    DataChannel data_channel) {
  auto construction_zone_list = std::make_shared<voy::ConstructionZoneList>();
  if (!construction_zone_list->ParseFromString(message.message_data())) {
    LOG(ERROR) << "Received error ConstructionZoneList message format, len = "
               << message.message_data().length();
    return nullptr;
  }

  if (callback_) {
    callback_(construction_zone_list, data_channel);
  }
  return construction_zone_list;
}

}  // namespace remote_assist
